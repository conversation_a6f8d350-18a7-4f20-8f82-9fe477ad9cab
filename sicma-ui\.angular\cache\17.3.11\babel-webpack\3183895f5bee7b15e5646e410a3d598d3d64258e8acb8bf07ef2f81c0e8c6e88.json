{"ast": null, "code": "import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { provideNativeDateAdapter } from '@angular/material/core';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { ContractorService } from '@contractor-management/services/contractor.service';\nimport { EducationLevelService } from '@contractor-management/services/education-level.service';\nimport { EpsService } from '@contractor-management/services/eps.service';\nimport { GenderService } from '@contractor-management/services/gender.service';\nimport { LegalNatureService } from '@contractor-management/services/legal-nature.service';\nimport { ProfessionService } from '@contractor-management/services/profession.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { DepartmentService } from '@shared/services/department.service';\nimport { IDTypeService } from '@shared/services/id-type.service';\nimport { MunicipalityService } from '@shared/services/municipality.service';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { of, throwError } from 'rxjs';\nimport { ContractorDialogComponent } from './contractor-dialog.component';\ndescribe('ContractorDialogComponent', () => {\n  let component;\n  let fixture;\n  let dialogRef;\n  let idTypeService;\n  let epsService;\n  let genderService;\n  let educationLevelService;\n  let professionService;\n  let departmentService;\n  let municipalityService;\n  let legalNatureService;\n  let contractorService;\n  let alertService;\n  let spinnerService;\n  const mockIdTypes = [{\n    id: 1,\n    name: 'CC'\n  }, {\n    id: 2,\n    name: 'CE'\n  }];\n  const mockEpss = [{\n    id: 1,\n    name: 'EPS 1'\n  }, {\n    id: 2,\n    name: 'EPS 2'\n  }];\n  const mockGenders = [{\n    id: 1,\n    name: 'Male'\n  }, {\n    id: 2,\n    name: 'Female'\n  }];\n  const mockEducationLevels = [{\n    id: 1,\n    name: 'BACHILLER'\n  }, {\n    id: 2,\n    name: 'PROFESIONAL'\n  }, {\n    id: 3,\n    name: 'TÉCNICO'\n  }, {\n    id: 4,\n    name: 'TECNÓLOGO'\n  }, {\n    id: 5,\n    name: 'OTRO'\n  }];\n  const mockProfessions = [{\n    id: 1,\n    name: 'BACHILLER'\n  }, {\n    id: 2,\n    name: 'Engineer'\n  }];\n  const mockDepartments = [{\n    id: 1,\n    name: 'Department 1'\n  }, {\n    id: 2,\n    name: 'Department 2'\n  }];\n  const mockMunicipalities = [{\n    id: 1,\n    name: 'Municipality 1',\n    departmentId: 1,\n    department: mockDepartments[0]\n  }, {\n    id: 2,\n    name: 'Municipality 2',\n    departmentId: 1,\n    department: mockDepartments[0]\n  }];\n  const mockLegalNatures = [{\n    id: 1,\n    name: 'Nature 1'\n  }, {\n    id: 2,\n    name: 'Nature 2'\n  }];\n  const mockContractor = {\n    id: 1,\n    fullName: 'Test Contractor',\n    idType: mockIdTypes[0],\n    idNumber: 123456789,\n    personalEmail: '<EMAIL>',\n    corporateEmail: '<EMAIL>',\n    phone: 1234567890,\n    eps: mockEpss[0],\n    birthDate: '2000-01-01',\n    gender: mockGenders[0],\n    educationLevel: mockEducationLevels[0],\n    profession: mockProfessions[0],\n    lastObtainedDegree: '',\n    department: mockDepartments[0],\n    municipality: mockMunicipalities[0],\n    legalNature: mockLegalNatures[0]\n  };\n  beforeEach(() => {\n    dialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);\n    idTypeService = jasmine.createSpyObj('IDTypeService', ['getAll']);\n    epsService = jasmine.createSpyObj('EpsService', ['getAll']);\n    genderService = jasmine.createSpyObj('GenderService', ['getAll']);\n    educationLevelService = jasmine.createSpyObj('EducationLevelService', ['getAll']);\n    professionService = jasmine.createSpyObj('ProfessionService', ['getAll']);\n    departmentService = jasmine.createSpyObj('DepartmentService', ['getAll']);\n    municipalityService = jasmine.createSpyObj('MunicipalityService', ['getAllByDepartmentId']);\n    legalNatureService = jasmine.createSpyObj('LegalNatureService', ['getAll']);\n    contractorService = jasmine.createSpyObj('ContractorService', ['create', 'update']);\n    alertService = jasmine.createSpyObj('AlertService', ['success', 'error']);\n    spinnerService = jasmine.createSpyObj('NgxSpinnerService', ['show', 'hide']);\n    idTypeService.getAll.and.returnValue(of(mockIdTypes));\n    epsService.getAll.and.returnValue(of(mockEpss));\n    genderService.getAll.and.returnValue(of(mockGenders));\n    educationLevelService.getAll.and.returnValue(of(mockEducationLevels));\n    professionService.getAll.and.returnValue(of(mockProfessions));\n    departmentService.getAll.and.returnValue(of(mockDepartments));\n    municipalityService.getAllByDepartmentId.and.returnValue(of(mockMunicipalities));\n    legalNatureService.getAll.and.returnValue(of(mockLegalNatures));\n    TestBed.configureTestingModule({\n      imports: [ContractorDialogComponent, HttpClientTestingModule, BrowserAnimationsModule, ReactiveFormsModule],\n      providers: [{\n        provide: MatDialogRef,\n        useValue: dialogRef\n      }, {\n        provide: MAT_DIALOG_DATA,\n        useValue: {}\n      }, {\n        provide: IDTypeService,\n        useValue: idTypeService\n      }, {\n        provide: EpsService,\n        useValue: epsService\n      }, {\n        provide: GenderService,\n        useValue: genderService\n      }, {\n        provide: EducationLevelService,\n        useValue: educationLevelService\n      }, {\n        provide: ProfessionService,\n        useValue: professionService\n      }, {\n        provide: DepartmentService,\n        useValue: departmentService\n      }, {\n        provide: MunicipalityService,\n        useValue: municipalityService\n      }, {\n        provide: LegalNatureService,\n        useValue: legalNatureService\n      }, {\n        provide: ContractorService,\n        useValue: contractorService\n      }, {\n        provide: AlertService,\n        useValue: alertService\n      }, {\n        provide: NgxSpinnerService,\n        useValue: spinnerService\n      }, FormBuilder, provideNativeDateAdapter()]\n    });\n    fixture = TestBed.createComponent(ContractorDialogComponent);\n    component = fixture.componentInstance;\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should load all required data on init', () => {\n    component.ngOnInit();\n    expect(spinnerService.show).toHaveBeenCalled();\n    expect(idTypeService.getAll).toHaveBeenCalled();\n    expect(epsService.getAll).toHaveBeenCalled();\n    expect(genderService.getAll).toHaveBeenCalled();\n    expect(educationLevelService.getAll).toHaveBeenCalled();\n    expect(professionService.getAll).toHaveBeenCalled();\n    expect(departmentService.getAll).toHaveBeenCalled();\n    expect(legalNatureService.getAll).toHaveBeenCalled();\n    expect(spinnerService.hide).toHaveBeenCalled();\n  });\n  it('should handle error when loading initial data', () => {\n    idTypeService.getAll.and.returnValue(throwError(() => ({\n      error: 'Error'\n    })));\n    component.ngOnInit();\n    expect(alertService.error).toHaveBeenCalledWith('Error al cargar los datos del formulario');\n    expect(dialogRef.close).toHaveBeenCalled();\n  });\n  it('should load municipalities when department is selected', () => {\n    component.ngOnInit();\n    component.contractorForm.get('departmentId')?.setValue(1);\n    expect(municipalityService.getAllByDepartmentId).toHaveBeenCalledWith(1);\n    expect(component.municipalities).toEqual(mockMunicipalities);\n  });\n  it('should clear municipalities when department is cleared', () => {\n    component.ngOnInit();\n    component.contractorForm.get('departmentId')?.setValue(1);\n    component.contractorForm.get('departmentId')?.setValue(null);\n    expect(component.municipalities).toEqual([]);\n    expect(component.contractorForm.get('municipalityId')?.value).toBeNull();\n  });\n  it('should handle education level change for BACHILLER', () => {\n    component.ngOnInit();\n    const bachillerLevel = mockEducationLevels.find(el => el.name === 'BACHILLER');\n    component.contractorForm.get('educationLevelId')?.setValue(bachillerLevel?.id);\n    expect(component.isProfessionEditable).toBeFalse();\n    expect(component.contractorForm.get('lastObtainedDegree')?.disabled).toBeTrue();\n    expect(component.contractorForm.get('professionId')?.disabled).toBeTrue();\n    const bachillerProfession = mockProfessions.find(p => p.name === 'BACHILLER');\n    expect(component.contractorForm.get('professionId')?.value).toBe(bachillerProfession?.id);\n  });\n  it('should handle education level change for PROFESIONAL', () => {\n    component.ngOnInit();\n    const profesionalLevel = mockEducationLevels.find(el => el.name === 'PROFESIONAL');\n    component.contractorForm.get('educationLevelId')?.setValue(profesionalLevel?.id);\n    expect(component.isProfessionEditable).toBeTrue();\n    expect(component.contractorForm.get('lastObtainedDegree')?.disabled).toBeTrue();\n    expect(component.contractorForm.get('professionId')?.enabled).toBeTrue();\n    expect(component.contractorForm.get('professionId')?.hasValidator(Validators.required)).toBeTrue();\n  });\n  it('should handle education level change for TÉCNICO', () => {\n    component.ngOnInit();\n    const tecnicoLevel = mockEducationLevels.find(el => el.name === 'TÉCNICO');\n    component.onEducationLevelChange(tecnicoLevel);\n    expect(component.isProfessionEditable).toBeFalse();\n    expect(component.isLastObtainedDegreeEnabled).toBeFalse();\n  });\n  it('should handle education level change for TECNÓLOGO', () => {\n    component.ngOnInit();\n    const tecnologoLevel = mockEducationLevels.find(el => el.name === 'TECNÓLOGO');\n    component.onEducationLevelChange(tecnologoLevel);\n    expect(component.isProfessionEditable).toBeFalse();\n    expect(component.isLastObtainedDegreeEnabled).toBeFalse();\n  });\n  it('should handle education level change for other education levels', () => {\n    component.ngOnInit();\n    const otherLevel = mockEducationLevels.find(el => el.name === 'OTRO');\n    component.contractorForm.get('educationLevelId')?.setValue(otherLevel?.id);\n    expect(component.isProfessionEditable).toBeTrue();\n    expect(component.isLastObtainedDegreeEnabled).toBeFalse();\n    expect(component.contractorForm.get('lastObtainedDegree')?.disabled).toBeTrue();\n    expect(component.contractorForm.get('professionId')?.enabled).toBeTrue();\n    expect(component.contractorForm.get('professionId')?.hasValidator(Validators.required)).toBeFalse();\n  });\n  it('should handle education level change to null', () => {\n    component.ngOnInit();\n    component.contractorForm.get('educationLevelId')?.setValue(1);\n    component.contractorForm.get('educationLevelId')?.setValue(null);\n    expect(component.isProfessionEditable).toBeTrue();\n    expect(component.isLastObtainedDegreeEnabled).toBeFalse();\n    expect(component.contractorForm.get('lastObtainedDegree')?.disabled).toBeTrue();\n    expect(component.contractorForm.get('professionId')?.enabled).toBeTrue();\n    expect(component.contractorForm.get('professionId')?.value).toBeNull();\n  });\n  it('should patch form values when editing existing contractor', () => {\n    TestBed.resetTestingModule();\n    TestBed.configureTestingModule({\n      imports: [ContractorDialogComponent, HttpClientTestingModule, BrowserAnimationsModule, ReactiveFormsModule],\n      providers: [{\n        provide: MatDialogRef,\n        useValue: dialogRef\n      }, {\n        provide: MAT_DIALOG_DATA,\n        useValue: mockContractor\n      }, {\n        provide: IDTypeService,\n        useValue: idTypeService\n      }, {\n        provide: EpsService,\n        useValue: epsService\n      }, {\n        provide: GenderService,\n        useValue: genderService\n      }, {\n        provide: EducationLevelService,\n        useValue: educationLevelService\n      }, {\n        provide: ProfessionService,\n        useValue: professionService\n      }, {\n        provide: DepartmentService,\n        useValue: departmentService\n      }, {\n        provide: MunicipalityService,\n        useValue: municipalityService\n      }, {\n        provide: LegalNatureService,\n        useValue: legalNatureService\n      }, {\n        provide: ContractorService,\n        useValue: contractorService\n      }, {\n        provide: AlertService,\n        useValue: alertService\n      }, {\n        provide: NgxSpinnerService,\n        useValue: spinnerService\n      }, FormBuilder, provideNativeDateAdapter()]\n    });\n    fixture = TestBed.createComponent(ContractorDialogComponent);\n    component = fixture.componentInstance;\n    component.ngOnInit();\n    expect(component.contractorForm.get('fullName')?.value).toBe(mockContractor.fullName);\n    expect(component.contractorForm.get('idTypeId')?.value).toBe(mockContractor.idType?.id);\n    expect(component.contractorForm.get('idNumber')?.value).toBe(mockContractor.idNumber);\n    expect(component.contractorForm.get('personalEmail')?.value).toBe(mockContractor.personalEmail);\n  });\n  it('should load municipalities when editing a contractor with department', () => {\n    TestBed.resetTestingModule();\n    TestBed.configureTestingModule({\n      imports: [ContractorDialogComponent, HttpClientTestingModule, BrowserAnimationsModule, ReactiveFormsModule],\n      providers: [{\n        provide: MatDialogRef,\n        useValue: dialogRef\n      }, {\n        provide: MAT_DIALOG_DATA,\n        useValue: mockContractor\n      }, {\n        provide: IDTypeService,\n        useValue: idTypeService\n      }, {\n        provide: EpsService,\n        useValue: epsService\n      }, {\n        provide: GenderService,\n        useValue: genderService\n      }, {\n        provide: EducationLevelService,\n        useValue: educationLevelService\n      }, {\n        provide: ProfessionService,\n        useValue: professionService\n      }, {\n        provide: DepartmentService,\n        useValue: departmentService\n      }, {\n        provide: MunicipalityService,\n        useValue: municipalityService\n      }, {\n        provide: LegalNatureService,\n        useValue: legalNatureService\n      }, {\n        provide: ContractorService,\n        useValue: contractorService\n      }, {\n        provide: AlertService,\n        useValue: alertService\n      }, {\n        provide: NgxSpinnerService,\n        useValue: spinnerService\n      }, FormBuilder, provideNativeDateAdapter()]\n    });\n    fixture = TestBed.createComponent(ContractorDialogComponent);\n    component = fixture.componentInstance;\n    component.ngOnInit();\n    expect(municipalityService.getAllByDepartmentId).toHaveBeenCalledWith(mockContractor.department.id);\n  });\n  it('should validate required fields', () => {\n    component.ngOnInit();\n    expect(component.contractorForm.valid).toBeFalse();\n    component.contractorForm.patchValue({\n      fullName: 'Test Name',\n      idTypeId: 1,\n      idNumber: '123456789',\n      personalEmail: '<EMAIL>',\n      legalNatureId: 1\n    });\n    expect(component.contractorForm.valid).toBeTrue();\n  });\n  it('should validate email format', () => {\n    component.ngOnInit();\n    const personalEmailControl = component.contractorForm.get('personalEmail');\n    const corporateEmailControl = component.contractorForm.get('corporateEmail');\n    personalEmailControl?.setValue('invalid-email');\n    expect(personalEmailControl?.errors?.['email']).toBeTruthy();\n    personalEmailControl?.setValue('<EMAIL>');\n    expect(personalEmailControl?.errors).toBeNull();\n    corporateEmailControl?.setValue('invalid-email');\n    expect(corporateEmailControl?.errors?.['email']).toBeTruthy();\n    corporateEmailControl?.setValue('<EMAIL>');\n    expect(corporateEmailControl?.errors).toBeNull();\n  });\n  it('should validate phone number format', () => {\n    component.ngOnInit();\n    const phoneControl = component.contractorForm.get('phone');\n    phoneControl?.setValue('abc123');\n    expect(phoneControl?.errors?.['pattern']).toBeTruthy();\n    phoneControl?.setValue('123456789');\n    expect(phoneControl?.errors).toBeNull();\n  });\n  it('should handle error when loading municipalities', () => {\n    component.ngOnInit();\n    municipalityService.getAllByDepartmentId.and.returnValue(throwError(() => ({\n      error: 'Error loading municipalities'\n    })));\n    component.contractorForm.get('departmentId')?.setValue(1);\n    expect(alertService.error).toHaveBeenCalledWith('Error al cargar municipios');\n  });\n  describe('onSubmit', () => {\n    it('should validate form before submission', () => {\n      component.ngOnInit();\n      component.contractorForm.patchValue({\n        fullName: ''\n      });\n      expect(component.contractorForm.valid).toBeFalse();\n      component.contractorForm.patchValue({\n        fullName: 'Test Name',\n        idTypeId: 1,\n        idNumber: '123456789',\n        personalEmail: '<EMAIL>',\n        legalNatureId: 1\n      });\n      expect(component.contractorForm.valid).toBeTrue();\n    });\n    it('should format date for API submission', () => {\n      component.ngOnInit();\n      const testDate = new Date('2023-05-15');\n      const formattedDate = testDate.toISOString().split('T')[0];\n      expect(formattedDate).toBe('2023-05-15');\n    });\n    it('should handle empty phone as undefined', () => {\n      component.ngOnInit();\n      const emptyPhone = '';\n      const processedPhone = emptyPhone || undefined;\n      expect(processedPhone).toBeUndefined();\n    });\n  });\n  describe('Error handling', () => {\n    it('should show appropriate error messages', () => {\n      component.ngOnInit();\n      alertService.error.calls.reset();\n      alertService.error('Número de cédula ya se encuentra registrado');\n      expect(alertService.error).toHaveBeenCalledWith('Número de cédula ya se encuentra registrado');\n      alertService.error.calls.reset();\n      alertService.error('El correo institucional ya se encuentra registrado');\n      expect(alertService.error).toHaveBeenCalledWith('El correo institucional ya se encuentra registrado');\n      alertService.error.calls.reset();\n      alertService.error('Error al procesar la solicitud');\n      expect(alertService.error).toHaveBeenCalledWith('Error al procesar la solicitud');\n      alertService.error('Error al editar contratista');\n      expect(alertService.error).toHaveBeenCalledWith('Error al editar contratista');\n      alertService.error('Error al crear contratista');\n      expect(alertService.error).toHaveBeenCalledWith('Error al crear contratista');\n    });\n    it('should log messages to console', () => {\n      component.ngOnInit();\n      spyOn(console, 'log');\n      console.log('contractor', {\n        id: 1,\n        fullName: 'Test Name'\n      });\n      expect(console.log).toHaveBeenCalledWith('contractor', jasmine.any(Object));\n      console.log('contractor error', {\n        error: {\n          detail: 'Error message'\n        }\n      });\n      expect(console.log).toHaveBeenCalledWith('contractor error', jasmine.any(Object));\n    });\n  });\n  describe('Success handling', () => {\n    it('should show success messages', () => {\n      component.ngOnInit();\n      alertService.success.calls.reset();\n      alertService.success('Contratista creado exitosamente');\n      expect(alertService.success).toHaveBeenCalledWith('Contratista creado exitosamente');\n      alertService.success.calls.reset();\n      alertService.success('Contratista editado exitosamente');\n      expect(alertService.success).toHaveBeenCalledWith('Contratista editado exitosamente');\n    });\n  });\n  it('should have a method to close dialog', () => {\n    expect(dialogRef.close).toBeDefined();\n  });\n  describe('Date formatting', () => {\n    it('should correctly format date for API submission', () => {\n      component.ngOnInit();\n      const testDate = new Date('2023-05-15');\n      component.contractorForm.patchValue({\n        fullName: 'Test Name',\n        idTypeId: 1,\n        idNumber: '123456789',\n        personalEmail: '<EMAIL>',\n        legalNatureId: 1,\n        birthDate: testDate\n      });\n      const formattedDate = testDate.toISOString().split('T')[0];\n      expect(formattedDate).toBe('2023-05-15');\n    });\n  });\n  describe('Error handling', () => {\n    it('should show appropriate error messages', () => {\n      alertService.error.calls.reset();\n      alertService.error('Número de cédula ya se encuentra registrado');\n      expect(alertService.error).toHaveBeenCalledWith('Número de cédula ya se encuentra registrado');\n      alertService.error.calls.reset();\n      alertService.error('El correo institucional ya se encuentra registrado');\n      expect(alertService.error).toHaveBeenCalledWith('El correo institucional ya se encuentra registrado');\n      alertService.error.calls.reset();\n      alertService.error('Error al procesar la solicitud');\n      expect(alertService.error).toHaveBeenCalledWith('Error al procesar la solicitud');\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "TestBed", "FormBuilder", "ReactiveFormsModule", "Validators", "provideNativeDateAdapter", "MAT_DIALOG_DATA", "MatDialogRef", "BrowserAnimationsModule", "ContractorService", "EducationLevelService", "EpsService", "GenderService", "LegalNatureService", "ProfessionService", "AlertService", "DepartmentService", "IDTypeService", "MunicipalityService", "NgxSpinnerService", "of", "throwError", "ContractorDialogComponent", "describe", "component", "fixture", "dialogRef", "idTypeService", "epsService", "genderService", "educationLevelService", "professionService", "departmentService", "municipalityService", "legalNatureService", "contractorService", "alertService", "spinnerService", "mockIdTypes", "id", "name", "mockEpss", "mockGenders", "mockEducationLevels", "mockProfessions", "mockDepartments", "mockMunicipalities", "departmentId", "department", "mockLegalNatures", "mockContractor", "fullName", "idType", "idNumber", "personalEmail", "corporateEmail", "phone", "eps", "birthDate", "gender", "educationLevel", "profession", "lastObtainedDegree", "municipality", "legalNature", "beforeEach", "jasmine", "createSpyObj", "getAll", "and", "returnValue", "getAllByDepartmentId", "configureTestingModule", "imports", "providers", "provide", "useValue", "createComponent", "componentInstance", "it", "expect", "toBeTruthy", "ngOnInit", "show", "toHaveBeenCalled", "hide", "error", "toHaveBeenCalledWith", "close", "contractorForm", "get", "setValue", "municipalities", "toEqual", "value", "toBeNull", "bachillerLevel", "find", "el", "isProfessionEditable", "toBeFalse", "disabled", "toBeTrue", "bachillerProfession", "p", "toBe", "profesionalLevel", "enabled", "hasValidator", "required", "tecnicoLevel", "onEducationLevelChange", "isLastObtainedDegreeEnabled", "tecnologoLevel", "otherLevel", "resetTestingModule", "valid", "patchValue", "idTypeId", "legalNatureId", "personalEmailControl", "corporateEmailControl", "errors", "phoneControl", "testDate", "Date", "formattedDate", "toISOString", "split", "emptyPhone", "processedPhone", "undefined", "toBeUndefined", "calls", "reset", "spyOn", "console", "log", "any", "Object", "detail", "success", "toBeDefined"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-management\\components\\contractor-dialog\\contractor-dialog.component.spec.ts"], "sourcesContent": ["import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { ComponentFixture, TestBed } from '@angular/core/testing';\nimport { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { provideNativeDateAdapter } from '@angular/material/core';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { Contractor } from '@contractor-management/models/contractor.model';\nimport { ContractorService } from '@contractor-management/services/contractor.service';\nimport { EducationLevelService } from '@contractor-management/services/education-level.service';\nimport { EpsService } from '@contractor-management/services/eps.service';\nimport { GenderService } from '@contractor-management/services/gender.service';\nimport { LegalNatureService } from '@contractor-management/services/legal-nature.service';\nimport { ProfessionService } from '@contractor-management/services/profession.service';\nimport { Municipality } from '@shared/models/municipality.model';\nimport { AlertService } from '@shared/services/alert.service';\nimport { DepartmentService } from '@shared/services/department.service';\nimport { IDTypeService } from '@shared/services/id-type.service';\nimport { MunicipalityService } from '@shared/services/municipality.service';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { of, throwError } from 'rxjs';\nimport { ContractorDialogComponent } from './contractor-dialog.component';\n\ndescribe('ContractorDialogComponent', () => {\n  let component: ContractorDialogComponent;\n  let fixture: ComponentFixture<ContractorDialogComponent>;\n  let dialogRef: jasmine.SpyObj<MatDialogRef<ContractorDialogComponent>>;\n  let idTypeService: jasmine.SpyObj<IDTypeService>;\n  let epsService: jasmine.SpyObj<EpsService>;\n  let genderService: jasmine.SpyObj<GenderService>;\n  let educationLevelService: jasmine.SpyObj<EducationLevelService>;\n  let professionService: jasmine.SpyObj<ProfessionService>;\n  let departmentService: jasmine.SpyObj<DepartmentService>;\n  let municipalityService: jasmine.SpyObj<MunicipalityService>;\n  let legalNatureService: jasmine.SpyObj<LegalNatureService>;\n  let contractorService: jasmine.SpyObj<ContractorService>;\n  let alertService: jasmine.SpyObj<AlertService>;\n  let spinnerService: jasmine.SpyObj<NgxSpinnerService>;\n\n  const mockIdTypes = [\n    { id: 1, name: 'CC' },\n    { id: 2, name: 'CE' },\n  ];\n\n  const mockEpss = [\n    { id: 1, name: 'EPS 1' },\n    { id: 2, name: 'EPS 2' },\n  ];\n\n  const mockGenders = [\n    { id: 1, name: 'Male' },\n    { id: 2, name: 'Female' },\n  ];\n\n  const mockEducationLevels = [\n    { id: 1, name: 'BACHILLER' },\n    { id: 2, name: 'PROFESIONAL' },\n    { id: 3, name: 'TÉCNICO' },\n    { id: 4, name: 'TECNÓLOGO' },\n    { id: 5, name: 'OTRO' },\n  ];\n\n  const mockProfessions = [\n    { id: 1, name: 'BACHILLER' },\n    { id: 2, name: 'Engineer' },\n  ];\n\n  const mockDepartments = [\n    { id: 1, name: 'Department 1' },\n    { id: 2, name: 'Department 2' },\n  ];\n\n  const mockMunicipalities: Municipality[] = [\n    {\n      id: 1,\n      name: 'Municipality 1',\n      departmentId: 1,\n      department: mockDepartments[0],\n    },\n    {\n      id: 2,\n      name: 'Municipality 2',\n      departmentId: 1,\n      department: mockDepartments[0],\n    },\n  ];\n\n  const mockLegalNatures = [\n    { id: 1, name: 'Nature 1' },\n    { id: 2, name: 'Nature 2' },\n  ];\n\n  const mockContractor: Contractor = {\n    id: 1,\n    fullName: 'Test Contractor',\n    idType: mockIdTypes[0],\n    idNumber: 123456789,\n    personalEmail: '<EMAIL>',\n    corporateEmail: '<EMAIL>',\n    phone: 1234567890,\n    eps: mockEpss[0],\n    birthDate: '2000-01-01',\n    gender: mockGenders[0],\n    educationLevel: mockEducationLevels[0],\n    profession: mockProfessions[0],\n    lastObtainedDegree: '',\n    department: mockDepartments[0],\n    municipality: mockMunicipalities[0],\n    legalNature: mockLegalNatures[0],\n  };\n\n  beforeEach(() => {\n    dialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);\n    idTypeService = jasmine.createSpyObj('IDTypeService', ['getAll']);\n    epsService = jasmine.createSpyObj('EpsService', ['getAll']);\n    genderService = jasmine.createSpyObj('GenderService', ['getAll']);\n    educationLevelService = jasmine.createSpyObj('EducationLevelService', [\n      'getAll',\n    ]);\n    professionService = jasmine.createSpyObj('ProfessionService', ['getAll']);\n    departmentService = jasmine.createSpyObj('DepartmentService', ['getAll']);\n    municipalityService = jasmine.createSpyObj('MunicipalityService', [\n      'getAllByDepartmentId',\n    ]);\n    legalNatureService = jasmine.createSpyObj('LegalNatureService', ['getAll']);\n    contractorService = jasmine.createSpyObj('ContractorService', [\n      'create',\n      'update',\n    ]);\n    alertService = jasmine.createSpyObj('AlertService', ['success', 'error']);\n    spinnerService = jasmine.createSpyObj('NgxSpinnerService', [\n      'show',\n      'hide',\n    ]);\n\n    idTypeService.getAll.and.returnValue(of(mockIdTypes));\n    epsService.getAll.and.returnValue(of(mockEpss));\n    genderService.getAll.and.returnValue(of(mockGenders));\n    educationLevelService.getAll.and.returnValue(of(mockEducationLevels));\n    professionService.getAll.and.returnValue(of(mockProfessions));\n    departmentService.getAll.and.returnValue(of(mockDepartments));\n    municipalityService.getAllByDepartmentId.and.returnValue(\n      of(mockMunicipalities),\n    );\n    legalNatureService.getAll.and.returnValue(of(mockLegalNatures));\n\n    TestBed.configureTestingModule({\n      imports: [\n        ContractorDialogComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n        ReactiveFormsModule,\n      ],\n      providers: [\n        { provide: MatDialogRef, useValue: dialogRef },\n        { provide: MAT_DIALOG_DATA, useValue: {} },\n        { provide: IDTypeService, useValue: idTypeService },\n        { provide: EpsService, useValue: epsService },\n        { provide: GenderService, useValue: genderService },\n        { provide: EducationLevelService, useValue: educationLevelService },\n        { provide: ProfessionService, useValue: professionService },\n        { provide: DepartmentService, useValue: departmentService },\n        { provide: MunicipalityService, useValue: municipalityService },\n        { provide: LegalNatureService, useValue: legalNatureService },\n        { provide: ContractorService, useValue: contractorService },\n        { provide: AlertService, useValue: alertService },\n        { provide: NgxSpinnerService, useValue: spinnerService },\n        FormBuilder,\n        provideNativeDateAdapter(),\n      ],\n    });\n\n    fixture = TestBed.createComponent(ContractorDialogComponent);\n    component = fixture.componentInstance;\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should load all required data on init', () => {\n    component.ngOnInit();\n    expect(spinnerService.show).toHaveBeenCalled();\n    expect(idTypeService.getAll).toHaveBeenCalled();\n    expect(epsService.getAll).toHaveBeenCalled();\n    expect(genderService.getAll).toHaveBeenCalled();\n    expect(educationLevelService.getAll).toHaveBeenCalled();\n    expect(professionService.getAll).toHaveBeenCalled();\n    expect(departmentService.getAll).toHaveBeenCalled();\n    expect(legalNatureService.getAll).toHaveBeenCalled();\n    expect(spinnerService.hide).toHaveBeenCalled();\n  });\n\n  it('should handle error when loading initial data', () => {\n    idTypeService.getAll.and.returnValue(\n      throwError(() => ({ error: 'Error' })),\n    );\n    component.ngOnInit();\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al cargar los datos del formulario',\n    );\n    expect(dialogRef.close).toHaveBeenCalled();\n  });\n\n  it('should load municipalities when department is selected', () => {\n    component.ngOnInit();\n    component.contractorForm.get('departmentId')?.setValue(1);\n    expect(municipalityService.getAllByDepartmentId).toHaveBeenCalledWith(1);\n    expect(component.municipalities).toEqual(mockMunicipalities);\n  });\n\n  it('should clear municipalities when department is cleared', () => {\n    component.ngOnInit();\n    component.contractorForm.get('departmentId')?.setValue(1);\n    component.contractorForm.get('departmentId')?.setValue(null);\n    expect(component.municipalities).toEqual([]);\n    expect(component.contractorForm.get('municipalityId')?.value).toBeNull();\n  });\n\n  it('should handle education level change for BACHILLER', () => {\n    component.ngOnInit();\n    const bachillerLevel = mockEducationLevels.find(\n      (el) => el.name === 'BACHILLER',\n    );\n    component.contractorForm\n      .get('educationLevelId')\n      ?.setValue(bachillerLevel?.id);\n\n    expect(component.isProfessionEditable).toBeFalse();\n    expect(\n      component.contractorForm.get('lastObtainedDegree')?.disabled,\n    ).toBeTrue();\n    expect(component.contractorForm.get('professionId')?.disabled).toBeTrue();\n\n    const bachillerProfession = mockProfessions.find(\n      (p) => p.name === 'BACHILLER',\n    );\n    expect(component.contractorForm.get('professionId')?.value).toBe(\n      bachillerProfession?.id,\n    );\n  });\n\n  it('should handle education level change for PROFESIONAL', () => {\n    component.ngOnInit();\n    const profesionalLevel = mockEducationLevels.find(\n      (el) => el.name === 'PROFESIONAL',\n    );\n    component.contractorForm\n      .get('educationLevelId')\n      ?.setValue(profesionalLevel?.id);\n\n    expect(component.isProfessionEditable).toBeTrue();\n    expect(\n      component.contractorForm.get('lastObtainedDegree')?.disabled,\n    ).toBeTrue();\n    expect(component.contractorForm.get('professionId')?.enabled).toBeTrue();\n    expect(\n      component.contractorForm\n        .get('professionId')\n        ?.hasValidator(Validators.required),\n    ).toBeTrue();\n  });\n\n  it('should handle education level change for TÉCNICO', () => {\n    component.ngOnInit();\n    const tecnicoLevel = mockEducationLevels.find(\n      (el) => el.name === 'TÉCNICO',\n    );\n\n    component.onEducationLevelChange(tecnicoLevel!);\n\n    expect(component.isProfessionEditable).toBeFalse();\n    expect(component.isLastObtainedDegreeEnabled).toBeFalse();\n  });\n\n  it('should handle education level change for TECNÓLOGO', () => {\n    component.ngOnInit();\n    const tecnologoLevel = mockEducationLevels.find(\n      (el) => el.name === 'TECNÓLOGO',\n    );\n\n    component.onEducationLevelChange(tecnologoLevel!);\n\n    expect(component.isProfessionEditable).toBeFalse();\n    expect(component.isLastObtainedDegreeEnabled).toBeFalse();\n  });\n\n  it('should handle education level change for other education levels', () => {\n    component.ngOnInit();\n    const otherLevel = mockEducationLevels.find((el) => el.name === 'OTRO');\n    component.contractorForm.get('educationLevelId')?.setValue(otherLevel?.id);\n\n    expect(component.isProfessionEditable).toBeTrue();\n    expect(component.isLastObtainedDegreeEnabled).toBeFalse();\n    expect(\n      component.contractorForm.get('lastObtainedDegree')?.disabled,\n    ).toBeTrue();\n    expect(component.contractorForm.get('professionId')?.enabled).toBeTrue();\n    expect(\n      component.contractorForm\n        .get('professionId')\n        ?.hasValidator(Validators.required),\n    ).toBeFalse();\n  });\n\n  it('should handle education level change to null', () => {\n    component.ngOnInit();\n\n    component.contractorForm.get('educationLevelId')?.setValue(1);\n    component.contractorForm.get('educationLevelId')?.setValue(null);\n\n    expect(component.isProfessionEditable).toBeTrue();\n    expect(component.isLastObtainedDegreeEnabled).toBeFalse();\n    expect(\n      component.contractorForm.get('lastObtainedDegree')?.disabled,\n    ).toBeTrue();\n    expect(component.contractorForm.get('professionId')?.enabled).toBeTrue();\n    expect(component.contractorForm.get('professionId')?.value).toBeNull();\n  });\n\n  it('should patch form values when editing existing contractor', () => {\n    TestBed.resetTestingModule();\n    TestBed.configureTestingModule({\n      imports: [\n        ContractorDialogComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n        ReactiveFormsModule,\n      ],\n      providers: [\n        { provide: MatDialogRef, useValue: dialogRef },\n        { provide: MAT_DIALOG_DATA, useValue: mockContractor },\n        { provide: IDTypeService, useValue: idTypeService },\n        { provide: EpsService, useValue: epsService },\n        { provide: GenderService, useValue: genderService },\n        { provide: EducationLevelService, useValue: educationLevelService },\n        { provide: ProfessionService, useValue: professionService },\n        { provide: DepartmentService, useValue: departmentService },\n        { provide: MunicipalityService, useValue: municipalityService },\n        { provide: LegalNatureService, useValue: legalNatureService },\n        { provide: ContractorService, useValue: contractorService },\n        { provide: AlertService, useValue: alertService },\n        { provide: NgxSpinnerService, useValue: spinnerService },\n        FormBuilder,\n        provideNativeDateAdapter(),\n      ],\n    });\n\n    fixture = TestBed.createComponent(ContractorDialogComponent);\n    component = fixture.componentInstance;\n    component.ngOnInit();\n\n    expect(component.contractorForm.get('fullName')?.value).toBe(\n      mockContractor.fullName,\n    );\n    expect(component.contractorForm.get('idTypeId')?.value).toBe(\n      mockContractor.idType?.id,\n    );\n    expect(component.contractorForm.get('idNumber')?.value).toBe(\n      mockContractor.idNumber,\n    );\n    expect(component.contractorForm.get('personalEmail')?.value).toBe(\n      mockContractor.personalEmail,\n    );\n  });\n\n  it('should load municipalities when editing a contractor with department', () => {\n    TestBed.resetTestingModule();\n    TestBed.configureTestingModule({\n      imports: [\n        ContractorDialogComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n        ReactiveFormsModule,\n      ],\n      providers: [\n        { provide: MatDialogRef, useValue: dialogRef },\n        { provide: MAT_DIALOG_DATA, useValue: mockContractor },\n        { provide: IDTypeService, useValue: idTypeService },\n        { provide: EpsService, useValue: epsService },\n        { provide: GenderService, useValue: genderService },\n        { provide: EducationLevelService, useValue: educationLevelService },\n        { provide: ProfessionService, useValue: professionService },\n        { provide: DepartmentService, useValue: departmentService },\n        { provide: MunicipalityService, useValue: municipalityService },\n        { provide: LegalNatureService, useValue: legalNatureService },\n        { provide: ContractorService, useValue: contractorService },\n        { provide: AlertService, useValue: alertService },\n        { provide: NgxSpinnerService, useValue: spinnerService },\n        FormBuilder,\n        provideNativeDateAdapter(),\n      ],\n    });\n\n    fixture = TestBed.createComponent(ContractorDialogComponent);\n    component = fixture.componentInstance;\n    component.ngOnInit();\n\n    expect(municipalityService.getAllByDepartmentId).toHaveBeenCalledWith(\n      mockContractor.department!.id,\n    );\n  });\n\n  it('should validate required fields', () => {\n    component.ngOnInit();\n    expect(component.contractorForm.valid).toBeFalse();\n\n    component.contractorForm.patchValue({\n      fullName: 'Test Name',\n      idTypeId: 1,\n      idNumber: '123456789',\n      personalEmail: '<EMAIL>',\n      legalNatureId: 1,\n    });\n\n    expect(component.contractorForm.valid).toBeTrue();\n  });\n\n  it('should validate email format', () => {\n    component.ngOnInit();\n    const personalEmailControl = component.contractorForm.get('personalEmail');\n    const corporateEmailControl =\n      component.contractorForm.get('corporateEmail');\n\n    personalEmailControl?.setValue('invalid-email');\n    expect(personalEmailControl?.errors?.['email']).toBeTruthy();\n\n    personalEmailControl?.setValue('<EMAIL>');\n    expect(personalEmailControl?.errors).toBeNull();\n\n    corporateEmailControl?.setValue('invalid-email');\n    expect(corporateEmailControl?.errors?.['email']).toBeTruthy();\n\n    corporateEmailControl?.setValue('<EMAIL>');\n    expect(corporateEmailControl?.errors).toBeNull();\n  });\n\n  it('should validate phone number format', () => {\n    component.ngOnInit();\n    const phoneControl = component.contractorForm.get('phone');\n\n    phoneControl?.setValue('abc123');\n    expect(phoneControl?.errors?.['pattern']).toBeTruthy();\n\n    phoneControl?.setValue('123456789');\n    expect(phoneControl?.errors).toBeNull();\n  });\n\n  it('should handle error when loading municipalities', () => {\n    component.ngOnInit();\n    municipalityService.getAllByDepartmentId.and.returnValue(\n      throwError(() => ({ error: 'Error loading municipalities' })),\n    );\n\n    component.contractorForm.get('departmentId')?.setValue(1);\n\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al cargar municipios',\n    );\n  });\n\n  describe('onSubmit', () => {\n    it('should validate form before submission', () => {\n      component.ngOnInit();\n\n      component.contractorForm.patchValue({ fullName: '' });\n      expect(component.contractorForm.valid).toBeFalse();\n\n      component.contractorForm.patchValue({\n        fullName: 'Test Name',\n        idTypeId: 1,\n        idNumber: '123456789',\n        personalEmail: '<EMAIL>',\n        legalNatureId: 1,\n      });\n      expect(component.contractorForm.valid).toBeTrue();\n    });\n\n    it('should format date for API submission', () => {\n      component.ngOnInit();\n      const testDate = new Date('2023-05-15');\n      const formattedDate = testDate.toISOString().split('T')[0];\n      expect(formattedDate).toBe('2023-05-15');\n    });\n\n    it('should handle empty phone as undefined', () => {\n      component.ngOnInit();\n      const emptyPhone = '';\n      const processedPhone = emptyPhone || undefined;\n      expect(processedPhone).toBeUndefined();\n    });\n  });\n\n  describe('Error handling', () => {\n    it('should show appropriate error messages', () => {\n      component.ngOnInit();\n\n      alertService.error.calls.reset();\n\n      alertService.error('Número de cédula ya se encuentra registrado');\n      expect(alertService.error).toHaveBeenCalledWith(\n        'Número de cédula ya se encuentra registrado',\n      );\n\n      alertService.error.calls.reset();\n\n      alertService.error('El correo institucional ya se encuentra registrado');\n      expect(alertService.error).toHaveBeenCalledWith(\n        'El correo institucional ya se encuentra registrado',\n      );\n\n      alertService.error.calls.reset();\n\n      alertService.error('Error al procesar la solicitud');\n      expect(alertService.error).toHaveBeenCalledWith(\n        'Error al procesar la solicitud',\n      );\n\n      alertService.error('Error al editar contratista');\n      expect(alertService.error).toHaveBeenCalledWith(\n        'Error al editar contratista',\n      );\n\n      alertService.error('Error al crear contratista');\n      expect(alertService.error).toHaveBeenCalledWith(\n        'Error al crear contratista',\n      );\n    });\n\n    it('should log messages to console', () => {\n      component.ngOnInit();\n      spyOn(console, 'log');\n\n      console.log('contractor', { id: 1, fullName: 'Test Name' });\n      expect(console.log).toHaveBeenCalledWith(\n        'contractor',\n        jasmine.any(Object),\n      );\n\n      console.log('contractor error', { error: { detail: 'Error message' } });\n      expect(console.log).toHaveBeenCalledWith(\n        'contractor error',\n        jasmine.any(Object),\n      );\n    });\n  });\n\n  describe('Success handling', () => {\n    it('should show success messages', () => {\n      component.ngOnInit();\n\n      alertService.success.calls.reset();\n\n      alertService.success('Contratista creado exitosamente');\n      expect(alertService.success).toHaveBeenCalledWith(\n        'Contratista creado exitosamente',\n      );\n\n      alertService.success.calls.reset();\n\n      alertService.success('Contratista editado exitosamente');\n      expect(alertService.success).toHaveBeenCalledWith(\n        'Contratista editado exitosamente',\n      );\n    });\n  });\n\n  it('should have a method to close dialog', () => {\n    expect(dialogRef.close).toBeDefined();\n  });\n\n  describe('Date formatting', () => {\n    it('should correctly format date for API submission', () => {\n      component.ngOnInit();\n\n      const testDate = new Date('2023-05-15');\n      component.contractorForm.patchValue({\n        fullName: 'Test Name',\n        idTypeId: 1,\n        idNumber: '123456789',\n        personalEmail: '<EMAIL>',\n        legalNatureId: 1,\n        birthDate: testDate,\n      });\n\n      const formattedDate = testDate.toISOString().split('T')[0];\n\n      expect(formattedDate).toBe('2023-05-15');\n    });\n  });\n\n  describe('Error handling', () => {\n    it('should show appropriate error messages', () => {\n      alertService.error.calls.reset();\n\n      alertService.error('Número de cédula ya se encuentra registrado');\n      expect(alertService.error).toHaveBeenCalledWith(\n        'Número de cédula ya se encuentra registrado',\n      );\n\n      alertService.error.calls.reset();\n\n      alertService.error('El correo institucional ya se encuentra registrado');\n      expect(alertService.error).toHaveBeenCalledWith(\n        'El correo institucional ya se encuentra registrado',\n      );\n\n      alertService.error.calls.reset();\n\n      alertService.error('Error al procesar la solicitud');\n      expect(alertService.error).toHaveBeenCalledWith(\n        'Error al procesar la solicitud',\n      );\n    });\n  });\n});\n"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,8BAA8B;AACtE,SAA2BC,OAAO,QAAQ,uBAAuB;AACjE,SAASC,WAAW,EAAEC,mBAAmB,EAAEC,UAAU,QAAQ,gBAAgB;AAC7E,SAASC,wBAAwB,QAAQ,wBAAwB;AACjE,SAASC,eAAe,EAAEC,YAAY,QAAQ,0BAA0B;AACxE,SAASC,uBAAuB,QAAQ,sCAAsC;AAE9E,SAASC,iBAAiB,QAAQ,oDAAoD;AACtF,SAASC,qBAAqB,QAAQ,yDAAyD;AAC/F,SAASC,UAAU,QAAQ,6CAA6C;AACxE,SAASC,aAAa,QAAQ,gDAAgD;AAC9E,SAASC,kBAAkB,QAAQ,sDAAsD;AACzF,SAASC,iBAAiB,QAAQ,oDAAoD;AAEtF,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,mBAAmB,QAAQ,uCAAuC;AAC3E,SAASC,iBAAiB,QAAQ,aAAa;AAC/C,SAASC,EAAE,EAAEC,UAAU,QAAQ,MAAM;AACrC,SAASC,yBAAyB,QAAQ,+BAA+B;AAEzEC,QAAQ,CAAC,2BAA2B,EAAE,MAAK;EACzC,IAAIC,SAAoC;EACxC,IAAIC,OAAoD;EACxD,IAAIC,SAAkE;EACtE,IAAIC,aAA4C;EAChD,IAAIC,UAAsC;EAC1C,IAAIC,aAA4C;EAChD,IAAIC,qBAA4D;EAChE,IAAIC,iBAAoD;EACxD,IAAIC,iBAAoD;EACxD,IAAIC,mBAAwD;EAC5D,IAAIC,kBAAsD;EAC1D,IAAIC,iBAAoD;EACxD,IAAIC,YAA0C;EAC9C,IAAIC,cAAiD;EAErD,MAAMC,WAAW,GAAG,CAClB;IAAEC,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAI,CAAE,EACrB;IAAED,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAI,CAAE,CACtB;EAED,MAAMC,QAAQ,GAAG,CACf;IAAEF,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAO,CAAE,EACxB;IAAED,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAO,CAAE,CACzB;EAED,MAAME,WAAW,GAAG,CAClB;IAAEH,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAM,CAAE,EACvB;IAAED,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAQ,CAAE,CAC1B;EAED,MAAMG,mBAAmB,GAAG,CAC1B;IAAEJ,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAW,CAAE,EAC5B;IAAED,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAa,CAAE,EAC9B;IAAED,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAS,CAAE,EAC1B;IAAED,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAW,CAAE,EAC5B;IAAED,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAM,CAAE,CACxB;EAED,MAAMI,eAAe,GAAG,CACtB;IAAEL,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAW,CAAE,EAC5B;IAAED,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAU,CAAE,CAC5B;EAED,MAAMK,eAAe,GAAG,CACtB;IAAEN,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAc,CAAE,EAC/B;IAAED,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAc,CAAE,CAChC;EAED,MAAMM,kBAAkB,GAAmB,CACzC;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBO,YAAY,EAAE,CAAC;IACfC,UAAU,EAAEH,eAAe,CAAC,CAAC;GAC9B,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBO,YAAY,EAAE,CAAC;IACfC,UAAU,EAAEH,eAAe,CAAC,CAAC;GAC9B,CACF;EAED,MAAMI,gBAAgB,GAAG,CACvB;IAAEV,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAU,CAAE,EAC3B;IAAED,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAU,CAAE,CAC5B;EAED,MAAMU,cAAc,GAAe;IACjCX,EAAE,EAAE,CAAC;IACLY,QAAQ,EAAE,iBAAiB;IAC3BC,MAAM,EAAEd,WAAW,CAAC,CAAC,CAAC;IACtBe,QAAQ,EAAE,SAAS;IACnBC,aAAa,EAAE,kBAAkB;IACjCC,cAAc,EAAE,eAAe;IAC/BC,KAAK,EAAE,UAAU;IACjBC,GAAG,EAAEhB,QAAQ,CAAC,CAAC,CAAC;IAChBiB,SAAS,EAAE,YAAY;IACvBC,MAAM,EAAEjB,WAAW,CAAC,CAAC,CAAC;IACtBkB,cAAc,EAAEjB,mBAAmB,CAAC,CAAC,CAAC;IACtCkB,UAAU,EAAEjB,eAAe,CAAC,CAAC,CAAC;IAC9BkB,kBAAkB,EAAE,EAAE;IACtBd,UAAU,EAAEH,eAAe,CAAC,CAAC,CAAC;IAC9BkB,YAAY,EAAEjB,kBAAkB,CAAC,CAAC,CAAC;IACnCkB,WAAW,EAAEf,gBAAgB,CAAC,CAAC;GAChC;EAEDgB,UAAU,CAAC,MAAK;IACdvC,SAAS,GAAGwC,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC;IAC3DxC,aAAa,GAAGuC,OAAO,CAACC,YAAY,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,CAAC;IACjEvC,UAAU,GAAGsC,OAAO,CAACC,YAAY,CAAC,YAAY,EAAE,CAAC,QAAQ,CAAC,CAAC;IAC3DtC,aAAa,GAAGqC,OAAO,CAACC,YAAY,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,CAAC;IACjErC,qBAAqB,GAAGoC,OAAO,CAACC,YAAY,CAAC,uBAAuB,EAAE,CACpE,QAAQ,CACT,CAAC;IACFpC,iBAAiB,GAAGmC,OAAO,CAACC,YAAY,CAAC,mBAAmB,EAAE,CAAC,QAAQ,CAAC,CAAC;IACzEnC,iBAAiB,GAAGkC,OAAO,CAACC,YAAY,CAAC,mBAAmB,EAAE,CAAC,QAAQ,CAAC,CAAC;IACzElC,mBAAmB,GAAGiC,OAAO,CAACC,YAAY,CAAC,qBAAqB,EAAE,CAChE,sBAAsB,CACvB,CAAC;IACFjC,kBAAkB,GAAGgC,OAAO,CAACC,YAAY,CAAC,oBAAoB,EAAE,CAAC,QAAQ,CAAC,CAAC;IAC3EhC,iBAAiB,GAAG+B,OAAO,CAACC,YAAY,CAAC,mBAAmB,EAAE,CAC5D,QAAQ,EACR,QAAQ,CACT,CAAC;IACF/B,YAAY,GAAG8B,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACzE9B,cAAc,GAAG6B,OAAO,CAACC,YAAY,CAAC,mBAAmB,EAAE,CACzD,MAAM,EACN,MAAM,CACP,CAAC;IAEFxC,aAAa,CAACyC,MAAM,CAACC,GAAG,CAACC,WAAW,CAAClD,EAAE,CAACkB,WAAW,CAAC,CAAC;IACrDV,UAAU,CAACwC,MAAM,CAACC,GAAG,CAACC,WAAW,CAAClD,EAAE,CAACqB,QAAQ,CAAC,CAAC;IAC/CZ,aAAa,CAACuC,MAAM,CAACC,GAAG,CAACC,WAAW,CAAClD,EAAE,CAACsB,WAAW,CAAC,CAAC;IACrDZ,qBAAqB,CAACsC,MAAM,CAACC,GAAG,CAACC,WAAW,CAAClD,EAAE,CAACuB,mBAAmB,CAAC,CAAC;IACrEZ,iBAAiB,CAACqC,MAAM,CAACC,GAAG,CAACC,WAAW,CAAClD,EAAE,CAACwB,eAAe,CAAC,CAAC;IAC7DZ,iBAAiB,CAACoC,MAAM,CAACC,GAAG,CAACC,WAAW,CAAClD,EAAE,CAACyB,eAAe,CAAC,CAAC;IAC7DZ,mBAAmB,CAACsC,oBAAoB,CAACF,GAAG,CAACC,WAAW,CACtDlD,EAAE,CAAC0B,kBAAkB,CAAC,CACvB;IACDZ,kBAAkB,CAACkC,MAAM,CAACC,GAAG,CAACC,WAAW,CAAClD,EAAE,CAAC6B,gBAAgB,CAAC,CAAC;IAE/DhD,OAAO,CAACuE,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPnD,yBAAyB,EACzBtB,uBAAuB,EACvBQ,uBAAuB,EACvBL,mBAAmB,CACpB;MACDuE,SAAS,EAAE,CACT;QAAEC,OAAO,EAAEpE,YAAY;QAAEqE,QAAQ,EAAElD;MAAS,CAAE,EAC9C;QAAEiD,OAAO,EAAErE,eAAe;QAAEsE,QAAQ,EAAE;MAAE,CAAE,EAC1C;QAAED,OAAO,EAAE1D,aAAa;QAAE2D,QAAQ,EAAEjD;MAAa,CAAE,EACnD;QAAEgD,OAAO,EAAEhE,UAAU;QAAEiE,QAAQ,EAAEhD;MAAU,CAAE,EAC7C;QAAE+C,OAAO,EAAE/D,aAAa;QAAEgE,QAAQ,EAAE/C;MAAa,CAAE,EACnD;QAAE8C,OAAO,EAAEjE,qBAAqB;QAAEkE,QAAQ,EAAE9C;MAAqB,CAAE,EACnE;QAAE6C,OAAO,EAAE7D,iBAAiB;QAAE8D,QAAQ,EAAE7C;MAAiB,CAAE,EAC3D;QAAE4C,OAAO,EAAE3D,iBAAiB;QAAE4D,QAAQ,EAAE5C;MAAiB,CAAE,EAC3D;QAAE2C,OAAO,EAAEzD,mBAAmB;QAAE0D,QAAQ,EAAE3C;MAAmB,CAAE,EAC/D;QAAE0C,OAAO,EAAE9D,kBAAkB;QAAE+D,QAAQ,EAAE1C;MAAkB,CAAE,EAC7D;QAAEyC,OAAO,EAAElE,iBAAiB;QAAEmE,QAAQ,EAAEzC;MAAiB,CAAE,EAC3D;QAAEwC,OAAO,EAAE5D,YAAY;QAAE6D,QAAQ,EAAExC;MAAY,CAAE,EACjD;QAAEuC,OAAO,EAAExD,iBAAiB;QAAEyD,QAAQ,EAAEvC;MAAc,CAAE,EACxDnC,WAAW,EACXG,wBAAwB,EAAE;KAE7B,CAAC;IAEFoB,OAAO,GAAGxB,OAAO,CAAC4E,eAAe,CAACvD,yBAAyB,CAAC;IAC5DE,SAAS,GAAGC,OAAO,CAACqD,iBAAiB;EACvC,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACxD,SAAS,CAAC,CAACyD,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,uCAAuC,EAAE,MAAK;IAC/CvD,SAAS,CAAC0D,QAAQ,EAAE;IACpBF,MAAM,CAAC3C,cAAc,CAAC8C,IAAI,CAAC,CAACC,gBAAgB,EAAE;IAC9CJ,MAAM,CAACrD,aAAa,CAACyC,MAAM,CAAC,CAACgB,gBAAgB,EAAE;IAC/CJ,MAAM,CAACpD,UAAU,CAACwC,MAAM,CAAC,CAACgB,gBAAgB,EAAE;IAC5CJ,MAAM,CAACnD,aAAa,CAACuC,MAAM,CAAC,CAACgB,gBAAgB,EAAE;IAC/CJ,MAAM,CAAClD,qBAAqB,CAACsC,MAAM,CAAC,CAACgB,gBAAgB,EAAE;IACvDJ,MAAM,CAACjD,iBAAiB,CAACqC,MAAM,CAAC,CAACgB,gBAAgB,EAAE;IACnDJ,MAAM,CAAChD,iBAAiB,CAACoC,MAAM,CAAC,CAACgB,gBAAgB,EAAE;IACnDJ,MAAM,CAAC9C,kBAAkB,CAACkC,MAAM,CAAC,CAACgB,gBAAgB,EAAE;IACpDJ,MAAM,CAAC3C,cAAc,CAACgD,IAAI,CAAC,CAACD,gBAAgB,EAAE;EAChD,CAAC,CAAC;EAEFL,EAAE,CAAC,+CAA+C,EAAE,MAAK;IACvDpD,aAAa,CAACyC,MAAM,CAACC,GAAG,CAACC,WAAW,CAClCjD,UAAU,CAAC,OAAO;MAAEiE,KAAK,EAAE;IAAO,CAAE,CAAC,CAAC,CACvC;IACD9D,SAAS,CAAC0D,QAAQ,EAAE;IACpBF,MAAM,CAAC5C,YAAY,CAACkD,KAAK,CAAC,CAACC,oBAAoB,CAC7C,0CAA0C,CAC3C;IACDP,MAAM,CAACtD,SAAS,CAAC8D,KAAK,CAAC,CAACJ,gBAAgB,EAAE;EAC5C,CAAC,CAAC;EAEFL,EAAE,CAAC,wDAAwD,EAAE,MAAK;IAChEvD,SAAS,CAAC0D,QAAQ,EAAE;IACpB1D,SAAS,CAACiE,cAAc,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAAC;IACzDX,MAAM,CAAC/C,mBAAmB,CAACsC,oBAAoB,CAAC,CAACgB,oBAAoB,CAAC,CAAC,CAAC;IACxEP,MAAM,CAACxD,SAAS,CAACoE,cAAc,CAAC,CAACC,OAAO,CAAC/C,kBAAkB,CAAC;EAC9D,CAAC,CAAC;EAEFiC,EAAE,CAAC,wDAAwD,EAAE,MAAK;IAChEvD,SAAS,CAAC0D,QAAQ,EAAE;IACpB1D,SAAS,CAACiE,cAAc,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAAC;IACzDnE,SAAS,CAACiE,cAAc,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEC,QAAQ,CAAC,IAAI,CAAC;IAC5DX,MAAM,CAACxD,SAAS,CAACoE,cAAc,CAAC,CAACC,OAAO,CAAC,EAAE,CAAC;IAC5Cb,MAAM,CAACxD,SAAS,CAACiE,cAAc,CAACC,GAAG,CAAC,gBAAgB,CAAC,EAAEI,KAAK,CAAC,CAACC,QAAQ,EAAE;EAC1E,CAAC,CAAC;EAEFhB,EAAE,CAAC,oDAAoD,EAAE,MAAK;IAC5DvD,SAAS,CAAC0D,QAAQ,EAAE;IACpB,MAAMc,cAAc,GAAGrD,mBAAmB,CAACsD,IAAI,CAC5CC,EAAE,IAAKA,EAAE,CAAC1D,IAAI,KAAK,WAAW,CAChC;IACDhB,SAAS,CAACiE,cAAc,CACrBC,GAAG,CAAC,kBAAkB,CAAC,EACtBC,QAAQ,CAACK,cAAc,EAAEzD,EAAE,CAAC;IAEhCyC,MAAM,CAACxD,SAAS,CAAC2E,oBAAoB,CAAC,CAACC,SAAS,EAAE;IAClDpB,MAAM,CACJxD,SAAS,CAACiE,cAAc,CAACC,GAAG,CAAC,oBAAoB,CAAC,EAAEW,QAAQ,CAC7D,CAACC,QAAQ,EAAE;IACZtB,MAAM,CAACxD,SAAS,CAACiE,cAAc,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEW,QAAQ,CAAC,CAACC,QAAQ,EAAE;IAEzE,MAAMC,mBAAmB,GAAG3D,eAAe,CAACqD,IAAI,CAC7CO,CAAC,IAAKA,CAAC,CAAChE,IAAI,KAAK,WAAW,CAC9B;IACDwC,MAAM,CAACxD,SAAS,CAACiE,cAAc,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEI,KAAK,CAAC,CAACW,IAAI,CAC9DF,mBAAmB,EAAEhE,EAAE,CACxB;EACH,CAAC,CAAC;EAEFwC,EAAE,CAAC,sDAAsD,EAAE,MAAK;IAC9DvD,SAAS,CAAC0D,QAAQ,EAAE;IACpB,MAAMwB,gBAAgB,GAAG/D,mBAAmB,CAACsD,IAAI,CAC9CC,EAAE,IAAKA,EAAE,CAAC1D,IAAI,KAAK,aAAa,CAClC;IACDhB,SAAS,CAACiE,cAAc,CACrBC,GAAG,CAAC,kBAAkB,CAAC,EACtBC,QAAQ,CAACe,gBAAgB,EAAEnE,EAAE,CAAC;IAElCyC,MAAM,CAACxD,SAAS,CAAC2E,oBAAoB,CAAC,CAACG,QAAQ,EAAE;IACjDtB,MAAM,CACJxD,SAAS,CAACiE,cAAc,CAACC,GAAG,CAAC,oBAAoB,CAAC,EAAEW,QAAQ,CAC7D,CAACC,QAAQ,EAAE;IACZtB,MAAM,CAACxD,SAAS,CAACiE,cAAc,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEiB,OAAO,CAAC,CAACL,QAAQ,EAAE;IACxEtB,MAAM,CACJxD,SAAS,CAACiE,cAAc,CACrBC,GAAG,CAAC,cAAc,CAAC,EAClBkB,YAAY,CAACxG,UAAU,CAACyG,QAAQ,CAAC,CACtC,CAACP,QAAQ,EAAE;EACd,CAAC,CAAC;EAEFvB,EAAE,CAAC,kDAAkD,EAAE,MAAK;IAC1DvD,SAAS,CAAC0D,QAAQ,EAAE;IACpB,MAAM4B,YAAY,GAAGnE,mBAAmB,CAACsD,IAAI,CAC1CC,EAAE,IAAKA,EAAE,CAAC1D,IAAI,KAAK,SAAS,CAC9B;IAEDhB,SAAS,CAACuF,sBAAsB,CAACD,YAAa,CAAC;IAE/C9B,MAAM,CAACxD,SAAS,CAAC2E,oBAAoB,CAAC,CAACC,SAAS,EAAE;IAClDpB,MAAM,CAACxD,SAAS,CAACwF,2BAA2B,CAAC,CAACZ,SAAS,EAAE;EAC3D,CAAC,CAAC;EAEFrB,EAAE,CAAC,oDAAoD,EAAE,MAAK;IAC5DvD,SAAS,CAAC0D,QAAQ,EAAE;IACpB,MAAM+B,cAAc,GAAGtE,mBAAmB,CAACsD,IAAI,CAC5CC,EAAE,IAAKA,EAAE,CAAC1D,IAAI,KAAK,WAAW,CAChC;IAEDhB,SAAS,CAACuF,sBAAsB,CAACE,cAAe,CAAC;IAEjDjC,MAAM,CAACxD,SAAS,CAAC2E,oBAAoB,CAAC,CAACC,SAAS,EAAE;IAClDpB,MAAM,CAACxD,SAAS,CAACwF,2BAA2B,CAAC,CAACZ,SAAS,EAAE;EAC3D,CAAC,CAAC;EAEFrB,EAAE,CAAC,iEAAiE,EAAE,MAAK;IACzEvD,SAAS,CAAC0D,QAAQ,EAAE;IACpB,MAAMgC,UAAU,GAAGvE,mBAAmB,CAACsD,IAAI,CAAEC,EAAE,IAAKA,EAAE,CAAC1D,IAAI,KAAK,MAAM,CAAC;IACvEhB,SAAS,CAACiE,cAAc,CAACC,GAAG,CAAC,kBAAkB,CAAC,EAAEC,QAAQ,CAACuB,UAAU,EAAE3E,EAAE,CAAC;IAE1EyC,MAAM,CAACxD,SAAS,CAAC2E,oBAAoB,CAAC,CAACG,QAAQ,EAAE;IACjDtB,MAAM,CAACxD,SAAS,CAACwF,2BAA2B,CAAC,CAACZ,SAAS,EAAE;IACzDpB,MAAM,CACJxD,SAAS,CAACiE,cAAc,CAACC,GAAG,CAAC,oBAAoB,CAAC,EAAEW,QAAQ,CAC7D,CAACC,QAAQ,EAAE;IACZtB,MAAM,CAACxD,SAAS,CAACiE,cAAc,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEiB,OAAO,CAAC,CAACL,QAAQ,EAAE;IACxEtB,MAAM,CACJxD,SAAS,CAACiE,cAAc,CACrBC,GAAG,CAAC,cAAc,CAAC,EAClBkB,YAAY,CAACxG,UAAU,CAACyG,QAAQ,CAAC,CACtC,CAACT,SAAS,EAAE;EACf,CAAC,CAAC;EAEFrB,EAAE,CAAC,8CAA8C,EAAE,MAAK;IACtDvD,SAAS,CAAC0D,QAAQ,EAAE;IAEpB1D,SAAS,CAACiE,cAAc,CAACC,GAAG,CAAC,kBAAkB,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAAC;IAC7DnE,SAAS,CAACiE,cAAc,CAACC,GAAG,CAAC,kBAAkB,CAAC,EAAEC,QAAQ,CAAC,IAAI,CAAC;IAEhEX,MAAM,CAACxD,SAAS,CAAC2E,oBAAoB,CAAC,CAACG,QAAQ,EAAE;IACjDtB,MAAM,CAACxD,SAAS,CAACwF,2BAA2B,CAAC,CAACZ,SAAS,EAAE;IACzDpB,MAAM,CACJxD,SAAS,CAACiE,cAAc,CAACC,GAAG,CAAC,oBAAoB,CAAC,EAAEW,QAAQ,CAC7D,CAACC,QAAQ,EAAE;IACZtB,MAAM,CAACxD,SAAS,CAACiE,cAAc,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEiB,OAAO,CAAC,CAACL,QAAQ,EAAE;IACxEtB,MAAM,CAACxD,SAAS,CAACiE,cAAc,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEI,KAAK,CAAC,CAACC,QAAQ,EAAE;EACxE,CAAC,CAAC;EAEFhB,EAAE,CAAC,2DAA2D,EAAE,MAAK;IACnE9E,OAAO,CAACkH,kBAAkB,EAAE;IAC5BlH,OAAO,CAACuE,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPnD,yBAAyB,EACzBtB,uBAAuB,EACvBQ,uBAAuB,EACvBL,mBAAmB,CACpB;MACDuE,SAAS,EAAE,CACT;QAAEC,OAAO,EAAEpE,YAAY;QAAEqE,QAAQ,EAAElD;MAAS,CAAE,EAC9C;QAAEiD,OAAO,EAAErE,eAAe;QAAEsE,QAAQ,EAAE1B;MAAc,CAAE,EACtD;QAAEyB,OAAO,EAAE1D,aAAa;QAAE2D,QAAQ,EAAEjD;MAAa,CAAE,EACnD;QAAEgD,OAAO,EAAEhE,UAAU;QAAEiE,QAAQ,EAAEhD;MAAU,CAAE,EAC7C;QAAE+C,OAAO,EAAE/D,aAAa;QAAEgE,QAAQ,EAAE/C;MAAa,CAAE,EACnD;QAAE8C,OAAO,EAAEjE,qBAAqB;QAAEkE,QAAQ,EAAE9C;MAAqB,CAAE,EACnE;QAAE6C,OAAO,EAAE7D,iBAAiB;QAAE8D,QAAQ,EAAE7C;MAAiB,CAAE,EAC3D;QAAE4C,OAAO,EAAE3D,iBAAiB;QAAE4D,QAAQ,EAAE5C;MAAiB,CAAE,EAC3D;QAAE2C,OAAO,EAAEzD,mBAAmB;QAAE0D,QAAQ,EAAE3C;MAAmB,CAAE,EAC/D;QAAE0C,OAAO,EAAE9D,kBAAkB;QAAE+D,QAAQ,EAAE1C;MAAkB,CAAE,EAC7D;QAAEyC,OAAO,EAAElE,iBAAiB;QAAEmE,QAAQ,EAAEzC;MAAiB,CAAE,EAC3D;QAAEwC,OAAO,EAAE5D,YAAY;QAAE6D,QAAQ,EAAExC;MAAY,CAAE,EACjD;QAAEuC,OAAO,EAAExD,iBAAiB;QAAEyD,QAAQ,EAAEvC;MAAc,CAAE,EACxDnC,WAAW,EACXG,wBAAwB,EAAE;KAE7B,CAAC;IAEFoB,OAAO,GAAGxB,OAAO,CAAC4E,eAAe,CAACvD,yBAAyB,CAAC;IAC5DE,SAAS,GAAGC,OAAO,CAACqD,iBAAiB;IACrCtD,SAAS,CAAC0D,QAAQ,EAAE;IAEpBF,MAAM,CAACxD,SAAS,CAACiE,cAAc,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEI,KAAK,CAAC,CAACW,IAAI,CAC1DvD,cAAc,CAACC,QAAQ,CACxB;IACD6B,MAAM,CAACxD,SAAS,CAACiE,cAAc,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEI,KAAK,CAAC,CAACW,IAAI,CAC1DvD,cAAc,CAACE,MAAM,EAAEb,EAAE,CAC1B;IACDyC,MAAM,CAACxD,SAAS,CAACiE,cAAc,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEI,KAAK,CAAC,CAACW,IAAI,CAC1DvD,cAAc,CAACG,QAAQ,CACxB;IACD2B,MAAM,CAACxD,SAAS,CAACiE,cAAc,CAACC,GAAG,CAAC,eAAe,CAAC,EAAEI,KAAK,CAAC,CAACW,IAAI,CAC/DvD,cAAc,CAACI,aAAa,CAC7B;EACH,CAAC,CAAC;EAEFyB,EAAE,CAAC,sEAAsE,EAAE,MAAK;IAC9E9E,OAAO,CAACkH,kBAAkB,EAAE;IAC5BlH,OAAO,CAACuE,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPnD,yBAAyB,EACzBtB,uBAAuB,EACvBQ,uBAAuB,EACvBL,mBAAmB,CACpB;MACDuE,SAAS,EAAE,CACT;QAAEC,OAAO,EAAEpE,YAAY;QAAEqE,QAAQ,EAAElD;MAAS,CAAE,EAC9C;QAAEiD,OAAO,EAAErE,eAAe;QAAEsE,QAAQ,EAAE1B;MAAc,CAAE,EACtD;QAAEyB,OAAO,EAAE1D,aAAa;QAAE2D,QAAQ,EAAEjD;MAAa,CAAE,EACnD;QAAEgD,OAAO,EAAEhE,UAAU;QAAEiE,QAAQ,EAAEhD;MAAU,CAAE,EAC7C;QAAE+C,OAAO,EAAE/D,aAAa;QAAEgE,QAAQ,EAAE/C;MAAa,CAAE,EACnD;QAAE8C,OAAO,EAAEjE,qBAAqB;QAAEkE,QAAQ,EAAE9C;MAAqB,CAAE,EACnE;QAAE6C,OAAO,EAAE7D,iBAAiB;QAAE8D,QAAQ,EAAE7C;MAAiB,CAAE,EAC3D;QAAE4C,OAAO,EAAE3D,iBAAiB;QAAE4D,QAAQ,EAAE5C;MAAiB,CAAE,EAC3D;QAAE2C,OAAO,EAAEzD,mBAAmB;QAAE0D,QAAQ,EAAE3C;MAAmB,CAAE,EAC/D;QAAE0C,OAAO,EAAE9D,kBAAkB;QAAE+D,QAAQ,EAAE1C;MAAkB,CAAE,EAC7D;QAAEyC,OAAO,EAAElE,iBAAiB;QAAEmE,QAAQ,EAAEzC;MAAiB,CAAE,EAC3D;QAAEwC,OAAO,EAAE5D,YAAY;QAAE6D,QAAQ,EAAExC;MAAY,CAAE,EACjD;QAAEuC,OAAO,EAAExD,iBAAiB;QAAEyD,QAAQ,EAAEvC;MAAc,CAAE,EACxDnC,WAAW,EACXG,wBAAwB,EAAE;KAE7B,CAAC;IAEFoB,OAAO,GAAGxB,OAAO,CAAC4E,eAAe,CAACvD,yBAAyB,CAAC;IAC5DE,SAAS,GAAGC,OAAO,CAACqD,iBAAiB;IACrCtD,SAAS,CAAC0D,QAAQ,EAAE;IAEpBF,MAAM,CAAC/C,mBAAmB,CAACsC,oBAAoB,CAAC,CAACgB,oBAAoB,CACnErC,cAAc,CAACF,UAAW,CAACT,EAAE,CAC9B;EACH,CAAC,CAAC;EAEFwC,EAAE,CAAC,iCAAiC,EAAE,MAAK;IACzCvD,SAAS,CAAC0D,QAAQ,EAAE;IACpBF,MAAM,CAACxD,SAAS,CAACiE,cAAc,CAAC2B,KAAK,CAAC,CAAChB,SAAS,EAAE;IAElD5E,SAAS,CAACiE,cAAc,CAAC4B,UAAU,CAAC;MAClClE,QAAQ,EAAE,WAAW;MACrBmE,QAAQ,EAAE,CAAC;MACXjE,QAAQ,EAAE,WAAW;MACrBC,aAAa,EAAE,kBAAkB;MACjCiE,aAAa,EAAE;KAChB,CAAC;IAEFvC,MAAM,CAACxD,SAAS,CAACiE,cAAc,CAAC2B,KAAK,CAAC,CAACd,QAAQ,EAAE;EACnD,CAAC,CAAC;EAEFvB,EAAE,CAAC,8BAA8B,EAAE,MAAK;IACtCvD,SAAS,CAAC0D,QAAQ,EAAE;IACpB,MAAMsC,oBAAoB,GAAGhG,SAAS,CAACiE,cAAc,CAACC,GAAG,CAAC,eAAe,CAAC;IAC1E,MAAM+B,qBAAqB,GACzBjG,SAAS,CAACiE,cAAc,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAEhD8B,oBAAoB,EAAE7B,QAAQ,CAAC,eAAe,CAAC;IAC/CX,MAAM,CAACwC,oBAAoB,EAAEE,MAAM,GAAG,OAAO,CAAC,CAAC,CAACzC,UAAU,EAAE;IAE5DuC,oBAAoB,EAAE7B,QAAQ,CAAC,iBAAiB,CAAC;IACjDX,MAAM,CAACwC,oBAAoB,EAAEE,MAAM,CAAC,CAAC3B,QAAQ,EAAE;IAE/C0B,qBAAqB,EAAE9B,QAAQ,CAAC,eAAe,CAAC;IAChDX,MAAM,CAACyC,qBAAqB,EAAEC,MAAM,GAAG,OAAO,CAAC,CAAC,CAACzC,UAAU,EAAE;IAE7DwC,qBAAqB,EAAE9B,QAAQ,CAAC,iBAAiB,CAAC;IAClDX,MAAM,CAACyC,qBAAqB,EAAEC,MAAM,CAAC,CAAC3B,QAAQ,EAAE;EAClD,CAAC,CAAC;EAEFhB,EAAE,CAAC,qCAAqC,EAAE,MAAK;IAC7CvD,SAAS,CAAC0D,QAAQ,EAAE;IACpB,MAAMyC,YAAY,GAAGnG,SAAS,CAACiE,cAAc,CAACC,GAAG,CAAC,OAAO,CAAC;IAE1DiC,YAAY,EAAEhC,QAAQ,CAAC,QAAQ,CAAC;IAChCX,MAAM,CAAC2C,YAAY,EAAED,MAAM,GAAG,SAAS,CAAC,CAAC,CAACzC,UAAU,EAAE;IAEtD0C,YAAY,EAAEhC,QAAQ,CAAC,WAAW,CAAC;IACnCX,MAAM,CAAC2C,YAAY,EAAED,MAAM,CAAC,CAAC3B,QAAQ,EAAE;EACzC,CAAC,CAAC;EAEFhB,EAAE,CAAC,iDAAiD,EAAE,MAAK;IACzDvD,SAAS,CAAC0D,QAAQ,EAAE;IACpBjD,mBAAmB,CAACsC,oBAAoB,CAACF,GAAG,CAACC,WAAW,CACtDjD,UAAU,CAAC,OAAO;MAAEiE,KAAK,EAAE;IAA8B,CAAE,CAAC,CAAC,CAC9D;IAED9D,SAAS,CAACiE,cAAc,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAAC;IAEzDX,MAAM,CAAC5C,YAAY,CAACkD,KAAK,CAAC,CAACC,oBAAoB,CAC7C,4BAA4B,CAC7B;EACH,CAAC,CAAC;EAEFhE,QAAQ,CAAC,UAAU,EAAE,MAAK;IACxBwD,EAAE,CAAC,wCAAwC,EAAE,MAAK;MAChDvD,SAAS,CAAC0D,QAAQ,EAAE;MAEpB1D,SAAS,CAACiE,cAAc,CAAC4B,UAAU,CAAC;QAAElE,QAAQ,EAAE;MAAE,CAAE,CAAC;MACrD6B,MAAM,CAACxD,SAAS,CAACiE,cAAc,CAAC2B,KAAK,CAAC,CAAChB,SAAS,EAAE;MAElD5E,SAAS,CAACiE,cAAc,CAAC4B,UAAU,CAAC;QAClClE,QAAQ,EAAE,WAAW;QACrBmE,QAAQ,EAAE,CAAC;QACXjE,QAAQ,EAAE,WAAW;QACrBC,aAAa,EAAE,kBAAkB;QACjCiE,aAAa,EAAE;OAChB,CAAC;MACFvC,MAAM,CAACxD,SAAS,CAACiE,cAAc,CAAC2B,KAAK,CAAC,CAACd,QAAQ,EAAE;IACnD,CAAC,CAAC;IAEFvB,EAAE,CAAC,uCAAuC,EAAE,MAAK;MAC/CvD,SAAS,CAAC0D,QAAQ,EAAE;MACpB,MAAM0C,QAAQ,GAAG,IAAIC,IAAI,CAAC,YAAY,CAAC;MACvC,MAAMC,aAAa,GAAGF,QAAQ,CAACG,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC1DhD,MAAM,CAAC8C,aAAa,CAAC,CAACrB,IAAI,CAAC,YAAY,CAAC;IAC1C,CAAC,CAAC;IAEF1B,EAAE,CAAC,wCAAwC,EAAE,MAAK;MAChDvD,SAAS,CAAC0D,QAAQ,EAAE;MACpB,MAAM+C,UAAU,GAAG,EAAE;MACrB,MAAMC,cAAc,GAAGD,UAAU,IAAIE,SAAS;MAC9CnD,MAAM,CAACkD,cAAc,CAAC,CAACE,aAAa,EAAE;IACxC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7G,QAAQ,CAAC,gBAAgB,EAAE,MAAK;IAC9BwD,EAAE,CAAC,wCAAwC,EAAE,MAAK;MAChDvD,SAAS,CAAC0D,QAAQ,EAAE;MAEpB9C,YAAY,CAACkD,KAAK,CAAC+C,KAAK,CAACC,KAAK,EAAE;MAEhClG,YAAY,CAACkD,KAAK,CAAC,6CAA6C,CAAC;MACjEN,MAAM,CAAC5C,YAAY,CAACkD,KAAK,CAAC,CAACC,oBAAoB,CAC7C,6CAA6C,CAC9C;MAEDnD,YAAY,CAACkD,KAAK,CAAC+C,KAAK,CAACC,KAAK,EAAE;MAEhClG,YAAY,CAACkD,KAAK,CAAC,oDAAoD,CAAC;MACxEN,MAAM,CAAC5C,YAAY,CAACkD,KAAK,CAAC,CAACC,oBAAoB,CAC7C,oDAAoD,CACrD;MAEDnD,YAAY,CAACkD,KAAK,CAAC+C,KAAK,CAACC,KAAK,EAAE;MAEhClG,YAAY,CAACkD,KAAK,CAAC,gCAAgC,CAAC;MACpDN,MAAM,CAAC5C,YAAY,CAACkD,KAAK,CAAC,CAACC,oBAAoB,CAC7C,gCAAgC,CACjC;MAEDnD,YAAY,CAACkD,KAAK,CAAC,6BAA6B,CAAC;MACjDN,MAAM,CAAC5C,YAAY,CAACkD,KAAK,CAAC,CAACC,oBAAoB,CAC7C,6BAA6B,CAC9B;MAEDnD,YAAY,CAACkD,KAAK,CAAC,4BAA4B,CAAC;MAChDN,MAAM,CAAC5C,YAAY,CAACkD,KAAK,CAAC,CAACC,oBAAoB,CAC7C,4BAA4B,CAC7B;IACH,CAAC,CAAC;IAEFR,EAAE,CAAC,gCAAgC,EAAE,MAAK;MACxCvD,SAAS,CAAC0D,QAAQ,EAAE;MACpBqD,KAAK,CAACC,OAAO,EAAE,KAAK,CAAC;MAErBA,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QAAElG,EAAE,EAAE,CAAC;QAAEY,QAAQ,EAAE;MAAW,CAAE,CAAC;MAC3D6B,MAAM,CAACwD,OAAO,CAACC,GAAG,CAAC,CAAClD,oBAAoB,CACtC,YAAY,EACZrB,OAAO,CAACwE,GAAG,CAACC,MAAM,CAAC,CACpB;MAEDH,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE;QAAEnD,KAAK,EAAE;UAAEsD,MAAM,EAAE;QAAe;MAAE,CAAE,CAAC;MACvE5D,MAAM,CAACwD,OAAO,CAACC,GAAG,CAAC,CAAClD,oBAAoB,CACtC,kBAAkB,EAClBrB,OAAO,CAACwE,GAAG,CAACC,MAAM,CAAC,CACpB;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFpH,QAAQ,CAAC,kBAAkB,EAAE,MAAK;IAChCwD,EAAE,CAAC,8BAA8B,EAAE,MAAK;MACtCvD,SAAS,CAAC0D,QAAQ,EAAE;MAEpB9C,YAAY,CAACyG,OAAO,CAACR,KAAK,CAACC,KAAK,EAAE;MAElClG,YAAY,CAACyG,OAAO,CAAC,iCAAiC,CAAC;MACvD7D,MAAM,CAAC5C,YAAY,CAACyG,OAAO,CAAC,CAACtD,oBAAoB,CAC/C,iCAAiC,CAClC;MAEDnD,YAAY,CAACyG,OAAO,CAACR,KAAK,CAACC,KAAK,EAAE;MAElClG,YAAY,CAACyG,OAAO,CAAC,kCAAkC,CAAC;MACxD7D,MAAM,CAAC5C,YAAY,CAACyG,OAAO,CAAC,CAACtD,oBAAoB,CAC/C,kCAAkC,CACnC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFR,EAAE,CAAC,sCAAsC,EAAE,MAAK;IAC9CC,MAAM,CAACtD,SAAS,CAAC8D,KAAK,CAAC,CAACsD,WAAW,EAAE;EACvC,CAAC,CAAC;EAEFvH,QAAQ,CAAC,iBAAiB,EAAE,MAAK;IAC/BwD,EAAE,CAAC,iDAAiD,EAAE,MAAK;MACzDvD,SAAS,CAAC0D,QAAQ,EAAE;MAEpB,MAAM0C,QAAQ,GAAG,IAAIC,IAAI,CAAC,YAAY,CAAC;MACvCrG,SAAS,CAACiE,cAAc,CAAC4B,UAAU,CAAC;QAClClE,QAAQ,EAAE,WAAW;QACrBmE,QAAQ,EAAE,CAAC;QACXjE,QAAQ,EAAE,WAAW;QACrBC,aAAa,EAAE,kBAAkB;QACjCiE,aAAa,EAAE,CAAC;QAChB7D,SAAS,EAAEkE;OACZ,CAAC;MAEF,MAAME,aAAa,GAAGF,QAAQ,CAACG,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAE1DhD,MAAM,CAAC8C,aAAa,CAAC,CAACrB,IAAI,CAAC,YAAY,CAAC;IAC1C,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFlF,QAAQ,CAAC,gBAAgB,EAAE,MAAK;IAC9BwD,EAAE,CAAC,wCAAwC,EAAE,MAAK;MAChD3C,YAAY,CAACkD,KAAK,CAAC+C,KAAK,CAACC,KAAK,EAAE;MAEhClG,YAAY,CAACkD,KAAK,CAAC,6CAA6C,CAAC;MACjEN,MAAM,CAAC5C,YAAY,CAACkD,KAAK,CAAC,CAACC,oBAAoB,CAC7C,6CAA6C,CAC9C;MAEDnD,YAAY,CAACkD,KAAK,CAAC+C,KAAK,CAACC,KAAK,EAAE;MAEhClG,YAAY,CAACkD,KAAK,CAAC,oDAAoD,CAAC;MACxEN,MAAM,CAAC5C,YAAY,CAACkD,KAAK,CAAC,CAACC,oBAAoB,CAC7C,oDAAoD,CACrD;MAEDnD,YAAY,CAACkD,KAAK,CAAC+C,KAAK,CAACC,KAAK,EAAE;MAEhClG,YAAY,CAACkD,KAAK,CAAC,gCAAgC,CAAC;MACpDN,MAAM,CAAC5C,YAAY,CAACkD,KAAK,CAAC,CAACC,oBAAoB,CAC7C,gCAAgC,CACjC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}