import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ContractDetailFormComponent } from '@contract-management/components/contract-detail-form/contract-detail-form.component';
import { ContractValuesFormComponent } from '@contract-management/components/contract-values-form/contract-values-form.component';
import { ContractorDetailFormComponent } from '@contract-management/components/contractor-detail-form/contractor-detail-form.component';
import { CompleteContract } from '@contract-management/models/complete-contract.model';
import { ContractValues } from '@contract-management/models/contract-values.model';
import { Contract } from '@contract-management/models/contract.model';
import { ContractService } from '@contract-management/services/contract.service';
import { ContractorContractService } from '@contract-management/services/contractor-contract.service';
import { AlertService } from '@shared/services/alert.service';
import { of, throwError } from 'rxjs';
import { ContractDialogComponent } from './contract-dialog.component';

describe('ContractDialogComponent', () => {
  let component: ContractDialogComponent;
  let fixture: ComponentFixture<ContractDialogComponent>;
  let contractService: jasmine.SpyObj<ContractService>;
  let contractorContractService: jasmine.SpyObj<ContractorContractService>;
  let alertService: jasmine.SpyObj<AlertService>;
  let dialogRef: jasmine.SpyObj<MatDialogRef<ContractDialogComponent>>;

  const mockContractor = {
    id: 1,
    idNumber: 123456789,
    fullName: 'Test Contractor',
    personalEmail: '<EMAIL>',
    idType: { id: 1, name: 'CC' },
    name: 'Test',
  };

  const mockContractDetailFormValue = {
    contractNumber: 123,
    contractYear: 2024,
    object: 'Test Contract',
    rup: true,
    sigepLink: 'link1',
    secopLink: 'link2',
    addition: false,
    cession: false,
    settled: false,
    selectionModality: 1,
    trackingType: 1,
    contractType: 1,
    dependency: 1,
    group: 1,
    monthlyPayment: 1000,
    municipalityId: 1,
    departmentId: 1,
    secopCode: '123',
    causesSelectionId: 1,
    managementSupportId: 1,
    warranty: true,
    dateExpeditionWarranty: '2024-02-20',
    typeWarrantyId: 1,
    insuredRisksId: 1,
    supervisorId: 1,
    earlyTermination: false,
    supervisorFullName: 'Test Supervisor',
    contractClassId: 1,
  };

  const mockContractValues: Omit<ContractValues, 'id'> = {
    numericValue: 1000,
    madsValue: 1000,
    isOtherEntity: false,
    subscriptionDate: '2024-02-20',
    startDate: '2024-02-20',
    endDate: '2024-12-31',
    cdp: 123,
    cdpEntityId: 1,
    cdpEntity: { id: 1, name: 'Test CDP Entity' },
  };

  const mockContract: Contract = {
    id: 1,
    contractNumber: 123,
    contractYearId: 2024,
    object: 'Test Contract',
    rup: true,
    sigepLink: 'link1',
    secopLink: 'link2',
    addition: false,
    cession: false,
    settled: false,
    selectionModalityId: 1,
    trackingTypeId: 1,
    contractTypeId: 1,
    statusId: 1,
    dependencyId: 1,
    groupId: 1,
    monthlyPayment: 1000,
    municipalityId: 1,
    departmentId: 1,
    secopCode: 123,
    causesSelectionId: 1,
    managementSupportId: 1,
    contractClassId: 1,
  };

  const mockCompleteContract: CompleteContract = {
    contractorIdNumber: 123456789,
    contract: mockContract,
    contractContractor: {
      warranty: true,
      dateExpeditionWarranty: '2024-02-20',
      typeWarrantyId: 1,
      insuredRisksId: 1,
      supervisorId: 1,
    },
    contractValues: mockContractValues,
  };

  beforeEach(() => {
    const contractServiceSpy = jasmine.createSpyObj('ContractService', [
      'createCompleteContract',
    ]);
    const contractorContractServiceSpy = jasmine.createSpyObj(
      'ContractorContractService',
      ['hasActiveContract'],
    );
    const alertServiceSpy = jasmine.createSpyObj('AlertService', [
      'success',
      'error',
      'warning',
    ]);
    const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close']);

    TestBed.configureTestingModule({
      imports: [
        ContractDialogComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
      ],
      providers: [
        { provide: MatDialogRef, useValue: dialogRefSpy },
        { provide: MAT_DIALOG_DATA, useValue: {} },
        { provide: ContractService, useValue: contractServiceSpy },
        {
          provide: ContractorContractService,
          useValue: contractorContractServiceSpy,
        },
        { provide: AlertService, useValue: alertServiceSpy },
        provideNativeDateAdapter(),
      ],
    });

    fixture = TestBed.createComponent(ContractDialogComponent);
    component = fixture.componentInstance;
    contractService = TestBed.inject(
      ContractService,
    ) as jasmine.SpyObj<ContractService>;
    contractorContractService = TestBed.inject(
      ContractorContractService,
    ) as jasmine.SpyObj<ContractorContractService>;
    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;
    dialogRef = TestBed.inject(MatDialogRef) as jasmine.SpyObj<
      MatDialogRef<ContractDialogComponent>
    >;

    component.contractorDetailForm = {
      getValue: () => mockContractor,
      isValid: () => true,
    } as unknown as ContractorDetailFormComponent;

    component.contractDetailForm = {
      getValue: () => mockContractDetailFormValue,
      isValid: () => true,
    } as unknown as ContractDetailFormComponent;

    component.contractValuesForm = {
      getValue: () => mockContractValues,
      isValid: () => true,
    } as unknown as ContractValuesFormComponent;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should check active contract and proceed if no active contract', () => {
    spyOn(component.contractorDetailForm, 'getValue').and.returnValue(
      mockContractor,
    );
    contractorContractService.hasActiveContract.and.returnValue(of(false));

    component.checkActiveContract();

    expect(contractorContractService.hasActiveContract).toHaveBeenCalledWith(1);
    expect(alertService.warning).not.toHaveBeenCalled();
  });

  it('should show warning if contractor has active contract', () => {
    spyOn(component.contractorDetailForm, 'getValue').and.returnValue(
      mockContractor,
    );
    contractorContractService.hasActiveContract.and.returnValue(of(true));

    component.checkActiveContract();

    expect(contractorContractService.hasActiveContract).toHaveBeenCalledWith(1);
    expect(alertService.warning).toHaveBeenCalledWith(
      'Este contratista tiene un contrato activo y no puede proceder.',
    );
  });

  it('should handle error when checking active contract', () => {
    spyOn(component.contractorDetailForm, 'getValue').and.returnValue(
      mockContractor,
    );
    contractorContractService.hasActiveContract.and.returnValue(
      throwError(() => ({ error: 'Error' })),
    );

    component.checkActiveContract();

    expect(alertService.error).toHaveBeenCalledWith(
      'Error al verificar contratos activos',
    );
  });

  it('should create complete contract successfully', () => {
    spyOn(component.contractDetailForm, 'getValue').and.returnValue(
      mockContractDetailFormValue,
    );
    spyOn(component.contractorDetailForm, 'getValue').and.returnValue(
      mockContractor,
    );
    spyOn(component.contractValuesForm, 'getValue').and.returnValue(
      mockContractValues,
    );

    contractService.createCompleteContract.and.returnValue(
      of(mockCompleteContract),
    );

    component.createCompleteContract();

    expect(contractService.createCompleteContract).toHaveBeenCalled();
    expect(alertService.success).toHaveBeenCalledWith(
      '¡Contrato creado correctamente!',
    );
    expect(dialogRef.close).toHaveBeenCalledWith('created');
  });

  it('should handle error when creating complete contract', () => {
    spyOn(component.contractDetailForm, 'getValue').and.returnValue(
      mockContractDetailFormValue,
    );
    spyOn(component.contractorDetailForm, 'getValue').and.returnValue(
      mockContractor,
    );
    spyOn(component.contractValuesForm, 'getValue').and.returnValue(
      mockContractValues,
    );

    contractService.createCompleteContract.and.returnValue(
      throwError(() => new Error()),
    );

    component.createCompleteContract();

    expect(contractService.createCompleteContract).toHaveBeenCalled();
    expect(alertService.error).toHaveBeenCalledWith(
      'Error al crear el contrato',
    );
    expect(dialogRef.close).not.toHaveBeenCalled();
  });
});
