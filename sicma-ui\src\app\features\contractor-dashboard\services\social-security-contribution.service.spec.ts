import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { SocialSecurityContribution } from '@contractor-dashboard/models/social-security-contribution.model';
import { environment } from '@env';
import { SocialSecurityContributionService } from './social-security-contribution.service';

describe('SocialSecurityContributionService', () => {
  let service: SocialSecurityContributionService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/social-security-contributions`;

  const mockSocialSecurityContribution: SocialSecurityContribution = {
    id: 1,
    monthlyReportId: 1,
    healthContribution: 1000000,
    pensionContribution: 1000000,
    arlContribution: 1000000,
    compensationFundContribution: 1000000,
    ibc: 1000000,
    paymentFormNumber: 12345,
    epsId: 1,
    arlId: 1,
    pensionFundId: 1,
    arlAffiliationClassId: 1,
    compensationFundId: 1,
    certificateFileUrl: 'path/to/file',
    certificateFileKey: 'file-key',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [SocialSecurityContributionService],
    });
    service = TestBed.inject(SocialSecurityContributionService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all social security contributions', () => {
      const mockContributions = [mockSocialSecurityContribution];

      service.getAll().subscribe((contributions) => {
        expect(contributions).toEqual(mockContributions);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockContributions);
    });

    it('should handle error when getting all social security contributions', () => {
      service.getAll().subscribe({
        error: (error) => {
          expect(error.status).toBe(500);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Server error'), { status: 500 });
    });
  });

  describe('getById', () => {
    it('should return a social security contribution by id', () => {
      const id = 1;

      service.getById(id).subscribe((contribution) => {
        expect(contribution).toEqual(mockSocialSecurityContribution);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockSocialSecurityContribution);
    });

    it('should handle error when getting social security contribution by id', () => {
      const id = 999;

      service.getById(id).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('getByMonthlyReportId', () => {
    it('should return a social security contribution by monthly report id', () => {
      const monthlyReportId = 1;

      service
        .getByMonthlyReportId(monthlyReportId)
        .subscribe((contribution) => {
          expect(contribution).toEqual(mockSocialSecurityContribution);
        });

      const req = httpMock.expectOne(
        `${apiUrl}/monthly-report/${monthlyReportId}`,
      );
      expect(req.request.method).toBe('GET');
      req.flush(mockSocialSecurityContribution);
    });

    it('should handle error when getting social security contribution by monthly report id', () => {
      const monthlyReportId = 999;

      service.getByMonthlyReportId(monthlyReportId).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(
        `${apiUrl}/monthly-report/${monthlyReportId}`,
      );
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('create', () => {
    it('should create a new social security contribution', () => {
      const newContribution: Omit<SocialSecurityContribution, 'id'> = {
        monthlyReportId: 1,
        healthContribution: 1000000,
        pensionContribution: 1000000,
        arlContribution: 1000000,
        compensationFundContribution: 1000000,
        ibc: 1000000,
        paymentFormNumber: 12345,
        epsId: 1,
        arlId: 1,
        pensionFundId: 1,
        arlAffiliationClassId: 1,
        compensationFundId: 1,
        certificateFileUrl: 'path/to/file',
        certificateFileKey: 'file-key',
      };

      service.create(newContribution).subscribe((contribution) => {
        expect(contribution).toEqual(mockSocialSecurityContribution);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newContribution);
      req.flush(mockSocialSecurityContribution);
    });

    it('should handle error when creating social security contribution', () => {
      const newContribution: Omit<SocialSecurityContribution, 'id'> = {
        monthlyReportId: 1,
        healthContribution: 1000000,
        pensionContribution: 1000000,
        arlContribution: 1000000,
        compensationFundContribution: 1000000,
        ibc: 1000000,
        paymentFormNumber: 12345,
        epsId: 1,
        arlId: 1,
        pensionFundId: 1,
        arlAffiliationClassId: 1,
        compensationFundId: 1,
        certificateFileUrl: 'path/to/file',
        certificateFileKey: 'file-key',
      };

      service.create(newContribution).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('update', () => {
    it('should update a social security contribution', () => {
      const id = 1;
      const updateData: Partial<SocialSecurityContribution> = {
        healthContribution: 2000000,
        pensionContribution: 2000000,
      };

      service.update(id, updateData).subscribe((contribution) => {
        expect(contribution).toEqual(mockSocialSecurityContribution);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockSocialSecurityContribution);
    });

    it('should handle error when updating social security contribution', () => {
      const id = 1;
      const updateData: Partial<SocialSecurityContribution> = {
        healthContribution: 2000000,
        pensionContribution: 2000000,
      };

      service.update(id, updateData).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('delete', () => {
    it('should delete a social security contribution', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });

    it('should handle error when deleting social security contribution', () => {
      const id = 1;

      service.delete(id).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('createWithFile', () => {
    it('should create a social security contribution with file', () => {
      const formData = new FormData();
      formData.append('file', new File([''], 'test.pdf'));
      formData.append(
        'data',
        JSON.stringify({
          monthlyReportId: 1,
          healthContribution: 1000000,
          pensionContribution: 1000000,
          arlContribution: 1000000,
          compensationFundContribution: 1000000,
          ibc: 1000000,
          paymentFormNumber: 12345,
          arlAffiliationClassId: 1,
          compensationFundId: 1,
        }),
      );

      service.createWithFile(formData).subscribe((contribution) => {
        expect(contribution).toEqual(mockSocialSecurityContribution);
      });

      const req = httpMock.expectOne(`${apiUrl}/with-file`);
      expect(req.request.method).toBe('POST');
      req.flush(mockSocialSecurityContribution);
    });

    it('should handle error when creating social security contribution with file', () => {
      const formData = new FormData();
      formData.append('file', new File([''], 'test.pdf'));
      formData.append(
        'data',
        JSON.stringify({
          monthlyReportId: 1,
          healthContribution: 1000000,
          pensionContribution: 1000000,
          arlContribution: 1000000,
          compensationFundContribution: 1000000,
          ibc: 1000000,
          paymentFormNumber: 12345,
          arlAffiliationClassId: 1,
          compensationFundId: 1,
        }),
      );

      service.createWithFile(formData).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/with-file`);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('updateWithFile', () => {
    it('should update a social security contribution with file', () => {
      const id = 1;
      const formData = new FormData();
      formData.append('file', new File([''], 'test.pdf'));
      formData.append(
        'data',
        JSON.stringify({
          monthlyReportId: 1,
          healthContribution: 1000000,
          pensionContribution: 1000000,
          arlContribution: 1000000,
          compensationFundContribution: 1000000,
          ibc: 1000000,
          paymentFormNumber: 12345,
          arlAffiliationClassId: 1,
          compensationFundId: 1,
        }),
      );

      service.updateWithFile(id, formData).subscribe((contribution) => {
        expect(contribution).toEqual(mockSocialSecurityContribution);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}/with-file`);
      expect(req.request.method).toBe('PUT');
      req.flush(mockSocialSecurityContribution);
    });

    it('should handle error when updating social security contribution with file', () => {
      const id = 1;
      const formData = new FormData();
      formData.append('file', new File([''], 'test.pdf'));
      formData.append(
        'data',
        JSON.stringify({
          monthlyReportId: 1,
          healthContribution: 1000000,
          pensionContribution: 1000000,
          arlContribution: 1000000,
          compensationFundContribution: 1000000,
          ibc: 1000000,
          paymentFormNumber: 12345,
          arlAffiliationClassId: 1,
          compensationFundId: 1,
        }),
      );

      service.updateWithFile(id, formData).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}/with-file`);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });
});