import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { Payment } from '@contractor-dashboard/models/payment.model';
import { environment } from '@env';
import { PaymentService } from './payment.service';

describe('PaymentService', () => {
  let service: PaymentService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/payments`;

  const mockPayment: Payment = {
    id: 1,
    monthlyReportId: 1,
    value: 1000000,
    initialValue: 1000000,
    totalValue: 1000000,
    paidValue: 1000000,
    additions: 0,
    paymentDate: new Date('2024-01-31'),
    paymentNumber: 12345,
    bankAccountTypeId: 1,
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [PaymentService],
    });
    service = TestBed.inject(PaymentService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should get all payments', () => {
    const mockPayments = [mockPayment];

    service.getAll().subscribe((payments) => {
      expect(payments).toEqual(mockPayments);
    });

    const req = httpMock.expectOne(apiUrl);
    expect(req.request.method).toBe('GET');
    req.flush(mockPayments);
  });

  it('should handle error when getting all payments', () => {
    service.getAll().subscribe({
      error: (error) => {
        expect(error.status).toBe(500);
      },
    });

    const req = httpMock.expectOne(apiUrl);
    req.flush('Error', { status: 500, statusText: 'Server Error' });
  });

  it('should get payment by id', () => {
    const id = 1;

    service.getById(id).subscribe((payment) => {
      expect(payment).toEqual(mockPayment);
    });

    const req = httpMock.expectOne(`${apiUrl}/${id}`);
    expect(req.request.method).toBe('GET');
    req.flush(mockPayment);
  });

  it('should handle error when getting payment by id', () => {
    const id = 999;

    service.getById(id).subscribe({
      error: (error) => {
        expect(error.status).toBe(404);
      },
    });

    const req = httpMock.expectOne(`${apiUrl}/${id}`);
    req.flush('Not found', { status: 404, statusText: 'Not Found' });
  });

  it('should create payment', () => {
    const newPayment: Omit<Payment, 'id'> = {
      monthlyReportId: 1,
      value: 1000000,
      initialValue: 1000000,
      totalValue: 1000000,
      paidValue: 1000000,
      additions: 0,
      paymentDate: new Date('2024-01-31'),
      paymentNumber: 12345,
      bankAccountTypeId: 1,
    };

    service.create(newPayment).subscribe((payment) => {
      expect(payment).toEqual(mockPayment);
    });

    const req = httpMock.expectOne(apiUrl);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(newPayment);
    req.flush(mockPayment);
  });

  it('should handle error when creating payment', () => {
    const invalidPayment = {} as Omit<Payment, 'id'>;

    service.create(invalidPayment).subscribe({
      error: (error) => {
        expect(error.status).toBe(400);
      },
    });

    const req = httpMock.expectOne(apiUrl);
    req.flush('Invalid data', { status: 400, statusText: 'Bad Request' });
  });

  it('should update payment', () => {
    const id = 1;
    const updateData: Partial<Payment> = {
      value: 2000000,
      totalValue: 2000000,
    };

    service.update(id, updateData).subscribe((payment) => {
      expect(payment).toEqual({ ...mockPayment, ...updateData });
    });

    const req = httpMock.expectOne(`${apiUrl}/${id}`);
    expect(req.request.method).toBe('PUT');
    expect(req.request.body).toEqual(updateData);
    req.flush({ ...mockPayment, ...updateData });
  });

  it('should handle error when updating payment', () => {
    const id = 999;
    const updateData: Partial<Payment> = {
      value: 2000000,
    };

    service.update(id, updateData).subscribe({
      error: (error) => {
        expect(error.status).toBe(404);
      },
    });

    const req = httpMock.expectOne(`${apiUrl}/${id}`);
    req.flush('Not found', { status: 404, statusText: 'Not Found' });
  });

  it('should delete payment', () => {
    const id = 1;

    service.delete(id).subscribe(() => {
      expect().nothing();
    });

    const req = httpMock.expectOne(`${apiUrl}/${id}`);
    expect(req.request.method).toBe('DELETE');
    req.flush(null);
  });

  it('should handle error when deleting payment', () => {
    const id = 999;

    service.delete(id).subscribe({
      error: (error) => {
        expect(error.status).toBe(404);
      },
    });

    const req = httpMock.expectOne(`${apiUrl}/${id}`);
    req.flush('Not found', { status: 404, statusText: 'Not Found' });
  });

  it('should get payments by monthly report id', () => {
    const monthlyReportId = 1;
    const mockPayments = [mockPayment];

    service.getByMonthlyReportId(monthlyReportId).subscribe((payments) => {
      expect(payments).toEqual(mockPayments);
    });

    const req = httpMock.expectOne(
      `${apiUrl}/monthly-report/${monthlyReportId}`,
    );
    expect(req.request.method).toBe('GET');
    req.flush(mockPayments);
  });

  it('should handle error when getting payments by monthly report id', () => {
    const monthlyReportId = 999;

    service.getByMonthlyReportId(monthlyReportId).subscribe({
      error: (error) => {
        expect(error.status).toBe(404);
      },
    });

    const req = httpMock.expectOne(
      `${apiUrl}/monthly-report/${monthlyReportId}`,
    );
    req.flush('Not found', { status: 404, statusText: 'Not Found' });
  });
});