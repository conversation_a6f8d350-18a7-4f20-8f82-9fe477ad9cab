{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/Github/SICMA/sicma-ui/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { DatePipe } from '@angular/common';\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { FormBuilder, ReactiveFormsModule } from '@angular/forms';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { ContractService } from '@contract-management/services/contract.service';\nimport { SupervisorContractService } from '@contract-management/services/supervisor-contract.service';\nimport { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';\nimport { PaymentService } from '@contractor-dashboard/services/payment.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { SupervisorService } from '@supervisor-management/services/supervisor.service';\nimport { of, throwError } from 'rxjs';\nimport { MonthlyReportBasicDataComponent } from './monthly-report-basic-data.component';\ndescribe('MonthlyReportBasicDataComponent', () => {\n  let component;\n  let fixture;\n  let contractService;\n  let paymentService;\n  let supervisorService;\n  let supervisorContractService;\n  let alertService;\n  let monthlyReportService;\n  const mockReport = {\n    id: 1,\n    reportNumber: 1,\n    startDate: new Date('2024-01-01'),\n    endDate: new Date('2024-01-31'),\n    creationDate: new Date(),\n    contractorContractId: 1,\n    contractorContract: {\n      id: 1,\n      subscriptionDate: '2024-01-01',\n      contractStartDate: '2024-01-01',\n      contract: {\n        id: 1,\n        contractNumber: 1001,\n        monthlyPayment: 1000000,\n        object: 'Test contract',\n        rup: true,\n        secopCode: 123456,\n        addition: false,\n        cession: false,\n        settled: false,\n        status: {\n          id: 1,\n          name: 'Active'\n        },\n        causesSelectionId: 1,\n        managementSupportId: 1,\n        contractClassId: 1\n      }\n    }\n  };\n  const mockContractDetails = {\n    id: 1,\n    contractNumber: 1001,\n    contractorId: 1,\n    fullName: 'Test Contractor',\n    contractorIdNumber: *********,\n    initialValue: 1000000,\n    startDate: new Date('2024-01-01'),\n    endDate: new Date('2024-12-31'),\n    object: 'Test contract',\n    rup: true,\n    selectionModalityName: 'Test Modality',\n    trackingTypeName: 'Test Type',\n    contractTypeName: 'Test Contract Type',\n    statusName: 'Active',\n    dependencyName: 'Test Dependency',\n    groupName: 'Test Group',\n    contractorEmail: '<EMAIL>',\n    contractorPhone: *********0,\n    accountNumber: '*********',\n    bankName: 'Test Bank',\n    accountTypeName: 'Savings',\n    totalValue: ********,\n    totalAdditionsValue: 0,\n    monthlyPayment: 1000000,\n    supervisorFullName: 'Test Supervisor',\n    supervisorIdNumber: '*********',\n    supervisorPosition: 'Test Position',\n    addition: false,\n    cession: false,\n    settled: false,\n    hasCcp: true\n  };\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    const contractServiceSpy = jasmine.createSpyObj('ContractService', ['getDetailsById']);\n    const paymentServiceSpy = jasmine.createSpyObj('PaymentService', ['getByMonthlyReportId']);\n    const supervisorServiceSpy = jasmine.createSpyObj('SupervisorService', ['getAll']);\n    const supervisorContractServiceSpy = jasmine.createSpyObj('SupervisorContractService', ['getByContractId', 'create']);\n    const alertServiceSpy = jasmine.createSpyObj('AlertService', ['error', 'success']);\n    const monthlyReportServiceSpy = jasmine.createSpyObj('MonthlyReportService', ['update', 'getByContractorContractId']);\n    yield TestBed.configureTestingModule({\n      imports: [MonthlyReportBasicDataComponent, HttpClientTestingModule, BrowserAnimationsModule, ReactiveFormsModule],\n      providers: [{\n        provide: ContractService,\n        useValue: contractServiceSpy\n      }, {\n        provide: PaymentService,\n        useValue: paymentServiceSpy\n      }, {\n        provide: SupervisorService,\n        useValue: supervisorServiceSpy\n      }, {\n        provide: SupervisorContractService,\n        useValue: supervisorContractServiceSpy\n      }, {\n        provide: AlertService,\n        useValue: alertServiceSpy\n      }, {\n        provide: MonthlyReportService,\n        useValue: monthlyReportServiceSpy\n      }, DatePipe, FormBuilder]\n    }).compileComponents();\n    contractService = TestBed.inject(ContractService);\n    paymentService = TestBed.inject(PaymentService);\n    supervisorService = TestBed.inject(SupervisorService);\n    supervisorContractService = TestBed.inject(SupervisorContractService);\n    alertService = TestBed.inject(AlertService);\n    monthlyReportService = TestBed.inject(MonthlyReportService);\n    contractService.getDetailsById.and.returnValue(of(mockContractDetails));\n    paymentService.getByMonthlyReportId.and.returnValue(of([]));\n    supervisorService.getAll.and.returnValue(of([]));\n    supervisorContractService.getByContractId.and.returnValue(of([]));\n    monthlyReportService.getByContractorContractId.and.returnValue(of([mockReport]));\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(MonthlyReportBasicDataComponent);\n    component = fixture.componentInstance;\n    component.report = mockReport;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should load contract details on init', () => {\n    expect(contractService.getDetailsById).toHaveBeenCalledWith(1);\n    expect(component.contractDetails).toEqual(mockContractDetails);\n  });\n  it('should handle error when loading contract details', () => {\n    contractService.getDetailsById.and.returnValue(throwError(() => new Error()));\n    component.loadContractDetails();\n    expect(alertService.error).toHaveBeenCalledWith('Error al obtener detalles del contrato');\n  });\n  it('should initialize with report data', () => {\n    expect(component.report).toBeDefined();\n    expect(component.report.id).toBe(1);\n    expect(component.report.reportNumber).toBe(1);\n  });\n  it('should handle contract information correctly', () => {\n    const contractorContract = component.report.contractorContract;\n    expect(contractorContract).toBeDefined();\n    if (contractorContract?.contract) {\n      expect(contractorContract.contract.contractNumber).toBe(1001);\n      expect(contractorContract.contract.monthlyPayment).toBe(1000000);\n    }\n  });\n  it('should handle invoice form changes', () => {\n    component.invoiceForm.patchValue({\n      hasElectronicInvoice: true\n    });\n    expect(component.invoiceForm.get('invoiceNumber')?.enabled).toBeTrue();\n    component.invoiceForm.patchValue({\n      hasElectronicInvoice: false\n    });\n    expect(component.invoiceForm.get('invoiceNumber')?.disabled).toBeTrue();\n  });\n  it('should validate invoice number when electronic invoice is enabled', () => {\n    component.invoiceForm.patchValue({\n      hasElectronicInvoice: true,\n      invoiceNumber: -1\n    });\n    expect(component.invoiceForm.get('invoiceNumber')?.valid).toBeFalse();\n    component.invoiceForm.patchValue({\n      invoiceNumber: 12345\n    });\n    expect(component.invoiceForm.get('invoiceNumber')?.valid).toBeTrue();\n  });\n  it('should load payments on init', () => {\n    component.ngOnInit();\n    expect(paymentService.getByMonthlyReportId).toHaveBeenCalledWith(mockReport.id);\n  });\n  it('should load supervisors on init', () => {\n    expect(supervisorService.getAll).toHaveBeenCalled();\n  });\n  it('should handle supervisor contract loading', () => {\n    expect(supervisorContractService.getByContractId).toHaveBeenCalled();\n  });\n  it('should toggle supervisor edit mode', () => {\n    component.toggleSupervisorEdit();\n    expect(component.isEditingSupervisor).toBeTrue();\n    component.toggleSupervisorEdit();\n    expect(component.isEditingSupervisor).toBeFalse();\n  });\n  it('should update CCPs validity', () => {\n    component.onCcpsValidityChange(true);\n    expect(component.isCcpsValid).toBeTrue();\n    component.onCcpsValidityChange(false);\n    expect(component.isCcpsValid).toBeFalse();\n  });\n  it('should load payments correctly', () => {\n    const mockPayment = {\n      id: 1,\n      paymentNumber: 1,\n      paymentDate: new Date(),\n      value: 1000000,\n      initialValue: 1000000,\n      totalValue: 1000000,\n      paidValue: 1000000,\n      additions: 0,\n      monthlyReportId: 1,\n      bankAccountTypeId: 1\n    };\n    paymentService.getByMonthlyReportId.and.returnValue(of([mockPayment]));\n    monthlyReportService.getByContractorContractId.and.returnValue(of([mockReport]));\n    component.loadPayments();\n    expect(monthlyReportService.getByContractorContractId).toHaveBeenCalledWith(mockReport.contractorContractId);\n    expect(paymentService.getByMonthlyReportId).toHaveBeenCalledWith(mockReport.id);\n    expect(component.totalPaid).toBe(0);\n  });\n  it('should handle errors when loading payments', () => {\n    monthlyReportService.getByContractorContractId.and.returnValue(throwError(() => new Error()));\n    component.loadPayments();\n    expect(alertService.error).toHaveBeenCalledWith('Error al cargar los informes');\n  });\n  it('should format currency correctly', () => {\n    const value = 1000000;\n    const formattedValue = component.formatCurrency(value);\n    expect(formattedValue).toContain('1.000.000,00');\n  });\n  it('should handle null values in currency formatting', () => {\n    expect(component.formatCurrency(undefined)).toBe('N/A');\n  });\n  it('should format date correctly', () => {\n    const date = new Date('2024-06-15T12:00:00.000Z');\n    const formattedDate = component.formatDate(date);\n    expect(formattedDate).toBe('15/06/2024');\n  });\n  it('should handle null values in date formatting', () => {\n    expect(component.formatDate(undefined)).toBe('N/A');\n  });\n  it('should update report when contract details are loaded', () => {\n    spyOn(component.reportChange, 'emit');\n    component.loadContractDetails();\n    expect(component.reportChange.emit).toHaveBeenCalled();\n  });\n  it('should initialize invoice form correctly', () => {\n    expect(component.invoiceForm.get('hasElectronicInvoice')?.value).toBeFalsy();\n    expect(component.invoiceForm.get('invoiceNumber')?.disabled).toBeTrue();\n  });\n  it('should enable invoice number field when hasElectronicInvoice is true', () => {\n    component.invoiceForm.patchValue({\n      hasElectronicInvoice: true\n    });\n    expect(component.invoiceForm.get('invoiceNumber')?.enabled).toBeTrue();\n  });\n  it('should disable invoice number field when hasElectronicInvoice is false', () => {\n    component.invoiceForm.patchValue({\n      hasElectronicInvoice: false\n    });\n    expect(component.invoiceForm.get('invoiceNumber')?.disabled).toBeTrue();\n  });\n  it('should handle missing contractorContractId when loading payments', () => {\n    const monthlyReportService = TestBed.inject(MonthlyReportService);\n    monthlyReportService.getByContractorContractId.calls.reset();\n    component.report = undefined;\n    component.loadPayments();\n    expect(monthlyReportService.getByContractorContractId).not.toHaveBeenCalled();\n  });\n  it('should handle errors when loading supervisors', () => {\n    const alertService = TestBed.inject(AlertService);\n    supervisorService.getAll.and.returnValue(throwError(() => new Error('Error')));\n    component.ngOnInit();\n    expect(alertService.error).toHaveBeenCalledWith('Error al cargar los supervisores');\n  });\n  it('should handle supervisor autocomplete filtering', () => {\n    const mockSupervisors = [{\n      id: 1,\n      fullName: 'John Doe',\n      idNumber: 123,\n      position: 'Dev',\n      email: '<EMAIL>',\n      idType: {\n        id: 1,\n        name: 'CC'\n      }\n    }, {\n      id: 2,\n      fullName: 'Jane Smith',\n      idNumber: 456,\n      position: 'QA',\n      email: '<EMAIL>',\n      idType: {\n        id: 1,\n        name: 'CC'\n      }\n    }];\n    component.supervisors = mockSupervisors;\n    const filtered = component['_filterSupervisors']('john');\n    expect(filtered.length).toBe(1);\n    expect(filtered[0].fullName).toBe('John Doe');\n  });\n  it('should display supervisor correctly', () => {\n    const mockSupervisor = {\n      id: 1,\n      fullName: 'John Doe',\n      idNumber: 123,\n      position: 'Dev',\n      email: '<EMAIL>',\n      idType: {\n        id: 1,\n        name: 'CC'\n      }\n    };\n    const displayText = component.displaySupervisor(mockSupervisor);\n    expect(displayText).toBe('John Doe (123)');\n    const nullSupervisor = null;\n    expect(component.displaySupervisor(nullSupervisor)).toBe('');\n  });\n  it('should handle CCPs save completion', () => {\n    component.onCcpsSaveComplete();\n    expect(alertService.success).toHaveBeenCalledWith('Distribución de CCPs guardada exitosamente');\n  });\n  it('should format month name correctly', () => {\n    expect(component.getMonthName(0)).toBe('Enero');\n    expect(component.getMonthName(11)).toBe('Diciembre');\n  });\n  it('should handle undefined values in formatters', () => {\n    expect(component.formatDate(undefined)).toBe('N/A');\n    expect(component.formatCurrency(undefined)).toBe('N/A');\n  });\n  it('should handle form initialization in supervisor mode', () => {\n    component.isSupervisor = true;\n    component['initializeForm']();\n    expect(component.invoiceForm.get('hasElectronicInvoice')?.disabled).toBeTrue();\n  });\n  describe('Form Validation', () => {\n    it('should validate invoice number when electronic invoice is enabled', () => {\n      component.invoiceForm.patchValue({\n        hasElectronicInvoice: true\n      });\n      expect(component.invoiceForm.get('invoiceNumber')?.enabled).toBeTrue();\n      component.invoiceForm.patchValue({\n        invoiceNumber: -1\n      });\n      expect(component.invoiceForm.get('invoiceNumber')?.valid).toBeFalse();\n      component.invoiceForm.patchValue({\n        invoiceNumber: 'abc'\n      });\n      expect(component.invoiceForm.get('invoiceNumber')?.valid).toBeFalse();\n      component.invoiceForm.patchValue({\n        invoiceNumber: 123\n      });\n      expect(component.invoiceForm.get('invoiceNumber')?.valid).toBeTrue();\n    });\n    it('should emit form validity changes', () => {\n      const formValidityEmitSpy = spyOn(component.formValidityChange, 'emit');\n      component.onCcpsValidityChange(true);\n      component.invoiceForm.patchValue({\n        hasElectronicInvoice: true,\n        invoiceNumber: 123\n      });\n      expect(formValidityEmitSpy).toHaveBeenCalledWith(true);\n    });\n    it('should handle form initialization in supervisor mode', () => {\n      component.isSupervisor = true;\n      component['initializeForm']();\n      expect(component.invoiceForm.get('hasElectronicInvoice')?.disabled).toBeTrue();\n    });\n  });\n  describe('Data Loading and Error Handling', () => {\n    it('should handle errors when loading contract details', () => {\n      contractService.getDetailsById.and.returnValue(throwError(() => new Error('Network error')));\n      component.loadContractDetails();\n      expect(alertService.error).toHaveBeenCalledWith('Error al obtener detalles del contrato');\n    });\n    it('should handle errors when loading payments', () => {\n      monthlyReportService.getByContractorContractId.and.returnValue(throwError(() => new Error('Network error')));\n      component.loadPayments();\n      expect(alertService.error).toHaveBeenCalledWith('Error al cargar los informes');\n    });\n    it('should handle errors when loading supervisors', () => {\n      supervisorService.getAll.and.returnValue(throwError(() => new Error('Network error')));\n      component.ngOnInit();\n      expect(alertService.error).toHaveBeenCalledWith('Error al cargar los supervisores');\n    });\n  });\n  describe('Supervisor Management', () => {\n    beforeEach(() => {\n      const mockSupervisor = {\n        id: 1,\n        idNumber: *********,\n        fullName: 'Test Supervisor',\n        email: '<EMAIL>',\n        position: 'Test Position',\n        idType: {\n          id: 1,\n          name: 'CC'\n        }\n      };\n      component.supervisors = [mockSupervisor];\n      component.supervisorForm.patchValue({\n        supervisor: mockSupervisor\n      });\n    });\n    it('should handle supervisor contract creation success', () => {\n      supervisorContractService.create.and.returnValue(of({\n        id: 1,\n        supervisorId: 1,\n        contractId: 1\n      }));\n      component.saveSupervisor();\n      expect(supervisorContractService.create).toHaveBeenCalledWith({\n        supervisorId: 1,\n        contractId: 1\n      });\n      expect(alertService.success).toHaveBeenCalledWith('Supervisor asignado exitosamente');\n    });\n    it('should handle supervisor contract creation error', () => {\n      supervisorContractService.create.and.returnValue(throwError(() => new Error()));\n      component.saveSupervisor();\n      expect(alertService.error).toHaveBeenCalledWith('Error al asignar el supervisor');\n    });\n  });\n  describe('Data Formatting', () => {\n    it('should format dates correctly', () => {\n      const date = new Date('2024-01-01T00:00:00');\n      expect(component.formatDate(date)).toBe('01/01/2024');\n      expect(component.formatDate(undefined)).toBe('N/A');\n      expect(component.formatDate('')).toBe('N/A');\n    });\n    it('should format currency values correctly', () => {\n      const formattedValue = component.formatCurrency(1000000);\n      expect(formattedValue).toContain('1.000.000,00');\n      expect(formattedValue).toContain(',00');\n      expect(component.formatCurrency(undefined)).toBe('N/A');\n    });\n    it('should get month names correctly', () => {\n      expect(component.getMonthName(0)).toBe('Enero');\n      expect(component.getMonthName(11)).toBe('Diciembre');\n    });\n  });\n  describe('CCPs Management', () => {\n    it('should handle CCPs validity changes', () => {\n      const validityEmitSpy = spyOn(component.formValidityChange, 'emit');\n      component.onCcpsValidityChange(true);\n      expect(component.isCcpsValid).toBeTrue();\n      expect(validityEmitSpy).toHaveBeenCalled();\n    });\n    it('should handle CCPs save completion', () => {\n      component.onCcpsSaveComplete();\n      expect(alertService.success).toHaveBeenCalledWith('Distribución de CCPs guardada exitosamente');\n    });\n  });\n});", "map": {"version": 3, "names": ["DatePipe", "HttpClientTestingModule", "TestBed", "FormBuilder", "ReactiveFormsModule", "BrowserAnimationsModule", "ContractService", "SupervisorContractService", "MonthlyReportService", "PaymentService", "AlertService", "SupervisorService", "of", "throwError", "MonthlyReportBasicDataComponent", "describe", "component", "fixture", "contractService", "paymentService", "supervisorService", "supervisorContractService", "alertService", "monthlyReportService", "mockReport", "id", "reportNumber", "startDate", "Date", "endDate", "creationDate", "contractorContractId", "contractorContract", "subscriptionDate", "contractStartDate", "contract", "contractNumber", "monthlyPayment", "object", "rup", "secopCode", "addition", "cession", "settled", "status", "name", "causesSelectionId", "managementSupportId", "contractClassId", "mockContractDetails", "contractorId", "fullName", "contractorIdNumber", "initialValue", "selectionModalityName", "trackingTypeName", "contractTypeName", "statusName", "dependencyName", "groupName", "contractorEmail", "contractorPhone", "accountNumber", "bankName", "accountTypeName", "totalValue", "totalAdditionsValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supervisorIdNumber", "supervisorPosition", "hasCcp", "beforeEach", "_asyncToGenerator", "contractServiceSpy", "jasmine", "createSpyObj", "paymentServiceSpy", "supervisorServiceSpy", "supervisorContractServiceSpy", "alertServiceSpy", "monthlyReportServiceSpy", "configureTestingModule", "imports", "providers", "provide", "useValue", "compileComponents", "inject", "getDetailsById", "and", "returnValue", "getByMonthlyReportId", "getAll", "getByContractId", "getByContractorContractId", "createComponent", "componentInstance", "report", "detectChanges", "it", "expect", "toBeTruthy", "toHaveBeenCalledWith", "contractDetails", "toEqual", "Error", "loadContractDetails", "error", "toBeDefined", "toBe", "invoiceForm", "patchValue", "hasElectronicInvoice", "get", "enabled", "toBeTrue", "disabled", "invoiceNumber", "valid", "toBeFalse", "ngOnInit", "toHaveBeenCalled", "toggleSupervisorEdit", "isEditingSupervisor", "onCcpsValidityChange", "isCcpsValid", "mockPayment", "paymentNumber", "paymentDate", "value", "paidValue", "additions", "monthlyReportId", "bankAccountTypeId", "loadPayments", "totalPaid", "formattedValue", "formatCurrency", "toContain", "undefined", "date", "formattedDate", "formatDate", "spyOn", "reportChange", "emit", "toBeFalsy", "calls", "reset", "not", "mockSupervisors", "idNumber", "position", "email", "idType", "supervisors", "filtered", "length", "mockSupervisor", "displayText", "displaySupervisor", "nullSupervisor", "onCcpsSaveComplete", "success", "getMonthName", "isSupervisor", "formValidityEmitSpy", "formValidityChange", "supervisorForm", "supervisor", "create", "supervisorId", "contractId", "saveSupervisor", "validityEmitSpy"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\components\\monthly-reports-tab\\monthly-report-dialog\\monthly-report-basic-data\\monthly-report-basic-data.component.spec.ts"], "sourcesContent": ["import { DatePipe } from '@angular/common';\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { ComponentFixture, TestBed } from '@angular/core/testing';\nimport { FormBuilder, ReactiveFormsModule } from '@angular/forms';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { ContractDetails } from '@contract-management/models/contract-details.model';\nimport { ContractService } from '@contract-management/services/contract.service';\nimport { SupervisorContractService } from '@contract-management/services/supervisor-contract.service';\nimport { MonthlyReport } from '@contractor-dashboard/models/monthly-report.model';\nimport { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';\nimport { PaymentService } from '@contractor-dashboard/services/payment.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { Supervisor } from '@supervisor-management/models/supervisor.model';\nimport { SupervisorService } from '@supervisor-management/services/supervisor.service';\nimport { of, throwError } from 'rxjs';\nimport { MonthlyReportBasicDataComponent } from './monthly-report-basic-data.component';\n\ndescribe('MonthlyReportBasicDataComponent', () => {\n  let component: MonthlyReportBasicDataComponent;\n  let fixture: ComponentFixture<MonthlyReportBasicDataComponent>;\n  let contractService: jasmine.SpyObj<ContractService>;\n  let paymentService: jasmine.SpyObj<PaymentService>;\n  let supervisorService: jasmine.SpyObj<SupervisorService>;\n  let supervisorContractService: jasmine.SpyObj<SupervisorContractService>;\n  let alertService: jasmine.SpyObj<AlertService>;\n  let monthlyReportService: jasmine.SpyObj<MonthlyReportService>;\n\n  const mockReport: MonthlyReport = {\n    id: 1,\n    reportNumber: 1,\n    startDate: new Date('2024-01-01'),\n    endDate: new Date('2024-01-31'),\n    creationDate: new Date(),\n    contractorContractId: 1,\n    contractorContract: {\n      id: 1,\n      subscriptionDate: '2024-01-01',\n      contractStartDate: '2024-01-01',\n      contract: {\n        id: 1,\n        contractNumber: 1001,\n        monthlyPayment: 1000000,\n        object: 'Test contract',\n        rup: true,\n        secopCode: 123456,\n        addition: false,\n        cession: false,\n        settled: false,\n        status: { id: 1, name: 'Active' },\n        causesSelectionId: 1,\n        managementSupportId: 1,\n        contractClassId: 1,\n      },\n    },\n  };\n\n  const mockContractDetails: ContractDetails = {\n    id: 1,\n    contractNumber: 1001,\n    contractorId: 1,\n    fullName: 'Test Contractor',\n    contractorIdNumber: *********,\n    initialValue: 1000000,\n    startDate: new Date('2024-01-01'),\n    endDate: new Date('2024-12-31'),\n    object: 'Test contract',\n    rup: true,\n    selectionModalityName: 'Test Modality',\n    trackingTypeName: 'Test Type',\n    contractTypeName: 'Test Contract Type',\n    statusName: 'Active',\n    dependencyName: 'Test Dependency',\n    groupName: 'Test Group',\n    contractorEmail: '<EMAIL>',\n    contractorPhone: *********0,\n    accountNumber: '*********',\n    bankName: 'Test Bank',\n    accountTypeName: 'Savings',\n    totalValue: ********,\n    totalAdditionsValue: 0,\n    monthlyPayment: 1000000,\n    supervisorFullName: 'Test Supervisor',\n    supervisorIdNumber: '*********',\n    supervisorPosition: 'Test Position',\n    addition: false,\n    cession: false,\n    settled: false,\n    hasCcp: true,\n  };\n\n  beforeEach(async () => {\n    const contractServiceSpy = jasmine.createSpyObj('ContractService', [\n      'getDetailsById',\n    ]);\n    const paymentServiceSpy = jasmine.createSpyObj('PaymentService', [\n      'getByMonthlyReportId',\n    ]);\n    const supervisorServiceSpy = jasmine.createSpyObj('SupervisorService', [\n      'getAll',\n    ]);\n    const supervisorContractServiceSpy = jasmine.createSpyObj(\n      'SupervisorContractService',\n      ['getByContractId', 'create'],\n    );\n    const alertServiceSpy = jasmine.createSpyObj('AlertService', [\n      'error',\n      'success',\n    ]);\n    const monthlyReportServiceSpy = jasmine.createSpyObj(\n      'MonthlyReportService',\n      ['update', 'getByContractorContractId'],\n    );\n\n    await TestBed.configureTestingModule({\n      imports: [\n        MonthlyReportBasicDataComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n        ReactiveFormsModule,\n      ],\n      providers: [\n        { provide: ContractService, useValue: contractServiceSpy },\n        { provide: PaymentService, useValue: paymentServiceSpy },\n        { provide: SupervisorService, useValue: supervisorServiceSpy },\n        {\n          provide: SupervisorContractService,\n          useValue: supervisorContractServiceSpy,\n        },\n        { provide: AlertService, useValue: alertServiceSpy },\n        { provide: MonthlyReportService, useValue: monthlyReportServiceSpy },\n        DatePipe,\n        FormBuilder,\n      ],\n    }).compileComponents();\n\n    contractService = TestBed.inject(\n      ContractService,\n    ) as jasmine.SpyObj<ContractService>;\n    paymentService = TestBed.inject(\n      PaymentService,\n    ) as jasmine.SpyObj<PaymentService>;\n    supervisorService = TestBed.inject(\n      SupervisorService,\n    ) as jasmine.SpyObj<SupervisorService>;\n    supervisorContractService = TestBed.inject(\n      SupervisorContractService,\n    ) as jasmine.SpyObj<SupervisorContractService>;\n    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;\n    monthlyReportService = TestBed.inject(\n      MonthlyReportService,\n    ) as jasmine.SpyObj<MonthlyReportService>;\n\n    contractService.getDetailsById.and.returnValue(of(mockContractDetails));\n    paymentService.getByMonthlyReportId.and.returnValue(of([]));\n    supervisorService.getAll.and.returnValue(of([]));\n    supervisorContractService.getByContractId.and.returnValue(of([]));\n    monthlyReportService.getByContractorContractId.and.returnValue(\n      of([mockReport]),\n    );\n  });\n\n  beforeEach(() => {\n    fixture = TestBed.createComponent(MonthlyReportBasicDataComponent);\n    component = fixture.componentInstance;\n    component.report = mockReport;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should load contract details on init', () => {\n    expect(contractService.getDetailsById).toHaveBeenCalledWith(1);\n    expect(component.contractDetails).toEqual(mockContractDetails);\n  });\n\n  it('should handle error when loading contract details', () => {\n    contractService.getDetailsById.and.returnValue(\n      throwError(() => new Error()),\n    );\n    component.loadContractDetails();\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al obtener detalles del contrato',\n    );\n  });\n\n  it('should initialize with report data', () => {\n    expect(component.report).toBeDefined();\n    expect(component.report.id).toBe(1);\n    expect(component.report.reportNumber).toBe(1);\n  });\n\n  it('should handle contract information correctly', () => {\n    const contractorContract = component.report.contractorContract;\n    expect(contractorContract).toBeDefined();\n    if (contractorContract?.contract) {\n      expect(contractorContract.contract.contractNumber).toBe(1001);\n      expect(contractorContract.contract.monthlyPayment).toBe(1000000);\n    }\n  });\n\n  it('should handle invoice form changes', () => {\n    component.invoiceForm.patchValue({ hasElectronicInvoice: true });\n    expect(component.invoiceForm.get('invoiceNumber')?.enabled).toBeTrue();\n\n    component.invoiceForm.patchValue({ hasElectronicInvoice: false });\n    expect(component.invoiceForm.get('invoiceNumber')?.disabled).toBeTrue();\n  });\n\n  it('should validate invoice number when electronic invoice is enabled', () => {\n    component.invoiceForm.patchValue({\n      hasElectronicInvoice: true,\n      invoiceNumber: -1,\n    });\n    expect(component.invoiceForm.get('invoiceNumber')?.valid).toBeFalse();\n\n    component.invoiceForm.patchValue({\n      invoiceNumber: 12345,\n    });\n    expect(component.invoiceForm.get('invoiceNumber')?.valid).toBeTrue();\n  });\n\n  it('should load payments on init', () => {\n    component.ngOnInit();\n    expect(paymentService.getByMonthlyReportId).toHaveBeenCalledWith(\n      mockReport.id,\n    );\n  });\n\n  it('should load supervisors on init', () => {\n    expect(supervisorService.getAll).toHaveBeenCalled();\n  });\n\n  it('should handle supervisor contract loading', () => {\n    expect(supervisorContractService.getByContractId).toHaveBeenCalled();\n  });\n\n  it('should toggle supervisor edit mode', () => {\n    component.toggleSupervisorEdit();\n    expect(component.isEditingSupervisor).toBeTrue();\n\n    component.toggleSupervisorEdit();\n    expect(component.isEditingSupervisor).toBeFalse();\n  });\n\n  it('should update CCPs validity', () => {\n    component.onCcpsValidityChange(true);\n    expect(component.isCcpsValid).toBeTrue();\n\n    component.onCcpsValidityChange(false);\n    expect(component.isCcpsValid).toBeFalse();\n  });\n\n  it('should load payments correctly', () => {\n    const mockPayment = {\n      id: 1,\n      paymentNumber: 1,\n      paymentDate: new Date(),\n      value: 1000000,\n      initialValue: 1000000,\n      totalValue: 1000000,\n      paidValue: 1000000,\n      additions: 0,\n      monthlyReportId: 1,\n      bankAccountTypeId: 1,\n    };\n\n    paymentService.getByMonthlyReportId.and.returnValue(of([mockPayment]));\n    monthlyReportService.getByContractorContractId.and.returnValue(\n      of([mockReport]),\n    );\n\n    component.loadPayments();\n\n    expect(monthlyReportService.getByContractorContractId).toHaveBeenCalledWith(\n      mockReport.contractorContractId,\n    );\n    expect(paymentService.getByMonthlyReportId).toHaveBeenCalledWith(\n      mockReport.id,\n    );\n    expect(component.totalPaid).toBe(0);\n  });\n\n  it('should handle errors when loading payments', () => {\n    monthlyReportService.getByContractorContractId.and.returnValue(\n      throwError(() => new Error()),\n    );\n\n    component.loadPayments();\n\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al cargar los informes',\n    );\n  });\n\n  it('should format currency correctly', () => {\n    const value = 1000000;\n    const formattedValue = component.formatCurrency(value);\n    expect(formattedValue).toContain('1.000.000,00');\n  });\n\n  it('should handle null values in currency formatting', () => {\n    expect(component.formatCurrency(undefined)).toBe('N/A');\n  });\n\n  it('should format date correctly', () => {\n    const date = new Date('2024-06-15T12:00:00.000Z');\n    const formattedDate = component.formatDate(date);\n    expect(formattedDate).toBe('15/06/2024');\n  });\n\n  it('should handle null values in date formatting', () => {\n    expect(component.formatDate(undefined)).toBe('N/A');\n  });\n\n  it('should update report when contract details are loaded', () => {\n    spyOn(component.reportChange, 'emit');\n    component.loadContractDetails();\n    expect(component.reportChange.emit).toHaveBeenCalled();\n  });\n\n  it('should initialize invoice form correctly', () => {\n    expect(\n      component.invoiceForm.get('hasElectronicInvoice')?.value,\n    ).toBeFalsy();\n    expect(component.invoiceForm.get('invoiceNumber')?.disabled).toBeTrue();\n  });\n\n  it('should enable invoice number field when hasElectronicInvoice is true', () => {\n    component.invoiceForm.patchValue({ hasElectronicInvoice: true });\n    expect(component.invoiceForm.get('invoiceNumber')?.enabled).toBeTrue();\n  });\n\n  it('should disable invoice number field when hasElectronicInvoice is false', () => {\n    component.invoiceForm.patchValue({ hasElectronicInvoice: false });\n    expect(component.invoiceForm.get('invoiceNumber')?.disabled).toBeTrue();\n  });\n\n  it('should handle missing contractorContractId when loading payments', () => {\n    const monthlyReportService = TestBed.inject(\n      MonthlyReportService,\n    ) as jasmine.SpyObj<MonthlyReportService>;\n    monthlyReportService.getByContractorContractId.calls.reset();\n    component.report = undefined as unknown as MonthlyReport;\n    component.loadPayments();\n    expect(\n      monthlyReportService.getByContractorContractId,\n    ).not.toHaveBeenCalled();\n  });\n\n  it('should handle errors when loading supervisors', () => {\n    const alertService = TestBed.inject(\n      AlertService,\n    ) as jasmine.SpyObj<AlertService>;\n    supervisorService.getAll.and.returnValue(\n      throwError(() => new Error('Error')),\n    );\n    component.ngOnInit();\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al cargar los supervisores',\n    );\n  });\n\n  it('should handle supervisor autocomplete filtering', () => {\n    const mockSupervisors: Supervisor[] = [\n      {\n        id: 1,\n        fullName: 'John Doe',\n        idNumber: 123,\n        position: 'Dev',\n        email: '<EMAIL>',\n        idType: { id: 1, name: 'CC' },\n      },\n      {\n        id: 2,\n        fullName: 'Jane Smith',\n        idNumber: 456,\n        position: 'QA',\n        email: '<EMAIL>',\n        idType: { id: 1, name: 'CC' },\n      },\n    ];\n    component.supervisors = mockSupervisors;\n\n    const filtered = component['_filterSupervisors']('john');\n    expect(filtered.length).toBe(1);\n    expect(filtered[0].fullName).toBe('John Doe');\n  });\n\n  it('should display supervisor correctly', () => {\n    const mockSupervisor: Supervisor = {\n      id: 1,\n      fullName: 'John Doe',\n      idNumber: 123,\n      position: 'Dev',\n      email: '<EMAIL>',\n      idType: { id: 1, name: 'CC' },\n    };\n    const displayText = component.displaySupervisor(mockSupervisor);\n    expect(displayText).toBe('John Doe (123)');\n\n    const nullSupervisor = null as unknown as Supervisor;\n    expect(component.displaySupervisor(nullSupervisor)).toBe('');\n  });\n\n  it('should handle CCPs save completion', () => {\n    component.onCcpsSaveComplete();\n    expect(alertService.success).toHaveBeenCalledWith(\n      'Distribución de CCPs guardada exitosamente',\n    );\n  });\n\n  it('should format month name correctly', () => {\n    expect(component.getMonthName(0)).toBe('Enero');\n    expect(component.getMonthName(11)).toBe('Diciembre');\n  });\n\n  it('should handle undefined values in formatters', () => {\n    expect(component.formatDate(undefined)).toBe('N/A');\n    expect(component.formatCurrency(undefined)).toBe('N/A');\n  });\n\n  it('should handle form initialization in supervisor mode', () => {\n    component.isSupervisor = true;\n    component['initializeForm']();\n    expect(\n      component.invoiceForm.get('hasElectronicInvoice')?.disabled,\n    ).toBeTrue();\n  });\n\n  describe('Form Validation', () => {\n    it('should validate invoice number when electronic invoice is enabled', () => {\n      component.invoiceForm.patchValue({ hasElectronicInvoice: true });\n      expect(component.invoiceForm.get('invoiceNumber')?.enabled).toBeTrue();\n\n      component.invoiceForm.patchValue({ invoiceNumber: -1 });\n      expect(component.invoiceForm.get('invoiceNumber')?.valid).toBeFalse();\n\n      component.invoiceForm.patchValue({ invoiceNumber: 'abc' });\n      expect(component.invoiceForm.get('invoiceNumber')?.valid).toBeFalse();\n\n      component.invoiceForm.patchValue({ invoiceNumber: 123 });\n      expect(component.invoiceForm.get('invoiceNumber')?.valid).toBeTrue();\n    });\n\n    it('should emit form validity changes', () => {\n      const formValidityEmitSpy = spyOn(component.formValidityChange, 'emit');\n      component.onCcpsValidityChange(true);\n      component.invoiceForm.patchValue({\n        hasElectronicInvoice: true,\n        invoiceNumber: 123,\n      });\n\n      expect(formValidityEmitSpy).toHaveBeenCalledWith(true);\n    });\n\n    it('should handle form initialization in supervisor mode', () => {\n      component.isSupervisor = true;\n      component['initializeForm']();\n      expect(\n        component.invoiceForm.get('hasElectronicInvoice')?.disabled,\n      ).toBeTrue();\n    });\n  });\n\n  describe('Data Loading and Error Handling', () => {\n    it('should handle errors when loading contract details', () => {\n      contractService.getDetailsById.and.returnValue(\n        throwError(() => new Error('Network error')),\n      );\n      component.loadContractDetails();\n      expect(alertService.error).toHaveBeenCalledWith(\n        'Error al obtener detalles del contrato',\n      );\n    });\n\n    it('should handle errors when loading payments', () => {\n      monthlyReportService.getByContractorContractId.and.returnValue(\n        throwError(() => new Error('Network error')),\n      );\n      component.loadPayments();\n      expect(alertService.error).toHaveBeenCalledWith(\n        'Error al cargar los informes',\n      );\n    });\n\n    it('should handle errors when loading supervisors', () => {\n      supervisorService.getAll.and.returnValue(\n        throwError(() => new Error('Network error')),\n      );\n      component.ngOnInit();\n      expect(alertService.error).toHaveBeenCalledWith(\n        'Error al cargar los supervisores',\n      );\n    });\n  });\n\n  describe('Supervisor Management', () => {\n    beforeEach(() => {\n      const mockSupervisor = {\n        id: 1,\n        idNumber: *********,\n        fullName: 'Test Supervisor',\n        email: '<EMAIL>',\n        position: 'Test Position',\n        idType: { id: 1, name: 'CC' },\n      };\n      component.supervisors = [mockSupervisor];\n      component.supervisorForm.patchValue({ supervisor: mockSupervisor });\n    });\n\n    it('should handle supervisor contract creation success', () => {\n      supervisorContractService.create.and.returnValue(\n        of({ id: 1, supervisorId: 1, contractId: 1 }),\n      );\n\n      component.saveSupervisor();\n\n      expect(supervisorContractService.create).toHaveBeenCalledWith({\n        supervisorId: 1,\n        contractId: 1,\n      });\n      expect(alertService.success).toHaveBeenCalledWith(\n        'Supervisor asignado exitosamente',\n      );\n    });\n\n    it('should handle supervisor contract creation error', () => {\n      supervisorContractService.create.and.returnValue(\n        throwError(() => new Error()),\n      );\n\n      component.saveSupervisor();\n\n      expect(alertService.error).toHaveBeenCalledWith(\n        'Error al asignar el supervisor',\n      );\n    });\n  });\n\n  describe('Data Formatting', () => {\n    it('should format dates correctly', () => {\n      const date = new Date('2024-01-01T00:00:00');\n      expect(component.formatDate(date)).toBe('01/01/2024');\n      expect(component.formatDate(undefined)).toBe('N/A');\n      expect(component.formatDate('')).toBe('N/A');\n    });\n\n    it('should format currency values correctly', () => {\n      const formattedValue = component.formatCurrency(1000000);\n      expect(formattedValue).toContain('1.000.000,00');\n      expect(formattedValue).toContain(',00');\n      expect(component.formatCurrency(undefined)).toBe('N/A');\n    });\n\n    it('should get month names correctly', () => {\n      expect(component.getMonthName(0)).toBe('Enero');\n      expect(component.getMonthName(11)).toBe('Diciembre');\n    });\n  });\n\n  describe('CCPs Management', () => {\n    it('should handle CCPs validity changes', () => {\n      const validityEmitSpy = spyOn(component.formValidityChange, 'emit');\n      component.onCcpsValidityChange(true);\n      expect(component.isCcpsValid).toBeTrue();\n      expect(validityEmitSpy).toHaveBeenCalled();\n    });\n\n    it('should handle CCPs save completion', () => {\n      component.onCcpsSaveComplete();\n      expect(alertService.success).toHaveBeenCalledWith(\n        'Distribución de CCPs guardada exitosamente',\n      );\n    });\n  });\n});"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAA2BC,OAAO,QAAQ,uBAAuB;AACjE,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,uBAAuB,QAAQ,sCAAsC;AAE9E,SAASC,eAAe,QAAQ,gDAAgD;AAChF,SAASC,yBAAyB,QAAQ,2DAA2D;AAErG,SAASC,oBAAoB,QAAQ,uDAAuD;AAC5F,SAASC,cAAc,QAAQ,gDAAgD;AAC/E,SAASC,YAAY,QAAQ,gCAAgC;AAE7D,SAASC,iBAAiB,QAAQ,oDAAoD;AACtF,SAASC,EAAE,EAAEC,UAAU,QAAQ,MAAM;AACrC,SAASC,+BAA+B,QAAQ,uCAAuC;AAEvFC,QAAQ,CAAC,iCAAiC,EAAE,MAAK;EAC/C,IAAIC,SAA0C;EAC9C,IAAIC,OAA0D;EAC9D,IAAIC,eAAgD;EACpD,IAAIC,cAA8C;EAClD,IAAIC,iBAAoD;EACxD,IAAIC,yBAAoE;EACxE,IAAIC,YAA0C;EAC9C,IAAIC,oBAA0D;EAE9D,MAAMC,UAAU,GAAkB;IAChCC,EAAE,EAAE,CAAC;IACLC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;IACjCC,OAAO,EAAE,IAAID,IAAI,CAAC,YAAY,CAAC;IAC/BE,YAAY,EAAE,IAAIF,IAAI,EAAE;IACxBG,oBAAoB,EAAE,CAAC;IACvBC,kBAAkB,EAAE;MAClBP,EAAE,EAAE,CAAC;MACLQ,gBAAgB,EAAE,YAAY;MAC9BC,iBAAiB,EAAE,YAAY;MAC/BC,QAAQ,EAAE;QACRV,EAAE,EAAE,CAAC;QACLW,cAAc,EAAE,IAAI;QACpBC,cAAc,EAAE,OAAO;QACvBC,MAAM,EAAE,eAAe;QACvBC,GAAG,EAAE,IAAI;QACTC,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,KAAK;QACfC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,KAAK;QACdC,MAAM,EAAE;UAAEnB,EAAE,EAAE,CAAC;UAAEoB,IAAI,EAAE;QAAQ,CAAE;QACjCC,iBAAiB,EAAE,CAAC;QACpBC,mBAAmB,EAAE,CAAC;QACtBC,eAAe,EAAE;;;GAGtB;EAED,MAAMC,mBAAmB,GAAoB;IAC3CxB,EAAE,EAAE,CAAC;IACLW,cAAc,EAAE,IAAI;IACpBc,YAAY,EAAE,CAAC;IACfC,QAAQ,EAAE,iBAAiB;IAC3BC,kBAAkB,EAAE,SAAS;IAC7BC,YAAY,EAAE,OAAO;IACrB1B,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;IACjCC,OAAO,EAAE,IAAID,IAAI,CAAC,YAAY,CAAC;IAC/BU,MAAM,EAAE,eAAe;IACvBC,GAAG,EAAE,IAAI;IACTe,qBAAqB,EAAE,eAAe;IACtCC,gBAAgB,EAAE,WAAW;IAC7BC,gBAAgB,EAAE,oBAAoB;IACtCC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,iBAAiB;IACjCC,SAAS,EAAE,YAAY;IACvBC,eAAe,EAAE,kBAAkB;IACnCC,eAAe,EAAE,UAAU;IAC3BC,aAAa,EAAE,WAAW;IAC1BC,QAAQ,EAAE,WAAW;IACrBC,eAAe,EAAE,SAAS;IAC1BC,UAAU,EAAE,QAAQ;IACpBC,mBAAmB,EAAE,CAAC;IACtB7B,cAAc,EAAE,OAAO;IACvB8B,kBAAkB,EAAE,iBAAiB;IACrCC,kBAAkB,EAAE,WAAW;IAC/BC,kBAAkB,EAAE,eAAe;IACnC5B,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,KAAK;IACd2B,MAAM,EAAE;GACT;EAEDC,UAAU,eAAAC,iBAAA,CAAC,aAAW;IACpB,MAAMC,kBAAkB,GAAGC,OAAO,CAACC,YAAY,CAAC,iBAAiB,EAAE,CACjE,gBAAgB,CACjB,CAAC;IACF,MAAMC,iBAAiB,GAAGF,OAAO,CAACC,YAAY,CAAC,gBAAgB,EAAE,CAC/D,sBAAsB,CACvB,CAAC;IACF,MAAME,oBAAoB,GAAGH,OAAO,CAACC,YAAY,CAAC,mBAAmB,EAAE,CACrE,QAAQ,CACT,CAAC;IACF,MAAMG,4BAA4B,GAAGJ,OAAO,CAACC,YAAY,CACvD,2BAA2B,EAC3B,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAC9B;IACD,MAAMI,eAAe,GAAGL,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAC3D,OAAO,EACP,SAAS,CACV,CAAC;IACF,MAAMK,uBAAuB,GAAGN,OAAO,CAACC,YAAY,CAClD,sBAAsB,EACtB,CAAC,QAAQ,EAAE,2BAA2B,CAAC,CACxC;IAED,MAAMzE,OAAO,CAAC+E,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CACPpE,+BAA+B,EAC/Bb,uBAAuB,EACvBI,uBAAuB,EACvBD,mBAAmB,CACpB;MACD+E,SAAS,EAAE,CACT;QAAEC,OAAO,EAAE9E,eAAe;QAAE+E,QAAQ,EAAEZ;MAAkB,CAAE,EAC1D;QAAEW,OAAO,EAAE3E,cAAc;QAAE4E,QAAQ,EAAET;MAAiB,CAAE,EACxD;QAAEQ,OAAO,EAAEzE,iBAAiB;QAAE0E,QAAQ,EAAER;MAAoB,CAAE,EAC9D;QACEO,OAAO,EAAE7E,yBAAyB;QAClC8E,QAAQ,EAAEP;OACX,EACD;QAAEM,OAAO,EAAE1E,YAAY;QAAE2E,QAAQ,EAAEN;MAAe,CAAE,EACpD;QAAEK,OAAO,EAAE5E,oBAAoB;QAAE6E,QAAQ,EAAEL;MAAuB,CAAE,EACpEhF,QAAQ,EACRG,WAAW;KAEd,CAAC,CAACmF,iBAAiB,EAAE;IAEtBpE,eAAe,GAAGhB,OAAO,CAACqF,MAAM,CAC9BjF,eAAe,CACmB;IACpCa,cAAc,GAAGjB,OAAO,CAACqF,MAAM,CAC7B9E,cAAc,CACmB;IACnCW,iBAAiB,GAAGlB,OAAO,CAACqF,MAAM,CAChC5E,iBAAiB,CACmB;IACtCU,yBAAyB,GAAGnB,OAAO,CAACqF,MAAM,CACxChF,yBAAyB,CACmB;IAC9Ce,YAAY,GAAGpB,OAAO,CAACqF,MAAM,CAAC7E,YAAY,CAAiC;IAC3Ea,oBAAoB,GAAGrB,OAAO,CAACqF,MAAM,CACnC/E,oBAAoB,CACmB;IAEzCU,eAAe,CAACsE,cAAc,CAACC,GAAG,CAACC,WAAW,CAAC9E,EAAE,CAACqC,mBAAmB,CAAC,CAAC;IACvE9B,cAAc,CAACwE,oBAAoB,CAACF,GAAG,CAACC,WAAW,CAAC9E,EAAE,CAAC,EAAE,CAAC,CAAC;IAC3DQ,iBAAiB,CAACwE,MAAM,CAACH,GAAG,CAACC,WAAW,CAAC9E,EAAE,CAAC,EAAE,CAAC,CAAC;IAChDS,yBAAyB,CAACwE,eAAe,CAACJ,GAAG,CAACC,WAAW,CAAC9E,EAAE,CAAC,EAAE,CAAC,CAAC;IACjEW,oBAAoB,CAACuE,yBAAyB,CAACL,GAAG,CAACC,WAAW,CAC5D9E,EAAE,CAAC,CAACY,UAAU,CAAC,CAAC,CACjB;EACH,CAAC,EAAC;EAEF+C,UAAU,CAAC,MAAK;IACdtD,OAAO,GAAGf,OAAO,CAAC6F,eAAe,CAACjF,+BAA+B,CAAC;IAClEE,SAAS,GAAGC,OAAO,CAAC+E,iBAAiB;IACrChF,SAAS,CAACiF,MAAM,GAAGzE,UAAU;IAC7BP,OAAO,CAACiF,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACpF,SAAS,CAAC,CAACqF,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,sCAAsC,EAAE,MAAK;IAC9CC,MAAM,CAAClF,eAAe,CAACsE,cAAc,CAAC,CAACc,oBAAoB,CAAC,CAAC,CAAC;IAC9DF,MAAM,CAACpF,SAAS,CAACuF,eAAe,CAAC,CAACC,OAAO,CAACvD,mBAAmB,CAAC;EAChE,CAAC,CAAC;EAEFkD,EAAE,CAAC,mDAAmD,EAAE,MAAK;IAC3DjF,eAAe,CAACsE,cAAc,CAACC,GAAG,CAACC,WAAW,CAC5C7E,UAAU,CAAC,MAAM,IAAI4F,KAAK,EAAE,CAAC,CAC9B;IACDzF,SAAS,CAAC0F,mBAAmB,EAAE;IAC/BN,MAAM,CAAC9E,YAAY,CAACqF,KAAK,CAAC,CAACL,oBAAoB,CAC7C,wCAAwC,CACzC;EACH,CAAC,CAAC;EAEFH,EAAE,CAAC,oCAAoC,EAAE,MAAK;IAC5CC,MAAM,CAACpF,SAAS,CAACiF,MAAM,CAAC,CAACW,WAAW,EAAE;IACtCR,MAAM,CAACpF,SAAS,CAACiF,MAAM,CAACxE,EAAE,CAAC,CAACoF,IAAI,CAAC,CAAC,CAAC;IACnCT,MAAM,CAACpF,SAAS,CAACiF,MAAM,CAACvE,YAAY,CAAC,CAACmF,IAAI,CAAC,CAAC,CAAC;EAC/C,CAAC,CAAC;EAEFV,EAAE,CAAC,8CAA8C,EAAE,MAAK;IACtD,MAAMnE,kBAAkB,GAAGhB,SAAS,CAACiF,MAAM,CAACjE,kBAAkB;IAC9DoE,MAAM,CAACpE,kBAAkB,CAAC,CAAC4E,WAAW,EAAE;IACxC,IAAI5E,kBAAkB,EAAEG,QAAQ,EAAE;MAChCiE,MAAM,CAACpE,kBAAkB,CAACG,QAAQ,CAACC,cAAc,CAAC,CAACyE,IAAI,CAAC,IAAI,CAAC;MAC7DT,MAAM,CAACpE,kBAAkB,CAACG,QAAQ,CAACE,cAAc,CAAC,CAACwE,IAAI,CAAC,OAAO,CAAC;IAClE;EACF,CAAC,CAAC;EAEFV,EAAE,CAAC,oCAAoC,EAAE,MAAK;IAC5CnF,SAAS,CAAC8F,WAAW,CAACC,UAAU,CAAC;MAAEC,oBAAoB,EAAE;IAAI,CAAE,CAAC;IAChEZ,MAAM,CAACpF,SAAS,CAAC8F,WAAW,CAACG,GAAG,CAAC,eAAe,CAAC,EAAEC,OAAO,CAAC,CAACC,QAAQ,EAAE;IAEtEnG,SAAS,CAAC8F,WAAW,CAACC,UAAU,CAAC;MAAEC,oBAAoB,EAAE;IAAK,CAAE,CAAC;IACjEZ,MAAM,CAACpF,SAAS,CAAC8F,WAAW,CAACG,GAAG,CAAC,eAAe,CAAC,EAAEG,QAAQ,CAAC,CAACD,QAAQ,EAAE;EACzE,CAAC,CAAC;EAEFhB,EAAE,CAAC,mEAAmE,EAAE,MAAK;IAC3EnF,SAAS,CAAC8F,WAAW,CAACC,UAAU,CAAC;MAC/BC,oBAAoB,EAAE,IAAI;MAC1BK,aAAa,EAAE,CAAC;KACjB,CAAC;IACFjB,MAAM,CAACpF,SAAS,CAAC8F,WAAW,CAACG,GAAG,CAAC,eAAe,CAAC,EAAEK,KAAK,CAAC,CAACC,SAAS,EAAE;IAErEvG,SAAS,CAAC8F,WAAW,CAACC,UAAU,CAAC;MAC/BM,aAAa,EAAE;KAChB,CAAC;IACFjB,MAAM,CAACpF,SAAS,CAAC8F,WAAW,CAACG,GAAG,CAAC,eAAe,CAAC,EAAEK,KAAK,CAAC,CAACH,QAAQ,EAAE;EACtE,CAAC,CAAC;EAEFhB,EAAE,CAAC,8BAA8B,EAAE,MAAK;IACtCnF,SAAS,CAACwG,QAAQ,EAAE;IACpBpB,MAAM,CAACjF,cAAc,CAACwE,oBAAoB,CAAC,CAACW,oBAAoB,CAC9D9E,UAAU,CAACC,EAAE,CACd;EACH,CAAC,CAAC;EAEF0E,EAAE,CAAC,iCAAiC,EAAE,MAAK;IACzCC,MAAM,CAAChF,iBAAiB,CAACwE,MAAM,CAAC,CAAC6B,gBAAgB,EAAE;EACrD,CAAC,CAAC;EAEFtB,EAAE,CAAC,2CAA2C,EAAE,MAAK;IACnDC,MAAM,CAAC/E,yBAAyB,CAACwE,eAAe,CAAC,CAAC4B,gBAAgB,EAAE;EACtE,CAAC,CAAC;EAEFtB,EAAE,CAAC,oCAAoC,EAAE,MAAK;IAC5CnF,SAAS,CAAC0G,oBAAoB,EAAE;IAChCtB,MAAM,CAACpF,SAAS,CAAC2G,mBAAmB,CAAC,CAACR,QAAQ,EAAE;IAEhDnG,SAAS,CAAC0G,oBAAoB,EAAE;IAChCtB,MAAM,CAACpF,SAAS,CAAC2G,mBAAmB,CAAC,CAACJ,SAAS,EAAE;EACnD,CAAC,CAAC;EAEFpB,EAAE,CAAC,6BAA6B,EAAE,MAAK;IACrCnF,SAAS,CAAC4G,oBAAoB,CAAC,IAAI,CAAC;IACpCxB,MAAM,CAACpF,SAAS,CAAC6G,WAAW,CAAC,CAACV,QAAQ,EAAE;IAExCnG,SAAS,CAAC4G,oBAAoB,CAAC,KAAK,CAAC;IACrCxB,MAAM,CAACpF,SAAS,CAAC6G,WAAW,CAAC,CAACN,SAAS,EAAE;EAC3C,CAAC,CAAC;EAEFpB,EAAE,CAAC,gCAAgC,EAAE,MAAK;IACxC,MAAM2B,WAAW,GAAG;MAClBrG,EAAE,EAAE,CAAC;MACLsG,aAAa,EAAE,CAAC;MAChBC,WAAW,EAAE,IAAIpG,IAAI,EAAE;MACvBqG,KAAK,EAAE,OAAO;MACd5E,YAAY,EAAE,OAAO;MACrBY,UAAU,EAAE,OAAO;MACnBiE,SAAS,EAAE,OAAO;MAClBC,SAAS,EAAE,CAAC;MACZC,eAAe,EAAE,CAAC;MAClBC,iBAAiB,EAAE;KACpB;IAEDlH,cAAc,CAACwE,oBAAoB,CAACF,GAAG,CAACC,WAAW,CAAC9E,EAAE,CAAC,CAACkH,WAAW,CAAC,CAAC,CAAC;IACtEvG,oBAAoB,CAACuE,yBAAyB,CAACL,GAAG,CAACC,WAAW,CAC5D9E,EAAE,CAAC,CAACY,UAAU,CAAC,CAAC,CACjB;IAEDR,SAAS,CAACsH,YAAY,EAAE;IAExBlC,MAAM,CAAC7E,oBAAoB,CAACuE,yBAAyB,CAAC,CAACQ,oBAAoB,CACzE9E,UAAU,CAACO,oBAAoB,CAChC;IACDqE,MAAM,CAACjF,cAAc,CAACwE,oBAAoB,CAAC,CAACW,oBAAoB,CAC9D9E,UAAU,CAACC,EAAE,CACd;IACD2E,MAAM,CAACpF,SAAS,CAACuH,SAAS,CAAC,CAAC1B,IAAI,CAAC,CAAC,CAAC;EACrC,CAAC,CAAC;EAEFV,EAAE,CAAC,4CAA4C,EAAE,MAAK;IACpD5E,oBAAoB,CAACuE,yBAAyB,CAACL,GAAG,CAACC,WAAW,CAC5D7E,UAAU,CAAC,MAAM,IAAI4F,KAAK,EAAE,CAAC,CAC9B;IAEDzF,SAAS,CAACsH,YAAY,EAAE;IAExBlC,MAAM,CAAC9E,YAAY,CAACqF,KAAK,CAAC,CAACL,oBAAoB,CAC7C,8BAA8B,CAC/B;EACH,CAAC,CAAC;EAEFH,EAAE,CAAC,kCAAkC,EAAE,MAAK;IAC1C,MAAM8B,KAAK,GAAG,OAAO;IACrB,MAAMO,cAAc,GAAGxH,SAAS,CAACyH,cAAc,CAACR,KAAK,CAAC;IACtD7B,MAAM,CAACoC,cAAc,CAAC,CAACE,SAAS,CAAC,cAAc,CAAC;EAClD,CAAC,CAAC;EAEFvC,EAAE,CAAC,kDAAkD,EAAE,MAAK;IAC1DC,MAAM,CAACpF,SAAS,CAACyH,cAAc,CAACE,SAAS,CAAC,CAAC,CAAC9B,IAAI,CAAC,KAAK,CAAC;EACzD,CAAC,CAAC;EAEFV,EAAE,CAAC,8BAA8B,EAAE,MAAK;IACtC,MAAMyC,IAAI,GAAG,IAAIhH,IAAI,CAAC,0BAA0B,CAAC;IACjD,MAAMiH,aAAa,GAAG7H,SAAS,CAAC8H,UAAU,CAACF,IAAI,CAAC;IAChDxC,MAAM,CAACyC,aAAa,CAAC,CAAChC,IAAI,CAAC,YAAY,CAAC;EAC1C,CAAC,CAAC;EAEFV,EAAE,CAAC,8CAA8C,EAAE,MAAK;IACtDC,MAAM,CAACpF,SAAS,CAAC8H,UAAU,CAACH,SAAS,CAAC,CAAC,CAAC9B,IAAI,CAAC,KAAK,CAAC;EACrD,CAAC,CAAC;EAEFV,EAAE,CAAC,uDAAuD,EAAE,MAAK;IAC/D4C,KAAK,CAAC/H,SAAS,CAACgI,YAAY,EAAE,MAAM,CAAC;IACrChI,SAAS,CAAC0F,mBAAmB,EAAE;IAC/BN,MAAM,CAACpF,SAAS,CAACgI,YAAY,CAACC,IAAI,CAAC,CAACxB,gBAAgB,EAAE;EACxD,CAAC,CAAC;EAEFtB,EAAE,CAAC,0CAA0C,EAAE,MAAK;IAClDC,MAAM,CACJpF,SAAS,CAAC8F,WAAW,CAACG,GAAG,CAAC,sBAAsB,CAAC,EAAEgB,KAAK,CACzD,CAACiB,SAAS,EAAE;IACb9C,MAAM,CAACpF,SAAS,CAAC8F,WAAW,CAACG,GAAG,CAAC,eAAe,CAAC,EAAEG,QAAQ,CAAC,CAACD,QAAQ,EAAE;EACzE,CAAC,CAAC;EAEFhB,EAAE,CAAC,sEAAsE,EAAE,MAAK;IAC9EnF,SAAS,CAAC8F,WAAW,CAACC,UAAU,CAAC;MAAEC,oBAAoB,EAAE;IAAI,CAAE,CAAC;IAChEZ,MAAM,CAACpF,SAAS,CAAC8F,WAAW,CAACG,GAAG,CAAC,eAAe,CAAC,EAAEC,OAAO,CAAC,CAACC,QAAQ,EAAE;EACxE,CAAC,CAAC;EAEFhB,EAAE,CAAC,wEAAwE,EAAE,MAAK;IAChFnF,SAAS,CAAC8F,WAAW,CAACC,UAAU,CAAC;MAAEC,oBAAoB,EAAE;IAAK,CAAE,CAAC;IACjEZ,MAAM,CAACpF,SAAS,CAAC8F,WAAW,CAACG,GAAG,CAAC,eAAe,CAAC,EAAEG,QAAQ,CAAC,CAACD,QAAQ,EAAE;EACzE,CAAC,CAAC;EAEFhB,EAAE,CAAC,kEAAkE,EAAE,MAAK;IAC1E,MAAM5E,oBAAoB,GAAGrB,OAAO,CAACqF,MAAM,CACzC/E,oBAAoB,CACmB;IACzCe,oBAAoB,CAACuE,yBAAyB,CAACqD,KAAK,CAACC,KAAK,EAAE;IAC5DpI,SAAS,CAACiF,MAAM,GAAG0C,SAAqC;IACxD3H,SAAS,CAACsH,YAAY,EAAE;IACxBlC,MAAM,CACJ7E,oBAAoB,CAACuE,yBAAyB,CAC/C,CAACuD,GAAG,CAAC5B,gBAAgB,EAAE;EAC1B,CAAC,CAAC;EAEFtB,EAAE,CAAC,+CAA+C,EAAE,MAAK;IACvD,MAAM7E,YAAY,GAAGpB,OAAO,CAACqF,MAAM,CACjC7E,YAAY,CACmB;IACjCU,iBAAiB,CAACwE,MAAM,CAACH,GAAG,CAACC,WAAW,CACtC7E,UAAU,CAAC,MAAM,IAAI4F,KAAK,CAAC,OAAO,CAAC,CAAC,CACrC;IACDzF,SAAS,CAACwG,QAAQ,EAAE;IACpBpB,MAAM,CAAC9E,YAAY,CAACqF,KAAK,CAAC,CAACL,oBAAoB,CAC7C,kCAAkC,CACnC;EACH,CAAC,CAAC;EAEFH,EAAE,CAAC,iDAAiD,EAAE,MAAK;IACzD,MAAMmD,eAAe,GAAiB,CACpC;MACE7H,EAAE,EAAE,CAAC;MACL0B,QAAQ,EAAE,UAAU;MACpBoG,QAAQ,EAAE,GAAG;MACbC,QAAQ,EAAE,KAAK;MACfC,KAAK,EAAE,eAAe;MACtBC,MAAM,EAAE;QAAEjI,EAAE,EAAE,CAAC;QAAEoB,IAAI,EAAE;MAAI;KAC5B,EACD;MACEpB,EAAE,EAAE,CAAC;MACL0B,QAAQ,EAAE,YAAY;MACtBoG,QAAQ,EAAE,GAAG;MACbC,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE,eAAe;MACtBC,MAAM,EAAE;QAAEjI,EAAE,EAAE,CAAC;QAAEoB,IAAI,EAAE;MAAI;KAC5B,CACF;IACD7B,SAAS,CAAC2I,WAAW,GAAGL,eAAe;IAEvC,MAAMM,QAAQ,GAAG5I,SAAS,CAAC,oBAAoB,CAAC,CAAC,MAAM,CAAC;IACxDoF,MAAM,CAACwD,QAAQ,CAACC,MAAM,CAAC,CAAChD,IAAI,CAAC,CAAC,CAAC;IAC/BT,MAAM,CAACwD,QAAQ,CAAC,CAAC,CAAC,CAACzG,QAAQ,CAAC,CAAC0D,IAAI,CAAC,UAAU,CAAC;EAC/C,CAAC,CAAC;EAEFV,EAAE,CAAC,qCAAqC,EAAE,MAAK;IAC7C,MAAM2D,cAAc,GAAe;MACjCrI,EAAE,EAAE,CAAC;MACL0B,QAAQ,EAAE,UAAU;MACpBoG,QAAQ,EAAE,GAAG;MACbC,QAAQ,EAAE,KAAK;MACfC,KAAK,EAAE,eAAe;MACtBC,MAAM,EAAE;QAAEjI,EAAE,EAAE,CAAC;QAAEoB,IAAI,EAAE;MAAI;KAC5B;IACD,MAAMkH,WAAW,GAAG/I,SAAS,CAACgJ,iBAAiB,CAACF,cAAc,CAAC;IAC/D1D,MAAM,CAAC2D,WAAW,CAAC,CAAClD,IAAI,CAAC,gBAAgB,CAAC;IAE1C,MAAMoD,cAAc,GAAG,IAA6B;IACpD7D,MAAM,CAACpF,SAAS,CAACgJ,iBAAiB,CAACC,cAAc,CAAC,CAAC,CAACpD,IAAI,CAAC,EAAE,CAAC;EAC9D,CAAC,CAAC;EAEFV,EAAE,CAAC,oCAAoC,EAAE,MAAK;IAC5CnF,SAAS,CAACkJ,kBAAkB,EAAE;IAC9B9D,MAAM,CAAC9E,YAAY,CAAC6I,OAAO,CAAC,CAAC7D,oBAAoB,CAC/C,4CAA4C,CAC7C;EACH,CAAC,CAAC;EAEFH,EAAE,CAAC,oCAAoC,EAAE,MAAK;IAC5CC,MAAM,CAACpF,SAAS,CAACoJ,YAAY,CAAC,CAAC,CAAC,CAAC,CAACvD,IAAI,CAAC,OAAO,CAAC;IAC/CT,MAAM,CAACpF,SAAS,CAACoJ,YAAY,CAAC,EAAE,CAAC,CAAC,CAACvD,IAAI,CAAC,WAAW,CAAC;EACtD,CAAC,CAAC;EAEFV,EAAE,CAAC,8CAA8C,EAAE,MAAK;IACtDC,MAAM,CAACpF,SAAS,CAAC8H,UAAU,CAACH,SAAS,CAAC,CAAC,CAAC9B,IAAI,CAAC,KAAK,CAAC;IACnDT,MAAM,CAACpF,SAAS,CAACyH,cAAc,CAACE,SAAS,CAAC,CAAC,CAAC9B,IAAI,CAAC,KAAK,CAAC;EACzD,CAAC,CAAC;EAEFV,EAAE,CAAC,sDAAsD,EAAE,MAAK;IAC9DnF,SAAS,CAACqJ,YAAY,GAAG,IAAI;IAC7BrJ,SAAS,CAAC,gBAAgB,CAAC,EAAE;IAC7BoF,MAAM,CACJpF,SAAS,CAAC8F,WAAW,CAACG,GAAG,CAAC,sBAAsB,CAAC,EAAEG,QAAQ,CAC5D,CAACD,QAAQ,EAAE;EACd,CAAC,CAAC;EAEFpG,QAAQ,CAAC,iBAAiB,EAAE,MAAK;IAC/BoF,EAAE,CAAC,mEAAmE,EAAE,MAAK;MAC3EnF,SAAS,CAAC8F,WAAW,CAACC,UAAU,CAAC;QAAEC,oBAAoB,EAAE;MAAI,CAAE,CAAC;MAChEZ,MAAM,CAACpF,SAAS,CAAC8F,WAAW,CAACG,GAAG,CAAC,eAAe,CAAC,EAAEC,OAAO,CAAC,CAACC,QAAQ,EAAE;MAEtEnG,SAAS,CAAC8F,WAAW,CAACC,UAAU,CAAC;QAAEM,aAAa,EAAE,CAAC;MAAC,CAAE,CAAC;MACvDjB,MAAM,CAACpF,SAAS,CAAC8F,WAAW,CAACG,GAAG,CAAC,eAAe,CAAC,EAAEK,KAAK,CAAC,CAACC,SAAS,EAAE;MAErEvG,SAAS,CAAC8F,WAAW,CAACC,UAAU,CAAC;QAAEM,aAAa,EAAE;MAAK,CAAE,CAAC;MAC1DjB,MAAM,CAACpF,SAAS,CAAC8F,WAAW,CAACG,GAAG,CAAC,eAAe,CAAC,EAAEK,KAAK,CAAC,CAACC,SAAS,EAAE;MAErEvG,SAAS,CAAC8F,WAAW,CAACC,UAAU,CAAC;QAAEM,aAAa,EAAE;MAAG,CAAE,CAAC;MACxDjB,MAAM,CAACpF,SAAS,CAAC8F,WAAW,CAACG,GAAG,CAAC,eAAe,CAAC,EAAEK,KAAK,CAAC,CAACH,QAAQ,EAAE;IACtE,CAAC,CAAC;IAEFhB,EAAE,CAAC,mCAAmC,EAAE,MAAK;MAC3C,MAAMmE,mBAAmB,GAAGvB,KAAK,CAAC/H,SAAS,CAACuJ,kBAAkB,EAAE,MAAM,CAAC;MACvEvJ,SAAS,CAAC4G,oBAAoB,CAAC,IAAI,CAAC;MACpC5G,SAAS,CAAC8F,WAAW,CAACC,UAAU,CAAC;QAC/BC,oBAAoB,EAAE,IAAI;QAC1BK,aAAa,EAAE;OAChB,CAAC;MAEFjB,MAAM,CAACkE,mBAAmB,CAAC,CAAChE,oBAAoB,CAAC,IAAI,CAAC;IACxD,CAAC,CAAC;IAEFH,EAAE,CAAC,sDAAsD,EAAE,MAAK;MAC9DnF,SAAS,CAACqJ,YAAY,GAAG,IAAI;MAC7BrJ,SAAS,CAAC,gBAAgB,CAAC,EAAE;MAC7BoF,MAAM,CACJpF,SAAS,CAAC8F,WAAW,CAACG,GAAG,CAAC,sBAAsB,CAAC,EAAEG,QAAQ,CAC5D,CAACD,QAAQ,EAAE;IACd,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFpG,QAAQ,CAAC,iCAAiC,EAAE,MAAK;IAC/CoF,EAAE,CAAC,oDAAoD,EAAE,MAAK;MAC5DjF,eAAe,CAACsE,cAAc,CAACC,GAAG,CAACC,WAAW,CAC5C7E,UAAU,CAAC,MAAM,IAAI4F,KAAK,CAAC,eAAe,CAAC,CAAC,CAC7C;MACDzF,SAAS,CAAC0F,mBAAmB,EAAE;MAC/BN,MAAM,CAAC9E,YAAY,CAACqF,KAAK,CAAC,CAACL,oBAAoB,CAC7C,wCAAwC,CACzC;IACH,CAAC,CAAC;IAEFH,EAAE,CAAC,4CAA4C,EAAE,MAAK;MACpD5E,oBAAoB,CAACuE,yBAAyB,CAACL,GAAG,CAACC,WAAW,CAC5D7E,UAAU,CAAC,MAAM,IAAI4F,KAAK,CAAC,eAAe,CAAC,CAAC,CAC7C;MACDzF,SAAS,CAACsH,YAAY,EAAE;MACxBlC,MAAM,CAAC9E,YAAY,CAACqF,KAAK,CAAC,CAACL,oBAAoB,CAC7C,8BAA8B,CAC/B;IACH,CAAC,CAAC;IAEFH,EAAE,CAAC,+CAA+C,EAAE,MAAK;MACvD/E,iBAAiB,CAACwE,MAAM,CAACH,GAAG,CAACC,WAAW,CACtC7E,UAAU,CAAC,MAAM,IAAI4F,KAAK,CAAC,eAAe,CAAC,CAAC,CAC7C;MACDzF,SAAS,CAACwG,QAAQ,EAAE;MACpBpB,MAAM,CAAC9E,YAAY,CAACqF,KAAK,CAAC,CAACL,oBAAoB,CAC7C,kCAAkC,CACnC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFvF,QAAQ,CAAC,uBAAuB,EAAE,MAAK;IACrCwD,UAAU,CAAC,MAAK;MACd,MAAMuF,cAAc,GAAG;QACrBrI,EAAE,EAAE,CAAC;QACL8H,QAAQ,EAAE,SAAS;QACnBpG,QAAQ,EAAE,iBAAiB;QAC3BsG,KAAK,EAAE,eAAe;QACtBD,QAAQ,EAAE,eAAe;QACzBE,MAAM,EAAE;UAAEjI,EAAE,EAAE,CAAC;UAAEoB,IAAI,EAAE;QAAI;OAC5B;MACD7B,SAAS,CAAC2I,WAAW,GAAG,CAACG,cAAc,CAAC;MACxC9I,SAAS,CAACwJ,cAAc,CAACzD,UAAU,CAAC;QAAE0D,UAAU,EAAEX;MAAc,CAAE,CAAC;IACrE,CAAC,CAAC;IAEF3D,EAAE,CAAC,oDAAoD,EAAE,MAAK;MAC5D9E,yBAAyB,CAACqJ,MAAM,CAACjF,GAAG,CAACC,WAAW,CAC9C9E,EAAE,CAAC;QAAEa,EAAE,EAAE,CAAC;QAAEkJ,YAAY,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAC,CAAE,CAAC,CAC9C;MAED5J,SAAS,CAAC6J,cAAc,EAAE;MAE1BzE,MAAM,CAAC/E,yBAAyB,CAACqJ,MAAM,CAAC,CAACpE,oBAAoB,CAAC;QAC5DqE,YAAY,EAAE,CAAC;QACfC,UAAU,EAAE;OACb,CAAC;MACFxE,MAAM,CAAC9E,YAAY,CAAC6I,OAAO,CAAC,CAAC7D,oBAAoB,CAC/C,kCAAkC,CACnC;IACH,CAAC,CAAC;IAEFH,EAAE,CAAC,kDAAkD,EAAE,MAAK;MAC1D9E,yBAAyB,CAACqJ,MAAM,CAACjF,GAAG,CAACC,WAAW,CAC9C7E,UAAU,CAAC,MAAM,IAAI4F,KAAK,EAAE,CAAC,CAC9B;MAEDzF,SAAS,CAAC6J,cAAc,EAAE;MAE1BzE,MAAM,CAAC9E,YAAY,CAACqF,KAAK,CAAC,CAACL,oBAAoB,CAC7C,gCAAgC,CACjC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFvF,QAAQ,CAAC,iBAAiB,EAAE,MAAK;IAC/BoF,EAAE,CAAC,+BAA+B,EAAE,MAAK;MACvC,MAAMyC,IAAI,GAAG,IAAIhH,IAAI,CAAC,qBAAqB,CAAC;MAC5CwE,MAAM,CAACpF,SAAS,CAAC8H,UAAU,CAACF,IAAI,CAAC,CAAC,CAAC/B,IAAI,CAAC,YAAY,CAAC;MACrDT,MAAM,CAACpF,SAAS,CAAC8H,UAAU,CAACH,SAAS,CAAC,CAAC,CAAC9B,IAAI,CAAC,KAAK,CAAC;MACnDT,MAAM,CAACpF,SAAS,CAAC8H,UAAU,CAAC,EAAE,CAAC,CAAC,CAACjC,IAAI,CAAC,KAAK,CAAC;IAC9C,CAAC,CAAC;IAEFV,EAAE,CAAC,yCAAyC,EAAE,MAAK;MACjD,MAAMqC,cAAc,GAAGxH,SAAS,CAACyH,cAAc,CAAC,OAAO,CAAC;MACxDrC,MAAM,CAACoC,cAAc,CAAC,CAACE,SAAS,CAAC,cAAc,CAAC;MAChDtC,MAAM,CAACoC,cAAc,CAAC,CAACE,SAAS,CAAC,KAAK,CAAC;MACvCtC,MAAM,CAACpF,SAAS,CAACyH,cAAc,CAACE,SAAS,CAAC,CAAC,CAAC9B,IAAI,CAAC,KAAK,CAAC;IACzD,CAAC,CAAC;IAEFV,EAAE,CAAC,kCAAkC,EAAE,MAAK;MAC1CC,MAAM,CAACpF,SAAS,CAACoJ,YAAY,CAAC,CAAC,CAAC,CAAC,CAACvD,IAAI,CAAC,OAAO,CAAC;MAC/CT,MAAM,CAACpF,SAAS,CAACoJ,YAAY,CAAC,EAAE,CAAC,CAAC,CAACvD,IAAI,CAAC,WAAW,CAAC;IACtD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF9F,QAAQ,CAAC,iBAAiB,EAAE,MAAK;IAC/BoF,EAAE,CAAC,qCAAqC,EAAE,MAAK;MAC7C,MAAM2E,eAAe,GAAG/B,KAAK,CAAC/H,SAAS,CAACuJ,kBAAkB,EAAE,MAAM,CAAC;MACnEvJ,SAAS,CAAC4G,oBAAoB,CAAC,IAAI,CAAC;MACpCxB,MAAM,CAACpF,SAAS,CAAC6G,WAAW,CAAC,CAACV,QAAQ,EAAE;MACxCf,MAAM,CAAC0E,eAAe,CAAC,CAACrD,gBAAgB,EAAE;IAC5C,CAAC,CAAC;IAEFtB,EAAE,CAAC,oCAAoC,EAAE,MAAK;MAC5CnF,SAAS,CAACkJ,kBAAkB,EAAE;MAC9B9D,MAAM,CAAC9E,YAAY,CAAC6I,OAAO,CAAC,CAAC7D,oBAAoB,CAC/C,4CAA4C,CAC7C;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}