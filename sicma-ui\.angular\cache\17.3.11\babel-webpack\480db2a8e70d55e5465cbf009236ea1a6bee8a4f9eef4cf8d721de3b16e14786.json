{"ast": null, "code": "function cov_1gavaw23lm() {\n  var path = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contract-management\\\\pages\\\\contract-detail-page\\\\contract-detail-page.component.ts\";\n  var hash = \"5409bf1c56a788a1063b5b0e7d715657b0deeae8\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contract-management\\\\pages\\\\contract-detail-page\\\\contract-detail-page.component.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 27,\n          column: 34\n        },\n        end: {\n          line: 194,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 29,\n          column: 8\n        },\n        end: {\n          line: 29,\n          column: 27\n        }\n      },\n      \"2\": {\n        start: {\n          line: 30,\n          column: 8\n        },\n        end: {\n          line: 30,\n          column: 47\n        }\n      },\n      \"3\": {\n        start: {\n          line: 31,\n          column: 8\n        },\n        end: {\n          line: 31,\n          column: 29\n        }\n      },\n      \"4\": {\n        start: {\n          line: 32,\n          column: 8\n        },\n        end: {\n          line: 32,\n          column: 27\n        }\n      },\n      \"5\": {\n        start: {\n          line: 33,\n          column: 8\n        },\n        end: {\n          line: 33,\n          column: 31\n        }\n      },\n      \"6\": {\n        start: {\n          line: 34,\n          column: 8\n        },\n        end: {\n          line: 34,\n          column: 29\n        }\n      },\n      \"7\": {\n        start: {\n          line: 35,\n          column: 8\n        },\n        end: {\n          line: 35,\n          column: 36\n        }\n      },\n      \"8\": {\n        start: {\n          line: 36,\n          column: 8\n        },\n        end: {\n          line: 36,\n          column: 40\n        }\n      },\n      \"9\": {\n        start: {\n          line: 39,\n          column: 8\n        },\n        end: {\n          line: 41,\n          column: 11\n        }\n      },\n      \"10\": {\n        start: {\n          line: 40,\n          column: 12\n        },\n        end: {\n          line: 40,\n          column: 49\n        }\n      },\n      \"11\": {\n        start: {\n          line: 44,\n          column: 8\n        },\n        end: {\n          line: 44,\n          column: 28\n        }\n      },\n      \"12\": {\n        start: {\n          line: 45,\n          column: 8\n        },\n        end: {\n          line: 105,\n          column: 11\n        }\n      },\n      \"13\": {\n        start: {\n          line: 48,\n          column: 12\n        },\n        end: {\n          line: 48,\n          column: 43\n        }\n      },\n      \"14\": {\n        start: {\n          line: 49,\n          column: 12\n        },\n        end: {\n          line: 49,\n          column: 53\n        }\n      },\n      \"15\": {\n        start: {\n          line: 50,\n          column: 12\n        },\n        end: {\n          line: 50,\n          column: 60\n        }\n      },\n      \"16\": {\n        start: {\n          line: 51,\n          column: 27\n        },\n        end: {\n          line: 51,\n          column: 46\n        }\n      },\n      \"17\": {\n        start: {\n          line: 54,\n          column: 16\n        },\n        end: {\n          line: 98,\n          column: 17\n        }\n      },\n      \"18\": {\n        start: {\n          line: 55,\n          column: 20\n        },\n        end: {\n          line: 60,\n          column: 21\n        }\n      },\n      \"19\": {\n        start: {\n          line: 56,\n          column: 24\n        },\n        end: {\n          line: 59,\n          column: 26\n        }\n      },\n      \"20\": {\n        start: {\n          line: 61,\n          column: 20\n        },\n        end: {\n          line: 66,\n          column: 21\n        }\n      },\n      \"21\": {\n        start: {\n          line: 62,\n          column: 24\n        },\n        end: {\n          line: 65,\n          column: 26\n        }\n      },\n      \"22\": {\n        start: {\n          line: 67,\n          column: 20\n        },\n        end: {\n          line: 72,\n          column: 21\n        }\n      },\n      \"23\": {\n        start: {\n          line: 68,\n          column: 24\n        },\n        end: {\n          line: 71,\n          column: 26\n        }\n      },\n      \"24\": {\n        start: {\n          line: 73,\n          column: 20\n        },\n        end: {\n          line: 97,\n          column: 21\n        }\n      },\n      \"25\": {\n        start: {\n          line: 74,\n          column: 24\n        },\n        end: {\n          line: 77,\n          column: 25\n        }\n      },\n      \"26\": {\n        start: {\n          line: 75,\n          column: 45\n        },\n        end: {\n          line: 75,\n          column: 98\n        }\n      },\n      \"27\": {\n        start: {\n          line: 76,\n          column: 28\n        },\n        end: {\n          line: 76,\n          column: 61\n        }\n      },\n      \"28\": {\n        start: {\n          line: 78,\n          column: 24\n        },\n        end: {\n          line: 85,\n          column: 26\n        }\n      },\n      \"29\": {\n        start: {\n          line: 86,\n          column: 24\n        },\n        end: {\n          line: 96,\n          column: 25\n        }\n      },\n      \"30\": {\n        start: {\n          line: 87,\n          column: 28\n        },\n        end: {\n          line: 95,\n          column: 31\n        }\n      },\n      \"31\": {\n        start: {\n          line: 88,\n          column: 32\n        },\n        end: {\n          line: 90,\n          column: 33\n        }\n      },\n      \"32\": {\n        start: {\n          line: 89,\n          column: 36\n        },\n        end: {\n          line: 89,\n          column: 93\n        }\n      },\n      \"33\": {\n        start: {\n          line: 91,\n          column: 32\n        },\n        end: {\n          line: 94,\n          column: 35\n        }\n      },\n      \"34\": {\n        start: {\n          line: 99,\n          column: 16\n        },\n        end: {\n          line: 99,\n          column: 41\n        }\n      },\n      \"35\": {\n        start: {\n          line: 100,\n          column: 16\n        },\n        end: {\n          line: 100,\n          column: 81\n        }\n      },\n      \"36\": {\n        start: {\n          line: 103,\n          column: 16\n        },\n        end: {\n          line: 103,\n          column: 87\n        }\n      },\n      \"37\": {\n        start: {\n          line: 108,\n          column: 8\n        },\n        end: {\n          line: 108,\n          column: 33\n        }\n      },\n      \"38\": {\n        start: {\n          line: 111,\n          column: 8\n        },\n        end: {\n          line: 112,\n          column: 19\n        }\n      },\n      \"39\": {\n        start: {\n          line: 112,\n          column: 12\n        },\n        end: {\n          line: 112,\n          column: 19\n        }\n      },\n      \"40\": {\n        start: {\n          line: 113,\n          column: 41\n        },\n        end: {\n          line: 113,\n          column: 110\n        }\n      },\n      \"41\": {\n        start: {\n          line: 114,\n          column: 8\n        },\n        end: {\n          line: 150,\n          column: 9\n        }\n      },\n      \"42\": {\n        start: {\n          line: 115,\n          column: 12\n        },\n        end: {\n          line: 146,\n          column: 15\n        }\n      },\n      \"43\": {\n        start: {\n          line: 127,\n          column: 20\n        },\n        end: {\n          line: 144,\n          column: 21\n        }\n      },\n      \"44\": {\n        start: {\n          line: 128,\n          column: 24\n        },\n        end: {\n          line: 132,\n          column: 25\n        }\n      },\n      \"45\": {\n        start: {\n          line: 129,\n          column: 28\n        },\n        end: {\n          line: 131,\n          column: 44\n        }\n      },\n      \"46\": {\n        start: {\n          line: 133,\n          column: 24\n        },\n        end: {\n          line: 136,\n          column: 25\n        }\n      },\n      \"47\": {\n        start: {\n          line: 135,\n          column: 28\n        },\n        end: {\n          line: 135,\n          column: 139\n        }\n      },\n      \"48\": {\n        start: {\n          line: 139,\n          column: 24\n        },\n        end: {\n          line: 143,\n          column: 25\n        }\n      },\n      \"49\": {\n        start: {\n          line: 140,\n          column: 28\n        },\n        end: {\n          line: 142,\n          column: 50\n        }\n      },\n      \"50\": {\n        start: {\n          line: 149,\n          column: 12\n        },\n        end: {\n          line: 149,\n          column: 80\n        }\n      },\n      \"51\": {\n        start: {\n          line: 153,\n          column: 8\n        },\n        end: {\n          line: 155,\n          column: 9\n        }\n      },\n      \"52\": {\n        start: {\n          line: 154,\n          column: 12\n        },\n        end: {\n          line: 154,\n          column: 41\n        }\n      },\n      \"53\": {\n        start: {\n          line: 156,\n          column: 8\n        },\n        end: {\n          line: 158,\n          column: 9\n        }\n      },\n      \"54\": {\n        start: {\n          line: 157,\n          column: 12\n        },\n        end: {\n          line: 157,\n          column: 79\n        }\n      },\n      \"55\": {\n        start: {\n          line: 161,\n          column: 8\n        },\n        end: {\n          line: 166,\n          column: 9\n        }\n      },\n      \"56\": {\n        start: {\n          line: 162,\n          column: 12\n        },\n        end: {\n          line: 164,\n          column: 34\n        }\n      },\n      \"57\": {\n        start: {\n          line: 165,\n          column: 12\n        },\n        end: {\n          line: 165,\n          column: 83\n        }\n      },\n      \"58\": {\n        start: {\n          line: 169,\n          column: 8\n        },\n        end: {\n          line: 169,\n          column: 46\n        }\n      },\n      \"59\": {\n        start: {\n          line: 172,\n          column: 8\n        },\n        end: {\n          line: 174,\n          column: 9\n        }\n      },\n      \"60\": {\n        start: {\n          line: 173,\n          column: 12\n        },\n        end: {\n          line: 173,\n          column: 58\n        }\n      },\n      \"61\": {\n        start: {\n          line: 177,\n          column: 8\n        },\n        end: {\n          line: 179,\n          column: 9\n        }\n      },\n      \"62\": {\n        start: {\n          line: 178,\n          column: 12\n        },\n        end: {\n          line: 178,\n          column: 49\n        }\n      },\n      \"63\": {\n        start: {\n          line: 181,\n          column: 13\n        },\n        end: {\n          line: 187,\n          column: 6\n        }\n      },\n      \"64\": {\n        start: {\n          line: 181,\n          column: 41\n        },\n        end: {\n          line: 187,\n          column: 5\n        }\n      },\n      \"65\": {\n        start: {\n          line: 188,\n          column: 13\n        },\n        end: {\n          line: 193,\n          column: 6\n        }\n      },\n      \"66\": {\n        start: {\n          line: 195,\n          column: 0\n        },\n        end: {\n          line: 220,\n          column: 32\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 28,\n            column: 4\n          },\n          end: {\n            line: 28,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 28,\n            column: 64\n          },\n          end: {\n            line: 37,\n            column: 5\n          }\n        },\n        line: 28\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 38,\n            column: 4\n          },\n          end: {\n            line: 38,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 38,\n            column: 15\n          },\n          end: {\n            line: 42,\n            column: 5\n          }\n        },\n        line: 38\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 39,\n            column: 36\n          },\n          end: {\n            line: 39,\n            column: 37\n          }\n        },\n        loc: {\n          start: {\n            line: 39,\n            column: 48\n          },\n          end: {\n            line: 41,\n            column: 9\n          }\n        },\n        line: 39\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 43,\n            column: 4\n          },\n          end: {\n            line: 43,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 43,\n            column: 33\n          },\n          end: {\n            line: 106,\n            column: 5\n          }\n        },\n        line: 43\n      },\n      \"4\": {\n        name: \"(anonymous_4)\",\n        decl: {\n          start: {\n            line: 47,\n            column: 28\n          },\n          end: {\n            line: 47,\n            column: 29\n          }\n        },\n        loc: {\n          start: {\n            line: 47,\n            column: 41\n          },\n          end: {\n            line: 51,\n            column: 9\n          }\n        },\n        line: 47\n      },\n      \"5\": {\n        name: \"(anonymous_5)\",\n        decl: {\n          start: {\n            line: 51,\n            column: 21\n          },\n          end: {\n            line: 51,\n            column: 22\n          }\n        },\n        loc: {\n          start: {\n            line: 51,\n            column: 27\n          },\n          end: {\n            line: 51,\n            column: 46\n          }\n        },\n        line: 51\n      },\n      \"6\": {\n        name: \"(anonymous_6)\",\n        decl: {\n          start: {\n            line: 53,\n            column: 18\n          },\n          end: {\n            line: 53,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 53,\n            column: 32\n          },\n          end: {\n            line: 101,\n            column: 13\n          }\n        },\n        line: 53\n      },\n      \"7\": {\n        name: \"(anonymous_7)\",\n        decl: {\n          start: {\n            line: 87,\n            column: 39\n          },\n          end: {\n            line: 87,\n            column: 40\n          }\n        },\n        loc: {\n          start: {\n            line: 87,\n            column: 45\n          },\n          end: {\n            line: 95,\n            column: 29\n          }\n        },\n        line: 87\n      },\n      \"8\": {\n        name: \"(anonymous_8)\",\n        decl: {\n          start: {\n            line: 102,\n            column: 19\n          },\n          end: {\n            line: 102,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 102,\n            column: 30\n          },\n          end: {\n            line: 104,\n            column: 13\n          }\n        },\n        line: 102\n      },\n      \"9\": {\n        name: \"(anonymous_9)\",\n        decl: {\n          start: {\n            line: 107,\n            column: 4\n          },\n          end: {\n            line: 107,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 107,\n            column: 22\n          },\n          end: {\n            line: 109,\n            column: 5\n          }\n        },\n        line: 107\n      },\n      \"10\": {\n        name: \"(anonymous_10)\",\n        decl: {\n          start: {\n            line: 110,\n            column: 4\n          },\n          end: {\n            line: 110,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 110,\n            column: 33\n          },\n          end: {\n            line: 151,\n            column: 5\n          }\n        },\n        line: 110\n      },\n      \"11\": {\n        name: \"(anonymous_11)\",\n        decl: {\n          start: {\n            line: 126,\n            column: 22\n          },\n          end: {\n            line: 126,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 126,\n            column: 34\n          },\n          end: {\n            line: 145,\n            column: 17\n          }\n        },\n        line: 126\n      },\n      \"12\": {\n        name: \"(anonymous_12)\",\n        decl: {\n          start: {\n            line: 152,\n            column: 4\n          },\n          end: {\n            line: 152,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 152,\n            column: 35\n          },\n          end: {\n            line: 159,\n            column: 5\n          }\n        },\n        line: 152\n      },\n      \"13\": {\n        name: \"(anonymous_13)\",\n        decl: {\n          start: {\n            line: 160,\n            column: 4\n          },\n          end: {\n            line: 160,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 160,\n            column: 30\n          },\n          end: {\n            line: 167,\n            column: 5\n          }\n        },\n        line: 160\n      },\n      \"14\": {\n        name: \"(anonymous_14)\",\n        decl: {\n          start: {\n            line: 168,\n            column: 4\n          },\n          end: {\n            line: 168,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 168,\n            column: 38\n          },\n          end: {\n            line: 170,\n            column: 5\n          }\n        },\n        line: 168\n      },\n      \"15\": {\n        name: \"(anonymous_15)\",\n        decl: {\n          start: {\n            line: 171,\n            column: 4\n          },\n          end: {\n            line: 171,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 171,\n            column: 44\n          },\n          end: {\n            line: 175,\n            column: 5\n          }\n        },\n        line: 171\n      },\n      \"16\": {\n        name: \"(anonymous_16)\",\n        decl: {\n          start: {\n            line: 176,\n            column: 4\n          },\n          end: {\n            line: 176,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 176,\n            column: 36\n          },\n          end: {\n            line: 180,\n            column: 5\n          }\n        },\n        line: 176\n      },\n      \"17\": {\n        name: \"(anonymous_17)\",\n        decl: {\n          start: {\n            line: 181,\n            column: 35\n          },\n          end: {\n            line: 181,\n            column: 36\n          }\n        },\n        loc: {\n          start: {\n            line: 181,\n            column: 41\n          },\n          end: {\n            line: 187,\n            column: 5\n          }\n        },\n        line: 181\n      }\n    },\n    branchMap: {\n      \"0\": {\n        loc: {\n          start: {\n            line: 54,\n            column: 16\n          },\n          end: {\n            line: 98,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 54,\n            column: 16\n          },\n          end: {\n            line: 98,\n            column: 17\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 54\n      },\n      \"1\": {\n        loc: {\n          start: {\n            line: 55,\n            column: 20\n          },\n          end: {\n            line: 60,\n            column: 21\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 55,\n            column: 20\n          },\n          end: {\n            line: 60,\n            column: 21\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 55\n      },\n      \"2\": {\n        loc: {\n          start: {\n            line: 55,\n            column: 24\n          },\n          end: {\n            line: 55,\n            column: 106\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 55,\n            column: 24\n          },\n          end: {\n            line: 55,\n            column: 62\n          }\n        }, {\n          start: {\n            line: 55,\n            column: 66\n          },\n          end: {\n            line: 55,\n            column: 106\n          }\n        }],\n        line: 55\n      },\n      \"3\": {\n        loc: {\n          start: {\n            line: 61,\n            column: 20\n          },\n          end: {\n            line: 66,\n            column: 21\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 61,\n            column: 20\n          },\n          end: {\n            line: 66,\n            column: 21\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 61\n      },\n      \"4\": {\n        loc: {\n          start: {\n            line: 61,\n            column: 24\n          },\n          end: {\n            line: 61,\n            column: 102\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 61,\n            column: 24\n          },\n          end: {\n            line: 61,\n            column: 60\n          }\n        }, {\n          start: {\n            line: 61,\n            column: 64\n          },\n          end: {\n            line: 61,\n            column: 102\n          }\n        }],\n        line: 61\n      },\n      \"5\": {\n        loc: {\n          start: {\n            line: 67,\n            column: 20\n          },\n          end: {\n            line: 72,\n            column: 21\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 67,\n            column: 20\n          },\n          end: {\n            line: 72,\n            column: 21\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 67\n      },\n      \"6\": {\n        loc: {\n          start: {\n            line: 67,\n            column: 24\n          },\n          end: {\n            line: 67,\n            column: 110\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 67,\n            column: 24\n          },\n          end: {\n            line: 67,\n            column: 64\n          }\n        }, {\n          start: {\n            line: 67,\n            column: 68\n          },\n          end: {\n            line: 67,\n            column: 110\n          }\n        }],\n        line: 67\n      },\n      \"7\": {\n        loc: {\n          start: {\n            line: 73,\n            column: 20\n          },\n          end: {\n            line: 97,\n            column: 21\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 73,\n            column: 20\n          },\n          end: {\n            line: 97,\n            column: 21\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 73\n      },\n      \"8\": {\n        loc: {\n          start: {\n            line: 73,\n            column: 24\n          },\n          end: {\n            line: 73,\n            column: 106\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 73,\n            column: 24\n          },\n          end: {\n            line: 73,\n            column: 63\n          }\n        }, {\n          start: {\n            line: 73,\n            column: 67\n          },\n          end: {\n            line: 73,\n            column: 106\n          }\n        }],\n        line: 73\n      },\n      \"9\": {\n        loc: {\n          start: {\n            line: 74,\n            column: 24\n          },\n          end: {\n            line: 77,\n            column: 25\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 74,\n            column: 24\n          },\n          end: {\n            line: 77,\n            column: 25\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 74\n      },\n      \"10\": {\n        loc: {\n          start: {\n            line: 79,\n            column: 32\n          },\n          end: {\n            line: 79,\n            column: 58\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 79,\n            column: 32\n          },\n          end: {\n            line: 79,\n            column: 53\n          }\n        }, {\n          start: {\n            line: 79,\n            column: 57\n          },\n          end: {\n            line: 79,\n            column: 58\n          }\n        }],\n        line: 79\n      },\n      \"11\": {\n        loc: {\n          start: {\n            line: 81,\n            column: 38\n          },\n          end: {\n            line: 81,\n            column: 96\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 81,\n            column: 38\n          },\n          end: {\n            line: 81,\n            column: 91\n          }\n        }, {\n          start: {\n            line: 81,\n            column: 95\n          },\n          end: {\n            line: 81,\n            column: 96\n          }\n        }],\n        line: 81\n      },\n      \"12\": {\n        loc: {\n          start: {\n            line: 82,\n            column: 38\n          },\n          end: {\n            line: 82,\n            column: 83\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 82,\n            column: 38\n          },\n          end: {\n            line: 82,\n            column: 77\n          }\n        }, {\n          start: {\n            line: 82,\n            column: 81\n          },\n          end: {\n            line: 82,\n            column: 83\n          }\n        }],\n        line: 82\n      },\n      \"13\": {\n        loc: {\n          start: {\n            line: 86,\n            column: 24\n          },\n          end: {\n            line: 96,\n            column: 25\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 86,\n            column: 24\n          },\n          end: {\n            line: 96,\n            column: 25\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 86\n      },\n      \"14\": {\n        loc: {\n          start: {\n            line: 88,\n            column: 32\n          },\n          end: {\n            line: 90,\n            column: 33\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 88,\n            column: 32\n          },\n          end: {\n            line: 90,\n            column: 33\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 88\n      },\n      \"15\": {\n        loc: {\n          start: {\n            line: 103,\n            column: 33\n          },\n          end: {\n            line: 103,\n            column: 85\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 103,\n            column: 33\n          },\n          end: {\n            line: 103,\n            column: 52\n          }\n        }, {\n          start: {\n            line: 103,\n            column: 56\n          },\n          end: {\n            line: 103,\n            column: 85\n          }\n        }],\n        line: 103\n      },\n      \"16\": {\n        loc: {\n          start: {\n            line: 111,\n            column: 8\n          },\n          end: {\n            line: 112,\n            column: 19\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 111,\n            column: 8\n          },\n          end: {\n            line: 112,\n            column: 19\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 111\n      },\n      \"17\": {\n        loc: {\n          start: {\n            line: 111,\n            column: 12\n          },\n          end: {\n            line: 111,\n            column: 70\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 111,\n            column: 12\n          },\n          end: {\n            line: 111,\n            column: 26\n          }\n        }, {\n          start: {\n            line: 111,\n            column: 30\n          },\n          end: {\n            line: 111,\n            column: 70\n          }\n        }],\n        line: 111\n      },\n      \"18\": {\n        loc: {\n          start: {\n            line: 114,\n            column: 8\n          },\n          end: {\n            line: 150,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 114,\n            column: 8\n          },\n          end: {\n            line: 150,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 148,\n            column: 13\n          },\n          end: {\n            line: 150,\n            column: 9\n          }\n        }],\n        line: 114\n      },\n      \"19\": {\n        loc: {\n          start: {\n            line: 127,\n            column: 20\n          },\n          end: {\n            line: 144,\n            column: 21\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 127,\n            column: 20\n          },\n          end: {\n            line: 144,\n            column: 21\n          }\n        }, {\n          start: {\n            line: 138,\n            column: 25\n          },\n          end: {\n            line: 144,\n            column: 21\n          }\n        }],\n        line: 127\n      },\n      \"20\": {\n        loc: {\n          start: {\n            line: 128,\n            column: 24\n          },\n          end: {\n            line: 132,\n            column: 25\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 128,\n            column: 24\n          },\n          end: {\n            line: 132,\n            column: 25\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 128\n      },\n      \"21\": {\n        loc: {\n          start: {\n            line: 133,\n            column: 24\n          },\n          end: {\n            line: 136,\n            column: 25\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 133,\n            column: 24\n          },\n          end: {\n            line: 136,\n            column: 25\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 133\n      },\n      \"22\": {\n        loc: {\n          start: {\n            line: 133,\n            column: 28\n          },\n          end: {\n            line: 134,\n            column: 55\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 133,\n            column: 28\n          },\n          end: {\n            line: 133,\n            column: 67\n          }\n        }, {\n          start: {\n            line: 134,\n            column: 28\n          },\n          end: {\n            line: 134,\n            column: 55\n          }\n        }],\n        line: 133\n      },\n      \"23\": {\n        loc: {\n          start: {\n            line: 139,\n            column: 24\n          },\n          end: {\n            line: 143,\n            column: 25\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 139,\n            column: 24\n          },\n          end: {\n            line: 143,\n            column: 25\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 139\n      },\n      \"24\": {\n        loc: {\n          start: {\n            line: 153,\n            column: 8\n          },\n          end: {\n            line: 155,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 153,\n            column: 8\n          },\n          end: {\n            line: 155,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 153\n      },\n      \"25\": {\n        loc: {\n          start: {\n            line: 156,\n            column: 8\n          },\n          end: {\n            line: 158,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 156,\n            column: 8\n          },\n          end: {\n            line: 158,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 156\n      },\n      \"26\": {\n        loc: {\n          start: {\n            line: 161,\n            column: 8\n          },\n          end: {\n            line: 166,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 161,\n            column: 8\n          },\n          end: {\n            line: 166,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 161\n      },\n      \"27\": {\n        loc: {\n          start: {\n            line: 172,\n            column: 8\n          },\n          end: {\n            line: 174,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 172,\n            column: 8\n          },\n          end: {\n            line: 174,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 172\n      },\n      \"28\": {\n        loc: {\n          start: {\n            line: 177,\n            column: 8\n          },\n          end: {\n            line: 179,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 177,\n            column: 8\n          },\n          end: {\n            line: 179,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 177\n      }\n    },\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0,\n      \"36\": 0,\n      \"37\": 0,\n      \"38\": 0,\n      \"39\": 0,\n      \"40\": 0,\n      \"41\": 0,\n      \"42\": 0,\n      \"43\": 0,\n      \"44\": 0,\n      \"45\": 0,\n      \"46\": 0,\n      \"47\": 0,\n      \"48\": 0,\n      \"49\": 0,\n      \"50\": 0,\n      \"51\": 0,\n      \"52\": 0,\n      \"53\": 0,\n      \"54\": 0,\n      \"55\": 0,\n      \"56\": 0,\n      \"57\": 0,\n      \"58\": 0,\n      \"59\": 0,\n      \"60\": 0,\n      \"61\": 0,\n      \"62\": 0,\n      \"63\": 0,\n      \"64\": 0,\n      \"65\": 0,\n      \"66\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0\n    },\n    b: {\n      \"0\": [0, 0],\n      \"1\": [0, 0],\n      \"2\": [0, 0],\n      \"3\": [0, 0],\n      \"4\": [0, 0],\n      \"5\": [0, 0],\n      \"6\": [0, 0],\n      \"7\": [0, 0],\n      \"8\": [0, 0],\n      \"9\": [0, 0],\n      \"10\": [0, 0],\n      \"11\": [0, 0],\n      \"12\": [0, 0],\n      \"13\": [0, 0],\n      \"14\": [0, 0],\n      \"15\": [0, 0],\n      \"16\": [0, 0],\n      \"17\": [0, 0],\n      \"18\": [0, 0],\n      \"19\": [0, 0],\n      \"20\": [0, 0],\n      \"21\": [0, 0],\n      \"22\": [0, 0],\n      \"23\": [0, 0],\n      \"24\": [0, 0],\n      \"25\": [0, 0],\n      \"26\": [0, 0],\n      \"27\": [0, 0],\n      \"28\": [0, 0]\n    },\n    inputSourceMap: {\n      version: 3,\n      file: \"contract-detail-page.component.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contract-management\\\\pages\\\\contract-detail-page\\\\contract-detail-page.component.ts\"],\n      names: [],\n      mappings: \";;;AAAA,OAAO,EAAE,SAAS,EAAU,SAAS,EAAE,MAAM,eAAe,CAAC;AAC7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,gBAAgB,CAAC;AACrD,OAAO,EAAE,SAAS,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AACtE,OAAO,EAAE,kBAAkB,EAAE,MAAM,8BAA8B,CAAC;AAClE,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACzD,OAAO,EAAE,cAAc,EAAE,MAAM,iBAAiB,CAAC;AACjD,OAAO,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AAChD,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAErD,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,kCAAkC,EAAE,MAAM,mGAAmG,CAAC;AACvJ,OAAO,EAAE,iBAAiB,EAAE,MAAM,+DAA+D,CAAC;AAClG,OAAO,EAAE,6BAA6B,EAAE,MAAM,yFAAyF,CAAC;AACxI,OAAO,EAAE,6BAA6B,EAAE,MAAM,yFAAyF,CAAC;AACxI,OAAO,EAAE,+BAA+B,EAAE,MAAM,6FAA6F,CAAC;AAC9I,OAAO,EAAE,wBAAwB,EAAE,MAAM,6EAA6E,CAAC;AACvH,OAAO,EAAE,uBAAuB,EAAE,MAAM,2EAA2E,CAAC;AACpH,OAAO,EAAE,wBAAwB,EAAE,MAAM,6EAA6E,CAAC;AAGvH,OAAO,EAAE,eAAe,EAAE,MAAM,gDAAgD,CAAC;AACjF,OAAO,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAC9D,OAAO,EAAE,2BAA2B,EAAE,MAAM,sEAAsE,CAAC;AACnH,OAAO,EAAE,sBAAsB,EAAE,MAAM,yEAAyE,CAAC;AACjH,OAAO,EAAE,6BAA6B,EAAE,MAAM,0EAA0E,CAAC;AA4BlH,IAAM,2BAA2B,GAAjC,MAAM,2BAA2B;IAkBtC,YACmB,KAAqB,EACrB,eAAgC,EAChC,MAAiB,EACjB,KAAmB,EACnB,OAA0B;QAJ1B,UAAK,GAAL,KAAK,CAAgB;QACrB,oBAAe,GAAf,eAAe,CAAiB;QAChC,WAAM,GAAN,MAAM,CAAW;QACjB,UAAK,GAAL,KAAK,CAAc;QACnB,YAAO,GAAP,OAAO,CAAmB;QAtB7C,aAAQ,GAAoB,IAAI,CAAC;QACjC,oBAAe,GAA2B,IAAI,CAAC;QAC/C,uBAAkB,GAAG,KAAK,CAAC;IAqBxB,CAAC;IAEJ,QAAQ;QACN,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB,CAAC,UAAkB;QACzC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QAEpB,IAAI,CAAC,eAAe;aACjB,cAAc,CAAC,UAAU,CAAC;aAC1B,IAAI,CACH,SAAS,CAAC,CAAC,OAAO,EAAE,EAAE;YACpB,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC;YAC/B,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;YAEzC,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAClD,CAAC,CAAC,EACF,QAAQ,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CACpC;aACA,SAAS,CAAC;YACT,IAAI,EAAE,CAAC,QAAQ,EAAE,EAAE;gBACjB,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;oBACzB,IAAI,IAAI,CAAC,eAAe,CAAC,iBAAiB,IAAI,IAAI,CAAC,eAAe,CAAC,mBAAmB,EAAE,CAAC;wBACvF,QAAQ,CAAC,eAAe,GAAG;4BACzB,EAAE,EAAE,IAAI,CAAC,eAAe,CAAC,iBAAiB;4BAC1C,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,mBAAmB;yBAC/C,CAAC;oBACJ,CAAC;oBAED,IAAI,IAAI,CAAC,eAAe,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,iBAAiB,EAAE,CAAC;wBACnF,QAAQ,CAAC,aAAa,GAAG;4BACvB,EAAE,EAAE,IAAI,CAAC,eAAe,CAAC,eAAe;4BACxC,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,iBAAiB;yBAC7C,CAAC;oBACJ,CAAC;oBAED,IAAI,IAAI,CAAC,eAAe,CAAC,mBAAmB,IAAI,IAAI,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC;wBAC3F,QAAQ,CAAC,iBAAiB,GAAG;4BAC3B,EAAE,EAAE,IAAI,CAAC,eAAe,CAAC,mBAAmB;4BAC5C,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,qBAAqB;yBACjD,CAAC;oBACJ,CAAC;oBAED,IAAI,IAAI,CAAC,eAAe,CAAC,kBAAkB,IAAI,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE,CAAC;wBACvF,IAAI,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE,CAAC;4BAC5C,MAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;4BACvE,QAAQ,CAAC,YAAY,GAAG,QAAQ,CAAC;wBACnC,CAAC;wBAED,QAAQ,CAAC,UAAU,GAAG;4BACpB,EAAE,EAAE,QAAQ,CAAC,YAAY,IAAI,CAAC;4BAC9B,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,kBAAkB;4BACjD,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE,EAAE,CAAC,IAAI,CAAC;4BACpE,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,kBAAkB,IAAI,EAAE;4BACvD,KAAK,EAAE,EAAE;4BACT,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;yBAC5B,CAAC;wBAEF,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;4BAC5B,UAAU,CAAC,GAAG,EAAE;gCACd,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;oCACxB,IAAI,CAAC,kBAAkB,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;gCAC3D,CAAC;gCACD,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,UAAU,CAAC;oCAC9C,YAAY,EAAE,QAAQ,CAAC,YAAY;oCACnC,kBAAkB,EAAE,IAAI,CAAC,eAAe,EAAE,kBAAkB;iCAC7D,CAAC,CAAC;4BACL,CAAC,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;gBACzB,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAC,MAAM,EAAE,IAAI,KAAK,YAAY,CAAC;YACnE,CAAC;YACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,6BAA6B,CAAC,CAAC;YACzE,CAAC;SACF,CAAC,CAAC;IACP,CAAC;IAED,eAAe;QACb,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED,0BAA0B;QACxB,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,kCAAkC;YAAE,OAAO;QAEvE,MAAM,wBAAwB,GAC5B,IAAI,CAAC,kCAAkC,CAAC,2BAA2B,EAAE,CAAC;QACxE,IAAI,wBAAwB,EAAE,CAAC;YAC7B,IAAI,CAAC,MAAM;iBACR,IAAI,CAAC,+BAA+B,EAAE;gBACrC,KAAK,EAAE,OAAO;gBACd,IAAI,EAAE;oBACJ,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE;oBAC5B,YAAY,EAAE,wBAAwB,CAAC,UAAU,EAAE,EAAE;oBACrD,uBAAuB,EAAE,wBAAwB,CAAC,iBAAiB;iBACpE;aACF,CAAC;iBACD,WAAW,EAAE;iBACb,SAAS,CAAC;gBACT,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE;oBACf,IAAI,MAAM,EAAE,CAAC;wBACX,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;4BAC5B,IAAI,CAAC,kBAAkB,CAAC,YAAY;iCACjC,GAAG,CAAC,kBAAkB,CAAC;gCACxB,EAAE,OAAO,EAAE,CAAC;wBAChB,CAAC;wBACD,IACE,IAAI,CAAC,kCAAkC;4BACvC,wBAAwB,CAAC,EAAE,EAC3B,CAAC;4BACD,IAAI,CAAC,kCAAkC,CAAC,yBAAyB,CAC/D,MAAM,CAAC,OAAO,EACd,wBAAwB,CAAC,EAAE,CAC5B,CAAC;wBACJ,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;4BAC5B,IAAI,CAAC,kBAAkB,CAAC,YAAY;iCACjC,GAAG,CAAC,kBAAkB,CAAC;gCACxB,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;wBACtB,CAAC;oBACH,CAAC;gBACH,CAAC;aACF,CAAC,CAAC;QACP,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,+CAA+C,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAED,4BAA4B;QAC1B,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;QAC/B,CAAC;QACD,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAED,uBAAuB;QACrB,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,IAAI,CAAC,kBAAkB,CAAC,YAAY;iBACjC,GAAG,CAAC,kBAAkB,CAAC;gBACxB,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;YACpB,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,GAAG,CAAC,kBAAkB,CAAC,EAAE,MAAM,EAAE,CAAC;QACzE,CAAC;IACH,CAAC;IAED,oBAAoB,CAAC,WAAoB;QACvC,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC;IACxC,CAAC;IAED,uBAAuB,CAAC,cAAgC;QACtD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG,cAAc,CAAC;QAChD,CAAC;IACH,CAAC;IAED,mBAAmB,CAAC,UAAuB;QACzC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,UAAU,CAAC;QACvC,CAAC;IACH,CAAC;;;;;;;;;qDAzLA,SAAS,SAAC,kCAAkC;qCAG5C,SAAS,SAAC,2BAA2B;uCAGrC,SAAS,SAAC,sBAAsB;uCAGhC,SAAS,SAAC,6BAA6B;;;AAf7B,2BAA2B;IAxBvC,SAAS,CAAC;QACT,QAAQ,EAAE,0BAA0B;QACpC,8BAAoD;QAEpD,UAAU,EAAE,IAAI;QAChB,OAAO,EAAE;YACP,mBAAmB;YACnB,kBAAkB;YAClB,cAAc;YACd,aAAa;YACb,eAAe;YACf,aAAa;YACb,kCAAkC;YAClC,sBAAsB;YACtB,wBAAwB;YACxB,wBAAwB;YACxB,2BAA2B;YAC3B,6BAA6B;YAC7B,iBAAiB;YACjB,6BAA6B;YAC7B,uBAAuB;YACvB,6BAA6B;SAC9B;;KACF,CAAC;GACW,2BAA2B,CAgMvC\",\n      sourcesContent: [\"import { Component, OnInit, ViewChild } from '@angular/core';\\nimport { ReactiveFormsModule } from '@angular/forms';\\nimport { MatDialog, MatDialogModule } from '@angular/material/dialog';\\nimport { MatFormFieldModule } from '@angular/material/form-field';\\nimport { MatIconModule } from '@angular/material/icon';\\nimport { MatInputModule } from '@angular/material/input';\\nimport { ActivatedRoute } from '@angular/router';\\nimport { NgxSpinnerService } from 'ngx-spinner';\\nimport { finalize, switchMap } from 'rxjs/operators';\\n\\nimport { MatCardModule } from '@angular/material/card';\\nimport { AssociatedContractorsListComponent } from '@contract-management/components/associated-contractors-list/associated-contractors-list.component';\\nimport { CcpsListComponent } from '@contract-management/components/ccps-list/ccps-list.component';\\nimport { ContractAuditHistoryComponent } from '@contract-management/components/contract-audit-history/contract-audit-history.component';\\nimport { ContractValueSummaryComponent } from '@contract-management/components/contract-value-summary/contract-value-summary.component';\\nimport { EarlyTerminationDialogComponent } from '@contract-management/components/early-termination-dialog/early-termination-dialog.component';\\nimport { ObligationsListComponent } from '@contract-management/components/obligations-list/obligations-list.component';\\nimport { ReductionsListComponent } from '@contract-management/components/reductions-list/reductions-list.component';\\nimport { SuspensionsListComponent } from '@contract-management/components/suspensions-list/suspensions-list.component';\\nimport { ContractDetails } from '@contract-management/models/contract-details.model';\\nimport { Contract } from '@contract-management/models/contract.model';\\nimport { ContractService } from '@contract-management/services/contract.service';\\nimport { AlertService } from '@shared/services/alert.service';\\nimport { ContractDetailFormComponent } from '../../components/contract-detail-form/contract-detail-form.component';\\nimport { AdditionsListComponent } from '@contract-management/components/additions-list/additions-list.component';\\nimport { ContractorDetailFormComponent } from '../../components/contractor-detail-form/contractor-detail-form.component';\\nimport { ContractValues } from '@contract-management/models/contract-values.model';\\nimport { Reduction } from '@contract-management/models/reduction.model';\\n\\n@Component({\\n  selector: 'app-contract-detail-page',\\n  templateUrl: './contract-detail-page.component.html',\\n  styleUrl: './contract-detail-page.component.scss',\\n  standalone: true,\\n  imports: [\\n    ReactiveFormsModule,\\n    MatFormFieldModule,\\n    MatInputModule,\\n    MatIconModule,\\n    MatDialogModule,\\n    MatCardModule,\\n    AssociatedContractorsListComponent,\\n    AdditionsListComponent,\\n    ObligationsListComponent,\\n    SuspensionsListComponent,\\n    ContractDetailFormComponent,\\n    ContractorDetailFormComponent,\\n    CcpsListComponent,\\n    ContractAuditHistoryComponent,\\n    ReductionsListComponent,\\n    ContractValueSummaryComponent,\\n  ],\\n})\\nexport class ContractDetailPageComponent implements OnInit {\\n  contract: Contract | null = null;\\n  contractDetails: ContractDetails | null = null;\\n  isContractFinished = false;\\n  contractorId?: number;\\n\\n  @ViewChild(AssociatedContractorsListComponent)\\n  AssociatedContractorsListComponent!: AssociatedContractorsListComponent;\\n\\n  @ViewChild(ContractDetailFormComponent)\\n  contractDetailForm!: ContractDetailFormComponent;\\n\\n  @ViewChild('contractorDetailForm')\\n  contractorDetailForm!: ContractorDetailFormComponent;\\n\\n  @ViewChild(ContractValueSummaryComponent)\\n  contractValueSummary!: ContractValueSummaryComponent;\\n\\n  constructor(\\n    private readonly route: ActivatedRoute,\\n    private readonly contractService: ContractService,\\n    private readonly dialog: MatDialog,\\n    private readonly alert: AlertService,\\n    private readonly spinner: NgxSpinnerService,\\n  ) {}\\n\\n  ngOnInit(): void {\\n    this.route.params.subscribe((params) => {\\n      this.loadContractData(+params['id']);\\n    });\\n  }\\n\\n  private loadContractData(contractId: number): void {\\n    this.spinner.show();\\n\\n    this.contractService\\n      .getDetailsById(contractId)\\n      .pipe(\\n        switchMap((details) => {\\n          this.contractDetails = details;\\n          this.contractorId = details.contractorId;\\n\\n          return this.contractService.getById(contractId);\\n        }),\\n        finalize(() => this.spinner.hide()),\\n      )\\n      .subscribe({\\n        next: (contract) => {\\n          if (this.contractDetails) {\\n            if (this.contractDetails.causesSelectionId && this.contractDetails.causesSelectionName) {\\n              contract.causesSelection = {\\n                id: this.contractDetails.causesSelectionId,\\n                name: this.contractDetails.causesSelectionName\\n              };\\n            }\\n\\n            if (this.contractDetails.contractClassId && this.contractDetails.contractClassName) {\\n              contract.contractClass = {\\n                id: this.contractDetails.contractClassId,\\n                name: this.contractDetails.contractClassName\\n              };\\n            }\\n\\n            if (this.contractDetails.managementSupportId && this.contractDetails.managementSupportName) {\\n              contract.managementSupport = {\\n                id: this.contractDetails.managementSupportId,\\n                name: this.contractDetails.managementSupportName\\n              };\\n            }\\n\\n            if (this.contractDetails.supervisorFullName && this.contractDetails.supervisorIdNumber) {\\n              if (this.contractDetails.supervisorIdNumber) {\\n                const idNumber = parseInt(this.contractDetails.supervisorIdNumber, 10);\\n                contract.supervisorId = idNumber;\\n              }\\n\\n              contract.supervisor = {\\n                id: contract.supervisorId || 0,\\n                fullName: this.contractDetails.supervisorFullName,\\n                idNumber: parseInt(this.contractDetails.supervisorIdNumber, 10) || 0,\\n                position: this.contractDetails.supervisorPosition || '',\\n                email: '',\\n                idType: { id: 0, name: '' }\\n              };\\n\\n              if (this.contractDetailForm) {\\n                setTimeout(() => {\\n                  if (contract.supervisor) {\\n                    this.contractDetailForm.supervisor = contract.supervisor;\\n                  }\\n                  this.contractDetailForm.contractForm.patchValue({\\n                    supervisorId: contract.supervisorId,\\n                    supervisorFullName: this.contractDetails?.supervisorFullName\\n                  });\\n                });\\n              }\\n            }\\n          }\\n\\n          this.contract = contract;\\n          this.isContractFinished = contract.status?.name === 'FINALIZADO';\\n        },\\n        error: (error) => {\\n          this.alert.error(error.error?.detail ?? 'Error al cargar el contrato');\\n        },\\n      });\\n  }\\n\\n  getContractorId(): number | undefined {\\n    return this.contractorId;\\n  }\\n\\n  openEarlyTerminationDialog(): void {\\n    if (!this.contract || !this.AssociatedContractorsListComponent) return;\\n\\n    const latestContractorContract =\\n      this.AssociatedContractorsListComponent.getLatestContractorContract();\\n    if (latestContractorContract) {\\n      this.dialog\\n        .open(EarlyTerminationDialogComponent, {\\n          width: '500px',\\n          data: {\\n            contractId: this.contract.id,\\n            contractorId: latestContractorContract.contractor?.id,\\n            lastContractorStartDate: latestContractorContract.contractStartDate,\\n          },\\n        })\\n        .afterClosed()\\n        .subscribe({\\n          next: (result) => {\\n            if (result) {\\n              if (this.contractDetailForm) {\\n                this.contractDetailForm.contractForm\\n                  .get('earlyTermination')\\n                  ?.disable();\\n              }\\n              if (\\n                this.AssociatedContractorsListComponent &&\\n                latestContractorContract.id\\n              ) {\\n                this.AssociatedContractorsListComponent.updateContractorContracts(\\n                  result.endDate,\\n                  latestContractorContract.id,\\n                );\\n              }\\n            } else {\\n              if (this.contractDetailForm) {\\n                this.contractDetailForm.contractForm\\n                  .get('earlyTermination')\\n                  ?.setValue(false);\\n              }\\n            }\\n          },\\n        });\\n    } else {\\n      this.alert.warning('No hay contratistas asociados a este contrato');\\n    }\\n  }\\n\\n  onContractorContractsChanged(): void {\\n    if (this.contract) {\\n      this.contract.cession = true;\\n    }\\n    if (this.contractDetailForm) {\\n      this.contractDetailForm.contractForm.patchValue({ cession: true });\\n    }\\n  }\\n\\n  uncheckEarlyTermination(): void {\\n    if (this.contractDetailForm) {\\n      this.contractDetailForm.contractForm\\n        .get('earlyTermination')\\n        ?.setValue(false);\\n      this.contractDetailForm.contractForm.get('earlyTermination')?.enable();\\n    }\\n  }\\n\\n  updateContractStatus(isCompleted: boolean): void {\\n    this.isContractFinished = isCompleted;\\n  }\\n\\n  onContractValuesChanged(contractValues: ContractValues[]): void {\\n    if (this.contract) {\\n      this.contract.contractValues = contractValues;\\n    }\\n  }\\n\\n  onReductionsChanged(reductions: Reduction[]): void {\\n    if (this.contract) {\\n      this.contract.reduction = reductions;\\n    }\\n  }\\n}\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"5409bf1c56a788a1063b5b0e7d715657b0deeae8\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_1gavaw23lm = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_1gavaw23lm();\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./contract-detail-page.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./contract-detail-page.component.scss?ngResource\";\nimport { Component, ViewChild } from '@angular/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatDialog, MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { ActivatedRoute } from '@angular/router';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { finalize, switchMap } from 'rxjs/operators';\nimport { MatCardModule } from '@angular/material/card';\nimport { AssociatedContractorsListComponent } from '@contract-management/components/associated-contractors-list/associated-contractors-list.component';\nimport { CcpsListComponent } from '@contract-management/components/ccps-list/ccps-list.component';\nimport { ContractAuditHistoryComponent } from '@contract-management/components/contract-audit-history/contract-audit-history.component';\nimport { ContractValueSummaryComponent } from '@contract-management/components/contract-value-summary/contract-value-summary.component';\nimport { EarlyTerminationDialogComponent } from '@contract-management/components/early-termination-dialog/early-termination-dialog.component';\nimport { ObligationsListComponent } from '@contract-management/components/obligations-list/obligations-list.component';\nimport { ReductionsListComponent } from '@contract-management/components/reductions-list/reductions-list.component';\nimport { SuspensionsListComponent } from '@contract-management/components/suspensions-list/suspensions-list.component';\nimport { ContractService } from '@contract-management/services/contract.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { ContractDetailFormComponent } from '../../components/contract-detail-form/contract-detail-form.component';\nimport { AdditionsListComponent } from '@contract-management/components/additions-list/additions-list.component';\nimport { ContractorDetailFormComponent } from '../../components/contractor-detail-form/contractor-detail-form.component';\ncov_1gavaw23lm().s[0]++;\nlet ContractDetailPageComponent = class ContractDetailPageComponent {\n  constructor(route, contractService, dialog, alert, spinner) {\n    cov_1gavaw23lm().f[0]++;\n    cov_1gavaw23lm().s[1]++;\n    this.route = route;\n    cov_1gavaw23lm().s[2]++;\n    this.contractService = contractService;\n    cov_1gavaw23lm().s[3]++;\n    this.dialog = dialog;\n    cov_1gavaw23lm().s[4]++;\n    this.alert = alert;\n    cov_1gavaw23lm().s[5]++;\n    this.spinner = spinner;\n    cov_1gavaw23lm().s[6]++;\n    this.contract = null;\n    cov_1gavaw23lm().s[7]++;\n    this.contractDetails = null;\n    cov_1gavaw23lm().s[8]++;\n    this.isContractFinished = false;\n  }\n  ngOnInit() {\n    cov_1gavaw23lm().f[1]++;\n    cov_1gavaw23lm().s[9]++;\n    this.route.params.subscribe(params => {\n      cov_1gavaw23lm().f[2]++;\n      cov_1gavaw23lm().s[10]++;\n      this.loadContractData(+params['id']);\n    });\n  }\n  loadContractData(contractId) {\n    cov_1gavaw23lm().f[3]++;\n    cov_1gavaw23lm().s[11]++;\n    this.spinner.show();\n    cov_1gavaw23lm().s[12]++;\n    this.contractService.getDetailsById(contractId).pipe(switchMap(details => {\n      cov_1gavaw23lm().f[4]++;\n      cov_1gavaw23lm().s[13]++;\n      this.contractDetails = details;\n      cov_1gavaw23lm().s[14]++;\n      this.contractorId = details.contractorId;\n      cov_1gavaw23lm().s[15]++;\n      return this.contractService.getById(contractId);\n    }), finalize(() => {\n      cov_1gavaw23lm().f[5]++;\n      cov_1gavaw23lm().s[16]++;\n      return this.spinner.hide();\n    })).subscribe({\n      next: contract => {\n        cov_1gavaw23lm().f[6]++;\n        cov_1gavaw23lm().s[17]++;\n        if (this.contractDetails) {\n          cov_1gavaw23lm().b[0][0]++;\n          cov_1gavaw23lm().s[18]++;\n          if ((cov_1gavaw23lm().b[2][0]++, this.contractDetails.causesSelectionId) && (cov_1gavaw23lm().b[2][1]++, this.contractDetails.causesSelectionName)) {\n            cov_1gavaw23lm().b[1][0]++;\n            cov_1gavaw23lm().s[19]++;\n            contract.causesSelection = {\n              id: this.contractDetails.causesSelectionId,\n              name: this.contractDetails.causesSelectionName\n            };\n          } else {\n            cov_1gavaw23lm().b[1][1]++;\n          }\n          cov_1gavaw23lm().s[20]++;\n          if ((cov_1gavaw23lm().b[4][0]++, this.contractDetails.contractClassId) && (cov_1gavaw23lm().b[4][1]++, this.contractDetails.contractClassName)) {\n            cov_1gavaw23lm().b[3][0]++;\n            cov_1gavaw23lm().s[21]++;\n            contract.contractClass = {\n              id: this.contractDetails.contractClassId,\n              name: this.contractDetails.contractClassName\n            };\n          } else {\n            cov_1gavaw23lm().b[3][1]++;\n          }\n          cov_1gavaw23lm().s[22]++;\n          if ((cov_1gavaw23lm().b[6][0]++, this.contractDetails.managementSupportId) && (cov_1gavaw23lm().b[6][1]++, this.contractDetails.managementSupportName)) {\n            cov_1gavaw23lm().b[5][0]++;\n            cov_1gavaw23lm().s[23]++;\n            contract.managementSupport = {\n              id: this.contractDetails.managementSupportId,\n              name: this.contractDetails.managementSupportName\n            };\n          } else {\n            cov_1gavaw23lm().b[5][1]++;\n          }\n          cov_1gavaw23lm().s[24]++;\n          if ((cov_1gavaw23lm().b[8][0]++, this.contractDetails.supervisorFullName) && (cov_1gavaw23lm().b[8][1]++, this.contractDetails.supervisorIdNumber)) {\n            cov_1gavaw23lm().b[7][0]++;\n            cov_1gavaw23lm().s[25]++;\n            if (this.contractDetails.supervisorIdNumber) {\n              cov_1gavaw23lm().b[9][0]++;\n              const idNumber = (cov_1gavaw23lm().s[26]++, parseInt(this.contractDetails.supervisorIdNumber, 10));\n              cov_1gavaw23lm().s[27]++;\n              contract.supervisorId = idNumber;\n            } else {\n              cov_1gavaw23lm().b[9][1]++;\n            }\n            cov_1gavaw23lm().s[28]++;\n            contract.supervisor = {\n              id: (cov_1gavaw23lm().b[10][0]++, contract.supervisorId) || (cov_1gavaw23lm().b[10][1]++, 0),\n              fullName: this.contractDetails.supervisorFullName,\n              idNumber: (cov_1gavaw23lm().b[11][0]++, parseInt(this.contractDetails.supervisorIdNumber, 10)) || (cov_1gavaw23lm().b[11][1]++, 0),\n              position: (cov_1gavaw23lm().b[12][0]++, this.contractDetails.supervisorPosition) || (cov_1gavaw23lm().b[12][1]++, ''),\n              email: '',\n              idType: {\n                id: 0,\n                name: ''\n              }\n            };\n            cov_1gavaw23lm().s[29]++;\n            if (this.contractDetailForm) {\n              cov_1gavaw23lm().b[13][0]++;\n              cov_1gavaw23lm().s[30]++;\n              setTimeout(() => {\n                cov_1gavaw23lm().f[7]++;\n                cov_1gavaw23lm().s[31]++;\n                if (contract.supervisor) {\n                  cov_1gavaw23lm().b[14][0]++;\n                  cov_1gavaw23lm().s[32]++;\n                  this.contractDetailForm.supervisor = contract.supervisor;\n                } else {\n                  cov_1gavaw23lm().b[14][1]++;\n                }\n                cov_1gavaw23lm().s[33]++;\n                this.contractDetailForm.contractForm.patchValue({\n                  supervisorId: contract.supervisorId,\n                  supervisorFullName: this.contractDetails?.supervisorFullName\n                });\n              });\n            } else {\n              cov_1gavaw23lm().b[13][1]++;\n            }\n          } else {\n            cov_1gavaw23lm().b[7][1]++;\n          }\n        } else {\n          cov_1gavaw23lm().b[0][1]++;\n        }\n        cov_1gavaw23lm().s[34]++;\n        this.contract = contract;\n        cov_1gavaw23lm().s[35]++;\n        this.isContractFinished = contract.status?.name === 'FINALIZADO';\n      },\n      error: error => {\n        cov_1gavaw23lm().f[8]++;\n        cov_1gavaw23lm().s[36]++;\n        this.alert.error((cov_1gavaw23lm().b[15][0]++, error.error?.detail) ?? (cov_1gavaw23lm().b[15][1]++, 'Error al cargar el contrato'));\n      }\n    });\n  }\n  getContractorId() {\n    cov_1gavaw23lm().f[9]++;\n    cov_1gavaw23lm().s[37]++;\n    return this.contractorId;\n  }\n  openEarlyTerminationDialog() {\n    cov_1gavaw23lm().f[10]++;\n    cov_1gavaw23lm().s[38]++;\n    if ((cov_1gavaw23lm().b[17][0]++, !this.contract) || (cov_1gavaw23lm().b[17][1]++, !this.AssociatedContractorsListComponent)) {\n      cov_1gavaw23lm().b[16][0]++;\n      cov_1gavaw23lm().s[39]++;\n      return;\n    } else {\n      cov_1gavaw23lm().b[16][1]++;\n    }\n    const latestContractorContract = (cov_1gavaw23lm().s[40]++, this.AssociatedContractorsListComponent.getLatestContractorContract());\n    cov_1gavaw23lm().s[41]++;\n    if (latestContractorContract) {\n      cov_1gavaw23lm().b[18][0]++;\n      cov_1gavaw23lm().s[42]++;\n      this.dialog.open(EarlyTerminationDialogComponent, {\n        width: '500px',\n        data: {\n          contractId: this.contract.id,\n          contractorId: latestContractorContract.contractor?.id,\n          lastContractorStartDate: latestContractorContract.contractStartDate\n        }\n      }).afterClosed().subscribe({\n        next: result => {\n          cov_1gavaw23lm().f[11]++;\n          cov_1gavaw23lm().s[43]++;\n          if (result) {\n            cov_1gavaw23lm().b[19][0]++;\n            cov_1gavaw23lm().s[44]++;\n            if (this.contractDetailForm) {\n              cov_1gavaw23lm().b[20][0]++;\n              cov_1gavaw23lm().s[45]++;\n              this.contractDetailForm.contractForm.get('earlyTermination')?.disable();\n            } else {\n              cov_1gavaw23lm().b[20][1]++;\n            }\n            cov_1gavaw23lm().s[46]++;\n            if ((cov_1gavaw23lm().b[22][0]++, this.AssociatedContractorsListComponent) && (cov_1gavaw23lm().b[22][1]++, latestContractorContract.id)) {\n              cov_1gavaw23lm().b[21][0]++;\n              cov_1gavaw23lm().s[47]++;\n              this.AssociatedContractorsListComponent.updateContractorContracts(result.endDate, latestContractorContract.id);\n            } else {\n              cov_1gavaw23lm().b[21][1]++;\n            }\n          } else {\n            cov_1gavaw23lm().b[19][1]++;\n            cov_1gavaw23lm().s[48]++;\n            if (this.contractDetailForm) {\n              cov_1gavaw23lm().b[23][0]++;\n              cov_1gavaw23lm().s[49]++;\n              this.contractDetailForm.contractForm.get('earlyTermination')?.setValue(false);\n            } else {\n              cov_1gavaw23lm().b[23][1]++;\n            }\n          }\n        }\n      });\n    } else {\n      cov_1gavaw23lm().b[18][1]++;\n      cov_1gavaw23lm().s[50]++;\n      this.alert.warning('No hay contratistas asociados a este contrato');\n    }\n  }\n  onContractorContractsChanged() {\n    cov_1gavaw23lm().f[12]++;\n    cov_1gavaw23lm().s[51]++;\n    if (this.contract) {\n      cov_1gavaw23lm().b[24][0]++;\n      cov_1gavaw23lm().s[52]++;\n      this.contract.cession = true;\n    } else {\n      cov_1gavaw23lm().b[24][1]++;\n    }\n    cov_1gavaw23lm().s[53]++;\n    if (this.contractDetailForm) {\n      cov_1gavaw23lm().b[25][0]++;\n      cov_1gavaw23lm().s[54]++;\n      this.contractDetailForm.contractForm.patchValue({\n        cession: true\n      });\n    } else {\n      cov_1gavaw23lm().b[25][1]++;\n    }\n  }\n  uncheckEarlyTermination() {\n    cov_1gavaw23lm().f[13]++;\n    cov_1gavaw23lm().s[55]++;\n    if (this.contractDetailForm) {\n      cov_1gavaw23lm().b[26][0]++;\n      cov_1gavaw23lm().s[56]++;\n      this.contractDetailForm.contractForm.get('earlyTermination')?.setValue(false);\n      cov_1gavaw23lm().s[57]++;\n      this.contractDetailForm.contractForm.get('earlyTermination')?.enable();\n    } else {\n      cov_1gavaw23lm().b[26][1]++;\n    }\n  }\n  updateContractStatus(isCompleted) {\n    cov_1gavaw23lm().f[14]++;\n    cov_1gavaw23lm().s[58]++;\n    this.isContractFinished = isCompleted;\n  }\n  onContractValuesChanged(contractValues) {\n    cov_1gavaw23lm().f[15]++;\n    cov_1gavaw23lm().s[59]++;\n    if (this.contract) {\n      cov_1gavaw23lm().b[27][0]++;\n      cov_1gavaw23lm().s[60]++;\n      this.contract.contractValues = contractValues;\n    } else {\n      cov_1gavaw23lm().b[27][1]++;\n    }\n  }\n  onReductionsChanged(reductions) {\n    cov_1gavaw23lm().f[16]++;\n    cov_1gavaw23lm().s[61]++;\n    if (this.contract) {\n      cov_1gavaw23lm().b[28][0]++;\n      cov_1gavaw23lm().s[62]++;\n      this.contract.reduction = reductions;\n    } else {\n      cov_1gavaw23lm().b[28][1]++;\n    }\n  }\n  static {\n    cov_1gavaw23lm().s[63]++;\n    this.ctorParameters = () => {\n      cov_1gavaw23lm().f[17]++;\n      cov_1gavaw23lm().s[64]++;\n      return [{\n        type: ActivatedRoute\n      }, {\n        type: ContractService\n      }, {\n        type: MatDialog\n      }, {\n        type: AlertService\n      }, {\n        type: NgxSpinnerService\n      }];\n    };\n  }\n  static {\n    cov_1gavaw23lm().s[65]++;\n    this.propDecorators = {\n      AssociatedContractorsListComponent: [{\n        type: ViewChild,\n        args: [AssociatedContractorsListComponent]\n      }],\n      contractDetailForm: [{\n        type: ViewChild,\n        args: [ContractDetailFormComponent]\n      }],\n      contractorDetailForm: [{\n        type: ViewChild,\n        args: ['contractorDetailForm']\n      }],\n      contractValueSummary: [{\n        type: ViewChild,\n        args: [ContractValueSummaryComponent]\n      }]\n    };\n  }\n};\ncov_1gavaw23lm().s[66]++;\nContractDetailPageComponent = __decorate([Component({\n  selector: 'app-contract-detail-page',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: true,\n  imports: [ReactiveFormsModule, MatFormFieldModule, MatInputModule, MatIconModule, MatDialogModule, MatCardModule, AssociatedContractorsListComponent, AdditionsListComponent, ObligationsListComponent, SuspensionsListComponent, ContractDetailFormComponent, ContractorDetailFormComponent, CcpsListComponent, ContractAuditHistoryComponent, ReductionsListComponent, ContractValueSummaryComponent],\n  styles: [__NG_CLI_RESOURCE__1]\n})], ContractDetailPageComponent);\nexport { ContractDetailPageComponent };", "map": {"version": 3, "names": ["cov_1gavaw23lm", "actualCoverage", "Component", "ViewChild", "ReactiveFormsModule", "MatDialog", "MatDialogModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "ActivatedRoute", "NgxSpinnerService", "finalize", "switchMap", "MatCardModule", "AssociatedContractorsListComponent", "CcpsListComponent", "ContractAuditHistoryComponent", "ContractValueSummaryComponent", "EarlyTerminationDialogComponent", "ObligationsListComponent", "ReductionsListComponent", "SuspensionsListComponent", "ContractService", "AlertService", "ContractDetailFormComponent", "AdditionsListComponent", "ContractorDetailFormComponent", "s", "ContractDetailPageComponent", "constructor", "route", "contractService", "dialog", "alert", "spinner", "f", "contract", "contractDetails", "isContractFinished", "ngOnInit", "params", "subscribe", "loadContractData", "contractId", "show", "getDetailsById", "pipe", "details", "contractorId", "getById", "hide", "next", "b", "causesSelectionId", "causesSelectionName", "causesSelection", "id", "name", "contractClassId", "contractClassName", "contractClass", "managementSupportId", "managementSupportName", "managementSupport", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supervisorIdNumber", "idNumber", "parseInt", "supervisorId", "supervisor", "fullName", "position", "supervisorPosition", "email", "idType", "contractDetailForm", "setTimeout", "contractForm", "patchValue", "status", "error", "detail", "getContractorId", "openEarlyTerminationDialog", "latestContractorContract", "getLatestContractorContract", "open", "width", "data", "contractor", "lastContractorStartDate", "contractStartDate", "afterClosed", "result", "get", "disable", "updateContractorContracts", "endDate", "setValue", "warning", "onContractorContractsChanged", "cession", "uncheckEarlyTermination", "enable", "updateContractStatus", "isCompleted", "onContractValuesChanged", "contractValues", "onReductionsChanged", "reductions", "reduction", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\pages\\contract-detail-page\\contract-detail-page.component.ts"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatDialog, MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { ActivatedRoute } from '@angular/router';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { finalize, switchMap } from 'rxjs/operators';\n\nimport { MatCardModule } from '@angular/material/card';\nimport { AssociatedContractorsListComponent } from '@contract-management/components/associated-contractors-list/associated-contractors-list.component';\nimport { CcpsListComponent } from '@contract-management/components/ccps-list/ccps-list.component';\nimport { ContractAuditHistoryComponent } from '@contract-management/components/contract-audit-history/contract-audit-history.component';\nimport { ContractValueSummaryComponent } from '@contract-management/components/contract-value-summary/contract-value-summary.component';\nimport { EarlyTerminationDialogComponent } from '@contract-management/components/early-termination-dialog/early-termination-dialog.component';\nimport { ObligationsListComponent } from '@contract-management/components/obligations-list/obligations-list.component';\nimport { ReductionsListComponent } from '@contract-management/components/reductions-list/reductions-list.component';\nimport { SuspensionsListComponent } from '@contract-management/components/suspensions-list/suspensions-list.component';\nimport { ContractDetails } from '@contract-management/models/contract-details.model';\nimport { Contract } from '@contract-management/models/contract.model';\nimport { ContractService } from '@contract-management/services/contract.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { ContractDetailFormComponent } from '../../components/contract-detail-form/contract-detail-form.component';\nimport { AdditionsListComponent } from '@contract-management/components/additions-list/additions-list.component';\nimport { ContractorDetailFormComponent } from '../../components/contractor-detail-form/contractor-detail-form.component';\nimport { ContractValues } from '@contract-management/models/contract-values.model';\nimport { Reduction } from '@contract-management/models/reduction.model';\n\n@Component({\n  selector: 'app-contract-detail-page',\n  templateUrl: './contract-detail-page.component.html',\n  styleUrl: './contract-detail-page.component.scss',\n  standalone: true,\n  imports: [\n    ReactiveFormsModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatIconModule,\n    MatDialogModule,\n    MatCardModule,\n    AssociatedContractorsListComponent,\n    AdditionsListComponent,\n    ObligationsListComponent,\n    SuspensionsListComponent,\n    ContractDetailFormComponent,\n    ContractorDetailFormComponent,\n    CcpsListComponent,\n    ContractAuditHistoryComponent,\n    ReductionsListComponent,\n    ContractValueSummaryComponent,\n  ],\n})\nexport class ContractDetailPageComponent implements OnInit {\n  contract: Contract | null = null;\n  contractDetails: ContractDetails | null = null;\n  isContractFinished = false;\n  contractorId?: number;\n\n  @ViewChild(AssociatedContractorsListComponent)\n  AssociatedContractorsListComponent!: AssociatedContractorsListComponent;\n\n  @ViewChild(ContractDetailFormComponent)\n  contractDetailForm!: ContractDetailFormComponent;\n\n  @ViewChild('contractorDetailForm')\n  contractorDetailForm!: ContractorDetailFormComponent;\n\n  @ViewChild(ContractValueSummaryComponent)\n  contractValueSummary!: ContractValueSummaryComponent;\n\n  constructor(\n    private readonly route: ActivatedRoute,\n    private readonly contractService: ContractService,\n    private readonly dialog: MatDialog,\n    private readonly alert: AlertService,\n    private readonly spinner: NgxSpinnerService,\n  ) {}\n\n  ngOnInit(): void {\n    this.route.params.subscribe((params) => {\n      this.loadContractData(+params['id']);\n    });\n  }\n\n  private loadContractData(contractId: number): void {\n    this.spinner.show();\n\n    this.contractService\n      .getDetailsById(contractId)\n      .pipe(\n        switchMap((details) => {\n          this.contractDetails = details;\n          this.contractorId = details.contractorId;\n\n          return this.contractService.getById(contractId);\n        }),\n        finalize(() => this.spinner.hide()),\n      )\n      .subscribe({\n        next: (contract) => {\n          if (this.contractDetails) {\n            if (this.contractDetails.causesSelectionId && this.contractDetails.causesSelectionName) {\n              contract.causesSelection = {\n                id: this.contractDetails.causesSelectionId,\n                name: this.contractDetails.causesSelectionName\n              };\n            }\n\n            if (this.contractDetails.contractClassId && this.contractDetails.contractClassName) {\n              contract.contractClass = {\n                id: this.contractDetails.contractClassId,\n                name: this.contractDetails.contractClassName\n              };\n            }\n\n            if (this.contractDetails.managementSupportId && this.contractDetails.managementSupportName) {\n              contract.managementSupport = {\n                id: this.contractDetails.managementSupportId,\n                name: this.contractDetails.managementSupportName\n              };\n            }\n\n            if (this.contractDetails.supervisorFullName && this.contractDetails.supervisorIdNumber) {\n              if (this.contractDetails.supervisorIdNumber) {\n                const idNumber = parseInt(this.contractDetails.supervisorIdNumber, 10);\n                contract.supervisorId = idNumber;\n              }\n\n              contract.supervisor = {\n                id: contract.supervisorId || 0,\n                fullName: this.contractDetails.supervisorFullName,\n                idNumber: parseInt(this.contractDetails.supervisorIdNumber, 10) || 0,\n                position: this.contractDetails.supervisorPosition || '',\n                email: '',\n                idType: { id: 0, name: '' }\n              };\n\n              if (this.contractDetailForm) {\n                setTimeout(() => {\n                  if (contract.supervisor) {\n                    this.contractDetailForm.supervisor = contract.supervisor;\n                  }\n                  this.contractDetailForm.contractForm.patchValue({\n                    supervisorId: contract.supervisorId,\n                    supervisorFullName: this.contractDetails?.supervisorFullName\n                  });\n                });\n              }\n            }\n          }\n\n          this.contract = contract;\n          this.isContractFinished = contract.status?.name === 'FINALIZADO';\n        },\n        error: (error) => {\n          this.alert.error(error.error?.detail ?? 'Error al cargar el contrato');\n        },\n      });\n  }\n\n  getContractorId(): number | undefined {\n    return this.contractorId;\n  }\n\n  openEarlyTerminationDialog(): void {\n    if (!this.contract || !this.AssociatedContractorsListComponent) return;\n\n    const latestContractorContract =\n      this.AssociatedContractorsListComponent.getLatestContractorContract();\n    if (latestContractorContract) {\n      this.dialog\n        .open(EarlyTerminationDialogComponent, {\n          width: '500px',\n          data: {\n            contractId: this.contract.id,\n            contractorId: latestContractorContract.contractor?.id,\n            lastContractorStartDate: latestContractorContract.contractStartDate,\n          },\n        })\n        .afterClosed()\n        .subscribe({\n          next: (result) => {\n            if (result) {\n              if (this.contractDetailForm) {\n                this.contractDetailForm.contractForm\n                  .get('earlyTermination')\n                  ?.disable();\n              }\n              if (\n                this.AssociatedContractorsListComponent &&\n                latestContractorContract.id\n              ) {\n                this.AssociatedContractorsListComponent.updateContractorContracts(\n                  result.endDate,\n                  latestContractorContract.id,\n                );\n              }\n            } else {\n              if (this.contractDetailForm) {\n                this.contractDetailForm.contractForm\n                  .get('earlyTermination')\n                  ?.setValue(false);\n              }\n            }\n          },\n        });\n    } else {\n      this.alert.warning('No hay contratistas asociados a este contrato');\n    }\n  }\n\n  onContractorContractsChanged(): void {\n    if (this.contract) {\n      this.contract.cession = true;\n    }\n    if (this.contractDetailForm) {\n      this.contractDetailForm.contractForm.patchValue({ cession: true });\n    }\n  }\n\n  uncheckEarlyTermination(): void {\n    if (this.contractDetailForm) {\n      this.contractDetailForm.contractForm\n        .get('earlyTermination')\n        ?.setValue(false);\n      this.contractDetailForm.contractForm.get('earlyTermination')?.enable();\n    }\n  }\n\n  updateContractStatus(isCompleted: boolean): void {\n    this.isContractFinished = isCompleted;\n  }\n\n  onContractValuesChanged(contractValues: ContractValues[]): void {\n    if (this.contract) {\n      this.contract.contractValues = contractValues;\n    }\n  }\n\n  onReductionsChanged(reductions: Reduction[]): void {\n    if (this.contract) {\n      this.contract.reduction = reductions;\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAaS;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;AAbT,SAASE,SAAS,EAAUC,SAAS,QAAQ,eAAe;AAC5D,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,SAAS,EAAEC,eAAe,QAAQ,0BAA0B;AACrE,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,iBAAiB,QAAQ,aAAa;AAC/C,SAASC,QAAQ,EAAEC,SAAS,QAAQ,gBAAgB;AAEpD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kCAAkC,QAAQ,mGAAmG;AACtJ,SAASC,iBAAiB,QAAQ,+DAA+D;AACjG,SAASC,6BAA6B,QAAQ,yFAAyF;AACvI,SAASC,6BAA6B,QAAQ,yFAAyF;AACvI,SAASC,+BAA+B,QAAQ,6FAA6F;AAC7I,SAASC,wBAAwB,QAAQ,6EAA6E;AACtH,SAASC,uBAAuB,QAAQ,2EAA2E;AACnH,SAASC,wBAAwB,QAAQ,6EAA6E;AAGtH,SAASC,eAAe,QAAQ,gDAAgD;AAChF,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,2BAA2B,QAAQ,sEAAsE;AAClH,SAASC,sBAAsB,QAAQ,yEAAyE;AAChH,SAASC,6BAA6B,QAAQ,0EAA0E;AAAC3B,cAAA,GAAA4B,CAAA;AA4BlH,IAAMC,2BAA2B,GAAjC,MAAMA,2BAA2B;EAkBtCC,YACmBC,KAAqB,EACrBC,eAAgC,EAChCC,MAAiB,EACjBC,KAAmB,EACnBC,OAA0B;IAAAnC,cAAA,GAAAoC,CAAA;IAAApC,cAAA,GAAA4B,CAAA;IAJ1B,KAAAG,KAAK,GAALA,KAAK;IAAgB/B,cAAA,GAAA4B,CAAA;IACrB,KAAAI,eAAe,GAAfA,eAAe;IAAiBhC,cAAA,GAAA4B,CAAA;IAChC,KAAAK,MAAM,GAANA,MAAM;IAAWjC,cAAA,GAAA4B,CAAA;IACjB,KAAAM,KAAK,GAALA,KAAK;IAAclC,cAAA,GAAA4B,CAAA;IACnB,KAAAO,OAAO,GAAPA,OAAO;IAAmBnC,cAAA,GAAA4B,CAAA;IAtB7C,KAAAS,QAAQ,GAAoB,IAAI;IAACrC,cAAA,GAAA4B,CAAA;IACjC,KAAAU,eAAe,GAA2B,IAAI;IAACtC,cAAA,GAAA4B,CAAA;IAC/C,KAAAW,kBAAkB,GAAG,KAAK;EAqBvB;EAEHC,QAAQA,CAAA;IAAAxC,cAAA,GAAAoC,CAAA;IAAApC,cAAA,GAAA4B,CAAA;IACN,IAAI,CAACG,KAAK,CAACU,MAAM,CAACC,SAAS,CAAED,MAAM,IAAI;MAAAzC,cAAA,GAAAoC,CAAA;MAAApC,cAAA,GAAA4B,CAAA;MACrC,IAAI,CAACe,gBAAgB,CAAC,CAACF,MAAM,CAAC,IAAI,CAAC,CAAC;IACtC,CAAC,CAAC;EACJ;EAEQE,gBAAgBA,CAACC,UAAkB;IAAA5C,cAAA,GAAAoC,CAAA;IAAApC,cAAA,GAAA4B,CAAA;IACzC,IAAI,CAACO,OAAO,CAACU,IAAI,EAAE;IAAC7C,cAAA,GAAA4B,CAAA;IAEpB,IAAI,CAACI,eAAe,CACjBc,cAAc,CAACF,UAAU,CAAC,CAC1BG,IAAI,CACHlC,SAAS,CAAEmC,OAAO,IAAI;MAAAhD,cAAA,GAAAoC,CAAA;MAAApC,cAAA,GAAA4B,CAAA;MACpB,IAAI,CAACU,eAAe,GAAGU,OAAO;MAAChD,cAAA,GAAA4B,CAAA;MAC/B,IAAI,CAACqB,YAAY,GAAGD,OAAO,CAACC,YAAY;MAACjD,cAAA,GAAA4B,CAAA;MAEzC,OAAO,IAAI,CAACI,eAAe,CAACkB,OAAO,CAACN,UAAU,CAAC;IACjD,CAAC,CAAC,EACFhC,QAAQ,CAAC,MAAM;MAAAZ,cAAA,GAAAoC,CAAA;MAAApC,cAAA,GAAA4B,CAAA;MAAA,WAAI,CAACO,OAAO,CAACgB,IAAI,EAAE;IAAF,CAAE,CAAC,CACpC,CACAT,SAAS,CAAC;MACTU,IAAI,EAAGf,QAAQ,IAAI;QAAArC,cAAA,GAAAoC,CAAA;QAAApC,cAAA,GAAA4B,CAAA;QACjB,IAAI,IAAI,CAACU,eAAe,EAAE;UAAAtC,cAAA,GAAAqD,CAAA;UAAArD,cAAA,GAAA4B,CAAA;UACxB,IAAI,CAAA5B,cAAA,GAAAqD,CAAA,cAAI,CAACf,eAAe,CAACgB,iBAAiB,MAAAtD,cAAA,GAAAqD,CAAA,UAAI,IAAI,CAACf,eAAe,CAACiB,mBAAmB,GAAE;YAAAvD,cAAA,GAAAqD,CAAA;YAAArD,cAAA,GAAA4B,CAAA;YACtFS,QAAQ,CAACmB,eAAe,GAAG;cACzBC,EAAE,EAAE,IAAI,CAACnB,eAAe,CAACgB,iBAAiB;cAC1CI,IAAI,EAAE,IAAI,CAACpB,eAAe,CAACiB;aAC5B;UACH,CAAC;YAAAvD,cAAA,GAAAqD,CAAA;UAAA;UAAArD,cAAA,GAAA4B,CAAA;UAED,IAAI,CAAA5B,cAAA,GAAAqD,CAAA,cAAI,CAACf,eAAe,CAACqB,eAAe,MAAA3D,cAAA,GAAAqD,CAAA,UAAI,IAAI,CAACf,eAAe,CAACsB,iBAAiB,GAAE;YAAA5D,cAAA,GAAAqD,CAAA;YAAArD,cAAA,GAAA4B,CAAA;YAClFS,QAAQ,CAACwB,aAAa,GAAG;cACvBJ,EAAE,EAAE,IAAI,CAACnB,eAAe,CAACqB,eAAe;cACxCD,IAAI,EAAE,IAAI,CAACpB,eAAe,CAACsB;aAC5B;UACH,CAAC;YAAA5D,cAAA,GAAAqD,CAAA;UAAA;UAAArD,cAAA,GAAA4B,CAAA;UAED,IAAI,CAAA5B,cAAA,GAAAqD,CAAA,cAAI,CAACf,eAAe,CAACwB,mBAAmB,MAAA9D,cAAA,GAAAqD,CAAA,UAAI,IAAI,CAACf,eAAe,CAACyB,qBAAqB,GAAE;YAAA/D,cAAA,GAAAqD,CAAA;YAAArD,cAAA,GAAA4B,CAAA;YAC1FS,QAAQ,CAAC2B,iBAAiB,GAAG;cAC3BP,EAAE,EAAE,IAAI,CAACnB,eAAe,CAACwB,mBAAmB;cAC5CJ,IAAI,EAAE,IAAI,CAACpB,eAAe,CAACyB;aAC5B;UACH,CAAC;YAAA/D,cAAA,GAAAqD,CAAA;UAAA;UAAArD,cAAA,GAAA4B,CAAA;UAED,IAAI,CAAA5B,cAAA,GAAAqD,CAAA,cAAI,CAACf,eAAe,CAAC2B,kBAAkB,MAAAjE,cAAA,GAAAqD,CAAA,UAAI,IAAI,CAACf,eAAe,CAAC4B,kBAAkB,GAAE;YAAAlE,cAAA,GAAAqD,CAAA;YAAArD,cAAA,GAAA4B,CAAA;YACtF,IAAI,IAAI,CAACU,eAAe,CAAC4B,kBAAkB,EAAE;cAAAlE,cAAA,GAAAqD,CAAA;cAC3C,MAAMc,QAAQ,IAAAnE,cAAA,GAAA4B,CAAA,QAAGwC,QAAQ,CAAC,IAAI,CAAC9B,eAAe,CAAC4B,kBAAkB,EAAE,EAAE,CAAC;cAAClE,cAAA,GAAA4B,CAAA;cACvES,QAAQ,CAACgC,YAAY,GAAGF,QAAQ;YAClC,CAAC;cAAAnE,cAAA,GAAAqD,CAAA;YAAA;YAAArD,cAAA,GAAA4B,CAAA;YAEDS,QAAQ,CAACiC,UAAU,GAAG;cACpBb,EAAE,EAAE,CAAAzD,cAAA,GAAAqD,CAAA,WAAAhB,QAAQ,CAACgC,YAAY,MAAArE,cAAA,GAAAqD,CAAA,WAAI,CAAC;cAC9BkB,QAAQ,EAAE,IAAI,CAACjC,eAAe,CAAC2B,kBAAkB;cACjDE,QAAQ,EAAE,CAAAnE,cAAA,GAAAqD,CAAA,WAAAe,QAAQ,CAAC,IAAI,CAAC9B,eAAe,CAAC4B,kBAAkB,EAAE,EAAE,CAAC,MAAAlE,cAAA,GAAAqD,CAAA,WAAI,CAAC;cACpEmB,QAAQ,EAAE,CAAAxE,cAAA,GAAAqD,CAAA,eAAI,CAACf,eAAe,CAACmC,kBAAkB,MAAAzE,cAAA,GAAAqD,CAAA,WAAI,EAAE;cACvDqB,KAAK,EAAE,EAAE;cACTC,MAAM,EAAE;gBAAElB,EAAE,EAAE,CAAC;gBAAEC,IAAI,EAAE;cAAE;aAC1B;YAAC1D,cAAA,GAAA4B,CAAA;YAEF,IAAI,IAAI,CAACgD,kBAAkB,EAAE;cAAA5E,cAAA,GAAAqD,CAAA;cAAArD,cAAA,GAAA4B,CAAA;cAC3BiD,UAAU,CAAC,MAAK;gBAAA7E,cAAA,GAAAoC,CAAA;gBAAApC,cAAA,GAAA4B,CAAA;gBACd,IAAIS,QAAQ,CAACiC,UAAU,EAAE;kBAAAtE,cAAA,GAAAqD,CAAA;kBAAArD,cAAA,GAAA4B,CAAA;kBACvB,IAAI,CAACgD,kBAAkB,CAACN,UAAU,GAAGjC,QAAQ,CAACiC,UAAU;gBAC1D,CAAC;kBAAAtE,cAAA,GAAAqD,CAAA;gBAAA;gBAAArD,cAAA,GAAA4B,CAAA;gBACD,IAAI,CAACgD,kBAAkB,CAACE,YAAY,CAACC,UAAU,CAAC;kBAC9CV,YAAY,EAAEhC,QAAQ,CAACgC,YAAY;kBACnCJ,kBAAkB,EAAE,IAAI,CAAC3B,eAAe,EAAE2B;iBAC3C,CAAC;cACJ,CAAC,CAAC;YACJ,CAAC;cAAAjE,cAAA,GAAAqD,CAAA;YAAA;UACH,CAAC;YAAArD,cAAA,GAAAqD,CAAA;UAAA;QACH,CAAC;UAAArD,cAAA,GAAAqD,CAAA;QAAA;QAAArD,cAAA,GAAA4B,CAAA;QAED,IAAI,CAACS,QAAQ,GAAGA,QAAQ;QAACrC,cAAA,GAAA4B,CAAA;QACzB,IAAI,CAACW,kBAAkB,GAAGF,QAAQ,CAAC2C,MAAM,EAAEtB,IAAI,KAAK,YAAY;MAClE,CAAC;MACDuB,KAAK,EAAGA,KAAK,IAAI;QAAAjF,cAAA,GAAAoC,CAAA;QAAApC,cAAA,GAAA4B,CAAA;QACf,IAAI,CAACM,KAAK,CAAC+C,KAAK,CAAC,CAAAjF,cAAA,GAAAqD,CAAA,WAAA4B,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAAlF,cAAA,GAAAqD,CAAA,WAAI,6BAA6B,EAAC;MACxE;KACD,CAAC;EACN;EAEA8B,eAAeA,CAAA;IAAAnF,cAAA,GAAAoC,CAAA;IAAApC,cAAA,GAAA4B,CAAA;IACb,OAAO,IAAI,CAACqB,YAAY;EAC1B;EAEAmC,0BAA0BA,CAAA;IAAApF,cAAA,GAAAoC,CAAA;IAAApC,cAAA,GAAA4B,CAAA;IACxB,IAAI,CAAA5B,cAAA,GAAAqD,CAAA,YAAC,IAAI,CAAChB,QAAQ,MAAArC,cAAA,GAAAqD,CAAA,WAAI,CAAC,IAAI,CAACtC,kCAAkC,GAAE;MAAAf,cAAA,GAAAqD,CAAA;MAAArD,cAAA,GAAA4B,CAAA;MAAA;IAAA,CAAO;MAAA5B,cAAA,GAAAqD,CAAA;IAAA;IAEvE,MAAMgC,wBAAwB,IAAArF,cAAA,GAAA4B,CAAA,QAC5B,IAAI,CAACb,kCAAkC,CAACuE,2BAA2B,EAAE;IAACtF,cAAA,GAAA4B,CAAA;IACxE,IAAIyD,wBAAwB,EAAE;MAAArF,cAAA,GAAAqD,CAAA;MAAArD,cAAA,GAAA4B,CAAA;MAC5B,IAAI,CAACK,MAAM,CACRsD,IAAI,CAACpE,+BAA+B,EAAE;QACrCqE,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE;UACJ7C,UAAU,EAAE,IAAI,CAACP,QAAQ,CAACoB,EAAE;UAC5BR,YAAY,EAAEoC,wBAAwB,CAACK,UAAU,EAAEjC,EAAE;UACrDkC,uBAAuB,EAAEN,wBAAwB,CAACO;;OAErD,CAAC,CACDC,WAAW,EAAE,CACbnD,SAAS,CAAC;QACTU,IAAI,EAAG0C,MAAM,IAAI;UAAA9F,cAAA,GAAAoC,CAAA;UAAApC,cAAA,GAAA4B,CAAA;UACf,IAAIkE,MAAM,EAAE;YAAA9F,cAAA,GAAAqD,CAAA;YAAArD,cAAA,GAAA4B,CAAA;YACV,IAAI,IAAI,CAACgD,kBAAkB,EAAE;cAAA5E,cAAA,GAAAqD,CAAA;cAAArD,cAAA,GAAA4B,CAAA;cAC3B,IAAI,CAACgD,kBAAkB,CAACE,YAAY,CACjCiB,GAAG,CAAC,kBAAkB,CAAC,EACtBC,OAAO,EAAE;YACf,CAAC;cAAAhG,cAAA,GAAAqD,CAAA;YAAA;YAAArD,cAAA,GAAA4B,CAAA;YACD,IACE,CAAA5B,cAAA,GAAAqD,CAAA,eAAI,CAACtC,kCAAkC,MAAAf,cAAA,GAAAqD,CAAA,WACvCgC,wBAAwB,CAAC5B,EAAE,GAC3B;cAAAzD,cAAA,GAAAqD,CAAA;cAAArD,cAAA,GAAA4B,CAAA;cACA,IAAI,CAACb,kCAAkC,CAACkF,yBAAyB,CAC/DH,MAAM,CAACI,OAAO,EACdb,wBAAwB,CAAC5B,EAAE,CAC5B;YACH,CAAC;cAAAzD,cAAA,GAAAqD,CAAA;YAAA;UACH,CAAC,MAAM;YAAArD,cAAA,GAAAqD,CAAA;YAAArD,cAAA,GAAA4B,CAAA;YACL,IAAI,IAAI,CAACgD,kBAAkB,EAAE;cAAA5E,cAAA,GAAAqD,CAAA;cAAArD,cAAA,GAAA4B,CAAA;cAC3B,IAAI,CAACgD,kBAAkB,CAACE,YAAY,CACjCiB,GAAG,CAAC,kBAAkB,CAAC,EACtBI,QAAQ,CAAC,KAAK,CAAC;YACrB,CAAC;cAAAnG,cAAA,GAAAqD,CAAA;YAAA;UACH;QACF;OACD,CAAC;IACN,CAAC,MAAM;MAAArD,cAAA,GAAAqD,CAAA;MAAArD,cAAA,GAAA4B,CAAA;MACL,IAAI,CAACM,KAAK,CAACkE,OAAO,CAAC,+CAA+C,CAAC;IACrE;EACF;EAEAC,4BAA4BA,CAAA;IAAArG,cAAA,GAAAoC,CAAA;IAAApC,cAAA,GAAA4B,CAAA;IAC1B,IAAI,IAAI,CAACS,QAAQ,EAAE;MAAArC,cAAA,GAAAqD,CAAA;MAAArD,cAAA,GAAA4B,CAAA;MACjB,IAAI,CAACS,QAAQ,CAACiE,OAAO,GAAG,IAAI;IAC9B,CAAC;MAAAtG,cAAA,GAAAqD,CAAA;IAAA;IAAArD,cAAA,GAAA4B,CAAA;IACD,IAAI,IAAI,CAACgD,kBAAkB,EAAE;MAAA5E,cAAA,GAAAqD,CAAA;MAAArD,cAAA,GAAA4B,CAAA;MAC3B,IAAI,CAACgD,kBAAkB,CAACE,YAAY,CAACC,UAAU,CAAC;QAAEuB,OAAO,EAAE;MAAI,CAAE,CAAC;IACpE,CAAC;MAAAtG,cAAA,GAAAqD,CAAA;IAAA;EACH;EAEAkD,uBAAuBA,CAAA;IAAAvG,cAAA,GAAAoC,CAAA;IAAApC,cAAA,GAAA4B,CAAA;IACrB,IAAI,IAAI,CAACgD,kBAAkB,EAAE;MAAA5E,cAAA,GAAAqD,CAAA;MAAArD,cAAA,GAAA4B,CAAA;MAC3B,IAAI,CAACgD,kBAAkB,CAACE,YAAY,CACjCiB,GAAG,CAAC,kBAAkB,CAAC,EACtBI,QAAQ,CAAC,KAAK,CAAC;MAACnG,cAAA,GAAA4B,CAAA;MACpB,IAAI,CAACgD,kBAAkB,CAACE,YAAY,CAACiB,GAAG,CAAC,kBAAkB,CAAC,EAAES,MAAM,EAAE;IACxE,CAAC;MAAAxG,cAAA,GAAAqD,CAAA;IAAA;EACH;EAEAoD,oBAAoBA,CAACC,WAAoB;IAAA1G,cAAA,GAAAoC,CAAA;IAAApC,cAAA,GAAA4B,CAAA;IACvC,IAAI,CAACW,kBAAkB,GAAGmE,WAAW;EACvC;EAEAC,uBAAuBA,CAACC,cAAgC;IAAA5G,cAAA,GAAAoC,CAAA;IAAApC,cAAA,GAAA4B,CAAA;IACtD,IAAI,IAAI,CAACS,QAAQ,EAAE;MAAArC,cAAA,GAAAqD,CAAA;MAAArD,cAAA,GAAA4B,CAAA;MACjB,IAAI,CAACS,QAAQ,CAACuE,cAAc,GAAGA,cAAc;IAC/C,CAAC;MAAA5G,cAAA,GAAAqD,CAAA;IAAA;EACH;EAEAwD,mBAAmBA,CAACC,UAAuB;IAAA9G,cAAA,GAAAoC,CAAA;IAAApC,cAAA,GAAA4B,CAAA;IACzC,IAAI,IAAI,CAACS,QAAQ,EAAE;MAAArC,cAAA,GAAAqD,CAAA;MAAArD,cAAA,GAAA4B,CAAA;MACjB,IAAI,CAACS,QAAQ,CAAC0E,SAAS,GAAGD,UAAU;IACtC,CAAC;MAAA9G,cAAA,GAAAqD,CAAA;IAAA;EACH;;;;;;;;;;;;;;;;;;;;;;;cAzLClD,SAAS;QAAA6G,IAAA,GAACjG,kCAAkC;MAAA;;cAG5CZ,SAAS;QAAA6G,IAAA,GAACvF,2BAA2B;MAAA;;cAGrCtB,SAAS;QAAA6G,IAAA,GAAC,sBAAsB;MAAA;;cAGhC7G,SAAS;QAAA6G,IAAA,GAAC9F,6BAA6B;MAAA;;;;;AAf7BW,2BAA2B,GAAAoF,UAAA,EAxBvC/G,SAAS,CAAC;EACTgH,QAAQ,EAAE,0BAA0B;EACpCC,QAAA,EAAAC,oBAAoD;EAEpDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPlH,mBAAmB,EACnBG,kBAAkB,EAClBE,cAAc,EACdD,aAAa,EACbF,eAAe,EACfQ,aAAa,EACbC,kCAAkC,EAClCW,sBAAsB,EACtBN,wBAAwB,EACxBE,wBAAwB,EACxBG,2BAA2B,EAC3BE,6BAA6B,EAC7BX,iBAAiB,EACjBC,6BAA6B,EAC7BI,uBAAuB,EACvBH,6BAA6B,CAC9B;;CACF,CAAC,C,EACWW,2BAA2B,CAgMvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}