import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { ReportReviewHistory } from '@contractor-dashboard/models/report-review-history.model';
import { environment } from '@env';
import { ReportReviewHistoryService } from './report-review-history.service';

describe('ReportReviewHistoryService', () => {
  let service: ReportReviewHistoryService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/report-review-histories`;

  const mockReportReviewHistory: ReportReviewHistory = {
    id: 1,
    comment: 'Test comments',
    reviewDate: new Date(),
    monthlyReportId: 1,
    reviewStatusId: 1,
    reviewerId: 1,
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ReportReviewHistoryService],
    });
    service = TestBed.inject(ReportReviewHistoryService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all report review histories', () => {
      const mockHistories = [mockReportReviewHistory];

      service.getAll().subscribe((histories) => {
        expect(histories).toEqual(mockHistories);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockHistories);
    });

    it('should handle error when getting all report review histories', () => {
      service.getAll().subscribe({
        error: (error) => {
          expect(error.status).toBe(500);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Server error'), { status: 500 });
    });
  });

  describe('getById', () => {
    it('should return a report review history by id', () => {
      const id = 1;

      service.getById(id).subscribe((history) => {
        expect(history).toEqual(mockReportReviewHistory);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockReportReviewHistory);
    });

    it('should handle error when getting report review history by id', () => {
      const id = 999;

      service.getById(id).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('create', () => {
    it('should create a new report review history', () => {
      const newHistory: Omit<ReportReviewHistory, 'id'> = {
        comment: 'Test comments',
        reviewDate: new Date(),
        monthlyReportId: 1,
        reviewStatusId: 1,
        reviewerId: 1,
      };

      service.create(newHistory).subscribe((history) => {
        expect(history).toEqual(mockReportReviewHistory);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newHistory);
      req.flush(mockReportReviewHistory);
    });

    it('should handle error when creating report review history', () => {
      const newHistory: Omit<ReportReviewHistory, 'id'> = {
        comment: 'Test comments',
        reviewDate: new Date(),
        monthlyReportId: 1,
        reviewStatusId: 1,
        reviewerId: 1,
      };

      service.create(newHistory).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('update', () => {
    it('should update a report review history', () => {
      const id = 1;
      const updateData: Partial<ReportReviewHistory> = {
        comment: 'Updated comments',
      };

      service.update(id, updateData).subscribe((history) => {
        expect(history).toEqual(mockReportReviewHistory);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockReportReviewHistory);
    });

    it('should handle error when updating report review history', () => {
      const id = 1;
      const updateData: Partial<ReportReviewHistory> = {
        comment: 'Updated comments',
      };

      service.update(id, updateData).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('delete', () => {
    it('should delete a report review history', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });

    it('should handle error when deleting report review history', () => {
      const id = 1;

      service.delete(id).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });
});