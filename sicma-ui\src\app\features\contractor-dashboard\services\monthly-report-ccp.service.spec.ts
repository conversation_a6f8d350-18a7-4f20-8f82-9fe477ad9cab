import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { MonthlyReportCcp } from '@contractor-dashboard/models/monthly-report-ccp.model';
import { environment } from '@env';
import { MonthlyReportCcpService } from './monthly-report-ccp.service';

describe('MonthlyReportCcpService', () => {
  let service: MonthlyReportCcpService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/monthly-report-ccp`;

  const mockMonthlyReportCcp: MonthlyReportCcp = {
    id: 1,
    ccpValue: 100,
    monthlyReportId: 1,
    ccpId: 1,
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [MonthlyReportCcpService],
    });
    service = TestBed.inject(MonthlyReportCcpService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all monthly report CCPs', () => {
      const mockReports = [mockMonthlyReportCcp];

      service.getAll().subscribe((reports) => {
        expect(reports).toEqual(mockReports);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockReports);
    });

    it('should handle error when getting all monthly report CCPs', () => {
      service.getAll().subscribe({
        error: (error) => {
          expect(error.status).toBe(500);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Server error'), { status: 500 });
    });
  });

  describe('getById', () => {
    it('should return a monthly report CCP by id', () => {
      const id = 1;

      service.getById(id).subscribe((report) => {
        expect(report).toEqual(mockMonthlyReportCcp);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockMonthlyReportCcp);
    });

    it('should handle error when getting monthly report CCP by id', () => {
      const id = 999;

      service.getById(id).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('create', () => {
    it('should create a new monthly report CCP', () => {
      const newReport: Omit<MonthlyReportCcp, 'id'> = {
        ccpValue: 100,
        monthlyReportId: 1,
        ccpId: 1,
      };

      service.create(newReport).subscribe((report) => {
        expect(report).toEqual(mockMonthlyReportCcp);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newReport);
      req.flush(mockMonthlyReportCcp);
    });

    it('should handle error when creating monthly report CCP', () => {
      const newReport: Omit<MonthlyReportCcp, 'id'> = {
        ccpValue: 100,
        monthlyReportId: 1,
        ccpId: 1,
      };

      service.create(newReport).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('update', () => {
    it('should update a monthly report CCP', () => {
      const id = 1;
      const updateData: Partial<MonthlyReportCcp> = {
        ccpValue: 200,
      };

      service.update(id, updateData).subscribe((report) => {
        expect(report).toEqual(mockMonthlyReportCcp);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockMonthlyReportCcp);
    });

    it('should handle error when updating monthly report CCP', () => {
      const id = 1;
      const updateData: Partial<MonthlyReportCcp> = {
        ccpValue: 200,
      };

      service.update(id, updateData).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('delete', () => {
    it('should delete a monthly report CCP', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });

    it('should handle error when deleting monthly report CCP', () => {
      const id = 1;

      service.delete(id).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });
});