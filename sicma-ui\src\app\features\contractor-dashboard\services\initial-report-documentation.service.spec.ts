import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { InitialReportDocumentation } from '@contractor-dashboard/models/initial-report-documentation.model';
import { environment } from '@env';
import { InitialReportDocumentationService } from './initial-report-documentation.service';

describe('InitialReportDocumentationService', () => {
  let service: InitialReportDocumentationService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/initial-report-documentations`;

  const mockInitialReportDocumentation: InitialReportDocumentation = {
    id: 1,
    contractorContractId: 1,
    epsId: 1,
    arlId: 1,
    pensionFundId: 1,
    bankId: 1,
    bankAccountTypeId: 1,
    taxRegimeId: 1,
    accountNumber: '*********',
    epsCertificateFileKey: 'eps-file-key',
    epsCertificateFileUrl: 'path/to/eps/file',
    arlCertificateFileKey: 'arl-file-key',
    arlCertificateFileUrl: 'path/to/arl/file',
    pensionCertificateFileKey: 'pension-file-key',
    pensionCertificateFileUrl: 'path/to/pension/file',
    bankCertificateFileKey: 'bank-file-key',
    bankCertificateFileUrl: 'path/to/bank/file',
    taxFormFileKey: 'tax-file-key',
    taxFormFileUrl: 'path/to/tax/file',
    signatureFileKey: 'signature-file-key',
    signatureFileUrl: 'path/to/signature/file',
    hasDependents: false,
    hasHousingInterest: false,
    hasPrepaidMedicine: false,
    hasAfcAccount: false,
    hasVoluntarySavings: false,
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [InitialReportDocumentationService],
    });
    service = TestBed.inject(InitialReportDocumentationService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all initial report documentations', () => {
      const mockDocumentations = [mockInitialReportDocumentation];

      service.getAll().subscribe((documentations) => {
        expect(documentations).toEqual(mockDocumentations);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockDocumentations);
    });
  });

  describe('getById', () => {
    it('should return an initial report documentation by id', () => {
      const id = 1;

      service.getById(id).subscribe((documentation) => {
        expect(documentation).toEqual(mockInitialReportDocumentation);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockInitialReportDocumentation);
    });
  });

  describe('getByContractorContractId', () => {
    it('should return an initial report documentation by contractor contract id', () => {
      const contractorContractId = 1;

      service
        .getByContractorContractId(contractorContractId)
        .subscribe((documentation) => {
          expect(documentation).toEqual(mockInitialReportDocumentation);
        });

      const req = httpMock.expectOne(
        `${apiUrl}/contractor-contract/${contractorContractId}`,
      );
      expect(req.request.method).toBe('GET');
      req.flush(mockInitialReportDocumentation);
    });
  });

  describe('create', () => {
    it('should create a new initial report documentation', () => {
      const newDocumentation: Omit<InitialReportDocumentation, 'id'> = {
        contractorContractId: 1,
        epsId: 1,
        arlId: 1,
        pensionFundId: 1,
        bankId: 1,
        bankAccountTypeId: 1,
        taxRegimeId: 1,
        accountNumber: '*********',
        hasDependents: false,
        hasHousingInterest: false,
        hasPrepaidMedicine: false,
        hasAfcAccount: false,
        hasVoluntarySavings: false,
      };

      service.create(newDocumentation).subscribe((documentation) => {
        expect(documentation).toEqual(mockInitialReportDocumentation);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newDocumentation);
      req.flush(mockInitialReportDocumentation);
    });
  });

  describe('update', () => {
    it('should update an initial report documentation', () => {
      const id = 1;
      const updateData: Partial<InitialReportDocumentation> = {
        accountNumber: '*********',
      };

      service.update(id, updateData).subscribe((documentation) => {
        expect(documentation).toEqual(mockInitialReportDocumentation);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockInitialReportDocumentation);
    });
  });

  describe('delete', () => {
    it('should delete an initial report documentation', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });

  describe('createWithFiles', () => {
    it('should create a new initial report documentation with files', () => {
      const formData = new FormData();
      formData.append('epsFile', new File([''], 'eps.pdf'));
      formData.append('arlFile', new File([''], 'arl.pdf'));
      formData.append('pensionFile', new File([''], 'pension.pdf'));
      formData.append('bankFile', new File([''], 'bank.pdf'));
      formData.append('taxFile', new File([''], 'tax.pdf'));
      formData.append('signatureFile', new File([''], 'signature.pdf'));
      formData.append(
        'data',
        JSON.stringify({
          contractorContractId: 1,
          epsId: 1,
          arlId: 1,
          pensionFundId: 1,
          bankId: 1,
          bankAccountTypeId: 1,
          taxRegimeId: 1,
          accountNumber: '*********',
          hasDependents: false,
          hasHousingInterest: false,
          hasPrepaidMedicine: false,
          hasAfcAccount: false,
          hasVoluntarySavings: false,
        }),
      );

      service.createWithFiles(formData).subscribe((documentation) => {
        expect(documentation).toEqual(mockInitialReportDocumentation);
      });

      const req = httpMock.expectOne(`${apiUrl}/with-files`);
      expect(req.request.method).toBe('POST');
      req.flush(mockInitialReportDocumentation);
    });
  });

  describe('updateWithFiles', () => {
    it('should update an initial report documentation with files', () => {
      const id = 1;
      const formData = new FormData();
      formData.append('epsFile', new File([''], 'eps.pdf'));
      formData.append('arlFile', new File([''], 'arl.pdf'));
      formData.append('pensionFile', new File([''], 'pension.pdf'));
      formData.append('bankFile', new File([''], 'bank.pdf'));
      formData.append('taxFile', new File([''], 'tax.pdf'));
      formData.append('signatureFile', new File([''], 'signature.pdf'));
      formData.append(
        'data',
        JSON.stringify({
          contractorContractId: 1,
          epsId: 1,
          arlId: 1,
          pensionFundId: 1,
          bankId: 1,
          bankAccountTypeId: 1,
          taxRegimeId: 1,
          accountNumber: '*********',
          hasDependents: false,
          hasHousingInterest: false,
          hasPrepaidMedicine: false,
          hasAfcAccount: false,
          hasVoluntarySavings: false,
        }),
      );

      service.updateWithFiles(id, formData).subscribe((documentation) => {
        expect(documentation).toEqual(mockInitialReportDocumentation);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}/with-files`);
      expect(req.request.method).toBe('PUT');
      req.flush(mockInitialReportDocumentation);
    });
  });
});