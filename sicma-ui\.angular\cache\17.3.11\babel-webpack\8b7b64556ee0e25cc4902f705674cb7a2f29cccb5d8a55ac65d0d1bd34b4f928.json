{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { MonthlyReportCcpService } from './monthly-report-ccp.service';\ndescribe('MonthlyReportCcpService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/monthly-report-ccp`;\n  const mockMonthlyReportCcp = {\n    id: 1,\n    ccpValue: 100,\n    monthlyReportId: 1,\n    ccpId: 1\n  };\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [MonthlyReportCcpService]\n    });\n    service = TestBed.inject(MonthlyReportCcpService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all monthly report CCPs', () => {\n      const mockReports = [mockMonthlyReportCcp];\n      service.getAll().subscribe(reports => {\n        expect(reports).toEqual(mockReports);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockReports);\n    });\n    it('should handle error when getting all monthly report CCPs', () => {\n      service.getAll().subscribe({\n        error: error => {\n          expect(error.status).toBe(500);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Server error'), {\n        status: 500\n      });\n    });\n  });\n  describe('getById', () => {\n    it('should return a monthly report CCP by id', () => {\n      const id = 1;\n      service.getById(id).subscribe(report => {\n        expect(report).toEqual(mockMonthlyReportCcp);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockMonthlyReportCcp);\n    });\n    it('should handle error when getting monthly report CCP by id', () => {\n      const id = 999;\n      service.getById(id).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n  describe('create', () => {\n    it('should create a new monthly report CCP', () => {\n      const newReport = {\n        ccpValue: 100,\n        monthlyReportId: 1,\n        ccpId: 1\n      };\n      service.create(newReport).subscribe(report => {\n        expect(report).toEqual(mockMonthlyReportCcp);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newReport);\n      req.flush(mockMonthlyReportCcp);\n    });\n    it('should handle error when creating monthly report CCP', () => {\n      const newReport = {\n        ccpValue: 100,\n        monthlyReportId: 1,\n        ccpId: 1\n      };\n      service.create(newReport).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n  describe('update', () => {\n    it('should update a monthly report CCP', () => {\n      const id = 1;\n      const updateData = {\n        ccpValue: 200\n      };\n      service.update(id, updateData).subscribe(report => {\n        expect(report).toEqual(mockMonthlyReportCcp);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockMonthlyReportCcp);\n    });\n    it('should handle error when updating monthly report CCP', () => {\n      const id = 1;\n      const updateData = {\n        ccpValue: 200\n      };\n      service.update(id, updateData).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n  describe('delete', () => {\n    it('should delete a monthly report CCP', () => {\n      const id = 1;\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n    it('should handle error when deleting monthly report CCP', () => {\n      const id = 1;\n      service.delete(id).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "MonthlyReportCcpService", "describe", "service", "httpMock", "apiUrl", "mockMonthlyReportCcp", "id", "ccpValue", "monthlyReportId", "ccpId", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockReports", "getAll", "subscribe", "reports", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "error", "status", "ErrorEvent", "getById", "report", "newReport", "create", "body", "updateData", "update", "delete", "nothing"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\services\\monthly-report-ccp.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { MonthlyReportCcp } from '@contractor-dashboard/models/monthly-report-ccp.model';\nimport { environment } from '@env';\nimport { MonthlyReportCcpService } from './monthly-report-ccp.service';\n\ndescribe('MonthlyReportCcpService', () => {\n  let service: MonthlyReportCcpService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/monthly-report-ccp`;\n\n  const mockMonthlyReportCcp: MonthlyReportCcp = {\n    id: 1,\n    ccpValue: 100,\n    monthlyReportId: 1,\n    ccpId: 1,\n  };\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [MonthlyReportCcpService],\n    });\n    service = TestBed.inject(MonthlyReportCcpService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all monthly report CCPs', () => {\n      const mockReports = [mockMonthlyReportCcp];\n\n      service.getAll().subscribe((reports) => {\n        expect(reports).toEqual(mockReports);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockReports);\n    });\n\n    it('should handle error when getting all monthly report CCPs', () => {\n      service.getAll().subscribe({\n        error: (error) => {\n          expect(error.status).toBe(500);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Server error'), { status: 500 });\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a monthly report CCP by id', () => {\n      const id = 1;\n\n      service.getById(id).subscribe((report) => {\n        expect(report).toEqual(mockMonthlyReportCcp);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockMonthlyReportCcp);\n    });\n\n    it('should handle error when getting monthly report CCP by id', () => {\n      const id = 999;\n\n      service.getById(id).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n\n  describe('create', () => {\n    it('should create a new monthly report CCP', () => {\n      const newReport: Omit<MonthlyReportCcp, 'id'> = {\n        ccpValue: 100,\n        monthlyReportId: 1,\n        ccpId: 1,\n      };\n\n      service.create(newReport).subscribe((report) => {\n        expect(report).toEqual(mockMonthlyReportCcp);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newReport);\n      req.flush(mockMonthlyReportCcp);\n    });\n\n    it('should handle error when creating monthly report CCP', () => {\n      const newReport: Omit<MonthlyReportCcp, 'id'> = {\n        ccpValue: 100,\n        monthlyReportId: 1,\n        ccpId: 1,\n      };\n\n      service.create(newReport).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n\n  describe('update', () => {\n    it('should update a monthly report CCP', () => {\n      const id = 1;\n      const updateData: Partial<MonthlyReportCcp> = {\n        ccpValue: 200,\n      };\n\n      service.update(id, updateData).subscribe((report) => {\n        expect(report).toEqual(mockMonthlyReportCcp);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockMonthlyReportCcp);\n    });\n\n    it('should handle error when updating monthly report CCP', () => {\n      const id = 1;\n      const updateData: Partial<MonthlyReportCcp> = {\n        ccpValue: 200,\n      };\n\n      service.update(id, updateData).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete a monthly report CCP', () => {\n      const id = 1;\n\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n\n    it('should handle error when deleting monthly report CCP', () => {\n      const id = 1;\n\n      service.delete(id).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,uBAAuB,QAAQ,8BAA8B;AAEtEC,QAAQ,CAAC,yBAAyB,EAAE,MAAK;EACvC,IAAIC,OAAgC;EACpC,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,qBAAqB;EAEzD,MAAMC,oBAAoB,GAAqB;IAC7CC,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,GAAG;IACbC,eAAe,EAAE,CAAC;IAClBC,KAAK,EAAE;GACR;EAEDC,UAAU,CAAC,MAAK;IACdZ,OAAO,CAACa,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAAChB,uBAAuB,CAAC;MAClCiB,SAAS,EAAE,CAACb,uBAAuB;KACpC,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACgB,MAAM,CAACd,uBAAuB,CAAC;IACjDG,QAAQ,GAAGL,OAAO,CAACgB,MAAM,CAACjB,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFkB,SAAS,CAAC,MAAK;IACbZ,QAAQ,CAACa,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAAChB,OAAO,CAAC,CAACiB,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFlB,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBgB,EAAE,CAAC,uCAAuC,EAAE,MAAK;MAC/C,MAAMG,WAAW,GAAG,CAACf,oBAAoB,CAAC;MAE1CH,OAAO,CAACmB,MAAM,EAAE,CAACC,SAAS,CAAEC,OAAO,IAAI;QACrCL,MAAM,CAACK,OAAO,CAAC,CAACC,OAAO,CAACJ,WAAW,CAAC;MACtC,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGtB,QAAQ,CAACuB,SAAS,CAACtB,MAAM,CAAC;MACtCc,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,WAAW,CAAC;IACxB,CAAC,CAAC;IAEFH,EAAE,CAAC,0DAA0D,EAAE,MAAK;MAClEf,OAAO,CAACmB,MAAM,EAAE,CAACC,SAAS,CAAC;QACzBS,KAAK,EAAGA,KAAK,IAAI;UACfb,MAAM,CAACa,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGtB,QAAQ,CAACuB,SAAS,CAACtB,MAAM,CAAC;MACtCqB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,cAAc,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC5D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF/B,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBgB,EAAE,CAAC,0CAA0C,EAAE,MAAK;MAClD,MAAMX,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAACgC,OAAO,CAAC5B,EAAE,CAAC,CAACgB,SAAS,CAAEa,MAAM,IAAI;QACvCjB,MAAM,CAACiB,MAAM,CAAC,CAACX,OAAO,CAACnB,oBAAoB,CAAC;MAC9C,CAAC,CAAC;MAEF,MAAMoB,GAAG,GAAGtB,QAAQ,CAACuB,SAAS,CAAC,GAAGtB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACzB,oBAAoB,CAAC;IACjC,CAAC,CAAC;IAEFY,EAAE,CAAC,2DAA2D,EAAE,MAAK;MACnE,MAAMX,EAAE,GAAG,GAAG;MAEdJ,OAAO,CAACgC,OAAO,CAAC5B,EAAE,CAAC,CAACgB,SAAS,CAAC;QAC5BS,KAAK,EAAGA,KAAK,IAAI;UACfb,MAAM,CAACa,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGtB,QAAQ,CAACuB,SAAS,CAAC,GAAGtB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF/B,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBgB,EAAE,CAAC,wCAAwC,EAAE,MAAK;MAChD,MAAMmB,SAAS,GAAiC;QAC9C7B,QAAQ,EAAE,GAAG;QACbC,eAAe,EAAE,CAAC;QAClBC,KAAK,EAAE;OACR;MAEDP,OAAO,CAACmC,MAAM,CAACD,SAAS,CAAC,CAACd,SAAS,CAAEa,MAAM,IAAI;QAC7CjB,MAAM,CAACiB,MAAM,CAAC,CAACX,OAAO,CAACnB,oBAAoB,CAAC;MAC9C,CAAC,CAAC;MAEF,MAAMoB,GAAG,GAAGtB,QAAQ,CAACuB,SAAS,CAACtB,MAAM,CAAC;MACtCc,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACW,IAAI,CAAC,CAACd,OAAO,CAACY,SAAS,CAAC;MAC3CX,GAAG,CAACK,KAAK,CAACzB,oBAAoB,CAAC;IACjC,CAAC,CAAC;IAEFY,EAAE,CAAC,sDAAsD,EAAE,MAAK;MAC9D,MAAMmB,SAAS,GAAiC;QAC9C7B,QAAQ,EAAE,GAAG;QACbC,eAAe,EAAE,CAAC;QAClBC,KAAK,EAAE;OACR;MAEDP,OAAO,CAACmC,MAAM,CAACD,SAAS,CAAC,CAACd,SAAS,CAAC;QAClCS,KAAK,EAAGA,KAAK,IAAI;UACfb,MAAM,CAACa,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGtB,QAAQ,CAACuB,SAAS,CAACtB,MAAM,CAAC;MACtCqB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF/B,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBgB,EAAE,CAAC,oCAAoC,EAAE,MAAK;MAC5C,MAAMX,EAAE,GAAG,CAAC;MACZ,MAAMiC,UAAU,GAA8B;QAC5ChC,QAAQ,EAAE;OACX;MAEDL,OAAO,CAACsC,MAAM,CAAClC,EAAE,EAAEiC,UAAU,CAAC,CAACjB,SAAS,CAAEa,MAAM,IAAI;QAClDjB,MAAM,CAACiB,MAAM,CAAC,CAACX,OAAO,CAACnB,oBAAoB,CAAC;MAC9C,CAAC,CAAC;MAEF,MAAMoB,GAAG,GAAGtB,QAAQ,CAACuB,SAAS,CAAC,GAAGtB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACW,IAAI,CAAC,CAACd,OAAO,CAACe,UAAU,CAAC;MAC5Cd,GAAG,CAACK,KAAK,CAACzB,oBAAoB,CAAC;IACjC,CAAC,CAAC;IAEFY,EAAE,CAAC,sDAAsD,EAAE,MAAK;MAC9D,MAAMX,EAAE,GAAG,CAAC;MACZ,MAAMiC,UAAU,GAA8B;QAC5ChC,QAAQ,EAAE;OACX;MAEDL,OAAO,CAACsC,MAAM,CAAClC,EAAE,EAAEiC,UAAU,CAAC,CAACjB,SAAS,CAAC;QACvCS,KAAK,EAAGA,KAAK,IAAI;UACfb,MAAM,CAACa,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGtB,QAAQ,CAACuB,SAAS,CAAC,GAAGtB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF/B,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBgB,EAAE,CAAC,oCAAoC,EAAE,MAAK;MAC5C,MAAMX,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAACuC,MAAM,CAACnC,EAAE,CAAC,CAACgB,SAAS,CAAC,MAAK;QAChCJ,MAAM,EAAE,CAACwB,OAAO,EAAE;MACpB,CAAC,CAAC;MAEF,MAAMjB,GAAG,GAAGtB,QAAQ,CAACuB,SAAS,CAAC,GAAGtB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;IAEFb,EAAE,CAAC,sDAAsD,EAAE,MAAK;MAC9D,MAAMX,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAACuC,MAAM,CAACnC,EAAE,CAAC,CAACgB,SAAS,CAAC;QAC3BS,KAAK,EAAGA,KAAK,IAAI;UACfb,MAAM,CAACa,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGtB,QAAQ,CAACuB,SAAS,CAAC,GAAGtB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}