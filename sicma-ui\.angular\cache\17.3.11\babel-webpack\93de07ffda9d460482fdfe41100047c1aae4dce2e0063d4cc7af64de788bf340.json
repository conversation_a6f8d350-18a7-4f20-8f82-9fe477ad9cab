{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/Github/SICMA/sicma-ui/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { FormBuilder, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { CcpService } from '@contract-management/services/ccp.service';\nimport { ContractValuesService } from '@contract-management/services/contract-values.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { NgxCurrencyDirective } from 'ngx-currency';\nimport { of, throwError } from 'rxjs';\nimport { CcpDialogComponent } from './ccp-dialog.component';\ndescribe('CcpDialogComponent', () => {\n  let component;\n  let fixture;\n  let dialogRef;\n  let ccpService;\n  let contractValuesService;\n  let alertService;\n  const mockCcp = {\n    id: 1,\n    expenseObjectUseCcp: 'Test Object',\n    expenseObjectDescription: 'Test Description',\n    value: 1000,\n    additionalValue: 500,\n    contractId: 1\n  };\n  const mockDialogData = {\n    contractId: 1,\n    totalCcpValue: 3000\n  };\n  const mockContractValues = [{\n    id: 1,\n    numericValue: 5000,\n    madsValue: 0,\n    isOtherEntity: false,\n    subscriptionDate: '2024-01-01',\n    cdp: 123,\n    cdpEntityId: 1,\n    cdpEntity: {\n      id: 1,\n      name: 'Test Entity'\n    }\n  }];\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    dialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);\n    ccpService = jasmine.createSpyObj('CcpService', ['create', 'update']);\n    contractValuesService = jasmine.createSpyObj('ContractValuesService', ['getAllByContractId']);\n    alertService = jasmine.createSpyObj('AlertService', ['success', 'error']);\n    contractValuesService.getAllByContractId.and.returnValue(of(mockContractValues));\n    yield TestBed.configureTestingModule({\n      imports: [CcpDialogComponent, HttpClientTestingModule, BrowserAnimationsModule, ReactiveFormsModule, MatDialogModule, MatButtonModule, MatFormFieldModule, MatInputModule, MatIconModule, NgxCurrencyDirective],\n      providers: [FormBuilder, {\n        provide: MatDialogRef,\n        useValue: dialogRef\n      }, {\n        provide: MAT_DIALOG_DATA,\n        useValue: mockDialogData\n      }, {\n        provide: CcpService,\n        useValue: ccpService\n      }, {\n        provide: ContractValuesService,\n        useValue: contractValuesService\n      }, {\n        provide: AlertService,\n        useValue: alertService\n      }, {\n        provide: 'NGX_CURRENCY_CONFIG',\n        useValue: {\n          align: 'right',\n          allowNegative: false,\n          allowZero: true,\n          decimal: '.',\n          precision: 2,\n          prefix: '$ ',\n          suffix: '',\n          thousands: ',',\n          nullable: true\n        }\n      }]\n    }).compileComponents();\n    fixture = TestBed.createComponent(CcpDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  }));\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should initialize form with empty values for new ccp', () => {\n    expect(component.ccpForm.get('expenseObjectUseCcp')?.value).toBe('');\n    expect(component.ccpForm.get('expenseObjectDescription')?.value).toBe('');\n    expect(component.ccpForm.get('value')?.value).toBeNull();\n    expect(contractValuesService.getAllByContractId).toHaveBeenCalledWith(mockDialogData.contractId);\n  });\n  it('should initialize form with existing ccp values', () => {\n    TestBed.resetTestingModule();\n    TestBed.configureTestingModule({\n      imports: [CcpDialogComponent, HttpClientTestingModule, BrowserAnimationsModule, ReactiveFormsModule, MatDialogModule, MatButtonModule, MatFormFieldModule, MatInputModule, MatIconModule, NgxCurrencyDirective],\n      providers: [FormBuilder, {\n        provide: MatDialogRef,\n        useValue: dialogRef\n      }, {\n        provide: MAT_DIALOG_DATA,\n        useValue: {\n          ...mockDialogData,\n          ccp: mockCcp\n        }\n      }, {\n        provide: CcpService,\n        useValue: ccpService\n      }, {\n        provide: ContractValuesService,\n        useValue: contractValuesService\n      }, {\n        provide: AlertService,\n        useValue: alertService\n      }, {\n        provide: 'NGX_CURRENCY_CONFIG',\n        useValue: {\n          align: 'right',\n          allowNegative: false,\n          allowZero: true,\n          decimal: '.',\n          precision: 2,\n          prefix: '$ ',\n          suffix: '',\n          thousands: ',',\n          nullable: true\n        }\n      }]\n    }).compileComponents();\n    fixture = TestBed.createComponent(CcpDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n    expect(component.ccpForm.get('expenseObjectUseCcp')?.value).toBe(mockCcp.expenseObjectUseCcp);\n    expect(component.ccpForm.get('expenseObjectDescription')?.value).toBe(mockCcp.expenseObjectDescription);\n    expect(component.ccpForm.get('value')?.value).toBe(mockCcp.value);\n    expect(component.ccpForm.get('additionalValue')?.value).toBe(mockCcp.additionalValue);\n    expect(contractValuesService.getAllByContractId).toHaveBeenCalledWith(mockDialogData.contractId);\n  });\n  it('should validate required fields', () => {\n    component.ccpForm.controls['expenseObjectUseCcp'].setValue('');\n    component.ccpForm.controls['expenseObjectDescription'].setValue('');\n    component.ccpForm.controls['value'].setValue(null);\n    expect(component.ccpForm.valid).toBeFalse();\n    expect(component.ccpForm.get('expenseObjectUseCcp')?.errors?.['required']).toBeTruthy();\n    expect(component.ccpForm.get('expenseObjectDescription')?.errors?.['required']).toBeTruthy();\n    expect(component.ccpForm.get('value')?.errors?.['required']).toBeTruthy();\n  });\n  it('should validate minimum value constraints', () => {\n    component.ccpForm.controls['value'].setValue(-1);\n    expect(component.ccpForm.get('value')?.errors?.['min']).toBeTruthy();\n    component.ccpForm.controls['additionalValue'].enable();\n    component.ccpForm.controls['additionalValue'].setValue(-1);\n    expect(component.ccpForm.get('additionalValue')?.errors?.['min']).toBeTruthy();\n  });\n  it('should close dialog without changes', () => {\n    component.onCancel();\n    expect(dialogRef.close).toHaveBeenCalled();\n  });\n  it('should create new ccp successfully', () => {\n    const newCcp = {\n      expenseObjectUseCcp: 'New Object',\n      expenseObjectDescription: 'New Description',\n      value: 2000,\n      contractId: 1,\n      additionalValue: 0\n    };\n    component.ccpForm.patchValue({\n      expenseObjectUseCcp: 'New Object',\n      expenseObjectDescription: 'New Description',\n      value: 2000\n    });\n    ccpService.create.and.returnValue(of({\n      ...newCcp,\n      id: 2\n    }));\n    component.onSubmit();\n    expect(ccpService.create).toHaveBeenCalledWith(newCcp);\n    expect(alertService.success).toHaveBeenCalledWith('CCP creado exitosamente');\n    expect(dialogRef.close).toHaveBeenCalledWith({\n      ...newCcp,\n      id: 2\n    });\n  });\n  it('should handle error when creating ccp', () => {\n    const newCcp = {\n      expenseObjectUseCcp: 'New Object',\n      expenseObjectDescription: 'New Description',\n      value: 2000,\n      contractId: 1,\n      additionalValue: 0\n    };\n    component.ccpForm.patchValue({\n      expenseObjectUseCcp: 'New Object',\n      expenseObjectDescription: 'New Description',\n      value: 2000\n    });\n    ccpService.create.and.returnValue(throwError(() => ({\n      error: 'Error'\n    })));\n    component.onSubmit();\n    expect(ccpService.create).toHaveBeenCalledWith(newCcp);\n    expect(alertService.error).toHaveBeenCalledWith('Error al crear el CCP');\n    expect(dialogRef.close).not.toHaveBeenCalled();\n  });\n  it('should update existing ccp successfully', () => {\n    TestBed.resetTestingModule();\n    dialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);\n    ccpService = jasmine.createSpyObj('CcpService', ['create', 'update']);\n    contractValuesService = jasmine.createSpyObj('ContractValuesService', ['getAllByContractId']);\n    alertService = jasmine.createSpyObj('AlertService', ['success', 'error']);\n    contractValuesService.getAllByContractId.and.returnValue(of(mockContractValues));\n    TestBed.configureTestingModule({\n      imports: [CcpDialogComponent, HttpClientTestingModule, BrowserAnimationsModule, ReactiveFormsModule, MatDialogModule, MatButtonModule, MatFormFieldModule, MatInputModule, MatIconModule, NgxCurrencyDirective],\n      providers: [FormBuilder, {\n        provide: MatDialogRef,\n        useValue: dialogRef\n      }, {\n        provide: MAT_DIALOG_DATA,\n        useValue: {\n          ...mockDialogData,\n          ccp: mockCcp\n        }\n      }, {\n        provide: CcpService,\n        useValue: ccpService\n      }, {\n        provide: ContractValuesService,\n        useValue: contractValuesService\n      }, {\n        provide: AlertService,\n        useValue: alertService\n      }, {\n        provide: 'NGX_CURRENCY_CONFIG',\n        useValue: {\n          align: 'right',\n          allowNegative: false,\n          allowZero: true,\n          decimal: '.',\n          precision: 2,\n          prefix: '$ ',\n          suffix: '',\n          thousands: ',',\n          nullable: true\n        }\n      }]\n    }).compileComponents();\n    fixture = TestBed.createComponent(CcpDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n    component.ccpForm.setValue({\n      expenseObjectUseCcp: 'Updated Object',\n      expenseObjectDescription: 'Updated Description',\n      value: 1000,\n      additionalValue: 3000\n    });\n    spyOn(component.ccpForm, 'get').and.returnValue({\n      value: 'test value',\n      valid: true,\n      disabled: false,\n      setValue: jasmine.createSpy('setValue'),\n      patchValue: jasmine.createSpy('patchValue'),\n      reset: jasmine.createSpy('reset'),\n      updateValueAndValidity: jasmine.createSpy('updateValueAndValidity')\n    });\n    const updatedCcp = {\n      id: 1,\n      expenseObjectUseCcp: 'Updated Object',\n      expenseObjectDescription: 'Updated Description',\n      value: 1000,\n      additionalValue: 3000,\n      contractId: 1\n    };\n    ccpService.update.and.returnValue(of(updatedCcp));\n    Object.defineProperty(component.ccpForm, 'valid', {\n      get: () => true\n    });\n    component.onSubmit();\n    expect(ccpService.update).toHaveBeenCalled();\n    expect(alertService.success).toHaveBeenCalledWith('CCP editado exitosamente');\n    expect(dialogRef.close).toHaveBeenCalled();\n  });\n  it('should handle error when updating ccp', () => {\n    TestBed.resetTestingModule();\n    dialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);\n    ccpService = jasmine.createSpyObj('CcpService', ['create', 'update']);\n    contractValuesService = jasmine.createSpyObj('ContractValuesService', ['getAllByContractId']);\n    alertService = jasmine.createSpyObj('AlertService', ['success', 'error']);\n    contractValuesService.getAllByContractId.and.returnValue(of(mockContractValues));\n    TestBed.configureTestingModule({\n      imports: [CcpDialogComponent, HttpClientTestingModule, BrowserAnimationsModule, ReactiveFormsModule, MatDialogModule, MatButtonModule, MatFormFieldModule, MatInputModule, MatIconModule, NgxCurrencyDirective],\n      providers: [FormBuilder, {\n        provide: MatDialogRef,\n        useValue: dialogRef\n      }, {\n        provide: MAT_DIALOG_DATA,\n        useValue: {\n          ...mockDialogData,\n          ccp: mockCcp\n        }\n      }, {\n        provide: CcpService,\n        useValue: ccpService\n      }, {\n        provide: ContractValuesService,\n        useValue: contractValuesService\n      }, {\n        provide: AlertService,\n        useValue: alertService\n      }, {\n        provide: 'NGX_CURRENCY_CONFIG',\n        useValue: {\n          align: 'right',\n          allowNegative: false,\n          allowZero: true,\n          decimal: '.',\n          precision: 2,\n          prefix: '$ ',\n          suffix: '',\n          thousands: ',',\n          nullable: true\n        }\n      }]\n    }).compileComponents();\n    fixture = TestBed.createComponent(CcpDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n    component.ccpForm.setValue({\n      expenseObjectUseCcp: 'Updated Object',\n      expenseObjectDescription: 'Updated Description',\n      value: 1000,\n      additionalValue: 3000\n    });\n    Object.defineProperty(component.ccpForm, 'valid', {\n      get: () => true\n    });\n    ccpService.update.and.returnValue(throwError(() => ({\n      error: 'Error'\n    })));\n    component.onSubmit();\n    expect(ccpService.update).toHaveBeenCalled();\n    expect(alertService.error).toHaveBeenCalledWith('Error al editar el CCP');\n    expect(dialogRef.close).not.toHaveBeenCalled();\n  });\n  it('should handle error when loading contract values', () => {\n    TestBed.resetTestingModule();\n    contractValuesService = jasmine.createSpyObj('ContractValuesService', ['getAllByContractId']);\n    contractValuesService.getAllByContractId.and.returnValue(throwError(() => ({\n      error: 'Error'\n    })));\n    TestBed.configureTestingModule({\n      imports: [CcpDialogComponent, HttpClientTestingModule, BrowserAnimationsModule, ReactiveFormsModule, MatDialogModule, MatButtonModule, MatFormFieldModule, MatInputModule, MatIconModule, NgxCurrencyDirective],\n      providers: [FormBuilder, {\n        provide: MatDialogRef,\n        useValue: dialogRef\n      }, {\n        provide: MAT_DIALOG_DATA,\n        useValue: mockDialogData\n      }, {\n        provide: CcpService,\n        useValue: ccpService\n      }, {\n        provide: ContractValuesService,\n        useValue: contractValuesService\n      }, {\n        provide: AlertService,\n        useValue: alertService\n      }, {\n        provide: 'NGX_CURRENCY_CONFIG',\n        useValue: {\n          align: 'right',\n          allowNegative: false,\n          allowZero: true,\n          decimal: '.',\n          precision: 2,\n          prefix: '$ ',\n          suffix: '',\n          thousands: ',',\n          nullable: true\n        }\n      }]\n    }).compileComponents();\n    fixture = TestBed.createComponent(CcpDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n    expect(alertService.error).toHaveBeenCalledWith('Error al cargar los valores del contrato');\n  });\n  it('should validate maxValueExceeded for new CCP', () => {\n    component.remainingValue = 1000;\n    component.ccpForm.get('value')?.setValue(1500);\n    fixture.detectChanges();\n    expect(component.ccpForm.get('value')?.errors?.['maxValueExceeded']).toBeTruthy();\n    component.ccpForm.get('value')?.setValue(800);\n    fixture.detectChanges();\n    expect(component.ccpForm.get('value')?.errors?.['maxValueExceeded']).toBeFalsy();\n  });\n  it('should validate additionalValue for existing CCP - less than previous value', () => {\n    TestBed.resetTestingModule();\n    const existingCcp = {\n      ...mockCcp,\n      additionalValue: 1000\n    };\n    TestBed.configureTestingModule({\n      imports: [CcpDialogComponent, HttpClientTestingModule, BrowserAnimationsModule, ReactiveFormsModule, MatDialogModule, MatButtonModule, MatFormFieldModule, MatInputModule, MatIconModule, NgxCurrencyDirective],\n      providers: [FormBuilder, {\n        provide: MatDialogRef,\n        useValue: dialogRef\n      }, {\n        provide: MAT_DIALOG_DATA,\n        useValue: {\n          ...mockDialogData,\n          ccp: existingCcp\n        }\n      }, {\n        provide: CcpService,\n        useValue: ccpService\n      }, {\n        provide: ContractValuesService,\n        useValue: contractValuesService\n      }, {\n        provide: AlertService,\n        useValue: alertService\n      }, {\n        provide: 'NGX_CURRENCY_CONFIG',\n        useValue: {\n          align: 'right',\n          allowNegative: false,\n          allowZero: true,\n          decimal: '.',\n          precision: 2,\n          prefix: '$ ',\n          suffix: '',\n          thousands: ',',\n          nullable: true\n        }\n      }]\n    }).compileComponents();\n    fixture = TestBed.createComponent(CcpDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n    component.ccpForm.get('additionalValue')?.setValue(500);\n    fixture.detectChanges();\n    expect(component.ccpForm.get('additionalValue')?.errors?.['lessThanPrevious']).toBeTruthy();\n  });\n  it('should validate additionalValue for existing CCP - exceeds available value', () => {\n    TestBed.resetTestingModule();\n    const existingCcp = {\n      ...mockCcp,\n      additionalValue: 1000\n    };\n    TestBed.configureTestingModule({\n      imports: [CcpDialogComponent, HttpClientTestingModule, BrowserAnimationsModule, ReactiveFormsModule, MatDialogModule, MatButtonModule, MatFormFieldModule, MatInputModule, MatIconModule, NgxCurrencyDirective],\n      providers: [FormBuilder, {\n        provide: MatDialogRef,\n        useValue: dialogRef\n      }, {\n        provide: MAT_DIALOG_DATA,\n        useValue: {\n          ...mockDialogData,\n          ccp: existingCcp\n        }\n      }, {\n        provide: CcpService,\n        useValue: ccpService\n      }, {\n        provide: ContractValuesService,\n        useValue: contractValuesService\n      }, {\n        provide: AlertService,\n        useValue: alertService\n      }, {\n        provide: 'NGX_CURRENCY_CONFIG',\n        useValue: {\n          align: 'right',\n          allowNegative: false,\n          allowZero: true,\n          decimal: '.',\n          precision: 2,\n          prefix: '$ ',\n          suffix: '',\n          thousands: ',',\n          nullable: true\n        }\n      }]\n    }).compileComponents();\n    fixture = TestBed.createComponent(CcpDialogComponent);\n    component = fixture.componentInstance;\n    component.totalContractValue = 5000;\n    component.currentTotalCcpValue = 4000;\n    component.remainingValue = 2000;\n    fixture.detectChanges();\n    component.ccpForm.get('additionalValue')?.setValue(4000);\n    fixture.detectChanges();\n    expect(component.ccpForm.get('additionalValue')?.errors?.['maxValueExceeded']).toBeTruthy();\n    component.ccpForm.get('additionalValue')?.setValue(1500);\n    fixture.detectChanges();\n    expect(component.ccpForm.get('additionalValue')?.errors?.['maxValueExceeded']).toBeFalsy();\n  });\n  it('should not submit form when it is invalid', () => {\n    component.ccpForm.controls['expenseObjectUseCcp'].setValue('');\n    component.ccpForm.controls['expenseObjectDescription'].setValue('');\n    component.ccpForm.controls['value'].setValue(null);\n    component.onSubmit();\n    expect(ccpService.create).not.toHaveBeenCalled();\n    expect(dialogRef.close).not.toHaveBeenCalled();\n  });\n  it('should subscribe to value changes for new CCPs', () => {\n    TestBed.resetTestingModule();\n    dialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);\n    ccpService = jasmine.createSpyObj('CcpService', ['create', 'update']);\n    contractValuesService = jasmine.createSpyObj('ContractValuesService', ['getAllByContractId']);\n    alertService = jasmine.createSpyObj('AlertService', ['success', 'error']);\n    contractValuesService.getAllByContractId.and.returnValue(of(mockContractValues));\n    TestBed.configureTestingModule({\n      imports: [CcpDialogComponent, HttpClientTestingModule, BrowserAnimationsModule, ReactiveFormsModule, MatDialogModule, MatButtonModule, MatFormFieldModule, MatInputModule, MatIconModule, NgxCurrencyDirective],\n      providers: [FormBuilder, {\n        provide: MatDialogRef,\n        useValue: dialogRef\n      }, {\n        provide: MAT_DIALOG_DATA,\n        useValue: mockDialogData\n      }, {\n        provide: CcpService,\n        useValue: ccpService\n      }, {\n        provide: ContractValuesService,\n        useValue: contractValuesService\n      }, {\n        provide: AlertService,\n        useValue: alertService\n      }, {\n        provide: 'NGX_CURRENCY_CONFIG',\n        useValue: {\n          align: 'right',\n          allowNegative: false,\n          allowZero: true,\n          decimal: '.',\n          precision: 2,\n          prefix: '$ ',\n          suffix: '',\n          thousands: ',',\n          nullable: true\n        }\n      }]\n    }).compileComponents();\n    fixture = TestBed.createComponent(CcpDialogComponent);\n    component = fixture.componentInstance;\n    component.remainingValue = 3000;\n    const valueControl = component.ccpForm.get('value');\n    spyOn(valueControl, 'setErrors');\n    fixture.detectChanges();\n    valueControl.setValue(4000);\n    expect(valueControl.setErrors).toHaveBeenCalledWith({\n      maxValueExceeded: true\n    });\n    valueControl.setValue(2000);\n    expect(valueControl.setErrors).toHaveBeenCalledTimes(1);\n  });\n  it('should calculate contract values properly', () => {\n    contractValuesService.getAllByContractId.and.returnValue(of([{\n      id: 1,\n      numericValue: 5000,\n      madsValue: 0,\n      isOtherEntity: false,\n      subscriptionDate: '2024-01-01',\n      cdp: 123,\n      cdpEntityId: 1,\n      cdpEntity: {\n        id: 1,\n        name: 'Test Entity'\n      }\n    }, {\n      id: 2,\n      numericValue: 3000,\n      madsValue: 0,\n      isOtherEntity: false,\n      subscriptionDate: '2024-01-01',\n      cdp: 456,\n      cdpEntityId: 1,\n      cdpEntity: {\n        id: 1,\n        name: 'Test Entity'\n      }\n    }]));\n    fixture = TestBed.createComponent(CcpDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n    expect(component.totalContractValue).toBe(8000);\n    expect(component.remainingValue).toBe(5000);\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "TestBed", "FormBuilder", "ReactiveFormsModule", "MatButtonModule", "MAT_DIALOG_DATA", "MatDialogModule", "MatDialogRef", "MatFormFieldModule", "MatIconModule", "MatInputModule", "BrowserAnimationsModule", "CcpService", "ContractValuesService", "AlertService", "NgxCurrencyDirective", "of", "throwError", "CcpDialogComponent", "describe", "component", "fixture", "dialogRef", "ccpService", "contractValuesService", "alertService", "mockCcp", "id", "expenseObjectUseCcp", "expenseObjectDescription", "value", "additionalValue", "contractId", "mockDialogData", "totalCcpValue", "mockContractValues", "numericValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isOtherEntity", "subscriptionDate", "cdp", "cdpEntityId", "cdpEntity", "name", "beforeEach", "_asyncToGenerator", "jasmine", "createSpyObj", "getAllByContractId", "and", "returnValue", "configureTestingModule", "imports", "providers", "provide", "useValue", "align", "allowNegative", "allowZero", "decimal", "precision", "prefix", "suffix", "thousands", "nullable", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy", "ccpForm", "get", "toBe", "toBeNull", "toHaveBeenCalledWith", "resetTestingModule", "ccp", "controls", "setValue", "valid", "toBeFalse", "errors", "enable", "onCancel", "close", "toHaveBeenCalled", "newCcp", "patchValue", "create", "onSubmit", "success", "error", "not", "spyOn", "disabled", "createSpy", "reset", "updateValueAndValidity", "updatedCcp", "update", "Object", "defineProperty", "remainingValue", "toBeFalsy", "existingCcp", "totalContractValue", "currentTotalCcpValue", "valueControl", "setErrors", "maxValueExceeded", "toHaveBeenCalledTimes"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\components\\ccps-list\\ccp-dialog\\ccp-dialog.component.spec.ts"], "sourcesContent": ["import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { ComponentFixture, TestBed } from '@angular/core/testing';\nimport {\n  AbstractControl,\n  FormBuilder,\n  ReactiveFormsModule,\n} from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport {\n  MAT_DIALOG_DATA,\n  MatDialogModule,\n  MatDialogRef,\n} from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { CCP } from '@contract-management/models/ccp.model';\nimport { CcpService } from '@contract-management/services/ccp.service';\nimport { ContractValuesService } from '@contract-management/services/contract-values.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { NgxCurrencyDirective } from 'ngx-currency';\nimport { of, throwError } from 'rxjs';\nimport { CcpDialogComponent } from './ccp-dialog.component';\n\ndescribe('CcpDialogComponent', () => {\n  let component: CcpDialogComponent;\n  let fixture: ComponentFixture<CcpDialogComponent>;\n  let dialogRef: jasmine.SpyObj<MatDialogRef<CcpDialogComponent>>;\n  let ccpService: jasmine.SpyObj<CcpService>;\n  let contractValuesService: jasmine.SpyObj<ContractValuesService>;\n  let alertService: jasmine.SpyObj<AlertService>;\n\n  const mockCcp: CCP = {\n    id: 1,\n    expenseObjectUseCcp: 'Test Object',\n    expenseObjectDescription: 'Test Description',\n    value: 1000,\n    additionalValue: 500,\n    contractId: 1,\n  };\n\n  const mockDialogData = {\n    contractId: 1,\n    totalCcpValue: 3000,\n  };\n\n  const mockContractValues = [\n    {\n      id: 1,\n      numericValue: 5000,\n      madsValue: 0,\n      isOtherEntity: false,\n      subscriptionDate: '2024-01-01',\n      cdp: 123,\n      cdpEntityId: 1,\n      cdpEntity: { id: 1, name: 'Test Entity' },\n    },\n  ];\n\n  beforeEach(async () => {\n    dialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);\n    ccpService = jasmine.createSpyObj('CcpService', ['create', 'update']);\n    contractValuesService = jasmine.createSpyObj('ContractValuesService', [\n      'getAllByContractId',\n    ]);\n    alertService = jasmine.createSpyObj('AlertService', ['success', 'error']);\n\n    contractValuesService.getAllByContractId.and.returnValue(\n      of(mockContractValues),\n    );\n\n    await TestBed.configureTestingModule({\n      imports: [\n        CcpDialogComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n        ReactiveFormsModule,\n        MatDialogModule,\n        MatButtonModule,\n        MatFormFieldModule,\n        MatInputModule,\n        MatIconModule,\n        NgxCurrencyDirective,\n      ],\n      providers: [\n        FormBuilder,\n        { provide: MatDialogRef, useValue: dialogRef },\n        { provide: MAT_DIALOG_DATA, useValue: mockDialogData },\n        { provide: CcpService, useValue: ccpService },\n        { provide: ContractValuesService, useValue: contractValuesService },\n        { provide: AlertService, useValue: alertService },\n        {\n          provide: 'NGX_CURRENCY_CONFIG',\n          useValue: {\n            align: 'right',\n            allowNegative: false,\n            allowZero: true,\n            decimal: '.',\n            precision: 2,\n            prefix: '$ ',\n            suffix: '',\n            thousands: ',',\n            nullable: true,\n          },\n        },\n      ],\n    }).compileComponents();\n\n    fixture = TestBed.createComponent(CcpDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should initialize form with empty values for new ccp', () => {\n    expect(component.ccpForm.get('expenseObjectUseCcp')?.value).toBe('');\n    expect(component.ccpForm.get('expenseObjectDescription')?.value).toBe('');\n    expect(component.ccpForm.get('value')?.value).toBeNull();\n    expect(contractValuesService.getAllByContractId).toHaveBeenCalledWith(\n      mockDialogData.contractId,\n    );\n  });\n\n  it('should initialize form with existing ccp values', () => {\n    TestBed.resetTestingModule();\n    TestBed.configureTestingModule({\n      imports: [\n        CcpDialogComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n        ReactiveFormsModule,\n        MatDialogModule,\n        MatButtonModule,\n        MatFormFieldModule,\n        MatInputModule,\n        MatIconModule,\n        NgxCurrencyDirective,\n      ],\n      providers: [\n        FormBuilder,\n        { provide: MatDialogRef, useValue: dialogRef },\n        {\n          provide: MAT_DIALOG_DATA,\n          useValue: { ...mockDialogData, ccp: mockCcp },\n        },\n        { provide: CcpService, useValue: ccpService },\n        { provide: ContractValuesService, useValue: contractValuesService },\n        { provide: AlertService, useValue: alertService },\n        {\n          provide: 'NGX_CURRENCY_CONFIG',\n          useValue: {\n            align: 'right',\n            allowNegative: false,\n            allowZero: true,\n            decimal: '.',\n            precision: 2,\n            prefix: '$ ',\n            suffix: '',\n            thousands: ',',\n            nullable: true,\n          },\n        },\n      ],\n    }).compileComponents();\n\n    fixture = TestBed.createComponent(CcpDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n\n    expect(component.ccpForm.get('expenseObjectUseCcp')?.value).toBe(\n      mockCcp.expenseObjectUseCcp,\n    );\n    expect(component.ccpForm.get('expenseObjectDescription')?.value).toBe(\n      mockCcp.expenseObjectDescription,\n    );\n    expect(component.ccpForm.get('value')?.value).toBe(mockCcp.value);\n    expect(component.ccpForm.get('additionalValue')?.value).toBe(\n      mockCcp.additionalValue,\n    );\n    expect(contractValuesService.getAllByContractId).toHaveBeenCalledWith(\n      mockDialogData.contractId,\n    );\n  });\n\n  it('should validate required fields', () => {\n    component.ccpForm.controls['expenseObjectUseCcp'].setValue('');\n    component.ccpForm.controls['expenseObjectDescription'].setValue('');\n    component.ccpForm.controls['value'].setValue(null);\n\n    expect(component.ccpForm.valid).toBeFalse();\n    expect(\n      component.ccpForm.get('expenseObjectUseCcp')?.errors?.['required'],\n    ).toBeTruthy();\n    expect(\n      component.ccpForm.get('expenseObjectDescription')?.errors?.['required'],\n    ).toBeTruthy();\n    expect(component.ccpForm.get('value')?.errors?.['required']).toBeTruthy();\n  });\n\n  it('should validate minimum value constraints', () => {\n    component.ccpForm.controls['value'].setValue(-1);\n    expect(component.ccpForm.get('value')?.errors?.['min']).toBeTruthy();\n\n    component.ccpForm.controls['additionalValue'].enable();\n    component.ccpForm.controls['additionalValue'].setValue(-1);\n    expect(\n      component.ccpForm.get('additionalValue')?.errors?.['min'],\n    ).toBeTruthy();\n  });\n\n  it('should close dialog without changes', () => {\n    component.onCancel();\n    expect(dialogRef.close).toHaveBeenCalled();\n  });\n\n  it('should create new ccp successfully', () => {\n    const newCcp = {\n      expenseObjectUseCcp: 'New Object',\n      expenseObjectDescription: 'New Description',\n      value: 2000,\n      contractId: 1,\n      additionalValue: 0,\n    };\n\n    component.ccpForm.patchValue({\n      expenseObjectUseCcp: 'New Object',\n      expenseObjectDescription: 'New Description',\n      value: 2000,\n    });\n\n    ccpService.create.and.returnValue(of({ ...newCcp, id: 2 }));\n\n    component.onSubmit();\n\n    expect(ccpService.create).toHaveBeenCalledWith(newCcp);\n    expect(alertService.success).toHaveBeenCalledWith(\n      'CCP creado exitosamente',\n    );\n    expect(dialogRef.close).toHaveBeenCalledWith({ ...newCcp, id: 2 });\n  });\n\n  it('should handle error when creating ccp', () => {\n    const newCcp = {\n      expenseObjectUseCcp: 'New Object',\n      expenseObjectDescription: 'New Description',\n      value: 2000,\n      contractId: 1,\n      additionalValue: 0,\n    };\n\n    component.ccpForm.patchValue({\n      expenseObjectUseCcp: 'New Object',\n      expenseObjectDescription: 'New Description',\n      value: 2000,\n    });\n\n    ccpService.create.and.returnValue(throwError(() => ({ error: 'Error' })));\n\n    component.onSubmit();\n\n    expect(ccpService.create).toHaveBeenCalledWith(newCcp);\n    expect(alertService.error).toHaveBeenCalledWith('Error al crear el CCP');\n    expect(dialogRef.close).not.toHaveBeenCalled();\n  });\n\n  it('should update existing ccp successfully', () => {\n    TestBed.resetTestingModule();\n\n    dialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);\n    ccpService = jasmine.createSpyObj('CcpService', ['create', 'update']);\n    contractValuesService = jasmine.createSpyObj('ContractValuesService', [\n      'getAllByContractId',\n    ]);\n    alertService = jasmine.createSpyObj('AlertService', ['success', 'error']);\n\n    contractValuesService.getAllByContractId.and.returnValue(\n      of(mockContractValues),\n    );\n\n    TestBed.configureTestingModule({\n      imports: [\n        CcpDialogComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n        ReactiveFormsModule,\n        MatDialogModule,\n        MatButtonModule,\n        MatFormFieldModule,\n        MatInputModule,\n        MatIconModule,\n        NgxCurrencyDirective,\n      ],\n      providers: [\n        FormBuilder,\n        { provide: MatDialogRef, useValue: dialogRef },\n        {\n          provide: MAT_DIALOG_DATA,\n          useValue: { ...mockDialogData, ccp: mockCcp },\n        },\n        { provide: CcpService, useValue: ccpService },\n        { provide: ContractValuesService, useValue: contractValuesService },\n        { provide: AlertService, useValue: alertService },\n        {\n          provide: 'NGX_CURRENCY_CONFIG',\n          useValue: {\n            align: 'right',\n            allowNegative: false,\n            allowZero: true,\n            decimal: '.',\n            precision: 2,\n            prefix: '$ ',\n            suffix: '',\n            thousands: ',',\n            nullable: true,\n          },\n        },\n      ],\n    }).compileComponents();\n\n    fixture = TestBed.createComponent(CcpDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n\n    component.ccpForm.setValue({\n      expenseObjectUseCcp: 'Updated Object',\n      expenseObjectDescription: 'Updated Description',\n      value: 1000,\n      additionalValue: 3000,\n    });\n\n    spyOn(component.ccpForm, 'get').and.returnValue({\n      value: 'test value',\n      valid: true,\n      disabled: false,\n      setValue: jasmine.createSpy('setValue'),\n      patchValue: jasmine.createSpy('patchValue'),\n      reset: jasmine.createSpy('reset'),\n      updateValueAndValidity: jasmine.createSpy('updateValueAndValidity'),\n    } as unknown as AbstractControl);\n\n    const updatedCcp = {\n      id: 1,\n      expenseObjectUseCcp: 'Updated Object',\n      expenseObjectDescription: 'Updated Description',\n      value: 1000,\n      additionalValue: 3000,\n      contractId: 1,\n    };\n    ccpService.update.and.returnValue(of(updatedCcp));\n\n    Object.defineProperty(component.ccpForm, 'valid', { get: () => true });\n\n    component.onSubmit();\n\n    expect(ccpService.update).toHaveBeenCalled();\n    expect(alertService.success).toHaveBeenCalledWith(\n      'CCP editado exitosamente',\n    );\n    expect(dialogRef.close).toHaveBeenCalled();\n  });\n\n  it('should handle error when updating ccp', () => {\n    TestBed.resetTestingModule();\n\n    dialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);\n    ccpService = jasmine.createSpyObj('CcpService', ['create', 'update']);\n    contractValuesService = jasmine.createSpyObj('ContractValuesService', [\n      'getAllByContractId',\n    ]);\n    alertService = jasmine.createSpyObj('AlertService', ['success', 'error']);\n\n    contractValuesService.getAllByContractId.and.returnValue(\n      of(mockContractValues),\n    );\n\n    TestBed.configureTestingModule({\n      imports: [\n        CcpDialogComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n        ReactiveFormsModule,\n        MatDialogModule,\n        MatButtonModule,\n        MatFormFieldModule,\n        MatInputModule,\n        MatIconModule,\n        NgxCurrencyDirective,\n      ],\n      providers: [\n        FormBuilder,\n        { provide: MatDialogRef, useValue: dialogRef },\n        {\n          provide: MAT_DIALOG_DATA,\n          useValue: { ...mockDialogData, ccp: mockCcp },\n        },\n        { provide: CcpService, useValue: ccpService },\n        { provide: ContractValuesService, useValue: contractValuesService },\n        { provide: AlertService, useValue: alertService },\n        {\n          provide: 'NGX_CURRENCY_CONFIG',\n          useValue: {\n            align: 'right',\n            allowNegative: false,\n            allowZero: true,\n            decimal: '.',\n            precision: 2,\n            prefix: '$ ',\n            suffix: '',\n            thousands: ',',\n            nullable: true,\n          },\n        },\n      ],\n    }).compileComponents();\n\n    fixture = TestBed.createComponent(CcpDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n\n    component.ccpForm.setValue({\n      expenseObjectUseCcp: 'Updated Object',\n      expenseObjectDescription: 'Updated Description',\n      value: 1000,\n      additionalValue: 3000,\n    });\n\n    Object.defineProperty(component.ccpForm, 'valid', { get: () => true });\n\n    ccpService.update.and.returnValue(throwError(() => ({ error: 'Error' })));\n\n    component.onSubmit();\n\n    expect(ccpService.update).toHaveBeenCalled();\n    expect(alertService.error).toHaveBeenCalledWith('Error al editar el CCP');\n    expect(dialogRef.close).not.toHaveBeenCalled();\n  });\n\n  it('should handle error when loading contract values', () => {\n    TestBed.resetTestingModule();\n\n    contractValuesService = jasmine.createSpyObj('ContractValuesService', [\n      'getAllByContractId',\n    ]);\n    contractValuesService.getAllByContractId.and.returnValue(\n      throwError(() => ({ error: 'Error' })),\n    );\n\n    TestBed.configureTestingModule({\n      imports: [\n        CcpDialogComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n        ReactiveFormsModule,\n        MatDialogModule,\n        MatButtonModule,\n        MatFormFieldModule,\n        MatInputModule,\n        MatIconModule,\n        NgxCurrencyDirective,\n      ],\n      providers: [\n        FormBuilder,\n        { provide: MatDialogRef, useValue: dialogRef },\n        { provide: MAT_DIALOG_DATA, useValue: mockDialogData },\n        { provide: CcpService, useValue: ccpService },\n        { provide: ContractValuesService, useValue: contractValuesService },\n        { provide: AlertService, useValue: alertService },\n        {\n          provide: 'NGX_CURRENCY_CONFIG',\n          useValue: {\n            align: 'right',\n            allowNegative: false,\n            allowZero: true,\n            decimal: '.',\n            precision: 2,\n            prefix: '$ ',\n            suffix: '',\n            thousands: ',',\n            nullable: true,\n          },\n        },\n      ],\n    }).compileComponents();\n\n    fixture = TestBed.createComponent(CcpDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al cargar los valores del contrato',\n    );\n  });\n\n  it('should validate maxValueExceeded for new CCP', () => {\n\n    component.remainingValue = 1000;\n\n    component.ccpForm.get('value')?.setValue(1500);\n    fixture.detectChanges();\n\n    expect(\n      component.ccpForm.get('value')?.errors?.['maxValueExceeded'],\n    ).toBeTruthy();\n\n    component.ccpForm.get('value')?.setValue(800);\n    fixture.detectChanges();\n\n    expect(\n      component.ccpForm.get('value')?.errors?.['maxValueExceeded'],\n    ).toBeFalsy();\n  });\n\n  it('should validate additionalValue for existing CCP - less than previous value', () => {\n    TestBed.resetTestingModule();\n\n    const existingCcp = {\n      ...mockCcp,\n      additionalValue: 1000,\n    };\n\n    TestBed.configureTestingModule({\n      imports: [\n        CcpDialogComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n        ReactiveFormsModule,\n        MatDialogModule,\n        MatButtonModule,\n        MatFormFieldModule,\n        MatInputModule,\n        MatIconModule,\n        NgxCurrencyDirective,\n      ],\n      providers: [\n        FormBuilder,\n        { provide: MatDialogRef, useValue: dialogRef },\n        {\n          provide: MAT_DIALOG_DATA,\n          useValue: { ...mockDialogData, ccp: existingCcp },\n        },\n        { provide: CcpService, useValue: ccpService },\n        { provide: ContractValuesService, useValue: contractValuesService },\n        { provide: AlertService, useValue: alertService },\n        {\n          provide: 'NGX_CURRENCY_CONFIG',\n          useValue: {\n            align: 'right',\n            allowNegative: false,\n            allowZero: true,\n            decimal: '.',\n            precision: 2,\n            prefix: '$ ',\n            suffix: '',\n            thousands: ',',\n            nullable: true,\n          },\n        },\n      ],\n    }).compileComponents();\n\n    fixture = TestBed.createComponent(CcpDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n\n    component.ccpForm.get('additionalValue')?.setValue(500);\n    fixture.detectChanges();\n\n    expect(\n      component.ccpForm.get('additionalValue')?.errors?.['lessThanPrevious'],\n    ).toBeTruthy();\n  });\n\n  it('should validate additionalValue for existing CCP - exceeds available value', () => {\n    TestBed.resetTestingModule();\n\n    const existingCcp = {\n      ...mockCcp,\n      additionalValue: 1000,\n    };\n\n    TestBed.configureTestingModule({\n      imports: [\n        CcpDialogComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n        ReactiveFormsModule,\n        MatDialogModule,\n        MatButtonModule,\n        MatFormFieldModule,\n        MatInputModule,\n        MatIconModule,\n        NgxCurrencyDirective,\n      ],\n      providers: [\n        FormBuilder,\n        { provide: MatDialogRef, useValue: dialogRef },\n        {\n          provide: MAT_DIALOG_DATA,\n          useValue: { ...mockDialogData, ccp: existingCcp },\n        },\n        { provide: CcpService, useValue: ccpService },\n        { provide: ContractValuesService, useValue: contractValuesService },\n        { provide: AlertService, useValue: alertService },\n        {\n          provide: 'NGX_CURRENCY_CONFIG',\n          useValue: {\n            align: 'right',\n            allowNegative: false,\n            allowZero: true,\n            decimal: '.',\n            precision: 2,\n            prefix: '$ ',\n            suffix: '',\n            thousands: ',',\n            nullable: true,\n          },\n        },\n      ],\n    }).compileComponents();\n\n    fixture = TestBed.createComponent(CcpDialogComponent);\n    component = fixture.componentInstance;\n\n    component.totalContractValue = 5000;\n    component.currentTotalCcpValue = 4000;\n    component.remainingValue = 2000;\n\n    fixture.detectChanges();\n\n    component.ccpForm.get('additionalValue')?.setValue(4000);\n    fixture.detectChanges();\n\n    expect(\n      component.ccpForm.get('additionalValue')?.errors?.['maxValueExceeded'],\n    ).toBeTruthy();\n\n    component.ccpForm.get('additionalValue')?.setValue(1500);\n    fixture.detectChanges();\n\n    expect(\n      component.ccpForm.get('additionalValue')?.errors?.['maxValueExceeded'],\n    ).toBeFalsy();\n  });\n\n  it('should not submit form when it is invalid', () => {\n\n    component.ccpForm.controls['expenseObjectUseCcp'].setValue('');\n    component.ccpForm.controls['expenseObjectDescription'].setValue('');\n    component.ccpForm.controls['value'].setValue(null);\n\n    component.onSubmit();\n\n    expect(ccpService.create).not.toHaveBeenCalled();\n    expect(dialogRef.close).not.toHaveBeenCalled();\n  });\n\n  it('should subscribe to value changes for new CCPs', () => {\n\n    TestBed.resetTestingModule();\n\n    dialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);\n    ccpService = jasmine.createSpyObj('CcpService', ['create', 'update']);\n    contractValuesService = jasmine.createSpyObj('ContractValuesService', [\n      'getAllByContractId',\n    ]);\n    alertService = jasmine.createSpyObj('AlertService', ['success', 'error']);\n\n    contractValuesService.getAllByContractId.and.returnValue(\n      of(mockContractValues),\n    );\n\n    TestBed.configureTestingModule({\n      imports: [\n        CcpDialogComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n        ReactiveFormsModule,\n        MatDialogModule,\n        MatButtonModule,\n        MatFormFieldModule,\n        MatInputModule,\n        MatIconModule,\n        NgxCurrencyDirective,\n      ],\n      providers: [\n        FormBuilder,\n        { provide: MatDialogRef, useValue: dialogRef },\n        { provide: MAT_DIALOG_DATA, useValue: mockDialogData },\n        { provide: CcpService, useValue: ccpService },\n        { provide: ContractValuesService, useValue: contractValuesService },\n        { provide: AlertService, useValue: alertService },\n        {\n          provide: 'NGX_CURRENCY_CONFIG',\n          useValue: {\n            align: 'right',\n            allowNegative: false,\n            allowZero: true,\n            decimal: '.',\n            precision: 2,\n            prefix: '$ ',\n            suffix: '',\n            thousands: ',',\n            nullable: true,\n          },\n        },\n      ],\n    }).compileComponents();\n\n    fixture = TestBed.createComponent(CcpDialogComponent);\n    component = fixture.componentInstance;\n\n    component.remainingValue = 3000;\n\n    const valueControl = component.ccpForm.get('value');\n    spyOn(valueControl!, 'setErrors');\n\n    fixture.detectChanges();\n\n    valueControl!.setValue(4000);\n\n    expect(valueControl!.setErrors).toHaveBeenCalledWith({\n      maxValueExceeded: true,\n    });\n\n    valueControl!.setValue(2000);\n\n    expect(valueControl!.setErrors).toHaveBeenCalledTimes(1);\n  });\n\n  it('should calculate contract values properly', () => {\n\n    contractValuesService.getAllByContractId.and.returnValue(\n      of([\n        {\n          id: 1,\n          numericValue: 5000,\n          madsValue: 0,\n          isOtherEntity: false,\n          subscriptionDate: '2024-01-01',\n          cdp: 123,\n          cdpEntityId: 1,\n          cdpEntity: { id: 1, name: 'Test Entity' },\n        },\n        {\n          id: 2,\n          numericValue: 3000,\n          madsValue: 0,\n          isOtherEntity: false,\n          subscriptionDate: '2024-01-01',\n          cdp: 456,\n          cdpEntityId: 1,\n          cdpEntity: { id: 1, name: 'Test Entity' },\n        },\n      ]),\n    );\n\n    fixture = TestBed.createComponent(CcpDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n\n    expect(component.totalContractValue).toBe(8000);\n    expect(component.remainingValue).toBe(5000);\n  });\n});"], "mappings": ";AAAA,SAASA,uBAAuB,QAAQ,8BAA8B;AACtE,SAA2BC,OAAO,QAAQ,uBAAuB;AACjE,SAEEC,WAAW,EACXC,mBAAmB,QACd,gBAAgB;AACvB,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SACEC,eAAe,EACfC,eAAe,EACfC,YAAY,QACP,0BAA0B;AACjC,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,uBAAuB,QAAQ,sCAAsC;AAE9E,SAASC,UAAU,QAAQ,2CAA2C;AACtE,SAASC,qBAAqB,QAAQ,uDAAuD;AAC7F,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,oBAAoB,QAAQ,cAAc;AACnD,SAASC,EAAE,EAAEC,UAAU,QAAQ,MAAM;AACrC,SAASC,kBAAkB,QAAQ,wBAAwB;AAE3DC,QAAQ,CAAC,oBAAoB,EAAE,MAAK;EAClC,IAAIC,SAA6B;EACjC,IAAIC,OAA6C;EACjD,IAAIC,SAA2D;EAC/D,IAAIC,UAAsC;EAC1C,IAAIC,qBAA4D;EAChE,IAAIC,YAA0C;EAE9C,MAAMC,OAAO,GAAQ;IACnBC,EAAE,EAAE,CAAC;IACLC,mBAAmB,EAAE,aAAa;IAClCC,wBAAwB,EAAE,kBAAkB;IAC5CC,KAAK,EAAE,IAAI;IACXC,eAAe,EAAE,GAAG;IACpBC,UAAU,EAAE;GACb;EAED,MAAMC,cAAc,GAAG;IACrBD,UAAU,EAAE,CAAC;IACbE,aAAa,EAAE;GAChB;EAED,MAAMC,kBAAkB,GAAG,CACzB;IACER,EAAE,EAAE,CAAC;IACLS,YAAY,EAAE,IAAI;IAClBC,SAAS,EAAE,CAAC;IACZC,aAAa,EAAE,KAAK;IACpBC,gBAAgB,EAAE,YAAY;IAC9BC,GAAG,EAAE,GAAG;IACRC,WAAW,EAAE,CAAC;IACdC,SAAS,EAAE;MAAEf,EAAE,EAAE,CAAC;MAAEgB,IAAI,EAAE;IAAa;GACxC,CACF;EAEDC,UAAU,eAAAC,iBAAA,CAAC,aAAW;IACpBvB,SAAS,GAAGwB,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC;IAC3DxB,UAAU,GAAGuB,OAAO,CAACC,YAAY,CAAC,YAAY,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACrEvB,qBAAqB,GAAGsB,OAAO,CAACC,YAAY,CAAC,uBAAuB,EAAE,CACpE,oBAAoB,CACrB,CAAC;IACFtB,YAAY,GAAGqB,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAEzEvB,qBAAqB,CAACwB,kBAAkB,CAACC,GAAG,CAACC,WAAW,CACtDlC,EAAE,CAACmB,kBAAkB,CAAC,CACvB;IAED,MAAMlC,OAAO,CAACkD,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CACPlC,kBAAkB,EAClBlB,uBAAuB,EACvBW,uBAAuB,EACvBR,mBAAmB,EACnBG,eAAe,EACfF,eAAe,EACfI,kBAAkB,EAClBE,cAAc,EACdD,aAAa,EACbM,oBAAoB,CACrB;MACDsC,SAAS,EAAE,CACTnD,WAAW,EACX;QAAEoD,OAAO,EAAE/C,YAAY;QAAEgD,QAAQ,EAAEjC;MAAS,CAAE,EAC9C;QAAEgC,OAAO,EAAEjD,eAAe;QAAEkD,QAAQ,EAAEtB;MAAc,CAAE,EACtD;QAAEqB,OAAO,EAAE1C,UAAU;QAAE2C,QAAQ,EAAEhC;MAAU,CAAE,EAC7C;QAAE+B,OAAO,EAAEzC,qBAAqB;QAAE0C,QAAQ,EAAE/B;MAAqB,CAAE,EACnE;QAAE8B,OAAO,EAAExC,YAAY;QAAEyC,QAAQ,EAAE9B;MAAY,CAAE,EACjD;QACE6B,OAAO,EAAE,qBAAqB;QAC9BC,QAAQ,EAAE;UACRC,KAAK,EAAE,OAAO;UACdC,aAAa,EAAE,KAAK;UACpBC,SAAS,EAAE,IAAI;UACfC,OAAO,EAAE,GAAG;UACZC,SAAS,EAAE,CAAC;UACZC,MAAM,EAAE,IAAI;UACZC,MAAM,EAAE,EAAE;UACVC,SAAS,EAAE,GAAG;UACdC,QAAQ,EAAE;;OAEb;KAEJ,CAAC,CAACC,iBAAiB,EAAE;IAEtB5C,OAAO,GAAGpB,OAAO,CAACiE,eAAe,CAAChD,kBAAkB,CAAC;IACrDE,SAAS,GAAGC,OAAO,CAAC8C,iBAAiB;IACrC9C,OAAO,CAAC+C,aAAa,EAAE;EACzB,CAAC,EAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAAClD,SAAS,CAAC,CAACmD,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,sDAAsD,EAAE,MAAK;IAC9DC,MAAM,CAAClD,SAAS,CAACoD,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC,EAAE3C,KAAK,CAAC,CAAC4C,IAAI,CAAC,EAAE,CAAC;IACpEJ,MAAM,CAAClD,SAAS,CAACoD,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC,EAAE3C,KAAK,CAAC,CAAC4C,IAAI,CAAC,EAAE,CAAC;IACzEJ,MAAM,CAAClD,SAAS,CAACoD,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC,EAAE3C,KAAK,CAAC,CAAC6C,QAAQ,EAAE;IACxDL,MAAM,CAAC9C,qBAAqB,CAACwB,kBAAkB,CAAC,CAAC4B,oBAAoB,CACnE3C,cAAc,CAACD,UAAU,CAC1B;EACH,CAAC,CAAC;EAEFqC,EAAE,CAAC,iDAAiD,EAAE,MAAK;IACzDpE,OAAO,CAAC4E,kBAAkB,EAAE;IAC5B5E,OAAO,CAACkD,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPlC,kBAAkB,EAClBlB,uBAAuB,EACvBW,uBAAuB,EACvBR,mBAAmB,EACnBG,eAAe,EACfF,eAAe,EACfI,kBAAkB,EAClBE,cAAc,EACdD,aAAa,EACbM,oBAAoB,CACrB;MACDsC,SAAS,EAAE,CACTnD,WAAW,EACX;QAAEoD,OAAO,EAAE/C,YAAY;QAAEgD,QAAQ,EAAEjC;MAAS,CAAE,EAC9C;QACEgC,OAAO,EAAEjD,eAAe;QACxBkD,QAAQ,EAAE;UAAE,GAAGtB,cAAc;UAAE6C,GAAG,EAAEpD;QAAO;OAC5C,EACD;QAAE4B,OAAO,EAAE1C,UAAU;QAAE2C,QAAQ,EAAEhC;MAAU,CAAE,EAC7C;QAAE+B,OAAO,EAAEzC,qBAAqB;QAAE0C,QAAQ,EAAE/B;MAAqB,CAAE,EACnE;QAAE8B,OAAO,EAAExC,YAAY;QAAEyC,QAAQ,EAAE9B;MAAY,CAAE,EACjD;QACE6B,OAAO,EAAE,qBAAqB;QAC9BC,QAAQ,EAAE;UACRC,KAAK,EAAE,OAAO;UACdC,aAAa,EAAE,KAAK;UACpBC,SAAS,EAAE,IAAI;UACfC,OAAO,EAAE,GAAG;UACZC,SAAS,EAAE,CAAC;UACZC,MAAM,EAAE,IAAI;UACZC,MAAM,EAAE,EAAE;UACVC,SAAS,EAAE,GAAG;UACdC,QAAQ,EAAE;;OAEb;KAEJ,CAAC,CAACC,iBAAiB,EAAE;IAEtB5C,OAAO,GAAGpB,OAAO,CAACiE,eAAe,CAAChD,kBAAkB,CAAC;IACrDE,SAAS,GAAGC,OAAO,CAAC8C,iBAAiB;IACrC9C,OAAO,CAAC+C,aAAa,EAAE;IAEvBE,MAAM,CAAClD,SAAS,CAACoD,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC,EAAE3C,KAAK,CAAC,CAAC4C,IAAI,CAC9DhD,OAAO,CAACE,mBAAmB,CAC5B;IACD0C,MAAM,CAAClD,SAAS,CAACoD,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC,EAAE3C,KAAK,CAAC,CAAC4C,IAAI,CACnEhD,OAAO,CAACG,wBAAwB,CACjC;IACDyC,MAAM,CAAClD,SAAS,CAACoD,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC,EAAE3C,KAAK,CAAC,CAAC4C,IAAI,CAAChD,OAAO,CAACI,KAAK,CAAC;IACjEwC,MAAM,CAAClD,SAAS,CAACoD,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC,EAAE3C,KAAK,CAAC,CAAC4C,IAAI,CAC1DhD,OAAO,CAACK,eAAe,CACxB;IACDuC,MAAM,CAAC9C,qBAAqB,CAACwB,kBAAkB,CAAC,CAAC4B,oBAAoB,CACnE3C,cAAc,CAACD,UAAU,CAC1B;EACH,CAAC,CAAC;EAEFqC,EAAE,CAAC,iCAAiC,EAAE,MAAK;IACzCjD,SAAS,CAACoD,OAAO,CAACO,QAAQ,CAAC,qBAAqB,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;IAC9D5D,SAAS,CAACoD,OAAO,CAACO,QAAQ,CAAC,0BAA0B,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;IACnE5D,SAAS,CAACoD,OAAO,CAACO,QAAQ,CAAC,OAAO,CAAC,CAACC,QAAQ,CAAC,IAAI,CAAC;IAElDV,MAAM,CAAClD,SAAS,CAACoD,OAAO,CAACS,KAAK,CAAC,CAACC,SAAS,EAAE;IAC3CZ,MAAM,CACJlD,SAAS,CAACoD,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC,EAAEU,MAAM,GAAG,UAAU,CAAC,CACnE,CAACZ,UAAU,EAAE;IACdD,MAAM,CACJlD,SAAS,CAACoD,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC,EAAEU,MAAM,GAAG,UAAU,CAAC,CACxE,CAACZ,UAAU,EAAE;IACdD,MAAM,CAAClD,SAAS,CAACoD,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC,EAAEU,MAAM,GAAG,UAAU,CAAC,CAAC,CAACZ,UAAU,EAAE;EAC3E,CAAC,CAAC;EAEFF,EAAE,CAAC,2CAA2C,EAAE,MAAK;IACnDjD,SAAS,CAACoD,OAAO,CAACO,QAAQ,CAAC,OAAO,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAChDV,MAAM,CAAClD,SAAS,CAACoD,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC,EAAEU,MAAM,GAAG,KAAK,CAAC,CAAC,CAACZ,UAAU,EAAE;IAEpEnD,SAAS,CAACoD,OAAO,CAACO,QAAQ,CAAC,iBAAiB,CAAC,CAACK,MAAM,EAAE;IACtDhE,SAAS,CAACoD,OAAO,CAACO,QAAQ,CAAC,iBAAiB,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC1DV,MAAM,CACJlD,SAAS,CAACoD,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC,EAAEU,MAAM,GAAG,KAAK,CAAC,CAC1D,CAACZ,UAAU,EAAE;EAChB,CAAC,CAAC;EAEFF,EAAE,CAAC,qCAAqC,EAAE,MAAK;IAC7CjD,SAAS,CAACiE,QAAQ,EAAE;IACpBf,MAAM,CAAChD,SAAS,CAACgE,KAAK,CAAC,CAACC,gBAAgB,EAAE;EAC5C,CAAC,CAAC;EAEFlB,EAAE,CAAC,oCAAoC,EAAE,MAAK;IAC5C,MAAMmB,MAAM,GAAG;MACb5D,mBAAmB,EAAE,YAAY;MACjCC,wBAAwB,EAAE,iBAAiB;MAC3CC,KAAK,EAAE,IAAI;MACXE,UAAU,EAAE,CAAC;MACbD,eAAe,EAAE;KAClB;IAEDX,SAAS,CAACoD,OAAO,CAACiB,UAAU,CAAC;MAC3B7D,mBAAmB,EAAE,YAAY;MACjCC,wBAAwB,EAAE,iBAAiB;MAC3CC,KAAK,EAAE;KACR,CAAC;IAEFP,UAAU,CAACmE,MAAM,CAACzC,GAAG,CAACC,WAAW,CAAClC,EAAE,CAAC;MAAE,GAAGwE,MAAM;MAAE7D,EAAE,EAAE;IAAC,CAAE,CAAC,CAAC;IAE3DP,SAAS,CAACuE,QAAQ,EAAE;IAEpBrB,MAAM,CAAC/C,UAAU,CAACmE,MAAM,CAAC,CAACd,oBAAoB,CAACY,MAAM,CAAC;IACtDlB,MAAM,CAAC7C,YAAY,CAACmE,OAAO,CAAC,CAAChB,oBAAoB,CAC/C,yBAAyB,CAC1B;IACDN,MAAM,CAAChD,SAAS,CAACgE,KAAK,CAAC,CAACV,oBAAoB,CAAC;MAAE,GAAGY,MAAM;MAAE7D,EAAE,EAAE;IAAC,CAAE,CAAC;EACpE,CAAC,CAAC;EAEF0C,EAAE,CAAC,uCAAuC,EAAE,MAAK;IAC/C,MAAMmB,MAAM,GAAG;MACb5D,mBAAmB,EAAE,YAAY;MACjCC,wBAAwB,EAAE,iBAAiB;MAC3CC,KAAK,EAAE,IAAI;MACXE,UAAU,EAAE,CAAC;MACbD,eAAe,EAAE;KAClB;IAEDX,SAAS,CAACoD,OAAO,CAACiB,UAAU,CAAC;MAC3B7D,mBAAmB,EAAE,YAAY;MACjCC,wBAAwB,EAAE,iBAAiB;MAC3CC,KAAK,EAAE;KACR,CAAC;IAEFP,UAAU,CAACmE,MAAM,CAACzC,GAAG,CAACC,WAAW,CAACjC,UAAU,CAAC,OAAO;MAAE4E,KAAK,EAAE;IAAO,CAAE,CAAC,CAAC,CAAC;IAEzEzE,SAAS,CAACuE,QAAQ,EAAE;IAEpBrB,MAAM,CAAC/C,UAAU,CAACmE,MAAM,CAAC,CAACd,oBAAoB,CAACY,MAAM,CAAC;IACtDlB,MAAM,CAAC7C,YAAY,CAACoE,KAAK,CAAC,CAACjB,oBAAoB,CAAC,uBAAuB,CAAC;IACxEN,MAAM,CAAChD,SAAS,CAACgE,KAAK,CAAC,CAACQ,GAAG,CAACP,gBAAgB,EAAE;EAChD,CAAC,CAAC;EAEFlB,EAAE,CAAC,yCAAyC,EAAE,MAAK;IACjDpE,OAAO,CAAC4E,kBAAkB,EAAE;IAE5BvD,SAAS,GAAGwB,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC;IAC3DxB,UAAU,GAAGuB,OAAO,CAACC,YAAY,CAAC,YAAY,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACrEvB,qBAAqB,GAAGsB,OAAO,CAACC,YAAY,CAAC,uBAAuB,EAAE,CACpE,oBAAoB,CACrB,CAAC;IACFtB,YAAY,GAAGqB,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAEzEvB,qBAAqB,CAACwB,kBAAkB,CAACC,GAAG,CAACC,WAAW,CACtDlC,EAAE,CAACmB,kBAAkB,CAAC,CACvB;IAEDlC,OAAO,CAACkD,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPlC,kBAAkB,EAClBlB,uBAAuB,EACvBW,uBAAuB,EACvBR,mBAAmB,EACnBG,eAAe,EACfF,eAAe,EACfI,kBAAkB,EAClBE,cAAc,EACdD,aAAa,EACbM,oBAAoB,CACrB;MACDsC,SAAS,EAAE,CACTnD,WAAW,EACX;QAAEoD,OAAO,EAAE/C,YAAY;QAAEgD,QAAQ,EAAEjC;MAAS,CAAE,EAC9C;QACEgC,OAAO,EAAEjD,eAAe;QACxBkD,QAAQ,EAAE;UAAE,GAAGtB,cAAc;UAAE6C,GAAG,EAAEpD;QAAO;OAC5C,EACD;QAAE4B,OAAO,EAAE1C,UAAU;QAAE2C,QAAQ,EAAEhC;MAAU,CAAE,EAC7C;QAAE+B,OAAO,EAAEzC,qBAAqB;QAAE0C,QAAQ,EAAE/B;MAAqB,CAAE,EACnE;QAAE8B,OAAO,EAAExC,YAAY;QAAEyC,QAAQ,EAAE9B;MAAY,CAAE,EACjD;QACE6B,OAAO,EAAE,qBAAqB;QAC9BC,QAAQ,EAAE;UACRC,KAAK,EAAE,OAAO;UACdC,aAAa,EAAE,KAAK;UACpBC,SAAS,EAAE,IAAI;UACfC,OAAO,EAAE,GAAG;UACZC,SAAS,EAAE,CAAC;UACZC,MAAM,EAAE,IAAI;UACZC,MAAM,EAAE,EAAE;UACVC,SAAS,EAAE,GAAG;UACdC,QAAQ,EAAE;;OAEb;KAEJ,CAAC,CAACC,iBAAiB,EAAE;IAEtB5C,OAAO,GAAGpB,OAAO,CAACiE,eAAe,CAAChD,kBAAkB,CAAC;IACrDE,SAAS,GAAGC,OAAO,CAAC8C,iBAAiB;IACrC9C,OAAO,CAAC+C,aAAa,EAAE;IAEvBhD,SAAS,CAACoD,OAAO,CAACQ,QAAQ,CAAC;MACzBpD,mBAAmB,EAAE,gBAAgB;MACrCC,wBAAwB,EAAE,qBAAqB;MAC/CC,KAAK,EAAE,IAAI;MACXC,eAAe,EAAE;KAClB,CAAC;IAEFgE,KAAK,CAAC3E,SAAS,CAACoD,OAAO,EAAE,KAAK,CAAC,CAACvB,GAAG,CAACC,WAAW,CAAC;MAC9CpB,KAAK,EAAE,YAAY;MACnBmD,KAAK,EAAE,IAAI;MACXe,QAAQ,EAAE,KAAK;MACfhB,QAAQ,EAAElC,OAAO,CAACmD,SAAS,CAAC,UAAU,CAAC;MACvCR,UAAU,EAAE3C,OAAO,CAACmD,SAAS,CAAC,YAAY,CAAC;MAC3CC,KAAK,EAAEpD,OAAO,CAACmD,SAAS,CAAC,OAAO,CAAC;MACjCE,sBAAsB,EAAErD,OAAO,CAACmD,SAAS,CAAC,wBAAwB;KACrC,CAAC;IAEhC,MAAMG,UAAU,GAAG;MACjBzE,EAAE,EAAE,CAAC;MACLC,mBAAmB,EAAE,gBAAgB;MACrCC,wBAAwB,EAAE,qBAAqB;MAC/CC,KAAK,EAAE,IAAI;MACXC,eAAe,EAAE,IAAI;MACrBC,UAAU,EAAE;KACb;IACDT,UAAU,CAAC8E,MAAM,CAACpD,GAAG,CAACC,WAAW,CAAClC,EAAE,CAACoF,UAAU,CAAC,CAAC;IAEjDE,MAAM,CAACC,cAAc,CAACnF,SAAS,CAACoD,OAAO,EAAE,OAAO,EAAE;MAAEC,GAAG,EAAEA,CAAA,KAAM;IAAI,CAAE,CAAC;IAEtErD,SAAS,CAACuE,QAAQ,EAAE;IAEpBrB,MAAM,CAAC/C,UAAU,CAAC8E,MAAM,CAAC,CAACd,gBAAgB,EAAE;IAC5CjB,MAAM,CAAC7C,YAAY,CAACmE,OAAO,CAAC,CAAChB,oBAAoB,CAC/C,0BAA0B,CAC3B;IACDN,MAAM,CAAChD,SAAS,CAACgE,KAAK,CAAC,CAACC,gBAAgB,EAAE;EAC5C,CAAC,CAAC;EAEFlB,EAAE,CAAC,uCAAuC,EAAE,MAAK;IAC/CpE,OAAO,CAAC4E,kBAAkB,EAAE;IAE5BvD,SAAS,GAAGwB,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC;IAC3DxB,UAAU,GAAGuB,OAAO,CAACC,YAAY,CAAC,YAAY,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACrEvB,qBAAqB,GAAGsB,OAAO,CAACC,YAAY,CAAC,uBAAuB,EAAE,CACpE,oBAAoB,CACrB,CAAC;IACFtB,YAAY,GAAGqB,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAEzEvB,qBAAqB,CAACwB,kBAAkB,CAACC,GAAG,CAACC,WAAW,CACtDlC,EAAE,CAACmB,kBAAkB,CAAC,CACvB;IAEDlC,OAAO,CAACkD,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPlC,kBAAkB,EAClBlB,uBAAuB,EACvBW,uBAAuB,EACvBR,mBAAmB,EACnBG,eAAe,EACfF,eAAe,EACfI,kBAAkB,EAClBE,cAAc,EACdD,aAAa,EACbM,oBAAoB,CACrB;MACDsC,SAAS,EAAE,CACTnD,WAAW,EACX;QAAEoD,OAAO,EAAE/C,YAAY;QAAEgD,QAAQ,EAAEjC;MAAS,CAAE,EAC9C;QACEgC,OAAO,EAAEjD,eAAe;QACxBkD,QAAQ,EAAE;UAAE,GAAGtB,cAAc;UAAE6C,GAAG,EAAEpD;QAAO;OAC5C,EACD;QAAE4B,OAAO,EAAE1C,UAAU;QAAE2C,QAAQ,EAAEhC;MAAU,CAAE,EAC7C;QAAE+B,OAAO,EAAEzC,qBAAqB;QAAE0C,QAAQ,EAAE/B;MAAqB,CAAE,EACnE;QAAE8B,OAAO,EAAExC,YAAY;QAAEyC,QAAQ,EAAE9B;MAAY,CAAE,EACjD;QACE6B,OAAO,EAAE,qBAAqB;QAC9BC,QAAQ,EAAE;UACRC,KAAK,EAAE,OAAO;UACdC,aAAa,EAAE,KAAK;UACpBC,SAAS,EAAE,IAAI;UACfC,OAAO,EAAE,GAAG;UACZC,SAAS,EAAE,CAAC;UACZC,MAAM,EAAE,IAAI;UACZC,MAAM,EAAE,EAAE;UACVC,SAAS,EAAE,GAAG;UACdC,QAAQ,EAAE;;OAEb;KAEJ,CAAC,CAACC,iBAAiB,EAAE;IAEtB5C,OAAO,GAAGpB,OAAO,CAACiE,eAAe,CAAChD,kBAAkB,CAAC;IACrDE,SAAS,GAAGC,OAAO,CAAC8C,iBAAiB;IACrC9C,OAAO,CAAC+C,aAAa,EAAE;IAEvBhD,SAAS,CAACoD,OAAO,CAACQ,QAAQ,CAAC;MACzBpD,mBAAmB,EAAE,gBAAgB;MACrCC,wBAAwB,EAAE,qBAAqB;MAC/CC,KAAK,EAAE,IAAI;MACXC,eAAe,EAAE;KAClB,CAAC;IAEFuE,MAAM,CAACC,cAAc,CAACnF,SAAS,CAACoD,OAAO,EAAE,OAAO,EAAE;MAAEC,GAAG,EAAEA,CAAA,KAAM;IAAI,CAAE,CAAC;IAEtElD,UAAU,CAAC8E,MAAM,CAACpD,GAAG,CAACC,WAAW,CAACjC,UAAU,CAAC,OAAO;MAAE4E,KAAK,EAAE;IAAO,CAAE,CAAC,CAAC,CAAC;IAEzEzE,SAAS,CAACuE,QAAQ,EAAE;IAEpBrB,MAAM,CAAC/C,UAAU,CAAC8E,MAAM,CAAC,CAACd,gBAAgB,EAAE;IAC5CjB,MAAM,CAAC7C,YAAY,CAACoE,KAAK,CAAC,CAACjB,oBAAoB,CAAC,wBAAwB,CAAC;IACzEN,MAAM,CAAChD,SAAS,CAACgE,KAAK,CAAC,CAACQ,GAAG,CAACP,gBAAgB,EAAE;EAChD,CAAC,CAAC;EAEFlB,EAAE,CAAC,kDAAkD,EAAE,MAAK;IAC1DpE,OAAO,CAAC4E,kBAAkB,EAAE;IAE5BrD,qBAAqB,GAAGsB,OAAO,CAACC,YAAY,CAAC,uBAAuB,EAAE,CACpE,oBAAoB,CACrB,CAAC;IACFvB,qBAAqB,CAACwB,kBAAkB,CAACC,GAAG,CAACC,WAAW,CACtDjC,UAAU,CAAC,OAAO;MAAE4E,KAAK,EAAE;IAAO,CAAE,CAAC,CAAC,CACvC;IAED5F,OAAO,CAACkD,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPlC,kBAAkB,EAClBlB,uBAAuB,EACvBW,uBAAuB,EACvBR,mBAAmB,EACnBG,eAAe,EACfF,eAAe,EACfI,kBAAkB,EAClBE,cAAc,EACdD,aAAa,EACbM,oBAAoB,CACrB;MACDsC,SAAS,EAAE,CACTnD,WAAW,EACX;QAAEoD,OAAO,EAAE/C,YAAY;QAAEgD,QAAQ,EAAEjC;MAAS,CAAE,EAC9C;QAAEgC,OAAO,EAAEjD,eAAe;QAAEkD,QAAQ,EAAEtB;MAAc,CAAE,EACtD;QAAEqB,OAAO,EAAE1C,UAAU;QAAE2C,QAAQ,EAAEhC;MAAU,CAAE,EAC7C;QAAE+B,OAAO,EAAEzC,qBAAqB;QAAE0C,QAAQ,EAAE/B;MAAqB,CAAE,EACnE;QAAE8B,OAAO,EAAExC,YAAY;QAAEyC,QAAQ,EAAE9B;MAAY,CAAE,EACjD;QACE6B,OAAO,EAAE,qBAAqB;QAC9BC,QAAQ,EAAE;UACRC,KAAK,EAAE,OAAO;UACdC,aAAa,EAAE,KAAK;UACpBC,SAAS,EAAE,IAAI;UACfC,OAAO,EAAE,GAAG;UACZC,SAAS,EAAE,CAAC;UACZC,MAAM,EAAE,IAAI;UACZC,MAAM,EAAE,EAAE;UACVC,SAAS,EAAE,GAAG;UACdC,QAAQ,EAAE;;OAEb;KAEJ,CAAC,CAACC,iBAAiB,EAAE;IAEtB5C,OAAO,GAAGpB,OAAO,CAACiE,eAAe,CAAChD,kBAAkB,CAAC;IACrDE,SAAS,GAAGC,OAAO,CAAC8C,iBAAiB;IACrC9C,OAAO,CAAC+C,aAAa,EAAE;IAEvBE,MAAM,CAAC7C,YAAY,CAACoE,KAAK,CAAC,CAACjB,oBAAoB,CAC7C,0CAA0C,CAC3C;EACH,CAAC,CAAC;EAEFP,EAAE,CAAC,8CAA8C,EAAE,MAAK;IAEtDjD,SAAS,CAACoF,cAAc,GAAG,IAAI;IAE/BpF,SAAS,CAACoD,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC,EAAEO,QAAQ,CAAC,IAAI,CAAC;IAC9C3D,OAAO,CAAC+C,aAAa,EAAE;IAEvBE,MAAM,CACJlD,SAAS,CAACoD,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC,EAAEU,MAAM,GAAG,kBAAkB,CAAC,CAC7D,CAACZ,UAAU,EAAE;IAEdnD,SAAS,CAACoD,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC,EAAEO,QAAQ,CAAC,GAAG,CAAC;IAC7C3D,OAAO,CAAC+C,aAAa,EAAE;IAEvBE,MAAM,CACJlD,SAAS,CAACoD,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC,EAAEU,MAAM,GAAG,kBAAkB,CAAC,CAC7D,CAACsB,SAAS,EAAE;EACf,CAAC,CAAC;EAEFpC,EAAE,CAAC,6EAA6E,EAAE,MAAK;IACrFpE,OAAO,CAAC4E,kBAAkB,EAAE;IAE5B,MAAM6B,WAAW,GAAG;MAClB,GAAGhF,OAAO;MACVK,eAAe,EAAE;KAClB;IAED9B,OAAO,CAACkD,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPlC,kBAAkB,EAClBlB,uBAAuB,EACvBW,uBAAuB,EACvBR,mBAAmB,EACnBG,eAAe,EACfF,eAAe,EACfI,kBAAkB,EAClBE,cAAc,EACdD,aAAa,EACbM,oBAAoB,CACrB;MACDsC,SAAS,EAAE,CACTnD,WAAW,EACX;QAAEoD,OAAO,EAAE/C,YAAY;QAAEgD,QAAQ,EAAEjC;MAAS,CAAE,EAC9C;QACEgC,OAAO,EAAEjD,eAAe;QACxBkD,QAAQ,EAAE;UAAE,GAAGtB,cAAc;UAAE6C,GAAG,EAAE4B;QAAW;OAChD,EACD;QAAEpD,OAAO,EAAE1C,UAAU;QAAE2C,QAAQ,EAAEhC;MAAU,CAAE,EAC7C;QAAE+B,OAAO,EAAEzC,qBAAqB;QAAE0C,QAAQ,EAAE/B;MAAqB,CAAE,EACnE;QAAE8B,OAAO,EAAExC,YAAY;QAAEyC,QAAQ,EAAE9B;MAAY,CAAE,EACjD;QACE6B,OAAO,EAAE,qBAAqB;QAC9BC,QAAQ,EAAE;UACRC,KAAK,EAAE,OAAO;UACdC,aAAa,EAAE,KAAK;UACpBC,SAAS,EAAE,IAAI;UACfC,OAAO,EAAE,GAAG;UACZC,SAAS,EAAE,CAAC;UACZC,MAAM,EAAE,IAAI;UACZC,MAAM,EAAE,EAAE;UACVC,SAAS,EAAE,GAAG;UACdC,QAAQ,EAAE;;OAEb;KAEJ,CAAC,CAACC,iBAAiB,EAAE;IAEtB5C,OAAO,GAAGpB,OAAO,CAACiE,eAAe,CAAChD,kBAAkB,CAAC;IACrDE,SAAS,GAAGC,OAAO,CAAC8C,iBAAiB;IACrC9C,OAAO,CAAC+C,aAAa,EAAE;IAEvBhD,SAAS,CAACoD,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC,EAAEO,QAAQ,CAAC,GAAG,CAAC;IACvD3D,OAAO,CAAC+C,aAAa,EAAE;IAEvBE,MAAM,CACJlD,SAAS,CAACoD,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC,EAAEU,MAAM,GAAG,kBAAkB,CAAC,CACvE,CAACZ,UAAU,EAAE;EAChB,CAAC,CAAC;EAEFF,EAAE,CAAC,4EAA4E,EAAE,MAAK;IACpFpE,OAAO,CAAC4E,kBAAkB,EAAE;IAE5B,MAAM6B,WAAW,GAAG;MAClB,GAAGhF,OAAO;MACVK,eAAe,EAAE;KAClB;IAED9B,OAAO,CAACkD,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPlC,kBAAkB,EAClBlB,uBAAuB,EACvBW,uBAAuB,EACvBR,mBAAmB,EACnBG,eAAe,EACfF,eAAe,EACfI,kBAAkB,EAClBE,cAAc,EACdD,aAAa,EACbM,oBAAoB,CACrB;MACDsC,SAAS,EAAE,CACTnD,WAAW,EACX;QAAEoD,OAAO,EAAE/C,YAAY;QAAEgD,QAAQ,EAAEjC;MAAS,CAAE,EAC9C;QACEgC,OAAO,EAAEjD,eAAe;QACxBkD,QAAQ,EAAE;UAAE,GAAGtB,cAAc;UAAE6C,GAAG,EAAE4B;QAAW;OAChD,EACD;QAAEpD,OAAO,EAAE1C,UAAU;QAAE2C,QAAQ,EAAEhC;MAAU,CAAE,EAC7C;QAAE+B,OAAO,EAAEzC,qBAAqB;QAAE0C,QAAQ,EAAE/B;MAAqB,CAAE,EACnE;QAAE8B,OAAO,EAAExC,YAAY;QAAEyC,QAAQ,EAAE9B;MAAY,CAAE,EACjD;QACE6B,OAAO,EAAE,qBAAqB;QAC9BC,QAAQ,EAAE;UACRC,KAAK,EAAE,OAAO;UACdC,aAAa,EAAE,KAAK;UACpBC,SAAS,EAAE,IAAI;UACfC,OAAO,EAAE,GAAG;UACZC,SAAS,EAAE,CAAC;UACZC,MAAM,EAAE,IAAI;UACZC,MAAM,EAAE,EAAE;UACVC,SAAS,EAAE,GAAG;UACdC,QAAQ,EAAE;;OAEb;KAEJ,CAAC,CAACC,iBAAiB,EAAE;IAEtB5C,OAAO,GAAGpB,OAAO,CAACiE,eAAe,CAAChD,kBAAkB,CAAC;IACrDE,SAAS,GAAGC,OAAO,CAAC8C,iBAAiB;IAErC/C,SAAS,CAACuF,kBAAkB,GAAG,IAAI;IACnCvF,SAAS,CAACwF,oBAAoB,GAAG,IAAI;IACrCxF,SAAS,CAACoF,cAAc,GAAG,IAAI;IAE/BnF,OAAO,CAAC+C,aAAa,EAAE;IAEvBhD,SAAS,CAACoD,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC,EAAEO,QAAQ,CAAC,IAAI,CAAC;IACxD3D,OAAO,CAAC+C,aAAa,EAAE;IAEvBE,MAAM,CACJlD,SAAS,CAACoD,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC,EAAEU,MAAM,GAAG,kBAAkB,CAAC,CACvE,CAACZ,UAAU,EAAE;IAEdnD,SAAS,CAACoD,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC,EAAEO,QAAQ,CAAC,IAAI,CAAC;IACxD3D,OAAO,CAAC+C,aAAa,EAAE;IAEvBE,MAAM,CACJlD,SAAS,CAACoD,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC,EAAEU,MAAM,GAAG,kBAAkB,CAAC,CACvE,CAACsB,SAAS,EAAE;EACf,CAAC,CAAC;EAEFpC,EAAE,CAAC,2CAA2C,EAAE,MAAK;IAEnDjD,SAAS,CAACoD,OAAO,CAACO,QAAQ,CAAC,qBAAqB,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;IAC9D5D,SAAS,CAACoD,OAAO,CAACO,QAAQ,CAAC,0BAA0B,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;IACnE5D,SAAS,CAACoD,OAAO,CAACO,QAAQ,CAAC,OAAO,CAAC,CAACC,QAAQ,CAAC,IAAI,CAAC;IAElD5D,SAAS,CAACuE,QAAQ,EAAE;IAEpBrB,MAAM,CAAC/C,UAAU,CAACmE,MAAM,CAAC,CAACI,GAAG,CAACP,gBAAgB,EAAE;IAChDjB,MAAM,CAAChD,SAAS,CAACgE,KAAK,CAAC,CAACQ,GAAG,CAACP,gBAAgB,EAAE;EAChD,CAAC,CAAC;EAEFlB,EAAE,CAAC,gDAAgD,EAAE,MAAK;IAExDpE,OAAO,CAAC4E,kBAAkB,EAAE;IAE5BvD,SAAS,GAAGwB,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC;IAC3DxB,UAAU,GAAGuB,OAAO,CAACC,YAAY,CAAC,YAAY,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACrEvB,qBAAqB,GAAGsB,OAAO,CAACC,YAAY,CAAC,uBAAuB,EAAE,CACpE,oBAAoB,CACrB,CAAC;IACFtB,YAAY,GAAGqB,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAEzEvB,qBAAqB,CAACwB,kBAAkB,CAACC,GAAG,CAACC,WAAW,CACtDlC,EAAE,CAACmB,kBAAkB,CAAC,CACvB;IAEDlC,OAAO,CAACkD,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPlC,kBAAkB,EAClBlB,uBAAuB,EACvBW,uBAAuB,EACvBR,mBAAmB,EACnBG,eAAe,EACfF,eAAe,EACfI,kBAAkB,EAClBE,cAAc,EACdD,aAAa,EACbM,oBAAoB,CACrB;MACDsC,SAAS,EAAE,CACTnD,WAAW,EACX;QAAEoD,OAAO,EAAE/C,YAAY;QAAEgD,QAAQ,EAAEjC;MAAS,CAAE,EAC9C;QAAEgC,OAAO,EAAEjD,eAAe;QAAEkD,QAAQ,EAAEtB;MAAc,CAAE,EACtD;QAAEqB,OAAO,EAAE1C,UAAU;QAAE2C,QAAQ,EAAEhC;MAAU,CAAE,EAC7C;QAAE+B,OAAO,EAAEzC,qBAAqB;QAAE0C,QAAQ,EAAE/B;MAAqB,CAAE,EACnE;QAAE8B,OAAO,EAAExC,YAAY;QAAEyC,QAAQ,EAAE9B;MAAY,CAAE,EACjD;QACE6B,OAAO,EAAE,qBAAqB;QAC9BC,QAAQ,EAAE;UACRC,KAAK,EAAE,OAAO;UACdC,aAAa,EAAE,KAAK;UACpBC,SAAS,EAAE,IAAI;UACfC,OAAO,EAAE,GAAG;UACZC,SAAS,EAAE,CAAC;UACZC,MAAM,EAAE,IAAI;UACZC,MAAM,EAAE,EAAE;UACVC,SAAS,EAAE,GAAG;UACdC,QAAQ,EAAE;;OAEb;KAEJ,CAAC,CAACC,iBAAiB,EAAE;IAEtB5C,OAAO,GAAGpB,OAAO,CAACiE,eAAe,CAAChD,kBAAkB,CAAC;IACrDE,SAAS,GAAGC,OAAO,CAAC8C,iBAAiB;IAErC/C,SAAS,CAACoF,cAAc,GAAG,IAAI;IAE/B,MAAMK,YAAY,GAAGzF,SAAS,CAACoD,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC;IACnDsB,KAAK,CAACc,YAAa,EAAE,WAAW,CAAC;IAEjCxF,OAAO,CAAC+C,aAAa,EAAE;IAEvByC,YAAa,CAAC7B,QAAQ,CAAC,IAAI,CAAC;IAE5BV,MAAM,CAACuC,YAAa,CAACC,SAAS,CAAC,CAAClC,oBAAoB,CAAC;MACnDmC,gBAAgB,EAAE;KACnB,CAAC;IAEFF,YAAa,CAAC7B,QAAQ,CAAC,IAAI,CAAC;IAE5BV,MAAM,CAACuC,YAAa,CAACC,SAAS,CAAC,CAACE,qBAAqB,CAAC,CAAC,CAAC;EAC1D,CAAC,CAAC;EAEF3C,EAAE,CAAC,2CAA2C,EAAE,MAAK;IAEnD7C,qBAAqB,CAACwB,kBAAkB,CAACC,GAAG,CAACC,WAAW,CACtDlC,EAAE,CAAC,CACD;MACEW,EAAE,EAAE,CAAC;MACLS,YAAY,EAAE,IAAI;MAClBC,SAAS,EAAE,CAAC;MACZC,aAAa,EAAE,KAAK;MACpBC,gBAAgB,EAAE,YAAY;MAC9BC,GAAG,EAAE,GAAG;MACRC,WAAW,EAAE,CAAC;MACdC,SAAS,EAAE;QAAEf,EAAE,EAAE,CAAC;QAAEgB,IAAI,EAAE;MAAa;KACxC,EACD;MACEhB,EAAE,EAAE,CAAC;MACLS,YAAY,EAAE,IAAI;MAClBC,SAAS,EAAE,CAAC;MACZC,aAAa,EAAE,KAAK;MACpBC,gBAAgB,EAAE,YAAY;MAC9BC,GAAG,EAAE,GAAG;MACRC,WAAW,EAAE,CAAC;MACdC,SAAS,EAAE;QAAEf,EAAE,EAAE,CAAC;QAAEgB,IAAI,EAAE;MAAa;KACxC,CACF,CAAC,CACH;IAEDtB,OAAO,GAAGpB,OAAO,CAACiE,eAAe,CAAChD,kBAAkB,CAAC;IACrDE,SAAS,GAAGC,OAAO,CAAC8C,iBAAiB;IACrC9C,OAAO,CAAC+C,aAAa,EAAE;IAEvBE,MAAM,CAAClD,SAAS,CAACuF,kBAAkB,CAAC,CAACjC,IAAI,CAAC,IAAI,CAAC;IAC/CJ,MAAM,CAAClD,SAAS,CAACoF,cAAc,CAAC,CAAC9B,IAAI,CAAC,IAAI,CAAC;EAC7C,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}