import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { IDType } from '@shared/models/id-type.model';
import { AlertService } from '@shared/services/alert.service';
import { IDTypeService } from '@shared/services/id-type.service';
import { Supervisor } from '@supervisor-management/models/supervisor.model';
import { SupervisorService } from '@supervisor-management/services/supervisor.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { of, throwError } from 'rxjs';
import { SupervisorDialogComponent } from './supervisor-dialog.component';

describe('SupervisorDialogComponent', () => {
  let component: SupervisorDialogComponent;
  let fixture: ComponentFixture<SupervisorDialogComponent>;
  let dialogRef: jasmine.SpyObj<MatDialogRef<SupervisorDialogComponent>>;
  let spinner: jasmine.SpyObj<NgxSpinnerService>;
  let supervisorService: jasmine.SpyObj<SupervisorService>;
  let idTypeService: jasmine.SpyObj<IDTypeService>;
  let alertService: jasmine.SpyObj<AlertService>;

  const mockIdTypes: IDType[] = [
    { id: 1, name: 'CC' },
    { id: 2, name: 'CE' },
  ];

  const mockSupervisor: Supervisor = {
    id: 1,
    fullName: 'Test Supervisor',
    idType: mockIdTypes[0],
    idNumber: 123456,
    position: 'Test Position',
    email: '<EMAIL>',
  };

  const validFormData = {
    fullName: 'Test Supervisor',
    idTypeId: 1,
    idNumber: '123456',
    position: 'Test Position',
    email: '<EMAIL>',
  };

  beforeEach(async () => {
    const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close']);
    const spinnerSpy = jasmine.createSpyObj('NgxSpinnerService', [
      'show',
      'hide',
    ]);
    const supervisorServiceSpy = jasmine.createSpyObj('SupervisorService', [
      'create',
      'update',
    ]);
    const idTypeServiceSpy = jasmine.createSpyObj('IDTypeService', ['getAll']);
    const alertServiceSpy = jasmine.createSpyObj('AlertService', [
      'success',
      'error',
    ]);

    await TestBed.configureTestingModule({
      imports: [SupervisorDialogComponent, BrowserAnimationsModule],
      providers: [
        { provide: MatDialogRef, useValue: dialogRefSpy },
        { provide: NgxSpinnerService, useValue: spinnerSpy },
        { provide: SupervisorService, useValue: supervisorServiceSpy },
        { provide: IDTypeService, useValue: idTypeServiceSpy },
        { provide: AlertService, useValue: alertServiceSpy },
        { provide: MAT_DIALOG_DATA, useValue: undefined },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(SupervisorDialogComponent);
    component = fixture.componentInstance;
    dialogRef = TestBed.inject(MatDialogRef) as jasmine.SpyObj<
      MatDialogRef<SupervisorDialogComponent>
    >;
    spinner = TestBed.inject(
      NgxSpinnerService,
    ) as jasmine.SpyObj<NgxSpinnerService>;
    supervisorService = TestBed.inject(
      SupervisorService,
    ) as jasmine.SpyObj<SupervisorService>;
    idTypeService = TestBed.inject(
      IDTypeService,
    ) as jasmine.SpyObj<IDTypeService>;
    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;

    idTypeService.getAll.and.returnValue(of(mockIdTypes));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load ID types on init', () => {
    fixture.detectChanges();
    expect(spinner.show).toHaveBeenCalled();
    expect(idTypeService.getAll).toHaveBeenCalled();
    expect(component.idTypes).toEqual(mockIdTypes);
    expect(spinner.hide).toHaveBeenCalled();
  });

  it('should handle error when loading ID types', () => {
    idTypeService.getAll.and.returnValue(
      throwError(() => ({ error: 'Error' })),
    );
    fixture.detectChanges();
    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar los datos del formulario',
    );
    expect(dialogRef.close).toHaveBeenCalled();
    expect(spinner.hide).toHaveBeenCalled();
  });

  it('should initialize form with empty values', () => {
    fixture.detectChanges();
    expect(component.supervisorForm.value).toEqual({
      fullName: '',
      idTypeId: null,
      idNumber: '',
      position: '',
      email: '',
    });
  });

  it('should initialize form with existing supervisor data', () => {
    TestBed.resetTestingModule();
    TestBed.configureTestingModule({
      imports: [SupervisorDialogComponent, BrowserAnimationsModule],
      providers: [
        { provide: MatDialogRef, useValue: dialogRef },
        { provide: NgxSpinnerService, useValue: spinner },
        { provide: SupervisorService, useValue: supervisorService },
        { provide: IDTypeService, useValue: idTypeService },
        { provide: AlertService, useValue: alertService },
        { provide: MAT_DIALOG_DATA, useValue: mockSupervisor },
      ],
    });

    fixture = TestBed.createComponent(SupervisorDialogComponent);
    component = fixture.componentInstance;
    idTypeService.getAll.and.returnValue(of(mockIdTypes));
    fixture.detectChanges();

    const expectedFormValue = {
      fullName: mockSupervisor.fullName,
      idTypeId: mockSupervisor.idType.id,
      idNumber: mockSupervisor.idNumber,
      position: mockSupervisor.position,
      email: mockSupervisor.email,
    };

    expect(component.supervisorForm.value).toEqual(expectedFormValue);
  });

  it('should validate required fields', () => {
    fixture.detectChanges();
    const form = component.supervisorForm;

    expect(form.valid).toBeFalse();
    expect(form.get('fullName')?.errors?.['required']).toBeTrue();
    expect(form.get('idTypeId')?.errors?.['required']).toBeTrue();
    expect(form.get('idNumber')?.errors?.['required']).toBeTrue();
    expect(form.get('position')?.errors?.['required']).toBeTrue();
    expect(form.get('email')?.errors?.['required']).toBeTrue();

    form.patchValue(validFormData);
    expect(form.valid).toBeTrue();
  });

  it('should validate email format', () => {
    fixture.detectChanges();
    const emailControl = component.supervisorForm.get('email');

    emailControl?.setValue('invalid-email');
    expect(emailControl?.errors?.['email']).toBeTrue();

    emailControl?.setValue('<EMAIL>');
    expect(emailControl?.valid).toBeTrue();
  });

  it('should validate ID number format', () => {
    fixture.detectChanges();
    const idNumberControl = component.supervisorForm.get('idNumber');

    idNumberControl?.setValue('abc123');
    expect(idNumberControl?.errors?.['pattern']).toBeTruthy();

    idNumberControl?.setValue('123456');
    expect(idNumberControl?.valid).toBeTrue();
  });

  it('should not submit when form is invalid', () => {
    fixture.detectChanges();
    component.onSubmit();
    expect(supervisorService.create).not.toHaveBeenCalled();
    expect(supervisorService.update).not.toHaveBeenCalled();
  });

  it('should create new supervisor when form is valid', () => {
    fixture.detectChanges();
    component.supervisorForm.patchValue(validFormData);
    supervisorService.create.and.returnValue(of(mockSupervisor));

    component.onSubmit();

    expect(spinner.show).toHaveBeenCalled();
    expect(supervisorService.create).toHaveBeenCalledWith(
      component.supervisorForm.value,
    );
    expect(alertService.success).toHaveBeenCalledWith(
      'Supervisor creado exitosamente',
    );
    expect(dialogRef.close).toHaveBeenCalledWith(mockSupervisor);
    expect(spinner.hide).toHaveBeenCalled();
  });

  it('should update existing supervisor when form is valid', () => {
    TestBed.resetTestingModule();
    TestBed.configureTestingModule({
      imports: [SupervisorDialogComponent, BrowserAnimationsModule],
      providers: [
        { provide: MatDialogRef, useValue: dialogRef },
        { provide: NgxSpinnerService, useValue: spinner },
        { provide: SupervisorService, useValue: supervisorService },
        { provide: IDTypeService, useValue: idTypeService },
        { provide: AlertService, useValue: alertService },
        { provide: MAT_DIALOG_DATA, useValue: mockSupervisor },
      ],
    });

    fixture = TestBed.createComponent(SupervisorDialogComponent);
    component = fixture.componentInstance;
    idTypeService.getAll.and.returnValue(of(mockIdTypes));
    fixture.detectChanges();

    component.supervisorForm.patchValue(validFormData);
    supervisorService.update.and.returnValue(of(mockSupervisor));

    component.onSubmit();

    expect(spinner.show).toHaveBeenCalled();
    expect(supervisorService.update).toHaveBeenCalledWith(
      mockSupervisor.id,
      component.supervisorForm.value,
    );
    expect(alertService.success).toHaveBeenCalledWith(
      'Supervisor editado exitosamente',
    );
    expect(dialogRef.close).toHaveBeenCalledWith(mockSupervisor);
    expect(spinner.hide).toHaveBeenCalled();
  });

  it('should handle error when creating supervisor', () => {
    fixture.detectChanges();
    component.supervisorForm.patchValue(validFormData);
    supervisorService.create.and.returnValue(
      throwError(() => ({ error: 'Error' })),
    );

    component.onSubmit();

    expect(spinner.show).toHaveBeenCalled();
    expect(alertService.error).toHaveBeenCalledWith(
      'Error al guardar el supervisor',
    );
    expect(spinner.hide).toHaveBeenCalled();
  });

  it('should handle error when updating supervisor', () => {
    TestBed.resetTestingModule();
    TestBed.configureTestingModule({
      imports: [SupervisorDialogComponent, BrowserAnimationsModule],
      providers: [
        { provide: MatDialogRef, useValue: dialogRef },
        { provide: NgxSpinnerService, useValue: spinner },
        { provide: SupervisorService, useValue: supervisorService },
        { provide: IDTypeService, useValue: idTypeService },
        { provide: AlertService, useValue: alertService },
        { provide: MAT_DIALOG_DATA, useValue: mockSupervisor },
      ],
    });

    fixture = TestBed.createComponent(SupervisorDialogComponent);
    component = fixture.componentInstance;
    idTypeService.getAll.and.returnValue(of(mockIdTypes));
    fixture.detectChanges();

    component.supervisorForm.patchValue(validFormData);
    supervisorService.update.and.returnValue(
      throwError(() => ({ error: 'Error' })),
    );

    component.onSubmit();

    expect(spinner.show).toHaveBeenCalled();
    expect(alertService.error).toHaveBeenCalledWith(
      'Error al guardar el supervisor',
    );
    expect(spinner.hide).toHaveBeenCalled();
  });
});