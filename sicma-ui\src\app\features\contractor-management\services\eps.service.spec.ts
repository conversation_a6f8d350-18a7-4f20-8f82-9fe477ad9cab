import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { Eps } from '@contractor-management/models/eps.model';
import { environment } from '@env';
import { EpsService } from './eps.service';

describe('EpsService', () => {
  let service: EpsService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/eps`;

  const mockEps: Eps = {
    id: 1,
    name: 'Test EPS',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [EpsService],
    });
    service = TestBed.inject(EpsService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all EPS', () => {
      const mockEpsList = [mockEps];

      service.getAll().subscribe((epsList) => {
        expect(epsList).toEqual(mockEpsList);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockEpsList);
    });
  });

  describe('getById', () => {
    it('should return an EPS by id', () => {
      const id = 1;

      service.getById(id).subscribe((eps) => {
        expect(eps).toEqual(mockEps);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockEps);
    });
  });

  describe('create', () => {
    it('should create a new EPS', () => {
      const newEps: Omit<Eps, 'id'> = {
        name: 'New EPS',
      };

      service.create(newEps).subscribe((eps) => {
        expect(eps).toEqual(mockEps);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newEps);
      req.flush(mockEps);
    });
  });

  describe('update', () => {
    it('should update an EPS', () => {
      const id = 1;
      const updateData: Partial<Eps> = {
        name: 'Updated EPS',
      };

      service.update(id, updateData).subscribe((eps) => {
        expect(eps).toEqual(mockEps);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockEps);
    });
  });

  describe('delete', () => {
    it('should delete an EPS', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });

  describe('getByName', () => {
    it('should return an EPS by name', () => {
      const name = 'Test EPS';

      service.getByName(name).subscribe((eps) => {
        expect(eps).toEqual(mockEps);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockEps);
    });
  });
});