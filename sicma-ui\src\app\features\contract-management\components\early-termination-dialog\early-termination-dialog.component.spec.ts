import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { Contract } from '@contract-management/models/contract.model';
import { ContractorContract } from '@contract-management/models/contractor-contract.model';
import { ContractValuesService } from '@contract-management/services/contract-values.service';
import { ContractService } from '@contract-management/services/contract.service';
import { ContractorContractService } from '@contract-management/services/contractor-contract.service';
import { AlertService } from '@shared/services/alert.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { of, throwError } from 'rxjs';
import { EarlyTerminationDialogComponent } from './early-termination-dialog.component';

describe('EarlyTerminationDialogComponent', () => {
  let component: EarlyTerminationDialogComponent;
  let fixture: ComponentFixture<EarlyTerminationDialogComponent>;
  let alertService: jasmine.SpyObj<AlertService>;
  let contractService: jasmine.SpyObj<ContractService>;
  let contractorContractService: jasmine.SpyObj<ContractorContractService>;
  let contractValuesService: jasmine.SpyObj<ContractValuesService>;
  let dialogRef: jasmine.SpyObj<MatDialogRef<EarlyTerminationDialogComponent>>;
  let spinnerService: jasmine.SpyObj<NgxSpinnerService>;

  const mockDialogData = {
    contractorId: 1,
    contractId: 1,
    lastContractorStartDate: new Date(),
  };

  const mockContractorContract: ContractorContract = {
    id: 1,
    contractorId: 1,
    contractId: 1,
    subscriptionDate: '2024-01-01',
    contractStartDate: '2024-01-01',
    contractEndDate: '2024-12-31',
    warranty: false,
    typeWarrantyId: 1,
    insuredRisksId: 1,
  };

  const mockContract: Contract = {
    id: 1,
    contractNumber: 1,
    monthlyPayment: 1000000,
    object: 'Test contract',
    rup: false,
    secopCode: 1,
    addition: false,
    cession: false,
    settled: false,
    contractTypeId: 1,
    statusId: 1,
    earlyTermination: true,
    causesSelectionId: 1,
    managementSupportId: 1,
    contractClassId: 1,
  };

  beforeEach(async () => {
    alertService = jasmine.createSpyObj('AlertService', [
      'success',
      'error',
      'warning',
    ]);
    contractService = jasmine.createSpyObj('ContractService', ['update']);
    contractorContractService = jasmine.createSpyObj(
      'ContractorContractService',
      ['earlyTerminateContract'],
    );
    contractValuesService = jasmine.createSpyObj('ContractValuesService', [
      'getLatestEndDateByContractId',
    ]);
    dialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
    spinnerService = jasmine.createSpyObj('NgxSpinnerService', [
      'show',
      'hide',
    ]);

    contractValuesService.getLatestEndDateByContractId.and.returnValue(
      of(new Date()),
    );
    contractorContractService.earlyTerminateContract.and.returnValue(
      of(mockContractorContract),
    );
    contractService.update.and.returnValue(of(mockContract));

    await TestBed.configureTestingModule({
      imports: [
        EarlyTerminationDialogComponent,
        ReactiveFormsModule,
        MatButtonModule,
        MatDialogModule,
        MatFormFieldModule,
        MatIconModule,
        MatInputModule,
        MatDatepickerModule,
        NoopAnimationsModule,
      ],
      providers: [
        { provide: AlertService, useValue: alertService },
        { provide: ContractService, useValue: contractService },
        {
          provide: ContractorContractService,
          useValue: contractorContractService,
        },
        { provide: ContractValuesService, useValue: contractValuesService },
        { provide: MatDialogRef, useValue: dialogRef },
        { provide: NgxSpinnerService, useValue: spinnerService },
        { provide: MAT_DIALOG_DATA, useValue: mockDialogData },
        provideNativeDateAdapter(),
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(EarlyTerminationDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Form Initialization', () => {
    it('should initialize form with empty values', () => {
      expect(component.terminationForm.get('endDate')?.value).toBe('');
      expect(component.terminationForm.get('terminationReason')?.value).toBe(
        '',
      );
    });
  });

  describe('Form Validation', () => {
    it('should validate required fields', () => {
      expect(component.terminationForm.valid).toBeFalsy();
      expect(
        component.terminationForm.get('endDate')?.errors?.['required'],
      ).toBeTruthy();
      expect(
        component.terminationForm.get('terminationReason')?.errors?.[
          'required'
        ],
      ).toBeTruthy();
    });

    it('should validate termination reason length', () => {
      component.terminationForm.patchValue({
        terminationReason: 'short',
      });
      expect(
        component.terminationForm.get('terminationReason')?.errors?.[
          'minlength'
        ],
      ).toBeTruthy();

      component.terminationForm.patchValue({
        terminationReason: 'This is a valid termination reason',
      });
      expect(
        component.terminationForm.get('terminationReason')?.errors?.[
          'minlength'
        ],
      ).toBeFalsy();
    });
  });

  describe('Form Submission', () => {
    it('should not submit if form is invalid', () => {
      const form = component.terminationForm;
      form.markAllAsTouched();
      component.onSubmit();
      expect(
        contractorContractService.earlyTerminateContract,
      ).not.toHaveBeenCalled();
      expect(contractService.update).not.toHaveBeenCalled();
      expect(spinnerService.show).not.toHaveBeenCalled();
    });

    it('should submit form successfully', () => {
      const today = new Date();
      component.terminationForm.patchValue({
        endDate: today,
        terminationReason: 'This is a valid termination reason',
      });

      component.onSubmit();

      expect(spinnerService.show).toHaveBeenCalled();
      expect(
        contractorContractService.earlyTerminateContract,
      ).toHaveBeenCalledWith({
        contractorId: mockDialogData.contractorId,
        contractId: mockDialogData.contractId,
        endDate: today.toISOString().slice(0, 10),
        terminationReason: 'This is a valid termination reason',
      });
      expect(contractService.update).toHaveBeenCalledWith(
        mockDialogData.contractId,
        { earlyTermination: true },
      );
      expect(alertService.success).toHaveBeenCalledWith(
        'El contrato ha sido terminado anticipadamente',
      );
      expect(dialogRef.close).toHaveBeenCalledWith({
        endDate: today.toISOString().slice(0, 10),
        terminationReason: 'This is a valid termination reason',
      });
      expect(spinnerService.hide).toHaveBeenCalled();
    });

    it('should handle submission error', () => {
      contractorContractService.earlyTerminateContract.and.returnValue(
        throwError(() => new Error()),
      );

      const today = new Date();
      component.terminationForm.patchValue({
        endDate: today,
        terminationReason: 'This is a valid termination reason',
      });

      component.onSubmit();

      expect(spinnerService.show).toHaveBeenCalled();
      expect(alertService.error).toHaveBeenCalledWith(
        'Hubo un problema al terminar anticipadamente el contrato',
      );
      expect(spinnerService.hide).toHaveBeenCalled();
    });
  });
});