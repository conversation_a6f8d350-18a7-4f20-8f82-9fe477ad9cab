import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { Gender } from '@contractor-management/models/gender.model';
import { environment } from '@env';
import { GenderService } from './gender.service';

describe('GenderService', () => {
  let service: GenderService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/genders`;

  const mockGender: Gender = {
    id: 1,
    name: 'Test Gender',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [GenderService],
    });
    service = TestBed.inject(GenderService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all genders', () => {
      const mockGenders = [mockGender];

      service.getAll().subscribe((genders) => {
        expect(genders).toEqual(mockGenders);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockGenders);
    });
  });

  describe('getById', () => {
    it('should return a gender by id', () => {
      const id = 1;

      service.getById(id).subscribe((gender) => {
        expect(gender).toEqual(mockGender);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockGender);
    });
  });

  describe('create', () => {
    it('should create a new gender', () => {
      const newGender: Omit<Gender, 'id'> = {
        name: 'New Gender',
      };

      service.create(newGender).subscribe((gender) => {
        expect(gender).toEqual(mockGender);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newGender);
      req.flush(mockGender);
    });
  });

  describe('update', () => {
    it('should update a gender', () => {
      const id = 1;
      const updateData: Partial<Gender> = {
        name: 'Updated Gender',
      };

      service.update(id, updateData).subscribe((gender) => {
        expect(gender).toEqual(mockGender);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockGender);
    });
  });

  describe('delete', () => {
    it('should delete a gender', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });

  describe('getByName', () => {
    it('should return a gender by name', () => {
      const name = 'Test Gender';

      service.getByName(name).subscribe((gender) => {
        expect(gender).toEqual(mockGender);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockGender);
    });
  });
});