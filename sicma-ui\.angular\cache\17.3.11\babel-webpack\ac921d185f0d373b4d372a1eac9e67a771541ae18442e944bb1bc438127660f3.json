{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { SelectionModalityService } from './selection-modality.service';\ndescribe('SelectionModalityService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/selection-modalities`;\n  const mockSelectionModality = {\n    id: 1,\n    name: 'Test Selection Modality'\n  };\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [SelectionModalityService]\n    });\n    service = TestBed.inject(SelectionModalityService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all selection modalities', () => {\n      const mockSelectionModalities = [mockSelectionModality];\n      service.getAll().subscribe(selectionModalities => {\n        expect(selectionModalities).toEqual(mockSelectionModalities);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockSelectionModalities);\n    });\n  });\n  describe('getById', () => {\n    it('should return a selection modality by id', () => {\n      const id = 1;\n      service.getById(id).subscribe(selectionModality => {\n        expect(selectionModality).toEqual(mockSelectionModality);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockSelectionModality);\n    });\n  });\n  describe('create', () => {\n    it('should create a new selection modality', () => {\n      const newSelectionModality = {\n        name: 'New Selection Modality'\n      };\n      service.create(newSelectionModality).subscribe(selectionModality => {\n        expect(selectionModality).toEqual(mockSelectionModality);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newSelectionModality);\n      req.flush(mockSelectionModality);\n    });\n  });\n  describe('update', () => {\n    it('should update a selection modality', () => {\n      const id = 1;\n      const updateData = {\n        name: 'Updated Selection Modality'\n      };\n      service.update(id, updateData).subscribe(selectionModality => {\n        expect(selectionModality).toEqual(mockSelectionModality);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockSelectionModality);\n    });\n  });\n  describe('delete', () => {\n    it('should delete a selection modality', () => {\n      const id = 1;\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n  describe('getByName', () => {\n    it('should return a selection modality by name', () => {\n      const name = 'Test Selection Modality';\n      service.getByName(name).subscribe(selectionModality => {\n        expect(selectionModality).toEqual(mockSelectionModality);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockSelectionModality);\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "SelectionModalityService", "describe", "service", "httpMock", "apiUrl", "mockSelectionModality", "id", "name", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockSelectionModalities", "getAll", "subscribe", "selectionModalities", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "getById", "selectionModality", "newSelectionModality", "create", "body", "updateData", "update", "delete", "nothing", "getByName"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\services\\selection-modality.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { SelectionModality } from '@contract-management/models/selection-modality.model';\nimport { environment } from '@env';\nimport { SelectionModalityService } from './selection-modality.service';\n\ndescribe('SelectionModalityService', () => {\n  let service: SelectionModalityService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/selection-modalities`;\n\n  const mockSelectionModality: SelectionModality = {\n    id: 1,\n    name: 'Test Selection Modality',\n  };\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [SelectionModalityService],\n    });\n    service = TestBed.inject(SelectionModalityService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all selection modalities', () => {\n      const mockSelectionModalities = [mockSelectionModality];\n\n      service.getAll().subscribe((selectionModalities) => {\n        expect(selectionModalities).toEqual(mockSelectionModalities);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockSelectionModalities);\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a selection modality by id', () => {\n      const id = 1;\n\n      service.getById(id).subscribe((selectionModality) => {\n        expect(selectionModality).toEqual(mockSelectionModality);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockSelectionModality);\n    });\n  });\n\n  describe('create', () => {\n    it('should create a new selection modality', () => {\n      const newSelectionModality: Omit<SelectionModality, 'id'> = {\n        name: 'New Selection Modality',\n      };\n\n      service.create(newSelectionModality).subscribe((selectionModality) => {\n        expect(selectionModality).toEqual(mockSelectionModality);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newSelectionModality);\n      req.flush(mockSelectionModality);\n    });\n  });\n\n  describe('update', () => {\n    it('should update a selection modality', () => {\n      const id = 1;\n      const updateData: Partial<SelectionModality> = {\n        name: 'Updated Selection Modality',\n      };\n\n      service.update(id, updateData).subscribe((selectionModality) => {\n        expect(selectionModality).toEqual(mockSelectionModality);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockSelectionModality);\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete a selection modality', () => {\n      const id = 1;\n\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n\n  describe('getByName', () => {\n    it('should return a selection modality by name', () => {\n      const name = 'Test Selection Modality';\n\n      service.getByName(name).subscribe((selectionModality) => {\n        expect(selectionModality).toEqual(mockSelectionModality);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockSelectionModality);\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,wBAAwB,QAAQ,8BAA8B;AAEvEC,QAAQ,CAAC,0BAA0B,EAAE,MAAK;EACxC,IAAIC,OAAiC;EACrC,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,uBAAuB;EAE3D,MAAMC,qBAAqB,GAAsB;IAC/CC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE;GACP;EAEDC,UAAU,CAAC,MAAK;IACdV,OAAO,CAACW,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACd,uBAAuB,CAAC;MAClCe,SAAS,EAAE,CAACX,wBAAwB;KACrC,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACc,MAAM,CAACZ,wBAAwB,CAAC;IAClDG,QAAQ,GAAGL,OAAO,CAACc,MAAM,CAACf,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFgB,SAAS,CAAC,MAAK;IACbV,QAAQ,CAACW,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACd,OAAO,CAAC,CAACe,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFhB,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,wCAAwC,EAAE,MAAK;MAChD,MAAMG,uBAAuB,GAAG,CAACb,qBAAqB,CAAC;MAEvDH,OAAO,CAACiB,MAAM,EAAE,CAACC,SAAS,CAAEC,mBAAmB,IAAI;QACjDL,MAAM,CAACK,mBAAmB,CAAC,CAACC,OAAO,CAACJ,uBAAuB,CAAC;MAC9D,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,uBAAuB,CAAC;IACpC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjB,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBc,EAAE,CAAC,0CAA0C,EAAE,MAAK;MAClD,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAAC2B,OAAO,CAACvB,EAAE,CAAC,CAACc,SAAS,CAAEU,iBAAiB,IAAI;QAClDd,MAAM,CAACc,iBAAiB,CAAC,CAACR,OAAO,CAACjB,qBAAqB,CAAC;MAC1D,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvB,qBAAqB,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,wCAAwC,EAAE,MAAK;MAChD,MAAMgB,oBAAoB,GAAkC;QAC1DxB,IAAI,EAAE;OACP;MAEDL,OAAO,CAAC8B,MAAM,CAACD,oBAAoB,CAAC,CAACX,SAAS,CAAEU,iBAAiB,IAAI;QACnEd,MAAM,CAACc,iBAAiB,CAAC,CAACR,OAAO,CAACjB,qBAAqB,CAAC;MAC1D,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACS,oBAAoB,CAAC;MACtDR,GAAG,CAACK,KAAK,CAACvB,qBAAqB,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,oCAAoC,EAAE,MAAK;MAC5C,MAAMT,EAAE,GAAG,CAAC;MACZ,MAAM4B,UAAU,GAA+B;QAC7C3B,IAAI,EAAE;OACP;MAEDL,OAAO,CAACiC,MAAM,CAAC7B,EAAE,EAAE4B,UAAU,CAAC,CAACd,SAAS,CAAEU,iBAAiB,IAAI;QAC7Dd,MAAM,CAACc,iBAAiB,CAAC,CAACR,OAAO,CAACjB,qBAAqB,CAAC;MAC1D,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACY,UAAU,CAAC;MAC5CX,GAAG,CAACK,KAAK,CAACvB,qBAAqB,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,oCAAoC,EAAE,MAAK;MAC5C,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAACkC,MAAM,CAAC9B,EAAE,CAAC,CAACc,SAAS,CAAC,MAAK;QAChCJ,MAAM,EAAE,CAACqB,OAAO,EAAE;MACpB,CAAC,CAAC;MAEF,MAAMd,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF3B,QAAQ,CAAC,WAAW,EAAE,MAAK;IACzBc,EAAE,CAAC,4CAA4C,EAAE,MAAK;MACpD,MAAMR,IAAI,GAAG,yBAAyB;MAEtCL,OAAO,CAACoC,SAAS,CAAC/B,IAAI,CAAC,CAACa,SAAS,CAAEU,iBAAiB,IAAI;QACtDd,MAAM,CAACc,iBAAiB,CAAC,CAACR,OAAO,CAACjB,qBAAqB,CAAC;MAC1D,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,SAASG,IAAI,EAAE,CAAC;MACxDS,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvB,qBAAqB,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}