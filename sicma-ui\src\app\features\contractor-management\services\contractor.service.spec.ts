import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { Contractor } from '@contractor-management/models/contractor.model';
import { environment } from '@env';
import { ContractorService } from './contractor.service';

describe('ContractorService', () => {
  let service: ContractorService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/contractors`;

  const mockContractor: Contractor = {
    id: 1,
    fullName: 'Test Contractor',
    idNumber: 123456789,
    birthDate: '2024-02-20',
    phone: 1234567890,
    personalEmail: '<EMAIL>',
    corporateEmail: '<EMAIL>',
    lastObtainedDegree: 'Bachelor',
    idType: { id: 1, name: 'CC' },
    idTypeId: 1,
    gender: { id: 1, name: 'Male' },
    genderId: 1,
    eps: { id: 1, name: 'Test EPS' },
    epsId: 1,
    educationLevel: { id: 1, name: 'Bachelor' },
    educationLevelId: 1,
    profession: { id: 1, name: 'Engineer' },
    professionId: 1,
    municipality: {
      id: 1,
      name: 'Test Municipality',
      departmentId: 1,
      department: { id: 1, name: 'Test Department' },
    },
    municipalityId: 1,
    department: { id: 1, name: 'Test Department' },
    departmentId: 1,
    legalNature: { id: 1, name: 'Test Legal Nature' },
    legalNatureId: 1,
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ContractorService],
    });
    service = TestBed.inject(ContractorService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all contractors', () => {
      const mockContractors = [mockContractor];

      service.getAll().subscribe((contractors) => {
        expect(contractors).toEqual(mockContractors);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockContractors);
    });
  });

  describe('getById', () => {
    it('should return a contractor by id', () => {
      const id = 1;

      service.getById(id).subscribe((contractor) => {
        expect(contractor).toEqual(mockContractor);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockContractor);
    });
  });

  describe('getByIdNumber', () => {
    it('should return a contractor by id number', () => {
      const idNumber = 123456789;

      service.getByIdNumber(idNumber).subscribe((contractor) => {
        expect(contractor).toEqual(mockContractor);
      });

      const req = httpMock.expectOne(`${apiUrl}/id-number/${idNumber}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockContractor);
    });
  });

  describe('getByEmail', () => {
    it('should return a contractor by email', () => {
      const email = '<EMAIL>';

      service.getByEmail(email).subscribe((contractor) => {
        expect(contractor).toEqual(mockContractor);
      });

      const req = httpMock.expectOne(`${apiUrl}/email/${email}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockContractor);
    });
  });

  describe('create', () => {
    it('should create a new contractor', () => {
      const newContractor: Omit<Contractor, 'id'> = {
        fullName: 'New Contractor',
        idNumber: 987654321,
        birthDate: '2024-02-20',
        phone: 9876543210,
        personalEmail: '<EMAIL>',
        corporateEmail: '<EMAIL>',
        lastObtainedDegree: 'Master',
        idType: { id: 1, name: 'CC' },
        idTypeId: 1,
        gender: { id: 1, name: 'Male' },
        genderId: 1,
        eps: { id: 1, name: 'Test EPS' },
        epsId: 1,
        educationLevel: { id: 1, name: 'Master' },
        educationLevelId: 1,
        profession: { id: 1, name: 'Engineer' },
        professionId: 1,
        municipality: {
          id: 1,
          name: 'Test Municipality',
          departmentId: 1,
          department: { id: 1, name: 'Test Department' },
        },
        municipalityId: 1,
        department: { id: 1, name: 'Test Department' },
        departmentId: 1,
        legalNature: { id: 1, name: 'Test Legal Nature' },
        legalNatureId: 1,
      };

      service.create(newContractor).subscribe((contractor) => {
        expect(contractor).toEqual(mockContractor);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newContractor);
      req.flush(mockContractor);
    });
  });

  describe('update', () => {
    it('should update a contractor', () => {
      const id = 1;
      const updateData: Partial<Contractor> = {
        fullName: 'Updated Contractor',
        personalEmail: '<EMAIL>',
        phone: 5555555555,
      };

      service.update(id, updateData).subscribe((contractor) => {
        expect(contractor).toEqual(mockContractor);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockContractor);
    });
  });

  describe('delete', () => {
    it('should delete a contractor', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });
});