import { Contract } from '@contract-management/models/contract.model';
import { ContractService } from '@contract-management/services/contract.service';
import { ARLAffiliationClass } from '@contractor-dashboard/models/arl-affiliation-class.model';
import { CompensationFund } from '@contractor-dashboard/models/compensation-fund.model';
import { InitialReportDocumentation } from '@contractor-dashboard/models/initial-report-documentation.model';
import { MonthlyReport } from '@contractor-dashboard/models/monthly-report.model';
import { SocialSecurityContribution } from '@contractor-dashboard/models/social-security-contribution.model';
import { ArlAffiliationClassService } from '@contractor-dashboard/services/arl-affiliation-class.service';
import { CompensationFundService } from '@contractor-dashboard/services/compensation-fund.service';
import { InitialReportDocumentationService } from '@contractor-dashboard/services/initial-report-documentation.service';
import { PeriodService } from '@contractor-dashboard/services/period.service';
import { SocialSecurityContributionService } from '@contractor-dashboard/services/social-security-contribution.service';
import { AlertService } from '@shared/services/alert.service';

import { CurrencyPipe } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButton, MatIconButton } from '@angular/material/button';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatOption } from '@angular/material/core';
import { MatError, MatFormField, MatLabel } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput } from '@angular/material/input';
import { MatSelect } from '@angular/material/select';
import { MatTooltip } from '@angular/material/tooltip';
import { NgxCurrencyDirective } from 'ngx-currency';

import { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';

@Component({
  selector: 'app-monthly-report-social-security-information',
  templateUrl: './monthly-report-social-security-information.component.html',
  styleUrl: './monthly-report-social-security-information.component.scss',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatIcon,
    MatFormField,
    MatLabel,
    MatInput,
    MatSelect,
    MatOption,
    MatError,
    MatCheckbox,
    MatButton,
    MatIconButton,
    MatTooltip,
    CurrencyPipe,
    NgxCurrencyDirective,
  ],
})
export class MonthlyReportSocialSecurityInformationComponent
  implements OnInit, OnChanges
{
  @Input() socialSecurityContribution: SocialSecurityContribution | null = null;
  @Input() contractorContractId!: number;
  @Input() report!: MonthlyReport;
  @Input() initialReportDocumentation: InitialReportDocumentation | null = null;
  @Input() isSupervisor = false;
  @Output() saveSocialSecurity = new EventEmitter<SocialSecurityContribution>();
  @Output() formValidityChange = new EventEmitter<boolean>();

  socialSecurityForm: FormGroup = this.fb.group({
    paymentFormNumber: [
      null,
      this.isSupervisor ? [] : [Validators.required, Validators.min(0)],
    ],
    certificateFile: [null, this.isSupervisor ? [] : [Validators.required]],
    arlAffiliationClass: [null, this.isSupervisor ? [] : [Validators.required]],
    arlContribution: [
      null,
      this.isSupervisor ? [] : [Validators.required, Validators.min(0)],
    ],
    hasCompensationFund: [false],
    compensationFund: [{ value: null, disabled: this.isSupervisor }],
    compensationFundContribution: [
      { value: null, disabled: this.isSupervisor },
    ],
  });
  fileName = '';
  contract: Contract | null = null;
  previousPaymentValue = 0;
  ibc = 0;
  healthContribution = 0;
  pensionContribution = 0;
  arlAffiliationClasses: ARLAffiliationClass[] = [];
  compensationFunds: CompensationFund[] = [];
  selectedFile: File | null = null;

  constructor(
    private readonly fb: FormBuilder,
    private readonly contractService: ContractService,
    private readonly initialReportDocumentationService: InitialReportDocumentationService,
    private readonly alert: AlertService,
    private readonly arlAffiliationClassService: ArlAffiliationClassService,
    private readonly compensationFundService: CompensationFundService,
    private readonly socialSecurityContributionService: SocialSecurityContributionService,
    private readonly periodService: PeriodService,
    private readonly MonthlyReportService: MonthlyReportService,
  ) {
    if (this.isSupervisor) {
      this.socialSecurityForm.get('hasCompensationFund')?.disable();
    }
  }

  private updateCompensationFundValidation(checked: boolean): void {
    const compensationFundControl =
      this.socialSecurityForm.get('compensationFund');
    const compensationFundContributionControl = this.socialSecurityForm.get(
      'compensationFundContribution',
    );

    if (checked) {
      compensationFundControl?.setValidators(Validators.required);
      compensationFundContributionControl?.setValidators([
        Validators.required,
        Validators.min(0),
      ]);
    } else {
      compensationFundControl?.clearValidators();
      compensationFundContributionControl?.clearValidators();
      compensationFundControl?.setValue(null);
      compensationFundContributionControl?.setValue(null);
    }

    compensationFundControl?.updateValueAndValidity({ emitEvent: false });
    compensationFundContributionControl?.updateValueAndValidity({
      emitEvent: false,
    });
    this.socialSecurityForm.updateValueAndValidity();
  }

  ngOnInit(): void {
    this.initializeForm();
    this.loadData();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['socialSecurityContribution']) {
      this.initializeForm();
    }
  }

  private initializeForm(): void {
    const certificateFileValidator = this.isSupervisor
      ? []
      : [Validators.required];

    if (
      !this.isSupervisor &&
      this.socialSecurityContribution?.certificateFileUrl
    ) {
      certificateFileValidator.length = 0;
    }

    this.socialSecurityForm = this.fb.group({
      paymentFormNumber: [
        this.socialSecurityContribution?.paymentFormNumber || null,
        this.isSupervisor ? [] : [Validators.required, Validators.min(0)],
      ],
      certificateFile: [null, certificateFileValidator],
      arlAffiliationClass: [
        this.socialSecurityContribution?.arlAffiliationClassId || null,
        this.isSupervisor ? [] : [Validators.required],
      ],
      arlContribution: [
        this.socialSecurityContribution?.arlContribution || null,
        this.isSupervisor ? [] : [Validators.required, Validators.min(0)],
      ],
      hasCompensationFund: [
        {
          value: Boolean(this.socialSecurityContribution?.compensationFundId),
          disabled: this.isSupervisor,
        },
      ],
      compensationFund: [
        this.socialSecurityContribution?.compensationFundId || null,
        { value: null, disabled: this.isSupervisor },
      ],
      compensationFundContribution: [
        this.socialSecurityContribution?.compensationFundContribution || null,
        { value: null, disabled: this.isSupervisor },
      ],
    });

    this.updateFormValidity();
  }

  loadData(): void {
    this.loadInitialReportDocumentation();
    this.loadARLAffiliationClasses();
    this.loadCompensationFunds();
    this.loadSocialSecurityContribution();
    this.loadPeriodPayment();

    this.socialSecurityForm.statusChanges.subscribe(() => {
      this.formValidityChange.emit(
        this.isSupervisor ? true : this.socialSecurityForm.valid,
      );
    });

    this.socialSecurityForm
      .get('hasCompensationFund')
      ?.valueChanges.subscribe((checked) => {
        if (!this.isSupervisor) {
          this.updateCompensationFundValidation(checked);
        }
      });
  }

  loadInitialReportDocumentation(): void {
    this.initialReportDocumentationService
      .getByContractorContractId(this.contractorContractId)
      .subscribe({
        next: (data) => (this.initialReportDocumentation = data),
        error: (error) => {
          if (error.status !== 404) {
            this.alert.error(error.error?.detail ?? 'Error al cargar la documentación inicial');
          }
        },
      });
  }

  calculateContributions(): void {
    this.ibc = this.previousPaymentValue * 0.4;
    this.healthContribution = (this.ibc * 12.5) / 100;
    this.pensionContribution = (this.ibc * 16) / 100;
  }

  onFileSelected(event: Event): void {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (file) {
      if (file.type === 'application/pdf') {
        if (file.size <= 1024 * 1024) {
          this.selectedFile = file;
          this.fileName = file.name;
          this.socialSecurityForm.patchValue({ certificateFile: file });
          this.socialSecurityForm
            .get('certificateFile')
            ?.setValidators([Validators.required]);
        } else {
          this.alert.error('El archivo no debe superar 1MB');
        }
      } else {
        this.alert.error('Solo se permiten archivos PDF');
      }
      this.socialSecurityForm.get('certificateFile')?.updateValueAndValidity();
      this.updateFormValidity();
    }
  }

  private updateFormValidity(): void {
    const formValid =
      this.isSupervisor ||
      this.socialSecurityForm.valid ||
      (this.socialSecurityForm.get('certificateFile')?.value === null &&
        Boolean(this.socialSecurityContribution?.certificateFileUrl));

    this.formValidityChange.emit(formValid);
  }

  onSave(): void {
    const updatedSocialSecurity = {
      ...this.socialSecurityContribution,
      ...this.socialSecurityForm.value,
      healthContribution: this.healthContribution,
      pensionContribution: this.pensionContribution,
    };
    this.saveSocialSecurity.emit(updatedSocialSecurity);
  }

  loadARLAffiliationClasses(): void {
    this.arlAffiliationClassService.getAll().subscribe({
      next: (classes) => (this.arlAffiliationClasses = classes),
      error: (error) => {
        this.alert.error(error.error?.detail ?? 'Error al cargar las clases de afiliación ARL');
      },
    });
  }

  loadCompensationFunds(): void {
    this.compensationFundService.getAll().subscribe({
      next: (funds) => (this.compensationFunds = funds),
      error: (error) => {
        this.alert.error(error.error?.detail ?? 'Error al cargar las cajas de compensación');
      },
    });
  }

  loadSocialSecurityContribution(): void {
    if (this.report && this.report.id) {
      this.socialSecurityContributionService
        .getByMonthlyReportId(this.report.id)
        .subscribe({
          next: (socialSecurity) => {
            this.socialSecurityContribution = socialSecurity;
            this.prefillForm();
          },
          error: (error) => {
            if (error.status !== 404) {
              this.alert.error(
                error.error?.detail ?? 'Error al cargar la información de seguridad social',
              );
            }
          },
        });
    }
  }

  prefillForm(): void {
    if (this.socialSecurityContribution) {
      const hasCompensationFund =
        !!this.socialSecurityContribution.compensationFund;

      if (hasCompensationFund) {
        const compensationFundControl =
          this.socialSecurityForm.get('compensationFund');
        const compensationFundContributionControl = this.socialSecurityForm.get(
          'compensationFundContribution',
        );

        compensationFundControl?.setValidators(Validators.required);
        compensationFundContributionControl?.setValidators([
          Validators.required,
          Validators.min(0),
        ]);
      }

      this.socialSecurityForm.patchValue(
        {
          paymentFormNumber: this.socialSecurityContribution.paymentFormNumber,
          arlAffiliationClass:
            this.socialSecurityContribution.arlAffiliationClass?.id,
          arlContribution: this.socialSecurityContribution.arlContribution,
          hasCompensationFund: hasCompensationFund,
          compensationFund:
            this.socialSecurityContribution.compensationFund?.id,
          compensationFundContribution:
            this.socialSecurityContribution.compensationFundContribution,
        },
        { emitEvent: false },
      );

      Object.keys(this.socialSecurityForm.controls).forEach((key) => {
        const control = this.socialSecurityForm.get(key);
        control?.updateValueAndValidity({ emitEvent: false });
      });

      this.socialSecurityForm.updateValueAndValidity();
    }
  }

  getSocialSecurityData(): SocialSecurityContribution {
    const formValue = this.socialSecurityForm.value;
    return {
      ...this.socialSecurityContribution,
      ...formValue,
      monthlyReportId: this.report.id || 0,
      healthContribution: this.healthContribution,
      pensionContribution: this.pensionContribution,
      arlContribution: formValue.arlContribution || 0,
      ibc: this.ibc,
      arlAffiliationClassId: formValue.arlAffiliationClass,
      compensationFundId: formValue.hasCompensationFund
        ? formValue.compensationFund
        : null,
      compensationFundContribution: formValue.hasCompensationFund
        ? formValue.compensationFundContribution
        : null,
    };
  }

  loadPeriodPayment(): void {
    if (this.report && this.contractorContractId) {
      this.MonthlyReportService.getPreviousPeriodPayment(
        this.contractorContractId,
        this.report.reportNumber,
      ).subscribe({
        next: (payment) => {
          this.previousPaymentValue = payment || 0;
          this.calculateContributions();
        },
        error: (error) => {
          this.alert.error(
            error.error?.detail ?? 'Error al cargar el valor a cobrar del periodo anterior',
          );
        },
      });
    }
  }

  downloadCertificate(): void {
    if (this.socialSecurityContribution?.certificateFileUrl) {
      window.open(this.socialSecurityContribution.certificateFileUrl, '_blank');
    }
  }

  getArlAffiliationClassName(id: number): string {
    return (
      this.arlAffiliationClasses.find((arlClass) => arlClass.id === id)?.name ||
      ''
    );
  }

  getCompensationFundName(id: number): string {
    return this.compensationFunds.find((fund) => fund.id === id)?.name || '';
  }
}
