{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { StatusService } from './status.service';\ndescribe('StatusService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/statuses`;\n  const mockStatus = {\n    id: 1,\n    name: 'Test Status'\n  };\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [StatusService]\n    });\n    service = TestBed.inject(StatusService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all statuses', () => {\n      const mockStatuses = [mockStatus];\n      service.getAll().subscribe(statuses => {\n        expect(statuses).toEqual(mockStatuses);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockStatuses);\n    });\n  });\n  describe('getById', () => {\n    it('should return a status by id', () => {\n      const id = 1;\n      service.getById(id).subscribe(status => {\n        expect(status).toEqual(mockStatus);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockStatus);\n    });\n  });\n  describe('create', () => {\n    it('should create a new status', () => {\n      const newStatus = {\n        name: 'New Status'\n      };\n      service.create(newStatus).subscribe(status => {\n        expect(status).toEqual(mockStatus);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newStatus);\n      req.flush(mockStatus);\n    });\n  });\n  describe('update', () => {\n    it('should update a status', () => {\n      const id = 1;\n      const updateData = {\n        name: 'Updated Status'\n      };\n      service.update(id, updateData).subscribe(status => {\n        expect(status).toEqual(mockStatus);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockStatus);\n    });\n  });\n  describe('delete', () => {\n    it('should delete a status', () => {\n      const id = 1;\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n  describe('getByName', () => {\n    it('should return a status by name', () => {\n      const name = 'Test Status';\n      service.getByName(name).subscribe(status => {\n        expect(status).toEqual(mockStatus);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockStatus);\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "StatusService", "describe", "service", "httpMock", "apiUrl", "mockStatus", "id", "name", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockStatuses", "getAll", "subscribe", "statuses", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "getById", "status", "newStatus", "create", "body", "updateData", "update", "delete", "nothing", "getByName"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\services\\status.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { Status } from '@contract-management/models/status.model';\nimport { environment } from '@env';\nimport { StatusService } from './status.service';\n\ndescribe('StatusService', () => {\n  let service: StatusService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/statuses`;\n\n  const mockStatus: Status = {\n    id: 1,\n    name: 'Test Status',\n  };\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [StatusService],\n    });\n    service = TestBed.inject(StatusService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all statuses', () => {\n      const mockStatuses = [mockStatus];\n\n      service.getAll().subscribe((statuses) => {\n        expect(statuses).toEqual(mockStatuses);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockStatuses);\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a status by id', () => {\n      const id = 1;\n\n      service.getById(id).subscribe((status) => {\n        expect(status).toEqual(mockStatus);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockStatus);\n    });\n  });\n\n  describe('create', () => {\n    it('should create a new status', () => {\n      const newStatus: Omit<Status, 'id'> = {\n        name: 'New Status',\n      };\n\n      service.create(newStatus).subscribe((status) => {\n        expect(status).toEqual(mockStatus);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newStatus);\n      req.flush(mockStatus);\n    });\n  });\n\n  describe('update', () => {\n    it('should update a status', () => {\n      const id = 1;\n      const updateData: Partial<Status> = {\n        name: 'Updated Status',\n      };\n\n      service.update(id, updateData).subscribe((status) => {\n        expect(status).toEqual(mockStatus);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockStatus);\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete a status', () => {\n      const id = 1;\n\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n\n  describe('getByName', () => {\n    it('should return a status by name', () => {\n      const name = 'Test Status';\n\n      service.getByName(name).subscribe((status) => {\n        expect(status).toEqual(mockStatus);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockStatus);\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,aAAa,QAAQ,kBAAkB;AAEhDC,QAAQ,CAAC,eAAe,EAAE,MAAK;EAC7B,IAAIC,OAAsB;EAC1B,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,WAAW;EAE/C,MAAMC,UAAU,GAAW;IACzBC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE;GACP;EAEDC,UAAU,CAAC,MAAK;IACdV,OAAO,CAACW,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACd,uBAAuB,CAAC;MAClCe,SAAS,EAAE,CAACX,aAAa;KAC1B,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACc,MAAM,CAACZ,aAAa,CAAC;IACvCG,QAAQ,GAAGL,OAAO,CAACc,MAAM,CAACf,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFgB,SAAS,CAAC,MAAK;IACbV,QAAQ,CAACW,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACd,OAAO,CAAC,CAACe,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFhB,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,4BAA4B,EAAE,MAAK;MACpC,MAAMG,YAAY,GAAG,CAACb,UAAU,CAAC;MAEjCH,OAAO,CAACiB,MAAM,EAAE,CAACC,SAAS,CAAEC,QAAQ,IAAI;QACtCL,MAAM,CAACK,QAAQ,CAAC,CAACC,OAAO,CAACJ,YAAY,CAAC;MACxC,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,YAAY,CAAC;IACzB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjB,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBc,EAAE,CAAC,8BAA8B,EAAE,MAAK;MACtC,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAAC2B,OAAO,CAACvB,EAAE,CAAC,CAACc,SAAS,CAAEU,MAAM,IAAI;QACvCd,MAAM,CAACc,MAAM,CAAC,CAACR,OAAO,CAACjB,UAAU,CAAC;MACpC,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvB,UAAU,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,4BAA4B,EAAE,MAAK;MACpC,MAAMgB,SAAS,GAAuB;QACpCxB,IAAI,EAAE;OACP;MAEDL,OAAO,CAAC8B,MAAM,CAACD,SAAS,CAAC,CAACX,SAAS,CAAEU,MAAM,IAAI;QAC7Cd,MAAM,CAACc,MAAM,CAAC,CAACR,OAAO,CAACjB,UAAU,CAAC;MACpC,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACS,SAAS,CAAC;MAC3CR,GAAG,CAACK,KAAK,CAACvB,UAAU,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,wBAAwB,EAAE,MAAK;MAChC,MAAMT,EAAE,GAAG,CAAC;MACZ,MAAM4B,UAAU,GAAoB;QAClC3B,IAAI,EAAE;OACP;MAEDL,OAAO,CAACiC,MAAM,CAAC7B,EAAE,EAAE4B,UAAU,CAAC,CAACd,SAAS,CAAEU,MAAM,IAAI;QAClDd,MAAM,CAACc,MAAM,CAAC,CAACR,OAAO,CAACjB,UAAU,CAAC;MACpC,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACY,UAAU,CAAC;MAC5CX,GAAG,CAACK,KAAK,CAACvB,UAAU,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,wBAAwB,EAAE,MAAK;MAChC,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAACkC,MAAM,CAAC9B,EAAE,CAAC,CAACc,SAAS,CAAC,MAAK;QAChCJ,MAAM,EAAE,CAACqB,OAAO,EAAE;MACpB,CAAC,CAAC;MAEF,MAAMd,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF3B,QAAQ,CAAC,WAAW,EAAE,MAAK;IACzBc,EAAE,CAAC,gCAAgC,EAAE,MAAK;MACxC,MAAMR,IAAI,GAAG,aAAa;MAE1BL,OAAO,CAACoC,SAAS,CAAC/B,IAAI,CAAC,CAACa,SAAS,CAAEU,MAAM,IAAI;QAC3Cd,MAAM,CAACc,MAAM,CAAC,CAACR,OAAO,CAACjB,UAAU,CAAC;MACpC,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,SAASG,IAAI,EAAE,CAAC;MACxDS,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvB,UAAU,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}