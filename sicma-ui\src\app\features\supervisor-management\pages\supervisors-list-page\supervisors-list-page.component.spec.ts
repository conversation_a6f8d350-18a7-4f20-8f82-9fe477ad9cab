import { HttpClientTestingModule } from '@angular/common/http/testing';
import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import {
  MatDialog,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { AlertService } from '@shared/services/alert.service';
import { SupervisorDialogComponent } from '@supervisor-management/components/supervisor-dialog/supervisor-dialog.component';
import { Supervisor } from '@supervisor-management/models/supervisor.model';
import { SupervisorService } from '@supervisor-management/services/supervisor.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { of, throwError } from 'rxjs';
import { SupervisorsListPageComponent } from './supervisors-list-page.component';

describe('SupervisorsListPageComponent', () => {
  let component: SupervisorsListPageComponent;
  let fixture: ComponentFixture<SupervisorsListPageComponent>;
  let supervisorService: jasmine.SpyObj<SupervisorService>;
  let dialog: jasmine.SpyObj<MatDialog>;
  let alertService: jasmine.SpyObj<AlertService>;
  let spinnerService: jasmine.SpyObj<NgxSpinnerService>;

  const mockSupervisor: Supervisor = {
    id: 1,
    fullName: 'Test Supervisor',
    idNumber: 123456789,
    position: 'Test Position',
    email: '<EMAIL>',
    idType: { id: 1, name: 'CC' },
  };

  beforeEach(() => {
    const supervisorServiceSpy = jasmine.createSpyObj('SupervisorService', [
      'getAll',
    ]);
    const dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);
    const alertServiceSpy = jasmine.createSpyObj('AlertService', ['error']);
    const spinnerServiceSpy = jasmine.createSpyObj('NgxSpinnerService', [
      'show',
      'hide',
    ]);

    TestBed.configureTestingModule({
      imports: [
        SupervisorsListPageComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        MatDialogModule,
        MatPaginatorModule,
        MatSortModule,
      ],
      providers: [
        { provide: SupervisorService, useValue: supervisorServiceSpy },
        { provide: MatDialog, useValue: dialogSpy },
        { provide: AlertService, useValue: alertServiceSpy },
        { provide: NgxSpinnerService, useValue: spinnerServiceSpy },
      ],
    });

    fixture = TestBed.createComponent(SupervisorsListPageComponent);
    component = fixture.componentInstance;
    supervisorService = TestBed.inject(
      SupervisorService,
    ) as jasmine.SpyObj<SupervisorService>;
    dialog = TestBed.inject(MatDialog) as jasmine.SpyObj<MatDialog>;
    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;
    spinnerService = TestBed.inject(
      NgxSpinnerService,
    ) as jasmine.SpyObj<NgxSpinnerService>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with empty data', () => {
    expect(component.dataSource.data).toEqual([]);
  });

  it('should load supervisors on init', fakeAsync(() => {
    const mockSupervisors = [mockSupervisor];
    supervisorService.getAll.and.returnValue(of(mockSupervisors));

    component.ngOnInit();
    tick();

    expect(spinnerService.show).toHaveBeenCalled();
    expect(supervisorService.getAll).toHaveBeenCalled();
    expect(component.dataSource.data).toEqual(mockSupervisors);
    expect(spinnerService.hide).toHaveBeenCalled();
  }));

  it('should handle error when loading supervisors', fakeAsync(() => {
    supervisorService.getAll.and.returnValue(throwError(() => new Error()));

    component.ngOnInit();
    tick();

    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar supervisores',
    );
    expect(spinnerService.hide).toHaveBeenCalled();
  }));

  it('should set up paginator and sort after view init', () => {
    supervisorService.getAll.and.returnValue(of([]));
    fixture.detectChanges();
    component.ngAfterViewInit();
    expect(component.dataSource.paginator).toBeTruthy();
    expect(component.dataSource.sort).toBeTruthy();
  });

  it('should open dialog to create new supervisor', () => {
    const dialogRef = {
      afterClosed: () => of(mockSupervisor),
    } as MatDialogRef<SupervisorDialogComponent>;

    dialog.open.and.returnValue(dialogRef);

    component.openSupervisorDialog();

    expect(dialog.open).toHaveBeenCalledWith(SupervisorDialogComponent, {
      width: '95%',
      maxWidth: '800px',
      height: 'auto',
      maxHeight: '90vh',
      data: undefined,
    });
  });

  it('should open dialog to edit existing supervisor', () => {
    const dialogRef = {
      afterClosed: () => of(mockSupervisor),
    } as MatDialogRef<SupervisorDialogComponent>;

    dialog.open.and.returnValue(dialogRef);

    component.openSupervisorDialog(mockSupervisor);

    expect(dialog.open).toHaveBeenCalledWith(SupervisorDialogComponent, {
      width: '95%',
      maxWidth: '800px',
      height: 'auto',
      maxHeight: '90vh',
      data: mockSupervisor,
    });
  });

  it('should update data source when creating new supervisor', () => {
    const dialogRef = {
      afterClosed: () => of(mockSupervisor),
    } as MatDialogRef<SupervisorDialogComponent>;

    dialog.open.and.returnValue(dialogRef);
    component.dataSource.data = [];

    component.openSupervisorDialog();

    expect(component.dataSource.data).toEqual([mockSupervisor]);
  });

  it('should update data source when editing supervisor', () => {
    const updatedSupervisor = { ...mockSupervisor, fullName: 'Updated Name' };
    const dialogRef = {
      afterClosed: () => of(updatedSupervisor),
    } as MatDialogRef<SupervisorDialogComponent>;

    dialog.open.and.returnValue(dialogRef);
    component.dataSource.data = [mockSupervisor];

    component.openSupervisorDialog(mockSupervisor);

    expect(component.dataSource.data).toEqual([updatedSupervisor]);
  });

  it('should not update data source when dialog is closed without result', () => {
    const dialogRef = {
      afterClosed: () => of(undefined),
    } as MatDialogRef<SupervisorDialogComponent>;

    dialog.open.and.returnValue(dialogRef);
    const initialData = [mockSupervisor];
    component.dataSource.data = initialData;

    component.openSupervisorDialog(mockSupervisor);

    expect(component.dataSource.data).toEqual(initialData);
  });

  it('should apply filter correctly', () => {
    const event = new Event('input');
    Object.defineProperty(event, 'target', { value: { value: 'test' } });

    component.applyFilter(event);

    expect(component.dataSource.filter).toBe('test');
  });

  it('should reset to first page when filtering', () => {
    const event = new Event('input');
    Object.defineProperty(event, 'target', { value: { value: 'test' } });

    supervisorService.getAll.and.returnValue(of([]));
    fixture.detectChanges();
    component.ngAfterViewInit();

    const paginatorSpy = spyOn(component.dataSource.paginator!, 'firstPage');

    component.applyFilter(event);

    expect(paginatorSpy).toHaveBeenCalled();
  });
});