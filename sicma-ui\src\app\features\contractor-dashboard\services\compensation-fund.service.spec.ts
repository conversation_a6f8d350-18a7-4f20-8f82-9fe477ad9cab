import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { CompensationFund } from '@contractor-dashboard/models/compensation-fund.model';
import { environment } from '@env';
import { CompensationFundService } from './compensation-fund.service';

describe('CompensationFundService', () => {
  let service: CompensationFundService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/compensation-funds`;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [CompensationFundService],
    });
    service = TestBed.inject(CompensationFundService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  const mockCompensationFund: CompensationFund = {
    id: 1,
    name: 'Comfama',
  };

  describe('getAll', () => {
    it('should return all compensation funds', () => {
      const mockCompensationFunds = [mockCompensationFund];

      service.getAll().subscribe((compensationFunds) => {
        expect(compensationFunds).toEqual(mockCompensationFunds);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockCompensationFunds);
    });

    it('should handle error when getting all compensation funds', () => {
      service.getAll().subscribe({
        error: (error) => {
          expect(error.status).toBe(500);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Server error'), { status: 500 });
    });
  });

  describe('getById', () => {
    it('should return a compensation fund by id', () => {
      service.getById(1).subscribe((compensationFund) => {
        expect(compensationFund).toEqual(mockCompensationFund);
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('GET');
      req.flush(mockCompensationFund);
    });

    it('should handle error when getting compensation fund by id', () => {
      service.getById(999).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/999`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('create', () => {
    const newCompensationFund: Omit<CompensationFund, 'id'> = {
      name: 'New Fund',
    };

    it('should create a new compensation fund', () => {
      service.create(newCompensationFund).subscribe((compensationFund) => {
        expect(compensationFund).toEqual(mockCompensationFund);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newCompensationFund);
      req.flush(mockCompensationFund);
    });

    it('should handle error when creating compensation fund', () => {
      service.create(newCompensationFund).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('update', () => {
    const updateData: Partial<CompensationFund> = {
      name: 'Updated Name',
    };

    it('should update a compensation fund', () => {
      service.update(1, updateData).subscribe((compensationFund) => {
        expect(compensationFund).toEqual({
          ...mockCompensationFund,
          ...updateData,
        });
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush({ ...mockCompensationFund, ...updateData });
    });

    it('should handle error when updating compensation fund', () => {
      service.update(1, updateData).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('delete', () => {
    it('should delete a compensation fund', () => {
      service.delete(1).subscribe((response) => {
        expect(response).toBeNull();
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });

    it('should handle error when deleting compensation fund', () => {
      service.delete(1).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('getByName', () => {
    it('should return a compensation fund by name', () => {
      const name = 'Comfama';

      service.getByName(name).subscribe((compensationFund) => {
        expect(compensationFund).toEqual(mockCompensationFund);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockCompensationFund);
    });

    it('should handle error when getting compensation fund by name', () => {
      const name = 'NonExistent';

      service.getByName(name).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });
});