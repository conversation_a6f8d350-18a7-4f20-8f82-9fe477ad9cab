import { <PERSON><PERSON><PERSON><PERSON><PERSON>, DatePipe, DecimalPipe, PercentPipe } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatAutocompleteModule, MatAutocompleteTrigger } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckbox } from '@angular/material/checkbox';
import { <PERSON><PERSON><PERSON><PERSON>, MatFormField, MatLabel } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput } from '@angular/material/input';
import { MatOption } from '@angular/material/core';
import { ContractDetails } from '@contract-management/models/contract-details.model';
import { SupervisorContract } from '@contract-management/models/supervisor-contract.model';
import { ContractService } from '@contract-management/services/contract.service';
import { SupervisorContractService } from '@contract-management/services/supervisor-contract.service';
import { MonthlyReport } from '@contractor-dashboard/models/monthly-report.model';
import { Payment } from '@contractor-dashboard/models/payment.model';
import { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';
import { PaymentService } from '@contractor-dashboard/services/payment.service';
import { AlertService } from '@shared/services/alert.service';
import { Supervisor } from '@supervisor-management/models/supervisor.model';
import { SupervisorService } from '@supervisor-management/services/supervisor.service';
import { Observable, forkJoin, map, of, startWith } from 'rxjs';
import { CcpsDistributionComponent } from './ccps-distribution/ccps-distribution.component';

@Component({
  selector: 'app-monthly-report-basic-data',
  templateUrl: './monthly-report-basic-data.component.html',
  styleUrl: './monthly-report-basic-data.component.scss',
  providers: [DatePipe],
  standalone: true,
  imports: [
    MatIcon,
    DecimalPipe,
    ReactiveFormsModule,
    MatCheckbox,
    MatFormField,
    MatLabel,
    MatInput,
    MatError,
    MatAutocompleteModule,
    MatButtonModule,
    AsyncPipe,
    CcpsDistributionComponent,
    MatOption,
    MatAutocompleteTrigger,
    PercentPipe,
  ],
})
export class MonthlyReportBasicDataComponent implements OnInit {
  @Input() report!: MonthlyReport;
  @Output() reportChange = new EventEmitter<MonthlyReport>();
  @Output() formValidityChange = new EventEmitter<boolean>();
  @Input() isSupervisor = false;
  @ViewChild(CcpsDistributionComponent)
  ccpsDistribution!: CcpsDistributionComponent;

  year = 0;
  month = '';
  contractDetails: ContractDetails | null = null;
  totalPaid = 0;
  pendingBalance = 0;
  progressPercentage = 0;
  supervisors: Supervisor[] = [];
  filteredSupervisors!: Observable<Supervisor[]>;
  supervisorContract: SupervisorContract | null = null;
  supervisorContracts: SupervisorContract[] = [];
  isEditingSupervisor = false;
  currentEditingSupervisorId: number | null = null;
  isCcpsValid = false;

  invoiceForm: FormGroup = this.fb.group({
    hasElectronicInvoice: [false],
    invoiceNumber: [{ value: '', disabled: true }],
  });

  supervisorForm: FormGroup = this.fb.group({
    supervisor: ['', Validators.required],
  });

  constructor(
    private readonly contractService: ContractService,
    private readonly alert: AlertService,
    private readonly monthlyReportService: MonthlyReportService,
    private readonly paymentService: PaymentService,
    private readonly supervisorService: SupervisorService,
    private readonly supervisorContractService: SupervisorContractService,
    private readonly datePipe: DatePipe,
    private readonly fb: FormBuilder,
  ) {}

  ngOnInit(): void {
    this.loadContractDetails();
    this.setReportPeriod();
    this.initializeForm();
    this.setupFormValidation();
    this.loadSupervisors();
    this.setupSupervisorAutocomplete();
    if (this.report.contractorContract?.contract?.id) {
      this.loadSupervisorContracts(this.report.contractorContract.contract.id);
    }
  }

  private loadSupervisors(): void {
    this.supervisorService.getAll().subscribe({
      next: (supervisors) => {
        this.supervisors = supervisors;
      },
      error: (error) => {
        this.alert.error(error.error?.detail ?? 'Error al cargar los supervisores');
      },
    });
  }

  private setupSupervisorAutocomplete(): void {
    this.filteredSupervisors = this.supervisorForm
      .get('supervisor')!
      .valueChanges.pipe(
        startWith(''),
        map((value) => {
          const name = typeof value === 'string' ? value : value?.fullName;
          return name
            ? this._filterSupervisors(name)
            : this.supervisors.slice();
        }),
      );
  }

  private _filterSupervisors(value: string): Supervisor[] {
    const filterValue = value.toLowerCase();
    return this.supervisors.filter(
      (supervisor) =>
        supervisor.fullName.toLowerCase().includes(filterValue) ||
        supervisor.idNumber.toString().toLowerCase().includes(filterValue),
    );
  }

  displaySupervisor(supervisor: Supervisor): string {
    return supervisor ? `${supervisor.fullName} (${supervisor.idNumber})` : '';
  }

  addNewSupervisor(): void {
    if (this.supervisorContracts.length >= 2) {
      this.alert.warning(
        'Límite excedido',
        'Solo se pueden asociar un máximo de 2 supervisores por contrato.',
      );
      return;
    }
    this.currentEditingSupervisorId = null;
    this.supervisorForm.reset();
    this.isEditingSupervisor = true;
  }

  editSupervisor(supervisorContract: SupervisorContract): void {
    this.currentEditingSupervisorId = supervisorContract.id;
    const currentSupervisor = this.supervisors.find(
      (s) => s.id === supervisorContract.supervisorId,
    );
    if (currentSupervisor) {
      this.supervisorForm.patchValue({ supervisor: currentSupervisor });
    }
    this.isEditingSupervisor = true;
  }

  removeSupervisor(supervisorContractId: number): void {
    this.alert
      .confirm(
        '¿Está seguro de eliminar este supervisor?',
        'Esta acción no se puede deshacer',
      )
      .then((isConfirmed) => {
        if (isConfirmed) {
          this.supervisorContractService
            .delete(supervisorContractId)
            .subscribe({
              next: () => {
                this.alert.success('Supervisor eliminado exitosamente');
                if (this.report.contractorContract?.contract?.id) {
                  this.loadSupervisorContracts(
                    this.report.contractorContract.contract.id,
                  );
                }
              },
              error: (error) => {
                this.alert.error(error.error?.detail ?? 'Error al eliminar el supervisor');
              },
            });
        }
      });
  }

  toggleSupervisorEdit(): void {
    this.isEditingSupervisor = !this.isEditingSupervisor;
    this.currentEditingSupervisorId = null;
    this.supervisorForm.reset();
  }

  private loadSupervisorContracts(contractId: number): void {
    this.supervisorContractService.getByContractId(contractId).subscribe({
      next: (contracts) => {
        this.supervisorContracts = contracts;

        this.supervisorContracts.forEach((contract, index) => {
          this.supervisorService.getById(contract.supervisorId).subscribe({
            next: (supervisor) => {
              this.supervisorContracts[index] = {
                ...this.supervisorContracts[index],
                supervisor: supervisor,
              };
            },
            error: (error) => {
              this.alert.error(
                error.error?.detail ?? `Error al cargar información del supervisor ${contract.supervisorId}`
              );
            },
          });
        });
      },
      error: (error) => {
        this.alert.error(error.error?.detail ?? 'Error al cargar los contratos de supervisores');
      },
    });
  }

  saveSupervisor(): void {
    if (
      this.supervisorForm.valid &&
      this.report.contractorContract?.contract?.id
    ) {
      const selectedSupervisor = this.supervisorForm.get('supervisor')?.value;
      const contractId = this.report.contractorContract.contract.id;

      if (this.currentEditingSupervisorId) {
        this.supervisorContractService
          .update(this.currentEditingSupervisorId, {
            id: this.currentEditingSupervisorId,
            supervisorId: selectedSupervisor.id,
            contractId: contractId,
          })
          .subscribe({
            next: () => {
              this.alert.success('Supervisor actualizado exitosamente');
              this.isEditingSupervisor = false;
              this.loadSupervisorContracts(contractId);
              this.loadContractDetails();
            },
            error: (error) => {
              this.alert.error(error.error?.detail ?? 'Error al actualizar el supervisor');
            },
          });
      } else {
        if (this.supervisorContracts.length >= 2) {
          this.alert.warning(
            'Límite excedido',
            'Solo se pueden asociar un máximo de 2 supervisores por contrato.',
          );
          return;
        }

        const supervisorExists = this.supervisorContracts.some(
          (contract) => contract.supervisorId === selectedSupervisor.id,
        );

        if (supervisorExists) {
          this.alert.warning(
            'Supervisor ya asignado',
            'Este supervisor ya está asignado a este contrato.',
          );
          return;
        }

        this.supervisorContractService
          .create({
            supervisorId: selectedSupervisor.id,
            contractId: contractId,
          })
          .subscribe({
            next: () => {
              this.alert.success('Supervisor asignado exitosamente');
              this.isEditingSupervisor = false;
              this.loadSupervisorContracts(contractId);
              this.loadContractDetails();
            },
            error: (error) => {
              if (
                error?.error?.detail?.includes(
                  'Multiple supervisors are associated',
                )
              ) {
                this.alert.warning(
                  'Límite excedido',
                  'Solo se pueden asociar un máximo de 2 supervisores por contrato.',
                );
              } else if (error?.error?.detail?.includes('already associated')) {
                this.alert.warning(
                  'Supervisor ya asignado',
                  'Este supervisor ya está asignado a este contrato.',
                );
              } else {
                this.alert.error(error.error?.detail ?? 'Error al asignar el supervisor');
              }
            },
          });
      }
    }
  }

  private setupFormValidation(): void {
    this.invoiceForm
      .get('hasElectronicInvoice')
      ?.valueChanges.subscribe((checked) => {
        const invoiceNumberControl = this.invoiceForm.get('invoiceNumber');
        if (checked) {
          invoiceNumberControl?.enable();
          invoiceNumberControl?.setValidators([
            Validators.required,
            Validators.min(0),
            Validators.pattern(/^\d+$/),
          ]);
        } else {
          invoiceNumberControl?.disable();
          invoiceNumberControl?.clearValidators();
          invoiceNumberControl?.setValue('');
        }
        invoiceNumberControl?.updateValueAndValidity({ emitEvent: false });
        this.invoiceForm.updateValueAndValidity();
      });

    this.invoiceForm.statusChanges.subscribe(() => {
      this.checkFormValidity();
    });

    this.invoiceForm.valueChanges.subscribe(() => {
      this.updateReport();
    });
  }

  onCcpsValidityChange(isValid: boolean): void {
    this.isCcpsValid = isValid;
    this.checkFormValidity();
  }

  onCcpsSaveComplete(): void {
    this.alert.success('Distribución de CCPs guardada exitosamente');
  }

  private checkFormValidity(): void {
    const hasElectronicInvoice = this.invoiceForm.get(
      'hasElectronicInvoice',
    )?.value;
    const isInvoiceFormValid = !hasElectronicInvoice || this.invoiceForm.valid;
    this.formValidityChange.emit(isInvoiceFormValid && this.isCcpsValid);
  }

  saveData(): Observable<boolean> {
    if (this.ccpsDistribution) {
      return this.ccpsDistribution.saveCcps();
    }
    return of(true);
  }

  private initializeForm(): void {
    const hasElectronicInvoice = this.report.hasElectronicInvoice || false;
    const invoiceNumber = this.report.invoiceNumber || '';

    const invoiceNumberControl = this.invoiceForm.get('invoiceNumber');
    if (hasElectronicInvoice) {
      invoiceNumberControl?.enable();
      invoiceNumberControl?.setValidators([
        Validators.required,
        Validators.min(0),
        Validators.pattern(/^\d+$/),
      ]);
    } else {
      invoiceNumberControl?.disable();
      invoiceNumberControl?.clearValidators();
    }

    this.invoiceForm.patchValue(
      {
        hasElectronicInvoice,
        invoiceNumber,
      },
      { emitEvent: false },
    );

    if (this.isSupervisor) {
      this.invoiceForm.get('hasElectronicInvoice')?.disable();
    }

    invoiceNumberControl?.updateValueAndValidity({ emitEvent: false });
    this.invoiceForm.updateValueAndValidity();
  }

  setReportPeriod(): void {
    if (this.report.startDate) {
      const startDate = new Date(this.report.startDate);
      this.year = startDate.getFullYear();
      this.month = this.getMonthName(startDate.getMonth());
    }
  }

  getMonthName(monthIndex: number): string {
    const monthNames = [
      'Enero',
      'Febrero',
      'Marzo',
      'Abril',
      'Mayo',
      'Junio',
      'Julio',
      'Agosto',
      'Septiembre',
      'Octubre',
      'Noviembre',
      'Diciembre',
    ];
    return monthNames[monthIndex];
  }

  formatDate(date: string | Date | undefined): string {
    if (!date) return 'N/A';
    return this.datePipe.transform(date, 'dd/MM/yyyy') || 'N/A';
  }

  formatCurrency(value: number | undefined): string {
    if (value === undefined) return 'N/A';
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
    }).format(value);
  }

  loadContractDetails(): void {
    if (this.report.contractorContract?.contract?.id) {
      this.contractService
        .getDetailsById(this.report.contractorContract.contract.id)
        .subscribe({
          next: (details: ContractDetails | null) => {
            if (details) {
              this.contractDetails = details;
              this.contractDetails.totalValue =
                (this.contractDetails.initialValue ?? 0) +
                (this.contractDetails.totalAdditionsValue ?? 0) -
                (this.contractDetails.totalReductionsValue ?? 0);
            }
            this.loadPayments();
          },
          error: (error) => {
            this.alert.error(error.error?.detail ?? 'Error al obtener detalles del contrato');
            this.loadPayments();
          },
        });
    } else {
      this.loadPayments();
    }
  }

  private updateReport(): void {
    if (this.report && this.contractDetails) {
      const updatedReport = {
        ...this.report,
        contractNumber: this.contractDetails.contractNumber,
        contractYear: Number(this.contractDetails.contractYear?.year),
        contractDurationDays: this.contractDetails.durationDays,
        contractStartDate: this.contractDetails.startDate,
        contractEndDate: this.contractDetails.endDate,
        contractTotalValue: this.contractDetails.totalValue,
        contractInitialValue: this.contractDetails.initialValue,
        contractTotalAdditionsValue: this.contractDetails.totalAdditionsValue,
        contractTotalReductionsValue: this.contractDetails.totalReductionsValue,
        contractorFullName: this.contractDetails.fullName,
        contractorIdNumber: this.contractDetails.contractorIdNumber,
        contractorEmail: this.contractDetails.contractorEmail,
        contractorPhone: this.contractDetails.contractorPhone,
        contractDependency: this.contractDetails.dependencyName,
        contractGroup: this.contractDetails.groupName,
        contractorBankName: this.contractDetails.bankName,
        contractorAccountNumber: this.contractDetails.accountNumber,
        contractorAccountType: this.contractDetails.accountTypeName,
        supervisorFullName: this.contractDetails.supervisorFullName,
        supervisorIdNumber: this.contractDetails.supervisorIdNumber,
        supervisorPosition: this.contractDetails.supervisorPosition,
        totalValue: this.report.totalValue || this.contractDetails.totalValue,
        paidValue: this.totalPaid,
        pendingValue: this.pendingBalance,
        executionPercentage: this.progressPercentage,
        hasElectronicInvoice: this.invoiceForm.get('hasElectronicInvoice')
          ?.value,
        invoiceNumber: this.invoiceForm.get('hasElectronicInvoice')?.value
          ? this.invoiceForm.get('invoiceNumber')?.value
          : 0,
      };

      this.report = updatedReport;
      this.reportChange.emit(updatedReport);
    }
  }

  loadPayments(): void {
    if (!this.report?.contractorContractId) {
      this.updateReport();
      return;
    }

    this.monthlyReportService
      .getByContractorContractId(this.report.contractorContractId)
      .subscribe({
        next: (reports: MonthlyReport[]) => {
          const paymentObservables = reports
            .filter((r) => r.id)
            .map((r) => this.paymentService.getByMonthlyReportId(r.id));

          if (paymentObservables.length === 0) {
            this.totalPaid = 0;
            if (this.contractDetails) {
              this.pendingBalance = this.contractDetails.totalValue ?? 0;
              this.progressPercentage = 0;
            }
            this.updateReport();
            return;
          }

          forkJoin(paymentObservables).subscribe({
            next: (paymentsArray: Payment[][]) => {
              const allPayments = paymentsArray.flat();
              const sortedPayments = allPayments.sort(
                (a, b) => a.paymentNumber - b.paymentNumber,
              );

              this.totalPaid = sortedPayments
                .filter((p) => p.paymentNumber < this.report.reportNumber)
                .reduce((sum, payment) => sum + payment.value, 0);

              if (this.contractDetails) {
                this.pendingBalance =
                  (this.contractDetails.totalValue ?? 0) - this.totalPaid;

                const totalPaidWithCurrent =
                  this.totalPaid + (this.report.totalValue || 0);

                this.progressPercentage =
                  this.contractDetails.totalValue &&
                  this.contractDetails.totalValue > 0
                    ? totalPaidWithCurrent / this.contractDetails.totalValue
                    : 0;
              }
              this.updateReport();
            },
            error: (error) => {
              this.alert.error(error.error?.detail ?? 'Error al cargar los pagos');
              this.updateReport();
            },
          });
        },
        error: (error) => {
          this.alert.error(error.error?.detail ?? 'Error al cargar los informes');
          this.updateReport();
        },
      });
  }
}
