import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { MonthlyReportApprovalDialogComponent } from './monthly-report-approval-dialog.component';
import { AlertService } from '@shared/services/alert.service';
import { ReactiveFormsModule } from '@angular/forms';
import { FormBuilder } from '@angular/forms';

describe('MonthlyReportApprovalDialogComponent', () => {
  let component: MonthlyReportApprovalDialogComponent;
  let fixture: ComponentFixture<MonthlyReportApprovalDialogComponent>;
  let dialogRefSpy: jasmine.SpyObj<
    MatDialogRef<MonthlyReportApprovalDialogComponent>
  >;
  let alertServiceSpy: jasmine.SpyObj<AlertService>;

  beforeEach(() => {
    dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close']);
    alertServiceSpy = jasmine.createSpyObj('AlertService', ['confirm']);

    TestBed.configureTestingModule({
      imports: [
        MonthlyReportApprovalDialogComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        ReactiveFormsModule,
      ],
      providers: [
        { provide: MatDialogRef, useValue: dialogRefSpy },
        { provide: MAT_DIALOG_DATA, useValue: { isRejection: false } },
        { provide: AlertService, useValue: alertServiceSpy },
        FormBuilder,
      ],
    });
    fixture = TestBed.createComponent(MonthlyReportApprovalDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with correct title and button text for approval', () => {
    expect(component.dialogTitle).toBe('Aprobar Informe');
    expect(component.confirmButtonText).toBe('Aprobar');
    expect(component.confirmButtonColor).toBe('primary');
  });

  it('should initialize with correct title and button text for rejection', () => {
    TestBed.resetTestingModule();
    TestBed.configureTestingModule({
      imports: [
        MonthlyReportApprovalDialogComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        ReactiveFormsModule,
      ],
      providers: [
        { provide: MatDialogRef, useValue: dialogRefSpy },
        { provide: MAT_DIALOG_DATA, useValue: { isRejection: true } },
        { provide: AlertService, useValue: alertServiceSpy },
        FormBuilder,
      ],
    });
    fixture = TestBed.createComponent(MonthlyReportApprovalDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    expect(component.dialogTitle).toBe('Rechazar Informe');
    expect(component.confirmButtonText).toBe('Rechazar');
    expect(component.confirmButtonColor).toBe('warn');
  });

  it('should close dialog when cancel is clicked', () => {
    component.onCancel();
    expect(dialogRefSpy.close).toHaveBeenCalled();
  });

  it('should mark form as touched when submitted with invalid data', async () => {
    spyOn(component.form, 'markAllAsTouched');
    component.form.controls['comments'].setValue('');
    await component.onSubmit();
    expect(component.form.markAllAsTouched).toHaveBeenCalled();
    expect(dialogRefSpy.close).not.toHaveBeenCalled();
  });

  it('should close with comments when form is valid and user confirms', async () => {
    const comments = 'Test comments';
    component.form.controls['comments'].setValue(comments);
    alertServiceSpy.confirm.and.resolveTo(true);

    await component.onSubmit();

    expect(alertServiceSpy.confirm).toHaveBeenCalled();
    expect(dialogRefSpy.close).toHaveBeenCalledWith(comments);
  });

  it('should not close when user cancels the confirmation', async () => {
    const comments = 'Test comments';
    component.form.controls['comments'].setValue(comments);
    alertServiceSpy.confirm.and.resolveTo(false);

    await component.onSubmit();

    expect(alertServiceSpy.confirm).toHaveBeenCalled();
    expect(dialogRefSpy.close).not.toHaveBeenCalled();
  });

  it('should use correct confirmation message for approval', async () => {
    component.form.controls['comments'].setValue('Test comments');
    alertServiceSpy.confirm.and.resolveTo(true);

    await component.onSubmit();

    expect(alertServiceSpy.confirm).toHaveBeenCalledWith(
      '¿Está seguro de aprobar este informe?',
    );
  });

  it('should use correct confirmation message for rejection', async () => {
    TestBed.resetTestingModule();
    TestBed.configureTestingModule({
      imports: [
        MonthlyReportApprovalDialogComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        ReactiveFormsModule,
      ],
      providers: [
        { provide: MatDialogRef, useValue: dialogRefSpy },
        { provide: MAT_DIALOG_DATA, useValue: { isRejection: true } },
        { provide: AlertService, useValue: alertServiceSpy },
        FormBuilder,
      ],
    });
    fixture = TestBed.createComponent(MonthlyReportApprovalDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    component.form.controls['comments'].setValue('Test comments');
    alertServiceSpy.confirm.and.resolveTo(true);

    await component.onSubmit();

    expect(alertServiceSpy.confirm).toHaveBeenCalledWith(
      '¿Está seguro de rechazar este informe?',
    );
  });
});