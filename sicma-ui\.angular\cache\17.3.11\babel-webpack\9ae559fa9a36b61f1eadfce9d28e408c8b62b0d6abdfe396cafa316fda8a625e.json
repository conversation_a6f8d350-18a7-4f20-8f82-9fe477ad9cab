{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { ContractDetailsTabComponent } from './contract-details-tab.component';\ndescribe('ContractDetailsTabComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [ContractDetailsTabComponent]\n    });\n    fixture = TestBed.createComponent(ContractDetailsTabComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "ContractDetailsTabComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "imports", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\components\\contract-details-tab\\contract-details-tab.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\n\nimport { ContractDetailsTabComponent } from './contract-details-tab.component';\n\ndescribe('ContractDetailsTabComponent', () => {\n  let component: ContractDetailsTabComponent;\n  let fixture: ComponentFixture<ContractDetailsTabComponent>;\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [ContractDetailsTabComponent],\n    });\n    fixture = TestBed.createComponent(ContractDetailsTabComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});"], "mappings": "AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AAEjE,SAASC,2BAA2B,QAAQ,kCAAkC;AAE9EC,QAAQ,CAAC,6BAA6B,EAAE,MAAK;EAC3C,IAAIC,SAAsC;EAC1C,IAAIC,OAAsD;EAE1DC,UAAU,CAAC,MAAK;IACdL,OAAO,CAACM,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACN,2BAA2B;KACtC,CAAC;IACFG,OAAO,GAAGJ,OAAO,CAACQ,eAAe,CAACP,2BAA2B,CAAC;IAC9DE,SAAS,GAAGC,OAAO,CAACK,iBAAiB;IACrCL,OAAO,CAACM,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACT,SAAS,CAAC,CAACU,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}