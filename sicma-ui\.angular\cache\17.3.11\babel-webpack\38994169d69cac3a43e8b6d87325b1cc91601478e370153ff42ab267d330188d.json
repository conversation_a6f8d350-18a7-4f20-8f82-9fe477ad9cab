{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/Github/SICMA/sicma-ui/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { TestBed, fakeAsync, tick } from '@angular/core/testing';\nimport { MatDialog } from '@angular/material/dialog';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { ContractorContractService } from '@contract-management/services/contractor-contract.service';\nimport { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';\nimport { ReportReviewStatusService } from '@contractor-dashboard/services/report-review-status.service';\nimport { PeriodService } from '@contractor-dashboard/services/period.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { of, throwError } from 'rxjs';\nimport { MonthlyReportDialogComponent } from './monthly-report-dialog/monthly-report-dialog.component';\nimport { MonthlyReportsTabComponent } from './monthly-reports-tab.component';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { RouterTestingModule } from '@angular/router/testing';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { SimpleChange } from '@angular/core';\nimport { ReportReviewHistoryService } from '@contractor-dashboard/services/report-review-history.service';\ndescribe('MonthlyReportsTabComponent', () => {\n  let component;\n  let fixture;\n  let monthlyReportService;\n  let contractorContractService;\n  let reportReviewStatusService;\n  let periodService;\n  let reportReviewHistoryService;\n  let spinner;\n  let dialog;\n  let alertService;\n  let authService;\n  const mockReport = {\n    id: 1,\n    reportNumber: 1,\n    startDate: new Date('2024-01-01'),\n    endDate: new Date('2024-01-31'),\n    creationDate: new Date('2024-01-01'),\n    contractorContractId: 1,\n    contractorContract: {\n      id: 1,\n      subscriptionDate: '2024-01-01',\n      contractStartDate: '2024-01-01',\n      contract: {\n        id: 1,\n        contractNumber: 123,\n        monthlyPayment: 1000000,\n        object: 'Test contract',\n        rup: true,\n        secopCode: 123456,\n        addition: false,\n        cession: false,\n        settled: false,\n        status: {\n          id: 1,\n          name: 'Active'\n        },\n        causesSelectionId: 1,\n        managementSupportId: 1,\n        contractClassId: 1,\n        contractYear: {\n          id: 1,\n          year: 2024\n        }\n      }\n    }\n  };\n  const mockReviewHistory = {\n    id: 1,\n    monthlyReportId: 1,\n    reviewDate: new Date('2024-01-01'),\n    reviewStatusId: 1,\n    comment: 'Informe creado',\n    reviewerId: 1,\n    reviewStatus: {\n      id: 1,\n      name: 'Borrador'\n    }\n  };\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    monthlyReportService = jasmine.createSpyObj('MonthlyReportService', ['getByContractorContractId', 'create', 'downloadPdf']);\n    contractorContractService = jasmine.createSpyObj('ContractorContractService', ['getById']);\n    reportReviewStatusService = jasmine.createSpyObj('ReportReviewStatusService', ['getByName']);\n    periodService = jasmine.createSpyObj('PeriodService', ['create']);\n    reportReviewHistoryService = jasmine.createSpyObj('ReportReviewHistoryService', ['create']);\n    authService = jasmine.createSpyObj('AuthService', ['getUserProfiles', 'getCurrentUser']);\n    const mockUser = {\n      id: 1,\n      username: 'testuser',\n      profiles: []\n    };\n    authService.getCurrentUser.and.returnValue(mockUser);\n    dialog = jasmine.createSpyObj('MatDialog', ['open']);\n    alertService = jasmine.createSpyObj('AlertService', ['success', 'error', 'warning']);\n    spinner = jasmine.createSpyObj('NgxSpinnerService', ['show', 'hide']);\n    yield TestBed.configureTestingModule({\n      imports: [RouterTestingModule, HttpClientTestingModule, BrowserAnimationsModule, MonthlyReportsTabComponent, MatTableModule, MatPaginatorModule, MatSortModule, MatButtonModule, MatIconModule],\n      providers: [{\n        provide: MonthlyReportService,\n        useValue: monthlyReportService\n      }, {\n        provide: ContractorContractService,\n        useValue: contractorContractService\n      }, {\n        provide: ReportReviewStatusService,\n        useValue: reportReviewStatusService\n      }, {\n        provide: PeriodService,\n        useValue: periodService\n      }, {\n        provide: ReportReviewHistoryService,\n        useValue: reportReviewHistoryService\n      }, {\n        provide: AuthService,\n        useValue: authService\n      }, {\n        provide: MatDialog,\n        useValue: dialog\n      }, {\n        provide: AlertService,\n        useValue: alertService\n      }, {\n        provide: NgxSpinnerService,\n        useValue: spinner\n      }]\n    }).compileComponents();\n    fixture = TestBed.createComponent(MonthlyReportsTabComponent);\n    component = fixture.componentInstance;\n    component.contractorContractId = 1;\n    component.contractStartDate = new Date('2024-01-01');\n    component.monthlyReports = [mockReport];\n    contractorContractService.getById.and.returnValue(of({\n      id: 1,\n      subscriptionDate: '2024-01-01',\n      contractStartDate: '2024-01-01',\n      contract: {\n        id: 1,\n        contractNumber: 123,\n        monthlyPayment: 1000000,\n        object: 'Test contract',\n        rup: true,\n        secopCode: 123456,\n        addition: false,\n        cession: false,\n        settled: false,\n        status: {\n          id: 1,\n          name: 'Active'\n        },\n        causesSelectionId: 1,\n        managementSupportId: 1,\n        contractClassId: 1,\n        contractYear: {\n          id: 1,\n          year: 2024\n        }\n      }\n    }));\n    reportReviewStatusService.getByName.and.returnValue(of({\n      id: 1,\n      name: 'Borrador'\n    }));\n    periodService.create.and.returnValue(of({\n      num_payment: 1,\n      payment: 1000000,\n      start_date: '2024-01-01',\n      end_date: '2024-01-31',\n      days_in_month: 31\n    }));\n    reportReviewHistoryService.create.and.returnValue(of(mockReviewHistory));\n    monthlyReportService.getByContractorContractId.and.returnValue(of([mockReport]));\n    monthlyReportService.create.and.returnValue(of(mockReport));\n    const dialogRef = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);\n    dialogRef.afterClosed.and.returnValue(of(true));\n    dialog.open.and.returnValue(dialogRef);\n    fixture.detectChanges();\n  }));\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should update reports on changes', () => {\n    const updatedReports = [{\n      ...mockReport,\n      id: 2,\n      reportNumber: 2\n    }];\n    component.monthlyReports = updatedReports;\n    const changes = {\n      monthlyReports: new SimpleChange(null, updatedReports, true)\n    };\n    component.ngOnChanges(changes);\n    expect(component.monthlyReports).toEqual(updatedReports);\n    expect(component.dataSource.data).toEqual(updatedReports);\n  });\n  it('should open report details form', fakeAsync(() => {\n    const dialogRef = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);\n    dialogRef.afterClosed.and.returnValue(of(true));\n    dialog.open.and.returnValue(dialogRef);\n    component.openReportDetailsForm(mockReport);\n    tick();\n    expect(dialog.open).toHaveBeenCalledWith(MonthlyReportDialogComponent, {\n      width: '90vw',\n      height: '90vh',\n      maxWidth: '100vw',\n      maxHeight: '100vh',\n      data: {\n        report: mockReport\n      }\n    });\n    expect(monthlyReportService.getByContractorContractId).toHaveBeenCalled();\n  }));\n  it('should not create report without contractStartDate', fakeAsync(() => {\n    component.contractStartDate = undefined;\n    component.createNewReport();\n    tick();\n    expect(alertService.warning).toHaveBeenCalledWith('No se puede crear un informe sin un contrato asociado');\n    expect(spinner.hide).toHaveBeenCalled();\n  }));\n  it('should create new report successfully', fakeAsync(() => {\n    const mockCreatedReport = {\n      ...mockReport,\n      id: 2,\n      reportNumber: 2\n    };\n    contractorContractService.getById.and.returnValue(of(mockReport.contractorContract));\n    reportReviewStatusService.getByName.and.returnValue(of({\n      id: 1,\n      name: 'Borrador'\n    }));\n    periodService.create.and.returnValue(of({\n      num_payment: 2,\n      payment: 1000000,\n      start_date: '2024-02-01',\n      end_date: '2024-02-29',\n      days_in_month: 29\n    }));\n    reportReviewHistoryService.create.and.returnValue(of(mockReviewHistory));\n    monthlyReportService.create.and.returnValue(of(mockCreatedReport));\n    monthlyReportService.getByContractorContractId.and.returnValue(of([mockReport, mockCreatedReport]));\n    const dialogRef = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);\n    dialogRef.afterClosed.and.returnValue(of(true));\n    dialog.open.and.returnValue(dialogRef);\n    component.createNewReport();\n    tick(2000);\n    expect(spinner.show).toHaveBeenCalled();\n    expect(contractorContractService.getById).toHaveBeenCalledWith(1);\n    expect(periodService.create).toHaveBeenCalled();\n    expect(reportReviewStatusService.getByName).toHaveBeenCalledWith('Borrador');\n    expect(monthlyReportService.create).toHaveBeenCalled();\n    expect(reportReviewHistoryService.create).toHaveBeenCalled();\n    expect(monthlyReportService.getByContractorContractId).toHaveBeenCalledWith(1);\n  }));\n  it('should handle error when getting contract', fakeAsync(() => {\n    contractorContractService.getById.and.returnValue(throwError(() => new Error()));\n    component.createNewReport();\n    tick();\n    expect(alertService.error).toHaveBeenCalledWith('Error al crear el nuevo informe.');\n    expect(spinner.hide).toHaveBeenCalled();\n  }));\n  it('should handle error when creating period', fakeAsync(() => {\n    periodService.create.and.returnValue(throwError(() => new Error()));\n    component.createNewReport();\n    tick();\n    expect(alertService.error).toHaveBeenCalledWith('Error al crear el nuevo informe.');\n    expect(spinner.hide).toHaveBeenCalled();\n  }));\n  it('should handle error when getting review status', fakeAsync(() => {\n    reportReviewStatusService.getByName.and.returnValue(throwError(() => new Error()));\n    component.createNewReport();\n    tick();\n    expect(alertService.error).toHaveBeenCalledWith('Error al crear el nuevo informe.');\n    expect(spinner.hide).toHaveBeenCalled();\n  }));\n  it('should handle error when creating report', fakeAsync(() => {\n    monthlyReportService.create.and.returnValue(throwError(() => new Error()));\n    component.createNewReport();\n    tick();\n    expect(alertService.error).toHaveBeenCalledWith('Error al crear el nuevo informe.');\n    expect(spinner.hide).toHaveBeenCalled();\n  }));\n  it('should handle error when creating review history', fakeAsync(() => {\n    const mockCreatedReport = {\n      ...mockReport,\n      id: 2,\n      reportNumber: 2\n    };\n    monthlyReportService.create.and.returnValue(of(mockCreatedReport));\n    reportReviewHistoryService.create.and.returnValue(throwError(() => new Error()));\n    component.createNewReport();\n    tick(1000);\n    expect(monthlyReportService.getByContractorContractId).toHaveBeenCalled();\n  }));\n  it('should apply filter to data source', () => {\n    const event = {\n      target: {\n        value: 'test'\n      }\n    };\n    component.applyFilter(event);\n    expect(component.dataSource.filter).toBe('test');\n  });\n  it('should initialize paginator and sort after view init', () => {\n    component.ngAfterViewInit();\n    expect(component.dataSource.paginator).toBeTruthy();\n    expect(component.dataSource.sort).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "TestBed", "fakeAsync", "tick", "MatDialog", "BrowserAnimationsModule", "ContractorContractService", "MonthlyReportService", "ReportReviewStatusService", "PeriodService", "AlertService", "NgxSpinnerService", "of", "throwError", "MonthlyReportDialogComponent", "MonthlyReportsTabComponent", "AuthService", "RouterTestingModule", "MatTableModule", "MatPaginatorModule", "MatSortModule", "MatButtonModule", "MatIconModule", "SimpleChange", "ReportReviewHistoryService", "describe", "component", "fixture", "monthlyReportService", "contractorContractService", "reportReviewStatusService", "periodService", "reportReviewHistoryService", "spinner", "dialog", "alertService", "authService", "mockReport", "id", "reportNumber", "startDate", "Date", "endDate", "creationDate", "contractorContractId", "contractorContract", "subscriptionDate", "contractStartDate", "contract", "contractNumber", "monthlyPayment", "object", "rup", "secopCode", "addition", "cession", "settled", "status", "name", "causesSelectionId", "managementSupportId", "contractClassId", "contractYear", "year", "mockReviewHistory", "monthlyReportId", "reviewDate", "reviewStatusId", "comment", "reviewerId", "reviewStatus", "beforeEach", "_asyncToGenerator", "jasmine", "createSpyObj", "mockUser", "username", "profiles", "getCurrentUser", "and", "returnValue", "configureTestingModule", "imports", "providers", "provide", "useValue", "compileComponents", "createComponent", "componentInstance", "monthlyReports", "getById", "getByName", "create", "num_payment", "payment", "start_date", "end_date", "days_in_month", "getByContractorContractId", "dialogRef", "afterClosed", "open", "detectChanges", "it", "expect", "toBeTruthy", "updatedReports", "changes", "ngOnChanges", "toEqual", "dataSource", "data", "openReportDetailsForm", "toHaveBeenCalledWith", "width", "height", "max<PERSON><PERSON><PERSON>", "maxHeight", "report", "toHaveBeenCalled", "undefined", "createNewReport", "warning", "hide", "mockCreatedReport", "show", "Error", "error", "event", "target", "value", "applyFilter", "filter", "toBe", "ngAfterViewInit", "paginator", "sort"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\components\\monthly-reports-tab\\monthly-reports-tab.component.spec.ts"], "sourcesContent": ["import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport {\n  ComponentFixture,\n  TestBed,\n  fakeAsync,\n  tick,\n} from '@angular/core/testing';\nimport { MatDialog } from '@angular/material/dialog';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { ContractorContractService } from '@contract-management/services/contractor-contract.service';\nimport { MonthlyReport } from '@contractor-dashboard/models/monthly-report.model';\nimport { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';\nimport { ReportReviewStatusService } from '@contractor-dashboard/services/report-review-status.service';\nimport { PeriodService } from '@contractor-dashboard/services/period.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { of, throwError } from 'rxjs';\nimport { MonthlyReportDialogComponent } from './monthly-report-dialog/monthly-report-dialog.component';\nimport { MonthlyReportsTabComponent } from './monthly-reports-tab.component';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { User } from '@core/auth/models/user.model';\nimport { RouterTestingModule } from '@angular/router/testing';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { SimpleChanges, SimpleChange } from '@angular/core';\nimport { ReportReviewHistoryService } from '@contractor-dashboard/services/report-review-history.service';\nimport { ReportReviewHistory } from '@contractor-dashboard/models/report-review-history.model';\n\ndescribe('MonthlyReportsTabComponent', () => {\n  let component: MonthlyReportsTabComponent;\n  let fixture: ComponentFixture<MonthlyReportsTabComponent>;\n  let monthlyReportService: jasmine.SpyObj<MonthlyReportService>;\n  let contractorContractService: jasmine.SpyObj<ContractorContractService>;\n  let reportReviewStatusService: jasmine.SpyObj<ReportReviewStatusService>;\n  let periodService: jasmine.SpyObj<PeriodService>;\n  let reportReviewHistoryService: jasmine.SpyObj<ReportReviewHistoryService>;\n  let spinner: jasmine.SpyObj<NgxSpinnerService>;\n  let dialog: jasmine.SpyObj<MatDialog>;\n  let alertService: jasmine.SpyObj<AlertService>;\n  let authService: jasmine.SpyObj<AuthService>;\n\n  const mockReport: MonthlyReport = {\n    id: 1,\n    reportNumber: 1,\n    startDate: new Date('2024-01-01'),\n    endDate: new Date('2024-01-31'),\n    creationDate: new Date('2024-01-01'),\n    contractorContractId: 1,\n    contractorContract: {\n      id: 1,\n      subscriptionDate: '2024-01-01',\n      contractStartDate: '2024-01-01',\n      contract: {\n        id: 1,\n        contractNumber: 123,\n        monthlyPayment: 1000000,\n        object: 'Test contract',\n        rup: true,\n        secopCode: 123456,\n        addition: false,\n        cession: false,\n        settled: false,\n        status: {\n          id: 1,\n          name: 'Active',\n        },\n        causesSelectionId: 1,\n        managementSupportId: 1,\n        contractClassId: 1,\n        contractYear: {\n          id: 1,\n          year: 2024,\n        },\n      },\n    },\n  };\n\n  const mockReviewHistory: ReportReviewHistory = {\n    id: 1,\n    monthlyReportId: 1,\n    reviewDate: new Date('2024-01-01'),\n    reviewStatusId: 1,\n    comment: 'Informe creado',\n    reviewerId: 1,\n    reviewStatus: {\n      id: 1,\n      name: 'Borrador',\n    },\n  };\n\n  beforeEach(async () => {\n    monthlyReportService = jasmine.createSpyObj('MonthlyReportService', [\n      'getByContractorContractId',\n      'create',\n      'downloadPdf',\n    ]);\n    contractorContractService = jasmine.createSpyObj(\n      'ContractorContractService',\n      ['getById'],\n    );\n    reportReviewStatusService = jasmine.createSpyObj(\n      'ReportReviewStatusService',\n      ['getByName'],\n    );\n    periodService = jasmine.createSpyObj('PeriodService', ['create']);\n    reportReviewHistoryService = jasmine.createSpyObj(\n      'ReportReviewHistoryService',\n      ['create'],\n    );\n    authService = jasmine.createSpyObj('AuthService', [\n      'getUserProfiles',\n      'getCurrentUser',\n    ]);\n    const mockUser: User = {\n      id: 1,\n      username: 'testuser',\n      profiles: [],\n    };\n    authService.getCurrentUser.and.returnValue(mockUser);\n    dialog = jasmine.createSpyObj('MatDialog', ['open']);\n    alertService = jasmine.createSpyObj('AlertService', [\n      'success',\n      'error',\n      'warning',\n    ]);\n    spinner = jasmine.createSpyObj('NgxSpinnerService', ['show', 'hide']);\n\n    await TestBed.configureTestingModule({\n      imports: [\n        RouterTestingModule,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n        MonthlyReportsTabComponent,\n        MatTableModule,\n        MatPaginatorModule,\n        MatSortModule,\n        MatButtonModule,\n        MatIconModule,\n      ],\n      providers: [\n        { provide: MonthlyReportService, useValue: monthlyReportService },\n        {\n          provide: ContractorContractService,\n          useValue: contractorContractService,\n        },\n        {\n          provide: ReportReviewStatusService,\n          useValue: reportReviewStatusService,\n        },\n        { provide: PeriodService, useValue: periodService },\n        {\n          provide: ReportReviewHistoryService,\n          useValue: reportReviewHistoryService,\n        },\n        { provide: AuthService, useValue: authService },\n        { provide: MatDialog, useValue: dialog },\n        { provide: AlertService, useValue: alertService },\n        { provide: NgxSpinnerService, useValue: spinner },\n      ],\n    }).compileComponents();\n\n    fixture = TestBed.createComponent(MonthlyReportsTabComponent);\n    component = fixture.componentInstance;\n\n    component.contractorContractId = 1;\n    component.contractStartDate = new Date('2024-01-01');\n    component.monthlyReports = [mockReport];\n\n    contractorContractService.getById.and.returnValue(\n      of({\n        id: 1,\n        subscriptionDate: '2024-01-01',\n        contractStartDate: '2024-01-01',\n        contract: {\n          id: 1,\n          contractNumber: 123,\n          monthlyPayment: 1000000,\n          object: 'Test contract',\n          rup: true,\n          secopCode: 123456,\n          addition: false,\n          cession: false,\n          settled: false,\n          status: {\n            id: 1,\n            name: 'Active',\n          },\n          causesSelectionId: 1,\n          managementSupportId: 1,\n          contractClassId: 1,\n          contractYear: {\n            id: 1,\n            year: 2024,\n          },\n        },\n      }),\n    );\n\n    reportReviewStatusService.getByName.and.returnValue(\n      of({\n        id: 1,\n        name: 'Borrador',\n      }),\n    );\n\n    periodService.create.and.returnValue(\n      of({\n        num_payment: 1,\n        payment: 1000000,\n        start_date: '2024-01-01',\n        end_date: '2024-01-31',\n        days_in_month: 31,\n      }),\n    );\n\n    reportReviewHistoryService.create.and.returnValue(of(mockReviewHistory));\n\n    monthlyReportService.getByContractorContractId.and.returnValue(\n      of([mockReport]),\n    );\n    monthlyReportService.create.and.returnValue(of(mockReport));\n\n    const dialogRef = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);\n    dialogRef.afterClosed.and.returnValue(of(true));\n    dialog.open.and.returnValue(dialogRef);\n\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should update reports on changes', () => {\n    const updatedReports = [{ ...mockReport, id: 2, reportNumber: 2 }];\n\n    component.monthlyReports = updatedReports;\n\n    const changes: SimpleChanges = {\n      monthlyReports: new SimpleChange(null, updatedReports, true),\n    };\n\n    component.ngOnChanges(changes);\n\n    expect(component.monthlyReports).toEqual(updatedReports);\n\n    expect(component.dataSource.data).toEqual(updatedReports);\n  });\n\n  it('should open report details form', fakeAsync(() => {\n    const dialogRef = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);\n    dialogRef.afterClosed.and.returnValue(of(true));\n    dialog.open.and.returnValue(dialogRef);\n\n    component.openReportDetailsForm(mockReport);\n    tick();\n\n    expect(dialog.open).toHaveBeenCalledWith(MonthlyReportDialogComponent, {\n      width: '90vw',\n      height: '90vh',\n      maxWidth: '100vw',\n      maxHeight: '100vh',\n      data: { report: mockReport },\n    });\n    expect(monthlyReportService.getByContractorContractId).toHaveBeenCalled();\n  }));\n\n  it('should not create report without contractStartDate', fakeAsync(() => {\n    component.contractStartDate = undefined;\n    component.createNewReport();\n    tick();\n\n    expect(alertService.warning).toHaveBeenCalledWith(\n      'No se puede crear un informe sin un contrato asociado',\n    );\n    expect(spinner.hide).toHaveBeenCalled();\n  }));\n\n  it('should create new report successfully', fakeAsync(() => {\n    const mockCreatedReport: MonthlyReport = {\n      ...mockReport,\n      id: 2,\n      reportNumber: 2,\n    };\n\n    contractorContractService.getById.and.returnValue(\n      of(mockReport.contractorContract!),\n    );\n    reportReviewStatusService.getByName.and.returnValue(\n      of({ id: 1, name: 'Borrador' }),\n    );\n    periodService.create.and.returnValue(\n      of({\n        num_payment: 2,\n        payment: 1000000,\n        start_date: '2024-02-01',\n        end_date: '2024-02-29',\n        days_in_month: 29,\n      }),\n    );\n    reportReviewHistoryService.create.and.returnValue(of(mockReviewHistory));\n    monthlyReportService.create.and.returnValue(of(mockCreatedReport));\n    monthlyReportService.getByContractorContractId.and.returnValue(\n      of([mockReport, mockCreatedReport]),\n    );\n\n    const dialogRef = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);\n    dialogRef.afterClosed.and.returnValue(of(true));\n    dialog.open.and.returnValue(dialogRef);\n\n    component.createNewReport();\n    tick(2000);\n\n    expect(spinner.show).toHaveBeenCalled();\n    expect(contractorContractService.getById).toHaveBeenCalledWith(1);\n    expect(periodService.create).toHaveBeenCalled();\n    expect(reportReviewStatusService.getByName).toHaveBeenCalledWith(\n      'Borrador',\n    );\n    expect(monthlyReportService.create).toHaveBeenCalled();\n    expect(reportReviewHistoryService.create).toHaveBeenCalled();\n    expect(monthlyReportService.getByContractorContractId).toHaveBeenCalledWith(\n      1,\n    );\n  }));\n\n  it('should handle error when getting contract', fakeAsync(() => {\n    contractorContractService.getById.and.returnValue(\n      throwError(() => new Error()),\n    );\n\n    component.createNewReport();\n    tick();\n\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al crear el nuevo informe.',\n    );\n    expect(spinner.hide).toHaveBeenCalled();\n  }));\n\n  it('should handle error when creating period', fakeAsync(() => {\n    periodService.create.and.returnValue(throwError(() => new Error()));\n\n    component.createNewReport();\n    tick();\n\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al crear el nuevo informe.',\n    );\n    expect(spinner.hide).toHaveBeenCalled();\n  }));\n\n  it('should handle error when getting review status', fakeAsync(() => {\n    reportReviewStatusService.getByName.and.returnValue(\n      throwError(() => new Error()),\n    );\n\n    component.createNewReport();\n    tick();\n\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al crear el nuevo informe.',\n    );\n    expect(spinner.hide).toHaveBeenCalled();\n  }));\n\n  it('should handle error when creating report', fakeAsync(() => {\n    monthlyReportService.create.and.returnValue(throwError(() => new Error()));\n\n    component.createNewReport();\n    tick();\n\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al crear el nuevo informe.',\n    );\n    expect(spinner.hide).toHaveBeenCalled();\n  }));\n\n  it('should handle error when creating review history', fakeAsync(() => {\n    const mockCreatedReport: MonthlyReport = {\n      ...mockReport,\n      id: 2,\n      reportNumber: 2,\n    };\n\n    monthlyReportService.create.and.returnValue(of(mockCreatedReport));\n\n    reportReviewHistoryService.create.and.returnValue(\n      throwError(() => new Error()),\n    );\n\n    component.createNewReport();\n    tick(1000);\n\n    expect(monthlyReportService.getByContractorContractId).toHaveBeenCalled();\n  }));\n\n  it('should apply filter to data source', () => {\n    const event = { target: { value: 'test' } } as unknown as Event;\n    component.applyFilter(event);\n    expect(component.dataSource.filter).toBe('test');\n  });\n\n  it('should initialize paginator and sort after view init', () => {\n    component.ngAfterViewInit();\n    expect(component.dataSource.paginator).toBeTruthy();\n    expect(component.dataSource.sort).toBeTruthy();\n  });\n});\n"], "mappings": ";AAAA,SAASA,uBAAuB,QAAQ,8BAA8B;AACtE,SAEEC,OAAO,EACPC,SAAS,EACTC,IAAI,QACC,uBAAuB;AAC9B,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,yBAAyB,QAAQ,2DAA2D;AAErG,SAASC,oBAAoB,QAAQ,uDAAuD;AAC5F,SAASC,yBAAyB,QAAQ,6DAA6D;AACvG,SAASC,aAAa,QAAQ,+CAA+C;AAC7E,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,iBAAiB,QAAQ,aAAa;AAC/C,SAASC,EAAE,EAAEC,UAAU,QAAQ,MAAM;AACrC,SAASC,4BAA4B,QAAQ,yDAAyD;AACtG,SAASC,0BAA0B,QAAQ,iCAAiC;AAC5E,SAASC,WAAW,QAAQ,kCAAkC;AAE9D,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAAwBC,YAAY,QAAQ,eAAe;AAC3D,SAASC,0BAA0B,QAAQ,8DAA8D;AAGzGC,QAAQ,CAAC,4BAA4B,EAAE,MAAK;EAC1C,IAAIC,SAAqC;EACzC,IAAIC,OAAqD;EACzD,IAAIC,oBAA0D;EAC9D,IAAIC,yBAAoE;EACxE,IAAIC,yBAAoE;EACxE,IAAIC,aAA4C;EAChD,IAAIC,0BAAsE;EAC1E,IAAIC,OAA0C;EAC9C,IAAIC,MAAiC;EACrC,IAAIC,YAA0C;EAC9C,IAAIC,WAAwC;EAE5C,MAAMC,UAAU,GAAkB;IAChCC,EAAE,EAAE,CAAC;IACLC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;IACjCC,OAAO,EAAE,IAAID,IAAI,CAAC,YAAY,CAAC;IAC/BE,YAAY,EAAE,IAAIF,IAAI,CAAC,YAAY,CAAC;IACpCG,oBAAoB,EAAE,CAAC;IACvBC,kBAAkB,EAAE;MAClBP,EAAE,EAAE,CAAC;MACLQ,gBAAgB,EAAE,YAAY;MAC9BC,iBAAiB,EAAE,YAAY;MAC/BC,QAAQ,EAAE;QACRV,EAAE,EAAE,CAAC;QACLW,cAAc,EAAE,GAAG;QACnBC,cAAc,EAAE,OAAO;QACvBC,MAAM,EAAE,eAAe;QACvBC,GAAG,EAAE,IAAI;QACTC,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,KAAK;QACfC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,KAAK;QACdC,MAAM,EAAE;UACNnB,EAAE,EAAE,CAAC;UACLoB,IAAI,EAAE;SACP;QACDC,iBAAiB,EAAE,CAAC;QACpBC,mBAAmB,EAAE,CAAC;QACtBC,eAAe,EAAE,CAAC;QAClBC,YAAY,EAAE;UACZxB,EAAE,EAAE,CAAC;UACLyB,IAAI,EAAE;;;;GAIb;EAED,MAAMC,iBAAiB,GAAwB;IAC7C1B,EAAE,EAAE,CAAC;IACL2B,eAAe,EAAE,CAAC;IAClBC,UAAU,EAAE,IAAIzB,IAAI,CAAC,YAAY,CAAC;IAClC0B,cAAc,EAAE,CAAC;IACjBC,OAAO,EAAE,gBAAgB;IACzBC,UAAU,EAAE,CAAC;IACbC,YAAY,EAAE;MACZhC,EAAE,EAAE,CAAC;MACLoB,IAAI,EAAE;;GAET;EAEDa,UAAU,eAAAC,iBAAA,CAAC,aAAW;IACpB5C,oBAAoB,GAAG6C,OAAO,CAACC,YAAY,CAAC,sBAAsB,EAAE,CAClE,2BAA2B,EAC3B,QAAQ,EACR,aAAa,CACd,CAAC;IACF7C,yBAAyB,GAAG4C,OAAO,CAACC,YAAY,CAC9C,2BAA2B,EAC3B,CAAC,SAAS,CAAC,CACZ;IACD5C,yBAAyB,GAAG2C,OAAO,CAACC,YAAY,CAC9C,2BAA2B,EAC3B,CAAC,WAAW,CAAC,CACd;IACD3C,aAAa,GAAG0C,OAAO,CAACC,YAAY,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,CAAC;IACjE1C,0BAA0B,GAAGyC,OAAO,CAACC,YAAY,CAC/C,4BAA4B,EAC5B,CAAC,QAAQ,CAAC,CACX;IACDtC,WAAW,GAAGqC,OAAO,CAACC,YAAY,CAAC,aAAa,EAAE,CAChD,iBAAiB,EACjB,gBAAgB,CACjB,CAAC;IACF,MAAMC,QAAQ,GAAS;MACrBrC,EAAE,EAAE,CAAC;MACLsC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE;KACX;IACDzC,WAAW,CAAC0C,cAAc,CAACC,GAAG,CAACC,WAAW,CAACL,QAAQ,CAAC;IACpDzC,MAAM,GAAGuC,OAAO,CAACC,YAAY,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,CAAC;IACpDvC,YAAY,GAAGsC,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAClD,SAAS,EACT,OAAO,EACP,SAAS,CACV,CAAC;IACFzC,OAAO,GAAGwC,OAAO,CAACC,YAAY,CAAC,mBAAmB,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAErE,MAAMzE,OAAO,CAACgF,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CACPjE,mBAAmB,EACnBjB,uBAAuB,EACvBK,uBAAuB,EACvBU,0BAA0B,EAC1BG,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,eAAe,EACfC,aAAa,CACd;MACD6D,SAAS,EAAE,CACT;QAAEC,OAAO,EAAE7E,oBAAoB;QAAE8E,QAAQ,EAAEzD;MAAoB,CAAE,EACjE;QACEwD,OAAO,EAAE9E,yBAAyB;QAClC+E,QAAQ,EAAExD;OACX,EACD;QACEuD,OAAO,EAAE5E,yBAAyB;QAClC6E,QAAQ,EAAEvD;OACX,EACD;QAAEsD,OAAO,EAAE3E,aAAa;QAAE4E,QAAQ,EAAEtD;MAAa,CAAE,EACnD;QACEqD,OAAO,EAAE5D,0BAA0B;QACnC6D,QAAQ,EAAErD;OACX,EACD;QAAEoD,OAAO,EAAEpE,WAAW;QAAEqE,QAAQ,EAAEjD;MAAW,CAAE,EAC/C;QAAEgD,OAAO,EAAEhF,SAAS;QAAEiF,QAAQ,EAAEnD;MAAM,CAAE,EACxC;QAAEkD,OAAO,EAAE1E,YAAY;QAAE2E,QAAQ,EAAElD;MAAY,CAAE,EACjD;QAAEiD,OAAO,EAAEzE,iBAAiB;QAAE0E,QAAQ,EAAEpD;MAAO,CAAE;KAEpD,CAAC,CAACqD,iBAAiB,EAAE;IAEtB3D,OAAO,GAAG1B,OAAO,CAACsF,eAAe,CAACxE,0BAA0B,CAAC;IAC7DW,SAAS,GAAGC,OAAO,CAAC6D,iBAAiB;IAErC9D,SAAS,CAACkB,oBAAoB,GAAG,CAAC;IAClClB,SAAS,CAACqB,iBAAiB,GAAG,IAAIN,IAAI,CAAC,YAAY,CAAC;IACpDf,SAAS,CAAC+D,cAAc,GAAG,CAACpD,UAAU,CAAC;IAEvCR,yBAAyB,CAAC6D,OAAO,CAACX,GAAG,CAACC,WAAW,CAC/CpE,EAAE,CAAC;MACD0B,EAAE,EAAE,CAAC;MACLQ,gBAAgB,EAAE,YAAY;MAC9BC,iBAAiB,EAAE,YAAY;MAC/BC,QAAQ,EAAE;QACRV,EAAE,EAAE,CAAC;QACLW,cAAc,EAAE,GAAG;QACnBC,cAAc,EAAE,OAAO;QACvBC,MAAM,EAAE,eAAe;QACvBC,GAAG,EAAE,IAAI;QACTC,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,KAAK;QACfC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,KAAK;QACdC,MAAM,EAAE;UACNnB,EAAE,EAAE,CAAC;UACLoB,IAAI,EAAE;SACP;QACDC,iBAAiB,EAAE,CAAC;QACpBC,mBAAmB,EAAE,CAAC;QACtBC,eAAe,EAAE,CAAC;QAClBC,YAAY,EAAE;UACZxB,EAAE,EAAE,CAAC;UACLyB,IAAI,EAAE;;;KAGX,CAAC,CACH;IAEDjC,yBAAyB,CAAC6D,SAAS,CAACZ,GAAG,CAACC,WAAW,CACjDpE,EAAE,CAAC;MACD0B,EAAE,EAAE,CAAC;MACLoB,IAAI,EAAE;KACP,CAAC,CACH;IAED3B,aAAa,CAAC6D,MAAM,CAACb,GAAG,CAACC,WAAW,CAClCpE,EAAE,CAAC;MACDiF,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE,OAAO;MAChBC,UAAU,EAAE,YAAY;MACxBC,QAAQ,EAAE,YAAY;MACtBC,aAAa,EAAE;KAChB,CAAC,CACH;IAEDjE,0BAA0B,CAAC4D,MAAM,CAACb,GAAG,CAACC,WAAW,CAACpE,EAAE,CAACoD,iBAAiB,CAAC,CAAC;IAExEpC,oBAAoB,CAACsE,yBAAyB,CAACnB,GAAG,CAACC,WAAW,CAC5DpE,EAAE,CAAC,CAACyB,UAAU,CAAC,CAAC,CACjB;IACDT,oBAAoB,CAACgE,MAAM,CAACb,GAAG,CAACC,WAAW,CAACpE,EAAE,CAACyB,UAAU,CAAC,CAAC;IAE3D,MAAM8D,SAAS,GAAG1B,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,aAAa,CAAC,CAAC;IACvEyB,SAAS,CAACC,WAAW,CAACrB,GAAG,CAACC,WAAW,CAACpE,EAAE,CAAC,IAAI,CAAC,CAAC;IAC/CsB,MAAM,CAACmE,IAAI,CAACtB,GAAG,CAACC,WAAW,CAACmB,SAAS,CAAC;IAEtCxE,OAAO,CAAC2E,aAAa,EAAE;EACzB,CAAC,EAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAAC9E,SAAS,CAAC,CAAC+E,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,kCAAkC,EAAE,MAAK;IAC1C,MAAMG,cAAc,GAAG,CAAC;MAAE,GAAGrE,UAAU;MAAEC,EAAE,EAAE,CAAC;MAAEC,YAAY,EAAE;IAAC,CAAE,CAAC;IAElEb,SAAS,CAAC+D,cAAc,GAAGiB,cAAc;IAEzC,MAAMC,OAAO,GAAkB;MAC7BlB,cAAc,EAAE,IAAIlE,YAAY,CAAC,IAAI,EAAEmF,cAAc,EAAE,IAAI;KAC5D;IAEDhF,SAAS,CAACkF,WAAW,CAACD,OAAO,CAAC;IAE9BH,MAAM,CAAC9E,SAAS,CAAC+D,cAAc,CAAC,CAACoB,OAAO,CAACH,cAAc,CAAC;IAExDF,MAAM,CAAC9E,SAAS,CAACoF,UAAU,CAACC,IAAI,CAAC,CAACF,OAAO,CAACH,cAAc,CAAC;EAC3D,CAAC,CAAC;EAEFH,EAAE,CAAC,iCAAiC,EAAErG,SAAS,CAAC,MAAK;IACnD,MAAMiG,SAAS,GAAG1B,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,aAAa,CAAC,CAAC;IACvEyB,SAAS,CAACC,WAAW,CAACrB,GAAG,CAACC,WAAW,CAACpE,EAAE,CAAC,IAAI,CAAC,CAAC;IAC/CsB,MAAM,CAACmE,IAAI,CAACtB,GAAG,CAACC,WAAW,CAACmB,SAAS,CAAC;IAEtCzE,SAAS,CAACsF,qBAAqB,CAAC3E,UAAU,CAAC;IAC3ClC,IAAI,EAAE;IAENqG,MAAM,CAACtE,MAAM,CAACmE,IAAI,CAAC,CAACY,oBAAoB,CAACnG,4BAA4B,EAAE;MACrEoG,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,OAAO;MACjBC,SAAS,EAAE,OAAO;MAClBN,IAAI,EAAE;QAAEO,MAAM,EAAEjF;MAAU;KAC3B,CAAC;IACFmE,MAAM,CAAC5E,oBAAoB,CAACsE,yBAAyB,CAAC,CAACqB,gBAAgB,EAAE;EAC3E,CAAC,CAAC,CAAC;EAEHhB,EAAE,CAAC,oDAAoD,EAAErG,SAAS,CAAC,MAAK;IACtEwB,SAAS,CAACqB,iBAAiB,GAAGyE,SAAS;IACvC9F,SAAS,CAAC+F,eAAe,EAAE;IAC3BtH,IAAI,EAAE;IAENqG,MAAM,CAACrE,YAAY,CAACuF,OAAO,CAAC,CAACT,oBAAoB,CAC/C,uDAAuD,CACxD;IACDT,MAAM,CAACvE,OAAO,CAAC0F,IAAI,CAAC,CAACJ,gBAAgB,EAAE;EACzC,CAAC,CAAC,CAAC;EAEHhB,EAAE,CAAC,uCAAuC,EAAErG,SAAS,CAAC,MAAK;IACzD,MAAM0H,iBAAiB,GAAkB;MACvC,GAAGvF,UAAU;MACbC,EAAE,EAAE,CAAC;MACLC,YAAY,EAAE;KACf;IAEDV,yBAAyB,CAAC6D,OAAO,CAACX,GAAG,CAACC,WAAW,CAC/CpE,EAAE,CAACyB,UAAU,CAACQ,kBAAmB,CAAC,CACnC;IACDf,yBAAyB,CAAC6D,SAAS,CAACZ,GAAG,CAACC,WAAW,CACjDpE,EAAE,CAAC;MAAE0B,EAAE,EAAE,CAAC;MAAEoB,IAAI,EAAE;IAAU,CAAE,CAAC,CAChC;IACD3B,aAAa,CAAC6D,MAAM,CAACb,GAAG,CAACC,WAAW,CAClCpE,EAAE,CAAC;MACDiF,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE,OAAO;MAChBC,UAAU,EAAE,YAAY;MACxBC,QAAQ,EAAE,YAAY;MACtBC,aAAa,EAAE;KAChB,CAAC,CACH;IACDjE,0BAA0B,CAAC4D,MAAM,CAACb,GAAG,CAACC,WAAW,CAACpE,EAAE,CAACoD,iBAAiB,CAAC,CAAC;IACxEpC,oBAAoB,CAACgE,MAAM,CAACb,GAAG,CAACC,WAAW,CAACpE,EAAE,CAACgH,iBAAiB,CAAC,CAAC;IAClEhG,oBAAoB,CAACsE,yBAAyB,CAACnB,GAAG,CAACC,WAAW,CAC5DpE,EAAE,CAAC,CAACyB,UAAU,EAAEuF,iBAAiB,CAAC,CAAC,CACpC;IAED,MAAMzB,SAAS,GAAG1B,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,aAAa,CAAC,CAAC;IACvEyB,SAAS,CAACC,WAAW,CAACrB,GAAG,CAACC,WAAW,CAACpE,EAAE,CAAC,IAAI,CAAC,CAAC;IAC/CsB,MAAM,CAACmE,IAAI,CAACtB,GAAG,CAACC,WAAW,CAACmB,SAAS,CAAC;IAEtCzE,SAAS,CAAC+F,eAAe,EAAE;IAC3BtH,IAAI,CAAC,IAAI,CAAC;IAEVqG,MAAM,CAACvE,OAAO,CAAC4F,IAAI,CAAC,CAACN,gBAAgB,EAAE;IACvCf,MAAM,CAAC3E,yBAAyB,CAAC6D,OAAO,CAAC,CAACuB,oBAAoB,CAAC,CAAC,CAAC;IACjET,MAAM,CAACzE,aAAa,CAAC6D,MAAM,CAAC,CAAC2B,gBAAgB,EAAE;IAC/Cf,MAAM,CAAC1E,yBAAyB,CAAC6D,SAAS,CAAC,CAACsB,oBAAoB,CAC9D,UAAU,CACX;IACDT,MAAM,CAAC5E,oBAAoB,CAACgE,MAAM,CAAC,CAAC2B,gBAAgB,EAAE;IACtDf,MAAM,CAACxE,0BAA0B,CAAC4D,MAAM,CAAC,CAAC2B,gBAAgB,EAAE;IAC5Df,MAAM,CAAC5E,oBAAoB,CAACsE,yBAAyB,CAAC,CAACe,oBAAoB,CACzE,CAAC,CACF;EACH,CAAC,CAAC,CAAC;EAEHV,EAAE,CAAC,2CAA2C,EAAErG,SAAS,CAAC,MAAK;IAC7D2B,yBAAyB,CAAC6D,OAAO,CAACX,GAAG,CAACC,WAAW,CAC/CnE,UAAU,CAAC,MAAM,IAAIiH,KAAK,EAAE,CAAC,CAC9B;IAEDpG,SAAS,CAAC+F,eAAe,EAAE;IAC3BtH,IAAI,EAAE;IAENqG,MAAM,CAACrE,YAAY,CAAC4F,KAAK,CAAC,CAACd,oBAAoB,CAC7C,kCAAkC,CACnC;IACDT,MAAM,CAACvE,OAAO,CAAC0F,IAAI,CAAC,CAACJ,gBAAgB,EAAE;EACzC,CAAC,CAAC,CAAC;EAEHhB,EAAE,CAAC,0CAA0C,EAAErG,SAAS,CAAC,MAAK;IAC5D6B,aAAa,CAAC6D,MAAM,CAACb,GAAG,CAACC,WAAW,CAACnE,UAAU,CAAC,MAAM,IAAIiH,KAAK,EAAE,CAAC,CAAC;IAEnEpG,SAAS,CAAC+F,eAAe,EAAE;IAC3BtH,IAAI,EAAE;IAENqG,MAAM,CAACrE,YAAY,CAAC4F,KAAK,CAAC,CAACd,oBAAoB,CAC7C,kCAAkC,CACnC;IACDT,MAAM,CAACvE,OAAO,CAAC0F,IAAI,CAAC,CAACJ,gBAAgB,EAAE;EACzC,CAAC,CAAC,CAAC;EAEHhB,EAAE,CAAC,gDAAgD,EAAErG,SAAS,CAAC,MAAK;IAClE4B,yBAAyB,CAAC6D,SAAS,CAACZ,GAAG,CAACC,WAAW,CACjDnE,UAAU,CAAC,MAAM,IAAIiH,KAAK,EAAE,CAAC,CAC9B;IAEDpG,SAAS,CAAC+F,eAAe,EAAE;IAC3BtH,IAAI,EAAE;IAENqG,MAAM,CAACrE,YAAY,CAAC4F,KAAK,CAAC,CAACd,oBAAoB,CAC7C,kCAAkC,CACnC;IACDT,MAAM,CAACvE,OAAO,CAAC0F,IAAI,CAAC,CAACJ,gBAAgB,EAAE;EACzC,CAAC,CAAC,CAAC;EAEHhB,EAAE,CAAC,0CAA0C,EAAErG,SAAS,CAAC,MAAK;IAC5D0B,oBAAoB,CAACgE,MAAM,CAACb,GAAG,CAACC,WAAW,CAACnE,UAAU,CAAC,MAAM,IAAIiH,KAAK,EAAE,CAAC,CAAC;IAE1EpG,SAAS,CAAC+F,eAAe,EAAE;IAC3BtH,IAAI,EAAE;IAENqG,MAAM,CAACrE,YAAY,CAAC4F,KAAK,CAAC,CAACd,oBAAoB,CAC7C,kCAAkC,CACnC;IACDT,MAAM,CAACvE,OAAO,CAAC0F,IAAI,CAAC,CAACJ,gBAAgB,EAAE;EACzC,CAAC,CAAC,CAAC;EAEHhB,EAAE,CAAC,kDAAkD,EAAErG,SAAS,CAAC,MAAK;IACpE,MAAM0H,iBAAiB,GAAkB;MACvC,GAAGvF,UAAU;MACbC,EAAE,EAAE,CAAC;MACLC,YAAY,EAAE;KACf;IAEDX,oBAAoB,CAACgE,MAAM,CAACb,GAAG,CAACC,WAAW,CAACpE,EAAE,CAACgH,iBAAiB,CAAC,CAAC;IAElE5F,0BAA0B,CAAC4D,MAAM,CAACb,GAAG,CAACC,WAAW,CAC/CnE,UAAU,CAAC,MAAM,IAAIiH,KAAK,EAAE,CAAC,CAC9B;IAEDpG,SAAS,CAAC+F,eAAe,EAAE;IAC3BtH,IAAI,CAAC,IAAI,CAAC;IAEVqG,MAAM,CAAC5E,oBAAoB,CAACsE,yBAAyB,CAAC,CAACqB,gBAAgB,EAAE;EAC3E,CAAC,CAAC,CAAC;EAEHhB,EAAE,CAAC,oCAAoC,EAAE,MAAK;IAC5C,MAAMyB,KAAK,GAAG;MAAEC,MAAM,EAAE;QAAEC,KAAK,EAAE;MAAM;IAAE,CAAsB;IAC/DxG,SAAS,CAACyG,WAAW,CAACH,KAAK,CAAC;IAC5BxB,MAAM,CAAC9E,SAAS,CAACoF,UAAU,CAACsB,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;EAClD,CAAC,CAAC;EAEF9B,EAAE,CAAC,sDAAsD,EAAE,MAAK;IAC9D7E,SAAS,CAAC4G,eAAe,EAAE;IAC3B9B,MAAM,CAAC9E,SAAS,CAACoF,UAAU,CAACyB,SAAS,CAAC,CAAC9B,UAAU,EAAE;IACnDD,MAAM,CAAC9E,SAAS,CAACoF,UAAU,CAAC0B,IAAI,CAAC,CAAC/B,UAAU,EAAE;EAChD,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}