{"ast": null, "code": "import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { RouterTestingModule } from '@angular/router/testing';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { HeaderComponent } from './header.component';\ndescribe('HeaderComponent', () => {\n  let component;\n  let fixture;\n  let authService;\n  let localStorageSpy;\n  const mockProfiles = [{\n    profile_id: 1,\n    profile_name: 'ADMIN'\n  }, {\n    profile_id: 2,\n    profile_name: 'USER'\n  }];\n  const mockUser = {\n    id: 1,\n    username: '<EMAIL>',\n    profiles: mockProfiles\n  };\n  beforeEach(() => {\n    const authServiceSpy = jasmine.createSpyObj('AuthService', ['getUserProfiles', 'hasProfile']);\n    authServiceSpy.getUserProfiles.and.returnValue(mockProfiles);\n    authServiceSpy.hasProfile.and.returnValue(true);\n    TestBed.configureTestingModule({\n      imports: [HeaderComponent, HttpClientTestingModule, BrowserAnimationsModule, RouterTestingModule],\n      providers: [{\n        provide: AuthService,\n        useValue: authServiceSpy\n      }]\n    });\n    fixture = TestBed.createComponent(HeaderComponent);\n    component = fixture.componentInstance;\n    authService = TestBed.inject(AuthService);\n    localStorageSpy = spyOn(localStorage, 'getItem');\n  });\n  it('should create', () => {\n    fixture.detectChanges();\n    expect(component).toBeTruthy();\n  });\n  it('should initialize with user from localStorage', () => {\n    localStorageSpy.and.returnValue(JSON.stringify(mockUser));\n    fixture.detectChanges();\n    expect(component.user).toEqual(mockUser);\n    expect(component.userProfiles).toEqual(mockProfiles);\n    expect(authService.getUserProfiles).toHaveBeenCalled();\n  });\n  it('should initialize without user when localStorage is empty', () => {\n    localStorageSpy.and.returnValue(null);\n    fixture.detectChanges();\n    expect(component.user).toBeUndefined();\n    expect(component.userProfiles).toEqual(mockProfiles);\n    expect(authService.getUserProfiles).toHaveBeenCalled();\n  });\n  it('should handle invalid JSON in localStorage', () => {\n    localStorageSpy.and.returnValue('invalid-json');\n    fixture.detectChanges();\n    expect(component.user).toBeUndefined();\n    expect(component.userProfiles).toEqual(mockProfiles);\n    expect(authService.getUserProfiles).toHaveBeenCalled();\n  });\n  it('should check if user can view profile', () => {\n    const profile = 'ADMIN';\n    authService.hasProfile.and.returnValue(true);\n    fixture.detectChanges();\n    const result = component.canView(profile);\n    expect(result).toBe(true);\n    expect(authService.hasProfile).toHaveBeenCalledWith(profile);\n  });\n  it('should check if user cannot view profile', () => {\n    const profile = 'SUPER_ADMIN';\n    authService.hasProfile.and.returnValue(false);\n    fixture.detectChanges();\n    const result = component.canView(profile);\n    expect(result).toBe(false);\n    expect(authService.hasProfile).toHaveBeenCalledWith(profile);\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "TestBed", "BrowserAnimationsModule", "RouterTestingModule", "AuthService", "HeaderComponent", "describe", "component", "fixture", "authService", "localStorageSpy", "mockProfiles", "profile_id", "profile_name", "mockUser", "id", "username", "profiles", "beforeEach", "authServiceSpy", "jasmine", "createSpyObj", "getUserProfiles", "and", "returnValue", "hasProfile", "configureTestingModule", "imports", "providers", "provide", "useValue", "createComponent", "componentInstance", "inject", "spyOn", "localStorage", "it", "detectChanges", "expect", "toBeTruthy", "JSON", "stringify", "user", "toEqual", "userProfiles", "toHaveBeenCalled", "toBeUndefined", "profile", "result", "canView", "toBe", "toHaveBeenCalledWith"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\core\\layout\\header\\header.component.spec.ts"], "sourcesContent": ["import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { ComponentFixture, TestBed } from '@angular/core/testing';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { RouterTestingModule } from '@angular/router/testing';\nimport { User } from '@core/auth/models/user.model';\nimport { UserProfile } from '@core/auth/models/user_profile.model';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { HeaderComponent } from './header.component';\n\ndescribe('HeaderComponent', () => {\n  let component: HeaderComponent;\n  let fixture: ComponentFixture<HeaderComponent>;\n  let authService: jasmine.SpyObj<AuthService>;\n  let localStorageSpy: jasmine.Spy;\n\n  const mockProfiles: UserProfile[] = [\n    { profile_id: 1, profile_name: 'ADMIN' },\n    { profile_id: 2, profile_name: 'USER' },\n  ];\n\n  const mockUser: User = {\n    id: 1,\n    username: '<EMAIL>',\n    profiles: mockProfiles,\n  };\n\n  beforeEach(() => {\n    const authServiceSpy = jasmine.createSpyObj('AuthService', [\n      'getUserProfiles',\n      'hasProfile',\n    ]);\n    authServiceSpy.getUserProfiles.and.returnValue(mockProfiles);\n    authServiceSpy.hasProfile.and.returnValue(true);\n\n    TestBed.configureTestingModule({\n      imports: [\n        HeaderComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n        RouterTestingModule,\n      ],\n      providers: [{ provide: AuthService, useValue: authServiceSpy }],\n    });\n\n    fixture = TestBed.createComponent(HeaderComponent);\n    component = fixture.componentInstance;\n    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;\n    localStorageSpy = spyOn(localStorage, 'getItem');\n  });\n\n  it('should create', () => {\n    fixture.detectChanges();\n    expect(component).toBeTruthy();\n  });\n\n  it('should initialize with user from localStorage', () => {\n    localStorageSpy.and.returnValue(JSON.stringify(mockUser));\n\n    fixture.detectChanges();\n\n    expect(component.user).toEqual(mockUser);\n    expect(component.userProfiles).toEqual(mockProfiles);\n    expect(authService.getUserProfiles).toHaveBeenCalled();\n  });\n\n  it('should initialize without user when localStorage is empty', () => {\n    localStorageSpy.and.returnValue(null);\n\n    fixture.detectChanges();\n\n    expect(component.user).toBeUndefined();\n    expect(component.userProfiles).toEqual(mockProfiles);\n    expect(authService.getUserProfiles).toHaveBeenCalled();\n  });\n\n  it('should handle invalid JSON in localStorage', () => {\n    localStorageSpy.and.returnValue('invalid-json');\n\n    fixture.detectChanges();\n\n    expect(component.user).toBeUndefined();\n    expect(component.userProfiles).toEqual(mockProfiles);\n    expect(authService.getUserProfiles).toHaveBeenCalled();\n  });\n\n  it('should check if user can view profile', () => {\n    const profile = 'ADMIN';\n    authService.hasProfile.and.returnValue(true);\n\n    fixture.detectChanges();\n    const result = component.canView(profile);\n\n    expect(result).toBe(true);\n    expect(authService.hasProfile).toHaveBeenCalledWith(profile);\n  });\n\n  it('should check if user cannot view profile', () => {\n    const profile = 'SUPER_ADMIN';\n    authService.hasProfile.and.returnValue(false);\n\n    fixture.detectChanges();\n    const result = component.canView(profile);\n\n    expect(result).toBe(false);\n    expect(authService.hasProfile).toHaveBeenCalledWith(profile);\n  });\n});"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,8BAA8B;AACtE,SAA2BC,OAAO,QAAQ,uBAAuB;AACjE,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,mBAAmB,QAAQ,yBAAyB;AAG7D,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,SAASC,eAAe,QAAQ,oBAAoB;AAEpDC,QAAQ,CAAC,iBAAiB,EAAE,MAAK;EAC/B,IAAIC,SAA0B;EAC9B,IAAIC,OAA0C;EAC9C,IAAIC,WAAwC;EAC5C,IAAIC,eAA4B;EAEhC,MAAMC,YAAY,GAAkB,CAClC;IAAEC,UAAU,EAAE,CAAC;IAAEC,YAAY,EAAE;EAAO,CAAE,EACxC;IAAED,UAAU,EAAE,CAAC;IAAEC,YAAY,EAAE;EAAM,CAAE,CACxC;EAED,MAAMC,QAAQ,GAAS;IACrBC,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,kBAAkB;IAC5BC,QAAQ,EAAEN;GACX;EAEDO,UAAU,CAAC,MAAK;IACd,MAAMC,cAAc,GAAGC,OAAO,CAACC,YAAY,CAAC,aAAa,EAAE,CACzD,iBAAiB,EACjB,YAAY,CACb,CAAC;IACFF,cAAc,CAACG,eAAe,CAACC,GAAG,CAACC,WAAW,CAACb,YAAY,CAAC;IAC5DQ,cAAc,CAACM,UAAU,CAACF,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;IAE/CvB,OAAO,CAACyB,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPtB,eAAe,EACfL,uBAAuB,EACvBE,uBAAuB,EACvBC,mBAAmB,CACpB;MACDyB,SAAS,EAAE,CAAC;QAAEC,OAAO,EAAEzB,WAAW;QAAE0B,QAAQ,EAAEX;MAAc,CAAE;KAC/D,CAAC;IAEFX,OAAO,GAAGP,OAAO,CAAC8B,eAAe,CAAC1B,eAAe,CAAC;IAClDE,SAAS,GAAGC,OAAO,CAACwB,iBAAiB;IACrCvB,WAAW,GAAGR,OAAO,CAACgC,MAAM,CAAC7B,WAAW,CAAgC;IACxEM,eAAe,GAAGwB,KAAK,CAACC,YAAY,EAAE,SAAS,CAAC;EAClD,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvB5B,OAAO,CAAC6B,aAAa,EAAE;IACvBC,MAAM,CAAC/B,SAAS,CAAC,CAACgC,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFH,EAAE,CAAC,+CAA+C,EAAE,MAAK;IACvD1B,eAAe,CAACa,GAAG,CAACC,WAAW,CAACgB,IAAI,CAACC,SAAS,CAAC3B,QAAQ,CAAC,CAAC;IAEzDN,OAAO,CAAC6B,aAAa,EAAE;IAEvBC,MAAM,CAAC/B,SAAS,CAACmC,IAAI,CAAC,CAACC,OAAO,CAAC7B,QAAQ,CAAC;IACxCwB,MAAM,CAAC/B,SAAS,CAACqC,YAAY,CAAC,CAACD,OAAO,CAAChC,YAAY,CAAC;IACpD2B,MAAM,CAAC7B,WAAW,CAACa,eAAe,CAAC,CAACuB,gBAAgB,EAAE;EACxD,CAAC,CAAC;EAEFT,EAAE,CAAC,2DAA2D,EAAE,MAAK;IACnE1B,eAAe,CAACa,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;IAErChB,OAAO,CAAC6B,aAAa,EAAE;IAEvBC,MAAM,CAAC/B,SAAS,CAACmC,IAAI,CAAC,CAACI,aAAa,EAAE;IACtCR,MAAM,CAAC/B,SAAS,CAACqC,YAAY,CAAC,CAACD,OAAO,CAAChC,YAAY,CAAC;IACpD2B,MAAM,CAAC7B,WAAW,CAACa,eAAe,CAAC,CAACuB,gBAAgB,EAAE;EACxD,CAAC,CAAC;EAEFT,EAAE,CAAC,4CAA4C,EAAE,MAAK;IACpD1B,eAAe,CAACa,GAAG,CAACC,WAAW,CAAC,cAAc,CAAC;IAE/ChB,OAAO,CAAC6B,aAAa,EAAE;IAEvBC,MAAM,CAAC/B,SAAS,CAACmC,IAAI,CAAC,CAACI,aAAa,EAAE;IACtCR,MAAM,CAAC/B,SAAS,CAACqC,YAAY,CAAC,CAACD,OAAO,CAAChC,YAAY,CAAC;IACpD2B,MAAM,CAAC7B,WAAW,CAACa,eAAe,CAAC,CAACuB,gBAAgB,EAAE;EACxD,CAAC,CAAC;EAEFT,EAAE,CAAC,uCAAuC,EAAE,MAAK;IAC/C,MAAMW,OAAO,GAAG,OAAO;IACvBtC,WAAW,CAACgB,UAAU,CAACF,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;IAE5ChB,OAAO,CAAC6B,aAAa,EAAE;IACvB,MAAMW,MAAM,GAAGzC,SAAS,CAAC0C,OAAO,CAACF,OAAO,CAAC;IAEzCT,MAAM,CAACU,MAAM,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC;IACzBZ,MAAM,CAAC7B,WAAW,CAACgB,UAAU,CAAC,CAAC0B,oBAAoB,CAACJ,OAAO,CAAC;EAC9D,CAAC,CAAC;EAEFX,EAAE,CAAC,0CAA0C,EAAE,MAAK;IAClD,MAAMW,OAAO,GAAG,aAAa;IAC7BtC,WAAW,CAACgB,UAAU,CAACF,GAAG,CAACC,WAAW,CAAC,KAAK,CAAC;IAE7ChB,OAAO,CAAC6B,aAAa,EAAE;IACvB,MAAMW,MAAM,GAAGzC,SAAS,CAAC0C,OAAO,CAACF,OAAO,CAAC;IAEzCT,MAAM,CAACU,MAAM,CAAC,CAACE,IAAI,CAAC,KAAK,CAAC;IAC1BZ,MAAM,CAAC7B,WAAW,CAACgB,UAAU,CAAC,CAAC0B,oBAAoB,CAACJ,OAAO,CAAC;EAC9D,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}