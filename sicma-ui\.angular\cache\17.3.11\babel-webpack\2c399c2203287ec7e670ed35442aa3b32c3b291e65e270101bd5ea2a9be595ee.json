{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { ContractAuditStatusService } from './contract-audit-status.service';\ndescribe('ContractAuditStatusService', () => {\n  let service;\n  let httpTestingController;\n  const API_URL = `${environment.apiUrl}/contract-audit-statuses`;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [ContractAuditStatusService]\n    });\n    service = TestBed.inject(ContractAuditStatusService);\n    httpTestingController = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpTestingController.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all contract audit statuses', () => {\n      const mockStatuses = [{\n        id: 1,\n        name: 'Pending',\n        description: 'Audit pending'\n      }, {\n        id: 2,\n        name: 'Approved',\n        description: 'Audit approved'\n      }, {\n        id: 3,\n        name: 'Rejected',\n        description: 'Audit rejected'\n      }];\n      service.getAll().subscribe(statuses => {\n        expect(statuses).toEqual(mockStatuses);\n      });\n      const req = httpTestingController.expectOne(API_URL);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockStatuses);\n    });\n  });\n  describe('getById', () => {\n    it('should return a single contract audit status by id', () => {\n      const mockStatus = {\n        id: 1,\n        name: 'Pending',\n        description: 'Audit pending'\n      };\n      service.getById(1).subscribe(status => {\n        expect(status).toEqual(mockStatus);\n      });\n      const req = httpTestingController.expectOne(`${API_URL}/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockStatus);\n    });\n  });\n  describe('getByName', () => {\n    it('should return a contract audit status by name', () => {\n      const statusName = 'Pending';\n      const mockStatus = {\n        id: 1,\n        name: statusName,\n        description: 'Audit pending'\n      };\n      service.getByName(statusName).subscribe(status => {\n        expect(status).toEqual(mockStatus);\n      });\n      const req = httpTestingController.expectOne(`${API_URL}/name/${statusName}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockStatus);\n    });\n  });\n  describe('create', () => {\n    it('should create a new contract audit status', () => {\n      const newStatus = {\n        name: 'In Progress',\n        description: 'Audit in progress'\n      };\n      const mockResponse = {\n        id: 4,\n        ...newStatus\n      };\n      service.create(newStatus).subscribe(status => {\n        expect(status).toEqual(mockResponse);\n      });\n      const req = httpTestingController.expectOne(API_URL);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newStatus);\n      req.flush(mockResponse);\n    });\n  });\n  describe('update', () => {\n    it('should update an existing contract audit status', () => {\n      const id = 1;\n      const updateStatus = {\n        description: 'Updated description'\n      };\n      const mockResponse = {\n        id: id,\n        name: 'Pending',\n        description: 'Updated description'\n      };\n      service.update(id, updateStatus).subscribe(status => {\n        expect(status).toEqual(mockResponse);\n      });\n      const req = httpTestingController.expectOne(`${API_URL}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateStatus);\n      req.flush(mockResponse);\n    });\n  });\n  describe('delete', () => {\n    it('should delete a contract audit status', () => {\n      const id = 1;\n      service.delete(id).subscribe(response => {\n        expect(response).toBeNull();\n      });\n      const req = httpTestingController.expectOne(`${API_URL}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "ContractAuditStatusService", "describe", "service", "httpTestingController", "API_URL", "apiUrl", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockStatuses", "id", "name", "description", "getAll", "subscribe", "statuses", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "mockStatus", "getById", "status", "statusName", "getByName", "newStatus", "mockResponse", "create", "body", "updateStatus", "update", "delete", "response", "toBeNull"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\services\\contract-audit-status.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { ContractAuditStatus } from '../models/contract-audit-status.model';\nimport { ContractAuditStatusService } from './contract-audit-status.service';\n\ndescribe('ContractAuditStatusService', () => {\n  let service: ContractAuditStatusService;\n  let httpTestingController: HttpTestingController;\n  const API_URL = `${environment.apiUrl}/contract-audit-statuses`;\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [ContractAuditStatusService],\n    });\n    service = TestBed.inject(ContractAuditStatusService);\n    httpTestingController = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpTestingController.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all contract audit statuses', () => {\n      const mockStatuses: ContractAuditStatus[] = [\n        { id: 1, name: 'Pending', description: 'Audit pending' },\n        { id: 2, name: 'Approved', description: 'Audit approved' },\n        { id: 3, name: 'Rejected', description: 'Audit rejected' },\n      ];\n\n      service.getAll().subscribe((statuses) => {\n        expect(statuses).toEqual(mockStatuses);\n      });\n\n      const req = httpTestingController.expectOne(API_URL);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockStatuses);\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a single contract audit status by id', () => {\n      const mockStatus: ContractAuditStatus = {\n        id: 1,\n        name: 'Pending',\n        description: 'Audit pending',\n      };\n\n      service.getById(1).subscribe((status) => {\n        expect(status).toEqual(mockStatus);\n      });\n\n      const req = httpTestingController.expectOne(`${API_URL}/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockStatus);\n    });\n  });\n\n  describe('getByName', () => {\n    it('should return a contract audit status by name', () => {\n      const statusName = 'Pending';\n      const mockStatus: ContractAuditStatus = {\n        id: 1,\n        name: statusName,\n        description: 'Audit pending',\n      };\n\n      service.getByName(statusName).subscribe((status) => {\n        expect(status).toEqual(mockStatus);\n      });\n\n      const req = httpTestingController.expectOne(\n        `${API_URL}/name/${statusName}`,\n      );\n      expect(req.request.method).toBe('GET');\n      req.flush(mockStatus);\n    });\n  });\n\n  describe('create', () => {\n    it('should create a new contract audit status', () => {\n      const newStatus: Omit<ContractAuditStatus, 'id'> = {\n        name: 'In Progress',\n        description: 'Audit in progress',\n      };\n\n      const mockResponse: ContractAuditStatus = {\n        id: 4,\n        ...newStatus,\n      };\n\n      service.create(newStatus).subscribe((status) => {\n        expect(status).toEqual(mockResponse);\n      });\n\n      const req = httpTestingController.expectOne(API_URL);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newStatus);\n      req.flush(mockResponse);\n    });\n  });\n\n  describe('update', () => {\n    it('should update an existing contract audit status', () => {\n      const id = 1;\n      const updateStatus: Partial<ContractAuditStatus> = {\n        description: 'Updated description',\n      };\n\n      const mockResponse: ContractAuditStatus = {\n        id: id,\n        name: 'Pending',\n        description: 'Updated description',\n      };\n\n      service.update(id, updateStatus).subscribe((status) => {\n        expect(status).toEqual(mockResponse);\n      });\n\n      const req = httpTestingController.expectOne(`${API_URL}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateStatus);\n      req.flush(mockResponse);\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete a contract audit status', () => {\n      const id = 1;\n\n      service.delete(id).subscribe((response) => {\n        expect(response).toBeNull();\n      });\n\n      const req = httpTestingController.expectOne(`${API_URL}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,WAAW,QAAQ,MAAM;AAElC,SAASC,0BAA0B,QAAQ,iCAAiC;AAE5EC,QAAQ,CAAC,4BAA4B,EAAE,MAAK;EAC1C,IAAIC,OAAmC;EACvC,IAAIC,qBAA4C;EAChD,MAAMC,OAAO,GAAG,GAAGL,WAAW,CAACM,MAAM,0BAA0B;EAE/DC,UAAU,CAAC,MAAK;IACdR,OAAO,CAACS,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACZ,uBAAuB,CAAC;MAClCa,SAAS,EAAE,CAACT,0BAA0B;KACvC,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACY,MAAM,CAACV,0BAA0B,CAAC;IACpDG,qBAAqB,GAAGL,OAAO,CAACY,MAAM,CAACb,qBAAqB,CAAC;EAC/D,CAAC,CAAC;EAEFc,SAAS,CAAC,MAAK;IACbR,qBAAqB,CAACS,MAAM,EAAE;EAChC,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACZ,OAAO,CAAC,CAACa,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFd,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBY,EAAE,CAAC,2CAA2C,EAAE,MAAK;MACnD,MAAMG,YAAY,GAA0B,CAC1C;QAAEC,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE,SAAS;QAAEC,WAAW,EAAE;MAAe,CAAE,EACxD;QAAEF,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE,UAAU;QAAEC,WAAW,EAAE;MAAgB,CAAE,EAC1D;QAAEF,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE,UAAU;QAAEC,WAAW,EAAE;MAAgB,CAAE,CAC3D;MAEDjB,OAAO,CAACkB,MAAM,EAAE,CAACC,SAAS,CAAEC,QAAQ,IAAI;QACtCR,MAAM,CAACQ,QAAQ,CAAC,CAACC,OAAO,CAACP,YAAY,CAAC;MACxC,CAAC,CAAC;MAEF,MAAMQ,GAAG,GAAGrB,qBAAqB,CAACsB,SAAS,CAACrB,OAAO,CAAC;MACpDU,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACb,YAAY,CAAC;IACzB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFf,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBY,EAAE,CAAC,oDAAoD,EAAE,MAAK;MAC5D,MAAMiB,UAAU,GAAwB;QACtCb,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,SAAS;QACfC,WAAW,EAAE;OACd;MAEDjB,OAAO,CAAC6B,OAAO,CAAC,CAAC,CAAC,CAACV,SAAS,CAAEW,MAAM,IAAI;QACtClB,MAAM,CAACkB,MAAM,CAAC,CAACT,OAAO,CAACO,UAAU,CAAC;MACpC,CAAC,CAAC;MAEF,MAAMN,GAAG,GAAGrB,qBAAqB,CAACsB,SAAS,CAAC,GAAGrB,OAAO,IAAI,CAAC;MAC3DU,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACC,UAAU,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,WAAW,EAAE,MAAK;IACzBY,EAAE,CAAC,+CAA+C,EAAE,MAAK;MACvD,MAAMoB,UAAU,GAAG,SAAS;MAC5B,MAAMH,UAAU,GAAwB;QACtCb,EAAE,EAAE,CAAC;QACLC,IAAI,EAAEe,UAAU;QAChBd,WAAW,EAAE;OACd;MAEDjB,OAAO,CAACgC,SAAS,CAACD,UAAU,CAAC,CAACZ,SAAS,CAAEW,MAAM,IAAI;QACjDlB,MAAM,CAACkB,MAAM,CAAC,CAACT,OAAO,CAACO,UAAU,CAAC;MACpC,CAAC,CAAC;MAEF,MAAMN,GAAG,GAAGrB,qBAAqB,CAACsB,SAAS,CACzC,GAAGrB,OAAO,SAAS6B,UAAU,EAAE,CAChC;MACDnB,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACC,UAAU,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBY,EAAE,CAAC,2CAA2C,EAAE,MAAK;MACnD,MAAMsB,SAAS,GAAoC;QACjDjB,IAAI,EAAE,aAAa;QACnBC,WAAW,EAAE;OACd;MAED,MAAMiB,YAAY,GAAwB;QACxCnB,EAAE,EAAE,CAAC;QACL,GAAGkB;OACJ;MAEDjC,OAAO,CAACmC,MAAM,CAACF,SAAS,CAAC,CAACd,SAAS,CAAEW,MAAM,IAAI;QAC7ClB,MAAM,CAACkB,MAAM,CAAC,CAACT,OAAO,CAACa,YAAY,CAAC;MACtC,CAAC,CAAC;MAEF,MAAMZ,GAAG,GAAGrB,qBAAqB,CAACsB,SAAS,CAACrB,OAAO,CAAC;MACpDU,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCd,MAAM,CAACU,GAAG,CAACE,OAAO,CAACY,IAAI,CAAC,CAACf,OAAO,CAACY,SAAS,CAAC;MAC3CX,GAAG,CAACK,KAAK,CAACO,YAAY,CAAC;IACzB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFnC,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBY,EAAE,CAAC,iDAAiD,EAAE,MAAK;MACzD,MAAMI,EAAE,GAAG,CAAC;MACZ,MAAMsB,YAAY,GAAiC;QACjDpB,WAAW,EAAE;OACd;MAED,MAAMiB,YAAY,GAAwB;QACxCnB,EAAE,EAAEA,EAAE;QACNC,IAAI,EAAE,SAAS;QACfC,WAAW,EAAE;OACd;MAEDjB,OAAO,CAACsC,MAAM,CAACvB,EAAE,EAAEsB,YAAY,CAAC,CAAClB,SAAS,CAAEW,MAAM,IAAI;QACpDlB,MAAM,CAACkB,MAAM,CAAC,CAACT,OAAO,CAACa,YAAY,CAAC;MACtC,CAAC,CAAC;MAEF,MAAMZ,GAAG,GAAGrB,qBAAqB,CAACsB,SAAS,CAAC,GAAGrB,OAAO,IAAIa,EAAE,EAAE,CAAC;MAC/DH,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCd,MAAM,CAACU,GAAG,CAACE,OAAO,CAACY,IAAI,CAAC,CAACf,OAAO,CAACgB,YAAY,CAAC;MAC9Cf,GAAG,CAACK,KAAK,CAACO,YAAY,CAAC;IACzB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFnC,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBY,EAAE,CAAC,uCAAuC,EAAE,MAAK;MAC/C,MAAMI,EAAE,GAAG,CAAC;MAEZf,OAAO,CAACuC,MAAM,CAACxB,EAAE,CAAC,CAACI,SAAS,CAAEqB,QAAQ,IAAI;QACxC5B,MAAM,CAAC4B,QAAQ,CAAC,CAACC,QAAQ,EAAE;MAC7B,CAAC,CAAC;MAEF,MAAMnB,GAAG,GAAGrB,qBAAqB,CAACsB,SAAS,CAAC,GAAGrB,OAAO,IAAIa,EAAE,EAAE,CAAC;MAC/DH,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}