import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { ContractAuditHistory } from '@contract-management/models/contract-audit-history.model';
import { ContractAuditStatus } from '@contract-management/models/contract-audit-status.model';
import { Obligation } from '@contract-management/models/obligation.model';
import { User } from '@core/auth/models/user.model';
import { AuthService } from '@core/auth/services/auth.service';
import { environment } from '@env';
import { of } from 'rxjs';
import { ContractAuditHistoryService } from './contract-audit-history.service';
import { ContractAuditStatusService } from './contract-audit-status.service';
import { ObligationService } from './obligation.service';

describe('ObligationService', () => {
  let service: ObligationService;
  let httpMock: HttpTestingController;
  let authServiceSpy: jasmine.SpyObj<AuthService>;
  let contractAuditHistoryServiceSpy: jasmine.SpyObj<ContractAuditHistoryService>;
  let contractAuditStatusServiceSpy: jasmine.SpyObj<ContractAuditStatusService>;
  const apiUrl = `${environment.apiUrl}/obligations`;

  const mockObligation: Obligation = {
    id: 1,
    name: 'Test Obligation',
    contractId: 1,
    number: 1,
  };

  const mockUser: User = { id: 1, username: 'testuser', profiles: [] };
  const mockCreateStatus: ContractAuditStatus = {
    id: 1,
    name: 'Creación de obligación',
    description: 'Audit status for obligation creation',
  };

  const mockEditStatus: ContractAuditStatus = {
    id: 2,
    name: 'Edición de obligación',
    description: 'Audit status for obligation editing',
  };

  const mockAuditHistory: ContractAuditHistory = {
    id: 1,
    contractId: 1,
    auditStatusId: 1,
    auditDate: new Date(),
    comment: 'Test comment',
    auditorId: 1,
  };

  beforeEach(() => {
    authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);
    contractAuditHistoryServiceSpy = jasmine.createSpyObj(
      'ContractAuditHistoryService',
      ['create'],
    );
    contractAuditStatusServiceSpy = jasmine.createSpyObj(
      'ContractAuditStatusService',
      ['getByName'],
    );

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        ObligationService,
        { provide: AuthService, useValue: authServiceSpy },
        {
          provide: ContractAuditHistoryService,
          useValue: contractAuditHistoryServiceSpy,
        },
        {
          provide: ContractAuditStatusService,
          useValue: contractAuditStatusServiceSpy,
        },
      ],
    });
    service = TestBed.inject(ObligationService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all obligations', () => {
      const mockObligations = [mockObligation];

      service.getAll().subscribe((obligations) => {
        expect(obligations).toEqual(mockObligations);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockObligations);
    });

    it('should handle error when getting all obligations', () => {
      service.getAll().subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(500);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.flush('Internal Server Error', {
        status: 500,
        statusText: 'Internal Server Error',
      });
    });
  });

  describe('getById', () => {
    it('should return an obligation by id', () => {
      const id = 1;

      service.getById(id).subscribe((obligation) => {
        expect(obligation).toEqual(mockObligation);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockObligation);
    });

    it('should handle error when getting obligation by id', () => {
      const id = 999;

      service.getById(id).subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.flush('Not Found', { status: 404, statusText: 'Not Found' });
    });
  });

  describe('create', () => {
    it('should create a new obligation with audit when user is authenticated', () => {
      const newObligation: Omit<Obligation, 'id'> = {
        name: 'Test Obligation',
        contractId: 1,
        number: 1,
      };

      authServiceSpy.getCurrentUser.and.returnValue(mockUser);
      contractAuditStatusServiceSpy.getByName.and.returnValue(
        of(mockCreateStatus),
      );
      contractAuditHistoryServiceSpy.create.and.returnValue(
        of(mockAuditHistory),
      );

      service.create(newObligation).subscribe((obligation) => {
        expect(obligation).toEqual(mockObligation);
        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();
        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith(
          'Creación de obligación',
        );
        expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newObligation);
      req.flush(mockObligation);
    });

    it('should create a new obligation without audit when user is not authenticated', () => {
      const newObligation: Omit<Obligation, 'id'> = {
        name: 'Test Obligation',
        contractId: 1,
        number: 1,
      };

      authServiceSpy.getCurrentUser.and.returnValue(null);

      service.create(newObligation).subscribe((obligation) => {
        expect(obligation).toEqual(mockObligation);
        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();
        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();
        expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newObligation);
      req.flush(mockObligation);
    });

    it('should handle error when creating a new obligation', () => {
      const newObligation: Omit<Obligation, 'id'> = {
        name: 'Test Obligation',
        contractId: 1,
      };

      authServiceSpy.getCurrentUser.and.returnValue(null);

      service.create(newObligation).subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.flush('Bad Request', { status: 400, statusText: 'Bad Request' });
    });
  });

  describe('update', () => {
    it('should update an obligation with audit when name has changed and user is authenticated', () => {
      const id = 1;
      const originalObligation: Obligation = {
        ...mockObligation,
        name: 'Original Name',
        number: 1,
      };
      const updateData: Partial<Obligation> = {
        name: 'Updated Name',
        contractId: 1,
        number: 1,
      };

      authServiceSpy.getCurrentUser.and.returnValue(mockUser);
      contractAuditStatusServiceSpy.getByName.and.returnValue(
        of(mockEditStatus),
      );
      contractAuditHistoryServiceSpy.create.and.returnValue(
        of(mockAuditHistory),
      );

      service.update(id, updateData).subscribe((obligation) => {
        expect(obligation).toEqual(mockObligation);
        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();
        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith(
          'Edición de obligación',
        );
        expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();
      });

      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(getReq.request.method).toBe('GET');
      getReq.flush(originalObligation);

      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(putReq.request.method).toBe('PUT');
      expect(putReq.request.body).toEqual(updateData);
      putReq.flush(mockObligation);
    });

    it('should update an obligation without audit when name has not changed and user is authenticated', () => {
      const id = 1;
      const originalObligation: Obligation = {
        ...mockObligation,
        number: 1,
      };
      const updateData: Partial<Obligation> = {
        name: 'Test Obligation',
        contractId: 1,
        number: 1,
      };

      authServiceSpy.getCurrentUser.and.returnValue(mockUser);

      service.update(id, updateData).subscribe((obligation) => {
        expect(obligation).toEqual(mockObligation);
        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();
        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();
        expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();
      });

      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(getReq.request.method).toBe('GET');
      getReq.flush(originalObligation);

      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(putReq.request.method).toBe('PUT');
      expect(putReq.request.body).toEqual(updateData);
      putReq.flush(mockObligation);
    });

    it('should update an obligation without audit when user is not authenticated', () => {
      const id = 1;
      const originalObligation: Obligation = {
        ...mockObligation,
        name: 'Original Name',
        number: 1,
      };
      const updateData: Partial<Obligation> = {
        name: 'Updated Name',
        contractId: 1,
        number: 1,
      };

      authServiceSpy.getCurrentUser.and.returnValue(null);

      service.update(id, updateData).subscribe((obligation) => {
        expect(obligation).toEqual(mockObligation);
        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();
        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();
        expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();
      });

      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(getReq.request.method).toBe('GET');
      getReq.flush(originalObligation);

      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(putReq.request.method).toBe('PUT');
      expect(putReq.request.body).toEqual(updateData);
      putReq.flush(mockObligation);
    });

    it('should handle error when updating an obligation', () => {
      const id = 999;
      const updateData: Partial<Obligation> = {
        name: 'Updated Name',
        contractId: 1,
      };

      service.update(id, updateData).subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.flush('Not Found', { status: 404, statusText: 'Not Found' });
    });
  });

  describe('delete', () => {
    it('should delete an obligation', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });

    it('should handle error when deleting an obligation', () => {
      const id = 999;

      service.delete(id).subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.flush('Not Found', { status: 404, statusText: 'Not Found' });
    });
  });

  describe('getAllByContractId', () => {
    it('should return all obligations by contract id', () => {
      const contractId = 1;
      const mockObligations = [mockObligation];

      service.getAllByContractId(contractId).subscribe((obligations) => {
        expect(obligations).toEqual(mockObligations);
      });

      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockObligations);
    });

    it('should handle error when getting obligations by contract id', () => {
      const contractId = 999;

      service.getAllByContractId(contractId).subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}`);
      req.flush('Not Found', { status: 404, statusText: 'Not Found' });
    });
  });
});