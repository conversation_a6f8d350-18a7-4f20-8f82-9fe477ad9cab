import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { environment } from '@env';
import {
  AssignProfileRequest,
  Profile,
  UserProfileAssignment,
} from '../models/profile.model';
import { ProfileService } from './profile.service';

describe('ProfileService', () => {
  let service: ProfileService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/profiles`;
  const userProfileUrl = `${environment.apiUrl}/user-profiles`;

  const mockProfile: Profile = {
    id: 1,
    name: 'Test Profile',
  };

  const mockUserProfileAssignment: UserProfileAssignment = {
    userId: 1,
    username: 'testuser',
    profiles: [mockProfile],
  };

  const mockAssignProfileRequest: AssignProfileRequest = {
    user_id: 1,
    profile_id: 1,
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ProfileService],
    });
    service = TestBed.inject(ProfileService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAllProfiles', () => {
    it('should return all profiles', () => {
      const mockProfiles = [mockProfile];

      service.getAllProfiles().subscribe((profiles) => {
        expect(profiles).toEqual(mockProfiles);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockProfiles);
    });

    it('should handle error when getting all profiles', () => {
      service.getAllProfiles().subscribe({
        error: (error) => {
          expect(error.status).toBe(500);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Server error'), { status: 500 });
    });
  });

  describe('getUsersWithProfiles', () => {
    it('should return all users with their profiles', () => {
      const mockUsersWithProfiles = [mockUserProfileAssignment];

      service.getUsersWithProfiles().subscribe((users) => {
        expect(users).toEqual(mockUsersWithProfiles);
      });

      const req = httpMock.expectOne(`${userProfileUrl}/users`);
      expect(req.request.method).toBe('GET');
      req.flush(mockUsersWithProfiles);
    });

    it('should handle error when getting users with profiles', () => {
      service.getUsersWithProfiles().subscribe({
        error: (error) => {
          expect(error.status).toBe(500);
        },
      });

      const req = httpMock.expectOne(`${userProfileUrl}/users`);
      req.error(new ErrorEvent('Server error'), { status: 500 });
    });
  });

  describe('assignProfile', () => {
    it('should assign a profile to a user', () => {
      service.assignProfile(mockAssignProfileRequest).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${userProfileUrl}/assign`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(mockAssignProfileRequest);
      req.flush(null);
    });

    it('should handle error when assigning profile', () => {
      service.assignProfile(mockAssignProfileRequest).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(`${userProfileUrl}/assign`);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('removeProfile', () => {
    it('should remove a profile from a user', () => {
      service.removeProfile(mockAssignProfileRequest).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${userProfileUrl}/remove`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(mockAssignProfileRequest);
      req.flush(null);
    });

    it('should handle error when removing profile', () => {
      service.removeProfile(mockAssignProfileRequest).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(`${userProfileUrl}/remove`);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });
});