import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { CausesSelection } from '@contract-management/models/causes-seletion.model';
import { environment } from '@env';
import { CausesSelectionService } from './causes-selection.service';

describe('CausesSelectionService', () => {
  let service: CausesSelectionService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/causes_selection`;

  const mockCausesSelection: CausesSelection = {
    id: 1,
    name: 'Test Causes Selection',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [CausesSelectionService],
    });
    service = TestBed.inject(CausesSelectionService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all causes selections', () => {
      const mockCausesSelections = [mockCausesSelection];

      service.getAll().subscribe((causesSelections) => {
        expect(causesSelections).toEqual(mockCausesSelections);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockCausesSelections);
    });
  });

  describe('getById', () => {
    it('should return a causes selection by id', () => {
      const id = 1;

      service.getById(id).subscribe((causesSelection) => {
        expect(causesSelection).toEqual(mockCausesSelection);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockCausesSelection);
    });
  });

  describe('create', () => {
    it('should create a new causes selection', () => {
      const newCausesSelection: Omit<CausesSelection, 'id'> = {
        name: 'New Causes Selection',
      };

      service.create(newCausesSelection).subscribe((causesSelection) => {
        expect(causesSelection).toEqual(mockCausesSelection);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newCausesSelection);
      req.flush(mockCausesSelection);
    });
  });

  describe('update', () => {
    it('should update a causes selection', () => {
      const id = 1;
      const updateData: Partial<CausesSelection> = {
        name: 'Updated Causes Selection',
      };

      service.update(id, updateData).subscribe((causesSelection) => {
        expect(causesSelection).toEqual(mockCausesSelection);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockCausesSelection);
    });
  });

  describe('delete', () => {
    it('should delete a causes selection', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });

  describe('getByName', () => {
    it('should return a causes selection by name', () => {
      const name = 'Test Causes Selection';

      service.getByName(name).subscribe((causesSelection) => {
        expect(causesSelection).toEqual(mockCausesSelection);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockCausesSelection);
    });
  });
});