import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { TypeWarranty } from '@contract-management/models/type_warranty.model';
import { environment } from '@env';
import { TypeWarrantyService } from './type_warranty.service';

describe('TypeWarrantyService', () => {
  let service: TypeWarrantyService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/type_warranty`;

  const mockTypeWarranty: TypeWarranty = {
    id: 1,
    name: 'Test Type Warranty',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [TypeWarrantyService],
    });
    service = TestBed.inject(TypeWarrantyService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all type warranties', () => {
      const mockTypeWarranties = [mockTypeWarranty];

      service.getAll().subscribe((typeWarranties) => {
        expect(typeWarranties).toEqual(mockTypeWarranties);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockTypeWarranties);
    });
  });

  describe('getById', () => {
    it('should return a type warranty by id', () => {
      const id = 1;

      service.getById(id).subscribe((typeWarranty) => {
        expect(typeWarranty).toEqual(mockTypeWarranty);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockTypeWarranty);
    });
  });

  describe('create', () => {
    it('should create a new type warranty', () => {
      const newTypeWarranty: Omit<TypeWarranty, 'id'> = {
        name: 'New Type Warranty',
      };

      service.create(newTypeWarranty).subscribe((typeWarranty) => {
        expect(typeWarranty).toEqual(mockTypeWarranty);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newTypeWarranty);
      req.flush(mockTypeWarranty);
    });
  });

  describe('update', () => {
    it('should update a type warranty', () => {
      const id = 1;
      const updateData: Partial<TypeWarranty> = {
        name: 'Updated Type Warranty',
      };

      service.update(id, updateData).subscribe((typeWarranty) => {
        expect(typeWarranty).toEqual(mockTypeWarranty);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockTypeWarranty);
    });
  });

  describe('delete', () => {
    it('should delete a type warranty', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });

  describe('getByName', () => {
    it('should return a type warranty by name', () => {
      const name = 'Test Type Warranty';

      service.getByName(name).subscribe((typeWarranty) => {
        expect(typeWarranty).toEqual(mockTypeWarranty);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockTypeWarranty);
    });
  });
});