import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { environment } from '@env';
import { Municipality } from '@shared/models/municipality.model';
import { MunicipalityService } from './municipality.service';

describe('MunicipalityService', () => {
  let service: MunicipalityService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/municipalities`;

  const mockMunicipality: Municipality = {
    id: 1,
    name: 'Test Municipality',
    departmentId: 1,
    department: {
      id: 1,
      name: 'Test Department',
    },
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [MunicipalityService],
    });
    service = TestBed.inject(MunicipalityService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all municipalities', () => {
      const mockMunicipalities = [mockMunicipality];

      service.getAll().subscribe((municipalities) => {
        expect(municipalities).toEqual(mockMunicipalities);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockMunicipalities);
    });
  });

  describe('getById', () => {
    it('should return a municipality by id', () => {
      const id = 1;

      service.getById(id).subscribe((municipality) => {
        expect(municipality).toEqual(mockMunicipality);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockMunicipality);
    });
  });

  describe('create', () => {
    it('should create a new municipality', () => {
      const newMunicipality: Omit<Municipality, 'id'> = {
        name: 'New Municipality',
        departmentId: 1,
        department: {
          id: 1,
          name: 'Test Department',
        },
      };

      service.create(newMunicipality).subscribe((municipality) => {
        expect(municipality).toEqual(mockMunicipality);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newMunicipality);
      req.flush(mockMunicipality);
    });
  });

  describe('update', () => {
    it('should update a municipality', () => {
      const id = 1;
      const updateData: Partial<Municipality> = {
        name: 'Updated Municipality',
      };

      service.update(id, updateData).subscribe((municipality) => {
        expect(municipality).toEqual(mockMunicipality);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockMunicipality);
    });
  });

  describe('delete', () => {
    it('should delete a municipality', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });

  describe('getAllByDepartmentId', () => {
    it('should return all municipalities by department id', () => {
      const departmentId = 1;
      const mockMunicipalities = [mockMunicipality];

      service.getAllByDepartmentId(departmentId).subscribe((municipalities) => {
        expect(municipalities).toEqual(mockMunicipalities);
      });

      const req = httpMock.expectOne(`${apiUrl}/department/${departmentId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockMunicipalities);
    });
  });

  describe('getByName', () => {
    it('should return a municipality by name', () => {
      const name = 'Test Municipality';

      service.getByName(name).subscribe((municipality) => {
        expect(municipality).toEqual(mockMunicipality);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockMunicipality);
    });
  });
});