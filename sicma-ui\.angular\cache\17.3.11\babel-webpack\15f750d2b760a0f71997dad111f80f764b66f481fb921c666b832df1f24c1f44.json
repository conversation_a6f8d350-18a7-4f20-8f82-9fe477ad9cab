{"ast": null, "code": "function cov_b84wb63gz() {\n  var path = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-dashboard\\\\components\\\\monthly-reports-tab\\\\monthly-report-dialog\\\\monthly-report-basic-data\\\\monthly-report-basic-data.component.ts\";\n  var hash = \"49b7e0b5aa203b0638679ab713c53631ea808ed9\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-dashboard\\\\components\\\\monthly-reports-tab\\\\monthly-report-dialog\\\\monthly-report-basic-data\\\\monthly-report-basic-data.component.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 22,\n          column: 38\n        },\n        end: {\n          line: 463,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 24,\n          column: 8\n        },\n        end: {\n          line: 24,\n          column: 47\n        }\n      },\n      \"2\": {\n        start: {\n          line: 25,\n          column: 8\n        },\n        end: {\n          line: 25,\n          column: 27\n        }\n      },\n      \"3\": {\n        start: {\n          line: 26,\n          column: 8\n        },\n        end: {\n          line: 26,\n          column: 57\n        }\n      },\n      \"4\": {\n        start: {\n          line: 27,\n          column: 8\n        },\n        end: {\n          line: 27,\n          column: 45\n        }\n      },\n      \"5\": {\n        start: {\n          line: 28,\n          column: 8\n        },\n        end: {\n          line: 28,\n          column: 51\n        }\n      },\n      \"6\": {\n        start: {\n          line: 29,\n          column: 8\n        },\n        end: {\n          line: 29,\n          column: 67\n        }\n      },\n      \"7\": {\n        start: {\n          line: 30,\n          column: 8\n        },\n        end: {\n          line: 30,\n          column: 33\n        }\n      },\n      \"8\": {\n        start: {\n          line: 31,\n          column: 8\n        },\n        end: {\n          line: 31,\n          column: 21\n        }\n      },\n      \"9\": {\n        start: {\n          line: 32,\n          column: 8\n        },\n        end: {\n          line: 32,\n          column: 47\n        }\n      },\n      \"10\": {\n        start: {\n          line: 33,\n          column: 8\n        },\n        end: {\n          line: 33,\n          column: 53\n        }\n      },\n      \"11\": {\n        start: {\n          line: 34,\n          column: 8\n        },\n        end: {\n          line: 34,\n          column: 34\n        }\n      },\n      \"12\": {\n        start: {\n          line: 35,\n          column: 8\n        },\n        end: {\n          line: 35,\n          column: 22\n        }\n      },\n      \"13\": {\n        start: {\n          line: 36,\n          column: 8\n        },\n        end: {\n          line: 36,\n          column: 24\n        }\n      },\n      \"14\": {\n        start: {\n          line: 37,\n          column: 8\n        },\n        end: {\n          line: 37,\n          column: 36\n        }\n      },\n      \"15\": {\n        start: {\n          line: 38,\n          column: 8\n        },\n        end: {\n          line: 38,\n          column: 27\n        }\n      },\n      \"16\": {\n        start: {\n          line: 39,\n          column: 8\n        },\n        end: {\n          line: 39,\n          column: 32\n        }\n      },\n      \"17\": {\n        start: {\n          line: 40,\n          column: 8\n        },\n        end: {\n          line: 40,\n          column: 36\n        }\n      },\n      \"18\": {\n        start: {\n          line: 41,\n          column: 8\n        },\n        end: {\n          line: 41,\n          column: 30\n        }\n      },\n      \"19\": {\n        start: {\n          line: 42,\n          column: 8\n        },\n        end: {\n          line: 42,\n          column: 39\n        }\n      },\n      \"20\": {\n        start: {\n          line: 43,\n          column: 8\n        },\n        end: {\n          line: 43,\n          column: 38\n        }\n      },\n      \"21\": {\n        start: {\n          line: 44,\n          column: 8\n        },\n        end: {\n          line: 44,\n          column: 41\n        }\n      },\n      \"22\": {\n        start: {\n          line: 45,\n          column: 8\n        },\n        end: {\n          line: 45,\n          column: 47\n        }\n      },\n      \"23\": {\n        start: {\n          line: 46,\n          column: 8\n        },\n        end: {\n          line: 46,\n          column: 33\n        }\n      },\n      \"24\": {\n        start: {\n          line: 47,\n          column: 8\n        },\n        end: {\n          line: 50,\n          column: 11\n        }\n      },\n      \"25\": {\n        start: {\n          line: 51,\n          column: 8\n        },\n        end: {\n          line: 53,\n          column: 11\n        }\n      },\n      \"26\": {\n        start: {\n          line: 56,\n          column: 8\n        },\n        end: {\n          line: 56,\n          column: 35\n        }\n      },\n      \"27\": {\n        start: {\n          line: 57,\n          column: 8\n        },\n        end: {\n          line: 57,\n          column: 31\n        }\n      },\n      \"28\": {\n        start: {\n          line: 58,\n          column: 8\n        },\n        end: {\n          line: 58,\n          column: 30\n        }\n      },\n      \"29\": {\n        start: {\n          line: 59,\n          column: 8\n        },\n        end: {\n          line: 59,\n          column: 35\n        }\n      },\n      \"30\": {\n        start: {\n          line: 60,\n          column: 8\n        },\n        end: {\n          line: 60,\n          column: 31\n        }\n      },\n      \"31\": {\n        start: {\n          line: 61,\n          column: 8\n        },\n        end: {\n          line: 61,\n          column: 43\n        }\n      },\n      \"32\": {\n        start: {\n          line: 62,\n          column: 8\n        },\n        end: {\n          line: 64,\n          column: 9\n        }\n      },\n      \"33\": {\n        start: {\n          line: 63,\n          column: 12\n        },\n        end: {\n          line: 63,\n          column: 85\n        }\n      },\n      \"34\": {\n        start: {\n          line: 67,\n          column: 8\n        },\n        end: {\n          line: 74,\n          column: 11\n        }\n      },\n      \"35\": {\n        start: {\n          line: 69,\n          column: 16\n        },\n        end: {\n          line: 69,\n          column: 47\n        }\n      },\n      \"36\": {\n        start: {\n          line: 72,\n          column: 16\n        },\n        end: {\n          line: 72,\n          column: 92\n        }\n      },\n      \"37\": {\n        start: {\n          line: 77,\n          column: 8\n        },\n        end: {\n          line: 84,\n          column: 12\n        }\n      },\n      \"38\": {\n        start: {\n          line: 80,\n          column: 25\n        },\n        end: {\n          line: 80,\n          column: 76\n        }\n      },\n      \"39\": {\n        start: {\n          line: 81,\n          column: 12\n        },\n        end: {\n          line: 83,\n          column: 43\n        }\n      },\n      \"40\": {\n        start: {\n          line: 87,\n          column: 28\n        },\n        end: {\n          line: 87,\n          column: 47\n        }\n      },\n      \"41\": {\n        start: {\n          line: 88,\n          column: 8\n        },\n        end: {\n          line: 89,\n          column: 80\n        }\n      },\n      \"42\": {\n        start: {\n          line: 88,\n          column: 55\n        },\n        end: {\n          line: 89,\n          column: 78\n        }\n      },\n      \"43\": {\n        start: {\n          line: 92,\n          column: 8\n        },\n        end: {\n          line: 92,\n          column: 83\n        }\n      },\n      \"44\": {\n        start: {\n          line: 95,\n          column: 8\n        },\n        end: {\n          line: 98,\n          column: 9\n        }\n      },\n      \"45\": {\n        start: {\n          line: 96,\n          column: 12\n        },\n        end: {\n          line: 96,\n          column: 118\n        }\n      },\n      \"46\": {\n        start: {\n          line: 97,\n          column: 12\n        },\n        end: {\n          line: 97,\n          column: 19\n        }\n      },\n      \"47\": {\n        start: {\n          line: 99,\n          column: 8\n        },\n        end: {\n          line: 99,\n          column: 47\n        }\n      },\n      \"48\": {\n        start: {\n          line: 100,\n          column: 8\n        },\n        end: {\n          line: 100,\n          column: 36\n        }\n      },\n      \"49\": {\n        start: {\n          line: 101,\n          column: 8\n        },\n        end: {\n          line: 101,\n          column: 40\n        }\n      },\n      \"50\": {\n        start: {\n          line: 104,\n          column: 8\n        },\n        end: {\n          line: 104,\n          column: 64\n        }\n      },\n      \"51\": {\n        start: {\n          line: 105,\n          column: 34\n        },\n        end: {\n          line: 105,\n          column: 104\n        }\n      },\n      \"52\": {\n        start: {\n          line: 105,\n          column: 63\n        },\n        end: {\n          line: 105,\n          column: 103\n        }\n      },\n      \"53\": {\n        start: {\n          line: 106,\n          column: 8\n        },\n        end: {\n          line: 108,\n          column: 9\n        }\n      },\n      \"54\": {\n        start: {\n          line: 107,\n          column: 12\n        },\n        end: {\n          line: 107,\n          column: 78\n        }\n      },\n      \"55\": {\n        start: {\n          line: 109,\n          column: 8\n        },\n        end: {\n          line: 109,\n          column: 40\n        }\n      },\n      \"56\": {\n        start: {\n          line: 112,\n          column: 8\n        },\n        end: {\n          line: 130,\n          column: 11\n        }\n      },\n      \"57\": {\n        start: {\n          line: 115,\n          column: 12\n        },\n        end: {\n          line: 129,\n          column: 13\n        }\n      },\n      \"58\": {\n        start: {\n          line: 116,\n          column: 16\n        },\n        end: {\n          line: 128,\n          column: 19\n        }\n      },\n      \"59\": {\n        start: {\n          line: 120,\n          column: 24\n        },\n        end: {\n          line: 120,\n          column: 80\n        }\n      },\n      \"60\": {\n        start: {\n          line: 121,\n          column: 24\n        },\n        end: {\n          line: 123,\n          column: 25\n        }\n      },\n      \"61\": {\n        start: {\n          line: 122,\n          column: 28\n        },\n        end: {\n          line: 122,\n          column: 101\n        }\n      },\n      \"62\": {\n        start: {\n          line: 126,\n          column: 24\n        },\n        end: {\n          line: 126,\n          column: 99\n        }\n      },\n      \"63\": {\n        start: {\n          line: 133,\n          column: 8\n        },\n        end: {\n          line: 133,\n          column: 61\n        }\n      },\n      \"64\": {\n        start: {\n          line: 134,\n          column: 8\n        },\n        end: {\n          line: 134,\n          column: 47\n        }\n      },\n      \"65\": {\n        start: {\n          line: 135,\n          column: 8\n        },\n        end: {\n          line: 135,\n          column: 36\n        }\n      },\n      \"66\": {\n        start: {\n          line: 138,\n          column: 8\n        },\n        end: {\n          line: 158,\n          column: 11\n        }\n      },\n      \"67\": {\n        start: {\n          line: 140,\n          column: 16\n        },\n        end: {\n          line: 140,\n          column: 53\n        }\n      },\n      \"68\": {\n        start: {\n          line: 141,\n          column: 16\n        },\n        end: {\n          line: 153,\n          column: 19\n        }\n      },\n      \"69\": {\n        start: {\n          line: 142,\n          column: 20\n        },\n        end: {\n          line: 152,\n          column: 23\n        }\n      },\n      \"70\": {\n        start: {\n          line: 144,\n          column: 28\n        },\n        end: {\n          line: 147,\n          column: 30\n        }\n      },\n      \"71\": {\n        start: {\n          line: 150,\n          column: 28\n        },\n        end: {\n          line: 150,\n          column: 139\n        }\n      },\n      \"72\": {\n        start: {\n          line: 156,\n          column: 16\n        },\n        end: {\n          line: 156,\n          column: 105\n        }\n      },\n      \"73\": {\n        start: {\n          line: 161,\n          column: 8\n        },\n        end: {\n          line: 219,\n          column: 9\n        }\n      },\n      \"74\": {\n        start: {\n          line: 163,\n          column: 39\n        },\n        end: {\n          line: 163,\n          column: 83\n        }\n      },\n      \"75\": {\n        start: {\n          line: 164,\n          column: 31\n        },\n        end: {\n          line: 164,\n          column: 73\n        }\n      },\n      \"76\": {\n        start: {\n          line: 165,\n          column: 12\n        },\n        end: {\n          line: 218,\n          column: 13\n        }\n      },\n      \"77\": {\n        start: {\n          line: 166,\n          column: 16\n        },\n        end: {\n          line: 182,\n          column: 19\n        }\n      },\n      \"78\": {\n        start: {\n          line: 174,\n          column: 24\n        },\n        end: {\n          line: 174,\n          column: 82\n        }\n      },\n      \"79\": {\n        start: {\n          line: 175,\n          column: 24\n        },\n        end: {\n          line: 175,\n          column: 57\n        }\n      },\n      \"80\": {\n        start: {\n          line: 176,\n          column: 24\n        },\n        end: {\n          line: 176,\n          column: 65\n        }\n      },\n      \"81\": {\n        start: {\n          line: 177,\n          column: 24\n        },\n        end: {\n          line: 177,\n          column: 51\n        }\n      },\n      \"82\": {\n        start: {\n          line: 180,\n          column: 24\n        },\n        end: {\n          line: 180,\n          column: 101\n        }\n      },\n      \"83\": {\n        start: {\n          line: 185,\n          column: 16\n        },\n        end: {\n          line: 188,\n          column: 17\n        }\n      },\n      \"84\": {\n        start: {\n          line: 186,\n          column: 20\n        },\n        end: {\n          line: 186,\n          column: 126\n        }\n      },\n      \"85\": {\n        start: {\n          line: 187,\n          column: 20\n        },\n        end: {\n          line: 187,\n          column: 27\n        }\n      },\n      \"86\": {\n        start: {\n          line: 189,\n          column: 41\n        },\n        end: {\n          line: 189,\n          column: 133\n        }\n      },\n      \"87\": {\n        start: {\n          line: 189,\n          column: 85\n        },\n        end: {\n          line: 189,\n          column: 132\n        }\n      },\n      \"88\": {\n        start: {\n          line: 190,\n          column: 16\n        },\n        end: {\n          line: 193,\n          column: 17\n        }\n      },\n      \"89\": {\n        start: {\n          line: 191,\n          column: 20\n        },\n        end: {\n          line: 191,\n          column: 118\n        }\n      },\n      \"90\": {\n        start: {\n          line: 192,\n          column: 20\n        },\n        end: {\n          line: 192,\n          column: 27\n        }\n      },\n      \"91\": {\n        start: {\n          line: 194,\n          column: 16\n        },\n        end: {\n          line: 217,\n          column: 19\n        }\n      },\n      \"92\": {\n        start: {\n          line: 201,\n          column: 24\n        },\n        end: {\n          line: 201,\n          column: 79\n        }\n      },\n      \"93\": {\n        start: {\n          line: 202,\n          column: 24\n        },\n        end: {\n          line: 202,\n          column: 57\n        }\n      },\n      \"94\": {\n        start: {\n          line: 203,\n          column: 24\n        },\n        end: {\n          line: 203,\n          column: 65\n        }\n      },\n      \"95\": {\n        start: {\n          line: 204,\n          column: 24\n        },\n        end: {\n          line: 204,\n          column: 51\n        }\n      },\n      \"96\": {\n        start: {\n          line: 207,\n          column: 24\n        },\n        end: {\n          line: 215,\n          column: 25\n        }\n      },\n      \"97\": {\n        start: {\n          line: 208,\n          column: 28\n        },\n        end: {\n          line: 208,\n          column: 134\n        }\n      },\n      \"98\": {\n        start: {\n          line: 210,\n          column: 29\n        },\n        end: {\n          line: 215,\n          column: 25\n        }\n      },\n      \"99\": {\n        start: {\n          line: 211,\n          column: 28\n        },\n        end: {\n          line: 211,\n          column: 126\n        }\n      },\n      \"100\": {\n        start: {\n          line: 214,\n          column: 28\n        },\n        end: {\n          line: 214,\n          column: 102\n        }\n      },\n      \"101\": {\n        start: {\n          line: 222,\n          column: 8\n        },\n        end: {\n          line: 241,\n          column: 11\n        }\n      },\n      \"102\": {\n        start: {\n          line: 225,\n          column: 41\n        },\n        end: {\n          line: 225,\n          column: 78\n        }\n      },\n      \"103\": {\n        start: {\n          line: 226,\n          column: 12\n        },\n        end: {\n          line: 238,\n          column: 13\n        }\n      },\n      \"104\": {\n        start: {\n          line: 227,\n          column: 16\n        },\n        end: {\n          line: 227,\n          column: 47\n        }\n      },\n      \"105\": {\n        start: {\n          line: 228,\n          column: 16\n        },\n        end: {\n          line: 232,\n          column: 19\n        }\n      },\n      \"106\": {\n        start: {\n          line: 235,\n          column: 16\n        },\n        end: {\n          line: 235,\n          column: 48\n        }\n      },\n      \"107\": {\n        start: {\n          line: 236,\n          column: 16\n        },\n        end: {\n          line: 236,\n          column: 56\n        }\n      },\n      \"108\": {\n        start: {\n          line: 237,\n          column: 16\n        },\n        end: {\n          line: 237,\n          column: 51\n        }\n      },\n      \"109\": {\n        start: {\n          line: 239,\n          column: 12\n        },\n        end: {\n          line: 239,\n          column: 79\n        }\n      },\n      \"110\": {\n        start: {\n          line: 240,\n          column: 12\n        },\n        end: {\n          line: 240,\n          column: 54\n        }\n      },\n      \"111\": {\n        start: {\n          line: 242,\n          column: 8\n        },\n        end: {\n          line: 244,\n          column: 11\n        }\n      },\n      \"112\": {\n        start: {\n          line: 243,\n          column: 12\n        },\n        end: {\n          line: 243,\n          column: 37\n        }\n      },\n      \"113\": {\n        start: {\n          line: 245,\n          column: 8\n        },\n        end: {\n          line: 247,\n          column: 11\n        }\n      },\n      \"114\": {\n        start: {\n          line: 246,\n          column: 12\n        },\n        end: {\n          line: 246,\n          column: 32\n        }\n      },\n      \"115\": {\n        start: {\n          line: 250,\n          column: 8\n        },\n        end: {\n          line: 250,\n          column: 35\n        }\n      },\n      \"116\": {\n        start: {\n          line: 251,\n          column: 8\n        },\n        end: {\n          line: 251,\n          column: 33\n        }\n      },\n      \"117\": {\n        start: {\n          line: 254,\n          column: 8\n        },\n        end: {\n          line: 254,\n          column: 73\n        }\n      },\n      \"118\": {\n        start: {\n          line: 257,\n          column: 37\n        },\n        end: {\n          line: 257,\n          column: 88\n        }\n      },\n      \"119\": {\n        start: {\n          line: 258,\n          column: 35\n        },\n        end: {\n          line: 258,\n          column: 82\n        }\n      },\n      \"120\": {\n        start: {\n          line: 259,\n          column: 8\n        },\n        end: {\n          line: 259,\n          column: 77\n        }\n      },\n      \"121\": {\n        start: {\n          line: 262,\n          column: 8\n        },\n        end: {\n          line: 264,\n          column: 9\n        }\n      },\n      \"122\": {\n        start: {\n          line: 263,\n          column: 12\n        },\n        end: {\n          line: 263,\n          column: 52\n        }\n      },\n      \"123\": {\n        start: {\n          line: 265,\n          column: 8\n        },\n        end: {\n          line: 265,\n          column: 24\n        }\n      },\n      \"124\": {\n        start: {\n          line: 268,\n          column: 37\n        },\n        end: {\n          line: 268,\n          column: 78\n        }\n      },\n      \"125\": {\n        start: {\n          line: 269,\n          column: 30\n        },\n        end: {\n          line: 269,\n          column: 61\n        }\n      },\n      \"126\": {\n        start: {\n          line: 270,\n          column: 37\n        },\n        end: {\n          line: 270,\n          column: 74\n        }\n      },\n      \"127\": {\n        start: {\n          line: 271,\n          column: 8\n        },\n        end: {\n          line: 282,\n          column: 9\n        }\n      },\n      \"128\": {\n        start: {\n          line: 272,\n          column: 12\n        },\n        end: {\n          line: 272,\n          column: 43\n        }\n      },\n      \"129\": {\n        start: {\n          line: 273,\n          column: 12\n        },\n        end: {\n          line: 277,\n          column: 15\n        }\n      },\n      \"130\": {\n        start: {\n          line: 280,\n          column: 12\n        },\n        end: {\n          line: 280,\n          column: 44\n        }\n      },\n      \"131\": {\n        start: {\n          line: 281,\n          column: 12\n        },\n        end: {\n          line: 281,\n          column: 52\n        }\n      },\n      \"132\": {\n        start: {\n          line: 283,\n          column: 8\n        },\n        end: {\n          line: 286,\n          column: 33\n        }\n      },\n      \"133\": {\n        start: {\n          line: 287,\n          column: 8\n        },\n        end: {\n          line: 289,\n          column: 9\n        }\n      },\n      \"134\": {\n        start: {\n          line: 288,\n          column: 12\n        },\n        end: {\n          line: 288,\n          column: 68\n        }\n      },\n      \"135\": {\n        start: {\n          line: 290,\n          column: 8\n        },\n        end: {\n          line: 290,\n          column: 75\n        }\n      },\n      \"136\": {\n        start: {\n          line: 291,\n          column: 8\n        },\n        end: {\n          line: 291,\n          column: 50\n        }\n      },\n      \"137\": {\n        start: {\n          line: 294,\n          column: 8\n        },\n        end: {\n          line: 298,\n          column: 9\n        }\n      },\n      \"138\": {\n        start: {\n          line: 295,\n          column: 30\n        },\n        end: {\n          line: 295,\n          column: 61\n        }\n      },\n      \"139\": {\n        start: {\n          line: 296,\n          column: 12\n        },\n        end: {\n          line: 296,\n          column: 48\n        }\n      },\n      \"140\": {\n        start: {\n          line: 297,\n          column: 12\n        },\n        end: {\n          line: 297,\n          column: 65\n        }\n      },\n      \"141\": {\n        start: {\n          line: 301,\n          column: 27\n        },\n        end: {\n          line: 314,\n          column: 9\n        }\n      },\n      \"142\": {\n        start: {\n          line: 315,\n          column: 8\n        },\n        end: {\n          line: 315,\n          column: 38\n        }\n      },\n      \"143\": {\n        start: {\n          line: 318,\n          column: 8\n        },\n        end: {\n          line: 319,\n          column: 25\n        }\n      },\n      \"144\": {\n        start: {\n          line: 319,\n          column: 12\n        },\n        end: {\n          line: 319,\n          column: 25\n        }\n      },\n      \"145\": {\n        start: {\n          line: 320,\n          column: 8\n        },\n        end: {\n          line: 320,\n          column: 68\n        }\n      },\n      \"146\": {\n        start: {\n          line: 323,\n          column: 8\n        },\n        end: {\n          line: 324,\n          column: 25\n        }\n      },\n      \"147\": {\n        start: {\n          line: 324,\n          column: 12\n        },\n        end: {\n          line: 324,\n          column: 25\n        }\n      },\n      \"148\": {\n        start: {\n          line: 325,\n          column: 8\n        },\n        end: {\n          line: 328,\n          column: 25\n        }\n      },\n      \"149\": {\n        start: {\n          line: 331,\n          column: 8\n        },\n        end: {\n          line: 353,\n          column: 9\n        }\n      },\n      \"150\": {\n        start: {\n          line: 332,\n          column: 12\n        },\n        end: {\n          line: 349,\n          column: 15\n        }\n      },\n      \"151\": {\n        start: {\n          line: 336,\n          column: 20\n        },\n        end: {\n          line: 342,\n          column: 21\n        }\n      },\n      \"152\": {\n        start: {\n          line: 337,\n          column: 24\n        },\n        end: {\n          line: 337,\n          column: 55\n        }\n      },\n      \"153\": {\n        start: {\n          line: 338,\n          column: 24\n        },\n        end: {\n          line: 341,\n          column: 81\n        }\n      },\n      \"154\": {\n        start: {\n          line: 343,\n          column: 20\n        },\n        end: {\n          line: 343,\n          column: 40\n        }\n      },\n      \"155\": {\n        start: {\n          line: 346,\n          column: 20\n        },\n        end: {\n          line: 346,\n          column: 102\n        }\n      },\n      \"156\": {\n        start: {\n          line: 347,\n          column: 20\n        },\n        end: {\n          line: 347,\n          column: 40\n        }\n      },\n      \"157\": {\n        start: {\n          line: 352,\n          column: 12\n        },\n        end: {\n          line: 352,\n          column: 32\n        }\n      },\n      \"158\": {\n        start: {\n          line: 356,\n          column: 8\n        },\n        end: {\n          line: 392,\n          column: 9\n        }\n      },\n      \"159\": {\n        start: {\n          line: 357,\n          column: 34\n        },\n        end: {\n          line: 389,\n          column: 13\n        }\n      },\n      \"160\": {\n        start: {\n          line: 390,\n          column: 12\n        },\n        end: {\n          line: 390,\n          column: 40\n        }\n      },\n      \"161\": {\n        start: {\n          line: 391,\n          column: 12\n        },\n        end: {\n          line: 391,\n          column: 50\n        }\n      },\n      \"162\": {\n        start: {\n          line: 395,\n          column: 8\n        },\n        end: {\n          line: 398,\n          column: 9\n        }\n      },\n      \"163\": {\n        start: {\n          line: 396,\n          column: 12\n        },\n        end: {\n          line: 396,\n          column: 32\n        }\n      },\n      \"164\": {\n        start: {\n          line: 397,\n          column: 12\n        },\n        end: {\n          line: 397,\n          column: 19\n        }\n      },\n      \"165\": {\n        start: {\n          line: 399,\n          column: 8\n        },\n        end: {\n          line: 444,\n          column: 11\n        }\n      },\n      \"166\": {\n        start: {\n          line: 403,\n          column: 43\n        },\n        end: {\n          line: 405,\n          column: 79\n        }\n      },\n      \"167\": {\n        start: {\n          line: 404,\n          column: 35\n        },\n        end: {\n          line: 404,\n          column: 39\n        }\n      },\n      \"168\": {\n        start: {\n          line: 405,\n          column: 32\n        },\n        end: {\n          line: 405,\n          column: 78\n        }\n      },\n      \"169\": {\n        start: {\n          line: 406,\n          column: 16\n        },\n        end: {\n          line: 414,\n          column: 17\n        }\n      },\n      \"170\": {\n        start: {\n          line: 407,\n          column: 20\n        },\n        end: {\n          line: 407,\n          column: 39\n        }\n      },\n      \"171\": {\n        start: {\n          line: 408,\n          column: 20\n        },\n        end: {\n          line: 411,\n          column: 21\n        }\n      },\n      \"172\": {\n        start: {\n          line: 409,\n          column: 24\n        },\n        end: {\n          line: 409,\n          column: 83\n        }\n      },\n      \"173\": {\n        start: {\n          line: 410,\n          column: 24\n        },\n        end: {\n          line: 410,\n          column: 52\n        }\n      },\n      \"174\": {\n        start: {\n          line: 412,\n          column: 20\n        },\n        end: {\n          line: 412,\n          column: 40\n        }\n      },\n      \"175\": {\n        start: {\n          line: 413,\n          column: 20\n        },\n        end: {\n          line: 413,\n          column: 27\n        }\n      },\n      \"176\": {\n        start: {\n          line: 415,\n          column: 16\n        },\n        end: {\n          line: 438,\n          column: 19\n        }\n      },\n      \"177\": {\n        start: {\n          line: 417,\n          column: 44\n        },\n        end: {\n          line: 417,\n          column: 64\n        }\n      },\n      \"178\": {\n        start: {\n          line: 418,\n          column: 47\n        },\n        end: {\n          line: 418,\n          column: 108\n        }\n      },\n      \"179\": {\n        start: {\n          line: 418,\n          column: 74\n        },\n        end: {\n          line: 418,\n          column: 107\n        }\n      },\n      \"180\": {\n        start: {\n          line: 419,\n          column: 24\n        },\n        end: {\n          line: 421,\n          column: 78\n        }\n      },\n      \"181\": {\n        start: {\n          line: 420,\n          column: 43\n        },\n        end: {\n          line: 420,\n          column: 85\n        }\n      },\n      \"182\": {\n        start: {\n          line: 421,\n          column: 54\n        },\n        end: {\n          line: 421,\n          column: 73\n        }\n      },\n      \"183\": {\n        start: {\n          line: 422,\n          column: 24\n        },\n        end: {\n          line: 431,\n          column: 25\n        }\n      },\n      \"184\": {\n        start: {\n          line: 423,\n          column: 28\n        },\n        end: {\n          line: 424,\n          column: 88\n        }\n      },\n      \"185\": {\n        start: {\n          line: 425,\n          column: 57\n        },\n        end: {\n          line: 425,\n          column: 103\n        }\n      },\n      \"186\": {\n        start: {\n          line: 426,\n          column: 28\n        },\n        end: {\n          line: 430,\n          column: 40\n        }\n      },\n      \"187\": {\n        start: {\n          line: 432,\n          column: 24\n        },\n        end: {\n          line: 432,\n          column: 44\n        }\n      },\n      \"188\": {\n        start: {\n          line: 435,\n          column: 24\n        },\n        end: {\n          line: 435,\n          column: 93\n        }\n      },\n      \"189\": {\n        start: {\n          line: 436,\n          column: 24\n        },\n        end: {\n          line: 436,\n          column: 44\n        }\n      },\n      \"190\": {\n        start: {\n          line: 441,\n          column: 16\n        },\n        end: {\n          line: 441,\n          column: 88\n        }\n      },\n      \"191\": {\n        start: {\n          line: 442,\n          column: 16\n        },\n        end: {\n          line: 442,\n          column: 36\n        }\n      },\n      \"192\": {\n        start: {\n          line: 446,\n          column: 13\n        },\n        end: {\n          line: 455,\n          column: 6\n        }\n      },\n      \"193\": {\n        start: {\n          line: 446,\n          column: 41\n        },\n        end: {\n          line: 455,\n          column: 5\n        }\n      },\n      \"194\": {\n        start: {\n          line: 456,\n          column: 13\n        },\n        end: {\n          line: 462,\n          column: 6\n        }\n      },\n      \"195\": {\n        start: {\n          line: 464,\n          column: 0\n        },\n        end: {\n          line: 489,\n          column: 36\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 23,\n            column: 4\n          },\n          end: {\n            line: 23,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 23,\n            column: 138\n          },\n          end: {\n            line: 54,\n            column: 5\n          }\n        },\n        line: 23\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 55,\n            column: 4\n          },\n          end: {\n            line: 55,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 55,\n            column: 15\n          },\n          end: {\n            line: 65,\n            column: 5\n          }\n        },\n        line: 55\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 66,\n            column: 4\n          },\n          end: {\n            line: 66,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 66,\n            column: 22\n          },\n          end: {\n            line: 75,\n            column: 5\n          }\n        },\n        line: 66\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 68,\n            column: 18\n          },\n          end: {\n            line: 68,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 68,\n            column: 35\n          },\n          end: {\n            line: 70,\n            column: 13\n          }\n        },\n        line: 68\n      },\n      \"4\": {\n        name: \"(anonymous_4)\",\n        decl: {\n          start: {\n            line: 71,\n            column: 19\n          },\n          end: {\n            line: 71,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 71,\n            column: 30\n          },\n          end: {\n            line: 73,\n            column: 13\n          }\n        },\n        line: 71\n      },\n      \"5\": {\n        name: \"(anonymous_5)\",\n        decl: {\n          start: {\n            line: 76,\n            column: 4\n          },\n          end: {\n            line: 76,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 76,\n            column: 34\n          },\n          end: {\n            line: 85,\n            column: 5\n          }\n        },\n        line: 76\n      },\n      \"6\": {\n        name: \"(anonymous_6)\",\n        decl: {\n          start: {\n            line: 79,\n            column: 50\n          },\n          end: {\n            line: 79,\n            column: 51\n          }\n        },\n        loc: {\n          start: {\n            line: 79,\n            column: 61\n          },\n          end: {\n            line: 84,\n            column: 9\n          }\n        },\n        line: 79\n      },\n      \"7\": {\n        name: \"(anonymous_7)\",\n        decl: {\n          start: {\n            line: 86,\n            column: 4\n          },\n          end: {\n            line: 86,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 86,\n            column: 30\n          },\n          end: {\n            line: 90,\n            column: 5\n          }\n        },\n        line: 86\n      },\n      \"8\": {\n        name: \"(anonymous_8)\",\n        decl: {\n          start: {\n            line: 88,\n            column: 39\n          },\n          end: {\n            line: 88,\n            column: 40\n          }\n        },\n        loc: {\n          start: {\n            line: 88,\n            column: 55\n          },\n          end: {\n            line: 89,\n            column: 78\n          }\n        },\n        line: 88\n      },\n      \"9\": {\n        name: \"(anonymous_9)\",\n        decl: {\n          start: {\n            line: 91,\n            column: 4\n          },\n          end: {\n            line: 91,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 91,\n            column: 34\n          },\n          end: {\n            line: 93,\n            column: 5\n          }\n        },\n        line: 91\n      },\n      \"10\": {\n        name: \"(anonymous_10)\",\n        decl: {\n          start: {\n            line: 94,\n            column: 4\n          },\n          end: {\n            line: 94,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 94,\n            column: 23\n          },\n          end: {\n            line: 102,\n            column: 5\n          }\n        },\n        line: 94\n      },\n      \"11\": {\n        name: \"(anonymous_11)\",\n        decl: {\n          start: {\n            line: 103,\n            column: 4\n          },\n          end: {\n            line: 103,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 103,\n            column: 39\n          },\n          end: {\n            line: 110,\n            column: 5\n          }\n        },\n        line: 103\n      },\n      \"12\": {\n        name: \"(anonymous_12)\",\n        decl: {\n          start: {\n            line: 105,\n            column: 56\n          },\n          end: {\n            line: 105,\n            column: 57\n          }\n        },\n        loc: {\n          start: {\n            line: 105,\n            column: 63\n          },\n          end: {\n            line: 105,\n            column: 103\n          }\n        },\n        line: 105\n      },\n      \"13\": {\n        name: \"(anonymous_13)\",\n        decl: {\n          start: {\n            line: 111,\n            column: 4\n          },\n          end: {\n            line: 111,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 111,\n            column: 43\n          },\n          end: {\n            line: 131,\n            column: 5\n          }\n        },\n        line: 111\n      },\n      \"14\": {\n        name: \"(anonymous_14)\",\n        decl: {\n          start: {\n            line: 114,\n            column: 18\n          },\n          end: {\n            line: 114,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 114,\n            column: 35\n          },\n          end: {\n            line: 130,\n            column: 9\n          }\n        },\n        line: 114\n      },\n      \"15\": {\n        name: \"(anonymous_15)\",\n        decl: {\n          start: {\n            line: 119,\n            column: 26\n          },\n          end: {\n            line: 119,\n            column: 27\n          }\n        },\n        loc: {\n          start: {\n            line: 119,\n            column: 32\n          },\n          end: {\n            line: 124,\n            column: 21\n          }\n        },\n        line: 119\n      },\n      \"16\": {\n        name: \"(anonymous_16)\",\n        decl: {\n          start: {\n            line: 125,\n            column: 27\n          },\n          end: {\n            line: 125,\n            column: 28\n          }\n        },\n        loc: {\n          start: {\n            line: 125,\n            column: 38\n          },\n          end: {\n            line: 127,\n            column: 21\n          }\n        },\n        line: 125\n      },\n      \"17\": {\n        name: \"(anonymous_17)\",\n        decl: {\n          start: {\n            line: 132,\n            column: 4\n          },\n          end: {\n            line: 132,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 132,\n            column: 27\n          },\n          end: {\n            line: 136,\n            column: 5\n          }\n        },\n        line: 132\n      },\n      \"18\": {\n        name: \"(anonymous_18)\",\n        decl: {\n          start: {\n            line: 137,\n            column: 4\n          },\n          end: {\n            line: 137,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 137,\n            column: 40\n          },\n          end: {\n            line: 159,\n            column: 5\n          }\n        },\n        line: 137\n      },\n      \"19\": {\n        name: \"(anonymous_19)\",\n        decl: {\n          start: {\n            line: 139,\n            column: 18\n          },\n          end: {\n            line: 139,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 139,\n            column: 33\n          },\n          end: {\n            line: 154,\n            column: 13\n          }\n        },\n        line: 139\n      },\n      \"20\": {\n        name: \"(anonymous_20)\",\n        decl: {\n          start: {\n            line: 141,\n            column: 49\n          },\n          end: {\n            line: 141,\n            column: 50\n          }\n        },\n        loc: {\n          start: {\n            line: 141,\n            column: 70\n          },\n          end: {\n            line: 153,\n            column: 17\n          }\n        },\n        line: 141\n      },\n      \"21\": {\n        name: \"(anonymous_21)\",\n        decl: {\n          start: {\n            line: 143,\n            column: 30\n          },\n          end: {\n            line: 143,\n            column: 31\n          }\n        },\n        loc: {\n          start: {\n            line: 143,\n            column: 46\n          },\n          end: {\n            line: 148,\n            column: 25\n          }\n        },\n        line: 143\n      },\n      \"22\": {\n        name: \"(anonymous_22)\",\n        decl: {\n          start: {\n            line: 149,\n            column: 31\n          },\n          end: {\n            line: 149,\n            column: 32\n          }\n        },\n        loc: {\n          start: {\n            line: 149,\n            column: 42\n          },\n          end: {\n            line: 151,\n            column: 25\n          }\n        },\n        line: 149\n      },\n      \"23\": {\n        name: \"(anonymous_23)\",\n        decl: {\n          start: {\n            line: 155,\n            column: 19\n          },\n          end: {\n            line: 155,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 155,\n            column: 30\n          },\n          end: {\n            line: 157,\n            column: 13\n          }\n        },\n        line: 155\n      },\n      \"24\": {\n        name: \"(anonymous_24)\",\n        decl: {\n          start: {\n            line: 160,\n            column: 4\n          },\n          end: {\n            line: 160,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 160,\n            column: 21\n          },\n          end: {\n            line: 220,\n            column: 5\n          }\n        },\n        line: 160\n      },\n      \"25\": {\n        name: \"(anonymous_25)\",\n        decl: {\n          start: {\n            line: 173,\n            column: 26\n          },\n          end: {\n            line: 173,\n            column: 27\n          }\n        },\n        loc: {\n          start: {\n            line: 173,\n            column: 32\n          },\n          end: {\n            line: 178,\n            column: 21\n          }\n        },\n        line: 173\n      },\n      \"26\": {\n        name: \"(anonymous_26)\",\n        decl: {\n          start: {\n            line: 179,\n            column: 27\n          },\n          end: {\n            line: 179,\n            column: 28\n          }\n        },\n        loc: {\n          start: {\n            line: 179,\n            column: 38\n          },\n          end: {\n            line: 181,\n            column: 21\n          }\n        },\n        line: 179\n      },\n      \"27\": {\n        name: \"(anonymous_27)\",\n        decl: {\n          start: {\n            line: 189,\n            column: 71\n          },\n          end: {\n            line: 189,\n            column: 72\n          }\n        },\n        loc: {\n          start: {\n            line: 189,\n            column: 85\n          },\n          end: {\n            line: 189,\n            column: 132\n          }\n        },\n        line: 189\n      },\n      \"28\": {\n        name: \"(anonymous_28)\",\n        decl: {\n          start: {\n            line: 200,\n            column: 26\n          },\n          end: {\n            line: 200,\n            column: 27\n          }\n        },\n        loc: {\n          start: {\n            line: 200,\n            column: 32\n          },\n          end: {\n            line: 205,\n            column: 21\n          }\n        },\n        line: 200\n      },\n      \"29\": {\n        name: \"(anonymous_29)\",\n        decl: {\n          start: {\n            line: 206,\n            column: 27\n          },\n          end: {\n            line: 206,\n            column: 28\n          }\n        },\n        loc: {\n          start: {\n            line: 206,\n            column: 38\n          },\n          end: {\n            line: 216,\n            column: 21\n          }\n        },\n        line: 206\n      },\n      \"30\": {\n        name: \"(anonymous_30)\",\n        decl: {\n          start: {\n            line: 221,\n            column: 4\n          },\n          end: {\n            line: 221,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 221,\n            column: 26\n          },\n          end: {\n            line: 248,\n            column: 5\n          }\n        },\n        line: 221\n      },\n      \"31\": {\n        name: \"(anonymous_31)\",\n        decl: {\n          start: {\n            line: 224,\n            column: 37\n          },\n          end: {\n            line: 224,\n            column: 38\n          }\n        },\n        loc: {\n          start: {\n            line: 224,\n            column: 50\n          },\n          end: {\n            line: 241,\n            column: 9\n          }\n        },\n        line: 224\n      },\n      \"32\": {\n        name: \"(anonymous_32)\",\n        decl: {\n          start: {\n            line: 242,\n            column: 49\n          },\n          end: {\n            line: 242,\n            column: 50\n          }\n        },\n        loc: {\n          start: {\n            line: 242,\n            column: 55\n          },\n          end: {\n            line: 244,\n            column: 9\n          }\n        },\n        line: 242\n      },\n      \"33\": {\n        name: \"(anonymous_33)\",\n        decl: {\n          start: {\n            line: 245,\n            column: 48\n          },\n          end: {\n            line: 245,\n            column: 49\n          }\n        },\n        loc: {\n          start: {\n            line: 245,\n            column: 54\n          },\n          end: {\n            line: 247,\n            column: 9\n          }\n        },\n        line: 245\n      },\n      \"34\": {\n        name: \"(anonymous_34)\",\n        decl: {\n          start: {\n            line: 249,\n            column: 4\n          },\n          end: {\n            line: 249,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 249,\n            column: 34\n          },\n          end: {\n            line: 252,\n            column: 5\n          }\n        },\n        line: 249\n      },\n      \"35\": {\n        name: \"(anonymous_35)\",\n        decl: {\n          start: {\n            line: 253,\n            column: 4\n          },\n          end: {\n            line: 253,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 253,\n            column: 25\n          },\n          end: {\n            line: 255,\n            column: 5\n          }\n        },\n        line: 253\n      },\n      \"36\": {\n        name: \"(anonymous_36)\",\n        decl: {\n          start: {\n            line: 256,\n            column: 4\n          },\n          end: {\n            line: 256,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 256,\n            column: 24\n          },\n          end: {\n            line: 260,\n            column: 5\n          }\n        },\n        line: 256\n      },\n      \"37\": {\n        name: \"(anonymous_37)\",\n        decl: {\n          start: {\n            line: 261,\n            column: 4\n          },\n          end: {\n            line: 261,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 261,\n            column: 15\n          },\n          end: {\n            line: 266,\n            column: 5\n          }\n        },\n        line: 261\n      },\n      \"38\": {\n        name: \"(anonymous_38)\",\n        decl: {\n          start: {\n            line: 267,\n            column: 4\n          },\n          end: {\n            line: 267,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 267,\n            column: 21\n          },\n          end: {\n            line: 292,\n            column: 5\n          }\n        },\n        line: 267\n      },\n      \"39\": {\n        name: \"(anonymous_39)\",\n        decl: {\n          start: {\n            line: 293,\n            column: 4\n          },\n          end: {\n            line: 293,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 293,\n            column: 22\n          },\n          end: {\n            line: 299,\n            column: 5\n          }\n        },\n        line: 293\n      },\n      \"40\": {\n        name: \"(anonymous_40)\",\n        decl: {\n          start: {\n            line: 300,\n            column: 4\n          },\n          end: {\n            line: 300,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 300,\n            column: 29\n          },\n          end: {\n            line: 316,\n            column: 5\n          }\n        },\n        line: 300\n      },\n      \"41\": {\n        name: \"(anonymous_41)\",\n        decl: {\n          start: {\n            line: 317,\n            column: 4\n          },\n          end: {\n            line: 317,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 317,\n            column: 21\n          },\n          end: {\n            line: 321,\n            column: 5\n          }\n        },\n        line: 317\n      },\n      \"42\": {\n        name: \"(anonymous_42)\",\n        decl: {\n          start: {\n            line: 322,\n            column: 4\n          },\n          end: {\n            line: 322,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 322,\n            column: 26\n          },\n          end: {\n            line: 329,\n            column: 5\n          }\n        },\n        line: 322\n      },\n      \"43\": {\n        name: \"(anonymous_43)\",\n        decl: {\n          start: {\n            line: 330,\n            column: 4\n          },\n          end: {\n            line: 330,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 330,\n            column: 26\n          },\n          end: {\n            line: 354,\n            column: 5\n          }\n        },\n        line: 330\n      },\n      \"44\": {\n        name: \"(anonymous_44)\",\n        decl: {\n          start: {\n            line: 335,\n            column: 22\n          },\n          end: {\n            line: 335,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 335,\n            column: 35\n          },\n          end: {\n            line: 344,\n            column: 17\n          }\n        },\n        line: 335\n      },\n      \"45\": {\n        name: \"(anonymous_45)\",\n        decl: {\n          start: {\n            line: 345,\n            column: 23\n          },\n          end: {\n            line: 345,\n            column: 24\n          }\n        },\n        loc: {\n          start: {\n            line: 345,\n            column: 34\n          },\n          end: {\n            line: 348,\n            column: 17\n          }\n        },\n        line: 345\n      },\n      \"46\": {\n        name: \"(anonymous_46)\",\n        decl: {\n          start: {\n            line: 355,\n            column: 4\n          },\n          end: {\n            line: 355,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 355,\n            column: 19\n          },\n          end: {\n            line: 393,\n            column: 5\n          }\n        },\n        line: 355\n      },\n      \"47\": {\n        name: \"(anonymous_47)\",\n        decl: {\n          start: {\n            line: 394,\n            column: 4\n          },\n          end: {\n            line: 394,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 394,\n            column: 19\n          },\n          end: {\n            line: 445,\n            column: 5\n          }\n        },\n        line: 394\n      },\n      \"48\": {\n        name: \"(anonymous_48)\",\n        decl: {\n          start: {\n            line: 402,\n            column: 18\n          },\n          end: {\n            line: 402,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 402,\n            column: 31\n          },\n          end: {\n            line: 439,\n            column: 13\n          }\n        },\n        line: 402\n      },\n      \"49\": {\n        name: \"(anonymous_49)\",\n        decl: {\n          start: {\n            line: 404,\n            column: 28\n          },\n          end: {\n            line: 404,\n            column: 29\n          }\n        },\n        loc: {\n          start: {\n            line: 404,\n            column: 35\n          },\n          end: {\n            line: 404,\n            column: 39\n          }\n        },\n        line: 404\n      },\n      \"50\": {\n        name: \"(anonymous_50)\",\n        decl: {\n          start: {\n            line: 405,\n            column: 25\n          },\n          end: {\n            line: 405,\n            column: 26\n          }\n        },\n        loc: {\n          start: {\n            line: 405,\n            column: 32\n          },\n          end: {\n            line: 405,\n            column: 78\n          }\n        },\n        line: 405\n      },\n      \"51\": {\n        name: \"(anonymous_51)\",\n        decl: {\n          start: {\n            line: 416,\n            column: 26\n          },\n          end: {\n            line: 416,\n            column: 27\n          }\n        },\n        loc: {\n          start: {\n            line: 416,\n            column: 45\n          },\n          end: {\n            line: 433,\n            column: 21\n          }\n        },\n        line: 416\n      },\n      \"52\": {\n        name: \"(anonymous_52)\",\n        decl: {\n          start: {\n            line: 418,\n            column: 64\n          },\n          end: {\n            line: 418,\n            column: 65\n          }\n        },\n        loc: {\n          start: {\n            line: 418,\n            column: 74\n          },\n          end: {\n            line: 418,\n            column: 107\n          }\n        },\n        line: 418\n      },\n      \"53\": {\n        name: \"(anonymous_53)\",\n        decl: {\n          start: {\n            line: 420,\n            column: 36\n          },\n          end: {\n            line: 420,\n            column: 37\n          }\n        },\n        loc: {\n          start: {\n            line: 420,\n            column: 43\n          },\n          end: {\n            line: 420,\n            column: 85\n          }\n        },\n        line: 420\n      },\n      \"54\": {\n        name: \"(anonymous_54)\",\n        decl: {\n          start: {\n            line: 421,\n            column: 36\n          },\n          end: {\n            line: 421,\n            column: 37\n          }\n        },\n        loc: {\n          start: {\n            line: 421,\n            column: 54\n          },\n          end: {\n            line: 421,\n            column: 73\n          }\n        },\n        line: 421\n      },\n      \"55\": {\n        name: \"(anonymous_55)\",\n        decl: {\n          start: {\n            line: 434,\n            column: 27\n          },\n          end: {\n            line: 434,\n            column: 28\n          }\n        },\n        loc: {\n          start: {\n            line: 434,\n            column: 38\n          },\n          end: {\n            line: 437,\n            column: 21\n          }\n        },\n        line: 434\n      },\n      \"56\": {\n        name: \"(anonymous_56)\",\n        decl: {\n          start: {\n            line: 440,\n            column: 19\n          },\n          end: {\n            line: 440,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 440,\n            column: 30\n          },\n          end: {\n            line: 443,\n            column: 13\n          }\n        },\n        line: 440\n      },\n      \"57\": {\n        name: \"(anonymous_57)\",\n        decl: {\n          start: {\n            line: 446,\n            column: 35\n          },\n          end: {\n            line: 446,\n            column: 36\n          }\n        },\n        loc: {\n          start: {\n            line: 446,\n            column: 41\n          },\n          end: {\n            line: 455,\n            column: 5\n          }\n        },\n        line: 446\n      }\n    },\n    branchMap: {\n      \"0\": {\n        loc: {\n          start: {\n            line: 62,\n            column: 8\n          },\n          end: {\n            line: 64,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 62,\n            column: 8\n          },\n          end: {\n            line: 64,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 62\n      },\n      \"1\": {\n        loc: {\n          start: {\n            line: 72,\n            column: 33\n          },\n          end: {\n            line: 72,\n            column: 90\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 72,\n            column: 33\n          },\n          end: {\n            line: 72,\n            column: 52\n          }\n        }, {\n          start: {\n            line: 72,\n            column: 56\n          },\n          end: {\n            line: 72,\n            column: 90\n          }\n        }],\n        line: 72\n      },\n      \"2\": {\n        loc: {\n          start: {\n            line: 80,\n            column: 25\n          },\n          end: {\n            line: 80,\n            column: 76\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 80,\n            column: 53\n          },\n          end: {\n            line: 80,\n            column: 58\n          }\n        }, {\n          start: {\n            line: 80,\n            column: 61\n          },\n          end: {\n            line: 80,\n            column: 76\n          }\n        }],\n        line: 80\n      },\n      \"3\": {\n        loc: {\n          start: {\n            line: 81,\n            column: 19\n          },\n          end: {\n            line: 83,\n            column: 42\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 82,\n            column: 18\n          },\n          end: {\n            line: 82,\n            column: 47\n          }\n        }, {\n          start: {\n            line: 83,\n            column: 18\n          },\n          end: {\n            line: 83,\n            column: 42\n          }\n        }],\n        line: 81\n      },\n      \"4\": {\n        loc: {\n          start: {\n            line: 88,\n            column: 55\n          },\n          end: {\n            line: 89,\n            column: 78\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 88,\n            column: 55\n          },\n          end: {\n            line: 88,\n            column: 110\n          }\n        }, {\n          start: {\n            line: 89,\n            column: 12\n          },\n          end: {\n            line: 89,\n            column: 78\n          }\n        }],\n        line: 88\n      },\n      \"5\": {\n        loc: {\n          start: {\n            line: 92,\n            column: 15\n          },\n          end: {\n            line: 92,\n            column: 82\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 92,\n            column: 28\n          },\n          end: {\n            line: 92,\n            column: 77\n          }\n        }, {\n          start: {\n            line: 92,\n            column: 80\n          },\n          end: {\n            line: 92,\n            column: 82\n          }\n        }],\n        line: 92\n      },\n      \"6\": {\n        loc: {\n          start: {\n            line: 95,\n            column: 8\n          },\n          end: {\n            line: 98,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 95,\n            column: 8\n          },\n          end: {\n            line: 98,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 95\n      },\n      \"7\": {\n        loc: {\n          start: {\n            line: 106,\n            column: 8\n          },\n          end: {\n            line: 108,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 106,\n            column: 8\n          },\n          end: {\n            line: 108,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 106\n      },\n      \"8\": {\n        loc: {\n          start: {\n            line: 115,\n            column: 12\n          },\n          end: {\n            line: 129,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 115,\n            column: 12\n          },\n          end: {\n            line: 129,\n            column: 13\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 115\n      },\n      \"9\": {\n        loc: {\n          start: {\n            line: 121,\n            column: 24\n          },\n          end: {\n            line: 123,\n            column: 25\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 121,\n            column: 24\n          },\n          end: {\n            line: 123,\n            column: 25\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 121\n      },\n      \"10\": {\n        loc: {\n          start: {\n            line: 126,\n            column: 41\n          },\n          end: {\n            line: 126,\n            column: 97\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 126,\n            column: 41\n          },\n          end: {\n            line: 126,\n            column: 60\n          }\n        }, {\n          start: {\n            line: 126,\n            column: 64\n          },\n          end: {\n            line: 126,\n            column: 97\n          }\n        }],\n        line: 126\n      },\n      \"11\": {\n        loc: {\n          start: {\n            line: 150,\n            column: 45\n          },\n          end: {\n            line: 150,\n            column: 137\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 150,\n            column: 45\n          },\n          end: {\n            line: 150,\n            column: 64\n          }\n        }, {\n          start: {\n            line: 150,\n            column: 68\n          },\n          end: {\n            line: 150,\n            column: 137\n          }\n        }],\n        line: 150\n      },\n      \"12\": {\n        loc: {\n          start: {\n            line: 156,\n            column: 33\n          },\n          end: {\n            line: 156,\n            column: 103\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 156,\n            column: 33\n          },\n          end: {\n            line: 156,\n            column: 52\n          }\n        }, {\n          start: {\n            line: 156,\n            column: 56\n          },\n          end: {\n            line: 156,\n            column: 103\n          }\n        }],\n        line: 156\n      },\n      \"13\": {\n        loc: {\n          start: {\n            line: 161,\n            column: 8\n          },\n          end: {\n            line: 219,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 161,\n            column: 8\n          },\n          end: {\n            line: 219,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 161\n      },\n      \"14\": {\n        loc: {\n          start: {\n            line: 161,\n            column: 12\n          },\n          end: {\n            line: 162,\n            column: 56\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 161,\n            column: 12\n          },\n          end: {\n            line: 161,\n            column: 37\n          }\n        }, {\n          start: {\n            line: 162,\n            column: 12\n          },\n          end: {\n            line: 162,\n            column: 56\n          }\n        }],\n        line: 161\n      },\n      \"15\": {\n        loc: {\n          start: {\n            line: 165,\n            column: 12\n          },\n          end: {\n            line: 218,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 165,\n            column: 12\n          },\n          end: {\n            line: 218,\n            column: 13\n          }\n        }, {\n          start: {\n            line: 184,\n            column: 17\n          },\n          end: {\n            line: 218,\n            column: 13\n          }\n        }],\n        line: 165\n      },\n      \"16\": {\n        loc: {\n          start: {\n            line: 180,\n            column: 41\n          },\n          end: {\n            line: 180,\n            column: 99\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 180,\n            column: 41\n          },\n          end: {\n            line: 180,\n            column: 60\n          }\n        }, {\n          start: {\n            line: 180,\n            column: 64\n          },\n          end: {\n            line: 180,\n            column: 99\n          }\n        }],\n        line: 180\n      },\n      \"17\": {\n        loc: {\n          start: {\n            line: 185,\n            column: 16\n          },\n          end: {\n            line: 188,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 185,\n            column: 16\n          },\n          end: {\n            line: 188,\n            column: 17\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 185\n      },\n      \"18\": {\n        loc: {\n          start: {\n            line: 190,\n            column: 16\n          },\n          end: {\n            line: 193,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 190,\n            column: 16\n          },\n          end: {\n            line: 193,\n            column: 17\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 190\n      },\n      \"19\": {\n        loc: {\n          start: {\n            line: 207,\n            column: 24\n          },\n          end: {\n            line: 215,\n            column: 25\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 207,\n            column: 24\n          },\n          end: {\n            line: 215,\n            column: 25\n          }\n        }, {\n          start: {\n            line: 210,\n            column: 29\n          },\n          end: {\n            line: 215,\n            column: 25\n          }\n        }],\n        line: 207\n      },\n      \"20\": {\n        loc: {\n          start: {\n            line: 210,\n            column: 29\n          },\n          end: {\n            line: 215,\n            column: 25\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 210,\n            column: 29\n          },\n          end: {\n            line: 215,\n            column: 25\n          }\n        }, {\n          start: {\n            line: 213,\n            column: 29\n          },\n          end: {\n            line: 215,\n            column: 25\n          }\n        }],\n        line: 210\n      },\n      \"21\": {\n        loc: {\n          start: {\n            line: 214,\n            column: 45\n          },\n          end: {\n            line: 214,\n            column: 100\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 214,\n            column: 45\n          },\n          end: {\n            line: 214,\n            column: 64\n          }\n        }, {\n          start: {\n            line: 214,\n            column: 68\n          },\n          end: {\n            line: 214,\n            column: 100\n          }\n        }],\n        line: 214\n      },\n      \"22\": {\n        loc: {\n          start: {\n            line: 226,\n            column: 12\n          },\n          end: {\n            line: 238,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 226,\n            column: 12\n          },\n          end: {\n            line: 238,\n            column: 13\n          }\n        }, {\n          start: {\n            line: 234,\n            column: 17\n          },\n          end: {\n            line: 238,\n            column: 13\n          }\n        }],\n        line: 226\n      },\n      \"23\": {\n        loc: {\n          start: {\n            line: 258,\n            column: 35\n          },\n          end: {\n            line: 258,\n            column: 82\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 258,\n            column: 35\n          },\n          end: {\n            line: 258,\n            column: 56\n          }\n        }, {\n          start: {\n            line: 258,\n            column: 60\n          },\n          end: {\n            line: 258,\n            column: 82\n          }\n        }],\n        line: 258\n      },\n      \"24\": {\n        loc: {\n          start: {\n            line: 259,\n            column: 37\n          },\n          end: {\n            line: 259,\n            column: 75\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 259,\n            column: 37\n          },\n          end: {\n            line: 259,\n            column: 55\n          }\n        }, {\n          start: {\n            line: 259,\n            column: 59\n          },\n          end: {\n            line: 259,\n            column: 75\n          }\n        }],\n        line: 259\n      },\n      \"25\": {\n        loc: {\n          start: {\n            line: 262,\n            column: 8\n          },\n          end: {\n            line: 264,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 262,\n            column: 8\n          },\n          end: {\n            line: 264,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 262\n      },\n      \"26\": {\n        loc: {\n          start: {\n            line: 268,\n            column: 37\n          },\n          end: {\n            line: 268,\n            column: 78\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 268,\n            column: 37\n          },\n          end: {\n            line: 268,\n            column: 69\n          }\n        }, {\n          start: {\n            line: 268,\n            column: 73\n          },\n          end: {\n            line: 268,\n            column: 78\n          }\n        }],\n        line: 268\n      },\n      \"27\": {\n        loc: {\n          start: {\n            line: 269,\n            column: 30\n          },\n          end: {\n            line: 269,\n            column: 61\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 269,\n            column: 30\n          },\n          end: {\n            line: 269,\n            column: 55\n          }\n        }, {\n          start: {\n            line: 269,\n            column: 59\n          },\n          end: {\n            line: 269,\n            column: 61\n          }\n        }],\n        line: 269\n      },\n      \"28\": {\n        loc: {\n          start: {\n            line: 271,\n            column: 8\n          },\n          end: {\n            line: 282,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 271,\n            column: 8\n          },\n          end: {\n            line: 282,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 279,\n            column: 13\n          },\n          end: {\n            line: 282,\n            column: 9\n          }\n        }],\n        line: 271\n      },\n      \"29\": {\n        loc: {\n          start: {\n            line: 287,\n            column: 8\n          },\n          end: {\n            line: 289,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 287,\n            column: 8\n          },\n          end: {\n            line: 289,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 287\n      },\n      \"30\": {\n        loc: {\n          start: {\n            line: 294,\n            column: 8\n          },\n          end: {\n            line: 298,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 294,\n            column: 8\n          },\n          end: {\n            line: 298,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 294\n      },\n      \"31\": {\n        loc: {\n          start: {\n            line: 318,\n            column: 8\n          },\n          end: {\n            line: 319,\n            column: 25\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 318,\n            column: 8\n          },\n          end: {\n            line: 319,\n            column: 25\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 318\n      },\n      \"32\": {\n        loc: {\n          start: {\n            line: 320,\n            column: 15\n          },\n          end: {\n            line: 320,\n            column: 67\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 320,\n            column: 15\n          },\n          end: {\n            line: 320,\n            column: 58\n          }\n        }, {\n          start: {\n            line: 320,\n            column: 62\n          },\n          end: {\n            line: 320,\n            column: 67\n          }\n        }],\n        line: 320\n      },\n      \"33\": {\n        loc: {\n          start: {\n            line: 323,\n            column: 8\n          },\n          end: {\n            line: 324,\n            column: 25\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 323,\n            column: 8\n          },\n          end: {\n            line: 324,\n            column: 25\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 323\n      },\n      \"34\": {\n        loc: {\n          start: {\n            line: 331,\n            column: 8\n          },\n          end: {\n            line: 353,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 331,\n            column: 8\n          },\n          end: {\n            line: 353,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 351,\n            column: 13\n          },\n          end: {\n            line: 353,\n            column: 9\n          }\n        }],\n        line: 331\n      },\n      \"35\": {\n        loc: {\n          start: {\n            line: 336,\n            column: 20\n          },\n          end: {\n            line: 342,\n            column: 21\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 336,\n            column: 20\n          },\n          end: {\n            line: 342,\n            column: 21\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 336\n      },\n      \"36\": {\n        loc: {\n          start: {\n            line: 339,\n            column: 29\n          },\n          end: {\n            line: 339,\n            column: 67\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 339,\n            column: 29\n          },\n          end: {\n            line: 339,\n            column: 62\n          }\n        }, {\n          start: {\n            line: 339,\n            column: 66\n          },\n          end: {\n            line: 339,\n            column: 67\n          }\n        }],\n        line: 339\n      },\n      \"37\": {\n        loc: {\n          start: {\n            line: 340,\n            column: 33\n          },\n          end: {\n            line: 340,\n            column: 78\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 340,\n            column: 33\n          },\n          end: {\n            line: 340,\n            column: 73\n          }\n        }, {\n          start: {\n            line: 340,\n            column: 77\n          },\n          end: {\n            line: 340,\n            column: 78\n          }\n        }],\n        line: 340\n      },\n      \"38\": {\n        loc: {\n          start: {\n            line: 341,\n            column: 33\n          },\n          end: {\n            line: 341,\n            column: 79\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 341,\n            column: 33\n          },\n          end: {\n            line: 341,\n            column: 74\n          }\n        }, {\n          start: {\n            line: 341,\n            column: 78\n          },\n          end: {\n            line: 341,\n            column: 79\n          }\n        }],\n        line: 341\n      },\n      \"39\": {\n        loc: {\n          start: {\n            line: 346,\n            column: 37\n          },\n          end: {\n            line: 346,\n            column: 100\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 346,\n            column: 37\n          },\n          end: {\n            line: 346,\n            column: 56\n          }\n        }, {\n          start: {\n            line: 346,\n            column: 60\n          },\n          end: {\n            line: 346,\n            column: 100\n          }\n        }],\n        line: 346\n      },\n      \"40\": {\n        loc: {\n          start: {\n            line: 356,\n            column: 8\n          },\n          end: {\n            line: 392,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 356,\n            column: 8\n          },\n          end: {\n            line: 392,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 356\n      },\n      \"41\": {\n        loc: {\n          start: {\n            line: 356,\n            column: 12\n          },\n          end: {\n            line: 356,\n            column: 47\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 356,\n            column: 12\n          },\n          end: {\n            line: 356,\n            column: 23\n          }\n        }, {\n          start: {\n            line: 356,\n            column: 27\n          },\n          end: {\n            line: 356,\n            column: 47\n          }\n        }],\n        line: 356\n      },\n      \"42\": {\n        loc: {\n          start: {\n            line: 380,\n            column: 28\n          },\n          end: {\n            line: 380,\n            column: 85\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 380,\n            column: 28\n          },\n          end: {\n            line: 380,\n            column: 50\n          }\n        }, {\n          start: {\n            line: 380,\n            column: 54\n          },\n          end: {\n            line: 380,\n            column: 85\n          }\n        }],\n        line: 380\n      },\n      \"43\": {\n        loc: {\n          start: {\n            line: 386,\n            column: 31\n          },\n          end: {\n            line: 388,\n            column: 23\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 387,\n            column: 22\n          },\n          end: {\n            line: 387,\n            column: 66\n          }\n        }, {\n          start: {\n            line: 388,\n            column: 22\n          },\n          end: {\n            line: 388,\n            column: 23\n          }\n        }],\n        line: 386\n      },\n      \"44\": {\n        loc: {\n          start: {\n            line: 395,\n            column: 8\n          },\n          end: {\n            line: 398,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 395,\n            column: 8\n          },\n          end: {\n            line: 398,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 395\n      },\n      \"45\": {\n        loc: {\n          start: {\n            line: 406,\n            column: 16\n          },\n          end: {\n            line: 414,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 406,\n            column: 16\n          },\n          end: {\n            line: 414,\n            column: 17\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 406\n      },\n      \"46\": {\n        loc: {\n          start: {\n            line: 408,\n            column: 20\n          },\n          end: {\n            line: 411,\n            column: 21\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 408,\n            column: 20\n          },\n          end: {\n            line: 411,\n            column: 21\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 408\n      },\n      \"47\": {\n        loc: {\n          start: {\n            line: 409,\n            column: 46\n          },\n          end: {\n            line: 409,\n            column: 82\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 409,\n            column: 46\n          },\n          end: {\n            line: 409,\n            column: 77\n          }\n        }, {\n          start: {\n            line: 409,\n            column: 81\n          },\n          end: {\n            line: 409,\n            column: 82\n          }\n        }],\n        line: 409\n      },\n      \"48\": {\n        loc: {\n          start: {\n            line: 422,\n            column: 24\n          },\n          end: {\n            line: 431,\n            column: 25\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 422,\n            column: 24\n          },\n          end: {\n            line: 431,\n            column: 25\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 422\n      },\n      \"49\": {\n        loc: {\n          start: {\n            line: 424,\n            column: 33\n          },\n          end: {\n            line: 424,\n            column: 69\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 424,\n            column: 33\n          },\n          end: {\n            line: 424,\n            column: 64\n          }\n        }, {\n          start: {\n            line: 424,\n            column: 68\n          },\n          end: {\n            line: 424,\n            column: 69\n          }\n        }],\n        line: 424\n      },\n      \"50\": {\n        loc: {\n          start: {\n            line: 425,\n            column: 75\n          },\n          end: {\n            line: 425,\n            column: 102\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 425,\n            column: 75\n          },\n          end: {\n            line: 425,\n            column: 97\n          }\n        }, {\n          start: {\n            line: 425,\n            column: 101\n          },\n          end: {\n            line: 425,\n            column: 102\n          }\n        }],\n        line: 425\n      },\n      \"51\": {\n        loc: {\n          start: {\n            line: 427,\n            column: 32\n          },\n          end: {\n            line: 430,\n            column: 39\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 429,\n            column: 38\n          },\n          end: {\n            line: 429,\n            column: 92\n          }\n        }, {\n          start: {\n            line: 430,\n            column: 38\n          },\n          end: {\n            line: 430,\n            column: 39\n          }\n        }],\n        line: 427\n      },\n      \"52\": {\n        loc: {\n          start: {\n            line: 427,\n            column: 32\n          },\n          end: {\n            line: 428,\n            column: 71\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 427,\n            column: 32\n          },\n          end: {\n            line: 427,\n            column: 63\n          }\n        }, {\n          start: {\n            line: 428,\n            column: 36\n          },\n          end: {\n            line: 428,\n            column: 71\n          }\n        }],\n        line: 427\n      },\n      \"53\": {\n        loc: {\n          start: {\n            line: 435,\n            column: 41\n          },\n          end: {\n            line: 435,\n            column: 91\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 435,\n            column: 41\n          },\n          end: {\n            line: 435,\n            column: 60\n          }\n        }, {\n          start: {\n            line: 435,\n            column: 64\n          },\n          end: {\n            line: 435,\n            column: 91\n          }\n        }],\n        line: 435\n      },\n      \"54\": {\n        loc: {\n          start: {\n            line: 441,\n            column: 33\n          },\n          end: {\n            line: 441,\n            column: 86\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 441,\n            column: 33\n          },\n          end: {\n            line: 441,\n            column: 52\n          }\n        }, {\n          start: {\n            line: 441,\n            column: 56\n          },\n          end: {\n            line: 441,\n            column: 86\n          }\n        }],\n        line: 441\n      }\n    },\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0,\n      \"36\": 0,\n      \"37\": 0,\n      \"38\": 0,\n      \"39\": 0,\n      \"40\": 0,\n      \"41\": 0,\n      \"42\": 0,\n      \"43\": 0,\n      \"44\": 0,\n      \"45\": 0,\n      \"46\": 0,\n      \"47\": 0,\n      \"48\": 0,\n      \"49\": 0,\n      \"50\": 0,\n      \"51\": 0,\n      \"52\": 0,\n      \"53\": 0,\n      \"54\": 0,\n      \"55\": 0,\n      \"56\": 0,\n      \"57\": 0,\n      \"58\": 0,\n      \"59\": 0,\n      \"60\": 0,\n      \"61\": 0,\n      \"62\": 0,\n      \"63\": 0,\n      \"64\": 0,\n      \"65\": 0,\n      \"66\": 0,\n      \"67\": 0,\n      \"68\": 0,\n      \"69\": 0,\n      \"70\": 0,\n      \"71\": 0,\n      \"72\": 0,\n      \"73\": 0,\n      \"74\": 0,\n      \"75\": 0,\n      \"76\": 0,\n      \"77\": 0,\n      \"78\": 0,\n      \"79\": 0,\n      \"80\": 0,\n      \"81\": 0,\n      \"82\": 0,\n      \"83\": 0,\n      \"84\": 0,\n      \"85\": 0,\n      \"86\": 0,\n      \"87\": 0,\n      \"88\": 0,\n      \"89\": 0,\n      \"90\": 0,\n      \"91\": 0,\n      \"92\": 0,\n      \"93\": 0,\n      \"94\": 0,\n      \"95\": 0,\n      \"96\": 0,\n      \"97\": 0,\n      \"98\": 0,\n      \"99\": 0,\n      \"100\": 0,\n      \"101\": 0,\n      \"102\": 0,\n      \"103\": 0,\n      \"104\": 0,\n      \"105\": 0,\n      \"106\": 0,\n      \"107\": 0,\n      \"108\": 0,\n      \"109\": 0,\n      \"110\": 0,\n      \"111\": 0,\n      \"112\": 0,\n      \"113\": 0,\n      \"114\": 0,\n      \"115\": 0,\n      \"116\": 0,\n      \"117\": 0,\n      \"118\": 0,\n      \"119\": 0,\n      \"120\": 0,\n      \"121\": 0,\n      \"122\": 0,\n      \"123\": 0,\n      \"124\": 0,\n      \"125\": 0,\n      \"126\": 0,\n      \"127\": 0,\n      \"128\": 0,\n      \"129\": 0,\n      \"130\": 0,\n      \"131\": 0,\n      \"132\": 0,\n      \"133\": 0,\n      \"134\": 0,\n      \"135\": 0,\n      \"136\": 0,\n      \"137\": 0,\n      \"138\": 0,\n      \"139\": 0,\n      \"140\": 0,\n      \"141\": 0,\n      \"142\": 0,\n      \"143\": 0,\n      \"144\": 0,\n      \"145\": 0,\n      \"146\": 0,\n      \"147\": 0,\n      \"148\": 0,\n      \"149\": 0,\n      \"150\": 0,\n      \"151\": 0,\n      \"152\": 0,\n      \"153\": 0,\n      \"154\": 0,\n      \"155\": 0,\n      \"156\": 0,\n      \"157\": 0,\n      \"158\": 0,\n      \"159\": 0,\n      \"160\": 0,\n      \"161\": 0,\n      \"162\": 0,\n      \"163\": 0,\n      \"164\": 0,\n      \"165\": 0,\n      \"166\": 0,\n      \"167\": 0,\n      \"168\": 0,\n      \"169\": 0,\n      \"170\": 0,\n      \"171\": 0,\n      \"172\": 0,\n      \"173\": 0,\n      \"174\": 0,\n      \"175\": 0,\n      \"176\": 0,\n      \"177\": 0,\n      \"178\": 0,\n      \"179\": 0,\n      \"180\": 0,\n      \"181\": 0,\n      \"182\": 0,\n      \"183\": 0,\n      \"184\": 0,\n      \"185\": 0,\n      \"186\": 0,\n      \"187\": 0,\n      \"188\": 0,\n      \"189\": 0,\n      \"190\": 0,\n      \"191\": 0,\n      \"192\": 0,\n      \"193\": 0,\n      \"194\": 0,\n      \"195\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0,\n      \"36\": 0,\n      \"37\": 0,\n      \"38\": 0,\n      \"39\": 0,\n      \"40\": 0,\n      \"41\": 0,\n      \"42\": 0,\n      \"43\": 0,\n      \"44\": 0,\n      \"45\": 0,\n      \"46\": 0,\n      \"47\": 0,\n      \"48\": 0,\n      \"49\": 0,\n      \"50\": 0,\n      \"51\": 0,\n      \"52\": 0,\n      \"53\": 0,\n      \"54\": 0,\n      \"55\": 0,\n      \"56\": 0,\n      \"57\": 0\n    },\n    b: {\n      \"0\": [0, 0],\n      \"1\": [0, 0],\n      \"2\": [0, 0],\n      \"3\": [0, 0],\n      \"4\": [0, 0],\n      \"5\": [0, 0],\n      \"6\": [0, 0],\n      \"7\": [0, 0],\n      \"8\": [0, 0],\n      \"9\": [0, 0],\n      \"10\": [0, 0],\n      \"11\": [0, 0],\n      \"12\": [0, 0],\n      \"13\": [0, 0],\n      \"14\": [0, 0],\n      \"15\": [0, 0],\n      \"16\": [0, 0],\n      \"17\": [0, 0],\n      \"18\": [0, 0],\n      \"19\": [0, 0],\n      \"20\": [0, 0],\n      \"21\": [0, 0],\n      \"22\": [0, 0],\n      \"23\": [0, 0],\n      \"24\": [0, 0],\n      \"25\": [0, 0],\n      \"26\": [0, 0],\n      \"27\": [0, 0],\n      \"28\": [0, 0],\n      \"29\": [0, 0],\n      \"30\": [0, 0],\n      \"31\": [0, 0],\n      \"32\": [0, 0],\n      \"33\": [0, 0],\n      \"34\": [0, 0],\n      \"35\": [0, 0],\n      \"36\": [0, 0],\n      \"37\": [0, 0],\n      \"38\": [0, 0],\n      \"39\": [0, 0],\n      \"40\": [0, 0],\n      \"41\": [0, 0],\n      \"42\": [0, 0],\n      \"43\": [0, 0],\n      \"44\": [0, 0],\n      \"45\": [0, 0],\n      \"46\": [0, 0],\n      \"47\": [0, 0],\n      \"48\": [0, 0],\n      \"49\": [0, 0],\n      \"50\": [0, 0],\n      \"51\": [0, 0],\n      \"52\": [0, 0],\n      \"53\": [0, 0],\n      \"54\": [0, 0]\n    },\n    inputSourceMap: {\n      version: 3,\n      file: \"monthly-report-basic-data.component.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-dashboard\\\\components\\\\monthly-reports-tab\\\\monthly-report-dialog\\\\monthly-report-basic-data\\\\monthly-report-basic-data.component.ts\"],\n      names: [],\n      mappings: \";;;AAAA,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAC;AAChF,OAAO,EACL,SAAS,EACT,YAAY,EACZ,KAAK,EAEL,MAAM,EACN,SAAS,GACV,MAAM,eAAe,CAAC;AACvB,OAAO,EACL,WAAW,EAEX,mBAAmB,EACnB,UAAU,GACX,MAAM,gBAAgB,CAAC;AACxB,OAAO,EAAE,qBAAqB,EAAE,sBAAsB,EAAE,MAAM,gCAAgC,CAAC;AAC/F,OAAO,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAC3D,OAAO,EAAE,WAAW,EAAE,MAAM,4BAA4B,CAAC;AACzD,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,8BAA8B,CAAC;AAChF,OAAO,EAAE,OAAO,EAAE,MAAM,wBAAwB,CAAC;AACjD,OAAO,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AACnD,OAAO,EAAE,SAAS,EAAE,MAAM,wBAAwB,CAAC;AAGnD,OAAO,EAAE,eAAe,EAAE,MAAM,gDAAgD,CAAC;AACjF,OAAO,EAAE,yBAAyB,EAAE,MAAM,2DAA2D,CAAC;AAGtG,OAAO,EAAE,oBAAoB,EAAE,MAAM,uDAAuD,CAAC;AAC7F,OAAO,EAAE,cAAc,EAAE,MAAM,gDAAgD,CAAC;AAChF,OAAO,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAE9D,OAAO,EAAE,iBAAiB,EAAE,MAAM,oDAAoD,CAAC;AACvF,OAAO,EAAc,QAAQ,EAAE,GAAG,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,MAAM,CAAC;AAChE,OAAO,EAAE,yBAAyB,EAAE,MAAM,iDAAiD,CAAC;AA0BrF,IAAM,+BAA+B,GAArC,MAAM,+BAA+B;IA+B1C,YACmB,eAAgC,EAChC,KAAmB,EACnB,oBAA0C,EAC1C,cAA8B,EAC9B,iBAAoC,EACpC,yBAAoD,EACpD,QAAkB,EAClB,EAAe;QAPf,oBAAe,GAAf,eAAe,CAAiB;QAChC,UAAK,GAAL,KAAK,CAAc;QACnB,yBAAoB,GAApB,oBAAoB,CAAsB;QAC1C,mBAAc,GAAd,cAAc,CAAgB;QAC9B,sBAAiB,GAAjB,iBAAiB,CAAmB;QACpC,8BAAyB,GAAzB,yBAAyB,CAA2B;QACpD,aAAQ,GAAR,QAAQ,CAAU;QAClB,OAAE,GAAF,EAAE,CAAa;QArCxB,iBAAY,GAAG,IAAI,YAAY,EAAiB;QAChD,uBAAkB,GAAG,IAAI,YAAY,EAAW;QACjD,iBAAY,GAAG,KAAK;QAI7B,SAAI,GAAG,CAAC,CAAC;QACT,UAAK,GAAG,EAAE,CAAC;QACX,oBAAe,GAA2B,IAAI,CAAC;QAC/C,cAAS,GAAG,CAAC,CAAC;QACd,mBAAc,GAAG,CAAC,CAAC;QACnB,uBAAkB,GAAG,CAAC,CAAC;QACvB,gBAAW,GAAiB,EAAE,CAAC;QAE/B,uBAAkB,GAA8B,IAAI,CAAC;QACrD,wBAAmB,GAAyB,EAAE,CAAC;QAC/C,wBAAmB,GAAG,KAAK,CAAC;QAC5B,+BAA0B,GAAkB,IAAI,CAAC;QACjD,gBAAW,GAAG,KAAK,CAAC;QAEpB,gBAAW,GAAc,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;YACrC,oBAAoB,EAAE,CAAC,KAAK,CAAC;YAC7B,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;SAC/C,CAAC,CAAC;QAEH,mBAAc,GAAc,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;YACxC,UAAU,EAAE,CAAC,EAAE,EAAE,UAAU,CAAC,QAAQ,CAAC;SACtC,CAAC,CAAC;IAWA,CAAC;IAEJ,QAAQ;QACN,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACnC,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;YACjD,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAEO,eAAe;QACrB,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,SAAS,CAAC;YACxC,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE;gBACpB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;YACjC,CAAC;YACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,kCAAkC,CAAC,CAAC;YAC9E,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAEO,2BAA2B;QACjC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,cAAc;aAC3C,GAAG,CAAC,YAAY,CAAE;aAClB,YAAY,CAAC,IAAI,CAChB,SAAS,CAAC,EAAE,CAAC,EACb,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YACZ,MAAM,IAAI,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC;YACjE,OAAO,IAAI;gBACT,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;gBAC/B,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QAC/B,CAAC,CAAC,CACH,CAAC;IACN,CAAC;IAEO,kBAAkB,CAAC,KAAa;QACtC,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACxC,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAC5B,CAAC,UAAU,EAAE,EAAE,CACb,UAAU,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC;YACvD,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,CACrE,CAAC;IACJ,CAAC;IAED,iBAAiB,CAAC,UAAsB;QACtC,OAAO,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,QAAQ,KAAK,UAAU,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;IAC7E,CAAC;IAED,gBAAgB;QACd,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACzC,IAAI,CAAC,KAAK,CAAC,OAAO,CAChB,iBAAiB,EACjB,kEAAkE,CACnE,CAAC;YACF,OAAO;QACT,CAAC;QACD,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;QACvC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAC5B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IAClC,CAAC;IAED,cAAc,CAAC,kBAAsC;QACnD,IAAI,CAAC,0BAA0B,GAAG,kBAAkB,CAAC,EAAE,CAAC;QACxD,MAAM,iBAAiB,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAC7C,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,kBAAkB,CAAC,YAAY,CAChD,CAAC;QACF,IAAI,iBAAiB,EAAE,CAAC;YACtB,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,UAAU,EAAE,iBAAiB,EAAE,CAAC,CAAC;QACpE,CAAC;QACD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IAClC,CAAC;IAED,gBAAgB,CAAC,oBAA4B;QAC3C,IAAI,CAAC,KAAK;aACP,OAAO,CACN,2CAA2C,EAC3C,kCAAkC,CACnC;aACA,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE;YACpB,IAAI,WAAW,EAAE,CAAC;gBAChB,IAAI,CAAC,yBAAyB;qBAC3B,MAAM,CAAC,oBAAoB,CAAC;qBAC5B,SAAS,CAAC;oBACT,IAAI,EAAE,GAAG,EAAE;wBACT,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAC;wBACxD,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;4BACjD,IAAI,CAAC,uBAAuB,CAC1B,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,CAC3C,CAAC;wBACJ,CAAC;oBACH,CAAC;oBACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;wBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,iCAAiC,CAAC,CAAC;oBAC7E,CAAC;iBACF,CAAC,CAAC;YACP,CAAC;QACH,CAAC,CAAC,CAAC;IACP,CAAC;IAED,oBAAoB;QAClB,IAAI,CAAC,mBAAmB,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC;QACrD,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;QACvC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;IAC9B,CAAC;IAEO,uBAAuB,CAAC,UAAkB;QAChD,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC;YACnE,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE;gBAClB,IAAI,CAAC,mBAAmB,GAAG,SAAS,CAAC;gBAErC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;oBACnD,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC;wBAC9D,IAAI,EAAE,CAAC,UAAU,EAAE,EAAE;4BACnB,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,GAAG;gCAChC,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC;gCAClC,UAAU,EAAE,UAAU;6BACvB,CAAC;wBACJ,CAAC;wBACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;4BACf,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,8CAA8C,QAAQ,CAAC,YAAY,EAAE,CAC7F,CAAC;wBACJ,CAAC;qBACF,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC;YACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,+CAA+C,CAAC,CAAC;YAC3F,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAED,cAAc;QACZ,IACE,IAAI,CAAC,cAAc,CAAC,KAAK;YACzB,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,QAAQ,EAAE,EAAE,EAC5C,CAAC;YACD,MAAM,kBAAkB,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,KAAK,CAAC;YACxE,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAE9D,IAAI,IAAI,CAAC,0BAA0B,EAAE,CAAC;gBACpC,IAAI,CAAC,yBAAyB;qBAC3B,MAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE;oBACvC,EAAE,EAAE,IAAI,CAAC,0BAA0B;oBACnC,YAAY,EAAE,kBAAkB,CAAC,EAAE;oBACnC,UAAU,EAAE,UAAU;iBACvB,CAAC;qBACD,SAAS,CAAC;oBACT,IAAI,EAAE,GAAG,EAAE;wBACT,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,qCAAqC,CAAC,CAAC;wBAC1D,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;wBACjC,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;wBACzC,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAC7B,CAAC;oBACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;wBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,mCAAmC,CAAC,CAAC;oBAC/E,CAAC;iBACF,CAAC,CAAC;YACP,CAAC;iBAAM,CAAC;gBACN,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;oBACzC,IAAI,CAAC,KAAK,CAAC,OAAO,CAChB,iBAAiB,EACjB,kEAAkE,CACnE,CAAC;oBACF,OAAO;gBACT,CAAC;gBAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CACpD,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,YAAY,KAAK,kBAAkB,CAAC,EAAE,CAC9D,CAAC;gBAEF,IAAI,gBAAgB,EAAE,CAAC;oBACrB,IAAI,CAAC,KAAK,CAAC,OAAO,CAChB,wBAAwB,EACxB,mDAAmD,CACpD,CAAC;oBACF,OAAO;gBACT,CAAC;gBAED,IAAI,CAAC,yBAAyB;qBAC3B,MAAM,CAAC;oBACN,YAAY,EAAE,kBAAkB,CAAC,EAAE;oBACnC,UAAU,EAAE,UAAU;iBACvB,CAAC;qBACD,SAAS,CAAC;oBACT,IAAI,EAAE,GAAG,EAAE;wBACT,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,kCAAkC,CAAC,CAAC;wBACvD,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;wBACjC,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;wBACzC,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAC7B,CAAC;oBACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;wBACf,IACE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,CAC5B,qCAAqC,CACtC,EACD,CAAC;4BACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAChB,iBAAiB,EACjB,kEAAkE,CACnE,CAAC;wBACJ,CAAC;6BAAM,IAAI,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,oBAAoB,CAAC,EAAE,CAAC;4BAChE,IAAI,CAAC,KAAK,CAAC,OAAO,CAChB,wBAAwB,EACxB,mDAAmD,CACpD,CAAC;wBACJ,CAAC;6BAAM,CAAC;4BACN,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,gCAAgC,CAAC,CAAC;wBAC5E,CAAC;oBACH,CAAC;iBACF,CAAC,CAAC;YACP,CAAC;QACH,CAAC;IACH,CAAC;IAEO,mBAAmB;QACzB,IAAI,CAAC,WAAW;aACb,GAAG,CAAC,sBAAsB,CAAC;YAC5B,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,EAAE;YACnC,MAAM,oBAAoB,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;YACnE,IAAI,OAAO,EAAE,CAAC;gBACZ,oBAAoB,EAAE,MAAM,EAAE,CAAC;gBAC/B,oBAAoB,EAAE,aAAa,CAAC;oBAClC,UAAU,CAAC,QAAQ;oBACnB,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;oBACjB,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC;iBAC5B,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,oBAAoB,EAAE,OAAO,EAAE,CAAC;gBAChC,oBAAoB,EAAE,eAAe,EAAE,CAAC;gBACxC,oBAAoB,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;YACrC,CAAC;YACD,oBAAoB,EAAE,sBAAsB,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;YACnE,IAAI,CAAC,WAAW,CAAC,sBAAsB,EAAE,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEL,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,EAAE;YAC5C,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,EAAE;YAC3C,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,oBAAoB,CAAC,OAAgB;QACnC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC;QAC3B,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAED,kBAAkB;QAChB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;IACnE,CAAC;IAEO,iBAAiB;QACvB,MAAM,oBAAoB,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAC/C,sBAAsB,CACvB,EAAE,KAAK,CAAC;QACT,MAAM,kBAAkB,GAAG,CAAC,oBAAoB,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;QAC3E,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC;IACvE,CAAC;IAED,QAAQ;QACN,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;QAC1C,CAAC;QACD,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC;IAClB,CAAC;IAEO,cAAc;QACpB,MAAM,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,IAAI,KAAK,CAAC;QACvE,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,EAAE,CAAC;QAEtD,MAAM,oBAAoB,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QACnE,IAAI,oBAAoB,EAAE,CAAC;YACzB,oBAAoB,EAAE,MAAM,EAAE,CAAC;YAC/B,oBAAoB,EAAE,aAAa,CAAC;gBAClC,UAAU,CAAC,QAAQ;gBACnB,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;gBACjB,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC;aAC5B,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,oBAAoB,EAAE,OAAO,EAAE,CAAC;YAChC,oBAAoB,EAAE,eAAe,EAAE,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,UAAU,CACzB;YACE,oBAAoB;YACpB,aAAa;SACd,EACD,EAAE,SAAS,EAAE,KAAK,EAAE,CACrB,CAAC;QAEF,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,sBAAsB,CAAC,EAAE,OAAO,EAAE,CAAC;QAC1D,CAAC;QAED,oBAAoB,EAAE,sBAAsB,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;QACnE,IAAI,CAAC,WAAW,CAAC,sBAAsB,EAAE,CAAC;IAC5C,CAAC;IAED,eAAe;QACb,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YAC1B,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAClD,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;YACpC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED,YAAY,CAAC,UAAkB;QAC7B,MAAM,UAAU,GAAG;YACjB,OAAO;YACP,SAAS;YACT,OAAO;YACP,OAAO;YACP,MAAM;YACN,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,SAAS;YACT,WAAW;YACX,WAAW;SACZ,CAAC;QACF,OAAO,UAAU,CAAC,UAAU,CAAC,CAAC;IAChC,CAAC;IAED,UAAU,CAAC,IAA+B;QACxC,IAAI,CAAC,IAAI;YAAE,OAAO,KAAK,CAAC;QACxB,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,KAAK,CAAC;IAC9D,CAAC;IAED,cAAc,CAAC,KAAyB;QACtC,IAAI,KAAK,KAAK,SAAS;YAAE,OAAO,KAAK,CAAC;QACtC,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;YACpC,KAAK,EAAE,UAAU;YACjB,QAAQ,EAAE,KAAK;SAChB,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC;IAED,mBAAmB;QACjB,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;YACjD,IAAI,CAAC,eAAe;iBACjB,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,CAAC;iBAC1D,SAAS,CAAC;gBACT,IAAI,EAAE,CAAC,OAA+B,EAAE,EAAE;oBACxC,IAAI,OAAO,EAAE,CAAC;wBACZ,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC;wBAC/B,IAAI,CAAC,eAAe,CAAC,UAAU;4BAC7B,CAAC,IAAI,CAAC,eAAe,CAAC,YAAY,IAAI,CAAC,CAAC;gCACxC,CAAC,IAAI,CAAC,eAAe,CAAC,mBAAmB,IAAI,CAAC,CAAC;gCAC/C,CAAC,IAAI,CAAC,eAAe,CAAC,oBAAoB,IAAI,CAAC,CAAC,CAAC;oBACrD,CAAC;oBACD,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,CAAC;gBACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;oBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,wCAAwC,CAAC,CAAC;oBAClF,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,CAAC;aACF,CAAC,CAAC;QACP,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC;IACH,CAAC;IAEO,YAAY;QAClB,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACxC,MAAM,aAAa,GAAG;gBACpB,GAAG,IAAI,CAAC,MAAM;gBACd,cAAc,EAAE,IAAI,CAAC,eAAe,CAAC,cAAc;gBACnD,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,IAAI,CAAC;gBAC7D,oBAAoB,EAAE,IAAI,CAAC,eAAe,CAAC,YAAY;gBACvD,iBAAiB,EAAE,IAAI,CAAC,eAAe,CAAC,SAAS;gBACjD,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO;gBAC7C,kBAAkB,EAAE,IAAI,CAAC,eAAe,CAAC,UAAU;gBACnD,oBAAoB,EAAE,IAAI,CAAC,eAAe,CAAC,YAAY;gBACvD,2BAA2B,EAAE,IAAI,CAAC,eAAe,CAAC,mBAAmB;gBACrE,4BAA4B,EAAE,IAAI,CAAC,eAAe,CAAC,oBAAoB;gBACvE,kBAAkB,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ;gBACjD,kBAAkB,EAAE,IAAI,CAAC,eAAe,CAAC,kBAAkB;gBAC3D,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,eAAe;gBACrD,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,eAAe;gBACrD,kBAAkB,EAAE,IAAI,CAAC,eAAe,CAAC,cAAc;gBACvD,aAAa,EAAE,IAAI,CAAC,eAAe,CAAC,SAAS;gBAC7C,kBAAkB,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ;gBACjD,uBAAuB,EAAE,IAAI,CAAC,eAAe,CAAC,aAAa;gBAC3D,qBAAqB,EAAE,IAAI,CAAC,eAAe,CAAC,eAAe;gBAC3D,kBAAkB,EAAE,IAAI,CAAC,eAAe,CAAC,kBAAkB;gBAC3D,kBAAkB,EAAE,IAAI,CAAC,eAAe,CAAC,kBAAkB;gBAC3D,kBAAkB,EAAE,IAAI,CAAC,eAAe,CAAC,kBAAkB;gBAC3D,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU;gBACrE,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,YAAY,EAAE,IAAI,CAAC,cAAc;gBACjC,mBAAmB,EAAE,IAAI,CAAC,kBAAkB;gBAC5C,oBAAoB,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,sBAAsB,CAAC;oBAChE,EAAE,KAAK;gBACT,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,sBAAsB,CAAC,EAAE,KAAK;oBAChE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,KAAK;oBAC9C,CAAC,CAAC,CAAC;aACN,CAAC;YAEF,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC;YAC5B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAED,YAAY;QACV,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,oBAAoB,EAAE,CAAC;YACvC,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,oBAAoB;aACtB,yBAAyB,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC;aAC3D,SAAS,CAAC;YACT,IAAI,EAAE,CAAC,OAAwB,EAAE,EAAE;gBACjC,MAAM,kBAAkB,GAAG,OAAO;qBAC/B,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;qBACnB,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAE9D,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACpC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;oBACnB,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;wBACzB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,IAAI,CAAC,CAAC;wBAC3D,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;oBAC9B,CAAC;oBACD,IAAI,CAAC,YAAY,EAAE,CAAC;oBACpB,OAAO;gBACT,CAAC;gBAED,QAAQ,CAAC,kBAAkB,CAAC,CAAC,SAAS,CAAC;oBACrC,IAAI,EAAE,CAAC,aAA0B,EAAE,EAAE;wBACnC,MAAM,WAAW,GAAG,aAAa,CAAC,IAAI,EAAE,CAAC;wBACzC,MAAM,cAAc,GAAG,WAAW,CAAC,IAAI,CACrC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,aAAa,CAC5C,CAAC;wBAEF,IAAI,CAAC,SAAS,GAAG,cAAc;6BAC5B,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;6BACzD,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,GAAG,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;wBAEpD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;4BACzB,IAAI,CAAC,cAAc;gCACjB,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;4BAE1D,MAAM,oBAAoB,GACxB,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC;4BAEjD,IAAI,CAAC,kBAAkB;gCACrB,IAAI,CAAC,eAAe,CAAC,UAAU;oCAC/B,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,CAAC;oCACjC,CAAC,CAAC,oBAAoB,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU;oCACxD,CAAC,CAAC,CAAC,CAAC;wBACV,CAAC;wBACD,IAAI,CAAC,YAAY,EAAE,CAAC;oBACtB,CAAC;oBACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;wBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,2BAA2B,CAAC,CAAC;wBACrE,IAAI,CAAC,YAAY,EAAE,CAAC;oBACtB,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;YACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,8BAA8B,CAAC,CAAC;gBACxE,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,CAAC;SACF,CAAC,CAAC;IACP,CAAC;;;;;;;;;;;;yBA/fA,KAAK;+BACL,MAAM;qCACN,MAAM;+BACN,KAAK;mCACL,SAAS,SAAC,yBAAyB;;;AALzB,+BAA+B;IAxB3C,SAAS,CAAC;QACT,QAAQ,EAAE,+BAA+B;QACzC,8BAAyD;QAEzD,SAAS,EAAE,CAAC,QAAQ,CAAC;QACrB,UAAU,EAAE,IAAI;QAChB,OAAO,EAAE;YACP,OAAO;YACP,WAAW;YACX,mBAAmB;YACnB,WAAW;YACX,YAAY;YACZ,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,qBAAqB;YACrB,eAAe;YACf,SAAS;YACT,yBAAyB;YACzB,SAAS;YACT,sBAAsB;YACtB,WAAW;SACZ;;KACF,CAAC;GACW,+BAA+B,CAigB3C\",\n      sourcesContent: [\"import { AsyncPipe, DatePipe, DecimalPipe, PercentPipe } from '@angular/common';\\nimport {\\n  Component,\\n  EventEmitter,\\n  Input,\\n  OnInit,\\n  Output,\\n  ViewChild,\\n} from '@angular/core';\\nimport {\\n  FormBuilder,\\n  FormGroup,\\n  ReactiveFormsModule,\\n  Validators,\\n} from '@angular/forms';\\nimport { MatAutocompleteModule, MatAutocompleteTrigger } from '@angular/material/autocomplete';\\nimport { MatButtonModule } from '@angular/material/button';\\nimport { MatCheckbox } from '@angular/material/checkbox';\\nimport { MatError, MatFormField, MatLabel } from '@angular/material/form-field';\\nimport { MatIcon } from '@angular/material/icon';\\nimport { MatInput } from '@angular/material/input';\\nimport { MatOption } from '@angular/material/core';\\nimport { ContractDetails } from '@contract-management/models/contract-details.model';\\nimport { SupervisorContract } from '@contract-management/models/supervisor-contract.model';\\nimport { ContractService } from '@contract-management/services/contract.service';\\nimport { SupervisorContractService } from '@contract-management/services/supervisor-contract.service';\\nimport { MonthlyReport } from '@contractor-dashboard/models/monthly-report.model';\\nimport { Payment } from '@contractor-dashboard/models/payment.model';\\nimport { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';\\nimport { PaymentService } from '@contractor-dashboard/services/payment.service';\\nimport { AlertService } from '@shared/services/alert.service';\\nimport { Supervisor } from '@supervisor-management/models/supervisor.model';\\nimport { SupervisorService } from '@supervisor-management/services/supervisor.service';\\nimport { Observable, forkJoin, map, of, startWith } from 'rxjs';\\nimport { CcpsDistributionComponent } from './ccps-distribution/ccps-distribution.component';\\n\\n@Component({\\n  selector: 'app-monthly-report-basic-data',\\n  templateUrl: './monthly-report-basic-data.component.html',\\n  styleUrl: './monthly-report-basic-data.component.scss',\\n  providers: [DatePipe],\\n  standalone: true,\\n  imports: [\\n    MatIcon,\\n    DecimalPipe,\\n    ReactiveFormsModule,\\n    MatCheckbox,\\n    MatFormField,\\n    MatLabel,\\n    MatInput,\\n    MatError,\\n    MatAutocompleteModule,\\n    MatButtonModule,\\n    AsyncPipe,\\n    CcpsDistributionComponent,\\n    MatOption,\\n    MatAutocompleteTrigger,\\n    PercentPipe,\\n  ],\\n})\\nexport class MonthlyReportBasicDataComponent implements OnInit {\\n  @Input() report!: MonthlyReport;\\n  @Output() reportChange = new EventEmitter<MonthlyReport>();\\n  @Output() formValidityChange = new EventEmitter<boolean>();\\n  @Input() isSupervisor = false;\\n  @ViewChild(CcpsDistributionComponent)\\n  ccpsDistribution!: CcpsDistributionComponent;\\n\\n  year = 0;\\n  month = '';\\n  contractDetails: ContractDetails | null = null;\\n  totalPaid = 0;\\n  pendingBalance = 0;\\n  progressPercentage = 0;\\n  supervisors: Supervisor[] = [];\\n  filteredSupervisors!: Observable<Supervisor[]>;\\n  supervisorContract: SupervisorContract | null = null;\\n  supervisorContracts: SupervisorContract[] = [];\\n  isEditingSupervisor = false;\\n  currentEditingSupervisorId: number | null = null;\\n  isCcpsValid = false;\\n\\n  invoiceForm: FormGroup = this.fb.group({\\n    hasElectronicInvoice: [false],\\n    invoiceNumber: [{ value: '', disabled: true }],\\n  });\\n\\n  supervisorForm: FormGroup = this.fb.group({\\n    supervisor: ['', Validators.required],\\n  });\\n\\n  constructor(\\n    private readonly contractService: ContractService,\\n    private readonly alert: AlertService,\\n    private readonly monthlyReportService: MonthlyReportService,\\n    private readonly paymentService: PaymentService,\\n    private readonly supervisorService: SupervisorService,\\n    private readonly supervisorContractService: SupervisorContractService,\\n    private readonly datePipe: DatePipe,\\n    private readonly fb: FormBuilder,\\n  ) {}\\n\\n  ngOnInit(): void {\\n    this.loadContractDetails();\\n    this.setReportPeriod();\\n    this.initializeForm();\\n    this.setupFormValidation();\\n    this.loadSupervisors();\\n    this.setupSupervisorAutocomplete();\\n    if (this.report.contractorContract?.contract?.id) {\\n      this.loadSupervisorContracts(this.report.contractorContract.contract.id);\\n    }\\n  }\\n\\n  private loadSupervisors(): void {\\n    this.supervisorService.getAll().subscribe({\\n      next: (supervisors) => {\\n        this.supervisors = supervisors;\\n      },\\n      error: (error) => {\\n        this.alert.error(error.error?.detail ?? 'Error al cargar los supervisores');\\n      },\\n    });\\n  }\\n\\n  private setupSupervisorAutocomplete(): void {\\n    this.filteredSupervisors = this.supervisorForm\\n      .get('supervisor')!\\n      .valueChanges.pipe(\\n        startWith(''),\\n        map((value) => {\\n          const name = typeof value === 'string' ? value : value?.fullName;\\n          return name\\n            ? this._filterSupervisors(name)\\n            : this.supervisors.slice();\\n        }),\\n      );\\n  }\\n\\n  private _filterSupervisors(value: string): Supervisor[] {\\n    const filterValue = value.toLowerCase();\\n    return this.supervisors.filter(\\n      (supervisor) =>\\n        supervisor.fullName.toLowerCase().includes(filterValue) ||\\n        supervisor.idNumber.toString().toLowerCase().includes(filterValue),\\n    );\\n  }\\n\\n  displaySupervisor(supervisor: Supervisor): string {\\n    return supervisor ? `${supervisor.fullName} (${supervisor.idNumber})` : '';\\n  }\\n\\n  addNewSupervisor(): void {\\n    if (this.supervisorContracts.length >= 2) {\\n      this.alert.warning(\\n        'L\\xEDmite excedido',\\n        'Solo se pueden asociar un m\\xE1ximo de 2 supervisores por contrato.',\\n      );\\n      return;\\n    }\\n    this.currentEditingSupervisorId = null;\\n    this.supervisorForm.reset();\\n    this.isEditingSupervisor = true;\\n  }\\n\\n  editSupervisor(supervisorContract: SupervisorContract): void {\\n    this.currentEditingSupervisorId = supervisorContract.id;\\n    const currentSupervisor = this.supervisors.find(\\n      (s) => s.id === supervisorContract.supervisorId,\\n    );\\n    if (currentSupervisor) {\\n      this.supervisorForm.patchValue({ supervisor: currentSupervisor });\\n    }\\n    this.isEditingSupervisor = true;\\n  }\\n\\n  removeSupervisor(supervisorContractId: number): void {\\n    this.alert\\n      .confirm(\\n        '\\xBFEst\\xE1 seguro de eliminar este supervisor?',\\n        'Esta acci\\xF3n no se puede deshacer',\\n      )\\n      .then((isConfirmed) => {\\n        if (isConfirmed) {\\n          this.supervisorContractService\\n            .delete(supervisorContractId)\\n            .subscribe({\\n              next: () => {\\n                this.alert.success('Supervisor eliminado exitosamente');\\n                if (this.report.contractorContract?.contract?.id) {\\n                  this.loadSupervisorContracts(\\n                    this.report.contractorContract.contract.id,\\n                  );\\n                }\\n              },\\n              error: (error) => {\\n                this.alert.error(error.error?.detail ?? 'Error al eliminar el supervisor');\\n              },\\n            });\\n        }\\n      });\\n  }\\n\\n  toggleSupervisorEdit(): void {\\n    this.isEditingSupervisor = !this.isEditingSupervisor;\\n    this.currentEditingSupervisorId = null;\\n    this.supervisorForm.reset();\\n  }\\n\\n  private loadSupervisorContracts(contractId: number): void {\\n    this.supervisorContractService.getByContractId(contractId).subscribe({\\n      next: (contracts) => {\\n        this.supervisorContracts = contracts;\\n\\n        this.supervisorContracts.forEach((contract, index) => {\\n          this.supervisorService.getById(contract.supervisorId).subscribe({\\n            next: (supervisor) => {\\n              this.supervisorContracts[index] = {\\n                ...this.supervisorContracts[index],\\n                supervisor: supervisor,\\n              };\\n            },\\n            error: (error) => {\\n              this.alert.error(\\n                error.error?.detail ?? `Error al cargar informaci\\xF3n del supervisor ${contract.supervisorId}`\\n              );\\n            },\\n          });\\n        });\\n      },\\n      error: (error) => {\\n        this.alert.error(error.error?.detail ?? 'Error al cargar los contratos de supervisores');\\n      },\\n    });\\n  }\\n\\n  saveSupervisor(): void {\\n    if (\\n      this.supervisorForm.valid &&\\n      this.report.contractorContract?.contract?.id\\n    ) {\\n      const selectedSupervisor = this.supervisorForm.get('supervisor')?.value;\\n      const contractId = this.report.contractorContract.contract.id;\\n\\n      if (this.currentEditingSupervisorId) {\\n        this.supervisorContractService\\n          .update(this.currentEditingSupervisorId, {\\n            id: this.currentEditingSupervisorId,\\n            supervisorId: selectedSupervisor.id,\\n            contractId: contractId,\\n          })\\n          .subscribe({\\n            next: () => {\\n              this.alert.success('Supervisor actualizado exitosamente');\\n              this.isEditingSupervisor = false;\\n              this.loadSupervisorContracts(contractId);\\n              this.loadContractDetails();\\n            },\\n            error: (error) => {\\n              this.alert.error(error.error?.detail ?? 'Error al actualizar el supervisor');\\n            },\\n          });\\n      } else {\\n        if (this.supervisorContracts.length >= 2) {\\n          this.alert.warning(\\n            'L\\xEDmite excedido',\\n            'Solo se pueden asociar un m\\xE1ximo de 2 supervisores por contrato.',\\n          );\\n          return;\\n        }\\n\\n        const supervisorExists = this.supervisorContracts.some(\\n          (contract) => contract.supervisorId === selectedSupervisor.id,\\n        );\\n\\n        if (supervisorExists) {\\n          this.alert.warning(\\n            'Supervisor ya asignado',\\n            'Este supervisor ya est\\xE1 asignado a este contrato.',\\n          );\\n          return;\\n        }\\n\\n        this.supervisorContractService\\n          .create({\\n            supervisorId: selectedSupervisor.id,\\n            contractId: contractId,\\n          })\\n          .subscribe({\\n            next: () => {\\n              this.alert.success('Supervisor asignado exitosamente');\\n              this.isEditingSupervisor = false;\\n              this.loadSupervisorContracts(contractId);\\n              this.loadContractDetails();\\n            },\\n            error: (error) => {\\n              if (\\n                error?.error?.detail?.includes(\\n                  'Multiple supervisors are associated',\\n                )\\n              ) {\\n                this.alert.warning(\\n                  'L\\xEDmite excedido',\\n                  'Solo se pueden asociar un m\\xE1ximo de 2 supervisores por contrato.',\\n                );\\n              } else if (error?.error?.detail?.includes('already associated')) {\\n                this.alert.warning(\\n                  'Supervisor ya asignado',\\n                  'Este supervisor ya est\\xE1 asignado a este contrato.',\\n                );\\n              } else {\\n                this.alert.error(error.error?.detail ?? 'Error al asignar el supervisor');\\n              }\\n            },\\n          });\\n      }\\n    }\\n  }\\n\\n  private setupFormValidation(): void {\\n    this.invoiceForm\\n      .get('hasElectronicInvoice')\\n      ?.valueChanges.subscribe((checked) => {\\n        const invoiceNumberControl = this.invoiceForm.get('invoiceNumber');\\n        if (checked) {\\n          invoiceNumberControl?.enable();\\n          invoiceNumberControl?.setValidators([\\n            Validators.required,\\n            Validators.min(0),\\n            Validators.pattern(/^\\\\d+$/),\\n          ]);\\n        } else {\\n          invoiceNumberControl?.disable();\\n          invoiceNumberControl?.clearValidators();\\n          invoiceNumberControl?.setValue('');\\n        }\\n        invoiceNumberControl?.updateValueAndValidity({ emitEvent: false });\\n        this.invoiceForm.updateValueAndValidity();\\n      });\\n\\n    this.invoiceForm.statusChanges.subscribe(() => {\\n      this.checkFormValidity();\\n    });\\n\\n    this.invoiceForm.valueChanges.subscribe(() => {\\n      this.updateReport();\\n    });\\n  }\\n\\n  onCcpsValidityChange(isValid: boolean): void {\\n    this.isCcpsValid = isValid;\\n    this.checkFormValidity();\\n  }\\n\\n  onCcpsSaveComplete(): void {\\n    this.alert.success('Distribuci\\xF3n de CCPs guardada exitosamente');\\n  }\\n\\n  private checkFormValidity(): void {\\n    const hasElectronicInvoice = this.invoiceForm.get(\\n      'hasElectronicInvoice',\\n    )?.value;\\n    const isInvoiceFormValid = !hasElectronicInvoice || this.invoiceForm.valid;\\n    this.formValidityChange.emit(isInvoiceFormValid && this.isCcpsValid);\\n  }\\n\\n  saveData(): Observable<boolean> {\\n    if (this.ccpsDistribution) {\\n      return this.ccpsDistribution.saveCcps();\\n    }\\n    return of(true);\\n  }\\n\\n  private initializeForm(): void {\\n    const hasElectronicInvoice = this.report.hasElectronicInvoice || false;\\n    const invoiceNumber = this.report.invoiceNumber || '';\\n\\n    const invoiceNumberControl = this.invoiceForm.get('invoiceNumber');\\n    if (hasElectronicInvoice) {\\n      invoiceNumberControl?.enable();\\n      invoiceNumberControl?.setValidators([\\n        Validators.required,\\n        Validators.min(0),\\n        Validators.pattern(/^\\\\d+$/),\\n      ]);\\n    } else {\\n      invoiceNumberControl?.disable();\\n      invoiceNumberControl?.clearValidators();\\n    }\\n\\n    this.invoiceForm.patchValue(\\n      {\\n        hasElectronicInvoice,\\n        invoiceNumber,\\n      },\\n      { emitEvent: false },\\n    );\\n\\n    if (this.isSupervisor) {\\n      this.invoiceForm.get('hasElectronicInvoice')?.disable();\\n    }\\n\\n    invoiceNumberControl?.updateValueAndValidity({ emitEvent: false });\\n    this.invoiceForm.updateValueAndValidity();\\n  }\\n\\n  setReportPeriod(): void {\\n    if (this.report.startDate) {\\n      const startDate = new Date(this.report.startDate);\\n      this.year = startDate.getFullYear();\\n      this.month = this.getMonthName(startDate.getMonth());\\n    }\\n  }\\n\\n  getMonthName(monthIndex: number): string {\\n    const monthNames = [\\n      'Enero',\\n      'Febrero',\\n      'Marzo',\\n      'Abril',\\n      'Mayo',\\n      'Junio',\\n      'Julio',\\n      'Agosto',\\n      'Septiembre',\\n      'Octubre',\\n      'Noviembre',\\n      'Diciembre',\\n    ];\\n    return monthNames[monthIndex];\\n  }\\n\\n  formatDate(date: string | Date | undefined): string {\\n    if (!date) return 'N/A';\\n    return this.datePipe.transform(date, 'dd/MM/yyyy') || 'N/A';\\n  }\\n\\n  formatCurrency(value: number | undefined): string {\\n    if (value === undefined) return 'N/A';\\n    return new Intl.NumberFormat('es-CO', {\\n      style: 'currency',\\n      currency: 'COP',\\n    }).format(value);\\n  }\\n\\n  loadContractDetails(): void {\\n    if (this.report.contractorContract?.contract?.id) {\\n      this.contractService\\n        .getDetailsById(this.report.contractorContract.contract.id)\\n        .subscribe({\\n          next: (details: ContractDetails | null) => {\\n            if (details) {\\n              this.contractDetails = details;\\n              this.contractDetails.totalValue =\\n                (this.contractDetails.initialValue ?? 0) +\\n                (this.contractDetails.totalAdditionsValue ?? 0) -\\n                (this.contractDetails.totalReductionsValue ?? 0);\\n            }\\n            this.loadPayments();\\n          },\\n          error: (error) => {\\n            this.alert.error(error.error?.detail ?? 'Error al obtener detalles del contrato');\\n            this.loadPayments();\\n          },\\n        });\\n    } else {\\n      this.loadPayments();\\n    }\\n  }\\n\\n  private updateReport(): void {\\n    if (this.report && this.contractDetails) {\\n      const updatedReport = {\\n        ...this.report,\\n        contractNumber: this.contractDetails.contractNumber,\\n        contractYear: Number(this.contractDetails.contractYear?.year),\\n        contractDurationDays: this.contractDetails.durationDays,\\n        contractStartDate: this.contractDetails.startDate,\\n        contractEndDate: this.contractDetails.endDate,\\n        contractTotalValue: this.contractDetails.totalValue,\\n        contractInitialValue: this.contractDetails.initialValue,\\n        contractTotalAdditionsValue: this.contractDetails.totalAdditionsValue,\\n        contractTotalReductionsValue: this.contractDetails.totalReductionsValue,\\n        contractorFullName: this.contractDetails.fullName,\\n        contractorIdNumber: this.contractDetails.contractorIdNumber,\\n        contractorEmail: this.contractDetails.contractorEmail,\\n        contractorPhone: this.contractDetails.contractorPhone,\\n        contractDependency: this.contractDetails.dependencyName,\\n        contractGroup: this.contractDetails.groupName,\\n        contractorBankName: this.contractDetails.bankName,\\n        contractorAccountNumber: this.contractDetails.accountNumber,\\n        contractorAccountType: this.contractDetails.accountTypeName,\\n        supervisorFullName: this.contractDetails.supervisorFullName,\\n        supervisorIdNumber: this.contractDetails.supervisorIdNumber,\\n        supervisorPosition: this.contractDetails.supervisorPosition,\\n        totalValue: this.report.totalValue || this.contractDetails.totalValue,\\n        paidValue: this.totalPaid,\\n        pendingValue: this.pendingBalance,\\n        executionPercentage: this.progressPercentage,\\n        hasElectronicInvoice: this.invoiceForm.get('hasElectronicInvoice')\\n          ?.value,\\n        invoiceNumber: this.invoiceForm.get('hasElectronicInvoice')?.value\\n          ? this.invoiceForm.get('invoiceNumber')?.value\\n          : 0,\\n      };\\n\\n      this.report = updatedReport;\\n      this.reportChange.emit(updatedReport);\\n    }\\n  }\\n\\n  loadPayments(): void {\\n    if (!this.report?.contractorContractId) {\\n      this.updateReport();\\n      return;\\n    }\\n\\n    this.monthlyReportService\\n      .getByContractorContractId(this.report.contractorContractId)\\n      .subscribe({\\n        next: (reports: MonthlyReport[]) => {\\n          const paymentObservables = reports\\n            .filter((r) => r.id)\\n            .map((r) => this.paymentService.getByMonthlyReportId(r.id));\\n\\n          if (paymentObservables.length === 0) {\\n            this.totalPaid = 0;\\n            if (this.contractDetails) {\\n              this.pendingBalance = this.contractDetails.totalValue ?? 0;\\n              this.progressPercentage = 0;\\n            }\\n            this.updateReport();\\n            return;\\n          }\\n\\n          forkJoin(paymentObservables).subscribe({\\n            next: (paymentsArray: Payment[][]) => {\\n              const allPayments = paymentsArray.flat();\\n              const sortedPayments = allPayments.sort(\\n                (a, b) => a.paymentNumber - b.paymentNumber,\\n              );\\n\\n              this.totalPaid = sortedPayments\\n                .filter((p) => p.paymentNumber < this.report.reportNumber)\\n                .reduce((sum, payment) => sum + payment.value, 0);\\n\\n              if (this.contractDetails) {\\n                this.pendingBalance =\\n                  (this.contractDetails.totalValue ?? 0) - this.totalPaid;\\n\\n                const totalPaidWithCurrent =\\n                  this.totalPaid + (this.report.totalValue || 0);\\n\\n                this.progressPercentage =\\n                  this.contractDetails.totalValue &&\\n                  this.contractDetails.totalValue > 0\\n                    ? totalPaidWithCurrent / this.contractDetails.totalValue\\n                    : 0;\\n              }\\n              this.updateReport();\\n            },\\n            error: (error) => {\\n              this.alert.error(error.error?.detail ?? 'Error al cargar los pagos');\\n              this.updateReport();\\n            },\\n          });\\n        },\\n        error: (error) => {\\n          this.alert.error(error.error?.detail ?? 'Error al cargar los informes');\\n          this.updateReport();\\n        },\\n      });\\n  }\\n}\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"49b7e0b5aa203b0638679ab713c53631ea808ed9\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_b84wb63gz = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_b84wb63gz();\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./monthly-report-basic-data.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./monthly-report-basic-data.component.scss?ngResource\";\nimport { AsyncPipe, DatePipe, DecimalPipe, PercentPipe } from '@angular/common';\nimport { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';\nimport { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatAutocompleteModule, MatAutocompleteTrigger } from '@angular/material/autocomplete';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckbox } from '@angular/material/checkbox';\nimport { MatError, MatFormField, MatLabel } from '@angular/material/form-field';\nimport { MatIcon } from '@angular/material/icon';\nimport { MatInput } from '@angular/material/input';\nimport { MatOption } from '@angular/material/core';\nimport { ContractService } from '@contract-management/services/contract.service';\nimport { SupervisorContractService } from '@contract-management/services/supervisor-contract.service';\nimport { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';\nimport { PaymentService } from '@contractor-dashboard/services/payment.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { SupervisorService } from '@supervisor-management/services/supervisor.service';\nimport { forkJoin, map, of, startWith } from 'rxjs';\nimport { CcpsDistributionComponent } from './ccps-distribution/ccps-distribution.component';\ncov_b84wb63gz().s[0]++;\nlet MonthlyReportBasicDataComponent = class MonthlyReportBasicDataComponent {\n  constructor(contractService, alert, monthlyReportService, paymentService, supervisorService, supervisorContractService, datePipe, fb) {\n    cov_b84wb63gz().f[0]++;\n    cov_b84wb63gz().s[1]++;\n    this.contractService = contractService;\n    cov_b84wb63gz().s[2]++;\n    this.alert = alert;\n    cov_b84wb63gz().s[3]++;\n    this.monthlyReportService = monthlyReportService;\n    cov_b84wb63gz().s[4]++;\n    this.paymentService = paymentService;\n    cov_b84wb63gz().s[5]++;\n    this.supervisorService = supervisorService;\n    cov_b84wb63gz().s[6]++;\n    this.supervisorContractService = supervisorContractService;\n    cov_b84wb63gz().s[7]++;\n    this.datePipe = datePipe;\n    cov_b84wb63gz().s[8]++;\n    this.fb = fb;\n    cov_b84wb63gz().s[9]++;\n    this.reportChange = new EventEmitter();\n    cov_b84wb63gz().s[10]++;\n    this.formValidityChange = new EventEmitter();\n    cov_b84wb63gz().s[11]++;\n    this.isSupervisor = false;\n    cov_b84wb63gz().s[12]++;\n    this.year = 0;\n    cov_b84wb63gz().s[13]++;\n    this.month = '';\n    cov_b84wb63gz().s[14]++;\n    this.contractDetails = null;\n    cov_b84wb63gz().s[15]++;\n    this.totalPaid = 0;\n    cov_b84wb63gz().s[16]++;\n    this.pendingBalance = 0;\n    cov_b84wb63gz().s[17]++;\n    this.progressPercentage = 0;\n    cov_b84wb63gz().s[18]++;\n    this.supervisors = [];\n    cov_b84wb63gz().s[19]++;\n    this.supervisorContract = null;\n    cov_b84wb63gz().s[20]++;\n    this.supervisorContracts = [];\n    cov_b84wb63gz().s[21]++;\n    this.isEditingSupervisor = false;\n    cov_b84wb63gz().s[22]++;\n    this.currentEditingSupervisorId = null;\n    cov_b84wb63gz().s[23]++;\n    this.isCcpsValid = false;\n    cov_b84wb63gz().s[24]++;\n    this.invoiceForm = this.fb.group({\n      hasElectronicInvoice: [false],\n      invoiceNumber: [{\n        value: '',\n        disabled: true\n      }]\n    });\n    cov_b84wb63gz().s[25]++;\n    this.supervisorForm = this.fb.group({\n      supervisor: ['', Validators.required]\n    });\n  }\n  ngOnInit() {\n    cov_b84wb63gz().f[1]++;\n    cov_b84wb63gz().s[26]++;\n    this.loadContractDetails();\n    cov_b84wb63gz().s[27]++;\n    this.setReportPeriod();\n    cov_b84wb63gz().s[28]++;\n    this.initializeForm();\n    cov_b84wb63gz().s[29]++;\n    this.setupFormValidation();\n    cov_b84wb63gz().s[30]++;\n    this.loadSupervisors();\n    cov_b84wb63gz().s[31]++;\n    this.setupSupervisorAutocomplete();\n    cov_b84wb63gz().s[32]++;\n    if (this.report.contractorContract?.contract?.id) {\n      cov_b84wb63gz().b[0][0]++;\n      cov_b84wb63gz().s[33]++;\n      this.loadSupervisorContracts(this.report.contractorContract.contract.id);\n    } else {\n      cov_b84wb63gz().b[0][1]++;\n    }\n  }\n  loadSupervisors() {\n    cov_b84wb63gz().f[2]++;\n    cov_b84wb63gz().s[34]++;\n    this.supervisorService.getAll().subscribe({\n      next: supervisors => {\n        cov_b84wb63gz().f[3]++;\n        cov_b84wb63gz().s[35]++;\n        this.supervisors = supervisors;\n      },\n      error: error => {\n        cov_b84wb63gz().f[4]++;\n        cov_b84wb63gz().s[36]++;\n        this.alert.error((cov_b84wb63gz().b[1][0]++, error.error?.detail) ?? (cov_b84wb63gz().b[1][1]++, 'Error al cargar los supervisores'));\n      }\n    });\n  }\n  setupSupervisorAutocomplete() {\n    cov_b84wb63gz().f[5]++;\n    cov_b84wb63gz().s[37]++;\n    this.filteredSupervisors = this.supervisorForm.get('supervisor').valueChanges.pipe(startWith(''), map(value => {\n      cov_b84wb63gz().f[6]++;\n      const name = (cov_b84wb63gz().s[38]++, typeof value === 'string' ? (cov_b84wb63gz().b[2][0]++, value) : (cov_b84wb63gz().b[2][1]++, value?.fullName));\n      cov_b84wb63gz().s[39]++;\n      return name ? (cov_b84wb63gz().b[3][0]++, this._filterSupervisors(name)) : (cov_b84wb63gz().b[3][1]++, this.supervisors.slice());\n    }));\n  }\n  _filterSupervisors(value) {\n    cov_b84wb63gz().f[7]++;\n    const filterValue = (cov_b84wb63gz().s[40]++, value.toLowerCase());\n    cov_b84wb63gz().s[41]++;\n    return this.supervisors.filter(supervisor => {\n      cov_b84wb63gz().f[8]++;\n      cov_b84wb63gz().s[42]++;\n      return (cov_b84wb63gz().b[4][0]++, supervisor.fullName.toLowerCase().includes(filterValue)) || (cov_b84wb63gz().b[4][1]++, supervisor.idNumber.toString().toLowerCase().includes(filterValue));\n    });\n  }\n  displaySupervisor(supervisor) {\n    cov_b84wb63gz().f[9]++;\n    cov_b84wb63gz().s[43]++;\n    return supervisor ? (cov_b84wb63gz().b[5][0]++, `${supervisor.fullName} (${supervisor.idNumber})`) : (cov_b84wb63gz().b[5][1]++, '');\n  }\n  addNewSupervisor() {\n    cov_b84wb63gz().f[10]++;\n    cov_b84wb63gz().s[44]++;\n    if (this.supervisorContracts.length >= 2) {\n      cov_b84wb63gz().b[6][0]++;\n      cov_b84wb63gz().s[45]++;\n      this.alert.warning('Límite excedido', 'Solo se pueden asociar un máximo de 2 supervisores por contrato.');\n      cov_b84wb63gz().s[46]++;\n      return;\n    } else {\n      cov_b84wb63gz().b[6][1]++;\n    }\n    cov_b84wb63gz().s[47]++;\n    this.currentEditingSupervisorId = null;\n    cov_b84wb63gz().s[48]++;\n    this.supervisorForm.reset();\n    cov_b84wb63gz().s[49]++;\n    this.isEditingSupervisor = true;\n  }\n  editSupervisor(supervisorContract) {\n    cov_b84wb63gz().f[11]++;\n    cov_b84wb63gz().s[50]++;\n    this.currentEditingSupervisorId = supervisorContract.id;\n    const currentSupervisor = (cov_b84wb63gz().s[51]++, this.supervisors.find(s => {\n      cov_b84wb63gz().f[12]++;\n      cov_b84wb63gz().s[52]++;\n      return s.id === supervisorContract.supervisorId;\n    }));\n    cov_b84wb63gz().s[53]++;\n    if (currentSupervisor) {\n      cov_b84wb63gz().b[7][0]++;\n      cov_b84wb63gz().s[54]++;\n      this.supervisorForm.patchValue({\n        supervisor: currentSupervisor\n      });\n    } else {\n      cov_b84wb63gz().b[7][1]++;\n    }\n    cov_b84wb63gz().s[55]++;\n    this.isEditingSupervisor = true;\n  }\n  removeSupervisor(supervisorContractId) {\n    cov_b84wb63gz().f[13]++;\n    cov_b84wb63gz().s[56]++;\n    this.alert.confirm('¿Está seguro de eliminar este supervisor?', 'Esta acción no se puede deshacer').then(isConfirmed => {\n      cov_b84wb63gz().f[14]++;\n      cov_b84wb63gz().s[57]++;\n      if (isConfirmed) {\n        cov_b84wb63gz().b[8][0]++;\n        cov_b84wb63gz().s[58]++;\n        this.supervisorContractService.delete(supervisorContractId).subscribe({\n          next: () => {\n            cov_b84wb63gz().f[15]++;\n            cov_b84wb63gz().s[59]++;\n            this.alert.success('Supervisor eliminado exitosamente');\n            cov_b84wb63gz().s[60]++;\n            if (this.report.contractorContract?.contract?.id) {\n              cov_b84wb63gz().b[9][0]++;\n              cov_b84wb63gz().s[61]++;\n              this.loadSupervisorContracts(this.report.contractorContract.contract.id);\n            } else {\n              cov_b84wb63gz().b[9][1]++;\n            }\n          },\n          error: error => {\n            cov_b84wb63gz().f[16]++;\n            cov_b84wb63gz().s[62]++;\n            this.alert.error((cov_b84wb63gz().b[10][0]++, error.error?.detail) ?? (cov_b84wb63gz().b[10][1]++, 'Error al eliminar el supervisor'));\n          }\n        });\n      } else {\n        cov_b84wb63gz().b[8][1]++;\n      }\n    });\n  }\n  toggleSupervisorEdit() {\n    cov_b84wb63gz().f[17]++;\n    cov_b84wb63gz().s[63]++;\n    this.isEditingSupervisor = !this.isEditingSupervisor;\n    cov_b84wb63gz().s[64]++;\n    this.currentEditingSupervisorId = null;\n    cov_b84wb63gz().s[65]++;\n    this.supervisorForm.reset();\n  }\n  loadSupervisorContracts(contractId) {\n    cov_b84wb63gz().f[18]++;\n    cov_b84wb63gz().s[66]++;\n    this.supervisorContractService.getByContractId(contractId).subscribe({\n      next: contracts => {\n        cov_b84wb63gz().f[19]++;\n        cov_b84wb63gz().s[67]++;\n        this.supervisorContracts = contracts;\n        cov_b84wb63gz().s[68]++;\n        this.supervisorContracts.forEach((contract, index) => {\n          cov_b84wb63gz().f[20]++;\n          cov_b84wb63gz().s[69]++;\n          this.supervisorService.getById(contract.supervisorId).subscribe({\n            next: supervisor => {\n              cov_b84wb63gz().f[21]++;\n              cov_b84wb63gz().s[70]++;\n              this.supervisorContracts[index] = {\n                ...this.supervisorContracts[index],\n                supervisor: supervisor\n              };\n            },\n            error: error => {\n              cov_b84wb63gz().f[22]++;\n              cov_b84wb63gz().s[71]++;\n              this.alert.error((cov_b84wb63gz().b[11][0]++, error.error?.detail) ?? (cov_b84wb63gz().b[11][1]++, `Error al cargar información del supervisor ${contract.supervisorId}`));\n            }\n          });\n        });\n      },\n      error: error => {\n        cov_b84wb63gz().f[23]++;\n        cov_b84wb63gz().s[72]++;\n        this.alert.error((cov_b84wb63gz().b[12][0]++, error.error?.detail) ?? (cov_b84wb63gz().b[12][1]++, 'Error al cargar los contratos de supervisores'));\n      }\n    });\n  }\n  saveSupervisor() {\n    cov_b84wb63gz().f[24]++;\n    cov_b84wb63gz().s[73]++;\n    if ((cov_b84wb63gz().b[14][0]++, this.supervisorForm.valid) && (cov_b84wb63gz().b[14][1]++, this.report.contractorContract?.contract?.id)) {\n      cov_b84wb63gz().b[13][0]++;\n      const selectedSupervisor = (cov_b84wb63gz().s[74]++, this.supervisorForm.get('supervisor')?.value);\n      const contractId = (cov_b84wb63gz().s[75]++, this.report.contractorContract.contract.id);\n      cov_b84wb63gz().s[76]++;\n      if (this.currentEditingSupervisorId) {\n        cov_b84wb63gz().b[15][0]++;\n        cov_b84wb63gz().s[77]++;\n        this.supervisorContractService.update(this.currentEditingSupervisorId, {\n          id: this.currentEditingSupervisorId,\n          supervisorId: selectedSupervisor.id,\n          contractId: contractId\n        }).subscribe({\n          next: () => {\n            cov_b84wb63gz().f[25]++;\n            cov_b84wb63gz().s[78]++;\n            this.alert.success('Supervisor actualizado exitosamente');\n            cov_b84wb63gz().s[79]++;\n            this.isEditingSupervisor = false;\n            cov_b84wb63gz().s[80]++;\n            this.loadSupervisorContracts(contractId);\n            cov_b84wb63gz().s[81]++;\n            this.loadContractDetails();\n          },\n          error: error => {\n            cov_b84wb63gz().f[26]++;\n            cov_b84wb63gz().s[82]++;\n            this.alert.error((cov_b84wb63gz().b[16][0]++, error.error?.detail) ?? (cov_b84wb63gz().b[16][1]++, 'Error al actualizar el supervisor'));\n          }\n        });\n      } else {\n        cov_b84wb63gz().b[15][1]++;\n        cov_b84wb63gz().s[83]++;\n        if (this.supervisorContracts.length >= 2) {\n          cov_b84wb63gz().b[17][0]++;\n          cov_b84wb63gz().s[84]++;\n          this.alert.warning('Límite excedido', 'Solo se pueden asociar un máximo de 2 supervisores por contrato.');\n          cov_b84wb63gz().s[85]++;\n          return;\n        } else {\n          cov_b84wb63gz().b[17][1]++;\n        }\n        const supervisorExists = (cov_b84wb63gz().s[86]++, this.supervisorContracts.some(contract => {\n          cov_b84wb63gz().f[27]++;\n          cov_b84wb63gz().s[87]++;\n          return contract.supervisorId === selectedSupervisor.id;\n        }));\n        cov_b84wb63gz().s[88]++;\n        if (supervisorExists) {\n          cov_b84wb63gz().b[18][0]++;\n          cov_b84wb63gz().s[89]++;\n          this.alert.warning('Supervisor ya asignado', 'Este supervisor ya está asignado a este contrato.');\n          cov_b84wb63gz().s[90]++;\n          return;\n        } else {\n          cov_b84wb63gz().b[18][1]++;\n        }\n        cov_b84wb63gz().s[91]++;\n        this.supervisorContractService.create({\n          supervisorId: selectedSupervisor.id,\n          contractId: contractId\n        }).subscribe({\n          next: () => {\n            cov_b84wb63gz().f[28]++;\n            cov_b84wb63gz().s[92]++;\n            this.alert.success('Supervisor asignado exitosamente');\n            cov_b84wb63gz().s[93]++;\n            this.isEditingSupervisor = false;\n            cov_b84wb63gz().s[94]++;\n            this.loadSupervisorContracts(contractId);\n            cov_b84wb63gz().s[95]++;\n            this.loadContractDetails();\n          },\n          error: error => {\n            cov_b84wb63gz().f[29]++;\n            cov_b84wb63gz().s[96]++;\n            if (error?.error?.detail?.includes('Multiple supervisors are associated')) {\n              cov_b84wb63gz().b[19][0]++;\n              cov_b84wb63gz().s[97]++;\n              this.alert.warning('Límite excedido', 'Solo se pueden asociar un máximo de 2 supervisores por contrato.');\n            } else {\n              cov_b84wb63gz().b[19][1]++;\n              cov_b84wb63gz().s[98]++;\n              if (error?.error?.detail?.includes('already associated')) {\n                cov_b84wb63gz().b[20][0]++;\n                cov_b84wb63gz().s[99]++;\n                this.alert.warning('Supervisor ya asignado', 'Este supervisor ya está asignado a este contrato.');\n              } else {\n                cov_b84wb63gz().b[20][1]++;\n                cov_b84wb63gz().s[100]++;\n                this.alert.error((cov_b84wb63gz().b[21][0]++, error.error?.detail) ?? (cov_b84wb63gz().b[21][1]++, 'Error al asignar el supervisor'));\n              }\n            }\n          }\n        });\n      }\n    } else {\n      cov_b84wb63gz().b[13][1]++;\n    }\n  }\n  setupFormValidation() {\n    cov_b84wb63gz().f[30]++;\n    cov_b84wb63gz().s[101]++;\n    this.invoiceForm.get('hasElectronicInvoice')?.valueChanges.subscribe(checked => {\n      cov_b84wb63gz().f[31]++;\n      const invoiceNumberControl = (cov_b84wb63gz().s[102]++, this.invoiceForm.get('invoiceNumber'));\n      cov_b84wb63gz().s[103]++;\n      if (checked) {\n        cov_b84wb63gz().b[22][0]++;\n        cov_b84wb63gz().s[104]++;\n        invoiceNumberControl?.enable();\n        cov_b84wb63gz().s[105]++;\n        invoiceNumberControl?.setValidators([Validators.required, Validators.min(0), Validators.pattern(/^\\d+$/)]);\n      } else {\n        cov_b84wb63gz().b[22][1]++;\n        cov_b84wb63gz().s[106]++;\n        invoiceNumberControl?.disable();\n        cov_b84wb63gz().s[107]++;\n        invoiceNumberControl?.clearValidators();\n        cov_b84wb63gz().s[108]++;\n        invoiceNumberControl?.setValue('');\n      }\n      cov_b84wb63gz().s[109]++;\n      invoiceNumberControl?.updateValueAndValidity({\n        emitEvent: false\n      });\n      cov_b84wb63gz().s[110]++;\n      this.invoiceForm.updateValueAndValidity();\n    });\n    cov_b84wb63gz().s[111]++;\n    this.invoiceForm.statusChanges.subscribe(() => {\n      cov_b84wb63gz().f[32]++;\n      cov_b84wb63gz().s[112]++;\n      this.checkFormValidity();\n    });\n    cov_b84wb63gz().s[113]++;\n    this.invoiceForm.valueChanges.subscribe(() => {\n      cov_b84wb63gz().f[33]++;\n      cov_b84wb63gz().s[114]++;\n      this.updateReport();\n    });\n  }\n  onCcpsValidityChange(isValid) {\n    cov_b84wb63gz().f[34]++;\n    cov_b84wb63gz().s[115]++;\n    this.isCcpsValid = isValid;\n    cov_b84wb63gz().s[116]++;\n    this.checkFormValidity();\n  }\n  onCcpsSaveComplete() {\n    cov_b84wb63gz().f[35]++;\n    cov_b84wb63gz().s[117]++;\n    this.alert.success('Distribución de CCPs guardada exitosamente');\n  }\n  checkFormValidity() {\n    cov_b84wb63gz().f[36]++;\n    const hasElectronicInvoice = (cov_b84wb63gz().s[118]++, this.invoiceForm.get('hasElectronicInvoice')?.value);\n    const isInvoiceFormValid = (cov_b84wb63gz().s[119]++, (cov_b84wb63gz().b[23][0]++, !hasElectronicInvoice) || (cov_b84wb63gz().b[23][1]++, this.invoiceForm.valid));\n    cov_b84wb63gz().s[120]++;\n    this.formValidityChange.emit((cov_b84wb63gz().b[24][0]++, isInvoiceFormValid) && (cov_b84wb63gz().b[24][1]++, this.isCcpsValid));\n  }\n  saveData() {\n    cov_b84wb63gz().f[37]++;\n    cov_b84wb63gz().s[121]++;\n    if (this.ccpsDistribution) {\n      cov_b84wb63gz().b[25][0]++;\n      cov_b84wb63gz().s[122]++;\n      return this.ccpsDistribution.saveCcps();\n    } else {\n      cov_b84wb63gz().b[25][1]++;\n    }\n    cov_b84wb63gz().s[123]++;\n    return of(true);\n  }\n  initializeForm() {\n    cov_b84wb63gz().f[38]++;\n    const hasElectronicInvoice = (cov_b84wb63gz().s[124]++, (cov_b84wb63gz().b[26][0]++, this.report.hasElectronicInvoice) || (cov_b84wb63gz().b[26][1]++, false));\n    const invoiceNumber = (cov_b84wb63gz().s[125]++, (cov_b84wb63gz().b[27][0]++, this.report.invoiceNumber) || (cov_b84wb63gz().b[27][1]++, ''));\n    const invoiceNumberControl = (cov_b84wb63gz().s[126]++, this.invoiceForm.get('invoiceNumber'));\n    cov_b84wb63gz().s[127]++;\n    if (hasElectronicInvoice) {\n      cov_b84wb63gz().b[28][0]++;\n      cov_b84wb63gz().s[128]++;\n      invoiceNumberControl?.enable();\n      cov_b84wb63gz().s[129]++;\n      invoiceNumberControl?.setValidators([Validators.required, Validators.min(0), Validators.pattern(/^\\d+$/)]);\n    } else {\n      cov_b84wb63gz().b[28][1]++;\n      cov_b84wb63gz().s[130]++;\n      invoiceNumberControl?.disable();\n      cov_b84wb63gz().s[131]++;\n      invoiceNumberControl?.clearValidators();\n    }\n    cov_b84wb63gz().s[132]++;\n    this.invoiceForm.patchValue({\n      hasElectronicInvoice,\n      invoiceNumber\n    }, {\n      emitEvent: false\n    });\n    cov_b84wb63gz().s[133]++;\n    if (this.isSupervisor) {\n      cov_b84wb63gz().b[29][0]++;\n      cov_b84wb63gz().s[134]++;\n      this.invoiceForm.get('hasElectronicInvoice')?.disable();\n    } else {\n      cov_b84wb63gz().b[29][1]++;\n    }\n    cov_b84wb63gz().s[135]++;\n    invoiceNumberControl?.updateValueAndValidity({\n      emitEvent: false\n    });\n    cov_b84wb63gz().s[136]++;\n    this.invoiceForm.updateValueAndValidity();\n  }\n  setReportPeriod() {\n    cov_b84wb63gz().f[39]++;\n    cov_b84wb63gz().s[137]++;\n    if (this.report.startDate) {\n      cov_b84wb63gz().b[30][0]++;\n      const startDate = (cov_b84wb63gz().s[138]++, new Date(this.report.startDate));\n      cov_b84wb63gz().s[139]++;\n      this.year = startDate.getFullYear();\n      cov_b84wb63gz().s[140]++;\n      this.month = this.getMonthName(startDate.getMonth());\n    } else {\n      cov_b84wb63gz().b[30][1]++;\n    }\n  }\n  getMonthName(monthIndex) {\n    cov_b84wb63gz().f[40]++;\n    const monthNames = (cov_b84wb63gz().s[141]++, ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio', 'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre']);\n    cov_b84wb63gz().s[142]++;\n    return monthNames[monthIndex];\n  }\n  formatDate(date) {\n    cov_b84wb63gz().f[41]++;\n    cov_b84wb63gz().s[143]++;\n    if (!date) {\n      cov_b84wb63gz().b[31][0]++;\n      cov_b84wb63gz().s[144]++;\n      return 'N/A';\n    } else {\n      cov_b84wb63gz().b[31][1]++;\n    }\n    cov_b84wb63gz().s[145]++;\n    return (cov_b84wb63gz().b[32][0]++, this.datePipe.transform(date, 'dd/MM/yyyy')) || (cov_b84wb63gz().b[32][1]++, 'N/A');\n  }\n  formatCurrency(value) {\n    cov_b84wb63gz().f[42]++;\n    cov_b84wb63gz().s[146]++;\n    if (value === undefined) {\n      cov_b84wb63gz().b[33][0]++;\n      cov_b84wb63gz().s[147]++;\n      return 'N/A';\n    } else {\n      cov_b84wb63gz().b[33][1]++;\n    }\n    cov_b84wb63gz().s[148]++;\n    return new Intl.NumberFormat('es-CO', {\n      style: 'currency',\n      currency: 'COP'\n    }).format(value);\n  }\n  loadContractDetails() {\n    cov_b84wb63gz().f[43]++;\n    cov_b84wb63gz().s[149]++;\n    if (this.report.contractorContract?.contract?.id) {\n      cov_b84wb63gz().b[34][0]++;\n      cov_b84wb63gz().s[150]++;\n      this.contractService.getDetailsById(this.report.contractorContract.contract.id).subscribe({\n        next: details => {\n          cov_b84wb63gz().f[44]++;\n          cov_b84wb63gz().s[151]++;\n          if (details) {\n            cov_b84wb63gz().b[35][0]++;\n            cov_b84wb63gz().s[152]++;\n            this.contractDetails = details;\n            cov_b84wb63gz().s[153]++;\n            this.contractDetails.totalValue = ((cov_b84wb63gz().b[36][0]++, this.contractDetails.initialValue) ?? (cov_b84wb63gz().b[36][1]++, 0)) + ((cov_b84wb63gz().b[37][0]++, this.contractDetails.totalAdditionsValue) ?? (cov_b84wb63gz().b[37][1]++, 0)) - ((cov_b84wb63gz().b[38][0]++, this.contractDetails.totalReductionsValue) ?? (cov_b84wb63gz().b[38][1]++, 0));\n          } else {\n            cov_b84wb63gz().b[35][1]++;\n          }\n          cov_b84wb63gz().s[154]++;\n          this.loadPayments();\n        },\n        error: error => {\n          cov_b84wb63gz().f[45]++;\n          cov_b84wb63gz().s[155]++;\n          this.alert.error((cov_b84wb63gz().b[39][0]++, error.error?.detail) ?? (cov_b84wb63gz().b[39][1]++, 'Error al obtener detalles del contrato'));\n          cov_b84wb63gz().s[156]++;\n          this.loadPayments();\n        }\n      });\n    } else {\n      cov_b84wb63gz().b[34][1]++;\n      cov_b84wb63gz().s[157]++;\n      this.loadPayments();\n    }\n  }\n  updateReport() {\n    cov_b84wb63gz().f[46]++;\n    cov_b84wb63gz().s[158]++;\n    if ((cov_b84wb63gz().b[41][0]++, this.report) && (cov_b84wb63gz().b[41][1]++, this.contractDetails)) {\n      cov_b84wb63gz().b[40][0]++;\n      const updatedReport = (cov_b84wb63gz().s[159]++, {\n        ...this.report,\n        contractNumber: this.contractDetails.contractNumber,\n        contractYear: Number(this.contractDetails.contractYear?.year),\n        contractDurationDays: this.contractDetails.durationDays,\n        contractStartDate: this.contractDetails.startDate,\n        contractEndDate: this.contractDetails.endDate,\n        contractTotalValue: this.contractDetails.totalValue,\n        contractInitialValue: this.contractDetails.initialValue,\n        contractTotalAdditionsValue: this.contractDetails.totalAdditionsValue,\n        contractTotalReductionsValue: this.contractDetails.totalReductionsValue,\n        contractorFullName: this.contractDetails.fullName,\n        contractorIdNumber: this.contractDetails.contractorIdNumber,\n        contractorEmail: this.contractDetails.contractorEmail,\n        contractorPhone: this.contractDetails.contractorPhone,\n        contractDependency: this.contractDetails.dependencyName,\n        contractGroup: this.contractDetails.groupName,\n        contractorBankName: this.contractDetails.bankName,\n        contractorAccountNumber: this.contractDetails.accountNumber,\n        contractorAccountType: this.contractDetails.accountTypeName,\n        supervisorFullName: this.contractDetails.supervisorFullName,\n        supervisorIdNumber: this.contractDetails.supervisorIdNumber,\n        supervisorPosition: this.contractDetails.supervisorPosition,\n        totalValue: (cov_b84wb63gz().b[42][0]++, this.report.totalValue) || (cov_b84wb63gz().b[42][1]++, this.contractDetails.totalValue),\n        paidValue: this.totalPaid,\n        pendingValue: this.pendingBalance,\n        executionPercentage: this.progressPercentage,\n        hasElectronicInvoice: this.invoiceForm.get('hasElectronicInvoice')?.value,\n        invoiceNumber: this.invoiceForm.get('hasElectronicInvoice')?.value ? (cov_b84wb63gz().b[43][0]++, this.invoiceForm.get('invoiceNumber')?.value) : (cov_b84wb63gz().b[43][1]++, 0)\n      });\n      cov_b84wb63gz().s[160]++;\n      this.report = updatedReport;\n      cov_b84wb63gz().s[161]++;\n      this.reportChange.emit(updatedReport);\n    } else {\n      cov_b84wb63gz().b[40][1]++;\n    }\n  }\n  loadPayments() {\n    cov_b84wb63gz().f[47]++;\n    cov_b84wb63gz().s[162]++;\n    if (!this.report?.contractorContractId) {\n      cov_b84wb63gz().b[44][0]++;\n      cov_b84wb63gz().s[163]++;\n      this.updateReport();\n      cov_b84wb63gz().s[164]++;\n      return;\n    } else {\n      cov_b84wb63gz().b[44][1]++;\n    }\n    cov_b84wb63gz().s[165]++;\n    this.monthlyReportService.getByContractorContractId(this.report.contractorContractId).subscribe({\n      next: reports => {\n        cov_b84wb63gz().f[48]++;\n        const paymentObservables = (cov_b84wb63gz().s[166]++, reports.filter(r => {\n          cov_b84wb63gz().f[49]++;\n          cov_b84wb63gz().s[167]++;\n          return r.id;\n        }).map(r => {\n          cov_b84wb63gz().f[50]++;\n          cov_b84wb63gz().s[168]++;\n          return this.paymentService.getByMonthlyReportId(r.id);\n        }));\n        cov_b84wb63gz().s[169]++;\n        if (paymentObservables.length === 0) {\n          cov_b84wb63gz().b[45][0]++;\n          cov_b84wb63gz().s[170]++;\n          this.totalPaid = 0;\n          cov_b84wb63gz().s[171]++;\n          if (this.contractDetails) {\n            cov_b84wb63gz().b[46][0]++;\n            cov_b84wb63gz().s[172]++;\n            this.pendingBalance = (cov_b84wb63gz().b[47][0]++, this.contractDetails.totalValue) ?? (cov_b84wb63gz().b[47][1]++, 0);\n            cov_b84wb63gz().s[173]++;\n            this.progressPercentage = 0;\n          } else {\n            cov_b84wb63gz().b[46][1]++;\n          }\n          cov_b84wb63gz().s[174]++;\n          this.updateReport();\n          cov_b84wb63gz().s[175]++;\n          return;\n        } else {\n          cov_b84wb63gz().b[45][1]++;\n        }\n        cov_b84wb63gz().s[176]++;\n        forkJoin(paymentObservables).subscribe({\n          next: paymentsArray => {\n            cov_b84wb63gz().f[51]++;\n            const allPayments = (cov_b84wb63gz().s[177]++, paymentsArray.flat());\n            const sortedPayments = (cov_b84wb63gz().s[178]++, allPayments.sort((a, b) => {\n              cov_b84wb63gz().f[52]++;\n              cov_b84wb63gz().s[179]++;\n              return a.paymentNumber - b.paymentNumber;\n            }));\n            cov_b84wb63gz().s[180]++;\n            this.totalPaid = sortedPayments.filter(p => {\n              cov_b84wb63gz().f[53]++;\n              cov_b84wb63gz().s[181]++;\n              return p.paymentNumber < this.report.reportNumber;\n            }).reduce((sum, payment) => {\n              cov_b84wb63gz().f[54]++;\n              cov_b84wb63gz().s[182]++;\n              return sum + payment.value;\n            }, 0);\n            cov_b84wb63gz().s[183]++;\n            if (this.contractDetails) {\n              cov_b84wb63gz().b[48][0]++;\n              cov_b84wb63gz().s[184]++;\n              this.pendingBalance = ((cov_b84wb63gz().b[49][0]++, this.contractDetails.totalValue) ?? (cov_b84wb63gz().b[49][1]++, 0)) - this.totalPaid;\n              const totalPaidWithCurrent = (cov_b84wb63gz().s[185]++, this.totalPaid + ((cov_b84wb63gz().b[50][0]++, this.report.totalValue) || (cov_b84wb63gz().b[50][1]++, 0)));\n              cov_b84wb63gz().s[186]++;\n              this.progressPercentage = (cov_b84wb63gz().b[52][0]++, this.contractDetails.totalValue) && (cov_b84wb63gz().b[52][1]++, this.contractDetails.totalValue > 0) ? (cov_b84wb63gz().b[51][0]++, totalPaidWithCurrent / this.contractDetails.totalValue) : (cov_b84wb63gz().b[51][1]++, 0);\n            } else {\n              cov_b84wb63gz().b[48][1]++;\n            }\n            cov_b84wb63gz().s[187]++;\n            this.updateReport();\n          },\n          error: error => {\n            cov_b84wb63gz().f[55]++;\n            cov_b84wb63gz().s[188]++;\n            this.alert.error((cov_b84wb63gz().b[53][0]++, error.error?.detail) ?? (cov_b84wb63gz().b[53][1]++, 'Error al cargar los pagos'));\n            cov_b84wb63gz().s[189]++;\n            this.updateReport();\n          }\n        });\n      },\n      error: error => {\n        cov_b84wb63gz().f[56]++;\n        cov_b84wb63gz().s[190]++;\n        this.alert.error((cov_b84wb63gz().b[54][0]++, error.error?.detail) ?? (cov_b84wb63gz().b[54][1]++, 'Error al cargar los informes'));\n        cov_b84wb63gz().s[191]++;\n        this.updateReport();\n      }\n    });\n  }\n  static {\n    cov_b84wb63gz().s[192]++;\n    this.ctorParameters = () => {\n      cov_b84wb63gz().f[57]++;\n      cov_b84wb63gz().s[193]++;\n      return [{\n        type: ContractService\n      }, {\n        type: AlertService\n      }, {\n        type: MonthlyReportService\n      }, {\n        type: PaymentService\n      }, {\n        type: SupervisorService\n      }, {\n        type: SupervisorContractService\n      }, {\n        type: DatePipe\n      }, {\n        type: FormBuilder\n      }];\n    };\n  }\n  static {\n    cov_b84wb63gz().s[194]++;\n    this.propDecorators = {\n      report: [{\n        type: Input\n      }],\n      reportChange: [{\n        type: Output\n      }],\n      formValidityChange: [{\n        type: Output\n      }],\n      isSupervisor: [{\n        type: Input\n      }],\n      ccpsDistribution: [{\n        type: ViewChild,\n        args: [CcpsDistributionComponent]\n      }]\n    };\n  }\n};\ncov_b84wb63gz().s[195]++;\nMonthlyReportBasicDataComponent = __decorate([Component({\n  selector: 'app-monthly-report-basic-data',\n  template: __NG_CLI_RESOURCE__0,\n  providers: [DatePipe],\n  standalone: true,\n  imports: [MatIcon, DecimalPipe, ReactiveFormsModule, MatCheckbox, MatFormField, MatLabel, MatInput, MatError, MatAutocompleteModule, MatButtonModule, AsyncPipe, CcpsDistributionComponent, MatOption, MatAutocompleteTrigger, PercentPipe],\n  styles: [__NG_CLI_RESOURCE__1]\n})], MonthlyReportBasicDataComponent);\nexport { MonthlyReportBasicDataComponent };", "map": {"version": 3, "names": ["cov_b84wb63gz", "actualCoverage", "AsyncPipe", "DatePipe", "DecimalPipe", "Percent<PERSON><PERSON>e", "Component", "EventEmitter", "Input", "Output", "ViewChild", "FormBuilder", "ReactiveFormsModule", "Validators", "MatAutocompleteModule", "MatAutocompleteTrigger", "MatButtonModule", "MatCheckbox", "<PERSON><PERSON><PERSON><PERSON>", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatIcon", "MatInput", "MatOption", "ContractService", "SupervisorContractService", "MonthlyReportService", "PaymentService", "AlertService", "SupervisorService", "fork<PERSON><PERSON>n", "map", "of", "startWith", "CcpsDistributionComponent", "s", "MonthlyReportBasicDataComponent", "constructor", "contractService", "alert", "monthlyReportService", "paymentService", "supervisorService", "supervisorContractService", "datePipe", "fb", "f", "reportChange", "formValidityChange", "isSupervisor", "year", "month", "contractDetails", "totalPaid", "pendingBalance", "progressPercentage", "supervisors", "supervisorContract", "supervisorContracts", "isEditingSupervisor", "currentEditingSupervisorId", "isCcpsValid", "invoiceForm", "group", "hasElectronicInvoice", "invoiceNumber", "value", "disabled", "supervisorForm", "supervisor", "required", "ngOnInit", "loadContractDetails", "setReportPeriod", "initializeForm", "setupFormValidation", "loadSupervisors", "setupSupervisorAutocomplete", "report", "contractorContract", "contract", "id", "b", "loadSupervisorContracts", "getAll", "subscribe", "next", "error", "detail", "filteredSupervisors", "get", "valueChanges", "pipe", "name", "fullName", "_filterSupervisors", "slice", "filterValue", "toLowerCase", "filter", "includes", "idNumber", "toString", "displaySupervisor", "addNewSupervisor", "length", "warning", "reset", "editSupervisor", "currentSupervisor", "find", "supervisorId", "patchValue", "removeSupervisor", "supervisorContractId", "confirm", "then", "isConfirmed", "delete", "success", "toggleSupervisorEdit", "contractId", "getByContractId", "contracts", "for<PERSON>ach", "index", "getById", "saveSupervisor", "valid", "selected<PERSON><PERSON><PERSON><PERSON>", "update", "supervisorExists", "some", "create", "checked", "invoiceNumberControl", "enable", "setValidators", "min", "pattern", "disable", "clearValidators", "setValue", "updateValueAndValidity", "emitEvent", "statusChanges", "checkFormValidity", "updateReport", "onCcpsValidityChange", "<PERSON><PERSON><PERSON><PERSON>", "onCcpsSaveComplete", "isInvoiceFormValid", "emit", "saveData", "ccpsDistribution", "saveCcps", "startDate", "Date", "getFullYear", "getMonthName", "getMonth", "monthIndex", "monthNames", "formatDate", "date", "transform", "formatCurrency", "undefined", "Intl", "NumberFormat", "style", "currency", "format", "getDetailsById", "details", "totalValue", "initialValue", "totalAdditionsValue", "totalReductionsValue", "loadPayments", "updatedReport", "contractNumber", "contractYear", "Number", "contractDurationDays", "durationDays", "contractStartDate", "contractEndDate", "endDate", "contractTotalValue", "contractInitialValue", "contractTotalAdditionsValue", "contractTotalReductionsValue", "contractorFullName", "contractorIdNumber", "contractorEmail", "contractorPhone", "contractDependency", "dependencyName", "contractGroup", "groupName", "contractorBankName", "bankName", "contractorAccountNumber", "accountNumber", "contractorAccountType", "accountTypeName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supervisorIdNumber", "supervisorPosition", "paidValue", "pendingValue", "executionPercentage", "contractorContractId", "getByContractorContractId", "reports", "paymentObservables", "r", "getByMonthlyReportId", "paymentsArray", "allPayments", "flat", "sortedPayments", "sort", "a", "paymentNumber", "p", "reportNumber", "reduce", "sum", "payment", "totalPaidWithCurrent", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "providers", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\components\\monthly-reports-tab\\monthly-report-dialog\\monthly-report-basic-data\\monthly-report-basic-data.component.ts"], "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON><PERSON>, DatePipe, DecimalPipe, PercentPipe } from '@angular/common';\nimport {\n  Component,\n  EventEmitter,\n  Input,\n  OnInit,\n  Output,\n  ViewChild,\n} from '@angular/core';\nimport {\n  FormBuilder,\n  FormGroup,\n  ReactiveFormsModule,\n  Validators,\n} from '@angular/forms';\nimport { MatAutocompleteModule, MatAutocompleteTrigger } from '@angular/material/autocomplete';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckbox } from '@angular/material/checkbox';\nimport { <PERSON><PERSON><PERSON><PERSON>, MatFormField, MatLabel } from '@angular/material/form-field';\nimport { MatIcon } from '@angular/material/icon';\nimport { MatInput } from '@angular/material/input';\nimport { MatOption } from '@angular/material/core';\nimport { ContractDetails } from '@contract-management/models/contract-details.model';\nimport { SupervisorContract } from '@contract-management/models/supervisor-contract.model';\nimport { ContractService } from '@contract-management/services/contract.service';\nimport { SupervisorContractService } from '@contract-management/services/supervisor-contract.service';\nimport { MonthlyReport } from '@contractor-dashboard/models/monthly-report.model';\nimport { Payment } from '@contractor-dashboard/models/payment.model';\nimport { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';\nimport { PaymentService } from '@contractor-dashboard/services/payment.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { Supervisor } from '@supervisor-management/models/supervisor.model';\nimport { SupervisorService } from '@supervisor-management/services/supervisor.service';\nimport { Observable, forkJoin, map, of, startWith } from 'rxjs';\nimport { CcpsDistributionComponent } from './ccps-distribution/ccps-distribution.component';\n\n@Component({\n  selector: 'app-monthly-report-basic-data',\n  templateUrl: './monthly-report-basic-data.component.html',\n  styleUrl: './monthly-report-basic-data.component.scss',\n  providers: [DatePipe],\n  standalone: true,\n  imports: [\n    MatIcon,\n    DecimalPipe,\n    ReactiveFormsModule,\n    MatCheckbox,\n    MatFormField,\n    MatLabel,\n    MatInput,\n    MatError,\n    MatAutocompleteModule,\n    MatButtonModule,\n    AsyncPipe,\n    CcpsDistributionComponent,\n    MatOption,\n    MatAutocompleteTrigger,\n    PercentPipe,\n  ],\n})\nexport class MonthlyReportBasicDataComponent implements OnInit {\n  @Input() report!: MonthlyReport;\n  @Output() reportChange = new EventEmitter<MonthlyReport>();\n  @Output() formValidityChange = new EventEmitter<boolean>();\n  @Input() isSupervisor = false;\n  @ViewChild(CcpsDistributionComponent)\n  ccpsDistribution!: CcpsDistributionComponent;\n\n  year = 0;\n  month = '';\n  contractDetails: ContractDetails | null = null;\n  totalPaid = 0;\n  pendingBalance = 0;\n  progressPercentage = 0;\n  supervisors: Supervisor[] = [];\n  filteredSupervisors!: Observable<Supervisor[]>;\n  supervisorContract: SupervisorContract | null = null;\n  supervisorContracts: SupervisorContract[] = [];\n  isEditingSupervisor = false;\n  currentEditingSupervisorId: number | null = null;\n  isCcpsValid = false;\n\n  invoiceForm: FormGroup = this.fb.group({\n    hasElectronicInvoice: [false],\n    invoiceNumber: [{ value: '', disabled: true }],\n  });\n\n  supervisorForm: FormGroup = this.fb.group({\n    supervisor: ['', Validators.required],\n  });\n\n  constructor(\n    private readonly contractService: ContractService,\n    private readonly alert: AlertService,\n    private readonly monthlyReportService: MonthlyReportService,\n    private readonly paymentService: PaymentService,\n    private readonly supervisorService: SupervisorService,\n    private readonly supervisorContractService: SupervisorContractService,\n    private readonly datePipe: DatePipe,\n    private readonly fb: FormBuilder,\n  ) {}\n\n  ngOnInit(): void {\n    this.loadContractDetails();\n    this.setReportPeriod();\n    this.initializeForm();\n    this.setupFormValidation();\n    this.loadSupervisors();\n    this.setupSupervisorAutocomplete();\n    if (this.report.contractorContract?.contract?.id) {\n      this.loadSupervisorContracts(this.report.contractorContract.contract.id);\n    }\n  }\n\n  private loadSupervisors(): void {\n    this.supervisorService.getAll().subscribe({\n      next: (supervisors) => {\n        this.supervisors = supervisors;\n      },\n      error: (error) => {\n        this.alert.error(error.error?.detail ?? 'Error al cargar los supervisores');\n      },\n    });\n  }\n\n  private setupSupervisorAutocomplete(): void {\n    this.filteredSupervisors = this.supervisorForm\n      .get('supervisor')!\n      .valueChanges.pipe(\n        startWith(''),\n        map((value) => {\n          const name = typeof value === 'string' ? value : value?.fullName;\n          return name\n            ? this._filterSupervisors(name)\n            : this.supervisors.slice();\n        }),\n      );\n  }\n\n  private _filterSupervisors(value: string): Supervisor[] {\n    const filterValue = value.toLowerCase();\n    return this.supervisors.filter(\n      (supervisor) =>\n        supervisor.fullName.toLowerCase().includes(filterValue) ||\n        supervisor.idNumber.toString().toLowerCase().includes(filterValue),\n    );\n  }\n\n  displaySupervisor(supervisor: Supervisor): string {\n    return supervisor ? `${supervisor.fullName} (${supervisor.idNumber})` : '';\n  }\n\n  addNewSupervisor(): void {\n    if (this.supervisorContracts.length >= 2) {\n      this.alert.warning(\n        'Límite excedido',\n        'Solo se pueden asociar un máximo de 2 supervisores por contrato.',\n      );\n      return;\n    }\n    this.currentEditingSupervisorId = null;\n    this.supervisorForm.reset();\n    this.isEditingSupervisor = true;\n  }\n\n  editSupervisor(supervisorContract: SupervisorContract): void {\n    this.currentEditingSupervisorId = supervisorContract.id;\n    const currentSupervisor = this.supervisors.find(\n      (s) => s.id === supervisorContract.supervisorId,\n    );\n    if (currentSupervisor) {\n      this.supervisorForm.patchValue({ supervisor: currentSupervisor });\n    }\n    this.isEditingSupervisor = true;\n  }\n\n  removeSupervisor(supervisorContractId: number): void {\n    this.alert\n      .confirm(\n        '¿Está seguro de eliminar este supervisor?',\n        'Esta acción no se puede deshacer',\n      )\n      .then((isConfirmed) => {\n        if (isConfirmed) {\n          this.supervisorContractService\n            .delete(supervisorContractId)\n            .subscribe({\n              next: () => {\n                this.alert.success('Supervisor eliminado exitosamente');\n                if (this.report.contractorContract?.contract?.id) {\n                  this.loadSupervisorContracts(\n                    this.report.contractorContract.contract.id,\n                  );\n                }\n              },\n              error: (error) => {\n                this.alert.error(error.error?.detail ?? 'Error al eliminar el supervisor');\n              },\n            });\n        }\n      });\n  }\n\n  toggleSupervisorEdit(): void {\n    this.isEditingSupervisor = !this.isEditingSupervisor;\n    this.currentEditingSupervisorId = null;\n    this.supervisorForm.reset();\n  }\n\n  private loadSupervisorContracts(contractId: number): void {\n    this.supervisorContractService.getByContractId(contractId).subscribe({\n      next: (contracts) => {\n        this.supervisorContracts = contracts;\n\n        this.supervisorContracts.forEach((contract, index) => {\n          this.supervisorService.getById(contract.supervisorId).subscribe({\n            next: (supervisor) => {\n              this.supervisorContracts[index] = {\n                ...this.supervisorContracts[index],\n                supervisor: supervisor,\n              };\n            },\n            error: (error) => {\n              this.alert.error(\n                error.error?.detail ?? `Error al cargar información del supervisor ${contract.supervisorId}`\n              );\n            },\n          });\n        });\n      },\n      error: (error) => {\n        this.alert.error(error.error?.detail ?? 'Error al cargar los contratos de supervisores');\n      },\n    });\n  }\n\n  saveSupervisor(): void {\n    if (\n      this.supervisorForm.valid &&\n      this.report.contractorContract?.contract?.id\n    ) {\n      const selectedSupervisor = this.supervisorForm.get('supervisor')?.value;\n      const contractId = this.report.contractorContract.contract.id;\n\n      if (this.currentEditingSupervisorId) {\n        this.supervisorContractService\n          .update(this.currentEditingSupervisorId, {\n            id: this.currentEditingSupervisorId,\n            supervisorId: selectedSupervisor.id,\n            contractId: contractId,\n          })\n          .subscribe({\n            next: () => {\n              this.alert.success('Supervisor actualizado exitosamente');\n              this.isEditingSupervisor = false;\n              this.loadSupervisorContracts(contractId);\n              this.loadContractDetails();\n            },\n            error: (error) => {\n              this.alert.error(error.error?.detail ?? 'Error al actualizar el supervisor');\n            },\n          });\n      } else {\n        if (this.supervisorContracts.length >= 2) {\n          this.alert.warning(\n            'Límite excedido',\n            'Solo se pueden asociar un máximo de 2 supervisores por contrato.',\n          );\n          return;\n        }\n\n        const supervisorExists = this.supervisorContracts.some(\n          (contract) => contract.supervisorId === selectedSupervisor.id,\n        );\n\n        if (supervisorExists) {\n          this.alert.warning(\n            'Supervisor ya asignado',\n            'Este supervisor ya está asignado a este contrato.',\n          );\n          return;\n        }\n\n        this.supervisorContractService\n          .create({\n            supervisorId: selectedSupervisor.id,\n            contractId: contractId,\n          })\n          .subscribe({\n            next: () => {\n              this.alert.success('Supervisor asignado exitosamente');\n              this.isEditingSupervisor = false;\n              this.loadSupervisorContracts(contractId);\n              this.loadContractDetails();\n            },\n            error: (error) => {\n              if (\n                error?.error?.detail?.includes(\n                  'Multiple supervisors are associated',\n                )\n              ) {\n                this.alert.warning(\n                  'Límite excedido',\n                  'Solo se pueden asociar un máximo de 2 supervisores por contrato.',\n                );\n              } else if (error?.error?.detail?.includes('already associated')) {\n                this.alert.warning(\n                  'Supervisor ya asignado',\n                  'Este supervisor ya está asignado a este contrato.',\n                );\n              } else {\n                this.alert.error(error.error?.detail ?? 'Error al asignar el supervisor');\n              }\n            },\n          });\n      }\n    }\n  }\n\n  private setupFormValidation(): void {\n    this.invoiceForm\n      .get('hasElectronicInvoice')\n      ?.valueChanges.subscribe((checked) => {\n        const invoiceNumberControl = this.invoiceForm.get('invoiceNumber');\n        if (checked) {\n          invoiceNumberControl?.enable();\n          invoiceNumberControl?.setValidators([\n            Validators.required,\n            Validators.min(0),\n            Validators.pattern(/^\\d+$/),\n          ]);\n        } else {\n          invoiceNumberControl?.disable();\n          invoiceNumberControl?.clearValidators();\n          invoiceNumberControl?.setValue('');\n        }\n        invoiceNumberControl?.updateValueAndValidity({ emitEvent: false });\n        this.invoiceForm.updateValueAndValidity();\n      });\n\n    this.invoiceForm.statusChanges.subscribe(() => {\n      this.checkFormValidity();\n    });\n\n    this.invoiceForm.valueChanges.subscribe(() => {\n      this.updateReport();\n    });\n  }\n\n  onCcpsValidityChange(isValid: boolean): void {\n    this.isCcpsValid = isValid;\n    this.checkFormValidity();\n  }\n\n  onCcpsSaveComplete(): void {\n    this.alert.success('Distribución de CCPs guardada exitosamente');\n  }\n\n  private checkFormValidity(): void {\n    const hasElectronicInvoice = this.invoiceForm.get(\n      'hasElectronicInvoice',\n    )?.value;\n    const isInvoiceFormValid = !hasElectronicInvoice || this.invoiceForm.valid;\n    this.formValidityChange.emit(isInvoiceFormValid && this.isCcpsValid);\n  }\n\n  saveData(): Observable<boolean> {\n    if (this.ccpsDistribution) {\n      return this.ccpsDistribution.saveCcps();\n    }\n    return of(true);\n  }\n\n  private initializeForm(): void {\n    const hasElectronicInvoice = this.report.hasElectronicInvoice || false;\n    const invoiceNumber = this.report.invoiceNumber || '';\n\n    const invoiceNumberControl = this.invoiceForm.get('invoiceNumber');\n    if (hasElectronicInvoice) {\n      invoiceNumberControl?.enable();\n      invoiceNumberControl?.setValidators([\n        Validators.required,\n        Validators.min(0),\n        Validators.pattern(/^\\d+$/),\n      ]);\n    } else {\n      invoiceNumberControl?.disable();\n      invoiceNumberControl?.clearValidators();\n    }\n\n    this.invoiceForm.patchValue(\n      {\n        hasElectronicInvoice,\n        invoiceNumber,\n      },\n      { emitEvent: false },\n    );\n\n    if (this.isSupervisor) {\n      this.invoiceForm.get('hasElectronicInvoice')?.disable();\n    }\n\n    invoiceNumberControl?.updateValueAndValidity({ emitEvent: false });\n    this.invoiceForm.updateValueAndValidity();\n  }\n\n  setReportPeriod(): void {\n    if (this.report.startDate) {\n      const startDate = new Date(this.report.startDate);\n      this.year = startDate.getFullYear();\n      this.month = this.getMonthName(startDate.getMonth());\n    }\n  }\n\n  getMonthName(monthIndex: number): string {\n    const monthNames = [\n      'Enero',\n      'Febrero',\n      'Marzo',\n      'Abril',\n      'Mayo',\n      'Junio',\n      'Julio',\n      'Agosto',\n      'Septiembre',\n      'Octubre',\n      'Noviembre',\n      'Diciembre',\n    ];\n    return monthNames[monthIndex];\n  }\n\n  formatDate(date: string | Date | undefined): string {\n    if (!date) return 'N/A';\n    return this.datePipe.transform(date, 'dd/MM/yyyy') || 'N/A';\n  }\n\n  formatCurrency(value: number | undefined): string {\n    if (value === undefined) return 'N/A';\n    return new Intl.NumberFormat('es-CO', {\n      style: 'currency',\n      currency: 'COP',\n    }).format(value);\n  }\n\n  loadContractDetails(): void {\n    if (this.report.contractorContract?.contract?.id) {\n      this.contractService\n        .getDetailsById(this.report.contractorContract.contract.id)\n        .subscribe({\n          next: (details: ContractDetails | null) => {\n            if (details) {\n              this.contractDetails = details;\n              this.contractDetails.totalValue =\n                (this.contractDetails.initialValue ?? 0) +\n                (this.contractDetails.totalAdditionsValue ?? 0) -\n                (this.contractDetails.totalReductionsValue ?? 0);\n            }\n            this.loadPayments();\n          },\n          error: (error) => {\n            this.alert.error(error.error?.detail ?? 'Error al obtener detalles del contrato');\n            this.loadPayments();\n          },\n        });\n    } else {\n      this.loadPayments();\n    }\n  }\n\n  private updateReport(): void {\n    if (this.report && this.contractDetails) {\n      const updatedReport = {\n        ...this.report,\n        contractNumber: this.contractDetails.contractNumber,\n        contractYear: Number(this.contractDetails.contractYear?.year),\n        contractDurationDays: this.contractDetails.durationDays,\n        contractStartDate: this.contractDetails.startDate,\n        contractEndDate: this.contractDetails.endDate,\n        contractTotalValue: this.contractDetails.totalValue,\n        contractInitialValue: this.contractDetails.initialValue,\n        contractTotalAdditionsValue: this.contractDetails.totalAdditionsValue,\n        contractTotalReductionsValue: this.contractDetails.totalReductionsValue,\n        contractorFullName: this.contractDetails.fullName,\n        contractorIdNumber: this.contractDetails.contractorIdNumber,\n        contractorEmail: this.contractDetails.contractorEmail,\n        contractorPhone: this.contractDetails.contractorPhone,\n        contractDependency: this.contractDetails.dependencyName,\n        contractGroup: this.contractDetails.groupName,\n        contractorBankName: this.contractDetails.bankName,\n        contractorAccountNumber: this.contractDetails.accountNumber,\n        contractorAccountType: this.contractDetails.accountTypeName,\n        supervisorFullName: this.contractDetails.supervisorFullName,\n        supervisorIdNumber: this.contractDetails.supervisorIdNumber,\n        supervisorPosition: this.contractDetails.supervisorPosition,\n        totalValue: this.report.totalValue || this.contractDetails.totalValue,\n        paidValue: this.totalPaid,\n        pendingValue: this.pendingBalance,\n        executionPercentage: this.progressPercentage,\n        hasElectronicInvoice: this.invoiceForm.get('hasElectronicInvoice')\n          ?.value,\n        invoiceNumber: this.invoiceForm.get('hasElectronicInvoice')?.value\n          ? this.invoiceForm.get('invoiceNumber')?.value\n          : 0,\n      };\n\n      this.report = updatedReport;\n      this.reportChange.emit(updatedReport);\n    }\n  }\n\n  loadPayments(): void {\n    if (!this.report?.contractorContractId) {\n      this.updateReport();\n      return;\n    }\n\n    this.monthlyReportService\n      .getByContractorContractId(this.report.contractorContractId)\n      .subscribe({\n        next: (reports: MonthlyReport[]) => {\n          const paymentObservables = reports\n            .filter((r) => r.id)\n            .map((r) => this.paymentService.getByMonthlyReportId(r.id));\n\n          if (paymentObservables.length === 0) {\n            this.totalPaid = 0;\n            if (this.contractDetails) {\n              this.pendingBalance = this.contractDetails.totalValue ?? 0;\n              this.progressPercentage = 0;\n            }\n            this.updateReport();\n            return;\n          }\n\n          forkJoin(paymentObservables).subscribe({\n            next: (paymentsArray: Payment[][]) => {\n              const allPayments = paymentsArray.flat();\n              const sortedPayments = allPayments.sort(\n                (a, b) => a.paymentNumber - b.paymentNumber,\n              );\n\n              this.totalPaid = sortedPayments\n                .filter((p) => p.paymentNumber < this.report.reportNumber)\n                .reduce((sum, payment) => sum + payment.value, 0);\n\n              if (this.contractDetails) {\n                this.pendingBalance =\n                  (this.contractDetails.totalValue ?? 0) - this.totalPaid;\n\n                const totalPaidWithCurrent =\n                  this.totalPaid + (this.report.totalValue || 0);\n\n                this.progressPercentage =\n                  this.contractDetails.totalValue &&\n                  this.contractDetails.totalValue > 0\n                    ? totalPaidWithCurrent / this.contractDetails.totalValue\n                    : 0;\n              }\n              this.updateReport();\n            },\n            error: (error) => {\n              this.alert.error(error.error?.detail ?? 'Error al cargar los pagos');\n              this.updateReport();\n            },\n          });\n        },\n        error: (error) => {\n          this.alert.error(error.error?.detail ?? 'Error al cargar los informes');\n          this.updateReport();\n        },\n      });\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA4BS;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;AA5BT,SAASE,SAAS,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,WAAW,QAAQ,iBAAiB;AAC/E,SACEC,SAAS,EACTC,YAAY,EACZC,KAAK,EAELC,MAAM,EACNC,SAAS,QACJ,eAAe;AACtB,SACEC,WAAW,EAEXC,mBAAmB,EACnBC,UAAU,QACL,gBAAgB;AACvB,SAASC,qBAAqB,EAAEC,sBAAsB,QAAQ,gCAAgC;AAC9F,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,WAAW,QAAQ,4BAA4B;AACxD,SAASC,QAAQ,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,8BAA8B;AAC/E,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,SAAS,QAAQ,wBAAwB;AAGlD,SAASC,eAAe,QAAQ,gDAAgD;AAChF,SAASC,yBAAyB,QAAQ,2DAA2D;AAGrG,SAASC,oBAAoB,QAAQ,uDAAuD;AAC5F,SAASC,cAAc,QAAQ,gDAAgD;AAC/E,SAASC,YAAY,QAAQ,gCAAgC;AAE7D,SAASC,iBAAiB,QAAQ,oDAAoD;AACtF,SAAqBC,QAAQ,EAAEC,GAAG,EAAEC,EAAE,EAAEC,SAAS,QAAQ,MAAM;AAC/D,SAASC,yBAAyB,QAAQ,iDAAiD;AAAClC,aAAA,GAAAmC,CAAA;AA0BrF,IAAMC,+BAA+B,GAArC,MAAMA,+BAA+B;EA+B1CC,YACmBC,eAAgC,EAChCC,KAAmB,EACnBC,oBAA0C,EAC1CC,cAA8B,EAC9BC,iBAAoC,EACpCC,yBAAoD,EACpDC,QAAkB,EAClBC,EAAe;IAAA7C,aAAA,GAAA8C,CAAA;IAAA9C,aAAA,GAAAmC,CAAA;IAPf,KAAAG,eAAe,GAAfA,eAAe;IAAiBtC,aAAA,GAAAmC,CAAA;IAChC,KAAAI,KAAK,GAALA,KAAK;IAAcvC,aAAA,GAAAmC,CAAA;IACnB,KAAAK,oBAAoB,GAApBA,oBAAoB;IAAsBxC,aAAA,GAAAmC,CAAA;IAC1C,KAAAM,cAAc,GAAdA,cAAc;IAAgBzC,aAAA,GAAAmC,CAAA;IAC9B,KAAAO,iBAAiB,GAAjBA,iBAAiB;IAAmB1C,aAAA,GAAAmC,CAAA;IACpC,KAAAQ,yBAAyB,GAAzBA,yBAAyB;IAA2B3C,aAAA,GAAAmC,CAAA;IACpD,KAAAS,QAAQ,GAARA,QAAQ;IAAU5C,aAAA,GAAAmC,CAAA;IAClB,KAAAU,EAAE,GAAFA,EAAE;IAAa7C,aAAA,GAAAmC,CAAA;IArCxB,KAAAY,YAAY,GAAG,IAAIxC,YAAY,EAAiB;IAAAP,aAAA,GAAAmC,CAAA;IAChD,KAAAa,kBAAkB,GAAG,IAAIzC,YAAY,EAAW;IAAAP,aAAA,GAAAmC,CAAA;IACjD,KAAAc,YAAY,GAAG,KAAK;IAAAjD,aAAA,GAAAmC,CAAA;IAI7B,KAAAe,IAAI,GAAG,CAAC;IAAClD,aAAA,GAAAmC,CAAA;IACT,KAAAgB,KAAK,GAAG,EAAE;IAACnD,aAAA,GAAAmC,CAAA;IACX,KAAAiB,eAAe,GAA2B,IAAI;IAACpD,aAAA,GAAAmC,CAAA;IAC/C,KAAAkB,SAAS,GAAG,CAAC;IAACrD,aAAA,GAAAmC,CAAA;IACd,KAAAmB,cAAc,GAAG,CAAC;IAACtD,aAAA,GAAAmC,CAAA;IACnB,KAAAoB,kBAAkB,GAAG,CAAC;IAACvD,aAAA,GAAAmC,CAAA;IACvB,KAAAqB,WAAW,GAAiB,EAAE;IAACxD,aAAA,GAAAmC,CAAA;IAE/B,KAAAsB,kBAAkB,GAA8B,IAAI;IAACzD,aAAA,GAAAmC,CAAA;IACrD,KAAAuB,mBAAmB,GAAyB,EAAE;IAAC1D,aAAA,GAAAmC,CAAA;IAC/C,KAAAwB,mBAAmB,GAAG,KAAK;IAAC3D,aAAA,GAAAmC,CAAA;IAC5B,KAAAyB,0BAA0B,GAAkB,IAAI;IAAC5D,aAAA,GAAAmC,CAAA;IACjD,KAAA0B,WAAW,GAAG,KAAK;IAAC7D,aAAA,GAAAmC,CAAA;IAEpB,KAAA2B,WAAW,GAAc,IAAI,CAACjB,EAAE,CAACkB,KAAK,CAAC;MACrCC,oBAAoB,EAAE,CAAC,KAAK,CAAC;MAC7BC,aAAa,EAAE,CAAC;QAAEC,KAAK,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;KAC9C,CAAC;IAACnE,aAAA,GAAAmC,CAAA;IAEH,KAAAiC,cAAc,GAAc,IAAI,CAACvB,EAAE,CAACkB,KAAK,CAAC;MACxCM,UAAU,EAAE,CAAC,EAAE,EAAExD,UAAU,CAACyD,QAAQ;KACrC,CAAC;EAWC;EAEHC,QAAQA,CAAA;IAAAvE,aAAA,GAAA8C,CAAA;IAAA9C,aAAA,GAAAmC,CAAA;IACN,IAAI,CAACqC,mBAAmB,EAAE;IAACxE,aAAA,GAAAmC,CAAA;IAC3B,IAAI,CAACsC,eAAe,EAAE;IAACzE,aAAA,GAAAmC,CAAA;IACvB,IAAI,CAACuC,cAAc,EAAE;IAAC1E,aAAA,GAAAmC,CAAA;IACtB,IAAI,CAACwC,mBAAmB,EAAE;IAAC3E,aAAA,GAAAmC,CAAA;IAC3B,IAAI,CAACyC,eAAe,EAAE;IAAC5E,aAAA,GAAAmC,CAAA;IACvB,IAAI,CAAC0C,2BAA2B,EAAE;IAAC7E,aAAA,GAAAmC,CAAA;IACnC,IAAI,IAAI,CAAC2C,MAAM,CAACC,kBAAkB,EAAEC,QAAQ,EAAEC,EAAE,EAAE;MAAAjF,aAAA,GAAAkF,CAAA;MAAAlF,aAAA,GAAAmC,CAAA;MAChD,IAAI,CAACgD,uBAAuB,CAAC,IAAI,CAACL,MAAM,CAACC,kBAAkB,CAACC,QAAQ,CAACC,EAAE,CAAC;IAC1E,CAAC;MAAAjF,aAAA,GAAAkF,CAAA;IAAA;EACH;EAEQN,eAAeA,CAAA;IAAA5E,aAAA,GAAA8C,CAAA;IAAA9C,aAAA,GAAAmC,CAAA;IACrB,IAAI,CAACO,iBAAiB,CAAC0C,MAAM,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAG9B,WAAW,IAAI;QAAAxD,aAAA,GAAA8C,CAAA;QAAA9C,aAAA,GAAAmC,CAAA;QACpB,IAAI,CAACqB,WAAW,GAAGA,WAAW;MAChC,CAAC;MACD+B,KAAK,EAAGA,KAAK,IAAI;QAAAvF,aAAA,GAAA8C,CAAA;QAAA9C,aAAA,GAAAmC,CAAA;QACf,IAAI,CAACI,KAAK,CAACgD,KAAK,CAAC,CAAAvF,aAAA,GAAAkF,CAAA,UAAAK,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAAxF,aAAA,GAAAkF,CAAA,UAAI,kCAAkC,EAAC;MAC7E;KACD,CAAC;EACJ;EAEQL,2BAA2BA,CAAA;IAAA7E,aAAA,GAAA8C,CAAA;IAAA9C,aAAA,GAAAmC,CAAA;IACjC,IAAI,CAACsD,mBAAmB,GAAG,IAAI,CAACrB,cAAc,CAC3CsB,GAAG,CAAC,YAAY,CAAE,CAClBC,YAAY,CAACC,IAAI,CAChB3D,SAAS,CAAC,EAAE,CAAC,EACbF,GAAG,CAAEmC,KAAK,IAAI;MAAAlE,aAAA,GAAA8C,CAAA;MACZ,MAAM+C,IAAI,IAAA7F,aAAA,GAAAmC,CAAA,QAAG,OAAO+B,KAAK,KAAK,QAAQ,IAAAlE,aAAA,GAAAkF,CAAA,UAAGhB,KAAK,KAAAlE,aAAA,GAAAkF,CAAA,UAAGhB,KAAK,EAAE4B,QAAQ;MAAC9F,aAAA,GAAAmC,CAAA;MACjE,OAAO0D,IAAI,IAAA7F,aAAA,GAAAkF,CAAA,UACP,IAAI,CAACa,kBAAkB,CAACF,IAAI,CAAC,KAAA7F,aAAA,GAAAkF,CAAA,UAC7B,IAAI,CAAC1B,WAAW,CAACwC,KAAK,EAAE;IAC9B,CAAC,CAAC,CACH;EACL;EAEQD,kBAAkBA,CAAC7B,KAAa;IAAAlE,aAAA,GAAA8C,CAAA;IACtC,MAAMmD,WAAW,IAAAjG,aAAA,GAAAmC,CAAA,QAAG+B,KAAK,CAACgC,WAAW,EAAE;IAAClG,aAAA,GAAAmC,CAAA;IACxC,OAAO,IAAI,CAACqB,WAAW,CAAC2C,MAAM,CAC3B9B,UAAU,IACT;MAAArE,aAAA,GAAA8C,CAAA;MAAA9C,aAAA,GAAAmC,CAAA;MAAA,QAAAnC,aAAA,GAAAkF,CAAA,UAAAb,UAAU,CAACyB,QAAQ,CAACI,WAAW,EAAE,CAACE,QAAQ,CAACH,WAAW,CAAC,MAAAjG,aAAA,GAAAkF,CAAA,UACvDb,UAAU,CAACgC,QAAQ,CAACC,QAAQ,EAAE,CAACJ,WAAW,EAAE,CAACE,QAAQ,CAACH,WAAW,CAAC;IAAD,CAAC,CACrE;EACH;EAEAM,iBAAiBA,CAAClC,UAAsB;IAAArE,aAAA,GAAA8C,CAAA;IAAA9C,aAAA,GAAAmC,CAAA;IACtC,OAAOkC,UAAU,IAAArE,aAAA,GAAAkF,CAAA,UAAG,GAAGb,UAAU,CAACyB,QAAQ,KAAKzB,UAAU,CAACgC,QAAQ,GAAG,KAAArG,aAAA,GAAAkF,CAAA,UAAG,EAAE;EAC5E;EAEAsB,gBAAgBA,CAAA;IAAAxG,aAAA,GAAA8C,CAAA;IAAA9C,aAAA,GAAAmC,CAAA;IACd,IAAI,IAAI,CAACuB,mBAAmB,CAAC+C,MAAM,IAAI,CAAC,EAAE;MAAAzG,aAAA,GAAAkF,CAAA;MAAAlF,aAAA,GAAAmC,CAAA;MACxC,IAAI,CAACI,KAAK,CAACmE,OAAO,CAChB,iBAAiB,EACjB,kEAAkE,CACnE;MAAC1G,aAAA,GAAAmC,CAAA;MACF;IACF,CAAC;MAAAnC,aAAA,GAAAkF,CAAA;IAAA;IAAAlF,aAAA,GAAAmC,CAAA;IACD,IAAI,CAACyB,0BAA0B,GAAG,IAAI;IAAC5D,aAAA,GAAAmC,CAAA;IACvC,IAAI,CAACiC,cAAc,CAACuC,KAAK,EAAE;IAAC3G,aAAA,GAAAmC,CAAA;IAC5B,IAAI,CAACwB,mBAAmB,GAAG,IAAI;EACjC;EAEAiD,cAAcA,CAACnD,kBAAsC;IAAAzD,aAAA,GAAA8C,CAAA;IAAA9C,aAAA,GAAAmC,CAAA;IACnD,IAAI,CAACyB,0BAA0B,GAAGH,kBAAkB,CAACwB,EAAE;IACvD,MAAM4B,iBAAiB,IAAA7G,aAAA,GAAAmC,CAAA,QAAG,IAAI,CAACqB,WAAW,CAACsD,IAAI,CAC5C3E,CAAC,IAAK;MAAAnC,aAAA,GAAA8C,CAAA;MAAA9C,aAAA,GAAAmC,CAAA;MAAA,OAAAA,CAAC,CAAC8C,EAAE,KAAKxB,kBAAkB,CAACsD,YAAY;IAAZ,CAAY,CAChD;IAAC/G,aAAA,GAAAmC,CAAA;IACF,IAAI0E,iBAAiB,EAAE;MAAA7G,aAAA,GAAAkF,CAAA;MAAAlF,aAAA,GAAAmC,CAAA;MACrB,IAAI,CAACiC,cAAc,CAAC4C,UAAU,CAAC;QAAE3C,UAAU,EAAEwC;MAAiB,CAAE,CAAC;IACnE,CAAC;MAAA7G,aAAA,GAAAkF,CAAA;IAAA;IAAAlF,aAAA,GAAAmC,CAAA;IACD,IAAI,CAACwB,mBAAmB,GAAG,IAAI;EACjC;EAEAsD,gBAAgBA,CAACC,oBAA4B;IAAAlH,aAAA,GAAA8C,CAAA;IAAA9C,aAAA,GAAAmC,CAAA;IAC3C,IAAI,CAACI,KAAK,CACP4E,OAAO,CACN,2CAA2C,EAC3C,kCAAkC,CACnC,CACAC,IAAI,CAAEC,WAAW,IAAI;MAAArH,aAAA,GAAA8C,CAAA;MAAA9C,aAAA,GAAAmC,CAAA;MACpB,IAAIkF,WAAW,EAAE;QAAArH,aAAA,GAAAkF,CAAA;QAAAlF,aAAA,GAAAmC,CAAA;QACf,IAAI,CAACQ,yBAAyB,CAC3B2E,MAAM,CAACJ,oBAAoB,CAAC,CAC5B7B,SAAS,CAAC;UACTC,IAAI,EAAEA,CAAA,KAAK;YAAAtF,aAAA,GAAA8C,CAAA;YAAA9C,aAAA,GAAAmC,CAAA;YACT,IAAI,CAACI,KAAK,CAACgF,OAAO,CAAC,mCAAmC,CAAC;YAACvH,aAAA,GAAAmC,CAAA;YACxD,IAAI,IAAI,CAAC2C,MAAM,CAACC,kBAAkB,EAAEC,QAAQ,EAAEC,EAAE,EAAE;cAAAjF,aAAA,GAAAkF,CAAA;cAAAlF,aAAA,GAAAmC,CAAA;cAChD,IAAI,CAACgD,uBAAuB,CAC1B,IAAI,CAACL,MAAM,CAACC,kBAAkB,CAACC,QAAQ,CAACC,EAAE,CAC3C;YACH,CAAC;cAAAjF,aAAA,GAAAkF,CAAA;YAAA;UACH,CAAC;UACDK,KAAK,EAAGA,KAAK,IAAI;YAAAvF,aAAA,GAAA8C,CAAA;YAAA9C,aAAA,GAAAmC,CAAA;YACf,IAAI,CAACI,KAAK,CAACgD,KAAK,CAAC,CAAAvF,aAAA,GAAAkF,CAAA,WAAAK,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAAxF,aAAA,GAAAkF,CAAA,WAAI,iCAAiC,EAAC;UAC5E;SACD,CAAC;MACN,CAAC;QAAAlF,aAAA,GAAAkF,CAAA;MAAA;IACH,CAAC,CAAC;EACN;EAEAsC,oBAAoBA,CAAA;IAAAxH,aAAA,GAAA8C,CAAA;IAAA9C,aAAA,GAAAmC,CAAA;IAClB,IAAI,CAACwB,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;IAAC3D,aAAA,GAAAmC,CAAA;IACrD,IAAI,CAACyB,0BAA0B,GAAG,IAAI;IAAC5D,aAAA,GAAAmC,CAAA;IACvC,IAAI,CAACiC,cAAc,CAACuC,KAAK,EAAE;EAC7B;EAEQxB,uBAAuBA,CAACsC,UAAkB;IAAAzH,aAAA,GAAA8C,CAAA;IAAA9C,aAAA,GAAAmC,CAAA;IAChD,IAAI,CAACQ,yBAAyB,CAAC+E,eAAe,CAACD,UAAU,CAAC,CAACpC,SAAS,CAAC;MACnEC,IAAI,EAAGqC,SAAS,IAAI;QAAA3H,aAAA,GAAA8C,CAAA;QAAA9C,aAAA,GAAAmC,CAAA;QAClB,IAAI,CAACuB,mBAAmB,GAAGiE,SAAS;QAAC3H,aAAA,GAAAmC,CAAA;QAErC,IAAI,CAACuB,mBAAmB,CAACkE,OAAO,CAAC,CAAC5C,QAAQ,EAAE6C,KAAK,KAAI;UAAA7H,aAAA,GAAA8C,CAAA;UAAA9C,aAAA,GAAAmC,CAAA;UACnD,IAAI,CAACO,iBAAiB,CAACoF,OAAO,CAAC9C,QAAQ,CAAC+B,YAAY,CAAC,CAAC1B,SAAS,CAAC;YAC9DC,IAAI,EAAGjB,UAAU,IAAI;cAAArE,aAAA,GAAA8C,CAAA;cAAA9C,aAAA,GAAAmC,CAAA;cACnB,IAAI,CAACuB,mBAAmB,CAACmE,KAAK,CAAC,GAAG;gBAChC,GAAG,IAAI,CAACnE,mBAAmB,CAACmE,KAAK,CAAC;gBAClCxD,UAAU,EAAEA;eACb;YACH,CAAC;YACDkB,KAAK,EAAGA,KAAK,IAAI;cAAAvF,aAAA,GAAA8C,CAAA;cAAA9C,aAAA,GAAAmC,CAAA;cACf,IAAI,CAACI,KAAK,CAACgD,KAAK,CACd,CAAAvF,aAAA,GAAAkF,CAAA,WAAAK,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAAxF,aAAA,GAAAkF,CAAA,WAAI,8CAA8CF,QAAQ,CAAC+B,YAAY,EAAE,EAC7F;YACH;WACD,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC;MACDxB,KAAK,EAAGA,KAAK,IAAI;QAAAvF,aAAA,GAAA8C,CAAA;QAAA9C,aAAA,GAAAmC,CAAA;QACf,IAAI,CAACI,KAAK,CAACgD,KAAK,CAAC,CAAAvF,aAAA,GAAAkF,CAAA,WAAAK,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAAxF,aAAA,GAAAkF,CAAA,WAAI,+CAA+C,EAAC;MAC1F;KACD,CAAC;EACJ;EAEA6C,cAAcA,CAAA;IAAA/H,aAAA,GAAA8C,CAAA;IAAA9C,aAAA,GAAAmC,CAAA;IACZ,IACE,CAAAnC,aAAA,GAAAkF,CAAA,eAAI,CAACd,cAAc,CAAC4D,KAAK,MAAAhI,aAAA,GAAAkF,CAAA,WACzB,IAAI,CAACJ,MAAM,CAACC,kBAAkB,EAAEC,QAAQ,EAAEC,EAAE,GAC5C;MAAAjF,aAAA,GAAAkF,CAAA;MACA,MAAM+C,kBAAkB,IAAAjI,aAAA,GAAAmC,CAAA,QAAG,IAAI,CAACiC,cAAc,CAACsB,GAAG,CAAC,YAAY,CAAC,EAAExB,KAAK;MACvE,MAAMuD,UAAU,IAAAzH,aAAA,GAAAmC,CAAA,QAAG,IAAI,CAAC2C,MAAM,CAACC,kBAAkB,CAACC,QAAQ,CAACC,EAAE;MAACjF,aAAA,GAAAmC,CAAA;MAE9D,IAAI,IAAI,CAACyB,0BAA0B,EAAE;QAAA5D,aAAA,GAAAkF,CAAA;QAAAlF,aAAA,GAAAmC,CAAA;QACnC,IAAI,CAACQ,yBAAyB,CAC3BuF,MAAM,CAAC,IAAI,CAACtE,0BAA0B,EAAE;UACvCqB,EAAE,EAAE,IAAI,CAACrB,0BAA0B;UACnCmD,YAAY,EAAEkB,kBAAkB,CAAChD,EAAE;UACnCwC,UAAU,EAAEA;SACb,CAAC,CACDpC,SAAS,CAAC;UACTC,IAAI,EAAEA,CAAA,KAAK;YAAAtF,aAAA,GAAA8C,CAAA;YAAA9C,aAAA,GAAAmC,CAAA;YACT,IAAI,CAACI,KAAK,CAACgF,OAAO,CAAC,qCAAqC,CAAC;YAACvH,aAAA,GAAAmC,CAAA;YAC1D,IAAI,CAACwB,mBAAmB,GAAG,KAAK;YAAC3D,aAAA,GAAAmC,CAAA;YACjC,IAAI,CAACgD,uBAAuB,CAACsC,UAAU,CAAC;YAACzH,aAAA,GAAAmC,CAAA;YACzC,IAAI,CAACqC,mBAAmB,EAAE;UAC5B,CAAC;UACDe,KAAK,EAAGA,KAAK,IAAI;YAAAvF,aAAA,GAAA8C,CAAA;YAAA9C,aAAA,GAAAmC,CAAA;YACf,IAAI,CAACI,KAAK,CAACgD,KAAK,CAAC,CAAAvF,aAAA,GAAAkF,CAAA,WAAAK,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAAxF,aAAA,GAAAkF,CAAA,WAAI,mCAAmC,EAAC;UAC9E;SACD,CAAC;MACN,CAAC,MAAM;QAAAlF,aAAA,GAAAkF,CAAA;QAAAlF,aAAA,GAAAmC,CAAA;QACL,IAAI,IAAI,CAACuB,mBAAmB,CAAC+C,MAAM,IAAI,CAAC,EAAE;UAAAzG,aAAA,GAAAkF,CAAA;UAAAlF,aAAA,GAAAmC,CAAA;UACxC,IAAI,CAACI,KAAK,CAACmE,OAAO,CAChB,iBAAiB,EACjB,kEAAkE,CACnE;UAAC1G,aAAA,GAAAmC,CAAA;UACF;QACF,CAAC;UAAAnC,aAAA,GAAAkF,CAAA;QAAA;QAED,MAAMiD,gBAAgB,IAAAnI,aAAA,GAAAmC,CAAA,QAAG,IAAI,CAACuB,mBAAmB,CAAC0E,IAAI,CACnDpD,QAAQ,IAAK;UAAAhF,aAAA,GAAA8C,CAAA;UAAA9C,aAAA,GAAAmC,CAAA;UAAA,OAAA6C,QAAQ,CAAC+B,YAAY,KAAKkB,kBAAkB,CAAChD,EAAE;QAAF,CAAE,CAC9D;QAACjF,aAAA,GAAAmC,CAAA;QAEF,IAAIgG,gBAAgB,EAAE;UAAAnI,aAAA,GAAAkF,CAAA;UAAAlF,aAAA,GAAAmC,CAAA;UACpB,IAAI,CAACI,KAAK,CAACmE,OAAO,CAChB,wBAAwB,EACxB,mDAAmD,CACpD;UAAC1G,aAAA,GAAAmC,CAAA;UACF;QACF,CAAC;UAAAnC,aAAA,GAAAkF,CAAA;QAAA;QAAAlF,aAAA,GAAAmC,CAAA;QAED,IAAI,CAACQ,yBAAyB,CAC3B0F,MAAM,CAAC;UACNtB,YAAY,EAAEkB,kBAAkB,CAAChD,EAAE;UACnCwC,UAAU,EAAEA;SACb,CAAC,CACDpC,SAAS,CAAC;UACTC,IAAI,EAAEA,CAAA,KAAK;YAAAtF,aAAA,GAAA8C,CAAA;YAAA9C,aAAA,GAAAmC,CAAA;YACT,IAAI,CAACI,KAAK,CAACgF,OAAO,CAAC,kCAAkC,CAAC;YAACvH,aAAA,GAAAmC,CAAA;YACvD,IAAI,CAACwB,mBAAmB,GAAG,KAAK;YAAC3D,aAAA,GAAAmC,CAAA;YACjC,IAAI,CAACgD,uBAAuB,CAACsC,UAAU,CAAC;YAACzH,aAAA,GAAAmC,CAAA;YACzC,IAAI,CAACqC,mBAAmB,EAAE;UAC5B,CAAC;UACDe,KAAK,EAAGA,KAAK,IAAI;YAAAvF,aAAA,GAAA8C,CAAA;YAAA9C,aAAA,GAAAmC,CAAA;YACf,IACEoD,KAAK,EAAEA,KAAK,EAAEC,MAAM,EAAEY,QAAQ,CAC5B,qCAAqC,CACtC,EACD;cAAApG,aAAA,GAAAkF,CAAA;cAAAlF,aAAA,GAAAmC,CAAA;cACA,IAAI,CAACI,KAAK,CAACmE,OAAO,CAChB,iBAAiB,EACjB,kEAAkE,CACnE;YACH,CAAC,MAAM;cAAA1G,aAAA,GAAAkF,CAAA;cAAAlF,aAAA,GAAAmC,CAAA;cAAA,IAAIoD,KAAK,EAAEA,KAAK,EAAEC,MAAM,EAAEY,QAAQ,CAAC,oBAAoB,CAAC,EAAE;gBAAApG,aAAA,GAAAkF,CAAA;gBAAAlF,aAAA,GAAAmC,CAAA;gBAC/D,IAAI,CAACI,KAAK,CAACmE,OAAO,CAChB,wBAAwB,EACxB,mDAAmD,CACpD;cACH,CAAC,MAAM;gBAAA1G,aAAA,GAAAkF,CAAA;gBAAAlF,aAAA,GAAAmC,CAAA;gBACL,IAAI,CAACI,KAAK,CAACgD,KAAK,CAAC,CAAAvF,aAAA,GAAAkF,CAAA,WAAAK,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAAxF,aAAA,GAAAkF,CAAA,WAAI,gCAAgC,EAAC;cAC3E;YAAA;UACF;SACD,CAAC;MACN;IACF,CAAC;MAAAlF,aAAA,GAAAkF,CAAA;IAAA;EACH;EAEQP,mBAAmBA,CAAA;IAAA3E,aAAA,GAAA8C,CAAA;IAAA9C,aAAA,GAAAmC,CAAA;IACzB,IAAI,CAAC2B,WAAW,CACb4B,GAAG,CAAC,sBAAsB,CAAC,EAC1BC,YAAY,CAACN,SAAS,CAAEiD,OAAO,IAAI;MAAAtI,aAAA,GAAA8C,CAAA;MACnC,MAAMyF,oBAAoB,IAAAvI,aAAA,GAAAmC,CAAA,SAAG,IAAI,CAAC2B,WAAW,CAAC4B,GAAG,CAAC,eAAe,CAAC;MAAC1F,aAAA,GAAAmC,CAAA;MACnE,IAAImG,OAAO,EAAE;QAAAtI,aAAA,GAAAkF,CAAA;QAAAlF,aAAA,GAAAmC,CAAA;QACXoG,oBAAoB,EAAEC,MAAM,EAAE;QAACxI,aAAA,GAAAmC,CAAA;QAC/BoG,oBAAoB,EAAEE,aAAa,CAAC,CAClC5H,UAAU,CAACyD,QAAQ,EACnBzD,UAAU,CAAC6H,GAAG,CAAC,CAAC,CAAC,EACjB7H,UAAU,CAAC8H,OAAO,CAAC,OAAO,CAAC,CAC5B,CAAC;MACJ,CAAC,MAAM;QAAA3I,aAAA,GAAAkF,CAAA;QAAAlF,aAAA,GAAAmC,CAAA;QACLoG,oBAAoB,EAAEK,OAAO,EAAE;QAAC5I,aAAA,GAAAmC,CAAA;QAChCoG,oBAAoB,EAAEM,eAAe,EAAE;QAAC7I,aAAA,GAAAmC,CAAA;QACxCoG,oBAAoB,EAAEO,QAAQ,CAAC,EAAE,CAAC;MACpC;MAAC9I,aAAA,GAAAmC,CAAA;MACDoG,oBAAoB,EAAEQ,sBAAsB,CAAC;QAAEC,SAAS,EAAE;MAAK,CAAE,CAAC;MAAChJ,aAAA,GAAAmC,CAAA;MACnE,IAAI,CAAC2B,WAAW,CAACiF,sBAAsB,EAAE;IAC3C,CAAC,CAAC;IAAC/I,aAAA,GAAAmC,CAAA;IAEL,IAAI,CAAC2B,WAAW,CAACmF,aAAa,CAAC5D,SAAS,CAAC,MAAK;MAAArF,aAAA,GAAA8C,CAAA;MAAA9C,aAAA,GAAAmC,CAAA;MAC5C,IAAI,CAAC+G,iBAAiB,EAAE;IAC1B,CAAC,CAAC;IAAClJ,aAAA,GAAAmC,CAAA;IAEH,IAAI,CAAC2B,WAAW,CAAC6B,YAAY,CAACN,SAAS,CAAC,MAAK;MAAArF,aAAA,GAAA8C,CAAA;MAAA9C,aAAA,GAAAmC,CAAA;MAC3C,IAAI,CAACgH,YAAY,EAAE;IACrB,CAAC,CAAC;EACJ;EAEAC,oBAAoBA,CAACC,OAAgB;IAAArJ,aAAA,GAAA8C,CAAA;IAAA9C,aAAA,GAAAmC,CAAA;IACnC,IAAI,CAAC0B,WAAW,GAAGwF,OAAO;IAACrJ,aAAA,GAAAmC,CAAA;IAC3B,IAAI,CAAC+G,iBAAiB,EAAE;EAC1B;EAEAI,kBAAkBA,CAAA;IAAAtJ,aAAA,GAAA8C,CAAA;IAAA9C,aAAA,GAAAmC,CAAA;IAChB,IAAI,CAACI,KAAK,CAACgF,OAAO,CAAC,4CAA4C,CAAC;EAClE;EAEQ2B,iBAAiBA,CAAA;IAAAlJ,aAAA,GAAA8C,CAAA;IACvB,MAAMkB,oBAAoB,IAAAhE,aAAA,GAAAmC,CAAA,SAAG,IAAI,CAAC2B,WAAW,CAAC4B,GAAG,CAC/C,sBAAsB,CACvB,EAAExB,KAAK;IACR,MAAMqF,kBAAkB,IAAAvJ,aAAA,GAAAmC,CAAA,SAAG,CAAAnC,aAAA,GAAAkF,CAAA,YAAClB,oBAAoB,MAAAhE,aAAA,GAAAkF,CAAA,WAAI,IAAI,CAACpB,WAAW,CAACkE,KAAK;IAAChI,aAAA,GAAAmC,CAAA;IAC3E,IAAI,CAACa,kBAAkB,CAACwG,IAAI,CAAC,CAAAxJ,aAAA,GAAAkF,CAAA,WAAAqE,kBAAkB,MAAAvJ,aAAA,GAAAkF,CAAA,WAAI,IAAI,CAACrB,WAAW,EAAC;EACtE;EAEA4F,QAAQA,CAAA;IAAAzJ,aAAA,GAAA8C,CAAA;IAAA9C,aAAA,GAAAmC,CAAA;IACN,IAAI,IAAI,CAACuH,gBAAgB,EAAE;MAAA1J,aAAA,GAAAkF,CAAA;MAAAlF,aAAA,GAAAmC,CAAA;MACzB,OAAO,IAAI,CAACuH,gBAAgB,CAACC,QAAQ,EAAE;IACzC,CAAC;MAAA3J,aAAA,GAAAkF,CAAA;IAAA;IAAAlF,aAAA,GAAAmC,CAAA;IACD,OAAOH,EAAE,CAAC,IAAI,CAAC;EACjB;EAEQ0C,cAAcA,CAAA;IAAA1E,aAAA,GAAA8C,CAAA;IACpB,MAAMkB,oBAAoB,IAAAhE,aAAA,GAAAmC,CAAA,SAAG,CAAAnC,aAAA,GAAAkF,CAAA,eAAI,CAACJ,MAAM,CAACd,oBAAoB,MAAAhE,aAAA,GAAAkF,CAAA,WAAI,KAAK;IACtE,MAAMjB,aAAa,IAAAjE,aAAA,GAAAmC,CAAA,SAAG,CAAAnC,aAAA,GAAAkF,CAAA,eAAI,CAACJ,MAAM,CAACb,aAAa,MAAAjE,aAAA,GAAAkF,CAAA,WAAI,EAAE;IAErD,MAAMqD,oBAAoB,IAAAvI,aAAA,GAAAmC,CAAA,SAAG,IAAI,CAAC2B,WAAW,CAAC4B,GAAG,CAAC,eAAe,CAAC;IAAC1F,aAAA,GAAAmC,CAAA;IACnE,IAAI6B,oBAAoB,EAAE;MAAAhE,aAAA,GAAAkF,CAAA;MAAAlF,aAAA,GAAAmC,CAAA;MACxBoG,oBAAoB,EAAEC,MAAM,EAAE;MAACxI,aAAA,GAAAmC,CAAA;MAC/BoG,oBAAoB,EAAEE,aAAa,CAAC,CAClC5H,UAAU,CAACyD,QAAQ,EACnBzD,UAAU,CAAC6H,GAAG,CAAC,CAAC,CAAC,EACjB7H,UAAU,CAAC8H,OAAO,CAAC,OAAO,CAAC,CAC5B,CAAC;IACJ,CAAC,MAAM;MAAA3I,aAAA,GAAAkF,CAAA;MAAAlF,aAAA,GAAAmC,CAAA;MACLoG,oBAAoB,EAAEK,OAAO,EAAE;MAAC5I,aAAA,GAAAmC,CAAA;MAChCoG,oBAAoB,EAAEM,eAAe,EAAE;IACzC;IAAC7I,aAAA,GAAAmC,CAAA;IAED,IAAI,CAAC2B,WAAW,CAACkD,UAAU,CACzB;MACEhD,oBAAoB;MACpBC;KACD,EACD;MAAE+E,SAAS,EAAE;IAAK,CAAE,CACrB;IAAChJ,aAAA,GAAAmC,CAAA;IAEF,IAAI,IAAI,CAACc,YAAY,EAAE;MAAAjD,aAAA,GAAAkF,CAAA;MAAAlF,aAAA,GAAAmC,CAAA;MACrB,IAAI,CAAC2B,WAAW,CAAC4B,GAAG,CAAC,sBAAsB,CAAC,EAAEkD,OAAO,EAAE;IACzD,CAAC;MAAA5I,aAAA,GAAAkF,CAAA;IAAA;IAAAlF,aAAA,GAAAmC,CAAA;IAEDoG,oBAAoB,EAAEQ,sBAAsB,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAE,CAAC;IAAChJ,aAAA,GAAAmC,CAAA;IACnE,IAAI,CAAC2B,WAAW,CAACiF,sBAAsB,EAAE;EAC3C;EAEAtE,eAAeA,CAAA;IAAAzE,aAAA,GAAA8C,CAAA;IAAA9C,aAAA,GAAAmC,CAAA;IACb,IAAI,IAAI,CAAC2C,MAAM,CAAC8E,SAAS,EAAE;MAAA5J,aAAA,GAAAkF,CAAA;MACzB,MAAM0E,SAAS,IAAA5J,aAAA,GAAAmC,CAAA,SAAG,IAAI0H,IAAI,CAAC,IAAI,CAAC/E,MAAM,CAAC8E,SAAS,CAAC;MAAC5J,aAAA,GAAAmC,CAAA;MAClD,IAAI,CAACe,IAAI,GAAG0G,SAAS,CAACE,WAAW,EAAE;MAAC9J,aAAA,GAAAmC,CAAA;MACpC,IAAI,CAACgB,KAAK,GAAG,IAAI,CAAC4G,YAAY,CAACH,SAAS,CAACI,QAAQ,EAAE,CAAC;IACtD,CAAC;MAAAhK,aAAA,GAAAkF,CAAA;IAAA;EACH;EAEA6E,YAAYA,CAACE,UAAkB;IAAAjK,aAAA,GAAA8C,CAAA;IAC7B,MAAMoH,UAAU,IAAAlK,aAAA,GAAAmC,CAAA,SAAG,CACjB,OAAO,EACP,SAAS,EACT,OAAO,EACP,OAAO,EACP,MAAM,EACN,OAAO,EACP,OAAO,EACP,QAAQ,EACR,YAAY,EACZ,SAAS,EACT,WAAW,EACX,WAAW,CACZ;IAACnC,aAAA,GAAAmC,CAAA;IACF,OAAO+H,UAAU,CAACD,UAAU,CAAC;EAC/B;EAEAE,UAAUA,CAACC,IAA+B;IAAApK,aAAA,GAAA8C,CAAA;IAAA9C,aAAA,GAAAmC,CAAA;IACxC,IAAI,CAACiI,IAAI,EAAE;MAAApK,aAAA,GAAAkF,CAAA;MAAAlF,aAAA,GAAAmC,CAAA;MAAA,OAAO,KAAK;IAAA,CAAC;MAAAnC,aAAA,GAAAkF,CAAA;IAAA;IAAAlF,aAAA,GAAAmC,CAAA;IACxB,OAAO,CAAAnC,aAAA,GAAAkF,CAAA,eAAI,CAACtC,QAAQ,CAACyH,SAAS,CAACD,IAAI,EAAE,YAAY,CAAC,MAAApK,aAAA,GAAAkF,CAAA,WAAI,KAAK;EAC7D;EAEAoF,cAAcA,CAACpG,KAAyB;IAAAlE,aAAA,GAAA8C,CAAA;IAAA9C,aAAA,GAAAmC,CAAA;IACtC,IAAI+B,KAAK,KAAKqG,SAAS,EAAE;MAAAvK,aAAA,GAAAkF,CAAA;MAAAlF,aAAA,GAAAmC,CAAA;MAAA,OAAO,KAAK;IAAA,CAAC;MAAAnC,aAAA,GAAAkF,CAAA;IAAA;IAAAlF,aAAA,GAAAmC,CAAA;IACtC,OAAO,IAAIqI,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;KACX,CAAC,CAACC,MAAM,CAAC1G,KAAK,CAAC;EAClB;EAEAM,mBAAmBA,CAAA;IAAAxE,aAAA,GAAA8C,CAAA;IAAA9C,aAAA,GAAAmC,CAAA;IACjB,IAAI,IAAI,CAAC2C,MAAM,CAACC,kBAAkB,EAAEC,QAAQ,EAAEC,EAAE,EAAE;MAAAjF,aAAA,GAAAkF,CAAA;MAAAlF,aAAA,GAAAmC,CAAA;MAChD,IAAI,CAACG,eAAe,CACjBuI,cAAc,CAAC,IAAI,CAAC/F,MAAM,CAACC,kBAAkB,CAACC,QAAQ,CAACC,EAAE,CAAC,CAC1DI,SAAS,CAAC;QACTC,IAAI,EAAGwF,OAA+B,IAAI;UAAA9K,aAAA,GAAA8C,CAAA;UAAA9C,aAAA,GAAAmC,CAAA;UACxC,IAAI2I,OAAO,EAAE;YAAA9K,aAAA,GAAAkF,CAAA;YAAAlF,aAAA,GAAAmC,CAAA;YACX,IAAI,CAACiB,eAAe,GAAG0H,OAAO;YAAC9K,aAAA,GAAAmC,CAAA;YAC/B,IAAI,CAACiB,eAAe,CAAC2H,UAAU,GAC7B,CAAC,CAAA/K,aAAA,GAAAkF,CAAA,eAAI,CAAC9B,eAAe,CAAC4H,YAAY,MAAAhL,aAAA,GAAAkF,CAAA,WAAI,CAAC,MACtC,CAAAlF,aAAA,GAAAkF,CAAA,eAAI,CAAC9B,eAAe,CAAC6H,mBAAmB,MAAAjL,aAAA,GAAAkF,CAAA,WAAI,CAAC,EAAC,IAC9C,CAAAlF,aAAA,GAAAkF,CAAA,eAAI,CAAC9B,eAAe,CAAC8H,oBAAoB,MAAAlL,aAAA,GAAAkF,CAAA,WAAI,CAAC,EAAC;UACpD,CAAC;YAAAlF,aAAA,GAAAkF,CAAA;UAAA;UAAAlF,aAAA,GAAAmC,CAAA;UACD,IAAI,CAACgJ,YAAY,EAAE;QACrB,CAAC;QACD5F,KAAK,EAAGA,KAAK,IAAI;UAAAvF,aAAA,GAAA8C,CAAA;UAAA9C,aAAA,GAAAmC,CAAA;UACf,IAAI,CAACI,KAAK,CAACgD,KAAK,CAAC,CAAAvF,aAAA,GAAAkF,CAAA,WAAAK,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAAxF,aAAA,GAAAkF,CAAA,WAAI,wCAAwC,EAAC;UAAClF,aAAA,GAAAmC,CAAA;UAClF,IAAI,CAACgJ,YAAY,EAAE;QACrB;OACD,CAAC;IACN,CAAC,MAAM;MAAAnL,aAAA,GAAAkF,CAAA;MAAAlF,aAAA,GAAAmC,CAAA;MACL,IAAI,CAACgJ,YAAY,EAAE;IACrB;EACF;EAEQhC,YAAYA,CAAA;IAAAnJ,aAAA,GAAA8C,CAAA;IAAA9C,aAAA,GAAAmC,CAAA;IAClB,IAAI,CAAAnC,aAAA,GAAAkF,CAAA,eAAI,CAACJ,MAAM,MAAA9E,aAAA,GAAAkF,CAAA,WAAI,IAAI,CAAC9B,eAAe,GAAE;MAAApD,aAAA,GAAAkF,CAAA;MACvC,MAAMkG,aAAa,IAAApL,aAAA,GAAAmC,CAAA,SAAG;QACpB,GAAG,IAAI,CAAC2C,MAAM;QACduG,cAAc,EAAE,IAAI,CAACjI,eAAe,CAACiI,cAAc;QACnDC,YAAY,EAAEC,MAAM,CAAC,IAAI,CAACnI,eAAe,CAACkI,YAAY,EAAEpI,IAAI,CAAC;QAC7DsI,oBAAoB,EAAE,IAAI,CAACpI,eAAe,CAACqI,YAAY;QACvDC,iBAAiB,EAAE,IAAI,CAACtI,eAAe,CAACwG,SAAS;QACjD+B,eAAe,EAAE,IAAI,CAACvI,eAAe,CAACwI,OAAO;QAC7CC,kBAAkB,EAAE,IAAI,CAACzI,eAAe,CAAC2H,UAAU;QACnDe,oBAAoB,EAAE,IAAI,CAAC1I,eAAe,CAAC4H,YAAY;QACvDe,2BAA2B,EAAE,IAAI,CAAC3I,eAAe,CAAC6H,mBAAmB;QACrEe,4BAA4B,EAAE,IAAI,CAAC5I,eAAe,CAAC8H,oBAAoB;QACvEe,kBAAkB,EAAE,IAAI,CAAC7I,eAAe,CAAC0C,QAAQ;QACjDoG,kBAAkB,EAAE,IAAI,CAAC9I,eAAe,CAAC8I,kBAAkB;QAC3DC,eAAe,EAAE,IAAI,CAAC/I,eAAe,CAAC+I,eAAe;QACrDC,eAAe,EAAE,IAAI,CAAChJ,eAAe,CAACgJ,eAAe;QACrDC,kBAAkB,EAAE,IAAI,CAACjJ,eAAe,CAACkJ,cAAc;QACvDC,aAAa,EAAE,IAAI,CAACnJ,eAAe,CAACoJ,SAAS;QAC7CC,kBAAkB,EAAE,IAAI,CAACrJ,eAAe,CAACsJ,QAAQ;QACjDC,uBAAuB,EAAE,IAAI,CAACvJ,eAAe,CAACwJ,aAAa;QAC3DC,qBAAqB,EAAE,IAAI,CAACzJ,eAAe,CAAC0J,eAAe;QAC3DC,kBAAkB,EAAE,IAAI,CAAC3J,eAAe,CAAC2J,kBAAkB;QAC3DC,kBAAkB,EAAE,IAAI,CAAC5J,eAAe,CAAC4J,kBAAkB;QAC3DC,kBAAkB,EAAE,IAAI,CAAC7J,eAAe,CAAC6J,kBAAkB;QAC3DlC,UAAU,EAAE,CAAA/K,aAAA,GAAAkF,CAAA,eAAI,CAACJ,MAAM,CAACiG,UAAU,MAAA/K,aAAA,GAAAkF,CAAA,WAAI,IAAI,CAAC9B,eAAe,CAAC2H,UAAU;QACrEmC,SAAS,EAAE,IAAI,CAAC7J,SAAS;QACzB8J,YAAY,EAAE,IAAI,CAAC7J,cAAc;QACjC8J,mBAAmB,EAAE,IAAI,CAAC7J,kBAAkB;QAC5CS,oBAAoB,EAAE,IAAI,CAACF,WAAW,CAAC4B,GAAG,CAAC,sBAAsB,CAAC,EAC9DxB,KAAK;QACTD,aAAa,EAAE,IAAI,CAACH,WAAW,CAAC4B,GAAG,CAAC,sBAAsB,CAAC,EAAExB,KAAK,IAAAlE,aAAA,GAAAkF,CAAA,WAC9D,IAAI,CAACpB,WAAW,CAAC4B,GAAG,CAAC,eAAe,CAAC,EAAExB,KAAK,KAAAlE,aAAA,GAAAkF,CAAA,WAC5C,CAAC;OACN;MAAClF,aAAA,GAAAmC,CAAA;MAEF,IAAI,CAAC2C,MAAM,GAAGsG,aAAa;MAACpL,aAAA,GAAAmC,CAAA;MAC5B,IAAI,CAACY,YAAY,CAACyG,IAAI,CAAC4B,aAAa,CAAC;IACvC,CAAC;MAAApL,aAAA,GAAAkF,CAAA;IAAA;EACH;EAEAiG,YAAYA,CAAA;IAAAnL,aAAA,GAAA8C,CAAA;IAAA9C,aAAA,GAAAmC,CAAA;IACV,IAAI,CAAC,IAAI,CAAC2C,MAAM,EAAEuI,oBAAoB,EAAE;MAAArN,aAAA,GAAAkF,CAAA;MAAAlF,aAAA,GAAAmC,CAAA;MACtC,IAAI,CAACgH,YAAY,EAAE;MAACnJ,aAAA,GAAAmC,CAAA;MACpB;IACF,CAAC;MAAAnC,aAAA,GAAAkF,CAAA;IAAA;IAAAlF,aAAA,GAAAmC,CAAA;IAED,IAAI,CAACK,oBAAoB,CACtB8K,yBAAyB,CAAC,IAAI,CAACxI,MAAM,CAACuI,oBAAoB,CAAC,CAC3DhI,SAAS,CAAC;MACTC,IAAI,EAAGiI,OAAwB,IAAI;QAAAvN,aAAA,GAAA8C,CAAA;QACjC,MAAM0K,kBAAkB,IAAAxN,aAAA,GAAAmC,CAAA,SAAGoL,OAAO,CAC/BpH,MAAM,CAAEsH,CAAC,IAAK;UAAAzN,aAAA,GAAA8C,CAAA;UAAA9C,aAAA,GAAAmC,CAAA;UAAA,OAAAsL,CAAC,CAACxI,EAAE;QAAF,CAAE,CAAC,CACnBlD,GAAG,CAAE0L,CAAC,IAAK;UAAAzN,aAAA,GAAA8C,CAAA;UAAA9C,aAAA,GAAAmC,CAAA;UAAA,WAAI,CAACM,cAAc,CAACiL,oBAAoB,CAACD,CAAC,CAACxI,EAAE,CAAC;QAAD,CAAC,CAAC;QAACjF,aAAA,GAAAmC,CAAA;QAE9D,IAAIqL,kBAAkB,CAAC/G,MAAM,KAAK,CAAC,EAAE;UAAAzG,aAAA,GAAAkF,CAAA;UAAAlF,aAAA,GAAAmC,CAAA;UACnC,IAAI,CAACkB,SAAS,GAAG,CAAC;UAACrD,aAAA,GAAAmC,CAAA;UACnB,IAAI,IAAI,CAACiB,eAAe,EAAE;YAAApD,aAAA,GAAAkF,CAAA;YAAAlF,aAAA,GAAAmC,CAAA;YACxB,IAAI,CAACmB,cAAc,GAAG,CAAAtD,aAAA,GAAAkF,CAAA,eAAI,CAAC9B,eAAe,CAAC2H,UAAU,MAAA/K,aAAA,GAAAkF,CAAA,WAAI,CAAC;YAAClF,aAAA,GAAAmC,CAAA;YAC3D,IAAI,CAACoB,kBAAkB,GAAG,CAAC;UAC7B,CAAC;YAAAvD,aAAA,GAAAkF,CAAA;UAAA;UAAAlF,aAAA,GAAAmC,CAAA;UACD,IAAI,CAACgH,YAAY,EAAE;UAACnJ,aAAA,GAAAmC,CAAA;UACpB;QACF,CAAC;UAAAnC,aAAA,GAAAkF,CAAA;QAAA;QAAAlF,aAAA,GAAAmC,CAAA;QAEDL,QAAQ,CAAC0L,kBAAkB,CAAC,CAACnI,SAAS,CAAC;UACrCC,IAAI,EAAGqI,aAA0B,IAAI;YAAA3N,aAAA,GAAA8C,CAAA;YACnC,MAAM8K,WAAW,IAAA5N,aAAA,GAAAmC,CAAA,SAAGwL,aAAa,CAACE,IAAI,EAAE;YACxC,MAAMC,cAAc,IAAA9N,aAAA,GAAAmC,CAAA,SAAGyL,WAAW,CAACG,IAAI,CACrC,CAACC,CAAC,EAAE9I,CAAC,KAAK;cAAAlF,aAAA,GAAA8C,CAAA;cAAA9C,aAAA,GAAAmC,CAAA;cAAA,OAAA6L,CAAC,CAACC,aAAa,GAAG/I,CAAC,CAAC+I,aAAa;YAAb,CAAa,CAC5C;YAACjO,aAAA,GAAAmC,CAAA;YAEF,IAAI,CAACkB,SAAS,GAAGyK,cAAc,CAC5B3H,MAAM,CAAE+H,CAAC,IAAK;cAAAlO,aAAA,GAAA8C,CAAA;cAAA9C,aAAA,GAAAmC,CAAA;cAAA,OAAA+L,CAAC,CAACD,aAAa,GAAG,IAAI,CAACnJ,MAAM,CAACqJ,YAAY;YAAZ,CAAY,CAAC,CACzDC,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAK;cAAAtO,aAAA,GAAA8C,CAAA;cAAA9C,aAAA,GAAAmC,CAAA;cAAA,OAAAkM,GAAG,GAAGC,OAAO,CAACpK,KAAK;YAAL,CAAK,EAAE,CAAC,CAAC;YAAClE,aAAA,GAAAmC,CAAA;YAEpD,IAAI,IAAI,CAACiB,eAAe,EAAE;cAAApD,aAAA,GAAAkF,CAAA;cAAAlF,aAAA,GAAAmC,CAAA;cACxB,IAAI,CAACmB,cAAc,GACjB,CAAC,CAAAtD,aAAA,GAAAkF,CAAA,eAAI,CAAC9B,eAAe,CAAC2H,UAAU,MAAA/K,aAAA,GAAAkF,CAAA,WAAI,CAAC,KAAI,IAAI,CAAC7B,SAAS;cAEzD,MAAMkL,oBAAoB,IAAAvO,aAAA,GAAAmC,CAAA,SACxB,IAAI,CAACkB,SAAS,IAAI,CAAArD,aAAA,GAAAkF,CAAA,eAAI,CAACJ,MAAM,CAACiG,UAAU,MAAA/K,aAAA,GAAAkF,CAAA,WAAI,CAAC,EAAC;cAAClF,aAAA,GAAAmC,CAAA;cAEjD,IAAI,CAACoB,kBAAkB,GACrB,CAAAvD,aAAA,GAAAkF,CAAA,eAAI,CAAC9B,eAAe,CAAC2H,UAAU,MAAA/K,aAAA,GAAAkF,CAAA,WAC/B,IAAI,CAAC9B,eAAe,CAAC2H,UAAU,GAAG,CAAC,KAAA/K,aAAA,GAAAkF,CAAA,WAC/BqJ,oBAAoB,GAAG,IAAI,CAACnL,eAAe,CAAC2H,UAAU,KAAA/K,aAAA,GAAAkF,CAAA,WACtD,CAAC;YACT,CAAC;cAAAlF,aAAA,GAAAkF,CAAA;YAAA;YAAAlF,aAAA,GAAAmC,CAAA;YACD,IAAI,CAACgH,YAAY,EAAE;UACrB,CAAC;UACD5D,KAAK,EAAGA,KAAK,IAAI;YAAAvF,aAAA,GAAA8C,CAAA;YAAA9C,aAAA,GAAAmC,CAAA;YACf,IAAI,CAACI,KAAK,CAACgD,KAAK,CAAC,CAAAvF,aAAA,GAAAkF,CAAA,WAAAK,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAAxF,aAAA,GAAAkF,CAAA,WAAI,2BAA2B,EAAC;YAAClF,aAAA,GAAAmC,CAAA;YACrE,IAAI,CAACgH,YAAY,EAAE;UACrB;SACD,CAAC;MACJ,CAAC;MACD5D,KAAK,EAAGA,KAAK,IAAI;QAAAvF,aAAA,GAAA8C,CAAA;QAAA9C,aAAA,GAAAmC,CAAA;QACf,IAAI,CAACI,KAAK,CAACgD,KAAK,CAAC,CAAAvF,aAAA,GAAAkF,CAAA,WAAAK,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAAxF,aAAA,GAAAkF,CAAA,WAAI,8BAA8B,EAAC;QAAClF,aAAA,GAAAmC,CAAA;QACxE,IAAI,CAACgH,YAAY,EAAE;MACrB;KACD,CAAC;EACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;cA/fC3I;MAAK;;cACLC;MAAM;;cACNA;MAAM;;cACND;MAAK;;cACLE,SAAS;QAAA8N,IAAA,GAACtM,yBAAyB;MAAA;;;;;AALzBE,+BAA+B,GAAAqM,UAAA,EAxB3CnO,SAAS,CAAC;EACToO,QAAQ,EAAE,+BAA+B;EACzCC,QAAA,EAAAC,oBAAyD;EAEzDC,SAAS,EAAE,CAAC1O,QAAQ,CAAC;EACrB2O,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP1N,OAAO,EACPjB,WAAW,EACXQ,mBAAmB,EACnBK,WAAW,EACXE,YAAY,EACZC,QAAQ,EACRE,QAAQ,EACRJ,QAAQ,EACRJ,qBAAqB,EACrBE,eAAe,EACfd,SAAS,EACTgC,yBAAyB,EACzBX,SAAS,EACTR,sBAAsB,EACtBV,WAAW,CACZ;;CACF,CAAC,C,EACW+B,+BAA+B,CAigB3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}