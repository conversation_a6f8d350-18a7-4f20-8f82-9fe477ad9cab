{"ast": null, "code": "import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { ActivatedRoute } from '@angular/router';\nimport { RouterTestingModule } from '@angular/router/testing';\nimport { ContractService } from '@contract-management/services/contract.service';\nimport { ContractorContractService } from '@contract-management/services/contractor-contract.service';\nimport { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { of, throwError } from 'rxjs';\nimport { ContractDetailsPageComponent } from './contract-details-page.component';\ndescribe('ContractDetailsPageComponent', () => {\n  let component;\n  let fixture;\n  let contractService;\n  let contractorContractService;\n  let monthlyReportService;\n  let alertService;\n  const mockContract = {\n    id: 1,\n    contractNumber: 123,\n    contractorId: 1,\n    fullName: 'Test Contractor',\n    contractorIdNumber: 12345,\n    object: 'Test Contract',\n    rup: true,\n    addition: false,\n    cession: false,\n    settled: false,\n    hasCcp: false,\n    selectionModalityName: 'Test Modality',\n    trackingTypeName: 'Test Tracking',\n    contractTypeName: 'Test Type',\n    statusName: 'Active',\n    dependencyName: 'Test Dependency',\n    groupName: 'Test Group',\n    contractorEmail: '<EMAIL>',\n    monthlyPayment: 1000,\n    supervisorFullName: 'Test Supervisor',\n    supervisorIdNumber: '98765',\n    supervisorPosition: 'Test Position'\n  };\n  const mockContractorContract = {\n    id: 1,\n    subscriptionDate: '2024-01-01',\n    contractStartDate: '2024-01-01',\n    contractId: 1,\n    contractorId: 1\n  };\n  const mockMonthlyReport = {\n    id: 1,\n    reportNumber: 1,\n    startDate: '2024-01-01',\n    endDate: '2024-01-31',\n    creationDate: '2024-01-01',\n    contractorContractId: 1\n  };\n  beforeEach(() => {\n    const contractServiceSpy = jasmine.createSpyObj('ContractService', ['getDetailsById']);\n    const contractorContractServiceSpy = jasmine.createSpyObj('ContractorContractService', ['getAllByContractId']);\n    const monthlyReportServiceSpy = jasmine.createSpyObj('MonthlyReportService', ['getByContractorContractId']);\n    const alertServiceSpy = jasmine.createSpyObj('AlertService', ['error']);\n    TestBed.configureTestingModule({\n      imports: [ContractDetailsPageComponent, HttpClientTestingModule, BrowserAnimationsModule, RouterTestingModule],\n      providers: [{\n        provide: ContractService,\n        useValue: contractServiceSpy\n      }, {\n        provide: ContractorContractService,\n        useValue: contractorContractServiceSpy\n      }, {\n        provide: MonthlyReportService,\n        useValue: monthlyReportServiceSpy\n      }, {\n        provide: AlertService,\n        useValue: alertServiceSpy\n      }, {\n        provide: ActivatedRoute,\n        useValue: {\n          snapshot: {\n            paramMap: {\n              get: () => '1'\n            }\n          }\n        }\n      }]\n    });\n    fixture = TestBed.createComponent(ContractDetailsPageComponent);\n    component = fixture.componentInstance;\n    contractService = TestBed.inject(ContractService);\n    contractorContractService = TestBed.inject(ContractorContractService);\n    monthlyReportService = TestBed.inject(MonthlyReportService);\n    alertService = TestBed.inject(AlertService);\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should load contract details on init', () => {\n    contractService.getDetailsById.and.returnValue(of(mockContract));\n    contractorContractService.getAllByContractId.and.returnValue(of([mockContractorContract]));\n    monthlyReportService.getByContractorContractId.and.returnValue(of([mockMonthlyReport]));\n    component.ngOnInit();\n    expect(contractService.getDetailsById).toHaveBeenCalledWith(1);\n    expect(component.contract).toEqual(mockContract);\n    expect(component.isLoading).toBeFalse();\n  });\n  it('should show error when contract ID is not provided', () => {\n    TestBed.resetTestingModule();\n    TestBed.configureTestingModule({\n      imports: [ContractDetailsPageComponent, HttpClientTestingModule, BrowserAnimationsModule, RouterTestingModule],\n      providers: [{\n        provide: ContractService,\n        useValue: contractService\n      }, {\n        provide: ContractorContractService,\n        useValue: contractorContractService\n      }, {\n        provide: MonthlyReportService,\n        useValue: monthlyReportService\n      }, {\n        provide: AlertService,\n        useValue: alertService\n      }, {\n        provide: ActivatedRoute,\n        useValue: {\n          snapshot: {\n            paramMap: {\n              get: () => null\n            }\n          }\n        }\n      }]\n    });\n    fixture = TestBed.createComponent(ContractDetailsPageComponent);\n    component = fixture.componentInstance;\n    component.ngOnInit();\n    expect(alertService.error).toHaveBeenCalledWith('ID del contrato no proporcionado');\n  });\n  it('should handle error when loading contract details', () => {\n    contractService.getDetailsById.and.returnValue(throwError(() => new Error()));\n    component.ngOnInit();\n    expect(alertService.error).toHaveBeenCalledWith('Error al cargar los detalles del contrato');\n    expect(component.isLoading).toBeFalse();\n  });\n  it('should load contractor contract after loading contract details', () => {\n    contractService.getDetailsById.and.returnValue(of(mockContract));\n    contractorContractService.getAllByContractId.and.returnValue(of([mockContractorContract]));\n    monthlyReportService.getByContractorContractId.and.returnValue(of([mockMonthlyReport]));\n    component.ngOnInit();\n    expect(contractorContractService.getAllByContractId).toHaveBeenCalledWith(1);\n    expect(component.contractorContractId).toBe(1);\n  });\n  it('should show error when no contractor contract is found', () => {\n    contractService.getDetailsById.and.returnValue(of(mockContract));\n    contractorContractService.getAllByContractId.and.returnValue(of([]));\n    component.ngOnInit();\n    expect(alertService.error).toHaveBeenCalledWith('No se encontró un contrato de contratista para este contrato');\n    expect(component.isLoading).toBeFalse();\n  });\n  it('should handle error when loading contractor contract', () => {\n    contractService.getDetailsById.and.returnValue(of(mockContract));\n    contractorContractService.getAllByContractId.and.returnValue(throwError(() => new Error()));\n    component.ngOnInit();\n    expect(alertService.error).toHaveBeenCalledWith('Error al cargar el contrato del contratista');\n    expect(component.isLoading).toBeFalse();\n  });\n  it('should load monthly reports after loading contractor contract', () => {\n    contractService.getDetailsById.and.returnValue(of(mockContract));\n    contractorContractService.getAllByContractId.and.returnValue(of([mockContractorContract]));\n    monthlyReportService.getByContractorContractId.and.returnValue(of([mockMonthlyReport]));\n    component.ngOnInit();\n    expect(monthlyReportService.getByContractorContractId).toHaveBeenCalledWith(1);\n    expect(component.monthlyReports).toEqual([mockMonthlyReport]);\n    expect(component.isLoading).toBeFalse();\n  });\n  it('should handle error when loading monthly reports', () => {\n    contractService.getDetailsById.and.returnValue(of(mockContract));\n    contractorContractService.getAllByContractId.and.returnValue(of([mockContractorContract]));\n    monthlyReportService.getByContractorContractId.and.returnValue(throwError(() => new Error()));\n    component.ngOnInit();\n    expect(alertService.error).toHaveBeenCalledWith('Error al cargar informes mensuales');\n    expect(component.isLoading).toBeFalse();\n  });\n  it('should show error when trying to load monthly reports without contractor contract ID', () => {\n    component.loadMonthlyReportsAndPayments();\n    expect(alertService.error).toHaveBeenCalledWith('ID de contrato de contratista no disponible');\n    expect(component.isLoading).toBeFalse();\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "TestBed", "BrowserAnimationsModule", "ActivatedRoute", "RouterTestingModule", "ContractService", "ContractorContractService", "MonthlyReportService", "AlertService", "of", "throwError", "ContractDetailsPageComponent", "describe", "component", "fixture", "contractService", "contractorContractService", "monthlyReportService", "alertService", "mockContract", "id", "contractNumber", "contractorId", "fullName", "contractorIdNumber", "object", "rup", "addition", "cession", "settled", "hasCcp", "selectionModalityName", "trackingTypeName", "contractTypeName", "statusName", "dependencyName", "groupName", "contractorEmail", "monthlyPayment", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supervisorIdNumber", "supervisorPosition", "mockContractorContract", "subscriptionDate", "contractStartDate", "contractId", "mockMonthlyReport", "reportNumber", "startDate", "endDate", "creationDate", "contractorContractId", "beforeEach", "contractServiceSpy", "jasmine", "createSpyObj", "contractorContractServiceSpy", "monthlyReportServiceSpy", "alertServiceSpy", "configureTestingModule", "imports", "providers", "provide", "useValue", "snapshot", "paramMap", "get", "createComponent", "componentInstance", "inject", "it", "expect", "toBeTruthy", "getDetailsById", "and", "returnValue", "getAllByContractId", "getByContractorContractId", "ngOnInit", "toHaveBeenCalledWith", "contract", "toEqual", "isLoading", "toBeFalse", "resetTestingModule", "error", "Error", "toBe", "monthlyReports", "loadMonthlyReportsAndPayments"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\pages\\contract-details-page\\contract-details-page.component.spec.ts"], "sourcesContent": ["import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { ComponentFixture, TestBed } from '@angular/core/testing';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { ActivatedRoute } from '@angular/router';\nimport { RouterTestingModule } from '@angular/router/testing';\nimport { ContractDetails } from '@contract-management/models/contract-details.model';\nimport { ContractorContract } from '@contract-management/models/contractor-contract.model';\nimport { ContractService } from '@contract-management/services/contract.service';\nimport { ContractorContractService } from '@contract-management/services/contractor-contract.service';\nimport { MonthlyReport } from '@contractor-dashboard/models/monthly-report.model';\nimport { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { of, throwError } from 'rxjs';\nimport { ContractDetailsPageComponent } from './contract-details-page.component';\n\ndescribe('ContractDetailsPageComponent', () => {\n  let component: ContractDetailsPageComponent;\n  let fixture: ComponentFixture<ContractDetailsPageComponent>;\n  let contractService: jasmine.SpyObj<ContractService>;\n  let contractorContractService: jasmine.SpyObj<ContractorContractService>;\n  let monthlyReportService: jasmine.SpyObj<MonthlyReportService>;\n  let alertService: jasmine.SpyObj<AlertService>;\n\n  const mockContract: ContractDetails = {\n    id: 1,\n    contractNumber: 123,\n    contractorId: 1,\n    fullName: 'Test Contractor',\n    contractorIdNumber: 12345,\n    object: 'Test Contract',\n    rup: true,\n    addition: false,\n    cession: false,\n    settled: false,\n    hasCcp: false,\n    selectionModalityName: 'Test Modality',\n    trackingTypeName: 'Test Tracking',\n    contractTypeName: 'Test Type',\n    statusName: 'Active',\n    dependencyName: 'Test Dependency',\n    groupName: 'Test Group',\n    contractorEmail: '<EMAIL>',\n    monthlyPayment: 1000,\n    supervisorFullName: 'Test Supervisor',\n    supervisorIdNumber: '98765',\n    supervisorPosition: 'Test Position',\n  };\n\n  const mockContractorContract: ContractorContract = {\n    id: 1,\n    subscriptionDate: '2024-01-01',\n    contractStartDate: '2024-01-01',\n    contractId: 1,\n    contractorId: 1,\n  };\n\n  const mockMonthlyReport: MonthlyReport = {\n    id: 1,\n    reportNumber: 1,\n    startDate: '2024-01-01',\n    endDate: '2024-01-31',\n    creationDate: '2024-01-01',\n    contractorContractId: 1,\n  };\n\n  beforeEach(() => {\n    const contractServiceSpy = jasmine.createSpyObj('ContractService', [\n      'getDetailsById',\n    ]);\n    const contractorContractServiceSpy = jasmine.createSpyObj(\n      'ContractorContractService',\n      ['getAllByContractId'],\n    );\n    const monthlyReportServiceSpy = jasmine.createSpyObj(\n      'MonthlyReportService',\n      ['getByContractorContractId'],\n    );\n    const alertServiceSpy = jasmine.createSpyObj('AlertService', ['error']);\n\n    TestBed.configureTestingModule({\n      imports: [\n        ContractDetailsPageComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n        RouterTestingModule,\n      ],\n      providers: [\n        { provide: ContractService, useValue: contractServiceSpy },\n        {\n          provide: ContractorContractService,\n          useValue: contractorContractServiceSpy,\n        },\n        { provide: MonthlyReportService, useValue: monthlyReportServiceSpy },\n        { provide: AlertService, useValue: alertServiceSpy },\n        {\n          provide: ActivatedRoute,\n          useValue: {\n            snapshot: {\n              paramMap: {\n                get: () => '1',\n              },\n            },\n          },\n        },\n      ],\n    });\n\n    fixture = TestBed.createComponent(ContractDetailsPageComponent);\n    component = fixture.componentInstance;\n    contractService = TestBed.inject(\n      ContractService,\n    ) as jasmine.SpyObj<ContractService>;\n    contractorContractService = TestBed.inject(\n      ContractorContractService,\n    ) as jasmine.SpyObj<ContractorContractService>;\n    monthlyReportService = TestBed.inject(\n      MonthlyReportService,\n    ) as jasmine.SpyObj<MonthlyReportService>;\n    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should load contract details on init', () => {\n    contractService.getDetailsById.and.returnValue(of(mockContract));\n    contractorContractService.getAllByContractId.and.returnValue(\n      of([mockContractorContract]),\n    );\n    monthlyReportService.getByContractorContractId.and.returnValue(\n      of([mockMonthlyReport]),\n    );\n\n    component.ngOnInit();\n\n    expect(contractService.getDetailsById).toHaveBeenCalledWith(1);\n    expect(component.contract).toEqual(mockContract);\n    expect(component.isLoading).toBeFalse();\n  });\n\n  it('should show error when contract ID is not provided', () => {\n    TestBed.resetTestingModule();\n    TestBed.configureTestingModule({\n      imports: [\n        ContractDetailsPageComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n        RouterTestingModule,\n      ],\n      providers: [\n        { provide: ContractService, useValue: contractService },\n        {\n          provide: ContractorContractService,\n          useValue: contractorContractService,\n        },\n        { provide: MonthlyReportService, useValue: monthlyReportService },\n        { provide: AlertService, useValue: alertService },\n        {\n          provide: ActivatedRoute,\n          useValue: {\n            snapshot: {\n              paramMap: {\n                get: () => null,\n              },\n            },\n          },\n        },\n      ],\n    });\n\n    fixture = TestBed.createComponent(ContractDetailsPageComponent);\n    component = fixture.componentInstance;\n    component.ngOnInit();\n\n    expect(alertService.error).toHaveBeenCalledWith(\n      'ID del contrato no proporcionado',\n    );\n  });\n\n  it('should handle error when loading contract details', () => {\n    contractService.getDetailsById.and.returnValue(\n      throwError(() => new Error()),\n    );\n\n    component.ngOnInit();\n\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al cargar los detalles del contrato',\n    );\n    expect(component.isLoading).toBeFalse();\n  });\n\n  it('should load contractor contract after loading contract details', () => {\n    contractService.getDetailsById.and.returnValue(of(mockContract));\n    contractorContractService.getAllByContractId.and.returnValue(\n      of([mockContractorContract]),\n    );\n    monthlyReportService.getByContractorContractId.and.returnValue(\n      of([mockMonthlyReport]),\n    );\n\n    component.ngOnInit();\n\n    expect(contractorContractService.getAllByContractId).toHaveBeenCalledWith(\n      1,\n    );\n    expect(component.contractorContractId).toBe(1);\n  });\n\n  it('should show error when no contractor contract is found', () => {\n    contractService.getDetailsById.and.returnValue(of(mockContract));\n    contractorContractService.getAllByContractId.and.returnValue(of([]));\n\n    component.ngOnInit();\n\n    expect(alertService.error).toHaveBeenCalledWith(\n      'No se encontró un contrato de contratista para este contrato',\n    );\n    expect(component.isLoading).toBeFalse();\n  });\n\n  it('should handle error when loading contractor contract', () => {\n    contractService.getDetailsById.and.returnValue(of(mockContract));\n    contractorContractService.getAllByContractId.and.returnValue(\n      throwError(() => new Error()),\n    );\n\n    component.ngOnInit();\n\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al cargar el contrato del contratista',\n    );\n    expect(component.isLoading).toBeFalse();\n  });\n\n  it('should load monthly reports after loading contractor contract', () => {\n    contractService.getDetailsById.and.returnValue(of(mockContract));\n    contractorContractService.getAllByContractId.and.returnValue(\n      of([mockContractorContract]),\n    );\n    monthlyReportService.getByContractorContractId.and.returnValue(\n      of([mockMonthlyReport]),\n    );\n\n    component.ngOnInit();\n\n    expect(monthlyReportService.getByContractorContractId).toHaveBeenCalledWith(\n      1,\n    );\n    expect(component.monthlyReports).toEqual([mockMonthlyReport]);\n    expect(component.isLoading).toBeFalse();\n  });\n\n  it('should handle error when loading monthly reports', () => {\n    contractService.getDetailsById.and.returnValue(of(mockContract));\n    contractorContractService.getAllByContractId.and.returnValue(\n      of([mockContractorContract]),\n    );\n    monthlyReportService.getByContractorContractId.and.returnValue(\n      throwError(() => new Error()),\n    );\n\n    component.ngOnInit();\n\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al cargar informes mensuales',\n    );\n    expect(component.isLoading).toBeFalse();\n  });\n\n  it('should show error when trying to load monthly reports without contractor contract ID', () => {\n    component.loadMonthlyReportsAndPayments();\n\n    expect(alertService.error).toHaveBeenCalledWith(\n      'ID de contrato de contratista no disponible',\n    );\n    expect(component.isLoading).toBeFalse();\n  });\n});"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,8BAA8B;AACtE,SAA2BC,OAAO,QAAQ,uBAAuB;AACjE,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,mBAAmB,QAAQ,yBAAyB;AAG7D,SAASC,eAAe,QAAQ,gDAAgD;AAChF,SAASC,yBAAyB,QAAQ,2DAA2D;AAErG,SAASC,oBAAoB,QAAQ,uDAAuD;AAC5F,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,EAAE,EAAEC,UAAU,QAAQ,MAAM;AACrC,SAASC,4BAA4B,QAAQ,mCAAmC;AAEhFC,QAAQ,CAAC,8BAA8B,EAAE,MAAK;EAC5C,IAAIC,SAAuC;EAC3C,IAAIC,OAAuD;EAC3D,IAAIC,eAAgD;EACpD,IAAIC,yBAAoE;EACxE,IAAIC,oBAA0D;EAC9D,IAAIC,YAA0C;EAE9C,MAAMC,YAAY,GAAoB;IACpCC,EAAE,EAAE,CAAC;IACLC,cAAc,EAAE,GAAG;IACnBC,YAAY,EAAE,CAAC;IACfC,QAAQ,EAAE,iBAAiB;IAC3BC,kBAAkB,EAAE,KAAK;IACzBC,MAAM,EAAE,eAAe;IACvBC,GAAG,EAAE,IAAI;IACTC,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE,KAAK;IACbC,qBAAqB,EAAE,eAAe;IACtCC,gBAAgB,EAAE,eAAe;IACjCC,gBAAgB,EAAE,WAAW;IAC7BC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,iBAAiB;IACjCC,SAAS,EAAE,YAAY;IACvBC,eAAe,EAAE,kBAAkB;IACnCC,cAAc,EAAE,IAAI;IACpBC,kBAAkB,EAAE,iBAAiB;IACrCC,kBAAkB,EAAE,OAAO;IAC3BC,kBAAkB,EAAE;GACrB;EAED,MAAMC,sBAAsB,GAAuB;IACjDtB,EAAE,EAAE,CAAC;IACLuB,gBAAgB,EAAE,YAAY;IAC9BC,iBAAiB,EAAE,YAAY;IAC/BC,UAAU,EAAE,CAAC;IACbvB,YAAY,EAAE;GACf;EAED,MAAMwB,iBAAiB,GAAkB;IACvC1B,EAAE,EAAE,CAAC;IACL2B,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,YAAY;IACrBC,YAAY,EAAE,YAAY;IAC1BC,oBAAoB,EAAE;GACvB;EAEDC,UAAU,CAAC,MAAK;IACd,MAAMC,kBAAkB,GAAGC,OAAO,CAACC,YAAY,CAAC,iBAAiB,EAAE,CACjE,gBAAgB,CACjB,CAAC;IACF,MAAMC,4BAA4B,GAAGF,OAAO,CAACC,YAAY,CACvD,2BAA2B,EAC3B,CAAC,oBAAoB,CAAC,CACvB;IACD,MAAME,uBAAuB,GAAGH,OAAO,CAACC,YAAY,CAClD,sBAAsB,EACtB,CAAC,2BAA2B,CAAC,CAC9B;IACD,MAAMG,eAAe,GAAGJ,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC;IAEvEtD,OAAO,CAAC0D,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPjD,4BAA4B,EAC5BX,uBAAuB,EACvBE,uBAAuB,EACvBE,mBAAmB,CACpB;MACDyD,SAAS,EAAE,CACT;QAAEC,OAAO,EAAEzD,eAAe;QAAE0D,QAAQ,EAAEV;MAAkB,CAAE,EAC1D;QACES,OAAO,EAAExD,yBAAyB;QAClCyD,QAAQ,EAAEP;OACX,EACD;QAAEM,OAAO,EAAEvD,oBAAoB;QAAEwD,QAAQ,EAAEN;MAAuB,CAAE,EACpE;QAAEK,OAAO,EAAEtD,YAAY;QAAEuD,QAAQ,EAAEL;MAAe,CAAE,EACpD;QACEI,OAAO,EAAE3D,cAAc;QACvB4D,QAAQ,EAAE;UACRC,QAAQ,EAAE;YACRC,QAAQ,EAAE;cACRC,GAAG,EAAEA,CAAA,KAAM;;;;OAIlB;KAEJ,CAAC;IAEFpD,OAAO,GAAGb,OAAO,CAACkE,eAAe,CAACxD,4BAA4B,CAAC;IAC/DE,SAAS,GAAGC,OAAO,CAACsD,iBAAiB;IACrCrD,eAAe,GAAGd,OAAO,CAACoE,MAAM,CAC9BhE,eAAe,CACmB;IACpCW,yBAAyB,GAAGf,OAAO,CAACoE,MAAM,CACxC/D,yBAAyB,CACmB;IAC9CW,oBAAoB,GAAGhB,OAAO,CAACoE,MAAM,CACnC9D,oBAAoB,CACmB;IACzCW,YAAY,GAAGjB,OAAO,CAACoE,MAAM,CAAC7D,YAAY,CAAiC;EAC7E,CAAC,CAAC;EAEF8D,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAAC1D,SAAS,CAAC,CAAC2D,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,sCAAsC,EAAE,MAAK;IAC9CvD,eAAe,CAAC0D,cAAc,CAACC,GAAG,CAACC,WAAW,CAAClE,EAAE,CAACU,YAAY,CAAC,CAAC;IAChEH,yBAAyB,CAAC4D,kBAAkB,CAACF,GAAG,CAACC,WAAW,CAC1DlE,EAAE,CAAC,CAACiC,sBAAsB,CAAC,CAAC,CAC7B;IACDzB,oBAAoB,CAAC4D,yBAAyB,CAACH,GAAG,CAACC,WAAW,CAC5DlE,EAAE,CAAC,CAACqC,iBAAiB,CAAC,CAAC,CACxB;IAEDjC,SAAS,CAACiE,QAAQ,EAAE;IAEpBP,MAAM,CAACxD,eAAe,CAAC0D,cAAc,CAAC,CAACM,oBAAoB,CAAC,CAAC,CAAC;IAC9DR,MAAM,CAAC1D,SAAS,CAACmE,QAAQ,CAAC,CAACC,OAAO,CAAC9D,YAAY,CAAC;IAChDoD,MAAM,CAAC1D,SAAS,CAACqE,SAAS,CAAC,CAACC,SAAS,EAAE;EACzC,CAAC,CAAC;EAEFb,EAAE,CAAC,oDAAoD,EAAE,MAAK;IAC5DrE,OAAO,CAACmF,kBAAkB,EAAE;IAC5BnF,OAAO,CAAC0D,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPjD,4BAA4B,EAC5BX,uBAAuB,EACvBE,uBAAuB,EACvBE,mBAAmB,CACpB;MACDyD,SAAS,EAAE,CACT;QAAEC,OAAO,EAAEzD,eAAe;QAAE0D,QAAQ,EAAEhD;MAAe,CAAE,EACvD;QACE+C,OAAO,EAAExD,yBAAyB;QAClCyD,QAAQ,EAAE/C;OACX,EACD;QAAE8C,OAAO,EAAEvD,oBAAoB;QAAEwD,QAAQ,EAAE9C;MAAoB,CAAE,EACjE;QAAE6C,OAAO,EAAEtD,YAAY;QAAEuD,QAAQ,EAAE7C;MAAY,CAAE,EACjD;QACE4C,OAAO,EAAE3D,cAAc;QACvB4D,QAAQ,EAAE;UACRC,QAAQ,EAAE;YACRC,QAAQ,EAAE;cACRC,GAAG,EAAEA,CAAA,KAAM;;;;OAIlB;KAEJ,CAAC;IAEFpD,OAAO,GAAGb,OAAO,CAACkE,eAAe,CAACxD,4BAA4B,CAAC;IAC/DE,SAAS,GAAGC,OAAO,CAACsD,iBAAiB;IACrCvD,SAAS,CAACiE,QAAQ,EAAE;IAEpBP,MAAM,CAACrD,YAAY,CAACmE,KAAK,CAAC,CAACN,oBAAoB,CAC7C,kCAAkC,CACnC;EACH,CAAC,CAAC;EAEFT,EAAE,CAAC,mDAAmD,EAAE,MAAK;IAC3DvD,eAAe,CAAC0D,cAAc,CAACC,GAAG,CAACC,WAAW,CAC5CjE,UAAU,CAAC,MAAM,IAAI4E,KAAK,EAAE,CAAC,CAC9B;IAEDzE,SAAS,CAACiE,QAAQ,EAAE;IAEpBP,MAAM,CAACrD,YAAY,CAACmE,KAAK,CAAC,CAACN,oBAAoB,CAC7C,2CAA2C,CAC5C;IACDR,MAAM,CAAC1D,SAAS,CAACqE,SAAS,CAAC,CAACC,SAAS,EAAE;EACzC,CAAC,CAAC;EAEFb,EAAE,CAAC,gEAAgE,EAAE,MAAK;IACxEvD,eAAe,CAAC0D,cAAc,CAACC,GAAG,CAACC,WAAW,CAAClE,EAAE,CAACU,YAAY,CAAC,CAAC;IAChEH,yBAAyB,CAAC4D,kBAAkB,CAACF,GAAG,CAACC,WAAW,CAC1DlE,EAAE,CAAC,CAACiC,sBAAsB,CAAC,CAAC,CAC7B;IACDzB,oBAAoB,CAAC4D,yBAAyB,CAACH,GAAG,CAACC,WAAW,CAC5DlE,EAAE,CAAC,CAACqC,iBAAiB,CAAC,CAAC,CACxB;IAEDjC,SAAS,CAACiE,QAAQ,EAAE;IAEpBP,MAAM,CAACvD,yBAAyB,CAAC4D,kBAAkB,CAAC,CAACG,oBAAoB,CACvE,CAAC,CACF;IACDR,MAAM,CAAC1D,SAAS,CAACsC,oBAAoB,CAAC,CAACoC,IAAI,CAAC,CAAC,CAAC;EAChD,CAAC,CAAC;EAEFjB,EAAE,CAAC,wDAAwD,EAAE,MAAK;IAChEvD,eAAe,CAAC0D,cAAc,CAACC,GAAG,CAACC,WAAW,CAAClE,EAAE,CAACU,YAAY,CAAC,CAAC;IAChEH,yBAAyB,CAAC4D,kBAAkB,CAACF,GAAG,CAACC,WAAW,CAAClE,EAAE,CAAC,EAAE,CAAC,CAAC;IAEpEI,SAAS,CAACiE,QAAQ,EAAE;IAEpBP,MAAM,CAACrD,YAAY,CAACmE,KAAK,CAAC,CAACN,oBAAoB,CAC7C,8DAA8D,CAC/D;IACDR,MAAM,CAAC1D,SAAS,CAACqE,SAAS,CAAC,CAACC,SAAS,EAAE;EACzC,CAAC,CAAC;EAEFb,EAAE,CAAC,sDAAsD,EAAE,MAAK;IAC9DvD,eAAe,CAAC0D,cAAc,CAACC,GAAG,CAACC,WAAW,CAAClE,EAAE,CAACU,YAAY,CAAC,CAAC;IAChEH,yBAAyB,CAAC4D,kBAAkB,CAACF,GAAG,CAACC,WAAW,CAC1DjE,UAAU,CAAC,MAAM,IAAI4E,KAAK,EAAE,CAAC,CAC9B;IAEDzE,SAAS,CAACiE,QAAQ,EAAE;IAEpBP,MAAM,CAACrD,YAAY,CAACmE,KAAK,CAAC,CAACN,oBAAoB,CAC7C,6CAA6C,CAC9C;IACDR,MAAM,CAAC1D,SAAS,CAACqE,SAAS,CAAC,CAACC,SAAS,EAAE;EACzC,CAAC,CAAC;EAEFb,EAAE,CAAC,+DAA+D,EAAE,MAAK;IACvEvD,eAAe,CAAC0D,cAAc,CAACC,GAAG,CAACC,WAAW,CAAClE,EAAE,CAACU,YAAY,CAAC,CAAC;IAChEH,yBAAyB,CAAC4D,kBAAkB,CAACF,GAAG,CAACC,WAAW,CAC1DlE,EAAE,CAAC,CAACiC,sBAAsB,CAAC,CAAC,CAC7B;IACDzB,oBAAoB,CAAC4D,yBAAyB,CAACH,GAAG,CAACC,WAAW,CAC5DlE,EAAE,CAAC,CAACqC,iBAAiB,CAAC,CAAC,CACxB;IAEDjC,SAAS,CAACiE,QAAQ,EAAE;IAEpBP,MAAM,CAACtD,oBAAoB,CAAC4D,yBAAyB,CAAC,CAACE,oBAAoB,CACzE,CAAC,CACF;IACDR,MAAM,CAAC1D,SAAS,CAAC2E,cAAc,CAAC,CAACP,OAAO,CAAC,CAACnC,iBAAiB,CAAC,CAAC;IAC7DyB,MAAM,CAAC1D,SAAS,CAACqE,SAAS,CAAC,CAACC,SAAS,EAAE;EACzC,CAAC,CAAC;EAEFb,EAAE,CAAC,kDAAkD,EAAE,MAAK;IAC1DvD,eAAe,CAAC0D,cAAc,CAACC,GAAG,CAACC,WAAW,CAAClE,EAAE,CAACU,YAAY,CAAC,CAAC;IAChEH,yBAAyB,CAAC4D,kBAAkB,CAACF,GAAG,CAACC,WAAW,CAC1DlE,EAAE,CAAC,CAACiC,sBAAsB,CAAC,CAAC,CAC7B;IACDzB,oBAAoB,CAAC4D,yBAAyB,CAACH,GAAG,CAACC,WAAW,CAC5DjE,UAAU,CAAC,MAAM,IAAI4E,KAAK,EAAE,CAAC,CAC9B;IAEDzE,SAAS,CAACiE,QAAQ,EAAE;IAEpBP,MAAM,CAACrD,YAAY,CAACmE,KAAK,CAAC,CAACN,oBAAoB,CAC7C,oCAAoC,CACrC;IACDR,MAAM,CAAC1D,SAAS,CAACqE,SAAS,CAAC,CAACC,SAAS,EAAE;EACzC,CAAC,CAAC;EAEFb,EAAE,CAAC,sFAAsF,EAAE,MAAK;IAC9FzD,SAAS,CAAC4E,6BAA6B,EAAE;IAEzClB,MAAM,CAACrD,YAAY,CAACmE,KAAK,CAAC,CAACN,oBAAoB,CAC7C,6CAA6C,CAC9C;IACDR,MAAM,CAAC1D,SAAS,CAACqE,SAAS,CAAC,CAACC,SAAS,EAAE;EACzC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}