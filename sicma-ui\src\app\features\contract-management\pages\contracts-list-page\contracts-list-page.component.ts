import { <PERSON><PERSON><PERSON>cy<PERSON>ip<PERSON>, DatePipe } from '@angular/common';
import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSelectModule } from '@angular/material/select';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Router, RouterModule } from '@angular/router';
import { ContractDialogComponent } from '@contract-management/components/contract-dialog/contract-dialog.component';
import { ContractList } from '@contract-management/models/contract-list.model';
import { ContractYear } from '@contract-management/models/contract-year.model';
import { ContractYearService } from '@contract-management/services/contract-year.service';
import { ContractService } from '@contract-management/services/contract.service';
import { AlertService } from '@shared/services/alert.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { finalize } from 'rxjs';

@Component({
  selector: 'app-contracts-list-page',
  templateUrl: './contracts-list-page.component.html',
  styleUrl: './contracts-list-page.component.scss',
  standalone: true,
  imports: [
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    MatTooltipModule,
    MatSelectModule,
    FormsModule,
    RouterModule,
    DatePipe,
    CurrencyPipe,
  ],
})
export class ContractsListPageComponent implements OnInit, AfterViewInit {
  displayedColumns: string[] = [
    'contractNumber',
    'contractYear',
    'contractorIdNumber',
    'fullName',
    'subscriptionDate',
    'startDate',
    'endDate',
    'durationDays',
    'initialValue',
    'totalValue',
    'monthlyPayment',
    'addition',
    'hasCcp',
    'actions',
  ];
  dataSource = new MatTableDataSource<ContractList>();
  selectedYear: number | null = new Date().getFullYear();
  contractYears: ContractYear[] = [];
  allContractsData: ContractList[] = [];

  contractNumberFilter = '';
  contractorNameFilter = '';
  contractorIdFilter = '';

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private readonly contractService: ContractService,
    private readonly contractYearService: ContractYearService,
    private readonly dialog: MatDialog,
    private readonly router: Router,
    private readonly spinner: NgxSpinnerService,
    private readonly alert: AlertService,
  ) {}

  ngOnInit(): void {
    this.loadContractList();
    this.loadContractYears();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyIndividualFilters() {
    if (!this.selectedYear) {
      this.dataSource.data = this.allContractsData.filter((contract) =>
        this.matchesIndividualFilters(contract),
      );
    } else {
      this.dataSource.data = this.allContractsData.filter(
        (contract) =>
          contract.contractYear?.year === this.selectedYear &&
          this.matchesIndividualFilters(contract),
      );
    }
  }

  private matchesIndividualFilters(contract: ContractList): boolean {
    const matchesContractNumber =
      this.contractNumberFilter === '' ||
      (contract.contractNumber &&
        contract.contractNumber
          .toString()
          .toUpperCase()
          .includes(this.contractNumberFilter.toUpperCase()));

    const matchesContractorName =
      this.contractorNameFilter === '' ||
      (contract.fullName &&
        contract.fullName
          .toUpperCase()
          .includes(this.contractorNameFilter.toUpperCase()));

    const matchesContractorId =
      this.contractorIdFilter === '' ||
      (contract.contractorIdNumber &&
        contract.contractorIdNumber
          .toString()
          .toUpperCase()
          .includes(this.contractorIdFilter.toUpperCase()));

    return Boolean(
      matchesContractNumber && matchesContractorName && matchesContractorId,
    );
  }

  openContractDialog(): void {
    this.dialog
      .open(ContractDialogComponent, { width: '1000px' })
      .afterClosed()
      .subscribe((result: 'created' | 'updated' | undefined) => {
        if (result === 'created' || result === 'updated') {
          this.loadContractList();
        }
      });
  }

  handleEditContract(contract: ContractList): void {
    this.router.navigate(['/contratos', contract.id]);
  }

  exportToCSV(): void {
    if (!this.selectedYear) {
      this.alert.warning('Por favor seleccione un año para exportar');
      return;
    }

    this.spinner.show();
    this.contractService
      .exportContracts(this.selectedYear)
      .pipe(finalize(() => this.spinner.hide()))
      .subscribe({
        next: (blob) => {
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `Contratos_MADS_${this.selectedYear}_${
            new Date().toISOString().split('T')[0]
          }.csv`;
          link.click();
          window.URL.revokeObjectURL(url);
          this.alert.success('Exportación completada exitosamente');
        },
        error: (_) => {
          this.alert.error(error.error?.detail ?? 'Error al exportar contratos.');
        },
      });
  }

  private loadContractYears(): void {
    this.spinner.show();
    this.contractYearService
      .getAll()
      .pipe(finalize(() => this.spinner.hide()))
      .subscribe({
        next: (years) => {
          this.contractYears = years;

          const currentYear = new Date().getFullYear();
          const yearExists = years.some((y) => y.year === currentYear);

          if (yearExists) {
            this.selectedYear = currentYear;
          } else if (years.length > 0) {
            this.selectedYear = years
              .map((y) => y.year)
              .sort((a, b) => b - a)[0];
          }

          this.applyIndividualFilters();
        },
        error: (_) => {
          this.alert.error(error.error?.detail ?? 'Error al cargar los años de contrato');
        },
      });
  }

  private loadContractList(): void {
    this.spinner.show();
    this.contractService
      .getContractList()
      .pipe(finalize(() => this.spinner.hide()))
      .subscribe({
        next: (contracts) => {
          this.allContractsData = contracts;
          this.dataSource.data = contracts;
        },
        error: (_) => {
          this.alert.error(error.error?.detail ?? 'Error al cargar la lista de contratos');
        },
      });
  }

  onYearChange(): void {
    this.applyIndividualFilters();
  }

  clearFilters(): void {
    this.selectedYear = null;
    this.contractNumberFilter = '';
    this.contractorNameFilter = '';
    this.contractorIdFilter = '';
    this.applyIndividualFilters();
  }
}
