{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { FooterComponent } from './footer.component';\ndescribe('FooterComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [FooterComponent]\n    });\n    fixture = TestBed.createComponent(FooterComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "FooterComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "imports", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\core\\layout\\footer\\footer.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\n\nimport { FooterComponent } from './footer.component';\n\ndescribe('FooterComponent', () => {\n  let component: FooterComponent;\n  let fixture: ComponentFixture<FooterComponent>;\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [FooterComponent],\n    });\n    fixture = TestBed.createComponent(FooterComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});"], "mappings": "AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AAEjE,SAASC,eAAe,QAAQ,oBAAoB;AAEpDC,QAAQ,CAAC,iBAAiB,EAAE,MAAK;EAC/B,IAAIC,SAA0B;EAC9B,IAAIC,OAA0C;EAE9CC,UAAU,CAAC,MAAK;IACdL,OAAO,CAACM,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACN,eAAe;KAC1B,CAAC;IACFG,OAAO,GAAGJ,OAAO,CAACQ,eAAe,CAACP,eAAe,CAAC;IAClDE,SAAS,GAAGC,OAAO,CAACK,iBAAiB;IACrCL,OAAO,CAACM,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACT,SAAS,CAAC,CAACU,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}