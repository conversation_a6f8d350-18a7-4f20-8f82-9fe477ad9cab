{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { GenderService } from './gender.service';\ndescribe('GenderService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/genders`;\n  const mockGender = {\n    id: 1,\n    name: 'Test Gender'\n  };\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [GenderService]\n    });\n    service = TestBed.inject(GenderService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all genders', () => {\n      const mockGenders = [mockGender];\n      service.getAll().subscribe(genders => {\n        expect(genders).toEqual(mockGenders);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockGenders);\n    });\n  });\n  describe('getById', () => {\n    it('should return a gender by id', () => {\n      const id = 1;\n      service.getById(id).subscribe(gender => {\n        expect(gender).toEqual(mockGender);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockGender);\n    });\n  });\n  describe('create', () => {\n    it('should create a new gender', () => {\n      const newGender = {\n        name: 'New Gender'\n      };\n      service.create(newGender).subscribe(gender => {\n        expect(gender).toEqual(mockGender);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newGender);\n      req.flush(mockGender);\n    });\n  });\n  describe('update', () => {\n    it('should update a gender', () => {\n      const id = 1;\n      const updateData = {\n        name: 'Updated Gender'\n      };\n      service.update(id, updateData).subscribe(gender => {\n        expect(gender).toEqual(mockGender);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockGender);\n    });\n  });\n  describe('delete', () => {\n    it('should delete a gender', () => {\n      const id = 1;\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n  describe('getByName', () => {\n    it('should return a gender by name', () => {\n      const name = 'Test Gender';\n      service.getByName(name).subscribe(gender => {\n        expect(gender).toEqual(mockGender);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockGender);\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "GenderService", "describe", "service", "httpMock", "apiUrl", "mockGender", "id", "name", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockGenders", "getAll", "subscribe", "genders", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "getById", "gender", "newGender", "create", "body", "updateData", "update", "delete", "nothing", "getByName"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-management\\services\\gender.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { Gender } from '@contractor-management/models/gender.model';\nimport { environment } from '@env';\nimport { GenderService } from './gender.service';\n\ndescribe('GenderService', () => {\n  let service: GenderService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/genders`;\n\n  const mockGender: Gender = {\n    id: 1,\n    name: 'Test Gender',\n  };\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [GenderService],\n    });\n    service = TestBed.inject(GenderService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all genders', () => {\n      const mockGenders = [mockGender];\n\n      service.getAll().subscribe((genders) => {\n        expect(genders).toEqual(mockGenders);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockGenders);\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a gender by id', () => {\n      const id = 1;\n\n      service.getById(id).subscribe((gender) => {\n        expect(gender).toEqual(mockGender);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockGender);\n    });\n  });\n\n  describe('create', () => {\n    it('should create a new gender', () => {\n      const newGender: Omit<Gender, 'id'> = {\n        name: 'New Gender',\n      };\n\n      service.create(newGender).subscribe((gender) => {\n        expect(gender).toEqual(mockGender);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newGender);\n      req.flush(mockGender);\n    });\n  });\n\n  describe('update', () => {\n    it('should update a gender', () => {\n      const id = 1;\n      const updateData: Partial<Gender> = {\n        name: 'Updated Gender',\n      };\n\n      service.update(id, updateData).subscribe((gender) => {\n        expect(gender).toEqual(mockGender);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockGender);\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete a gender', () => {\n      const id = 1;\n\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n\n  describe('getByName', () => {\n    it('should return a gender by name', () => {\n      const name = 'Test Gender';\n\n      service.getByName(name).subscribe((gender) => {\n        expect(gender).toEqual(mockGender);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockGender);\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,aAAa,QAAQ,kBAAkB;AAEhDC,QAAQ,CAAC,eAAe,EAAE,MAAK;EAC7B,IAAIC,OAAsB;EAC1B,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,UAAU;EAE9C,MAAMC,UAAU,GAAW;IACzBC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE;GACP;EAEDC,UAAU,CAAC,MAAK;IACdV,OAAO,CAACW,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACd,uBAAuB,CAAC;MAClCe,SAAS,EAAE,CAACX,aAAa;KAC1B,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACc,MAAM,CAACZ,aAAa,CAAC;IACvCG,QAAQ,GAAGL,OAAO,CAACc,MAAM,CAACf,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFgB,SAAS,CAAC,MAAK;IACbV,QAAQ,CAACW,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACd,OAAO,CAAC,CAACe,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFhB,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,2BAA2B,EAAE,MAAK;MACnC,MAAMG,WAAW,GAAG,CAACb,UAAU,CAAC;MAEhCH,OAAO,CAACiB,MAAM,EAAE,CAACC,SAAS,CAAEC,OAAO,IAAI;QACrCL,MAAM,CAACK,OAAO,CAAC,CAACC,OAAO,CAACJ,WAAW,CAAC;MACtC,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,WAAW,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjB,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBc,EAAE,CAAC,8BAA8B,EAAE,MAAK;MACtC,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAAC2B,OAAO,CAACvB,EAAE,CAAC,CAACc,SAAS,CAAEU,MAAM,IAAI;QACvCd,MAAM,CAACc,MAAM,CAAC,CAACR,OAAO,CAACjB,UAAU,CAAC;MACpC,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvB,UAAU,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,4BAA4B,EAAE,MAAK;MACpC,MAAMgB,SAAS,GAAuB;QACpCxB,IAAI,EAAE;OACP;MAEDL,OAAO,CAAC8B,MAAM,CAACD,SAAS,CAAC,CAACX,SAAS,CAAEU,MAAM,IAAI;QAC7Cd,MAAM,CAACc,MAAM,CAAC,CAACR,OAAO,CAACjB,UAAU,CAAC;MACpC,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACS,SAAS,CAAC;MAC3CR,GAAG,CAACK,KAAK,CAACvB,UAAU,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,wBAAwB,EAAE,MAAK;MAChC,MAAMT,EAAE,GAAG,CAAC;MACZ,MAAM4B,UAAU,GAAoB;QAClC3B,IAAI,EAAE;OACP;MAEDL,OAAO,CAACiC,MAAM,CAAC7B,EAAE,EAAE4B,UAAU,CAAC,CAACd,SAAS,CAAEU,MAAM,IAAI;QAClDd,MAAM,CAACc,MAAM,CAAC,CAACR,OAAO,CAACjB,UAAU,CAAC;MACpC,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACY,UAAU,CAAC;MAC5CX,GAAG,CAACK,KAAK,CAACvB,UAAU,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,wBAAwB,EAAE,MAAK;MAChC,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAACkC,MAAM,CAAC9B,EAAE,CAAC,CAACc,SAAS,CAAC,MAAK;QAChCJ,MAAM,EAAE,CAACqB,OAAO,EAAE;MACpB,CAAC,CAAC;MAEF,MAAMd,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF3B,QAAQ,CAAC,WAAW,EAAE,MAAK;IACzBc,EAAE,CAAC,gCAAgC,EAAE,MAAK;MACxC,MAAMR,IAAI,GAAG,aAAa;MAE1BL,OAAO,CAACoC,SAAS,CAAC/B,IAAI,CAAC,CAACa,SAAS,CAAEU,MAAM,IAAI;QAC3Cd,MAAM,CAACc,MAAM,CAAC,CAACR,OAAO,CAACjB,UAAU,CAAC;MACpC,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,SAASG,IAAI,EAAE,CAAC;MACxDS,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvB,UAAU,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}