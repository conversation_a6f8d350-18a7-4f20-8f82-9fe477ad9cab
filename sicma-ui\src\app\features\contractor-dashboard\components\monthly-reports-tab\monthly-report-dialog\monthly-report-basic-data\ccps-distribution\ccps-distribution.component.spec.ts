import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { CCP } from '@contract-management/models/ccp.model';
import { CcpService } from '@contract-management/services/ccp.service';
import { MonthlyReportCcpService } from '@contractor-dashboard/services/monthly-report-ccp.service';
import { AlertService } from '@shared/services/alert.service';
import { of, throwError } from 'rxjs';
import { CcpsDistributionComponent } from './ccps-distribution.component';

describe('CcpsDistributionComponent', () => {
  let component: CcpsDistributionComponent;
  let fixture: ComponentFixture<CcpsDistributionComponent>;
  let ccpService: jasmine.SpyObj<CcpService>;
  let monthlyReportCcpService: jasmine.SpyObj<MonthlyReportCcpService>;
  let alertService: jasmine.SpyObj<AlertService>;

  const mockCcps: CCP[] = [
    {
      id: 1,
      expenseObjectUseCcp: 'Test Expense Object 1',
      expenseObjectDescription: 'Test Description 1',
      value: 1000000,
      contractId: 1,
    },
    {
      id: 2,
      expenseObjectUseCcp: 'Test Expense Object 2',
      expenseObjectDescription: 'Test Description 2',
      value: 2000000,
      contractId: 1,
    },
  ];

  const mockMonthlyReportCcps = [
    { id: 1, ccpId: 1, monthlyReportId: 1, ccpValue: 500000 },
    { id: 2, ccpId: 2, monthlyReportId: 1, ccpValue: 1500000 },
  ];

  beforeEach(async () => {
    const ccpServiceSpy = jasmine.createSpyObj('CcpService', [
      'getAllByContractId',
    ]);
    const monthlyReportCcpServiceSpy = jasmine.createSpyObj(
      'MonthlyReportCcpService',
      ['getAll', 'create', 'update'],
    );
    const alertServiceSpy = jasmine.createSpyObj('AlertService', ['error']);

    await TestBed.configureTestingModule({
      imports: [
        CcpsDistributionComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        ReactiveFormsModule,
      ],
      providers: [
        FormBuilder,
        { provide: CcpService, useValue: ccpServiceSpy },
        {
          provide: MonthlyReportCcpService,
          useValue: monthlyReportCcpServiceSpy,
        },
        { provide: AlertService, useValue: alertServiceSpy },
      ],
    }).compileComponents();

    ccpService = TestBed.inject(CcpService) as jasmine.SpyObj<CcpService>;
    monthlyReportCcpService = TestBed.inject(
      MonthlyReportCcpService,
    ) as jasmine.SpyObj<MonthlyReportCcpService>;
    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;

    ccpService.getAllByContractId.and.returnValue(of(mockCcps));
    monthlyReportCcpService.getAll.and.returnValue(of(mockMonthlyReportCcps));
    monthlyReportCcpService.create.and.returnValue(
      of(mockMonthlyReportCcps[0]),
    );
    monthlyReportCcpService.update.and.returnValue(
      of(mockMonthlyReportCcps[0]),
    );

    fixture = TestBed.createComponent(CcpsDistributionComponent);
    component = fixture.componentInstance;
    component.contractId = 1;
    component.monthlyReportId = 1;
    component.totalValue = 2000000;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Initialization', () => {
    it('should load CCPs on init', () => {
      fixture.detectChanges();
      expect(ccpService.getAllByContractId).toHaveBeenCalledWith(1);
      expect(component.ccpsFormArray.length).toBe(2);
    });

    it('should handle error when loading CCPs', () => {
      ccpService.getAllByContractId.and.returnValue(
        throwError(() => new Error()),
      );
      fixture.detectChanges();
      expect(alertService.error).toHaveBeenCalledWith(
        'Error al cargar los CCPs',
      );
    });

    it('should load existing CCP values', () => {
      fixture.detectChanges();
      expect(monthlyReportCcpService.getAll).toHaveBeenCalled();
      expect(component.ccpsFormArray.at(0).get('ccpValue')?.value).toBe(500000);
      expect(component.ccpsFormArray.at(1).get('ccpValue')?.value).toBe(
        1500000,
      );
    });

    it('should handle error when loading existing CCP values', () => {
      monthlyReportCcpService.getAll.and.returnValue(
        throwError(() => new Error()),
      );
      fixture.detectChanges();
      expect(alertService.error).toHaveBeenCalledWith(
        'Error al cargar los valores de CCPs existentes',
      );
    });
  });

  describe('Form Validation', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should validate total CCP value matches total value', () => {
      const validityChangeSpy = spyOn(component.validityChange, 'emit');
      component.ccpsFormArray.at(0).get('ccpValue')?.setValue(1000000);
      component.ccpsFormArray.at(1).get('ccpValue')?.setValue(1000000);
      expect(validityChangeSpy).toHaveBeenCalledWith(true);
    });

    it('should invalidate when total CCP value does not match total value', () => {
      const validityChangeSpy = spyOn(component.validityChange, 'emit');
      component.ccpsFormArray.at(0).get('ccpValue')?.setValue(500000);
      component.ccpsFormArray.at(1).get('ccpValue')?.setValue(500000);
      expect(validityChangeSpy).toHaveBeenCalledWith(false);
    });

    it('should validate CCP values do not exceed maximum', () => {
      const validityChangeSpy = spyOn(component.validityChange, 'emit');
      component.ccpsFormArray.at(0).get('ccpValue')?.setValue(2000000);
      expect(validityChangeSpy).toHaveBeenCalledWith(false);
    });
  });

  describe('Currency Formatting', () => {
    it('should format currency values correctly', () => {
      const formattedValue = new Intl.NumberFormat('es-CO', {
        style: 'currency',
        currency: 'COP',
        minimumFractionDigits: 0,
      }).format(1000000);
      expect(component.formatCurrency(1000000)).toBe(formattedValue);
      expect(component.formatCurrency(null)).toBe('N/A');
      expect(component.formatCurrency(undefined)).toBe('N/A');
    });
  });

  describe('Save CCPs', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should save CCPs successfully', (done) => {
      component.saveCcps().subscribe((result) => {
        expect(result).toBeTrue();
        expect(monthlyReportCcpService.update).toHaveBeenCalled();
        done();
      });
    });

    it('should handle error when saving CCPs', (done) => {
      monthlyReportCcpService.update.and.returnValue(
        throwError(() => new Error()),
      );
      component.saveCcps().subscribe((result) => {
        expect(result).toBeFalse();
        expect(alertService.error).toHaveBeenCalledWith(
          'Error al guardar los CCPs',
        );
        done();
      });
    });

    it('should return false when no CCPs exist', (done) => {
      component.ccpsFormArray.clear();
      component.saveCcps().subscribe((result) => {
        expect(result).toBeFalse();
        done();
      });
    });
  });

  describe('Cleanup', () => {
    it('should complete destroy subject on destroy', () => {
      const nextSpy = spyOn(component['destroy$'], 'next');
      const completeSpy = spyOn(component['destroy$'], 'complete');

      component.ngOnDestroy();

      expect(nextSpy).toHaveBeenCalled();
      expect(completeSpy).toHaveBeenCalled();
    });
  });
});