import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { User } from '@core/auth/models/user.model';
import { UserProfile } from '@core/auth/models/user_profile.model';
import { environment } from '@env';
import { UserService } from './user.service';

describe('UserService', () => {
  let service: UserService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/users`;

  const mockUserProfile: UserProfile = {
    profile_id: 1,
    profile_name: 'admin',
  };

  const mockUser: User = {
    id: 1,
    username: 'testuser',
    profiles: [mockUserProfile],
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [UserService],
    });
    service = TestBed.inject(UserService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all users', () => {
      const mockUsers = [mockUser];

      service.getAll().subscribe((users) => {
        expect(users).toEqual(mockUsers);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockUsers);
    });

    it('should handle error when getting all users', () => {
      service.getAll().subscribe({
        error: (error: { status: number }) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('getById', () => {
    it('should return a single user by id', () => {
      const id = 1;

      service.getById(id).subscribe((user) => {
        expect(user).toEqual(mockUser);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockUser);
    });

    it('should handle error when getting user by id', () => {
      const id = 999;

      service.getById(id).subscribe({
        error: (error: { status: number }) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('getByUsername', () => {
    it('should return a single user by username', () => {
      const username = 'testuser';

      service.getByUsername(username).subscribe((user) => {
        expect(user).toEqual(mockUser);
      });

      const req = httpMock.expectOne(`${apiUrl}/username/${username}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockUser);
    });

    it('should handle error when getting user by username', () => {
      const username = 'nonexistent';

      service.getByUsername(username).subscribe({
        error: (error: { status: number }) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/username/${username}`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('create', () => {
    it('should create a new user', () => {
      const newUser = {
        username: 'newuser',
        profile_ids: [1],
      };

      const mockAssignment = {
        user_id: 1,
        profile_ids: [1],
      };

      service.create(newUser).subscribe((result) => {
        expect(result).toEqual(jasmine.objectContaining(mockAssignment));
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newUser);
      req.flush(mockAssignment);
    });

    it('should handle error when creating user', () => {
      const newUser = {
        username: 'newuser',
        profile_ids: [1],
      };

      service.create(newUser).subscribe({
        error: (error: { status: number }) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('authenticate', () => {
    it('should authenticate user with token', () => {
      const credentials = { token: 'test-token' };
      const mockResponse = {
        token: 'new-token',
        user: mockUser,
      };

      service.authenticate(credentials).subscribe((response) => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${apiUrl}/authenticate`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(credentials);
      req.flush(mockResponse);
    });

    it('should handle authentication error', () => {
      const credentials = { token: 'invalid-token' };

      service.authenticate(credentials).subscribe({
        error: (error: { status: number }) => {
          expect(error.status).toBe(401);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/authenticate`);
      req.error(new ErrorEvent('Unauthorized'), { status: 401 });
    });
  });

  describe('update', () => {
    it('should update an existing user', () => {
      const id = 1;
      const updateData: User = {
        id: 1,
        username: 'updated',
        profiles: [mockUserProfile],
      };

      service.update(id, updateData).subscribe((user) => {
        expect(user).toEqual(mockUser);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockUser);
    });

    it('should handle error when updating user', () => {
      const id = 1;
      const updateData: User = {
        id: 1,
        username: 'updated',
        profiles: [mockUserProfile],
      };

      service.update(id, updateData).subscribe({
        error: (error: { status: number }) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('delete', () => {
    it('should delete a user', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });

    it('should handle error when deleting user', () => {
      const id = 1;

      service.delete(id).subscribe({
        error: (error: { status: number }) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });
});