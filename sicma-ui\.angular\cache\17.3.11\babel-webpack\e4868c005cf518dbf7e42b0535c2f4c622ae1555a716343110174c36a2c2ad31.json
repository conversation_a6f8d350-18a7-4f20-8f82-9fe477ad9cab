{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { EmailTemplateService } from './email-template.service';\ndescribe('EmailTemplateService', () => {\n  let service;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule]\n    });\n    service = TestBed.inject(EmailTemplateService);\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "HttpClientTestingModule", "EmailTemplateService", "describe", "service", "beforeEach", "configureTestingModule", "imports", "inject", "it", "expect", "toBeTruthy"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\shared\\services\\email-template.service.spec.ts"], "sourcesContent": ["import { TestBed } from '@angular/core/testing';\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\n\nimport { EmailTemplateService } from './email-template.service';\n\ndescribe('EmailTemplateService', () => {\n  let service: EmailTemplateService;\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n    });\n    service = TestBed.inject(EmailTemplateService);\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,uBAAuB,QAAQ,8BAA8B;AAEtE,SAASC,oBAAoB,QAAQ,0BAA0B;AAE/DC,QAAQ,CAAC,sBAAsB,EAAE,MAAK;EACpC,IAAIC,OAA6B;EAEjCC,UAAU,CAAC,MAAK;IACdL,OAAO,CAACM,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACN,uBAAuB;KAClC,CAAC;IACFG,OAAO,GAAGJ,OAAO,CAACQ,MAAM,CAACN,oBAAoB,CAAC;EAChD,CAAC,CAAC;EAEFO,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACN,OAAO,CAAC,CAACO,UAAU,EAAE;EAC9B,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}