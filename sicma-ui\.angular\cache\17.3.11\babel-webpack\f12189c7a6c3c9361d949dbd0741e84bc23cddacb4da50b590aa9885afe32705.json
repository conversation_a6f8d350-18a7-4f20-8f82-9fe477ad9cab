{"ast": null, "code": "function cov_1m58kj5l7g() {\n  var path = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-management\\\\pages\\\\contractors-list-page\\\\contractors-list-page.component.ts\";\n  var hash = \"d882746c25767322fc59852831e26b22ccd6b916\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-management\\\\pages\\\\contractors-list-page\\\\contractors-list-page.component.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 23,\n          column: 35\n        },\n        end: {\n          line: 92,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 25,\n          column: 8\n        },\n        end: {\n          line: 25,\n          column: 51\n        }\n      },\n      \"2\": {\n        start: {\n          line: 26,\n          column: 8\n        },\n        end: {\n          line: 26,\n          column: 29\n        }\n      },\n      \"3\": {\n        start: {\n          line: 27,\n          column: 8\n        },\n        end: {\n          line: 27,\n          column: 31\n        }\n      },\n      \"4\": {\n        start: {\n          line: 28,\n          column: 8\n        },\n        end: {\n          line: 28,\n          column: 27\n        }\n      },\n      \"5\": {\n        start: {\n          line: 29,\n          column: 8\n        },\n        end: {\n          line: 40,\n          column: 10\n        }\n      },\n      \"6\": {\n        start: {\n          line: 41,\n          column: 8\n        },\n        end: {\n          line: 41,\n          column: 51\n        }\n      },\n      \"7\": {\n        start: {\n          line: 44,\n          column: 8\n        },\n        end: {\n          line: 44,\n          column: 28\n        }\n      },\n      \"8\": {\n        start: {\n          line: 45,\n          column: 8\n        },\n        end: {\n          line: 53,\n          column: 11\n        }\n      },\n      \"9\": {\n        start: {\n          line: 47,\n          column: 33\n        },\n        end: {\n          line: 47,\n          column: 52\n        }\n      },\n      \"10\": {\n        start: {\n          line: 49,\n          column: 36\n        },\n        end: {\n          line: 49,\n          column: 70\n        }\n      },\n      \"11\": {\n        start: {\n          line: 51,\n          column: 16\n        },\n        end: {\n          line: 51,\n          column: 88\n        }\n      },\n      \"12\": {\n        start: {\n          line: 56,\n          column: 8\n        },\n        end: {\n          line: 56,\n          column: 51\n        }\n      },\n      \"13\": {\n        start: {\n          line: 57,\n          column: 8\n        },\n        end: {\n          line: 57,\n          column: 41\n        }\n      },\n      \"14\": {\n        start: {\n          line: 60,\n          column: 8\n        },\n        end: {\n          line: 75,\n          column: 11\n        }\n      },\n      \"15\": {\n        start: {\n          line: 70,\n          column: 12\n        },\n        end: {\n          line: 71,\n          column: 23\n        }\n      },\n      \"16\": {\n        start: {\n          line: 71,\n          column: 16\n        },\n        end: {\n          line: 71,\n          column: 23\n        }\n      },\n      \"17\": {\n        start: {\n          line: 72,\n          column: 12\n        },\n        end: {\n          line: 74,\n          column: 52\n        }\n      },\n      \"18\": {\n        start: {\n          line: 73,\n          column: 51\n        },\n        end: {\n          line: 73,\n          column: 82\n        }\n      },\n      \"19\": {\n        start: {\n          line: 78,\n          column: 28\n        },\n        end: {\n          line: 78,\n          column: 46\n        }\n      },\n      \"20\": {\n        start: {\n          line: 79,\n          column: 8\n        },\n        end: {\n          line: 79,\n          column: 66\n        }\n      },\n      \"21\": {\n        start: {\n          line: 80,\n          column: 8\n        },\n        end: {\n          line: 80,\n          column: 47\n        }\n      },\n      \"22\": {\n        start: {\n          line: 82,\n          column: 13\n        },\n        end: {\n          line: 87,\n          column: 6\n        }\n      },\n      \"23\": {\n        start: {\n          line: 82,\n          column: 41\n        },\n        end: {\n          line: 87,\n          column: 5\n        }\n      },\n      \"24\": {\n        start: {\n          line: 88,\n          column: 13\n        },\n        end: {\n          line: 91,\n          column: 6\n        }\n      },\n      \"25\": {\n        start: {\n          line: 93,\n          column: 0\n        },\n        end: {\n          line: 111,\n          column: 33\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 24,\n            column: 4\n          },\n          end: {\n            line: 24,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 24,\n            column: 59\n          },\n          end: {\n            line: 42,\n            column: 5\n          }\n        },\n        line: 24\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 43,\n            column: 4\n          },\n          end: {\n            line: 43,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 43,\n            column: 15\n          },\n          end: {\n            line: 54,\n            column: 5\n          }\n        },\n        line: 43\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 47,\n            column: 27\n          },\n          end: {\n            line: 47,\n            column: 28\n          }\n        },\n        loc: {\n          start: {\n            line: 47,\n            column: 33\n          },\n          end: {\n            line: 47,\n            column: 52\n          }\n        },\n        line: 47\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 49,\n            column: 18\n          },\n          end: {\n            line: 49,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 49,\n            column: 36\n          },\n          end: {\n            line: 49,\n            column: 70\n          }\n        },\n        line: 49\n      },\n      \"4\": {\n        name: \"(anonymous_4)\",\n        decl: {\n          start: {\n            line: 50,\n            column: 19\n          },\n          end: {\n            line: 50,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 50,\n            column: 30\n          },\n          end: {\n            line: 52,\n            column: 13\n          }\n        },\n        line: 50\n      },\n      \"5\": {\n        name: \"(anonymous_5)\",\n        decl: {\n          start: {\n            line: 55,\n            column: 4\n          },\n          end: {\n            line: 55,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 55,\n            column: 22\n          },\n          end: {\n            line: 58,\n            column: 5\n          }\n        },\n        line: 55\n      },\n      \"6\": {\n        name: \"(anonymous_6)\",\n        decl: {\n          start: {\n            line: 59,\n            column: 4\n          },\n          end: {\n            line: 59,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 59,\n            column: 37\n          },\n          end: {\n            line: 76,\n            column: 5\n          }\n        },\n        line: 59\n      },\n      \"7\": {\n        name: \"(anonymous_7)\",\n        decl: {\n          start: {\n            line: 69,\n            column: 23\n          },\n          end: {\n            line: 69,\n            column: 24\n          }\n        },\n        loc: {\n          start: {\n            line: 69,\n            column: 35\n          },\n          end: {\n            line: 75,\n            column: 9\n          }\n        },\n        line: 69\n      },\n      \"8\": {\n        name: \"(anonymous_8)\",\n        decl: {\n          start: {\n            line: 73,\n            column: 43\n          },\n          end: {\n            line: 73,\n            column: 44\n          }\n        },\n        loc: {\n          start: {\n            line: 73,\n            column: 51\n          },\n          end: {\n            line: 73,\n            column: 82\n          }\n        },\n        line: 73\n      },\n      \"9\": {\n        name: \"(anonymous_9)\",\n        decl: {\n          start: {\n            line: 77,\n            column: 4\n          },\n          end: {\n            line: 77,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 77,\n            column: 23\n          },\n          end: {\n            line: 81,\n            column: 5\n          }\n        },\n        line: 77\n      },\n      \"10\": {\n        name: \"(anonymous_10)\",\n        decl: {\n          start: {\n            line: 82,\n            column: 35\n          },\n          end: {\n            line: 82,\n            column: 36\n          }\n        },\n        loc: {\n          start: {\n            line: 82,\n            column: 41\n          },\n          end: {\n            line: 87,\n            column: 5\n          }\n        },\n        line: 82\n      }\n    },\n    branchMap: {\n      \"0\": {\n        loc: {\n          start: {\n            line: 51,\n            column: 33\n          },\n          end: {\n            line: 51,\n            column: 86\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 51,\n            column: 33\n          },\n          end: {\n            line: 51,\n            column: 52\n          }\n        }, {\n          start: {\n            line: 51,\n            column: 56\n          },\n          end: {\n            line: 51,\n            column: 86\n          }\n        }],\n        line: 51\n      },\n      \"1\": {\n        loc: {\n          start: {\n            line: 70,\n            column: 12\n          },\n          end: {\n            line: 71,\n            column: 23\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 70,\n            column: 12\n          },\n          end: {\n            line: 71,\n            column: 23\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 70\n      },\n      \"2\": {\n        loc: {\n          start: {\n            line: 72,\n            column: 35\n          },\n          end: {\n            line: 74,\n            column: 51\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 73,\n            column: 18\n          },\n          end: {\n            line: 73,\n            column: 84\n          }\n        }, {\n          start: {\n            line: 74,\n            column: 18\n          },\n          end: {\n            line: 74,\n            column: 51\n          }\n        }],\n        line: 72\n      },\n      \"3\": {\n        loc: {\n          start: {\n            line: 73,\n            column: 51\n          },\n          end: {\n            line: 73,\n            column: 82\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 73,\n            column: 72\n          },\n          end: {\n            line: 73,\n            column: 78\n          }\n        }, {\n          start: {\n            line: 73,\n            column: 81\n          },\n          end: {\n            line: 73,\n            column: 82\n          }\n        }],\n        line: 73\n      }\n    },\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0\n    },\n    b: {\n      \"0\": [0, 0],\n      \"1\": [0, 0],\n      \"2\": [0, 0],\n      \"3\": [0, 0]\n    },\n    inputSourceMap: {\n      version: 3,\n      file: \"contractors-list-page.component.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-management\\\\pages\\\\contractors-list-page\\\\contractors-list-page.component.ts\"],\n      names: [],\n      mappings: \";;;AAAA,OAAO,EAAiB,SAAS,EAAU,SAAS,EAAE,MAAM,eAAe,CAAC;AAC5E,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AACrD,OAAO,EAAE,YAAY,EAAE,MAAM,6BAA6B,CAAC;AAC3D,OAAO,EAAE,OAAO,EAAE,MAAM,wBAAwB,CAAC;AACjD,OAAO,EAAE,kBAAkB,EAAE,MAAM,yBAAyB,CAAC;AAE7D,OAAO,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAC3D,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,kBAAkB,EAAE,MAAM,8BAA8B,CAAC;AAClE,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACzD,OAAO,EAAE,kBAAkB,EAAE,MAAM,6BAA6B,CAAC;AACjE,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACzD,OAAO,EAAE,gBAAgB,EAAE,MAAM,2BAA2B,CAAC;AAC7D,OAAO,EAAE,yBAAyB,EAAE,MAAM,iFAAiF,CAAC;AAE5H,OAAO,EAAE,iBAAiB,EAAE,MAAM,oDAAoD,CAAC;AACvF,OAAO,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAC9D,OAAO,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AAChD,OAAO,EAAE,QAAQ,EAAE,MAAM,MAAM,CAAC;AAmBzB,IAAM,4BAA4B,GAAlC,MAAM,4BAA4B;IAkBvC,YACmB,iBAAoC,EACpC,MAAiB,EACjB,OAA0B,EAC1B,KAAmB;QAHnB,sBAAiB,GAAjB,iBAAiB,CAAmB;QACpC,WAAM,GAAN,MAAM,CAAW;QACjB,YAAO,GAAP,OAAO,CAAmB;QAC1B,UAAK,GAAL,KAAK,CAAc;QArBtC,qBAAgB,GAAa;YAC3B,UAAU;YACV,QAAQ;YACR,UAAU;YACV,eAAe;YACf,gBAAgB;YAChB,KAAK;YACL,QAAQ;YACR,gBAAgB;YAChB,YAAY;YACZ,SAAS;SACV,CAAC;QACF,eAAU,GAAG,IAAI,kBAAkB,EAAc,CAAC;IAU/C,CAAC;IAEJ,QAAQ;QACN,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACpB,IAAI,CAAC,iBAAiB;aACnB,MAAM,EAAE;aACR,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;aACzC,SAAS,CAAC;YACT,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,WAAW,CAAC;YAC3D,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,8BAA8B,CAAC,CAAC;YAC1E,CAAC;SACF,CAAC,CAAC;IACP,CAAC;IAED,eAAe;QACb,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC3C,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;IACnC,CAAC;IAED,oBAAoB,CAAC,UAAuB;QAC1C,IAAI,CAAC,MAAM;aACR,IAAI,CAAC,yBAAyB,EAAE;YAC/B,KAAK,EAAE,KAAK;YACZ,QAAQ,EAAE,OAAO;YACjB,MAAM,EAAE,MAAM;YACd,SAAS,EAAE,MAAM;YACjB,IAAI,EAAE,UAAU;SACjB,CAAC;aACD,WAAW,EAAE;aACb,SAAS,CAAC,CAAC,MAAmB,EAAE,EAAE;YACjC,IAAI,CAAC,MAAM;gBAAE,OAAO;YAEpB,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,UAAU;gBAC/B,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACP,CAAC;IAED,WAAW,CAAC,KAAY;QACtB,MAAM,WAAW,GAAI,KAAK,CAAC,MAA2B,CAAC,KAAK,CAAC;QAC7D,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAC1D,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,SAAS,EAAE,CAAC;IACzC,CAAC;;;;;;;;4BAnDA,SAAS,SAAC,YAAY;uBACtB,SAAS,SAAC,OAAO;;;AAhBP,4BAA4B;IAjBxC,SAAS,CAAC;QACT,QAAQ,EAAE,2BAA2B;QACrC,8BAAqD;QAErD,UAAU,EAAE,IAAI;QAChB,OAAO,EAAE;YACP,eAAe;YACf,aAAa;YACb,aAAa;YACb,kBAAkB;YAClB,cAAc;YACd,cAAc;YACd,aAAa;YACb,kBAAkB;YAClB,gBAAgB;SACjB;;KACF,CAAC;GACW,4BAA4B,CAmExC\",\n      sourcesContent: [\"import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';\\nimport { MatDialog } from '@angular/material/dialog';\\nimport { MatPaginator } from '@angular/material/paginator';\\nimport { MatSort } from '@angular/material/sort';\\nimport { MatTableDataSource } from '@angular/material/table';\\n\\nimport { MatButtonModule } from '@angular/material/button';\\nimport { MatCardModule } from '@angular/material/card';\\nimport { MatFormFieldModule } from '@angular/material/form-field';\\nimport { MatIconModule } from '@angular/material/icon';\\nimport { MatInputModule } from '@angular/material/input';\\nimport { MatPaginatorModule } from '@angular/material/paginator';\\nimport { MatSortModule } from '@angular/material/sort';\\nimport { MatTableModule } from '@angular/material/table';\\nimport { MatTooltipModule } from '@angular/material/tooltip';\\nimport { ContractorDialogComponent } from '@contractor-management/components/contractor-dialog/contractor-dialog.component';\\nimport { Contractor } from '@contractor-management/models/contractor.model';\\nimport { ContractorService } from '@contractor-management/services/contractor.service';\\nimport { AlertService } from '@shared/services/alert.service';\\nimport { NgxSpinnerService } from 'ngx-spinner';\\nimport { finalize } from 'rxjs';\\n\\n@Component({\\n  selector: 'app-contractors-list-page',\\n  templateUrl: './contractors-list-page.component.html',\\n  styleUrl: './contractors-list-page.component.scss',\\n  standalone: true,\\n  imports: [\\n    MatButtonModule,\\n    MatCardModule,\\n    MatIconModule,\\n    MatFormFieldModule,\\n    MatInputModule,\\n    MatTableModule,\\n    MatSortModule,\\n    MatPaginatorModule,\\n    MatTooltipModule,\\n  ],\\n})\\nexport class ContractorsListPageComponent implements OnInit, AfterViewInit {\\n  displayedColumns: string[] = [\\n    'fullName',\\n    'idType',\\n    'idNumber',\\n    'personalEmail',\\n    'corporateEmail',\\n    'eps',\\n    'gender',\\n    'educationLevel',\\n    'profession',\\n    'actions',\\n  ];\\n  dataSource = new MatTableDataSource<Contractor>();\\n\\n  @ViewChild(MatPaginator) paginator!: MatPaginator;\\n  @ViewChild(MatSort) sort!: MatSort;\\n\\n  constructor(\\n    private readonly contractorService: ContractorService,\\n    private readonly dialog: MatDialog,\\n    private readonly spinner: NgxSpinnerService,\\n    private readonly alert: AlertService,\\n  ) {}\\n\\n  ngOnInit(): void {\\n    this.spinner.show();\\n    this.contractorService\\n      .getAll()\\n      .pipe(finalize(() => this.spinner.hide()))\\n      .subscribe({\\n        next: (contractors) => (this.dataSource.data = contractors),\\n        error: (error) => {\\n          this.alert.error(error.error?.detail ?? 'Error al cargar contratistas');\\n        },\\n      });\\n  }\\n\\n  ngAfterViewInit(): void {\\n    this.dataSource.paginator = this.paginator;\\n    this.dataSource.sort = this.sort;\\n  }\\n\\n  openContractorDialog(contractor?: Contractor): void {\\n    this.dialog\\n      .open(ContractorDialogComponent, {\\n        width: '95%',\\n        maxWidth: '800px',\\n        height: 'auto',\\n        maxHeight: '90vh',\\n        data: contractor,\\n      })\\n      .afterClosed()\\n      .subscribe((result?: Contractor) => {\\n        if (!result) return;\\n\\n        this.dataSource.data = contractor\\n          ? this.dataSource.data.map((c) => (c.id === result.id ? result : c))\\n          : [...this.dataSource.data, result];\\n      });\\n  }\\n\\n  applyFilter(event: Event): void {\\n    const filterValue = (event.target as HTMLInputElement).value;\\n    this.dataSource.filter = filterValue.trim().toUpperCase();\\n    this.dataSource.paginator?.firstPage();\\n  }\\n}\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"d882746c25767322fc59852831e26b22ccd6b916\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_1m58kj5l7g = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_1m58kj5l7g();\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./contractors-list-page.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./contractors-list-page.component.scss?ngResource\";\nimport { Component, ViewChild } from '@angular/core';\nimport { MatDialog } from '@angular/material/dialog';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport { MatTableDataSource } from '@angular/material/table';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { ContractorDialogComponent } from '@contractor-management/components/contractor-dialog/contractor-dialog.component';\nimport { ContractorService } from '@contractor-management/services/contractor.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { finalize } from 'rxjs';\ncov_1m58kj5l7g().s[0]++;\nlet ContractorsListPageComponent = class ContractorsListPageComponent {\n  constructor(contractorService, dialog, spinner, alert) {\n    cov_1m58kj5l7g().f[0]++;\n    cov_1m58kj5l7g().s[1]++;\n    this.contractorService = contractorService;\n    cov_1m58kj5l7g().s[2]++;\n    this.dialog = dialog;\n    cov_1m58kj5l7g().s[3]++;\n    this.spinner = spinner;\n    cov_1m58kj5l7g().s[4]++;\n    this.alert = alert;\n    cov_1m58kj5l7g().s[5]++;\n    this.displayedColumns = ['fullName', 'idType', 'idNumber', 'personalEmail', 'corporateEmail', 'eps', 'gender', 'educationLevel', 'profession', 'actions'];\n    cov_1m58kj5l7g().s[6]++;\n    this.dataSource = new MatTableDataSource();\n  }\n  ngOnInit() {\n    cov_1m58kj5l7g().f[1]++;\n    cov_1m58kj5l7g().s[7]++;\n    this.spinner.show();\n    cov_1m58kj5l7g().s[8]++;\n    this.contractorService.getAll().pipe(finalize(() => {\n      cov_1m58kj5l7g().f[2]++;\n      cov_1m58kj5l7g().s[9]++;\n      return this.spinner.hide();\n    })).subscribe({\n      next: contractors => {\n        cov_1m58kj5l7g().f[3]++;\n        cov_1m58kj5l7g().s[10]++;\n        return this.dataSource.data = contractors;\n      },\n      error: error => {\n        cov_1m58kj5l7g().f[4]++;\n        cov_1m58kj5l7g().s[11]++;\n        this.alert.error((cov_1m58kj5l7g().b[0][0]++, error.error?.detail) ?? (cov_1m58kj5l7g().b[0][1]++, 'Error al cargar contratistas'));\n      }\n    });\n  }\n  ngAfterViewInit() {\n    cov_1m58kj5l7g().f[5]++;\n    cov_1m58kj5l7g().s[12]++;\n    this.dataSource.paginator = this.paginator;\n    cov_1m58kj5l7g().s[13]++;\n    this.dataSource.sort = this.sort;\n  }\n  openContractorDialog(contractor) {\n    cov_1m58kj5l7g().f[6]++;\n    cov_1m58kj5l7g().s[14]++;\n    this.dialog.open(ContractorDialogComponent, {\n      width: '95%',\n      maxWidth: '800px',\n      height: 'auto',\n      maxHeight: '90vh',\n      data: contractor\n    }).afterClosed().subscribe(result => {\n      cov_1m58kj5l7g().f[7]++;\n      cov_1m58kj5l7g().s[15]++;\n      if (!result) {\n        cov_1m58kj5l7g().b[1][0]++;\n        cov_1m58kj5l7g().s[16]++;\n        return;\n      } else {\n        cov_1m58kj5l7g().b[1][1]++;\n      }\n      cov_1m58kj5l7g().s[17]++;\n      this.dataSource.data = contractor ? (cov_1m58kj5l7g().b[2][0]++, this.dataSource.data.map(c => {\n        cov_1m58kj5l7g().f[8]++;\n        cov_1m58kj5l7g().s[18]++;\n        return c.id === result.id ? (cov_1m58kj5l7g().b[3][0]++, result) : (cov_1m58kj5l7g().b[3][1]++, c);\n      })) : (cov_1m58kj5l7g().b[2][1]++, [...this.dataSource.data, result]);\n    });\n  }\n  applyFilter(event) {\n    cov_1m58kj5l7g().f[9]++;\n    const filterValue = (cov_1m58kj5l7g().s[19]++, event.target.value);\n    cov_1m58kj5l7g().s[20]++;\n    this.dataSource.filter = filterValue.trim().toUpperCase();\n    cov_1m58kj5l7g().s[21]++;\n    this.dataSource.paginator?.firstPage();\n  }\n  static {\n    cov_1m58kj5l7g().s[22]++;\n    this.ctorParameters = () => {\n      cov_1m58kj5l7g().f[10]++;\n      cov_1m58kj5l7g().s[23]++;\n      return [{\n        type: ContractorService\n      }, {\n        type: MatDialog\n      }, {\n        type: NgxSpinnerService\n      }, {\n        type: AlertService\n      }];\n    };\n  }\n  static {\n    cov_1m58kj5l7g().s[24]++;\n    this.propDecorators = {\n      paginator: [{\n        type: ViewChild,\n        args: [MatPaginator]\n      }],\n      sort: [{\n        type: ViewChild,\n        args: [MatSort]\n      }]\n    };\n  }\n};\ncov_1m58kj5l7g().s[25]++;\nContractorsListPageComponent = __decorate([Component({\n  selector: 'app-contractors-list-page',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: true,\n  imports: [MatButtonModule, MatCardModule, MatIconModule, MatFormFieldModule, MatInputModule, MatTableModule, MatSortModule, MatPaginatorModule, MatTooltipModule],\n  styles: [__NG_CLI_RESOURCE__1]\n})], ContractorsListPageComponent);\nexport { ContractorsListPageComponent };", "map": {"version": 3, "names": ["cov_1m58kj5l7g", "actualCoverage", "Component", "ViewChild", "MatDialog", "MatPaginator", "MatSort", "MatTableDataSource", "MatButtonModule", "MatCardModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatPaginatorModule", "MatSortModule", "MatTableModule", "MatTooltipModule", "ContractorDialogComponent", "ContractorService", "AlertService", "NgxSpinnerService", "finalize", "s", "ContractorsListPageComponent", "constructor", "contractorService", "dialog", "spinner", "alert", "f", "displayedColumns", "dataSource", "ngOnInit", "show", "getAll", "pipe", "hide", "subscribe", "next", "contractors", "data", "error", "b", "detail", "ngAfterViewInit", "paginator", "sort", "openContractorDialog", "contractor", "open", "width", "max<PERSON><PERSON><PERSON>", "height", "maxHeight", "afterClosed", "result", "map", "c", "id", "applyFilter", "event", "filterValue", "target", "value", "filter", "trim", "toUpperCase", "firstPage", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-management\\pages\\contractors-list-page\\contractors-list-page.component.ts"], "sourcesContent": ["import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';\nimport { MatDialog } from '@angular/material/dialog';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport { MatTableDataSource } from '@angular/material/table';\n\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { ContractorDialogComponent } from '@contractor-management/components/contractor-dialog/contractor-dialog.component';\nimport { Contractor } from '@contractor-management/models/contractor.model';\nimport { ContractorService } from '@contractor-management/services/contractor.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { finalize } from 'rxjs';\n\n@Component({\n  selector: 'app-contractors-list-page',\n  templateUrl: './contractors-list-page.component.html',\n  styleUrl: './contractors-list-page.component.scss',\n  standalone: true,\n  imports: [\n    MatButtonModule,\n    MatCardModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatTableModule,\n    MatSortModule,\n    MatPaginatorModule,\n    MatTooltipModule,\n  ],\n})\nexport class ContractorsListPageComponent implements OnInit, AfterViewInit {\n  displayedColumns: string[] = [\n    'fullName',\n    'idType',\n    'idNumber',\n    'personalEmail',\n    'corporateEmail',\n    'eps',\n    'gender',\n    'educationLevel',\n    'profession',\n    'actions',\n  ];\n  dataSource = new MatTableDataSource<Contractor>();\n\n  @ViewChild(MatPaginator) paginator!: MatPaginator;\n  @ViewChild(MatSort) sort!: MatSort;\n\n  constructor(\n    private readonly contractorService: ContractorService,\n    private readonly dialog: MatDialog,\n    private readonly spinner: NgxSpinnerService,\n    private readonly alert: AlertService,\n  ) {}\n\n  ngOnInit(): void {\n    this.spinner.show();\n    this.contractorService\n      .getAll()\n      .pipe(finalize(() => this.spinner.hide()))\n      .subscribe({\n        next: (contractors) => (this.dataSource.data = contractors),\n        error: (error) => {\n          this.alert.error(error.error?.detail ?? 'Error al cargar contratistas');\n        },\n      });\n  }\n\n  ngAfterViewInit(): void {\n    this.dataSource.paginator = this.paginator;\n    this.dataSource.sort = this.sort;\n  }\n\n  openContractorDialog(contractor?: Contractor): void {\n    this.dialog\n      .open(ContractorDialogComponent, {\n        width: '95%',\n        maxWidth: '800px',\n        height: 'auto',\n        maxHeight: '90vh',\n        data: contractor,\n      })\n      .afterClosed()\n      .subscribe((result?: Contractor) => {\n        if (!result) return;\n\n        this.dataSource.data = contractor\n          ? this.dataSource.data.map((c) => (c.id === result.id ? result : c))\n          : [...this.dataSource.data, result];\n      });\n  }\n\n  applyFilter(event: Event): void {\n    const filterValue = (event.target as HTMLInputElement).value;\n    this.dataSource.filter = filterValue.trim().toUpperCase();\n    this.dataSource.paginator?.firstPage();\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAaS;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;AAbT,SAAwBE,SAAS,EAAUC,SAAS,QAAQ,eAAe;AAC3E,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,kBAAkB,QAAQ,yBAAyB;AAE5D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,yBAAyB,QAAQ,iFAAiF;AAE3H,SAASC,iBAAiB,QAAQ,oDAAoD;AACtF,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,iBAAiB,QAAQ,aAAa;AAC/C,SAASC,QAAQ,QAAQ,MAAM;AAACrB,cAAA,GAAAsB,CAAA;AAmBzB,IAAMC,4BAA4B,GAAlC,MAAMA,4BAA4B;EAkBvCC,YACmBC,iBAAoC,EACpCC,MAAiB,EACjBC,OAA0B,EAC1BC,KAAmB;IAAA5B,cAAA,GAAA6B,CAAA;IAAA7B,cAAA,GAAAsB,CAAA;IAHnB,KAAAG,iBAAiB,GAAjBA,iBAAiB;IAAmBzB,cAAA,GAAAsB,CAAA;IACpC,KAAAI,MAAM,GAANA,MAAM;IAAW1B,cAAA,GAAAsB,CAAA;IACjB,KAAAK,OAAO,GAAPA,OAAO;IAAmB3B,cAAA,GAAAsB,CAAA;IAC1B,KAAAM,KAAK,GAALA,KAAK;IAAc5B,cAAA,GAAAsB,CAAA;IArBtC,KAAAQ,gBAAgB,GAAa,CAC3B,UAAU,EACV,QAAQ,EACR,UAAU,EACV,eAAe,EACf,gBAAgB,EAChB,KAAK,EACL,QAAQ,EACR,gBAAgB,EAChB,YAAY,EACZ,SAAS,CACV;IAAC9B,cAAA,GAAAsB,CAAA;IACF,KAAAS,UAAU,GAAG,IAAIxB,kBAAkB,EAAc;EAU9C;EAEHyB,QAAQA,CAAA;IAAAhC,cAAA,GAAA6B,CAAA;IAAA7B,cAAA,GAAAsB,CAAA;IACN,IAAI,CAACK,OAAO,CAACM,IAAI,EAAE;IAACjC,cAAA,GAAAsB,CAAA;IACpB,IAAI,CAACG,iBAAiB,CACnBS,MAAM,EAAE,CACRC,IAAI,CAACd,QAAQ,CAAC,MAAM;MAAArB,cAAA,GAAA6B,CAAA;MAAA7B,cAAA,GAAAsB,CAAA;MAAA,WAAI,CAACK,OAAO,CAACS,IAAI,EAAE;IAAF,CAAE,CAAC,CAAC,CACzCC,SAAS,CAAC;MACTC,IAAI,EAAGC,WAAW,IAAM;QAAAvC,cAAA,GAAA6B,CAAA;QAAA7B,cAAA,GAAAsB,CAAA;QAAA,WAAI,CAACS,UAAU,CAACS,IAAI,GAAGD,WAAW;MAAX,CAAY;MAC3DE,KAAK,EAAGA,KAAK,IAAI;QAAAzC,cAAA,GAAA6B,CAAA;QAAA7B,cAAA,GAAAsB,CAAA;QACf,IAAI,CAACM,KAAK,CAACa,KAAK,CAAC,CAAAzC,cAAA,GAAA0C,CAAA,UAAAD,KAAK,CAACA,KAAK,EAAEE,MAAM,MAAA3C,cAAA,GAAA0C,CAAA,UAAI,8BAA8B,EAAC;MACzE;KACD,CAAC;EACN;EAEAE,eAAeA,CAAA;IAAA5C,cAAA,GAAA6B,CAAA;IAAA7B,cAAA,GAAAsB,CAAA;IACb,IAAI,CAACS,UAAU,CAACc,SAAS,GAAG,IAAI,CAACA,SAAS;IAAC7C,cAAA,GAAAsB,CAAA;IAC3C,IAAI,CAACS,UAAU,CAACe,IAAI,GAAG,IAAI,CAACA,IAAI;EAClC;EAEAC,oBAAoBA,CAACC,UAAuB;IAAAhD,cAAA,GAAA6B,CAAA;IAAA7B,cAAA,GAAAsB,CAAA;IAC1C,IAAI,CAACI,MAAM,CACRuB,IAAI,CAAChC,yBAAyB,EAAE;MAC/BiC,KAAK,EAAE,KAAK;MACZC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE,MAAM;MACjBb,IAAI,EAAEQ;KACP,CAAC,CACDM,WAAW,EAAE,CACbjB,SAAS,CAAEkB,MAAmB,IAAI;MAAAvD,cAAA,GAAA6B,CAAA;MAAA7B,cAAA,GAAAsB,CAAA;MACjC,IAAI,CAACiC,MAAM,EAAE;QAAAvD,cAAA,GAAA0C,CAAA;QAAA1C,cAAA,GAAAsB,CAAA;QAAA;MAAA,CAAO;QAAAtB,cAAA,GAAA0C,CAAA;MAAA;MAAA1C,cAAA,GAAAsB,CAAA;MAEpB,IAAI,CAACS,UAAU,CAACS,IAAI,GAAGQ,UAAU,IAAAhD,cAAA,GAAA0C,CAAA,UAC7B,IAAI,CAACX,UAAU,CAACS,IAAI,CAACgB,GAAG,CAAEC,CAAC,IAAM;QAAAzD,cAAA,GAAA6B,CAAA;QAAA7B,cAAA,GAAAsB,CAAA;QAAA,OAAAmC,CAAC,CAACC,EAAE,KAAKH,MAAM,CAACG,EAAE,IAAA1D,cAAA,GAAA0C,CAAA,UAAGa,MAAM,KAAAvD,cAAA,GAAA0C,CAAA,UAAGe,CAAC;MAAD,CAAE,CAAC,KAAAzD,cAAA,GAAA0C,CAAA,UAClE,CAAC,GAAG,IAAI,CAACX,UAAU,CAACS,IAAI,EAAEe,MAAM,CAAC;IACvC,CAAC,CAAC;EACN;EAEAI,WAAWA,CAACC,KAAY;IAAA5D,cAAA,GAAA6B,CAAA;IACtB,MAAMgC,WAAW,IAAA7D,cAAA,GAAAsB,CAAA,QAAIsC,KAAK,CAACE,MAA2B,CAACC,KAAK;IAAC/D,cAAA,GAAAsB,CAAA;IAC7D,IAAI,CAACS,UAAU,CAACiC,MAAM,GAAGH,WAAW,CAACI,IAAI,EAAE,CAACC,WAAW,EAAE;IAAClE,cAAA,GAAAsB,CAAA;IAC1D,IAAI,CAACS,UAAU,CAACc,SAAS,EAAEsB,SAAS,EAAE;EACxC;;;;;;;;;;;;;;;;;;;;;cAnDChE,SAAS;QAAAiE,IAAA,GAAC/D,YAAY;MAAA;;cACtBF,SAAS;QAAAiE,IAAA,GAAC9D,OAAO;MAAA;;;;;AAhBPiB,4BAA4B,GAAA8C,UAAA,EAjBxCnE,SAAS,CAAC;EACToE,QAAQ,EAAE,2BAA2B;EACrCC,QAAA,EAAAC,oBAAqD;EAErDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPlE,eAAe,EACfC,aAAa,EACbE,aAAa,EACbD,kBAAkB,EAClBE,cAAc,EACdG,cAAc,EACdD,aAAa,EACbD,kBAAkB,EAClBG,gBAAgB,CACjB;;CACF,CAAC,C,EACWO,4BAA4B,CAmExC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}