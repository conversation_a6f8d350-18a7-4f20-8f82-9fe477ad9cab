import {
  AfterViewInit,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { NgxSpinnerService } from 'ngx-spinner';
import { finalize } from 'rxjs/operators';

import { DatePipe } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AssociateContractorDialogComponent } from '@contract-management/components/associated-contractors-list/associate-contractor-dialog/associate-contractor-dialog.component';
import { Contract } from '@contract-management/models/contract.model';
import { ContractorContract } from '@contract-management/models/contractor-contract.model';
import { ContractorContractService } from '@contract-management/services/contractor-contract.service';
import { AlertService } from '@shared/services/alert.service';

@Component({
  selector: 'app-contract-associated-contractors',
  templateUrl: './associated-contractors-list.component.html',
  styleUrl: './associated-contractors-list.component.scss',
  standalone: true,
  imports: [
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatTableModule,
    MatSortModule,
    MatTooltipModule,
    DatePipe,
  ],
})
export class AssociatedContractorsListComponent
  implements OnInit, AfterViewInit
{
  @Input() contract!: Contract;
  @Input() isEarlyTerminationDisabled = false;
  @Input() isContractFinished = false;
  @Output() contractorContractsChanged = new EventEmitter<void>();
  @Output() contractorAdded = new EventEmitter<void>();

  contractorContractsColumns: string[] = [
    'fullName',
    'idNumber',
    'personalEmail',
    'corporateEmail',
    'subscriptionDate',
    'contractStartDate',
    'contractEndDate',
  ];
  dataSource = new MatTableDataSource<ContractorContract>();

  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private readonly dialog: MatDialog,
    private readonly contractorContractService: ContractorContractService,
    private readonly alert: AlertService,
    private readonly spinner: NgxSpinnerService,
  ) {}

  ngOnInit(): void {
    this.loadContractorContracts();
  }

  ngAfterViewInit(): void {
    this.dataSource.sort = this.sort;
  }

  loadContractorContracts(): void {
    if (this.contract?.id) {
      this.spinner.show();
      this.contractorContractService
        .getAllByContractId(this.contract.id)
        .pipe(finalize(() => this.spinner.hide()))
        .subscribe({
          next: (data) => (this.dataSource.data = data),
          error: (_) => {
            this.alert.error(error.error?.detail ?? 'Error al cargar los contratos de contratistas');
          },
        });
    }
  }

  updateContractorContracts(
    earlyTerminationDate: string,
    contractorContractId: number,
  ): void {
    const contractorContract = this.dataSource.data.find(
      (cc) => cc.id === contractorContractId,
    );
    if (contractorContract) {
      contractorContract.contractEndDate = earlyTerminationDate;
      this.isEarlyTerminationDisabled = false;
    }
  }

  getLatestContractorContract(): ContractorContract | undefined {
    return this.dataSource.data[this.dataSource.data.length - 1];
  }

  openAssociateContractorDialog(): void {
    this.dialog
      .open(AssociateContractorDialogComponent, {
        width: '800px',
        data: { contractId: this.contract.id },
      })
      .afterClosed()
      .subscribe((result) => {
        if (result?.success) {
          this.loadContractorContracts();
          this.contractorContractsChanged.emit();
          if (result.contractorAdded) {
            this.contractorAdded.emit();
            this.isEarlyTerminationDisabled = true;
          }
        }
      });
  }
}
