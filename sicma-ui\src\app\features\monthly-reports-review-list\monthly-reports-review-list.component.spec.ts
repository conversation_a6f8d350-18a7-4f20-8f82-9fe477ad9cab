import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { MonthlyReportSupervisorExportModel } from '@contractor-dashboard/models/monthly_report_supervisor_export.model';
import { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';
import { AuthService } from '@core/auth/services/auth.service';
import { AlertService } from '@shared/services/alert.service';
import { SupervisorService } from '@supervisor-management/services/supervisor.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { of, throwError } from 'rxjs';

import { MonthlyReportsReviewListComponent } from './monthly-reports-review-list.component';

describe('MonthlyReportsReviewListComponent', () => {
  let component: MonthlyReportsReviewListComponent;
  let fixture: ComponentFixture<MonthlyReportsReviewListComponent>;
  let monthlyReportService: jasmine.SpyObj<MonthlyReportService>;
  let supervisorService: jasmine.SpyObj<SupervisorService>;
  let authService: jasmine.SpyObj<AuthService>;
  let alertService: jasmine.SpyObj<AlertService>;
  let spinnerService: jasmine.SpyObj<NgxSpinnerService>;
  let dialog: jasmine.SpyObj<MatDialog>;

  const mockUser = {
    id: 1,
    username: '<EMAIL>',
    profiles: [],
  };

  const mockSupervisor = {
    id: 1,
    fullName: 'Test Supervisor',
    email: '<EMAIL>',
    signatureFileKey: undefined,
    signatureFileUrl: undefined,
    idNumber: 123456789,
    position: 'Test Position',
    idType: { id: 1, name: 'CC' },
  };

  const mockReport: MonthlyReportSupervisorExportModel = {
    report_id: 1,
    report_number: 1,
    start_date: '2024-01-01',
    end_date: '2024-01-31',
    contract_number: 123,
    contract_year: 2024,
    contractor_full_name: 'Test Contractor',
    contract_dependency: 'Test Dependency',
    contract_group: 'Test Group',
    total_value: 1000,
    review_status_id: 1,
    review_status_name: 'Pendiente de revisión',
  };

  beforeEach(() => {
    const monthlyReportServiceSpy = jasmine.createSpyObj(
      'MonthlyReportService',
      ['getFilteredReportsBySupervisorEmail', 'getById'],
    );
    const supervisorServiceSpy = jasmine.createSpyObj('SupervisorService', [
      'getByEmail',
    ]);
    const authServiceSpy = jasmine.createSpyObj('AuthService', [
      'getCurrentUser',
    ]);
    const alertServiceSpy = jasmine.createSpyObj('AlertService', ['error']);
    const spinnerServiceSpy = jasmine.createSpyObj('NgxSpinnerService', [
      'show',
      'hide',
    ]);
    const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
    dialogRefSpy.afterClosed.and.returnValue(of(true));
    const dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);
    dialogSpy.open.and.returnValue(dialogRefSpy);

    TestBed.configureTestingModule({
      imports: [
        MonthlyReportsReviewListComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
      ],
      providers: [
        { provide: MonthlyReportService, useValue: monthlyReportServiceSpy },
        { provide: SupervisorService, useValue: supervisorServiceSpy },
        { provide: AuthService, useValue: authServiceSpy },
        { provide: AlertService, useValue: alertServiceSpy },
        { provide: NgxSpinnerService, useValue: spinnerServiceSpy },
        { provide: MatDialog, useValue: dialogSpy },
      ],
    });

    fixture = TestBed.createComponent(MonthlyReportsReviewListComponent);
    component = fixture.componentInstance;
    monthlyReportService = TestBed.inject(
      MonthlyReportService,
    ) as jasmine.SpyObj<MonthlyReportService>;
    supervisorService = TestBed.inject(
      SupervisorService,
    ) as jasmine.SpyObj<SupervisorService>;
    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;
    spinnerService = TestBed.inject(
      NgxSpinnerService,
    ) as jasmine.SpyObj<NgxSpinnerService>;
    dialog = TestBed.inject(MatDialog) as jasmine.SpyObj<MatDialog>;

    const supervisorWithSignature = {
      ...mockSupervisor,
      signatureFileKey: 'key123',
      signatureFileUrl: 'http://example.com/signature.png',
    };

    authService.getCurrentUser.and.returnValue(mockUser);
    supervisorService.getByEmail.and.returnValue(of(supervisorWithSignature));
    monthlyReportService.getFilteredReportsBySupervisorEmail.and.returnValue(
      of([mockReport]),
    );
    const mockMonthlyReport = {
      id: 1,
      reportNumber: 1,
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-01-31'),
      creationDate: new Date('2024-01-01'),
      contractorContractId: 1,
      currentReviewStatus: { id: 1, name: 'Pendiente de revisión' },
    };
    monthlyReportService.getById.and.returnValue(of(mockMonthlyReport));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load monthly reports on init', () => {
    component.ngOnInit();
    expect(spinnerService.show).toHaveBeenCalled();
    expect(supervisorService.getByEmail).toHaveBeenCalledWith(
      mockUser.username,
    );
    expect(
      monthlyReportService.getFilteredReportsBySupervisorEmail,
    ).toHaveBeenCalledWith(mockUser.username);
    expect(component.dataSource.data).toEqual([mockReport]);
    expect(component.supervisorFullName).toBe(mockSupervisor.fullName);
  });

  it('should handle error when user is not found', () => {
    authService.getCurrentUser.and.returnValue(null);
    component.ngOnInit();
    expect(alertService.error).toHaveBeenCalledWith(
      'No se encontró información del usuario actual',
    );
  });

  it('should handle error when loading supervisor information', () => {
    supervisorService.getByEmail.and.returnValue(throwError(() => new Error()));
    component.ngOnInit();
    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar información del supervisor',
    );
  });

  it('should handle error when loading monthly reports', () => {
    monthlyReportService.getFilteredReportsBySupervisorEmail.and.returnValue(
      throwError(() => new Error()),
    );
    component.ngOnInit();
    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar los informes',
    );
  });

  it('should apply filter', () => {
    component.ngOnInit();
    const event = { target: { value: 'test' } } as unknown as Event;
    component.dataSource.paginator = component.paginator;
    component.applyFilter(event);
    expect(component.dataSource.filter).toBe('test');
  });

  it('should handle error when opening report details without report', () => {
    component.openReportDetails(
      null as unknown as MonthlyReportSupervisorExportModel,
    );
    expect(alertService.error).toHaveBeenCalledWith(
      'No se encontró información del informe',
    );
  });

  it('should handle error when opening report details without user', () => {
    authService.getCurrentUser.and.returnValue(null);
    component.openReportDetails(mockReport);
    expect(alertService.error).toHaveBeenCalledWith(
      'No se encontró información del usuario actual',
    );
  });

  it('should open digital signature dialog when supervisor has no signature', () => {
    supervisorService.getByEmail.and.returnValue(of(mockSupervisor));

    const dialogRef = {
      afterClosed: () => of(false),
    } as MatDialogRef<unknown>;
    dialog.open.and.returnValue(dialogRef);

    component.openReportDetails(mockReport);

    expect(dialog.open).toHaveBeenCalledWith(jasmine.any(Function), {
      width: '90vw',
      height: '90vh',
      maxWidth: '100vw',
      maxHeight: '100vh',
      data: { report: jasmine.any(Object) },
    });
  });

  it('should open report dialog when supervisor has signature', () => {
    const dialogRef = {
      afterClosed: () => of(false),
    } as MatDialogRef<unknown>;
    dialog.open.and.returnValue(dialogRef);

    component.openReportDetails(mockReport);

    expect(dialog.open).toHaveBeenCalledWith(jasmine.any(Function), {
      width: '90vw',
      height: '90vh',
      maxWidth: '100vw',
      maxHeight: '100vh',
      data: { report: jasmine.any(Object) },
    });
  });

  it('should reload reports when dialog is closed with result', () => {
    const dialogRef = {
      afterClosed: () => of(true),
    } as MatDialogRef<unknown>;
    dialog.open.and.returnValue(dialogRef);

    spyOn(component, 'loadMonthlyReports');
    component.openReportDetails(mockReport);
    expect(component.loadMonthlyReports).toHaveBeenCalled();
  });

  it('should handle error when verifying supervisor information', () => {
    supervisorService.getByEmail.and.returnValue(throwError(() => new Error()));
    component.openReportDetails(mockReport);
    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar información del supervisor',
    );
  });
});
