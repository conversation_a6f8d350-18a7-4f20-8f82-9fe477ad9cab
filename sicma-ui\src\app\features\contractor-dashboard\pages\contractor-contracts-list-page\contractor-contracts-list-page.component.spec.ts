import { HttpClientTestingModule } from '@angular/common/http/testing';
import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { Router } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { ContractValues } from '@contract-management/models/contract-values.model';
import { Contract } from '@contract-management/models/contract.model';
import { Status } from '@contract-management/models/status.model';
import { ContractValuesService } from '@contract-management/services/contract-values.service';
import { ContractService } from '@contract-management/services/contract.service';
import { Contractor } from '@contractor-management/models/contractor.model';
import { ContractorService } from '@contractor-management/services/contractor.service';
import { AuthService } from '@core/auth/services/auth.service';
import { AlertService } from '@shared/services/alert.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { of, throwError } from 'rxjs';
import { ContractorContractsListPageComponent } from './contractor-contracts-list-page.component';

describe('ContractorContractsListPageComponent', () => {
  let component: ContractorContractsListPageComponent;
  let fixture: ComponentFixture<ContractorContractsListPageComponent>;
  let contractService: jasmine.SpyObj<ContractService>;
  let contractValuesService: jasmine.SpyObj<ContractValuesService>;
  let contractorService: jasmine.SpyObj<ContractorService>;
  let authService: jasmine.SpyObj<AuthService>;
  let alertService: jasmine.SpyObj<AlertService>;
  let spinnerService: jasmine.SpyObj<NgxSpinnerService>;
  let router: jasmine.SpyObj<Router>;

  const mockStatus: Status = {
    id: 1,
    name: 'Active',
  };

  const mockContract: Contract = {
    id: 1,
    contractNumber: 1001,
    object: 'Test Contract',
    status: mockStatus,
    monthlyPayment: 1000000,
    rup: true,
    secopCode: 123,
    addition: false,
    cession: false,
    settled: false,
    causesSelectionId: 1,
    managementSupportId: 1,
    contractClassId: 1,
  };

  const mockContractValues: ContractValues = {
    numericValue: 1000000,
    madsValue: 0,
    isOtherEntity: false,
    subscriptionDate: '2024-01-01',
    cdp: 123,
    cdpEntityId: 1,
    cdpEntity: { id: 1, name: 'Test Entity' },
    startDate: '2024-01-01',
    endDate: '2024-12-31',
  };

  beforeEach(() => {
    const contractServiceSpy = jasmine.createSpyObj('ContractService', [
      'getByContractorIdNumber',
    ]);
    const contractValuesServiceSpy = jasmine.createSpyObj(
      'ContractValuesService',
      ['getAllByContractId'],
    );
    const contractorServiceSpy = jasmine.createSpyObj('ContractorService', [
      'getByEmail',
      'getByIdNumber',
    ]);
    const authServiceSpy = jasmine.createSpyObj('AuthService', [
      'getCurrentUser',
    ]);
    const alertServiceSpy = jasmine.createSpyObj('AlertService', [
      'error',
      'info',
    ]);
    const spinnerServiceSpy = jasmine.createSpyObj('NgxSpinnerService', [
      'show',
      'hide',
    ]);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    TestBed.configureTestingModule({
      imports: [
        ContractorContractsListPageComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        RouterTestingModule,
        MatPaginatorModule,
        MatSortModule,
      ],
      providers: [
        { provide: ContractService, useValue: contractServiceSpy },
        { provide: ContractValuesService, useValue: contractValuesServiceSpy },
        { provide: ContractorService, useValue: contractorServiceSpy },
        { provide: AuthService, useValue: authServiceSpy },
        { provide: AlertService, useValue: alertServiceSpy },
        { provide: NgxSpinnerService, useValue: spinnerServiceSpy },
        { provide: Router, useValue: routerSpy },
      ],
    });

    fixture = TestBed.createComponent(ContractorContractsListPageComponent);
    component = fixture.componentInstance;
    contractService = TestBed.inject(
      ContractService,
    ) as jasmine.SpyObj<ContractService>;
    contractValuesService = TestBed.inject(
      ContractValuesService,
    ) as jasmine.SpyObj<ContractValuesService>;
    contractorService = TestBed.inject(
      ContractorService,
    ) as jasmine.SpyObj<ContractorService>;
    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;
    spinnerService = TestBed.inject(
      NgxSpinnerService,
    ) as jasmine.SpyObj<NgxSpinnerService>;
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with empty data', () => {
    expect(component.dataSource.data).toEqual([]);
    expect(component.contractorFullName).toBe('');
    expect(component.contractorIdNumber).toBeUndefined();
  });

  it('should load contractor contracts on init when user is authenticated', fakeAsync(() => {
    const mockUser = {
      id: 1,
      username: '<EMAIL>',
      profiles: [],
    };
    const mockContractor: Contractor = {
      id: 1,
      fullName: 'Test Contractor',
      idNumber: 123456789,
      personalEmail: '<EMAIL>',
      idType: { id: 1, name: 'CC' },
    };

    authService.getCurrentUser.and.returnValue(mockUser);
    contractorService.getByEmail.and.returnValue(of(mockContractor));
    contractorService.getByIdNumber.and.returnValue(of(mockContractor));
    contractService.getByContractorIdNumber.and.returnValue(of([mockContract]));
    contractValuesService.getAllByContractId.and.returnValue(
      of([mockContractValues]),
    );

    component.ngOnInit();
    tick();

    expect(spinnerService.show).toHaveBeenCalled();
    expect(contractorService.getByEmail).toHaveBeenCalledWith(
      mockUser.username,
    );
    expect(contractorService.getByIdNumber).toHaveBeenCalledWith(
      mockContractor.idNumber,
    );
    expect(contractService.getByContractorIdNumber).toHaveBeenCalledWith(
      mockContractor.idNumber,
    );
    expect(component.contractorFullName).toBe(mockContractor.fullName);
    expect(component.dataSource.data.length).toBe(1);
    expect(spinnerService.hide).toHaveBeenCalled();
  }));

  it('should show error when user is not authenticated', fakeAsync(() => {
    authService.getCurrentUser.and.returnValue(null);

    component.ngOnInit();
    tick();

    expect(alertService.error).toHaveBeenCalledWith('Usuario no autenticado');
    expect(spinnerService.hide).toHaveBeenCalled();
  }));

  it('should handle error when getting contractor by email fails', fakeAsync(() => {
    const mockUser = {
      id: 1,
      username: '<EMAIL>',
      profiles: [],
    };
    authService.getCurrentUser.and.returnValue(mockUser);
    contractorService.getByEmail.and.returnValue(throwError(() => new Error()));

    component.ngOnInit();
    tick();

    expect(alertService.info).toHaveBeenCalledWith(
      'El Contrastista no tiene contratos asignados',
    );
    expect(spinnerService.hide).toHaveBeenCalled();
  }));

  it('should handle error when getting contractor details fails', fakeAsync(() => {
    const mockUser = {
      id: 1,
      username: '<EMAIL>',
      profiles: [],
    };
    const mockContractor: Contractor = {
      id: 1,
      fullName: 'Test Contractor',
      idNumber: 123456789,
      personalEmail: '<EMAIL>',
      idType: { id: 1, name: 'CC' },
    };

    authService.getCurrentUser.and.returnValue(mockUser);
    contractorService.getByEmail.and.returnValue(of(mockContractor));
    contractorService.getByIdNumber.and.returnValue(
      throwError(() => new Error()),
    );

    component.ngOnInit();
    tick();

    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar los datos del contratista',
    );
    expect(spinnerService.hide).toHaveBeenCalled();
  }));

  it('should handle error when getting contracts fails', fakeAsync(() => {
    const mockUser = {
      id: 1,
      username: '<EMAIL>',
      profiles: [],
    };
    const mockContractor: Contractor = {
      id: 1,
      fullName: 'Test Contractor',
      idNumber: 123456789,
      personalEmail: '<EMAIL>',
      idType: { id: 1, name: 'CC' },
    };

    authService.getCurrentUser.and.returnValue(mockUser);
    contractorService.getByEmail.and.returnValue(of(mockContractor));
    contractorService.getByIdNumber.and.returnValue(of(mockContractor));
    contractService.getByContractorIdNumber.and.returnValue(
      throwError(() => new Error()),
    );

    component.ngOnInit();
    tick();

    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar los contratos',
    );
  }));

  it('should handle error when getting contract values fails', fakeAsync(() => {
    const mockUser = {
      id: 1,
      username: '<EMAIL>',
      profiles: [],
    };
    const mockContractor: Contractor = {
      id: 1,
      fullName: 'Test Contractor',
      idNumber: 123456789,
      personalEmail: '<EMAIL>',
      idType: { id: 1, name: 'CC' },
    };

    authService.getCurrentUser.and.returnValue(mockUser);
    contractorService.getByEmail.and.returnValue(of(mockContractor));
    contractorService.getByIdNumber.and.returnValue(of(mockContractor));
    contractService.getByContractorIdNumber.and.returnValue(of([mockContract]));
    contractValuesService.getAllByContractId.and.returnValue(
      throwError(() => new Error()),
    );

    component.ngOnInit();
    tick();

    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar los valores de los contratos',
    );
  }));

  it('should apply filter correctly', () => {
    const event = new Event('input');
    Object.defineProperty(event, 'target', { value: { value: 'test' } });
    component.applyFilter(event);
    expect(component.dataSource.filter).toBe('test');
  });

  it('should navigate to contract details when visualizing contract', () => {
    const contract = {
      id: 1,
      contractNumber: 1001,
      object: 'Test Contract',
      status: { name: 'Active' },
    };
    component.handleVisualizeContract(contract);
    expect(router.navigate).toHaveBeenCalledWith(['/mis-contratos', 1]);
  });

  it('should set up paginator and sort after view init', () => {
    fixture.detectChanges();
    component.ngAfterViewInit();
    expect(component.dataSource.paginator).toBeTruthy();
    expect(component.dataSource.sort).toBeTruthy();
  });
});