
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for app/features/contract-management/components/contract-values-dialog</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../../index.html">All files</a> app/features/contract-management/components/contract-values-dialog</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">66.66% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>18/27</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">54.16% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>13/24</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">83.33% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>5/6</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">65.38% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>17/26</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="contract-values-dialog.component.ts"><a href="contract-values-dialog.component.ts.html">contract-values-dialog.component.ts</a></td>
	<td data-value="66.66" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 66%"></div><div class="cover-empty" style="width: 34%"></div></div>
	</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="27" class="abs medium">18/27</td>
	<td data-value="54.16" class="pct medium">54.16%</td>
	<td data-value="24" class="abs medium">13/24</td>
	<td data-value="83.33" class="pct high">83.33%</td>
	<td data-value="6" class="abs high">5/6</td>
	<td data-value="65.38" class="pct medium">65.38%</td>
	<td data-value="26" class="abs medium">17/26</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-15T23:53:42.515Z
            </div>
        <script src="../../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../../sorter.js"></script>
        <script src="../../../../../block-navigation.js"></script>
    </body>
</html>
    