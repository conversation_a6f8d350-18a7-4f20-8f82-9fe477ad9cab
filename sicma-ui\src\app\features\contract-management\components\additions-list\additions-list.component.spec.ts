import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ContractValues } from '@contract-management/models/contract-values.model';
import { Contract } from '@contract-management/models/contract.model';
import { of } from 'rxjs';
import { ContractValuesDialogComponent } from '../contract-values-dialog/contract-values-dialog.component';
import { AdditionsListComponent } from './additions-list.component';

describe('AdditionsListComponent', () => {
  let component: AdditionsListComponent;
  let fixture: ComponentFixture<AdditionsListComponent>;
  let dialog: jasmine.SpyObj<MatDialog>;

  const mockContractValues: ContractValues[] = [
    {
      id: 1,
      numericValue: 1000,
      madsValue: 1000,
      otherValue: 1000,
      futureValidityValue: 1000,
      cdpEntityId: 1,
      cdp: 1001,
      rp: 1002,
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      subscriptionDate: '2024-01-01',
      isOtherEntity: false,
      contractId: 1,
      cdpEntity: {
        id: 1,
        name: 'Entity1',
      },
    },
    {
      id: 2,
      numericValue: 2000,
      madsValue: 2000,
      otherValue: 2000,
      futureValidityValue: 2000,
      cdpEntityId: 2,
      cdp: 2001,
      rp: 2002,
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      subscriptionDate: '2024-01-01',
      isOtherEntity: true,
      contractId: 1,
      cdpEntity: {
        id: 2,
        name: 'Entity2',
      },
    },
  ];

  const mockContract: Contract = {
    id: 1,
    contractNumber: 123,
    monthlyPayment: 1000000,
    object: 'Test Contract',
    rup: true,
    secopCode: 123456,
    addition: false,
    cession: false,
    settled: false,
    contractTypeId: 1,
    statusId: 1,
    causesSelectionId: 1,
    managementSupportId: 1,
    contractClassId: 1,
    contractValues: mockContractValues,
  };

  beforeEach(async () => {
    const dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);
    dialogSpy.open.and.returnValue({
      afterClosed: () => of(undefined),
    } as MatDialogRef<unknown>);

    await TestBed.configureTestingModule({
      imports: [
        AdditionsListComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        MatTableModule,
        MatPaginatorModule,
        MatSortModule,
      ],
      providers: [{ provide: MatDialog, useValue: dialogSpy }],
    }).compileComponents();

    fixture = TestBed.createComponent(AdditionsListComponent);
    component = fixture.componentInstance;
    dialog = TestBed.inject(MatDialog) as jasmine.SpyObj<MatDialog>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with contract values', () => {
    component.contract = mockContract;
    component.ngOnInit();
    expect(component.dataSource.data.length).toBe(1);
    expect(component.dataSource.data.some((cv) => cv.isOtherEntity)).toBe(true);
  });

  it('should initialize with empty array when contract has no values', () => {
    component.contract = { ...mockContract, contractValues: undefined };
    component.ngOnInit();
    expect(component.dataSource.data).toEqual([]);
  });

  it('should set up sort after view init', () => {
    component.contract = mockContract;
    component.ngOnInit();
    fixture.detectChanges();
    component.ngAfterViewInit();

    expect(component.dataSource.sort).toBeTruthy();
  });

  it('should calculate total numeric value', () => {
    component.contract = mockContract;
    component.ngOnInit();
    const total = component.getTotalNumericValue();
    expect(total).toBe(5000);
  });

  it('should open dialog for new contract value', () => {
    component.contract = mockContract;
    component.ngOnInit();

    const newContractValue: ContractValues = {
      id: 3,
      numericValue: 3000,
      madsValue: 3000,
      otherValue: 3000,
      futureValidityValue: 3000,
      cdpEntityId: 3,
      cdp: 3001,
      rp: 3002,
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      subscriptionDate: '2024-01-01',
      isOtherEntity: false,
      contractId: 1,
      cdpEntity: {
        id: 3,
        name: 'Entity3',
      },
    };

    const mockDialogRef = {
      afterClosed: () => of(newContractValue),
    } as MatDialogRef<unknown>;

    dialog.open.and.returnValue(mockDialogRef);

    component.openContractValuesDialog();

    expect(dialog.open).toHaveBeenCalledWith(ContractValuesDialogComponent, {
      width: '1000px',
      data: {
        contractValue: undefined,
        contract: mockContract,
      },
    });

    expect(component.dataSource.data.length).toBe(2);
    expect(component.dataSource.data).toContain(newContractValue);
  });

  it('should open dialog for existing contract value', () => {
    component.contract = mockContract;
    component.ngOnInit();

    const updatedContractValue: ContractValues = {
      ...mockContractValues[1],
      numericValue: 1500,
      madsValue: 1500,
    };

    const mockDialogRef = {
      afterClosed: () => of(updatedContractValue),
    } as MatDialogRef<unknown>;

    dialog.open.and.returnValue(mockDialogRef);

    component.openContractValuesDialog(mockContractValues[1]);

    expect(dialog.open).toHaveBeenCalledWith(ContractValuesDialogComponent, {
      width: '1000px',
      data: {
        contractValue: jasmine.objectContaining({
          id: 2,
          numericValue: 2000,
          isOtherEntity: true,
        }),
        contract: mockContract,
      },
    });

    expect(component.dataSource.data[0]).toEqual(updatedContractValue);
  });

  it('should not update data when dialog is closed without result', () => {
    component.contract = mockContract;
    component.ngOnInit();
    const initialData = [...component.dataSource.data];

    const mockDialogRef = {
      afterClosed: () => of(undefined),
    } as MatDialogRef<unknown>;

    dialog.open.and.returnValue(mockDialogRef);

    component.openContractValuesDialog();

    expect(component.dataSource.data).toEqual(initialData);
  });
});
