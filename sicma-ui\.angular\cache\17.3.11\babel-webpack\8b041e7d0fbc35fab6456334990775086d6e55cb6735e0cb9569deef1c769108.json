{"ast": null, "code": "function cov_soh2jbacz() {\n  var path = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-dashboard\\\\components\\\\monthly-reports-tab\\\\select-period-dialog\\\\select-period-dialog.component.ts\";\n  var hash = \"12054d2faf80ed02de6fcbf2b705a243414bc89c\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-dashboard\\\\components\\\\monthly-reports-tab\\\\select-period-dialog\\\\select-period-dialog.component.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 16,\n          column: 0\n        },\n        end: {\n          line: 16,\n          column: 29\n        }\n      },\n      \"1\": {\n        start: {\n          line: 17,\n          column: 34\n        },\n        end: {\n          line: 94,\n          column: 1\n        }\n      },\n      \"2\": {\n        start: {\n          line: 19,\n          column: 8\n        },\n        end: {\n          line: 19,\n          column: 35\n        }\n      },\n      \"3\": {\n        start: {\n          line: 20,\n          column: 8\n        },\n        end: {\n          line: 20,\n          column: 25\n        }\n      },\n      \"4\": {\n        start: {\n          line: 21,\n          column: 8\n        },\n        end: {\n          line: 21,\n          column: 43\n        }\n      },\n      \"5\": {\n        start: {\n          line: 22,\n          column: 8\n        },\n        end: {\n          line: 22,\n          column: 57\n        }\n      },\n      \"6\": {\n        start: {\n          line: 23,\n          column: 8\n        },\n        end: {\n          line: 23,\n          column: 41\n        }\n      },\n      \"7\": {\n        start: {\n          line: 24,\n          column: 8\n        },\n        end: {\n          line: 24,\n          column: 31\n        }\n      },\n      \"8\": {\n        start: {\n          line: 25,\n          column: 8\n        },\n        end: {\n          line: 25,\n          column: 29\n        }\n      },\n      \"9\": {\n        start: {\n          line: 26,\n          column: 8\n        },\n        end: {\n          line: 26,\n          column: 26\n        }\n      },\n      \"10\": {\n        start: {\n          line: 27,\n          column: 8\n        },\n        end: {\n          line: 27,\n          column: 35\n        }\n      },\n      \"11\": {\n        start: {\n          line: 28,\n          column: 8\n        },\n        end: {\n          line: 28,\n          column: 31\n        }\n      },\n      \"12\": {\n        start: {\n          line: 29,\n          column: 8\n        },\n        end: {\n          line: 29,\n          column: 42\n        }\n      },\n      \"13\": {\n        start: {\n          line: 30,\n          column: 8\n        },\n        end: {\n          line: 30,\n          column: 62\n        }\n      },\n      \"14\": {\n        start: {\n          line: 33,\n          column: 8\n        },\n        end: {\n          line: 33,\n          column: 27\n        }\n      },\n      \"15\": {\n        start: {\n          line: 36,\n          column: 8\n        },\n        end: {\n          line: 36,\n          column: 30\n        }\n      },\n      \"16\": {\n        start: {\n          line: 37,\n          column: 8\n        },\n        end: {\n          line: 37,\n          column: 28\n        }\n      },\n      \"17\": {\n        start: {\n          line: 38,\n          column: 8\n        },\n        end: {\n          line: 50,\n          column: 11\n        }\n      },\n      \"18\": {\n        start: {\n          line: 40,\n          column: 16\n        },\n        end: {\n          line: 40,\n          column: 39\n        }\n      },\n      \"19\": {\n        start: {\n          line: 41,\n          column: 16\n        },\n        end: {\n          line: 41,\n          column: 39\n        }\n      },\n      \"20\": {\n        start: {\n          line: 42,\n          column: 16\n        },\n        end: {\n          line: 42,\n          column: 36\n        }\n      },\n      \"21\": {\n        start: {\n          line: 45,\n          column: 16\n        },\n        end: {\n          line: 45,\n          column: 39\n        }\n      },\n      \"22\": {\n        start: {\n          line: 46,\n          column: 16\n        },\n        end: {\n          line: 46,\n          column: 36\n        }\n      },\n      \"23\": {\n        start: {\n          line: 47,\n          column: 16\n        },\n        end: {\n          line: 47,\n          column: 73\n        }\n      },\n      \"24\": {\n        start: {\n          line: 48,\n          column: 16\n        },\n        end: {\n          line: 48,\n          column: 39\n        }\n      },\n      \"25\": {\n        start: {\n          line: 53,\n          column: 8\n        },\n        end: {\n          line: 53,\n          column: 37\n        }\n      },\n      \"26\": {\n        start: {\n          line: 56,\n          column: 8\n        },\n        end: {\n          line: 56,\n          column: 31\n        }\n      },\n      \"27\": {\n        start: {\n          line: 59,\n          column: 8\n        },\n        end: {\n          line: 83,\n          column: 9\n        }\n      },\n      \"28\": {\n        start: {\n          line: 60,\n          column: 12\n        },\n        end: {\n          line: 60,\n          column: 32\n        }\n      },\n      \"29\": {\n        start: {\n          line: 61,\n          column: 12\n        },\n        end: {\n          line: 79,\n          column: 15\n        }\n      },\n      \"30\": {\n        start: {\n          line: 71,\n          column: 37\n        },\n        end: {\n          line: 71,\n          column: 56\n        }\n      },\n      \"31\": {\n        start: {\n          line: 74,\n          column: 20\n        },\n        end: {\n          line: 74,\n          column: 62\n        }\n      },\n      \"32\": {\n        start: {\n          line: 77,\n          column: 20\n        },\n        end: {\n          line: 77,\n          column: 85\n        }\n      },\n      \"33\": {\n        start: {\n          line: 82,\n          column: 12\n        },\n        end: {\n          line: 82,\n          column: 70\n        }\n      },\n      \"34\": {\n        start: {\n          line: 85,\n          column: 13\n        },\n        end: {\n          line: 93,\n          column: 6\n        }\n      },\n      \"35\": {\n        start: {\n          line: 85,\n          column: 41\n        },\n        end: {\n          line: 93,\n          column: 5\n        }\n      },\n      \"36\": {\n        start: {\n          line: 95,\n          column: 0\n        },\n        end: {\n          line: 109,\n          column: 32\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 18,\n            column: 4\n          },\n          end: {\n            line: 18,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 18,\n            column: 101\n          },\n          end: {\n            line: 31,\n            column: 5\n          }\n        },\n        line: 18\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 32,\n            column: 4\n          },\n          end: {\n            line: 32,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 32,\n            column: 15\n          },\n          end: {\n            line: 34,\n            column: 5\n          }\n        },\n        line: 32\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 35,\n            column: 4\n          },\n          end: {\n            line: 35,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 35,\n            column: 18\n          },\n          end: {\n            line: 51,\n            column: 5\n          }\n        },\n        line: 35\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 39,\n            column: 18\n          },\n          end: {\n            line: 39,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 39,\n            column: 31\n          },\n          end: {\n            line: 43,\n            column: 13\n          }\n        },\n        line: 39\n      },\n      \"4\": {\n        name: \"(anonymous_4)\",\n        decl: {\n          start: {\n            line: 44,\n            column: 19\n          },\n          end: {\n            line: 44,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 44,\n            column: 25\n          },\n          end: {\n            line: 49,\n            column: 13\n          }\n        },\n        line: 44\n      },\n      \"5\": {\n        name: \"(anonymous_5)\",\n        decl: {\n          start: {\n            line: 52,\n            column: 4\n          },\n          end: {\n            line: 52,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 52,\n            column: 25\n          },\n          end: {\n            line: 54,\n            column: 5\n          }\n        },\n        line: 52\n      },\n      \"6\": {\n        name: \"(anonymous_6)\",\n        decl: {\n          start: {\n            line: 55,\n            column: 4\n          },\n          end: {\n            line: 55,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 55,\n            column: 15\n          },\n          end: {\n            line: 57,\n            column: 5\n          }\n        },\n        line: 55\n      },\n      \"7\": {\n        name: \"(anonymous_7)\",\n        decl: {\n          start: {\n            line: 58,\n            column: 4\n          },\n          end: {\n            line: 58,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 58,\n            column: 15\n          },\n          end: {\n            line: 84,\n            column: 5\n          }\n        },\n        line: 58\n      },\n      \"8\": {\n        name: \"(anonymous_8)\",\n        decl: {\n          start: {\n            line: 71,\n            column: 31\n          },\n          end: {\n            line: 71,\n            column: 32\n          }\n        },\n        loc: {\n          start: {\n            line: 71,\n            column: 37\n          },\n          end: {\n            line: 71,\n            column: 56\n          }\n        },\n        line: 71\n      },\n      \"9\": {\n        name: \"(anonymous_9)\",\n        decl: {\n          start: {\n            line: 73,\n            column: 22\n          },\n          end: {\n            line: 73,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 73,\n            column: 28\n          },\n          end: {\n            line: 75,\n            column: 17\n          }\n        },\n        line: 73\n      },\n      \"10\": {\n        name: \"(anonymous_10)\",\n        decl: {\n          start: {\n            line: 76,\n            column: 23\n          },\n          end: {\n            line: 76,\n            column: 24\n          }\n        },\n        loc: {\n          start: {\n            line: 76,\n            column: 29\n          },\n          end: {\n            line: 78,\n            column: 17\n          }\n        },\n        line: 76\n      },\n      \"11\": {\n        name: \"(anonymous_11)\",\n        decl: {\n          start: {\n            line: 85,\n            column: 35\n          },\n          end: {\n            line: 85,\n            column: 36\n          }\n        },\n        loc: {\n          start: {\n            line: 85,\n            column: 41\n          },\n          end: {\n            line: 93,\n            column: 5\n          }\n        },\n        line: 85\n      }\n    },\n    branchMap: {\n      \"0\": {\n        loc: {\n          start: {\n            line: 59,\n            column: 8\n          },\n          end: {\n            line: 83,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 59,\n            column: 8\n          },\n          end: {\n            line: 83,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 81,\n            column: 13\n          },\n          end: {\n            line: 83,\n            column: 9\n          }\n        }],\n        line: 59\n      }\n    },\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0,\n      \"36\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0\n    },\n    b: {\n      \"0\": [0, 0]\n    },\n    inputSourceMap: {\n      version: 3,\n      file: \"select-period-dialog.component.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-dashboard\\\\components\\\\monthly-reports-tab\\\\select-period-dialog\\\\select-period-dialog.component.ts\"],\n      names: [],\n      mappings: \";;;AAAA,OAAO,EAAE,YAAY,EAAE,kBAAkB,EAAE,MAAM,iBAAiB,CAAC;AACnE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAU,SAAS,EAAE,MAAM,eAAe,CAAC;AACrE,OAAO,EACL,eAAe,EACf,eAAe,EACf,YAAY,GACb,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,aAAa,EAAE,MAAM,+CAA+C,CAAC;AAE9E,OAAO,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAC9D,OAAO,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAC3D,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AAChD,OAAO,QAAQ,MAAM,4BAA4B,CAAC;AAClD,OAAO,EAAE,oBAAoB,EAAE,MAAM,uDAAuD,CAAC;AAC7F,OAAO,EAAE,QAAQ,EAAE,MAAM,MAAM,CAAC;AAEhC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AAetB,IAAM,2BAA2B,GAAjC,MAAM,2BAA2B;IAOtC,YACS,SAAoD,EAEpD,IAA0D,EAChD,aAA4B,EAC5B,oBAA0C,EAC1C,YAA0B,EAC1B,OAA0B,EACjB,MAAc;QAPjC,cAAS,GAAT,SAAS,CAA2C;QAEpD,SAAI,GAAJ,IAAI,CAAsD;QAChD,kBAAa,GAAb,aAAa,CAAe;QAC5B,yBAAoB,GAApB,oBAAoB,CAAsB;QAC1C,iBAAY,GAAZ,YAAY,CAAc;QAC1B,YAAO,GAAP,OAAO,CAAmB;QACjB,WAAM,GAAN,MAAM,CAAQ;QAd1C,YAAO,GAAa,EAAE,CAAC;QACvB,mBAAc,GAAkB,IAAI,CAAC;QAGrC,cAAS,GAAG,KAAK,CAAC;QAYhB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;IACxD,CAAC;IAED,QAAQ;QACN,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAED,WAAW;QACT,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QAEpB,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC;YACnE,IAAI,EAAE,CAAC,OAAO,EAAE,EAAE;gBAChB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;gBACvB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;gBACvB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YACtB,CAAC;YACD,KAAK,EAAE,GAAG,EAAE;gBACV,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;gBACvB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;gBACpB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;gBACzD,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YACzB,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAED,YAAY,CAAC,MAAc;QACzB,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC;IAC/B,CAAC;IAED,QAAQ;QACN,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;IACzB,CAAC;IAED,QAAQ;QACN,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YACpB,IAAI,CAAC,oBAAoB;iBACtB,SAAS,CAAC;gBACT,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;gBAC/C,YAAY,EAAE,IAAI,CAAC,cAAc,CAAC,WAAW;gBAC7C,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,UAAU;gBACzC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ;gBACrC,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO;gBACvC,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;gBACnD,aAAa,EAAE,IAAI;aACpB,CAAC;iBACD,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;iBACzC,SAAS,CAAC;gBACT,IAAI,EAAE,GAAG,EAAE;oBACT,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAC5C,CAAC;gBACD,KAAK,EAAE,GAAG,EAAE;oBACV,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;gBACnE,CAAC;aACF,CAAC,CAAC;QACP,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;;;gDApEE,MAAM,SAAC,eAAe;;;;;6CAMtB,MAAM,SAAC,SAAS;;;AAfR,2BAA2B;IAbvC,SAAS,CAAC;QACT,QAAQ,EAAE,0BAA0B;QACpC,8BAAoD;QAEpD,UAAU,EAAE,IAAI;QAChB,OAAO,EAAE;YACP,YAAY;YACZ,eAAe;YACf,eAAe;YACf,aAAa;YACb,aAAa;SACd;;KACF,CAAC;GACW,2BAA2B,CA8EvC\",\n      sourcesContent: [\"import { CommonModule, registerLocaleData } from '@angular/common';\\nimport { Component, Inject, OnInit, LOCALE_ID } from '@angular/core';\\nimport {\\n  MAT_DIALOG_DATA,\\n  MatDialogModule,\\n  MatDialogRef,\\n} from '@angular/material/dialog';\\nimport { PeriodService } from '@contractor-dashboard/services/period.service';\\nimport { Period } from '@contractor-dashboard/models/Period.model';\\nimport { AlertService } from '@shared/services/alert.service';\\nimport { MatButtonModule } from '@angular/material/button';\\nimport { MatCardModule } from '@angular/material/card';\\nimport { MatIconModule } from '@angular/material/icon';\\nimport { NgxSpinnerService } from 'ngx-spinner';\\nimport localeEs from '@angular/common/locales/es';\\nimport { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';\\nimport { finalize } from 'rxjs';\\n\\nregisterLocaleData(localeEs);\\n\\n@Component({\\n  selector: 'app-select-period-dialog',\\n  templateUrl: './select-period-dialog.component.html',\\n  styleUrls: ['./select-period-dialog.component.scss'],\\n  standalone: true,\\n  imports: [\\n    CommonModule,\\n    MatDialogModule,\\n    MatButtonModule,\\n    MatCardModule,\\n    MatIconModule,\\n  ],\\n})\\nexport class SelectPeriodDialogComponent implements OnInit {\\n  periods: Period[] = [];\\n  selectedPeriod: Period | null = null;\\n  contractId: number;\\n  contractorContractId: number;\\n  isLoading = false;\\n\\n  constructor(\\n    public dialogRef: MatDialogRef<SelectPeriodDialogComponent>,\\n    @Inject(MAT_DIALOG_DATA)\\n    public data: { contractId: number; contractorContractId: number },\\n    private readonly periodService: PeriodService,\\n    private readonly monthlyReportService: MonthlyReportService,\\n    private readonly alertService: AlertService,\\n    private readonly spinner: NgxSpinnerService,\\n    @Inject(LOCALE_ID) public locale: string,\\n  ) {\\n    this.contractId = data.contractId;\\n    this.contractorContractId = data.contractorContractId;\\n  }\\n\\n  ngOnInit(): void {\\n    this.loadPeriods();\\n  }\\n\\n  loadPeriods(): void {\\n    this.isLoading = true;\\n    this.spinner.show();\\n\\n    this.periodService.getPeriodsByContractId(this.contractId).subscribe({\\n      next: (periods) => {\\n        this.periods = periods;\\n        this.isLoading = false;\\n        this.spinner.hide();\\n      },\\n      error: () => {\\n        this.isLoading = false;\\n        this.spinner.hide();\\n        this.alertService.error('Error al cargar los per\\xEDodos.');\\n        this.dialogRef.close();\\n      },\\n    });\\n  }\\n\\n  selectPeriod(period: Period): void {\\n    this.selectedPeriod = period;\\n  }\\n\\n  onCancel(): void {\\n    this.dialogRef.close();\\n  }\\n\\n  onCreate(): void {\\n    if (this.selectedPeriod) {\\n      this.spinner.show();\\n      this.monthlyReportService\\n        .createMax({\\n          contractorContractId: this.contractorContractId,\\n          reportNumber: this.selectedPeriod.num_payment,\\n          startDate: this.selectedPeriod.start_date,\\n          endDate: this.selectedPeriod.end_date,\\n          totalValue: this.selectedPeriod.payment,\\n          creationDate: new Date().toISOString().slice(0, 10),\\n          isFirstReport: true,\\n        })\\n        .pipe(finalize(() => this.spinner.hide()))\\n        .subscribe({\\n          next: () => {\\n            this.dialogRef.close(this.selectedPeriod);\\n          },\\n          error: () => {\\n            this.alertService.error('Error al crear los informes mensuales');\\n          },\\n        });\\n    } else {\\n      this.alertService.warning('Debe seleccionar un per\\xEDodo.');\\n    }\\n  }\\n}\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"12054d2faf80ed02de6fcbf2b705a243414bc89c\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_soh2jbacz = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_soh2jbacz();\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./select-period-dialog.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./select-period-dialog.component.scss?ngResource\";\nimport { CommonModule, registerLocaleData } from '@angular/common';\nimport { Component, Inject, LOCALE_ID } from '@angular/core';\nimport { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';\nimport { PeriodService } from '@contractor-dashboard/services/period.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport localeEs from '@angular/common/locales/es';\nimport { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';\nimport { finalize } from 'rxjs';\ncov_soh2jbacz().s[0]++;\nregisterLocaleData(localeEs);\ncov_soh2jbacz().s[1]++;\nlet SelectPeriodDialogComponent = class SelectPeriodDialogComponent {\n  constructor(dialogRef, data, periodService, monthlyReportService, alertService, spinner, locale) {\n    cov_soh2jbacz().f[0]++;\n    cov_soh2jbacz().s[2]++;\n    this.dialogRef = dialogRef;\n    cov_soh2jbacz().s[3]++;\n    this.data = data;\n    cov_soh2jbacz().s[4]++;\n    this.periodService = periodService;\n    cov_soh2jbacz().s[5]++;\n    this.monthlyReportService = monthlyReportService;\n    cov_soh2jbacz().s[6]++;\n    this.alertService = alertService;\n    cov_soh2jbacz().s[7]++;\n    this.spinner = spinner;\n    cov_soh2jbacz().s[8]++;\n    this.locale = locale;\n    cov_soh2jbacz().s[9]++;\n    this.periods = [];\n    cov_soh2jbacz().s[10]++;\n    this.selectedPeriod = null;\n    cov_soh2jbacz().s[11]++;\n    this.isLoading = false;\n    cov_soh2jbacz().s[12]++;\n    this.contractId = data.contractId;\n    cov_soh2jbacz().s[13]++;\n    this.contractorContractId = data.contractorContractId;\n  }\n  ngOnInit() {\n    cov_soh2jbacz().f[1]++;\n    cov_soh2jbacz().s[14]++;\n    this.loadPeriods();\n  }\n  loadPeriods() {\n    cov_soh2jbacz().f[2]++;\n    cov_soh2jbacz().s[15]++;\n    this.isLoading = true;\n    cov_soh2jbacz().s[16]++;\n    this.spinner.show();\n    cov_soh2jbacz().s[17]++;\n    this.periodService.getPeriodsByContractId(this.contractId).subscribe({\n      next: periods => {\n        cov_soh2jbacz().f[3]++;\n        cov_soh2jbacz().s[18]++;\n        this.periods = periods;\n        cov_soh2jbacz().s[19]++;\n        this.isLoading = false;\n        cov_soh2jbacz().s[20]++;\n        this.spinner.hide();\n      },\n      error: () => {\n        cov_soh2jbacz().f[4]++;\n        cov_soh2jbacz().s[21]++;\n        this.isLoading = false;\n        cov_soh2jbacz().s[22]++;\n        this.spinner.hide();\n        cov_soh2jbacz().s[23]++;\n        this.alertService.error('Error al cargar los períodos.');\n        cov_soh2jbacz().s[24]++;\n        this.dialogRef.close();\n      }\n    });\n  }\n  selectPeriod(period) {\n    cov_soh2jbacz().f[5]++;\n    cov_soh2jbacz().s[25]++;\n    this.selectedPeriod = period;\n  }\n  onCancel() {\n    cov_soh2jbacz().f[6]++;\n    cov_soh2jbacz().s[26]++;\n    this.dialogRef.close();\n  }\n  onCreate() {\n    cov_soh2jbacz().f[7]++;\n    cov_soh2jbacz().s[27]++;\n    if (this.selectedPeriod) {\n      cov_soh2jbacz().b[0][0]++;\n      cov_soh2jbacz().s[28]++;\n      this.spinner.show();\n      cov_soh2jbacz().s[29]++;\n      this.monthlyReportService.createMax({\n        contractorContractId: this.contractorContractId,\n        reportNumber: this.selectedPeriod.num_payment,\n        startDate: this.selectedPeriod.start_date,\n        endDate: this.selectedPeriod.end_date,\n        totalValue: this.selectedPeriod.payment,\n        creationDate: new Date().toISOString().slice(0, 10),\n        isFirstReport: true\n      }).pipe(finalize(() => {\n        cov_soh2jbacz().f[8]++;\n        cov_soh2jbacz().s[30]++;\n        return this.spinner.hide();\n      })).subscribe({\n        next: () => {\n          cov_soh2jbacz().f[9]++;\n          cov_soh2jbacz().s[31]++;\n          this.dialogRef.close(this.selectedPeriod);\n        },\n        error: () => {\n          cov_soh2jbacz().f[10]++;\n          cov_soh2jbacz().s[32]++;\n          this.alertService.error('Error al crear los informes mensuales');\n        }\n      });\n    } else {\n      cov_soh2jbacz().b[0][1]++;\n      cov_soh2jbacz().s[33]++;\n      this.alertService.warning('Debe seleccionar un período.');\n    }\n  }\n  static {\n    cov_soh2jbacz().s[34]++;\n    this.ctorParameters = () => {\n      cov_soh2jbacz().f[11]++;\n      cov_soh2jbacz().s[35]++;\n      return [{\n        type: MatDialogRef\n      }, {\n        type: undefined,\n        decorators: [{\n          type: Inject,\n          args: [MAT_DIALOG_DATA]\n        }]\n      }, {\n        type: PeriodService\n      }, {\n        type: MonthlyReportService\n      }, {\n        type: AlertService\n      }, {\n        type: NgxSpinnerService\n      }, {\n        type: String,\n        decorators: [{\n          type: Inject,\n          args: [LOCALE_ID]\n        }]\n      }];\n    };\n  }\n};\ncov_soh2jbacz().s[36]++;\nSelectPeriodDialogComponent = __decorate([Component({\n  selector: 'app-select-period-dialog',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: true,\n  imports: [CommonModule, MatDialogModule, MatButtonModule, MatCardModule, MatIconModule],\n  styles: [__NG_CLI_RESOURCE__1]\n})], SelectPeriodDialogComponent);\nexport { SelectPeriodDialogComponent };", "map": {"version": 3, "names": ["cov_soh2jbacz", "actualCoverage", "CommonModule", "registerLocaleData", "Component", "Inject", "LOCALE_ID", "MAT_DIALOG_DATA", "MatDialogModule", "MatDialogRef", "PeriodService", "AlertService", "MatButtonModule", "MatCardModule", "MatIconModule", "NgxSpinnerService", "localeEs", "MonthlyReportService", "finalize", "s", "SelectPeriodDialogComponent", "constructor", "dialogRef", "data", "periodService", "monthlyReportService", "alertService", "spinner", "locale", "f", "periods", "<PERSON><PERSON><PERSON><PERSON>", "isLoading", "contractId", "contractorContractId", "ngOnInit", "loadPeriods", "show", "getPeriodsByContractId", "subscribe", "next", "hide", "error", "close", "selectPeriod", "period", "onCancel", "onCreate", "b", "createMax", "reportNumber", "num_payment", "startDate", "start_date", "endDate", "end_date", "totalValue", "payment", "creationDate", "Date", "toISOString", "slice", "isFirstReport", "pipe", "warning", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\components\\monthly-reports-tab\\select-period-dialog\\select-period-dialog.component.ts"], "sourcesContent": ["import { CommonModule, registerLocaleData } from '@angular/common';\nimport { Component, Inject, OnInit, LOCALE_ID } from '@angular/core';\nimport {\n  MAT_DIALOG_DATA,\n  MatDialogModule,\n  MatDialogRef,\n} from '@angular/material/dialog';\nimport { PeriodService } from '@contractor-dashboard/services/period.service';\nimport { Period } from '@contractor-dashboard/models/Period.model';\nimport { AlertService } from '@shared/services/alert.service';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport localeEs from '@angular/common/locales/es';\nimport { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';\nimport { finalize } from 'rxjs';\n\nregisterLocaleData(localeEs);\n\n@Component({\n  selector: 'app-select-period-dialog',\n  templateUrl: './select-period-dialog.component.html',\n  styleUrls: ['./select-period-dialog.component.scss'],\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatDialogModule,\n    MatButtonModule,\n    MatCardModule,\n    MatIconModule,\n  ],\n})\nexport class SelectPeriodDialogComponent implements OnInit {\n  periods: Period[] = [];\n  selectedPeriod: Period | null = null;\n  contractId: number;\n  contractorContractId: number;\n  isLoading = false;\n\n  constructor(\n    public dialogRef: MatDialogRef<SelectPeriodDialogComponent>,\n    @Inject(MAT_DIALOG_DATA)\n    public data: { contractId: number; contractorContractId: number },\n    private readonly periodService: PeriodService,\n    private readonly monthlyReportService: MonthlyReportService,\n    private readonly alertService: AlertService,\n    private readonly spinner: NgxSpinnerService,\n    @Inject(LOCALE_ID) public locale: string,\n  ) {\n    this.contractId = data.contractId;\n    this.contractorContractId = data.contractorContractId;\n  }\n\n  ngOnInit(): void {\n    this.loadPeriods();\n  }\n\n  loadPeriods(): void {\n    this.isLoading = true;\n    this.spinner.show();\n\n    this.periodService.getPeriodsByContractId(this.contractId).subscribe({\n      next: (periods) => {\n        this.periods = periods;\n        this.isLoading = false;\n        this.spinner.hide();\n      },\n      error: () => {\n        this.isLoading = false;\n        this.spinner.hide();\n        this.alertService.error('Error al cargar los períodos.');\n        this.dialogRef.close();\n      },\n    });\n  }\n\n  selectPeriod(period: Period): void {\n    this.selectedPeriod = period;\n  }\n\n  onCancel(): void {\n    this.dialogRef.close();\n  }\n\n  onCreate(): void {\n    if (this.selectedPeriod) {\n      this.spinner.show();\n      this.monthlyReportService\n        .createMax({\n          contractorContractId: this.contractorContractId,\n          reportNumber: this.selectedPeriod.num_payment,\n          startDate: this.selectedPeriod.start_date,\n          endDate: this.selectedPeriod.end_date,\n          totalValue: this.selectedPeriod.payment,\n          creationDate: new Date().toISOString().slice(0, 10),\n          isFirstReport: true,\n        })\n        .pipe(finalize(() => this.spinner.hide()))\n        .subscribe({\n          next: () => {\n            this.dialogRef.close(this.selectedPeriod);\n          },\n          error: () => {\n            this.alertService.error('Error al crear los informes mensuales');\n          },\n        });\n    } else {\n      this.alertService.warning('Debe seleccionar un período.');\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAkBA;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;AAlBA,SAASE,YAAY,EAAEC,kBAAkB,QAAQ,iBAAiB;AAClE,SAASC,SAAS,EAAEC,MAAM,EAAUC,SAAS,QAAQ,eAAe;AACpE,SACEC,eAAe,EACfC,eAAe,EACfC,YAAY,QACP,0BAA0B;AACjC,SAASC,aAAa,QAAQ,+CAA+C;AAE7E,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,iBAAiB,QAAQ,aAAa;AAC/C,OAAOC,QAAQ,MAAM,4BAA4B;AACjD,SAASC,oBAAoB,QAAQ,uDAAuD;AAC5F,SAASC,QAAQ,QAAQ,MAAM;AAAClB,aAAA,GAAAmB,CAAA;AAEhChB,kBAAkB,CAACa,QAAQ,CAAC;AAAChB,aAAA,GAAAmB,CAAA;AAetB,IAAMC,2BAA2B,GAAjC,MAAMA,2BAA2B;EAOtCC,YACSC,SAAoD,EAEpDC,IAA0D,EAChDC,aAA4B,EAC5BC,oBAA0C,EAC1CC,YAA0B,EAC1BC,OAA0B,EACjBC,MAAc;IAAA5B,aAAA,GAAA6B,CAAA;IAAA7B,aAAA,GAAAmB,CAAA;IAPjC,KAAAG,SAAS,GAATA,SAAS;IAA2CtB,aAAA,GAAAmB,CAAA;IAEpD,KAAAI,IAAI,GAAJA,IAAI;IAAsDvB,aAAA,GAAAmB,CAAA;IAChD,KAAAK,aAAa,GAAbA,aAAa;IAAexB,aAAA,GAAAmB,CAAA;IAC5B,KAAAM,oBAAoB,GAApBA,oBAAoB;IAAsBzB,aAAA,GAAAmB,CAAA;IAC1C,KAAAO,YAAY,GAAZA,YAAY;IAAc1B,aAAA,GAAAmB,CAAA;IAC1B,KAAAQ,OAAO,GAAPA,OAAO;IAAmB3B,aAAA,GAAAmB,CAAA;IACjB,KAAAS,MAAM,GAANA,MAAM;IAAQ5B,aAAA,GAAAmB,CAAA;IAd1C,KAAAW,OAAO,GAAa,EAAE;IAAC9B,aAAA,GAAAmB,CAAA;IACvB,KAAAY,cAAc,GAAkB,IAAI;IAAC/B,aAAA,GAAAmB,CAAA;IAGrC,KAAAa,SAAS,GAAG,KAAK;IAAChC,aAAA,GAAAmB,CAAA;IAYhB,IAAI,CAACc,UAAU,GAAGV,IAAI,CAACU,UAAU;IAACjC,aAAA,GAAAmB,CAAA;IAClC,IAAI,CAACe,oBAAoB,GAAGX,IAAI,CAACW,oBAAoB;EACvD;EAEAC,QAAQA,CAAA;IAAAnC,aAAA,GAAA6B,CAAA;IAAA7B,aAAA,GAAAmB,CAAA;IACN,IAAI,CAACiB,WAAW,EAAE;EACpB;EAEAA,WAAWA,CAAA;IAAApC,aAAA,GAAA6B,CAAA;IAAA7B,aAAA,GAAAmB,CAAA;IACT,IAAI,CAACa,SAAS,GAAG,IAAI;IAAChC,aAAA,GAAAmB,CAAA;IACtB,IAAI,CAACQ,OAAO,CAACU,IAAI,EAAE;IAACrC,aAAA,GAAAmB,CAAA;IAEpB,IAAI,CAACK,aAAa,CAACc,sBAAsB,CAAC,IAAI,CAACL,UAAU,CAAC,CAACM,SAAS,CAAC;MACnEC,IAAI,EAAGV,OAAO,IAAI;QAAA9B,aAAA,GAAA6B,CAAA;QAAA7B,aAAA,GAAAmB,CAAA;QAChB,IAAI,CAACW,OAAO,GAAGA,OAAO;QAAC9B,aAAA,GAAAmB,CAAA;QACvB,IAAI,CAACa,SAAS,GAAG,KAAK;QAAChC,aAAA,GAAAmB,CAAA;QACvB,IAAI,CAACQ,OAAO,CAACc,IAAI,EAAE;MACrB,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAK;QAAA1C,aAAA,GAAA6B,CAAA;QAAA7B,aAAA,GAAAmB,CAAA;QACV,IAAI,CAACa,SAAS,GAAG,KAAK;QAAChC,aAAA,GAAAmB,CAAA;QACvB,IAAI,CAACQ,OAAO,CAACc,IAAI,EAAE;QAACzC,aAAA,GAAAmB,CAAA;QACpB,IAAI,CAACO,YAAY,CAACgB,KAAK,CAAC,+BAA+B,CAAC;QAAC1C,aAAA,GAAAmB,CAAA;QACzD,IAAI,CAACG,SAAS,CAACqB,KAAK,EAAE;MACxB;KACD,CAAC;EACJ;EAEAC,YAAYA,CAACC,MAAc;IAAA7C,aAAA,GAAA6B,CAAA;IAAA7B,aAAA,GAAAmB,CAAA;IACzB,IAAI,CAACY,cAAc,GAAGc,MAAM;EAC9B;EAEAC,QAAQA,CAAA;IAAA9C,aAAA,GAAA6B,CAAA;IAAA7B,aAAA,GAAAmB,CAAA;IACN,IAAI,CAACG,SAAS,CAACqB,KAAK,EAAE;EACxB;EAEAI,QAAQA,CAAA;IAAA/C,aAAA,GAAA6B,CAAA;IAAA7B,aAAA,GAAAmB,CAAA;IACN,IAAI,IAAI,CAACY,cAAc,EAAE;MAAA/B,aAAA,GAAAgD,CAAA;MAAAhD,aAAA,GAAAmB,CAAA;MACvB,IAAI,CAACQ,OAAO,CAACU,IAAI,EAAE;MAACrC,aAAA,GAAAmB,CAAA;MACpB,IAAI,CAACM,oBAAoB,CACtBwB,SAAS,CAAC;QACTf,oBAAoB,EAAE,IAAI,CAACA,oBAAoB;QAC/CgB,YAAY,EAAE,IAAI,CAACnB,cAAc,CAACoB,WAAW;QAC7CC,SAAS,EAAE,IAAI,CAACrB,cAAc,CAACsB,UAAU;QACzCC,OAAO,EAAE,IAAI,CAACvB,cAAc,CAACwB,QAAQ;QACrCC,UAAU,EAAE,IAAI,CAACzB,cAAc,CAAC0B,OAAO;QACvCC,YAAY,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;QACnDC,aAAa,EAAE;OAChB,CAAC,CACDC,IAAI,CAAC7C,QAAQ,CAAC,MAAM;QAAAlB,aAAA,GAAA6B,CAAA;QAAA7B,aAAA,GAAAmB,CAAA;QAAA,WAAI,CAACQ,OAAO,CAACc,IAAI,EAAE;MAAF,CAAE,CAAC,CAAC,CACzCF,SAAS,CAAC;QACTC,IAAI,EAAEA,CAAA,KAAK;UAAAxC,aAAA,GAAA6B,CAAA;UAAA7B,aAAA,GAAAmB,CAAA;UACT,IAAI,CAACG,SAAS,CAACqB,KAAK,CAAC,IAAI,CAACZ,cAAc,CAAC;QAC3C,CAAC;QACDW,KAAK,EAAEA,CAAA,KAAK;UAAA1C,aAAA,GAAA6B,CAAA;UAAA7B,aAAA,GAAAmB,CAAA;UACV,IAAI,CAACO,YAAY,CAACgB,KAAK,CAAC,uCAAuC,CAAC;QAClE;OACD,CAAC;IACN,CAAC,MAAM;MAAA1C,aAAA,GAAAgD,CAAA;MAAAhD,aAAA,GAAAmB,CAAA;MACL,IAAI,CAACO,YAAY,CAACsC,OAAO,CAAC,8BAA8B,CAAC;IAC3D;EACF;;;;;;;;;;;gBApEG3D,MAAM;UAAA4D,IAAA,GAAC1D,eAAe;QAAA;MAAA,G;;;;;;;;;;;gBAMtBF,MAAM;UAAA4D,IAAA,GAAC3D,SAAS;QAAA;MAAA,E;;;;;AAfRc,2BAA2B,GAAA8C,UAAA,EAbvC9D,SAAS,CAAC;EACT+D,QAAQ,EAAE,0BAA0B;EACpCC,QAAA,EAAAC,oBAAoD;EAEpDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPrE,YAAY,EACZM,eAAe,EACfI,eAAe,EACfC,aAAa,EACbC,aAAa,CACd;;CACF,CAAC,C,EACWM,2BAA2B,CA8EvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}