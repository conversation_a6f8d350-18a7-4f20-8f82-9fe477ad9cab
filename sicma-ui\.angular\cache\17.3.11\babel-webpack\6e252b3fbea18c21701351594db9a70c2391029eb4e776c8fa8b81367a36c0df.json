{"ast": null, "code": "import { FormBuilder } from '@angular/forms';\nimport { createDateRangeValidator } from './date-range.validator';\ndescribe('DateRangeValidator', () => {\n  let fb;\n  let form;\n  let config;\n  let currentYear;\n  beforeEach(() => {\n    fb = new FormBuilder();\n    form = fb.group({\n      startDate: [null],\n      endDate: [null],\n      subscriptionDate: [null]\n    });\n    currentYear = new Date().getFullYear();\n    config = {\n      latestEndDate: null,\n      subscriptionDate: null,\n      isEdit: false,\n      valuesForm: form\n    };\n  });\n  it('should not validate when value is null', () => {\n    const validator = createDateRangeValidator(config);\n    const control = form.get('startDate');\n    const result = validator(control);\n    expect(result).toBeNull();\n  });\n  describe('Start Date Validation', () => {\n    it('should not return error when date is not in current year', () => {\n      const validator = createDateRangeValidator(config);\n      const lastYear = new Date();\n      lastYear.setFullYear(currentYear - 1);\n      form.get('startDate')?.setValue(lastYear);\n      const result = validator(form.get('startDate'));\n      expect(result).toBeNull();\n    });\n    it('should return dateTooEarly error when date is before latestEndDate', () => {\n      const currentYearDate = new Date();\n      currentYearDate.setFullYear(currentYear);\n      config.latestEndDate = new Date(currentYearDate.setMonth(5));\n      const validator = createDateRangeValidator(config);\n      const startDate = new Date(currentYearDate);\n      startDate.setMonth(4);\n      form.get('startDate')?.setValue(startDate);\n      const result = validator(form.get('startDate'));\n      expect(result).toEqual({\n        dateTooEarly: true\n      });\n    });\n    it('should return startDateBeforeSubscription error when date is before subscription date', () => {\n      const currentYearDate = new Date();\n      currentYearDate.setFullYear(currentYear);\n      config.subscriptionDate = new Date(currentYear, 5, 15);\n      form.get('subscriptionDate')?.setValue(new Date(currentYear, 5, 15));\n      const startDate = new Date(currentYear, 4, 15);\n      form.get('startDate')?.setValue(startDate);\n      const validator = createDateRangeValidator(config);\n      const result = validator(form.get('startDate'));\n      expect(result).toEqual({\n        startDateBeforeSubscription: true\n      });\n    });\n    it('should return null when start date is valid', () => {\n      const currentYearDate = new Date();\n      currentYearDate.setFullYear(currentYear);\n      config.latestEndDate = new Date(currentYearDate.setMonth(4));\n      config.subscriptionDate = new Date(currentYearDate);\n      const validator = createDateRangeValidator(config);\n      const startDate = new Date(currentYearDate);\n      startDate.setMonth(5);\n      form.get('startDate')?.setValue(startDate);\n      const result = validator(form.get('startDate'));\n      expect(result).toBeNull();\n    });\n    it('should not return dateTooEarly error in edit mode', () => {\n      const currentYearDate = new Date();\n      currentYearDate.setFullYear(currentYear);\n      config.latestEndDate = new Date(currentYearDate.setMonth(5));\n      config.isEdit = true;\n      const validator = createDateRangeValidator(config);\n      const startDate = new Date(currentYearDate);\n      startDate.setMonth(4);\n      form.get('startDate')?.setValue(startDate);\n      const result = validator(form.get('startDate'));\n      expect(result?.['dateTooEarly']).toBeUndefined();\n    });\n  });\n  describe('End Date Validation', () => {\n    it('should return endDateBeforeStartDate error when end date is before start date', () => {\n      const currentYearDate = new Date();\n      currentYearDate.setFullYear(currentYear);\n      const validator = createDateRangeValidator(config);\n      form.get('startDate')?.setValue(new Date(currentYearDate.setMonth(5)));\n      form.get('endDate')?.setValue(new Date(currentYearDate.setMonth(4)));\n      const result = validator(form.get('endDate'));\n      expect(result).toEqual({\n        endDateBeforeStartDate: true\n      });\n    });\n    it('should return null when end date is valid', () => {\n      const currentYearDate = new Date();\n      currentYearDate.setFullYear(currentYear);\n      const validator = createDateRangeValidator(config);\n      form.get('startDate')?.setValue(new Date(currentYearDate.setMonth(5)));\n      form.get('endDate')?.setValue(new Date(currentYearDate.setMonth(6)));\n      const result = validator(form.get('endDate'));\n      expect(result).toBeNull();\n    });\n  });\n  describe('Subscription Date Validation', () => {\n    it('should return subscriptionDateAfterStartDate error when subscription date is after start date', () => {\n      const validator = createDateRangeValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-06-01'));\n      form.get('subscriptionDate')?.setValue(new Date('2024-07-01'));\n      const result = validator(form.get('subscriptionDate'));\n      expect(result).toEqual({\n        subscriptionDateAfterStartDate: true\n      });\n    });\n    it('should return null when subscription date is valid', () => {\n      const validator = createDateRangeValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-06-01'));\n      form.get('subscriptionDate')?.setValue(new Date('2024-05-01'));\n      const result = validator(form.get('subscriptionDate'));\n      expect(result).toBeNull();\n    });\n  });\n});", "map": {"version": 3, "names": ["FormBuilder", "createDateRangeValidator", "describe", "fb", "form", "config", "currentYear", "beforeEach", "group", "startDate", "endDate", "subscriptionDate", "Date", "getFullYear", "latestEndDate", "isEdit", "valuesForm", "it", "validator", "control", "get", "result", "expect", "toBeNull", "lastYear", "setFullYear", "setValue", "currentYearDate", "setMonth", "toEqual", "dateToo<PERSON>arly", "startDateBeforeSubscription", "toBeUndefined", "endDateBeforeStartDate", "subscriptionDateAfterStartDate"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\components\\contract-values-form\\validators\\date-range.validator.spec.ts"], "sourcesContent": ["import { FormBuilder, FormGroup } from '@angular/forms';\nimport {\n  createDateRangeValidator,\n  DateRangeValidatorConfig,\n} from './date-range.validator';\n\ndescribe('DateRangeValidator', () => {\n  let fb: FormBuilder;\n  let form: FormGroup;\n  let config: DateRangeValidatorConfig;\n  let currentYear: number;\n\n  beforeEach(() => {\n    fb = new FormBuilder();\n    form = fb.group({\n      startDate: [null],\n      endDate: [null],\n      subscriptionDate: [null],\n    });\n\n    currentYear = new Date().getFullYear();\n    config = {\n      latestEndDate: null,\n      subscriptionDate: null,\n      isEdit: false,\n      valuesForm: form,\n    };\n  });\n\n  it('should not validate when value is null', () => {\n    const validator = createDateRangeValidator(config);\n    const control = form.get('startDate')!;\n\n    const result = validator(control);\n    expect(result).toBeNull();\n  });\n\n  describe('Start Date Validation', () => {\n    it('should not return error when date is not in current year', () => {\n      const validator = createDateRangeValidator(config);\n      const lastYear = new Date();\n      lastYear.setFullYear(currentYear - 1);\n      form.get('startDate')?.setValue(lastYear);\n      const result = validator(form.get('startDate')!);\n      expect(result).toBeNull();\n    });\n\n    it('should return dateTooEarly error when date is before latestEndDate', () => {\n      const currentYearDate = new Date();\n      currentYearDate.setFullYear(currentYear);\n      config.latestEndDate = new Date(currentYearDate.setMonth(5));\n      const validator = createDateRangeValidator(config);\n      const startDate = new Date(currentYearDate);\n      startDate.setMonth(4);\n      form.get('startDate')?.setValue(startDate);\n      const result = validator(form.get('startDate')!);\n      expect(result).toEqual({ dateTooEarly: true });\n    });\n\n    it('should return startDateBeforeSubscription error when date is before subscription date', () => {\n      const currentYearDate = new Date();\n      currentYearDate.setFullYear(currentYear);\n\n      config.subscriptionDate = new Date(currentYear, 5, 15);\n\n      form.get('subscriptionDate')?.setValue(new Date(currentYear, 5, 15));\n\n      const startDate = new Date(currentYear, 4, 15);\n      form.get('startDate')?.setValue(startDate);\n\n      const validator = createDateRangeValidator(config);\n      const result = validator(form.get('startDate')!);\n\n      expect(result).toEqual({ startDateBeforeSubscription: true });\n    });\n\n    it('should return null when start date is valid', () => {\n      const currentYearDate = new Date();\n      currentYearDate.setFullYear(currentYear);\n      config.latestEndDate = new Date(currentYearDate.setMonth(4));\n      config.subscriptionDate = new Date(currentYearDate);\n      const validator = createDateRangeValidator(config);\n      const startDate = new Date(currentYearDate);\n      startDate.setMonth(5);\n      form.get('startDate')?.setValue(startDate);\n      const result = validator(form.get('startDate')!);\n      expect(result).toBeNull();\n    });\n\n    it('should not return dateTooEarly error in edit mode', () => {\n      const currentYearDate = new Date();\n      currentYearDate.setFullYear(currentYear);\n      config.latestEndDate = new Date(currentYearDate.setMonth(5));\n      config.isEdit = true;\n      const validator = createDateRangeValidator(config);\n      const startDate = new Date(currentYearDate);\n      startDate.setMonth(4);\n      form.get('startDate')?.setValue(startDate);\n      const result = validator(form.get('startDate')!);\n      expect(result?.['dateTooEarly']).toBeUndefined();\n    });\n  });\n\n  describe('End Date Validation', () => {\n    it('should return endDateBeforeStartDate error when end date is before start date', () => {\n      const currentYearDate = new Date();\n      currentYearDate.setFullYear(currentYear);\n      const validator = createDateRangeValidator(config);\n      form.get('startDate')?.setValue(new Date(currentYearDate.setMonth(5)));\n      form.get('endDate')?.setValue(new Date(currentYearDate.setMonth(4)));\n      const result = validator(form.get('endDate')!);\n      expect(result).toEqual({ endDateBeforeStartDate: true });\n    });\n\n    it('should return null when end date is valid', () => {\n      const currentYearDate = new Date();\n      currentYearDate.setFullYear(currentYear);\n      const validator = createDateRangeValidator(config);\n      form.get('startDate')?.setValue(new Date(currentYearDate.setMonth(5)));\n      form.get('endDate')?.setValue(new Date(currentYearDate.setMonth(6)));\n      const result = validator(form.get('endDate')!);\n      expect(result).toBeNull();\n    });\n  });\n\n  describe('Subscription Date Validation', () => {\n    it('should return subscriptionDateAfterStartDate error when subscription date is after start date', () => {\n      const validator = createDateRangeValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-06-01'));\n      form.get('subscriptionDate')?.setValue(new Date('2024-07-01'));\n      const result = validator(form.get('subscriptionDate')!);\n      expect(result).toEqual({ subscriptionDateAfterStartDate: true });\n    });\n\n    it('should return null when subscription date is valid', () => {\n      const validator = createDateRangeValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-06-01'));\n      form.get('subscriptionDate')?.setValue(new Date('2024-05-01'));\n      const result = validator(form.get('subscriptionDate')!);\n      expect(result).toBeNull();\n    });\n  });\n});"], "mappings": "AAAA,SAASA,WAAW,QAAmB,gBAAgB;AACvD,SACEC,wBAAwB,QAEnB,wBAAwB;AAE/BC,QAAQ,CAAC,oBAAoB,EAAE,MAAK;EAClC,IAAIC,EAAe;EACnB,IAAIC,IAAe;EACnB,IAAIC,MAAgC;EACpC,IAAIC,WAAmB;EAEvBC,UAAU,CAAC,MAAK;IACdJ,EAAE,GAAG,IAAIH,WAAW,EAAE;IACtBI,IAAI,GAAGD,EAAE,CAACK,KAAK,CAAC;MACdC,SAAS,EAAE,CAAC,IAAI,CAAC;MACjBC,OAAO,EAAE,CAAC,IAAI,CAAC;MACfC,gBAAgB,EAAE,CAAC,IAAI;KACxB,CAAC;IAEFL,WAAW,GAAG,IAAIM,IAAI,EAAE,CAACC,WAAW,EAAE;IACtCR,MAAM,GAAG;MACPS,aAAa,EAAE,IAAI;MACnBH,gBAAgB,EAAE,IAAI;MACtBI,MAAM,EAAE,KAAK;MACbC,UAAU,EAAEZ;KACb;EACH,CAAC,CAAC;EAEFa,EAAE,CAAC,wCAAwC,EAAE,MAAK;IAChD,MAAMC,SAAS,GAAGjB,wBAAwB,CAACI,MAAM,CAAC;IAClD,MAAMc,OAAO,GAAGf,IAAI,CAACgB,GAAG,CAAC,WAAW,CAAE;IAEtC,MAAMC,MAAM,GAAGH,SAAS,CAACC,OAAO,CAAC;IACjCG,MAAM,CAACD,MAAM,CAAC,CAACE,QAAQ,EAAE;EAC3B,CAAC,CAAC;EAEFrB,QAAQ,CAAC,uBAAuB,EAAE,MAAK;IACrCe,EAAE,CAAC,0DAA0D,EAAE,MAAK;MAClE,MAAMC,SAAS,GAAGjB,wBAAwB,CAACI,MAAM,CAAC;MAClD,MAAMmB,QAAQ,GAAG,IAAIZ,IAAI,EAAE;MAC3BY,QAAQ,CAACC,WAAW,CAACnB,WAAW,GAAG,CAAC,CAAC;MACrCF,IAAI,CAACgB,GAAG,CAAC,WAAW,CAAC,EAAEM,QAAQ,CAACF,QAAQ,CAAC;MACzC,MAAMH,MAAM,GAAGH,SAAS,CAACd,IAAI,CAACgB,GAAG,CAAC,WAAW,CAAE,CAAC;MAChDE,MAAM,CAACD,MAAM,CAAC,CAACE,QAAQ,EAAE;IAC3B,CAAC,CAAC;IAEFN,EAAE,CAAC,oEAAoE,EAAE,MAAK;MAC5E,MAAMU,eAAe,GAAG,IAAIf,IAAI,EAAE;MAClCe,eAAe,CAACF,WAAW,CAACnB,WAAW,CAAC;MACxCD,MAAM,CAACS,aAAa,GAAG,IAAIF,IAAI,CAACe,eAAe,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;MAC5D,MAAMV,SAAS,GAAGjB,wBAAwB,CAACI,MAAM,CAAC;MAClD,MAAMI,SAAS,GAAG,IAAIG,IAAI,CAACe,eAAe,CAAC;MAC3ClB,SAAS,CAACmB,QAAQ,CAAC,CAAC,CAAC;MACrBxB,IAAI,CAACgB,GAAG,CAAC,WAAW,CAAC,EAAEM,QAAQ,CAACjB,SAAS,CAAC;MAC1C,MAAMY,MAAM,GAAGH,SAAS,CAACd,IAAI,CAACgB,GAAG,CAAC,WAAW,CAAE,CAAC;MAChDE,MAAM,CAACD,MAAM,CAAC,CAACQ,OAAO,CAAC;QAAEC,YAAY,EAAE;MAAI,CAAE,CAAC;IAChD,CAAC,CAAC;IAEFb,EAAE,CAAC,uFAAuF,EAAE,MAAK;MAC/F,MAAMU,eAAe,GAAG,IAAIf,IAAI,EAAE;MAClCe,eAAe,CAACF,WAAW,CAACnB,WAAW,CAAC;MAExCD,MAAM,CAACM,gBAAgB,GAAG,IAAIC,IAAI,CAACN,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC;MAEtDF,IAAI,CAACgB,GAAG,CAAC,kBAAkB,CAAC,EAAEM,QAAQ,CAAC,IAAId,IAAI,CAACN,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;MAEpE,MAAMG,SAAS,GAAG,IAAIG,IAAI,CAACN,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC;MAC9CF,IAAI,CAACgB,GAAG,CAAC,WAAW,CAAC,EAAEM,QAAQ,CAACjB,SAAS,CAAC;MAE1C,MAAMS,SAAS,GAAGjB,wBAAwB,CAACI,MAAM,CAAC;MAClD,MAAMgB,MAAM,GAAGH,SAAS,CAACd,IAAI,CAACgB,GAAG,CAAC,WAAW,CAAE,CAAC;MAEhDE,MAAM,CAACD,MAAM,CAAC,CAACQ,OAAO,CAAC;QAAEE,2BAA2B,EAAE;MAAI,CAAE,CAAC;IAC/D,CAAC,CAAC;IAEFd,EAAE,CAAC,6CAA6C,EAAE,MAAK;MACrD,MAAMU,eAAe,GAAG,IAAIf,IAAI,EAAE;MAClCe,eAAe,CAACF,WAAW,CAACnB,WAAW,CAAC;MACxCD,MAAM,CAACS,aAAa,GAAG,IAAIF,IAAI,CAACe,eAAe,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;MAC5DvB,MAAM,CAACM,gBAAgB,GAAG,IAAIC,IAAI,CAACe,eAAe,CAAC;MACnD,MAAMT,SAAS,GAAGjB,wBAAwB,CAACI,MAAM,CAAC;MAClD,MAAMI,SAAS,GAAG,IAAIG,IAAI,CAACe,eAAe,CAAC;MAC3ClB,SAAS,CAACmB,QAAQ,CAAC,CAAC,CAAC;MACrBxB,IAAI,CAACgB,GAAG,CAAC,WAAW,CAAC,EAAEM,QAAQ,CAACjB,SAAS,CAAC;MAC1C,MAAMY,MAAM,GAAGH,SAAS,CAACd,IAAI,CAACgB,GAAG,CAAC,WAAW,CAAE,CAAC;MAChDE,MAAM,CAACD,MAAM,CAAC,CAACE,QAAQ,EAAE;IAC3B,CAAC,CAAC;IAEFN,EAAE,CAAC,mDAAmD,EAAE,MAAK;MAC3D,MAAMU,eAAe,GAAG,IAAIf,IAAI,EAAE;MAClCe,eAAe,CAACF,WAAW,CAACnB,WAAW,CAAC;MACxCD,MAAM,CAACS,aAAa,GAAG,IAAIF,IAAI,CAACe,eAAe,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;MAC5DvB,MAAM,CAACU,MAAM,GAAG,IAAI;MACpB,MAAMG,SAAS,GAAGjB,wBAAwB,CAACI,MAAM,CAAC;MAClD,MAAMI,SAAS,GAAG,IAAIG,IAAI,CAACe,eAAe,CAAC;MAC3ClB,SAAS,CAACmB,QAAQ,CAAC,CAAC,CAAC;MACrBxB,IAAI,CAACgB,GAAG,CAAC,WAAW,CAAC,EAAEM,QAAQ,CAACjB,SAAS,CAAC;MAC1C,MAAMY,MAAM,GAAGH,SAAS,CAACd,IAAI,CAACgB,GAAG,CAAC,WAAW,CAAE,CAAC;MAChDE,MAAM,CAACD,MAAM,GAAG,cAAc,CAAC,CAAC,CAACW,aAAa,EAAE;IAClD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF9B,QAAQ,CAAC,qBAAqB,EAAE,MAAK;IACnCe,EAAE,CAAC,+EAA+E,EAAE,MAAK;MACvF,MAAMU,eAAe,GAAG,IAAIf,IAAI,EAAE;MAClCe,eAAe,CAACF,WAAW,CAACnB,WAAW,CAAC;MACxC,MAAMY,SAAS,GAAGjB,wBAAwB,CAACI,MAAM,CAAC;MAClDD,IAAI,CAACgB,GAAG,CAAC,WAAW,CAAC,EAAEM,QAAQ,CAAC,IAAId,IAAI,CAACe,eAAe,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;MACtExB,IAAI,CAACgB,GAAG,CAAC,SAAS,CAAC,EAAEM,QAAQ,CAAC,IAAId,IAAI,CAACe,eAAe,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;MACpE,MAAMP,MAAM,GAAGH,SAAS,CAACd,IAAI,CAACgB,GAAG,CAAC,SAAS,CAAE,CAAC;MAC9CE,MAAM,CAACD,MAAM,CAAC,CAACQ,OAAO,CAAC;QAAEI,sBAAsB,EAAE;MAAI,CAAE,CAAC;IAC1D,CAAC,CAAC;IAEFhB,EAAE,CAAC,2CAA2C,EAAE,MAAK;MACnD,MAAMU,eAAe,GAAG,IAAIf,IAAI,EAAE;MAClCe,eAAe,CAACF,WAAW,CAACnB,WAAW,CAAC;MACxC,MAAMY,SAAS,GAAGjB,wBAAwB,CAACI,MAAM,CAAC;MAClDD,IAAI,CAACgB,GAAG,CAAC,WAAW,CAAC,EAAEM,QAAQ,CAAC,IAAId,IAAI,CAACe,eAAe,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;MACtExB,IAAI,CAACgB,GAAG,CAAC,SAAS,CAAC,EAAEM,QAAQ,CAAC,IAAId,IAAI,CAACe,eAAe,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;MACpE,MAAMP,MAAM,GAAGH,SAAS,CAACd,IAAI,CAACgB,GAAG,CAAC,SAAS,CAAE,CAAC;MAC9CE,MAAM,CAACD,MAAM,CAAC,CAACE,QAAQ,EAAE;IAC3B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFrB,QAAQ,CAAC,8BAA8B,EAAE,MAAK;IAC5Ce,EAAE,CAAC,+FAA+F,EAAE,MAAK;MACvG,MAAMC,SAAS,GAAGjB,wBAAwB,CAACI,MAAM,CAAC;MAClDD,IAAI,CAACgB,GAAG,CAAC,WAAW,CAAC,EAAEM,QAAQ,CAAC,IAAId,IAAI,CAAC,YAAY,CAAC,CAAC;MACvDR,IAAI,CAACgB,GAAG,CAAC,kBAAkB,CAAC,EAAEM,QAAQ,CAAC,IAAId,IAAI,CAAC,YAAY,CAAC,CAAC;MAC9D,MAAMS,MAAM,GAAGH,SAAS,CAACd,IAAI,CAACgB,GAAG,CAAC,kBAAkB,CAAE,CAAC;MACvDE,MAAM,CAACD,MAAM,CAAC,CAACQ,OAAO,CAAC;QAAEK,8BAA8B,EAAE;MAAI,CAAE,CAAC;IAClE,CAAC,CAAC;IAEFjB,EAAE,CAAC,oDAAoD,EAAE,MAAK;MAC5D,MAAMC,SAAS,GAAGjB,wBAAwB,CAACI,MAAM,CAAC;MAClDD,IAAI,CAACgB,GAAG,CAAC,WAAW,CAAC,EAAEM,QAAQ,CAAC,IAAId,IAAI,CAAC,YAAY,CAAC,CAAC;MACvDR,IAAI,CAACgB,GAAG,CAAC,kBAAkB,CAAC,EAAEM,QAAQ,CAAC,IAAId,IAAI,CAAC,YAAY,CAAC,CAAC;MAC9D,MAAMS,MAAM,GAAGH,SAAS,CAACd,IAAI,CAACgB,GAAG,CAAC,kBAAkB,CAAE,CAAC;MACvDE,MAAM,CAACD,MAAM,CAAC,CAACE,QAAQ,EAAE;IAC3B,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}