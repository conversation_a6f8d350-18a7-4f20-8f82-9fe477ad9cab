
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for app/features/contractor-management/components/contractor-dialog</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../../index.html">All files</a> app/features/contractor-management/components/contractor-dialog</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">63.31% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>126/199</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">39.7% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>27/68</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">34.69% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>17/49</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">63.63% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>126/198</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="contractor-dialog.component.ts"><a href="contractor-dialog.component.ts.html">contractor-dialog.component.ts</a></td>
	<td data-value="63.31" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 63%"></div><div class="cover-empty" style="width: 37%"></div></div>
	</td>
	<td data-value="63.31" class="pct medium">63.31%</td>
	<td data-value="199" class="abs medium">126/199</td>
	<td data-value="39.7" class="pct low">39.7%</td>
	<td data-value="68" class="abs low">27/68</td>
	<td data-value="34.69" class="pct low">34.69%</td>
	<td data-value="49" class="abs low">17/49</td>
	<td data-value="63.63" class="pct medium">63.63%</td>
	<td data-value="198" class="abs medium">126/198</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-16T16:33:29.688Z
            </div>
        <script src="../../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../../sorter.js"></script>
        <script src="../../../../../block-navigation.js"></script>
    </body>
</html>
    