{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { LegalNatureService } from './legal-nature.service';\ndescribe('LegalNatureService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/legal-nature`;\n  const mockLegalNature = {\n    id: 1,\n    name: 'Test Legal Nature'\n  };\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [LegalNatureService]\n    });\n    service = TestBed.inject(LegalNatureService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all legal natures', () => {\n      const mockLegalNatures = [mockLegalNature];\n      service.getAll().subscribe(legalNatures => {\n        expect(legalNatures).toEqual(mockLegalNatures);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockLegalNatures);\n    });\n  });\n  describe('getById', () => {\n    it('should return a legal nature by id', () => {\n      const id = 1;\n      service.getById(id).subscribe(legalNature => {\n        expect(legalNature).toEqual(mockLegalNature);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockLegalNature);\n    });\n  });\n  describe('create', () => {\n    it('should create a new legal nature', () => {\n      const newLegalNature = {\n        name: 'New Legal Nature'\n      };\n      service.create(newLegalNature).subscribe(legalNature => {\n        expect(legalNature).toEqual(mockLegalNature);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newLegalNature);\n      req.flush(mockLegalNature);\n    });\n  });\n  describe('update', () => {\n    it('should update a legal nature', () => {\n      const id = 1;\n      const updateData = {\n        name: 'Updated Legal Nature'\n      };\n      service.update(id, updateData).subscribe(legalNature => {\n        expect(legalNature).toEqual(mockLegalNature);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockLegalNature);\n    });\n  });\n  describe('delete', () => {\n    it('should delete a legal nature', () => {\n      const id = 1;\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "LegalNatureService", "describe", "service", "httpMock", "apiUrl", "mockLegalNature", "id", "name", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockLegalNatures", "getAll", "subscribe", "legalNatures", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "getById", "legalNature", "newLegalNature", "create", "body", "updateData", "update", "delete", "nothing"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-management\\services\\legal-nature.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { LegalNature } from '@contractor-management/models/legal-nature.model';\nimport { environment } from '@env';\nimport { LegalNatureService } from './legal-nature.service';\n\ndescribe('LegalNatureService', () => {\n  let service: LegalNatureService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/legal-nature`;\n\n  const mockLegalNature: LegalNature = {\n    id: 1,\n    name: 'Test Legal Nature',\n  };\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [LegalNatureService],\n    });\n    service = TestBed.inject(LegalNatureService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all legal natures', () => {\n      const mockLegalNatures = [mockLegalNature];\n\n      service.getAll().subscribe((legalNatures) => {\n        expect(legalNatures).toEqual(mockLegalNatures);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockLegalNatures);\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a legal nature by id', () => {\n      const id = 1;\n\n      service.getById(id).subscribe((legalNature) => {\n        expect(legalNature).toEqual(mockLegalNature);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockLegalNature);\n    });\n  });\n\n  describe('create', () => {\n    it('should create a new legal nature', () => {\n      const newLegalNature: Omit<LegalNature, 'id'> = {\n        name: 'New Legal Nature',\n      };\n\n      service.create(newLegalNature).subscribe((legalNature) => {\n        expect(legalNature).toEqual(mockLegalNature);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newLegalNature);\n      req.flush(mockLegalNature);\n    });\n  });\n\n  describe('update', () => {\n    it('should update a legal nature', () => {\n      const id = 1;\n      const updateData: Partial<LegalNature> = {\n        name: 'Updated Legal Nature',\n      };\n\n      service.update(id, updateData).subscribe((legalNature) => {\n        expect(legalNature).toEqual(mockLegalNature);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockLegalNature);\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete a legal nature', () => {\n      const id = 1;\n\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,kBAAkB,QAAQ,wBAAwB;AAE3DC,QAAQ,CAAC,oBAAoB,EAAE,MAAK;EAClC,IAAIC,OAA2B;EAC/B,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,eAAe;EAEnD,MAAMC,eAAe,GAAgB;IACnCC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE;GACP;EAEDC,UAAU,CAAC,MAAK;IACdV,OAAO,CAACW,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACd,uBAAuB,CAAC;MAClCe,SAAS,EAAE,CAACX,kBAAkB;KAC/B,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACc,MAAM,CAACZ,kBAAkB,CAAC;IAC5CG,QAAQ,GAAGL,OAAO,CAACc,MAAM,CAACf,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFgB,SAAS,CAAC,MAAK;IACbV,QAAQ,CAACW,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACd,OAAO,CAAC,CAACe,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFhB,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,iCAAiC,EAAE,MAAK;MACzC,MAAMG,gBAAgB,GAAG,CAACb,eAAe,CAAC;MAE1CH,OAAO,CAACiB,MAAM,EAAE,CAACC,SAAS,CAAEC,YAAY,IAAI;QAC1CL,MAAM,CAACK,YAAY,CAAC,CAACC,OAAO,CAACJ,gBAAgB,CAAC;MAChD,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,gBAAgB,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjB,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBc,EAAE,CAAC,oCAAoC,EAAE,MAAK;MAC5C,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAAC2B,OAAO,CAACvB,EAAE,CAAC,CAACc,SAAS,CAAEU,WAAW,IAAI;QAC5Cd,MAAM,CAACc,WAAW,CAAC,CAACR,OAAO,CAACjB,eAAe,CAAC;MAC9C,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvB,eAAe,CAAC;IAC5B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,kCAAkC,EAAE,MAAK;MAC1C,MAAMgB,cAAc,GAA4B;QAC9CxB,IAAI,EAAE;OACP;MAEDL,OAAO,CAAC8B,MAAM,CAACD,cAAc,CAAC,CAACX,SAAS,CAAEU,WAAW,IAAI;QACvDd,MAAM,CAACc,WAAW,CAAC,CAACR,OAAO,CAACjB,eAAe,CAAC;MAC9C,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACS,cAAc,CAAC;MAChDR,GAAG,CAACK,KAAK,CAACvB,eAAe,CAAC;IAC5B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,8BAA8B,EAAE,MAAK;MACtC,MAAMT,EAAE,GAAG,CAAC;MACZ,MAAM4B,UAAU,GAAyB;QACvC3B,IAAI,EAAE;OACP;MAEDL,OAAO,CAACiC,MAAM,CAAC7B,EAAE,EAAE4B,UAAU,CAAC,CAACd,SAAS,CAAEU,WAAW,IAAI;QACvDd,MAAM,CAACc,WAAW,CAAC,CAACR,OAAO,CAACjB,eAAe,CAAC;MAC9C,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACY,UAAU,CAAC;MAC5CX,GAAG,CAACK,KAAK,CAACvB,eAAe,CAAC;IAC5B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,8BAA8B,EAAE,MAAK;MACtC,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAACkC,MAAM,CAAC9B,EAAE,CAAC,CAACc,SAAS,CAAC,MAAK;QAChCJ,MAAM,EAAE,CAACqB,OAAO,EAAE;MACpB,CAAC,CAAC;MAEF,MAAMd,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}