import { HttpClientTestingModule } from '@angular/common/http/testing';
import {
  ComponentFixture,
  TestBed,
  discardPeriodicTasks,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { Router } from '@angular/router';
import { AuthService } from '@core/auth/services/auth.service';
import { AlertService } from '@shared/services/alert.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { of, throwError } from 'rxjs';
import { KeycloackComponent } from './keycloack.component';
import { UserService } from '@core/auth/services/user.service';
import { ContractorToken } from '@core/auth/models/contractorToken.model';

interface CustomEvent {
  detail: string;
}

describe('KeycloackComponent', () => {
  let component: KeycloackComponent;
  let fixture: ComponentFixture<KeycloackComponent>;
  let authService: jasmine.SpyObj<AuthService>;
  let router: jasmine.SpyObj<Router>;
  let spinner: jasmine.SpyObj<NgxSpinnerService>;
  let alertService: jasmine.SpyObj<AlertService>;
  let userService: jasmine.SpyObj<UserService>;

  beforeEach(async () => {
    authService = jasmine.createSpyObj('AuthService', [
      'getUserProfiles',
      'hasProfile',
      'logout',
      'login',
    ]);
    router = jasmine.createSpyObj('Router', ['navigate']);
    spinner = jasmine.createSpyObj('NgxSpinnerService', ['show', 'hide']);
    alertService = jasmine.createSpyObj('AlertService', ['error', 'success']);
    userService = jasmine.createSpyObj('UserService', [
      'getCurrentSession',
      'createLogin',
    ]);

    await TestBed.configureTestingModule({
      imports: [
        KeycloackComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
      ],
      providers: [
        { provide: AuthService, useValue: authService },
        { provide: Router, useValue: router },
        { provide: NgxSpinnerService, useValue: spinner },
        { provide: AlertService, useValue: alertService },
        { provide: UserService, useValue: userService },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(KeycloackComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should handle failed login', () => {
    authService.login.and.returnValue(of(false));
    const mockResponse: ContractorToken = { token: 'test-token' };
    userService.createLogin.and.returnValue(of(mockResponse));

    const event = { detail: 'token' } as CustomEvent;
    component.onTokenResolved(event);

    expect(authService.login).toHaveBeenCalledWith('token');
    expect(alertService.error).toHaveBeenCalledWith('Credenciales inválidas');
  });

  it('should handle login error', () => {
    authService.login.and.returnValue(throwError(() => new Error('Error')));

    const event = { detail: 'token' } as CustomEvent;
    component.onTokenResolved(event);

    expect(authService.login).toHaveBeenCalledWith('token');
    expect(alertService.error).toHaveBeenCalledWith(
      'Error al intentar autenticarse',
    );
  });

  it('should start token monitoring on init', fakeAsync(() => {
    spyOn(component, 'monitorToken');
    component.ngOnInit();
    expect(component.monitorToken).toHaveBeenCalled();
  }));

  it('should handle successful login with administrator profile', () => {
    authService.login.and.returnValue(of(true));
    authService.getUserProfiles.and.returnValue([
      { profile_id: 1, profile_name: 'ADMINISTRATOR' },
    ]);

    const event = { detail: 'token' } as CustomEvent;
    component.onTokenResolved(event);

    expect(authService.login).toHaveBeenCalledWith('token');
    expect(router.navigate).toHaveBeenCalledWith(['/contratistas']);
    expect(alertService.success).toHaveBeenCalledWith('Autenticación exitosa');
  });

  it('should handle successful login for SUPERVISOR', () => {
    authService.login.and.returnValue(of(true));
    authService.getUserProfiles.and.returnValue([
      { profile_id: 2, profile_name: 'SUPERVISOR' },
    ]);

    const event = { detail: 'token' } as CustomEvent;
    component.onTokenResolved(event);

    expect(authService.login).toHaveBeenCalledWith('token');
    expect(router.navigate).toHaveBeenCalledWith(['/revisar-informes']);
    expect(alertService.success).toHaveBeenCalledWith('Autenticación exitosa');
  });

  it('should handle successful login for CONTRACTOR', () => {
    authService.login.and.returnValue(of(true));
    authService.getUserProfiles.and.returnValue([
      { profile_id: 3, profile_name: 'CONTRACTOR' },
    ]);

    const event = { detail: 'token' } as CustomEvent;
    component.onTokenResolved(event);

    expect(authService.login).toHaveBeenCalledWith('token');
    expect(router.navigate).toHaveBeenCalledWith(['/mis-contratos']);
    expect(alertService.success).toHaveBeenCalledWith('Autenticación exitosa');
  });

  it('should handle successful login for CONTRACT-MANAGER', () => {
    authService.login.and.returnValue(of(true));
    authService.getUserProfiles.and.returnValue([
      { profile_id: 4, profile_name: 'CONTRACT-MANAGER' },
    ]);

    const event = { detail: 'token' } as CustomEvent;
    component.onTokenResolved(event);

    expect(authService.login).toHaveBeenCalledWith('token');
    expect(router.navigate).toHaveBeenCalledWith(['/contratos']);
    expect(alertService.success).toHaveBeenCalledWith('Autenticación exitosa');
  });

  it('should handle successful login for unknown profile', () => {
    authService.login.and.returnValue(of(true));
    authService.getUserProfiles.and.returnValue([
      { profile_id: 5, profile_name: 'UNKNOWN' },
    ]);

    const event = { detail: 'token' } as CustomEvent;
    component.onTokenResolved(event);

    expect(authService.login).toHaveBeenCalledWith('token');
    expect(router.navigate).toHaveBeenCalledWith(['/']);
    expect(alertService.success).toHaveBeenCalledWith('Autenticación exitosa');
  });

  it('should navigate to login on logout', () => {
    component.getUrlLogOut();
    expect(router.navigate).toHaveBeenCalledWith(['/login']);
  });

  it('should monitor token and navigate to login when token is missing', fakeAsync(() => {
    spyOn(localStorage, 'getItem').and.returnValue(null);
    component.monitorToken();
    tick(60000);
    expect(router.navigate).toHaveBeenCalledWith(['/login']);
    discardPeriodicTasks();
  }));

  it('should not navigate to login when token exists', fakeAsync(() => {
    spyOn(localStorage, 'getItem').and.returnValue('valid-token');
    component.monitorToken();
    tick(60000);
    expect(router.navigate).not.toHaveBeenCalled();
    discardPeriodicTasks();
  }));
});