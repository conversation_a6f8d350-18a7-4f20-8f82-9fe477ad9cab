import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { BankAccountType } from '@contractor-dashboard/models/bank-account-type.model';
import { environment } from '@env';
import { BankAccountTypeService } from './bank-account-type.service';

describe('BankAccountTypeService', () => {
  let service: BankAccountTypeService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/bank-account-types`;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [BankAccountTypeService],
    });
    service = TestBed.inject(BankAccountTypeService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  const mockBankAccountType: BankAccountType = {
    id: 1,
    name: 'Savings Account',
  };

  describe('getAll', () => {
    it('should return all bank account types', () => {
      const mockBankAccountTypes = [mockBankAccountType];

      service.getAll().subscribe((bankAccountTypes) => {
        expect(bankAccountTypes).toEqual(mockBankAccountTypes);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockBankAccountTypes);
    });

    it('should handle error when getting all bank account types', () => {
      service.getAll().subscribe({
        error: (error) => {
          expect(error.status).toBe(500);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Server error'), { status: 500 });
    });
  });

  describe('getById', () => {
    it('should return a bank account type by id', () => {
      service.getById(1).subscribe((bankAccountType) => {
        expect(bankAccountType).toEqual(mockBankAccountType);
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('GET');
      req.flush(mockBankAccountType);
    });

    it('should handle error when getting bank account type by id', () => {
      service.getById(999).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/999`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('create', () => {
    const newBankAccountType: Omit<BankAccountType, 'id'> = {
      name: 'New Account Type',
    };

    it('should create a new bank account type', () => {
      service.create(newBankAccountType).subscribe((bankAccountType) => {
        expect(bankAccountType).toEqual(mockBankAccountType);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newBankAccountType);
      req.flush(mockBankAccountType);
    });

    it('should handle error when creating bank account type', () => {
      service.create(newBankAccountType).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('update', () => {
    const updateData: Partial<BankAccountType> = {
      name: 'Updated Account Type',
    };

    it('should update a bank account type', () => {
      service.update(1, updateData).subscribe((bankAccountType) => {
        expect(bankAccountType).toEqual({
          ...mockBankAccountType,
          ...updateData,
        });
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush({ ...mockBankAccountType, ...updateData });
    });

    it('should handle error when updating bank account type', () => {
      service.update(1, updateData).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('delete', () => {
    it('should delete a bank account type', () => {
      service.delete(1).subscribe((response) => {
        expect(response).toBeNull();
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });

    it('should handle error when deleting bank account type', () => {
      service.delete(1).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('getByName', () => {
    it('should return a bank account type by name', () => {
      const name = 'Savings Account';

      service.getByName(name).subscribe((bankAccountType) => {
        expect(bankAccountType).toEqual(mockBankAccountType);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockBankAccountType);
    });

    it('should handle error when getting bank account type by name', () => {
      const name = 'NonExistent';

      service.getByName(name).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });
});