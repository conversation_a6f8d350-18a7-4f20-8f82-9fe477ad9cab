import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { InitialReportDocumentation } from '@contractor-dashboard/models/initial-report-documentation.model';
import { ArlService } from '@contractor-dashboard/services/arl.service';
import { PensionFundService } from '@contractor-dashboard/services/pension-fund.service';
import { EpsService } from '@contractor-management/services/eps.service';
import { AlertService } from '@shared/services/alert.service';
import { of, throwError } from 'rxjs';
import { SocialSecurityInfoComponent } from './social-security-info.component';

describe('SocialSecurityInfoComponent', () => {
  let component: SocialSecurityInfoComponent;
  let fixture: ComponentFixture<SocialSecurityInfoComponent>;
  let alertService: jasmine.SpyObj<AlertService>;
  let epsService: jasmine.SpyObj<EpsService>;
  let arlService: jasmine.SpyObj<ArlService>;
  let pensionFundService: jasmine.SpyObj<PensionFundService>;

  const mockEpsList = [
    { id: 1, name: 'EPS 1' },
    { id: 2, name: 'EPS 2' },
  ];

  const mockArlList = [
    { id: 1, name: 'ARL 1' },
    { id: 2, name: 'ARL 2' },
  ];

  const mockPensionFundList = [
    { id: 1, name: 'Pension Fund 1' },
    { id: 2, name: 'Pension Fund 2' },
  ];

  beforeEach(async () => {
    alertService = jasmine.createSpyObj('AlertService', ['error']);
    epsService = jasmine.createSpyObj('EpsService', ['getAll']);
    arlService = jasmine.createSpyObj('ArlService', ['getAll']);
    pensionFundService = jasmine.createSpyObj('PensionFundService', ['getAll']);

    epsService.getAll.and.returnValue(of(mockEpsList));
    arlService.getAll.and.returnValue(of(mockArlList));
    pensionFundService.getAll.and.returnValue(of(mockPensionFundList));

    await TestBed.configureTestingModule({
      imports: [
        ReactiveFormsModule,
        BrowserAnimationsModule,
        SocialSecurityInfoComponent,
      ],
      providers: [
        FormBuilder,
        { provide: AlertService, useValue: alertService },
        { provide: EpsService, useValue: epsService },
        { provide: ArlService, useValue: arlService },
        { provide: PensionFundService, useValue: pensionFundService },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(SocialSecurityInfoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load initial data on init', fakeAsync(() => {
    tick();

    expect(component.epsList).toEqual(mockEpsList);
    expect(component.arlList).toEqual(mockArlList);
    expect(component.pensionFundList).toEqual(mockPensionFundList);
  }));

  it('should handle error when loading initial data', fakeAsync(() => {
    epsService.getAll.and.returnValue(throwError(() => new Error()));

    component.ngOnInit();
    tick();

    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar los datos del formulario',
    );
  }));

  it('should update form with initial data on changes', () => {
    const mockInitialData: Partial<InitialReportDocumentation> = {
      epsId: 1,
      arlId: 2,
      pensionFundId: 1,
    };

    component.ngOnChanges({
      initialData: {
        currentValue: mockInitialData as InitialReportDocumentation,
        firstChange: true,
        previousValue: undefined,
        isFirstChange: () => true,
      },
    });

    expect(component.form.get('eps')?.value).toBe(1);
    expect(component.form.get('arl')?.value).toBe(2);
    expect(component.form.get('pensionFund')?.value).toBe(1);
  });

  it('should handle file selection with valid PDF file', () => {
    const mockFile = new File([''], 'test.pdf', { type: 'application/pdf' });
    const mockEvent = { target: { files: [mockFile] } } as unknown as Event;

    component.onFileSelected(mockEvent, 'epsSupportFile');

    expect(component.epsCertificateFile).toBe(mockFile);
    expect(component.epsCertificateFileName).toBe('test.pdf');
    expect(component.form.get('epsSupportFile')?.value).toBe(mockFile);
  });

  it('should show error for non-PDF file', () => {
    const mockFile = new File([''], 'test.jpg', { type: 'image/jpeg' });
    const mockEvent = { target: { files: [mockFile] } } as unknown as Event;

    component.onFileSelected(mockEvent, 'epsSupportFile');

    expect(alertService.error).toHaveBeenCalledWith(
      'Solo se permiten archivos PDF',
    );
    expect(component.epsCertificateFile).toBeNull();
  });

  it('should show error for file larger than 1MB', () => {
    const mockFile = new File(['x'.repeat(1024 * 1024 + 1)], 'large.pdf', {
      type: 'application/pdf',
    });
    const mockEvent = { target: { files: [mockFile] } } as unknown as Event;

    component.onFileSelected(mockEvent, 'epsSupportFile');

    expect(alertService.error).toHaveBeenCalledWith(
      'El archivo no debe superar 1MB',
    );
    expect(component.epsCertificateFile).toBeNull();
  });

  it('should validate file existence', () => {
    const mockFile = new File([''], 'test.pdf', { type: 'application/pdf' });
    component.form.patchValue({ epsSupportFile: mockFile });

    expect(component.isFileValid('epsSupportFile')).toBeTrue();
  });

  it('should validate file with existing URL', () => {
    const mockInitialData: Partial<InitialReportDocumentation> = {
      epsCertificateFileUrl: 'http://example.com/file.pdf',
    };
    component.initialData = mockInitialData as InitialReportDocumentation;

    expect(component.isFileValid('epsSupportFile')).toBeTrue();
  });

  it('should validate form with all required fields and files', () => {
    const mockFile = new File([''], 'test.pdf', { type: 'application/pdf' });
    component.form.patchValue({
      eps: 1,
      arl: 1,
      pensionFund: 1,
      epsSupportFile: mockFile,
      arlSupportFile: mockFile,
      pensionFundSupportFile: mockFile,
    });

    expect(component.isValid).toBeTrue();
  });

  it('should open file in new tab when downloading', () => {
    const mockInitialData: Partial<InitialReportDocumentation> = {
      epsCertificateFileUrl: 'http://example.com/file.pdf',
    };
    component.initialData = mockInitialData as InitialReportDocumentation;

    const windowSpy = spyOn(window, 'open');
    component.downloadFile('eps');

    expect(windowSpy).toHaveBeenCalledWith(
      'http://example.com/file.pdf',
      '_blank',
    );
  });

  it('should get entity names correctly', () => {
    expect(component.getEpsName(1)).toBe('EPS 1');
    expect(component.getArlName(2)).toBe('ARL 2');
    expect(component.getPensionFundName(1)).toBe('Pension Fund 1');
  });

  it('should emit form change event on value changes', () => {
    const emitSpy = spyOn(component.formChange, 'emit');
    component.form.patchValue({ eps: 1 });
    expect(emitSpy).toHaveBeenCalled();
  });

  describe('Error handling in service calls', () => {
    it('should handle errors when loading EPS data', () => {
      epsService.getAll.and.returnValue(
        throwError(() => new Error('Error loading EPS')),
      );
      component.ngOnInit();

      expect(alertService.error).toHaveBeenCalled();
    });

    it('should handle errors when loading pension fund data', () => {
      pensionFundService.getAll.and.returnValue(
        throwError(() => new Error('Error loading pension funds')),
      );
      component.ngOnInit();

      expect(alertService.error).toHaveBeenCalled();
    });

    it('should handle multiple service errors simultaneously', () => {
      epsService.getAll.and.returnValue(
        throwError(() => new Error('Error loading EPS')),
      );
      pensionFundService.getAll.and.returnValue(
        throwError(() => new Error('Error loading pension funds')),
      );

      component.ngOnInit();

      expect(alertService.error).toHaveBeenCalled();
    });
  });
});
