{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { of } from 'rxjs';\nimport { environment } from '@env';\nimport { ContractorContractService } from './contractor-contract.service';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { ContractAuditHistoryService } from './contract-audit-history.service';\nimport { ContractAuditStatusService } from './contract-audit-status.service';\nimport { ContractorService } from '@contractor-management/services/contractor.service';\ndescribe('ContractorContractService', () => {\n  let service;\n  let httpMock;\n  let authServiceSpy;\n  let contractAuditHistoryServiceSpy;\n  let contractAuditStatusServiceSpy;\n  let contractorServiceSpy;\n  const apiUrl = `${environment.apiUrl}/contractor-contracts`;\n  const mockContractorContract = {\n    id: 1,\n    subscriptionDate: '2024-02-20',\n    contractStartDate: '2024-02-20',\n    contractEndDate: '2024-12-31',\n    warranty: false,\n    typeWarrantyId: 1,\n    insuredRisksId: 1,\n    contractorId: 1,\n    contractId: 1\n  };\n  const mockUser = {\n    id: 1,\n    username: 'testuser',\n    profiles: []\n  };\n  const mockContractor = {\n    id: 1,\n    fullName: 'Test Contractor',\n    idNumber: 12345678,\n    personalEmail: '<EMAIL>'\n  };\n  const mockAuditStatus = {\n    id: 1,\n    name: 'Cesión de contrato',\n    description: 'Audit status for contract transfer'\n  };\n  const mockTerminationStatus = {\n    id: 2,\n    name: 'Terminación anticipada',\n    description: 'Audit status for early termination'\n  };\n  const mockAuditHistory = {\n    id: 1,\n    contractId: 1,\n    auditStatusId: 1,\n    auditDate: new Date(),\n    comment: 'Test comment',\n    auditorId: 1\n  };\n  beforeEach(() => {\n    authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);\n    contractAuditHistoryServiceSpy = jasmine.createSpyObj('ContractAuditHistoryService', ['create']);\n    contractAuditStatusServiceSpy = jasmine.createSpyObj('ContractAuditStatusService', ['getByName']);\n    contractorServiceSpy = jasmine.createSpyObj('ContractorService', ['getById']);\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [ContractorContractService, {\n        provide: AuthService,\n        useValue: authServiceSpy\n      }, {\n        provide: ContractAuditHistoryService,\n        useValue: contractAuditHistoryServiceSpy\n      }, {\n        provide: ContractAuditStatusService,\n        useValue: contractAuditStatusServiceSpy\n      }, {\n        provide: ContractorService,\n        useValue: contractorServiceSpy\n      }]\n    });\n    service = TestBed.inject(ContractorContractService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all contractor contracts', () => {\n      const mockContractorContracts = [mockContractorContract];\n      service.getAll().subscribe(contractorContracts => {\n        expect(contractorContracts).toEqual(mockContractorContracts);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractorContracts);\n    });\n    it('should handle error when getting all contractor contracts', () => {\n      service.getAll().subscribe({\n        next: () => fail('should have failed'),\n        error: error => {\n          expect(error.status).toBe(500);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.flush('Internal Server Error', {\n        status: 500,\n        statusText: 'Internal Server Error'\n      });\n    });\n  });\n  describe('getById', () => {\n    it('should return a contractor contract by id', () => {\n      const id = 1;\n      service.getById(id).subscribe(contractorContract => {\n        expect(contractorContract).toEqual(mockContractorContract);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractorContract);\n    });\n    it('should handle error when getting contractor contract by id', () => {\n      const id = 999;\n      service.getById(id).subscribe({\n        next: () => fail('should have failed'),\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.flush('Not Found', {\n        status: 404,\n        statusText: 'Not Found'\n      });\n    });\n  });\n  describe('getAllByContractorId', () => {\n    it('should return all contractor contracts by contractor id', () => {\n      const contractorId = 1;\n      const mockContractorContracts = [mockContractorContract];\n      service.getAllByContractorId(contractorId).subscribe(contractorContracts => {\n        expect(contractorContracts).toEqual(mockContractorContracts);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/contractor/${contractorId}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractorContracts);\n    });\n    it('should handle error when getting contractor contracts by contractor id', () => {\n      const contractorId = 999;\n      service.getAllByContractorId(contractorId).subscribe({\n        next: () => fail('should have failed'),\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/contractor/${contractorId}`);\n      req.flush('Not Found', {\n        status: 404,\n        statusText: 'Not Found'\n      });\n    });\n  });\n  describe('getAllByContractId', () => {\n    it('should return all contractor contracts by contract id', () => {\n      const contractId = 1;\n      const mockContractorContracts = [mockContractorContract];\n      service.getAllByContractId(contractId).subscribe(contractorContracts => {\n        expect(contractorContracts).toEqual(mockContractorContracts);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractorContracts);\n    });\n    it('should handle error when getting contractor contracts by contract id', () => {\n      const contractId = 999;\n      service.getAllByContractId(contractId).subscribe({\n        next: () => fail('should have failed'),\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}`);\n      req.flush('Not Found', {\n        status: 404,\n        statusText: 'Not Found'\n      });\n    });\n  });\n  describe('getByContractorAndContractId', () => {\n    it('should return a contractor contract by contractor and contract id', () => {\n      const contractorId = 1;\n      const contractId = 1;\n      service.getByContractorAndContractId(contractorId, contractId).subscribe(contractorContract => {\n        expect(contractorContract).toEqual(mockContractorContract);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/contractor/${contractorId}/contract/${contractId}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractorContract);\n    });\n    it('should handle error when getting contractor contract by contractor and contract id', () => {\n      const contractorId = 999;\n      const contractId = 999;\n      service.getByContractorAndContractId(contractorId, contractId).subscribe({\n        next: () => fail('should have failed'),\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/contractor/${contractorId}/contract/${contractId}`);\n      req.flush('Not Found', {\n        status: 404,\n        statusText: 'Not Found'\n      });\n    });\n  });\n  describe('getLatestTerminationDate', () => {\n    it('should return the latest termination date for a contract', () => {\n      const contractId = 1;\n      const mockDate = '2024-12-31';\n      service.getLatestTerminationDate(contractId).subscribe(date => {\n        expect(date).toEqual(mockDate);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}/latest-termination-date`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockDate);\n    });\n    it('should handle error when getting latest termination date', () => {\n      const contractId = 999;\n      service.getLatestTerminationDate(contractId).subscribe({\n        next: () => fail('should have failed'),\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}/latest-termination-date`);\n      req.flush('Not Found', {\n        status: 404,\n        statusText: 'Not Found'\n      });\n    });\n  });\n  describe('hasActiveContract', () => {\n    it('should check if contractor has active contract - true case', () => {\n      const contractorId = 1;\n      service.hasActiveContract(contractorId).subscribe(hasActive => {\n        expect(hasActive).toBeTrue();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/contractor/${contractorId}/has-active-contract`);\n      expect(req.request.method).toBe('GET');\n      req.flush(true);\n    });\n    it('should check if contractor has active contract - false case', () => {\n      const contractorId = 2;\n      service.hasActiveContract(contractorId).subscribe(hasActive => {\n        expect(hasActive).toBeFalse();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/contractor/${contractorId}/has-active-contract`);\n      expect(req.request.method).toBe('GET');\n      req.flush(false);\n    });\n    it('should handle error when checking if contractor has active contract', () => {\n      const contractorId = 999;\n      service.hasActiveContract(contractorId).subscribe({\n        next: () => fail('should have failed'),\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/contractor/${contractorId}/has-active-contract`);\n      req.flush('Not Found', {\n        status: 404,\n        statusText: 'Not Found'\n      });\n    });\n  });\n  describe('create', () => {\n    it('should create a new contractor contract with audit when user is authenticated', () => {\n      const newContractorContract = {\n        subscriptionDate: '2024-02-20',\n        contractStartDate: '2024-02-20',\n        contractEndDate: '2024-12-31',\n        warranty: false,\n        typeWarrantyId: 1,\n        insuredRisksId: 1,\n        contractorId: 1,\n        contractId: 1\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n      contractorServiceSpy.getById.and.returnValue(of(mockContractor));\n      contractAuditStatusServiceSpy.getByName.and.returnValue(of(mockAuditStatus));\n      contractAuditHistoryServiceSpy.create.and.returnValue(of(mockAuditHistory));\n      service.create(newContractorContract).subscribe(contractorContract => {\n        expect(contractorContract).toEqual(mockContractorContract);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractorServiceSpy.getById).toHaveBeenCalledWith(1);\n        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith('Cesión de contrato');\n        expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newContractorContract);\n      req.flush(mockContractorContract);\n    });\n    it('should create a new contractor contract without audit when user is not authenticated', () => {\n      const newContractorContract = {\n        subscriptionDate: '2024-02-20',\n        contractStartDate: '2024-02-20',\n        contractEndDate: '2024-12-31',\n        warranty: false,\n        typeWarrantyId: 1,\n        insuredRisksId: 1,\n        contractorId: 1,\n        contractId: 1\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n      service.create(newContractorContract).subscribe(contractorContract => {\n        expect(contractorContract).toEqual(mockContractorContract);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractorServiceSpy.getById).not.toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n        expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newContractorContract);\n      req.flush(mockContractorContract);\n    });\n    it('should handle error when creating a new contractor contract', () => {\n      const newContractorContract = {\n        subscriptionDate: '2024-02-20',\n        contractStartDate: '2024-02-20',\n        contractEndDate: '2024-12-31',\n        warranty: false,\n        typeWarrantyId: 1,\n        insuredRisksId: 1,\n        contractorId: 1,\n        contractId: 1\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n      service.create(newContractorContract).subscribe({\n        next: () => fail('should have failed'),\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.flush('Bad Request', {\n        status: 400,\n        statusText: 'Bad Request'\n      });\n    });\n  });\n  describe('earlyTerminateContract', () => {\n    it('should early terminate a contract with audit when user is authenticated', () => {\n      const terminationData = {\n        contractorId: 1,\n        contractId: 1,\n        endDate: '2024-03-01',\n        terminationReason: 'Test reason'\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n      contractAuditStatusServiceSpy.getByName.and.returnValue(of(mockTerminationStatus));\n      contractAuditHistoryServiceSpy.create.and.returnValue(of(mockAuditHistory));\n      service.earlyTerminateContract(terminationData).subscribe(contractorContract => {\n        expect(contractorContract).toEqual(mockContractorContract);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith('Terminación anticipada');\n        expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/early-termination`);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(terminationData);\n      req.flush(mockContractorContract);\n    });\n    it('should early terminate a contract without audit when user is not authenticated', () => {\n      const terminationData = {\n        contractorId: 1,\n        contractId: 1,\n        endDate: '2024-03-01',\n        terminationReason: 'Test reason'\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n      service.earlyTerminateContract(terminationData).subscribe(contractorContract => {\n        expect(contractorContract).toEqual(mockContractorContract);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n        expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/early-termination`);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(terminationData);\n      req.flush(mockContractorContract);\n    });\n    it('should handle error when early terminating a contract', () => {\n      const terminationData = {\n        contractorId: 1,\n        contractId: 1,\n        endDate: '2024-03-01',\n        terminationReason: 'Test reason'\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n      service.earlyTerminateContract(terminationData).subscribe({\n        next: () => fail('should have failed'),\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/early-termination`);\n      req.flush('Bad Request', {\n        status: 400,\n        statusText: 'Bad Request'\n      });\n    });\n  });\n  describe('update', () => {\n    it('should update a contractor contract', () => {\n      const id = 1;\n      const updateData = {\n        contractEndDate: '2024-06-30'\n      };\n      service.update(id, updateData).subscribe(contractorContract => {\n        expect(contractorContract).toEqual(mockContractorContract);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockContractorContract);\n    });\n    it('should handle error when updating a contractor contract', () => {\n      const id = 999;\n      const updateData = {\n        contractEndDate: '2024-06-30'\n      };\n      service.update(id, updateData).subscribe({\n        next: () => fail('should have failed'),\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.flush('Not Found', {\n        status: 404,\n        statusText: 'Not Found'\n      });\n    });\n  });\n  describe('delete', () => {\n    it('should delete a contractor contract', () => {\n      const id = 1;\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n    it('should handle error when deleting a contractor contract', () => {\n      const id = 999;\n      service.delete(id).subscribe({\n        next: () => fail('should have failed'),\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.flush('Not Found', {\n        status: 404,\n        statusText: 'Not Found'\n      });\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "of", "environment", "ContractorContractService", "AuthService", "ContractAuditHistoryService", "ContractAuditStatusService", "ContractorService", "describe", "service", "httpMock", "authServiceSpy", "contractAuditHistoryServiceSpy", "contractAuditStatusServiceSpy", "contractorServiceSpy", "apiUrl", "mockContractorContract", "id", "subscriptionDate", "contractStartDate", "contractEndDate", "warranty", "typeWarrantyId", "insuredRisksId", "contractorId", "contractId", "mockUser", "username", "profiles", "mockContractor", "fullName", "idNumber", "personalEmail", "mockAuditStatus", "name", "description", "mockTerminationStatus", "mockAuditHistory", "auditStatusId", "auditDate", "Date", "comment", "auditorId", "beforeEach", "jasmine", "createSpyObj", "configureTestingModule", "imports", "providers", "provide", "useValue", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockContractorContracts", "getAll", "subscribe", "contractorContracts", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "next", "fail", "error", "status", "statusText", "getById", "contractorContract", "getAllByContractorId", "getAllByContractId", "getByContractorAndContractId", "mockDate", "getLatestTerminationDate", "date", "hasActiveContract", "hasActive", "toBeTrue", "toBeFalse", "newContractorContract", "getCurrentUser", "and", "returnValue", "getByName", "create", "toHaveBeenCalled", "toHaveBeenCalledWith", "body", "not", "terminationData", "endDate", "terminationReason", "earlyTerminateContract", "updateData", "update", "delete", "nothing"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\services\\contractor-contract.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { of } from 'rxjs';\nimport { ContractEarlyTermination } from '@contract-management/models/contract-early-termination.model';\nimport { ContractorContract } from '@contract-management/models/contractor-contract.model';\nimport { environment } from '@env';\nimport { ContractorContractService } from './contractor-contract.service';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { ContractAuditHistoryService } from './contract-audit-history.service';\nimport { ContractAuditStatusService } from './contract-audit-status.service';\nimport { ContractorService } from '@contractor-management/services/contractor.service';\nimport { User } from '@core/auth/models/user.model';\nimport { Contractor } from '@contractor-management/models/contractor.model';\nimport { ContractAuditStatus } from '@contract-management/models/contract-audit-status.model';\nimport { ContractAuditHistory } from '@contract-management/models/contract-audit-history.model';\n\ndescribe('ContractorContractService', () => {\n  let service: ContractorContractService;\n  let httpMock: HttpTestingController;\n  let authServiceSpy: jasmine.SpyObj<AuthService>;\n  let contractAuditHistoryServiceSpy: jasmine.SpyObj<ContractAuditHistoryService>;\n  let contractAuditStatusServiceSpy: jasmine.SpyObj<ContractAuditStatusService>;\n  let contractorServiceSpy: jasmine.SpyObj<ContractorService>;\n\n  const apiUrl = `${environment.apiUrl}/contractor-contracts`;\n\n  const mockContractorContract: ContractorContract = {\n    id: 1,\n    subscriptionDate: '2024-02-20',\n    contractStartDate: '2024-02-20',\n    contractEndDate: '2024-12-31',\n    warranty: false,\n    typeWarrantyId: 1,\n    insuredRisksId: 1,\n    contractorId: 1,\n    contractId: 1,\n  };\n\n  const mockUser: User = { id: 1, username: 'testuser', profiles: [] };\n\n  const mockContractor: Partial<Contractor> = {\n    id: 1,\n    fullName: 'Test Contractor',\n    idNumber: 12345678,\n    personalEmail: '<EMAIL>',\n\n  };\n\n  const mockAuditStatus: ContractAuditStatus = {\n    id: 1,\n    name: 'Cesión de contrato',\n    description: 'Audit status for contract transfer',\n  };\n\n  const mockTerminationStatus: ContractAuditStatus = {\n    id: 2,\n    name: 'Terminación anticipada',\n    description: 'Audit status for early termination',\n  };\n\n  const mockAuditHistory: ContractAuditHistory = {\n    id: 1,\n    contractId: 1,\n    auditStatusId: 1,\n    auditDate: new Date(),\n    comment: 'Test comment',\n    auditorId: 1,\n  };\n\n  beforeEach(() => {\n    authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);\n    contractAuditHistoryServiceSpy = jasmine.createSpyObj(\n      'ContractAuditHistoryService',\n      ['create'],\n    );\n    contractAuditStatusServiceSpy = jasmine.createSpyObj(\n      'ContractAuditStatusService',\n      ['getByName'],\n    );\n    contractorServiceSpy = jasmine.createSpyObj('ContractorService', [\n      'getById',\n    ]);\n\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [\n        ContractorContractService,\n        { provide: AuthService, useValue: authServiceSpy },\n        {\n          provide: ContractAuditHistoryService,\n          useValue: contractAuditHistoryServiceSpy,\n        },\n        {\n          provide: ContractAuditStatusService,\n          useValue: contractAuditStatusServiceSpy,\n        },\n        { provide: ContractorService, useValue: contractorServiceSpy },\n      ],\n    });\n    service = TestBed.inject(ContractorContractService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all contractor contracts', () => {\n      const mockContractorContracts = [mockContractorContract];\n\n      service.getAll().subscribe((contractorContracts) => {\n        expect(contractorContracts).toEqual(mockContractorContracts);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractorContracts);\n    });\n\n    it('should handle error when getting all contractor contracts', () => {\n      service.getAll().subscribe({\n        next: () => fail('should have failed'),\n        error: (error) => {\n          expect(error.status).toBe(500);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.flush('Internal Server Error', {\n        status: 500,\n        statusText: 'Internal Server Error',\n      });\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a contractor contract by id', () => {\n      const id = 1;\n\n      service.getById(id).subscribe((contractorContract) => {\n        expect(contractorContract).toEqual(mockContractorContract);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractorContract);\n    });\n\n    it('should handle error when getting contractor contract by id', () => {\n      const id = 999;\n\n      service.getById(id).subscribe({\n        next: () => fail('should have failed'),\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.flush('Not Found', { status: 404, statusText: 'Not Found' });\n    });\n  });\n\n  describe('getAllByContractorId', () => {\n    it('should return all contractor contracts by contractor id', () => {\n      const contractorId = 1;\n      const mockContractorContracts = [mockContractorContract];\n\n      service\n        .getAllByContractorId(contractorId)\n        .subscribe((contractorContracts) => {\n          expect(contractorContracts).toEqual(mockContractorContracts);\n        });\n\n      const req = httpMock.expectOne(`${apiUrl}/contractor/${contractorId}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractorContracts);\n    });\n\n    it('should handle error when getting contractor contracts by contractor id', () => {\n      const contractorId = 999;\n\n      service.getAllByContractorId(contractorId).subscribe({\n        next: () => fail('should have failed'),\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/contractor/${contractorId}`);\n      req.flush('Not Found', { status: 404, statusText: 'Not Found' });\n    });\n  });\n\n  describe('getAllByContractId', () => {\n    it('should return all contractor contracts by contract id', () => {\n      const contractId = 1;\n      const mockContractorContracts = [mockContractorContract];\n\n      service\n        .getAllByContractId(contractId)\n        .subscribe((contractorContracts) => {\n          expect(contractorContracts).toEqual(mockContractorContracts);\n        });\n\n      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractorContracts);\n    });\n\n    it('should handle error when getting contractor contracts by contract id', () => {\n      const contractId = 999;\n\n      service.getAllByContractId(contractId).subscribe({\n        next: () => fail('should have failed'),\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}`);\n      req.flush('Not Found', { status: 404, statusText: 'Not Found' });\n    });\n  });\n\n  describe('getByContractorAndContractId', () => {\n    it('should return a contractor contract by contractor and contract id', () => {\n      const contractorId = 1;\n      const contractId = 1;\n\n      service\n        .getByContractorAndContractId(contractorId, contractId)\n        .subscribe((contractorContract) => {\n          expect(contractorContract).toEqual(mockContractorContract);\n        });\n\n      const req = httpMock.expectOne(\n        `${apiUrl}/contractor/${contractorId}/contract/${contractId}`,\n      );\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractorContract);\n    });\n\n    it('should handle error when getting contractor contract by contractor and contract id', () => {\n      const contractorId = 999;\n      const contractId = 999;\n\n      service.getByContractorAndContractId(contractorId, contractId).subscribe({\n        next: () => fail('should have failed'),\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(\n        `${apiUrl}/contractor/${contractorId}/contract/${contractId}`,\n      );\n      req.flush('Not Found', { status: 404, statusText: 'Not Found' });\n    });\n  });\n\n  describe('getLatestTerminationDate', () => {\n    it('should return the latest termination date for a contract', () => {\n      const contractId = 1;\n      const mockDate = '2024-12-31';\n\n      service.getLatestTerminationDate(contractId).subscribe((date) => {\n        expect(date).toEqual(mockDate);\n      });\n\n      const req = httpMock.expectOne(\n        `${apiUrl}/contract/${contractId}/latest-termination-date`,\n      );\n      expect(req.request.method).toBe('GET');\n      req.flush(mockDate);\n    });\n\n    it('should handle error when getting latest termination date', () => {\n      const contractId = 999;\n\n      service.getLatestTerminationDate(contractId).subscribe({\n        next: () => fail('should have failed'),\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(\n        `${apiUrl}/contract/${contractId}/latest-termination-date`,\n      );\n      req.flush('Not Found', { status: 404, statusText: 'Not Found' });\n    });\n  });\n\n  describe('hasActiveContract', () => {\n    it('should check if contractor has active contract - true case', () => {\n      const contractorId = 1;\n\n      service.hasActiveContract(contractorId).subscribe((hasActive) => {\n        expect(hasActive).toBeTrue();\n      });\n\n      const req = httpMock.expectOne(\n        `${apiUrl}/contractor/${contractorId}/has-active-contract`,\n      );\n      expect(req.request.method).toBe('GET');\n      req.flush(true);\n    });\n\n    it('should check if contractor has active contract - false case', () => {\n      const contractorId = 2;\n\n      service.hasActiveContract(contractorId).subscribe((hasActive) => {\n        expect(hasActive).toBeFalse();\n      });\n\n      const req = httpMock.expectOne(\n        `${apiUrl}/contractor/${contractorId}/has-active-contract`,\n      );\n      expect(req.request.method).toBe('GET');\n      req.flush(false);\n    });\n\n    it('should handle error when checking if contractor has active contract', () => {\n      const contractorId = 999;\n\n      service.hasActiveContract(contractorId).subscribe({\n        next: () => fail('should have failed'),\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(\n        `${apiUrl}/contractor/${contractorId}/has-active-contract`,\n      );\n      req.flush('Not Found', { status: 404, statusText: 'Not Found' });\n    });\n  });\n\n  describe('create', () => {\n    it('should create a new contractor contract with audit when user is authenticated', () => {\n      const newContractorContract: Omit<ContractorContract, 'id'> = {\n        subscriptionDate: '2024-02-20',\n        contractStartDate: '2024-02-20',\n        contractEndDate: '2024-12-31',\n        warranty: false,\n        typeWarrantyId: 1,\n        insuredRisksId: 1,\n        contractorId: 1,\n        contractId: 1,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n      contractorServiceSpy.getById.and.returnValue(\n        of(mockContractor as Contractor),\n      );\n      contractAuditStatusServiceSpy.getByName.and.returnValue(\n        of(mockAuditStatus),\n      );\n      contractAuditHistoryServiceSpy.create.and.returnValue(\n        of(mockAuditHistory),\n      );\n\n      service.create(newContractorContract).subscribe((contractorContract) => {\n        expect(contractorContract).toEqual(mockContractorContract);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractorServiceSpy.getById).toHaveBeenCalledWith(1);\n        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith(\n          'Cesión de contrato',\n        );\n        expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newContractorContract);\n      req.flush(mockContractorContract);\n    });\n\n    it('should create a new contractor contract without audit when user is not authenticated', () => {\n      const newContractorContract: Omit<ContractorContract, 'id'> = {\n        subscriptionDate: '2024-02-20',\n        contractStartDate: '2024-02-20',\n        contractEndDate: '2024-12-31',\n        warranty: false,\n        typeWarrantyId: 1,\n        insuredRisksId: 1,\n        contractorId: 1,\n        contractId: 1,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n\n      service.create(newContractorContract).subscribe((contractorContract) => {\n        expect(contractorContract).toEqual(mockContractorContract);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractorServiceSpy.getById).not.toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n        expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newContractorContract);\n      req.flush(mockContractorContract);\n    });\n\n    it('should handle error when creating a new contractor contract', () => {\n      const newContractorContract: Omit<ContractorContract, 'id'> = {\n        subscriptionDate: '2024-02-20',\n        contractStartDate: '2024-02-20',\n        contractEndDate: '2024-12-31',\n        warranty: false,\n        typeWarrantyId: 1,\n        insuredRisksId: 1,\n        contractorId: 1,\n        contractId: 1,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n\n      service.create(newContractorContract).subscribe({\n        next: () => fail('should have failed'),\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.flush('Bad Request', { status: 400, statusText: 'Bad Request' });\n    });\n  });\n\n  describe('earlyTerminateContract', () => {\n    it('should early terminate a contract with audit when user is authenticated', () => {\n      const terminationData: ContractEarlyTermination = {\n        contractorId: 1,\n        contractId: 1,\n        endDate: '2024-03-01',\n        terminationReason: 'Test reason',\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n      contractAuditStatusServiceSpy.getByName.and.returnValue(\n        of(mockTerminationStatus),\n      );\n      contractAuditHistoryServiceSpy.create.and.returnValue(\n        of(mockAuditHistory),\n      );\n\n      service\n        .earlyTerminateContract(terminationData)\n        .subscribe((contractorContract) => {\n          expect(contractorContract).toEqual(mockContractorContract);\n          expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n          expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith(\n            'Terminación anticipada',\n          );\n          expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();\n        });\n\n      const req = httpMock.expectOne(`${apiUrl}/early-termination`);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(terminationData);\n      req.flush(mockContractorContract);\n    });\n\n    it('should early terminate a contract without audit when user is not authenticated', () => {\n      const terminationData: ContractEarlyTermination = {\n        contractorId: 1,\n        contractId: 1,\n        endDate: '2024-03-01',\n        terminationReason: 'Test reason',\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n\n      service\n        .earlyTerminateContract(terminationData)\n        .subscribe((contractorContract) => {\n          expect(contractorContract).toEqual(mockContractorContract);\n          expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n          expect(\n            contractAuditStatusServiceSpy.getByName,\n          ).not.toHaveBeenCalled();\n          expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();\n        });\n\n      const req = httpMock.expectOne(`${apiUrl}/early-termination`);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(terminationData);\n      req.flush(mockContractorContract);\n    });\n\n    it('should handle error when early terminating a contract', () => {\n      const terminationData: ContractEarlyTermination = {\n        contractorId: 1,\n        contractId: 1,\n        endDate: '2024-03-01',\n        terminationReason: 'Test reason',\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n\n      service.earlyTerminateContract(terminationData).subscribe({\n        next: () => fail('should have failed'),\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/early-termination`);\n      req.flush('Bad Request', { status: 400, statusText: 'Bad Request' });\n    });\n  });\n\n  describe('update', () => {\n    it('should update a contractor contract', () => {\n      const id = 1;\n      const updateData: Partial<ContractorContract> = {\n        contractEndDate: '2024-06-30',\n      };\n\n      service.update(id, updateData).subscribe((contractorContract) => {\n        expect(contractorContract).toEqual(mockContractorContract);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockContractorContract);\n    });\n\n    it('should handle error when updating a contractor contract', () => {\n      const id = 999;\n      const updateData: Partial<ContractorContract> = {\n        contractEndDate: '2024-06-30',\n      };\n\n      service.update(id, updateData).subscribe({\n        next: () => fail('should have failed'),\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.flush('Not Found', { status: 404, statusText: 'Not Found' });\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete a contractor contract', () => {\n      const id = 1;\n\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n\n    it('should handle error when deleting a contractor contract', () => {\n      const id = 999;\n\n      service.delete(id).subscribe({\n        next: () => fail('should have failed'),\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.flush('Not Found', { status: 404, statusText: 'Not Found' });\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,EAAE,QAAQ,MAAM;AAGzB,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,yBAAyB,QAAQ,+BAA+B;AACzE,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,0BAA0B,QAAQ,iCAAiC;AAC5E,SAASC,iBAAiB,QAAQ,oDAAoD;AAMtFC,QAAQ,CAAC,2BAA2B,EAAE,MAAK;EACzC,IAAIC,OAAkC;EACtC,IAAIC,QAA+B;EACnC,IAAIC,cAA2C;EAC/C,IAAIC,8BAA2E;EAC/E,IAAIC,6BAAyE;EAC7E,IAAIC,oBAAuD;EAE3D,MAAMC,MAAM,GAAG,GAAGb,WAAW,CAACa,MAAM,uBAAuB;EAE3D,MAAMC,sBAAsB,GAAuB;IACjDC,EAAE,EAAE,CAAC;IACLC,gBAAgB,EAAE,YAAY;IAC9BC,iBAAiB,EAAE,YAAY;IAC/BC,eAAe,EAAE,YAAY;IAC7BC,QAAQ,EAAE,KAAK;IACfC,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE,CAAC;IACjBC,YAAY,EAAE,CAAC;IACfC,UAAU,EAAE;GACb;EAED,MAAMC,QAAQ,GAAS;IAAET,EAAE,EAAE,CAAC;IAAEU,QAAQ,EAAE,UAAU;IAAEC,QAAQ,EAAE;EAAE,CAAE;EAEpE,MAAMC,cAAc,GAAwB;IAC1CZ,EAAE,EAAE,CAAC;IACLa,QAAQ,EAAE,iBAAiB;IAC3BC,QAAQ,EAAE,QAAQ;IAClBC,aAAa,EAAE;GAEhB;EAED,MAAMC,eAAe,GAAwB;IAC3ChB,EAAE,EAAE,CAAC;IACLiB,IAAI,EAAE,oBAAoB;IAC1BC,WAAW,EAAE;GACd;EAED,MAAMC,qBAAqB,GAAwB;IACjDnB,EAAE,EAAE,CAAC;IACLiB,IAAI,EAAE,wBAAwB;IAC9BC,WAAW,EAAE;GACd;EAED,MAAME,gBAAgB,GAAyB;IAC7CpB,EAAE,EAAE,CAAC;IACLQ,UAAU,EAAE,CAAC;IACba,aAAa,EAAE,CAAC;IAChBC,SAAS,EAAE,IAAIC,IAAI,EAAE;IACrBC,OAAO,EAAE,cAAc;IACvBC,SAAS,EAAE;GACZ;EAEDC,UAAU,CAAC,MAAK;IACdhC,cAAc,GAAGiC,OAAO,CAACC,YAAY,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,CAAC;IACxEjC,8BAA8B,GAAGgC,OAAO,CAACC,YAAY,CACnD,6BAA6B,EAC7B,CAAC,QAAQ,CAAC,CACX;IACDhC,6BAA6B,GAAG+B,OAAO,CAACC,YAAY,CAClD,4BAA4B,EAC5B,CAAC,WAAW,CAAC,CACd;IACD/B,oBAAoB,GAAG8B,OAAO,CAACC,YAAY,CAAC,mBAAmB,EAAE,CAC/D,SAAS,CACV,CAAC;IAEF7C,OAAO,CAAC8C,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACjD,uBAAuB,CAAC;MAClCkD,SAAS,EAAE,CACT7C,yBAAyB,EACzB;QAAE8C,OAAO,EAAE7C,WAAW;QAAE8C,QAAQ,EAAEvC;MAAc,CAAE,EAClD;QACEsC,OAAO,EAAE5C,2BAA2B;QACpC6C,QAAQ,EAAEtC;OACX,EACD;QACEqC,OAAO,EAAE3C,0BAA0B;QACnC4C,QAAQ,EAAErC;OACX,EACD;QAAEoC,OAAO,EAAE1C,iBAAiB;QAAE2C,QAAQ,EAAEpC;MAAoB,CAAE;KAEjE,CAAC;IACFL,OAAO,GAAGT,OAAO,CAACmD,MAAM,CAAChD,yBAAyB,CAAC;IACnDO,QAAQ,GAAGV,OAAO,CAACmD,MAAM,CAACpD,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFqD,SAAS,CAAC,MAAK;IACb1C,QAAQ,CAAC2C,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAAC9C,OAAO,CAAC,CAAC+C,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFhD,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB8C,EAAE,CAAC,wCAAwC,EAAE,MAAK;MAChD,MAAMG,uBAAuB,GAAG,CAACzC,sBAAsB,CAAC;MAExDP,OAAO,CAACiD,MAAM,EAAE,CAACC,SAAS,CAAEC,mBAAmB,IAAI;QACjDL,MAAM,CAACK,mBAAmB,CAAC,CAACC,OAAO,CAACJ,uBAAuB,CAAC;MAC9D,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGpD,QAAQ,CAACqD,SAAS,CAAChD,MAAM,CAAC;MACtCwC,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,uBAAuB,CAAC;IACpC,CAAC,CAAC;IAEFH,EAAE,CAAC,2DAA2D,EAAE,MAAK;MACnE7C,OAAO,CAACiD,MAAM,EAAE,CAACC,SAAS,CAAC;QACzBS,IAAI,EAAEA,CAAA,KAAMC,IAAI,CAAC,oBAAoB,CAAC;QACtCC,KAAK,EAAGA,KAAK,IAAI;UACff,MAAM,CAACe,KAAK,CAACC,MAAM,CAAC,CAACL,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpD,QAAQ,CAACqD,SAAS,CAAChD,MAAM,CAAC;MACtC+C,GAAG,CAACK,KAAK,CAAC,uBAAuB,EAAE;QACjCI,MAAM,EAAE,GAAG;QACXC,UAAU,EAAE;OACb,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFhE,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvB8C,EAAE,CAAC,2CAA2C,EAAE,MAAK;MACnD,MAAMrC,EAAE,GAAG,CAAC;MAEZR,OAAO,CAACgE,OAAO,CAACxD,EAAE,CAAC,CAAC0C,SAAS,CAAEe,kBAAkB,IAAI;QACnDnB,MAAM,CAACmB,kBAAkB,CAAC,CAACb,OAAO,CAAC7C,sBAAsB,CAAC;MAC5D,CAAC,CAAC;MAEF,MAAM8C,GAAG,GAAGpD,QAAQ,CAACqD,SAAS,CAAC,GAAGhD,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDsC,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACnD,sBAAsB,CAAC;IACnC,CAAC,CAAC;IAEFsC,EAAE,CAAC,4DAA4D,EAAE,MAAK;MACpE,MAAMrC,EAAE,GAAG,GAAG;MAEdR,OAAO,CAACgE,OAAO,CAACxD,EAAE,CAAC,CAAC0C,SAAS,CAAC;QAC5BS,IAAI,EAAEA,CAAA,KAAMC,IAAI,CAAC,oBAAoB,CAAC;QACtCC,KAAK,EAAGA,KAAK,IAAI;UACff,MAAM,CAACe,KAAK,CAACC,MAAM,CAAC,CAACL,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpD,QAAQ,CAACqD,SAAS,CAAC,GAAGhD,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjD6C,GAAG,CAACK,KAAK,CAAC,WAAW,EAAE;QAAEI,MAAM,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAW,CAAE,CAAC;IAClE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFhE,QAAQ,CAAC,sBAAsB,EAAE,MAAK;IACpC8C,EAAE,CAAC,yDAAyD,EAAE,MAAK;MACjE,MAAM9B,YAAY,GAAG,CAAC;MACtB,MAAMiC,uBAAuB,GAAG,CAACzC,sBAAsB,CAAC;MAExDP,OAAO,CACJkE,oBAAoB,CAACnD,YAAY,CAAC,CAClCmC,SAAS,CAAEC,mBAAmB,IAAI;QACjCL,MAAM,CAACK,mBAAmB,CAAC,CAACC,OAAO,CAACJ,uBAAuB,CAAC;MAC9D,CAAC,CAAC;MAEJ,MAAMK,GAAG,GAAGpD,QAAQ,CAACqD,SAAS,CAAC,GAAGhD,MAAM,eAAeS,YAAY,EAAE,CAAC;MACtE+B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,uBAAuB,CAAC;IACpC,CAAC,CAAC;IAEFH,EAAE,CAAC,wEAAwE,EAAE,MAAK;MAChF,MAAM9B,YAAY,GAAG,GAAG;MAExBf,OAAO,CAACkE,oBAAoB,CAACnD,YAAY,CAAC,CAACmC,SAAS,CAAC;QACnDS,IAAI,EAAEA,CAAA,KAAMC,IAAI,CAAC,oBAAoB,CAAC;QACtCC,KAAK,EAAGA,KAAK,IAAI;UACff,MAAM,CAACe,KAAK,CAACC,MAAM,CAAC,CAACL,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpD,QAAQ,CAACqD,SAAS,CAAC,GAAGhD,MAAM,eAAeS,YAAY,EAAE,CAAC;MACtEsC,GAAG,CAACK,KAAK,CAAC,WAAW,EAAE;QAAEI,MAAM,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAW,CAAE,CAAC;IAClE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFhE,QAAQ,CAAC,oBAAoB,EAAE,MAAK;IAClC8C,EAAE,CAAC,uDAAuD,EAAE,MAAK;MAC/D,MAAM7B,UAAU,GAAG,CAAC;MACpB,MAAMgC,uBAAuB,GAAG,CAACzC,sBAAsB,CAAC;MAExDP,OAAO,CACJmE,kBAAkB,CAACnD,UAAU,CAAC,CAC9BkC,SAAS,CAAEC,mBAAmB,IAAI;QACjCL,MAAM,CAACK,mBAAmB,CAAC,CAACC,OAAO,CAACJ,uBAAuB,CAAC;MAC9D,CAAC,CAAC;MAEJ,MAAMK,GAAG,GAAGpD,QAAQ,CAACqD,SAAS,CAAC,GAAGhD,MAAM,aAAaU,UAAU,EAAE,CAAC;MAClE8B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,uBAAuB,CAAC;IACpC,CAAC,CAAC;IAEFH,EAAE,CAAC,sEAAsE,EAAE,MAAK;MAC9E,MAAM7B,UAAU,GAAG,GAAG;MAEtBhB,OAAO,CAACmE,kBAAkB,CAACnD,UAAU,CAAC,CAACkC,SAAS,CAAC;QAC/CS,IAAI,EAAEA,CAAA,KAAMC,IAAI,CAAC,oBAAoB,CAAC;QACtCC,KAAK,EAAGA,KAAK,IAAI;UACff,MAAM,CAACe,KAAK,CAACC,MAAM,CAAC,CAACL,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpD,QAAQ,CAACqD,SAAS,CAAC,GAAGhD,MAAM,aAAaU,UAAU,EAAE,CAAC;MAClEqC,GAAG,CAACK,KAAK,CAAC,WAAW,EAAE;QAAEI,MAAM,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAW,CAAE,CAAC;IAClE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFhE,QAAQ,CAAC,8BAA8B,EAAE,MAAK;IAC5C8C,EAAE,CAAC,mEAAmE,EAAE,MAAK;MAC3E,MAAM9B,YAAY,GAAG,CAAC;MACtB,MAAMC,UAAU,GAAG,CAAC;MAEpBhB,OAAO,CACJoE,4BAA4B,CAACrD,YAAY,EAAEC,UAAU,CAAC,CACtDkC,SAAS,CAAEe,kBAAkB,IAAI;QAChCnB,MAAM,CAACmB,kBAAkB,CAAC,CAACb,OAAO,CAAC7C,sBAAsB,CAAC;MAC5D,CAAC,CAAC;MAEJ,MAAM8C,GAAG,GAAGpD,QAAQ,CAACqD,SAAS,CAC5B,GAAGhD,MAAM,eAAeS,YAAY,aAAaC,UAAU,EAAE,CAC9D;MACD8B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACnD,sBAAsB,CAAC;IACnC,CAAC,CAAC;IAEFsC,EAAE,CAAC,oFAAoF,EAAE,MAAK;MAC5F,MAAM9B,YAAY,GAAG,GAAG;MACxB,MAAMC,UAAU,GAAG,GAAG;MAEtBhB,OAAO,CAACoE,4BAA4B,CAACrD,YAAY,EAAEC,UAAU,CAAC,CAACkC,SAAS,CAAC;QACvES,IAAI,EAAEA,CAAA,KAAMC,IAAI,CAAC,oBAAoB,CAAC;QACtCC,KAAK,EAAGA,KAAK,IAAI;UACff,MAAM,CAACe,KAAK,CAACC,MAAM,CAAC,CAACL,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpD,QAAQ,CAACqD,SAAS,CAC5B,GAAGhD,MAAM,eAAeS,YAAY,aAAaC,UAAU,EAAE,CAC9D;MACDqC,GAAG,CAACK,KAAK,CAAC,WAAW,EAAE;QAAEI,MAAM,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAW,CAAE,CAAC;IAClE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFhE,QAAQ,CAAC,0BAA0B,EAAE,MAAK;IACxC8C,EAAE,CAAC,0DAA0D,EAAE,MAAK;MAClE,MAAM7B,UAAU,GAAG,CAAC;MACpB,MAAMqD,QAAQ,GAAG,YAAY;MAE7BrE,OAAO,CAACsE,wBAAwB,CAACtD,UAAU,CAAC,CAACkC,SAAS,CAAEqB,IAAI,IAAI;QAC9DzB,MAAM,CAACyB,IAAI,CAAC,CAACnB,OAAO,CAACiB,QAAQ,CAAC;MAChC,CAAC,CAAC;MAEF,MAAMhB,GAAG,GAAGpD,QAAQ,CAACqD,SAAS,CAC5B,GAAGhD,MAAM,aAAaU,UAAU,0BAA0B,CAC3D;MACD8B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACW,QAAQ,CAAC;IACrB,CAAC,CAAC;IAEFxB,EAAE,CAAC,0DAA0D,EAAE,MAAK;MAClE,MAAM7B,UAAU,GAAG,GAAG;MAEtBhB,OAAO,CAACsE,wBAAwB,CAACtD,UAAU,CAAC,CAACkC,SAAS,CAAC;QACrDS,IAAI,EAAEA,CAAA,KAAMC,IAAI,CAAC,oBAAoB,CAAC;QACtCC,KAAK,EAAGA,KAAK,IAAI;UACff,MAAM,CAACe,KAAK,CAACC,MAAM,CAAC,CAACL,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpD,QAAQ,CAACqD,SAAS,CAC5B,GAAGhD,MAAM,aAAaU,UAAU,0BAA0B,CAC3D;MACDqC,GAAG,CAACK,KAAK,CAAC,WAAW,EAAE;QAAEI,MAAM,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAW,CAAE,CAAC;IAClE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFhE,QAAQ,CAAC,mBAAmB,EAAE,MAAK;IACjC8C,EAAE,CAAC,4DAA4D,EAAE,MAAK;MACpE,MAAM9B,YAAY,GAAG,CAAC;MAEtBf,OAAO,CAACwE,iBAAiB,CAACzD,YAAY,CAAC,CAACmC,SAAS,CAAEuB,SAAS,IAAI;QAC9D3B,MAAM,CAAC2B,SAAS,CAAC,CAACC,QAAQ,EAAE;MAC9B,CAAC,CAAC;MAEF,MAAMrB,GAAG,GAAGpD,QAAQ,CAACqD,SAAS,CAC5B,GAAGhD,MAAM,eAAeS,YAAY,sBAAsB,CAC3D;MACD+B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;IAEFb,EAAE,CAAC,6DAA6D,EAAE,MAAK;MACrE,MAAM9B,YAAY,GAAG,CAAC;MAEtBf,OAAO,CAACwE,iBAAiB,CAACzD,YAAY,CAAC,CAACmC,SAAS,CAAEuB,SAAS,IAAI;QAC9D3B,MAAM,CAAC2B,SAAS,CAAC,CAACE,SAAS,EAAE;MAC/B,CAAC,CAAC;MAEF,MAAMtB,GAAG,GAAGpD,QAAQ,CAACqD,SAAS,CAC5B,GAAGhD,MAAM,eAAeS,YAAY,sBAAsB,CAC3D;MACD+B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAAC,KAAK,CAAC;IAClB,CAAC,CAAC;IAEFb,EAAE,CAAC,qEAAqE,EAAE,MAAK;MAC7E,MAAM9B,YAAY,GAAG,GAAG;MAExBf,OAAO,CAACwE,iBAAiB,CAACzD,YAAY,CAAC,CAACmC,SAAS,CAAC;QAChDS,IAAI,EAAEA,CAAA,KAAMC,IAAI,CAAC,oBAAoB,CAAC;QACtCC,KAAK,EAAGA,KAAK,IAAI;UACff,MAAM,CAACe,KAAK,CAACC,MAAM,CAAC,CAACL,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpD,QAAQ,CAACqD,SAAS,CAC5B,GAAGhD,MAAM,eAAeS,YAAY,sBAAsB,CAC3D;MACDsC,GAAG,CAACK,KAAK,CAAC,WAAW,EAAE;QAAEI,MAAM,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAW,CAAE,CAAC;IAClE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFhE,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB8C,EAAE,CAAC,+EAA+E,EAAE,MAAK;MACvF,MAAM+B,qBAAqB,GAAmC;QAC5DnE,gBAAgB,EAAE,YAAY;QAC9BC,iBAAiB,EAAE,YAAY;QAC/BC,eAAe,EAAE,YAAY;QAC7BC,QAAQ,EAAE,KAAK;QACfC,cAAc,EAAE,CAAC;QACjBC,cAAc,EAAE,CAAC;QACjBC,YAAY,EAAE,CAAC;QACfC,UAAU,EAAE;OACb;MAEDd,cAAc,CAAC2E,cAAc,CAACC,GAAG,CAACC,WAAW,CAAC9D,QAAQ,CAAC;MACvDZ,oBAAoB,CAAC2D,OAAO,CAACc,GAAG,CAACC,WAAW,CAC1CvF,EAAE,CAAC4B,cAA4B,CAAC,CACjC;MACDhB,6BAA6B,CAAC4E,SAAS,CAACF,GAAG,CAACC,WAAW,CACrDvF,EAAE,CAACgC,eAAe,CAAC,CACpB;MACDrB,8BAA8B,CAAC8E,MAAM,CAACH,GAAG,CAACC,WAAW,CACnDvF,EAAE,CAACoC,gBAAgB,CAAC,CACrB;MAED5B,OAAO,CAACiF,MAAM,CAACL,qBAAqB,CAAC,CAAC1B,SAAS,CAAEe,kBAAkB,IAAI;QACrEnB,MAAM,CAACmB,kBAAkB,CAAC,CAACb,OAAO,CAAC7C,sBAAsB,CAAC;QAC1DuC,MAAM,CAAC5C,cAAc,CAAC2E,cAAc,CAAC,CAACK,gBAAgB,EAAE;QACxDpC,MAAM,CAACzC,oBAAoB,CAAC2D,OAAO,CAAC,CAACmB,oBAAoB,CAAC,CAAC,CAAC;QAC5DrC,MAAM,CAAC1C,6BAA6B,CAAC4E,SAAS,CAAC,CAACG,oBAAoB,CAClE,oBAAoB,CACrB;QACDrC,MAAM,CAAC3C,8BAA8B,CAAC8E,MAAM,CAAC,CAACC,gBAAgB,EAAE;MAClE,CAAC,CAAC;MAEF,MAAM7B,GAAG,GAAGpD,QAAQ,CAACqD,SAAS,CAAChD,MAAM,CAAC;MACtCwC,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAAC6B,IAAI,CAAC,CAAChC,OAAO,CAACwB,qBAAqB,CAAC;MACvDvB,GAAG,CAACK,KAAK,CAACnD,sBAAsB,CAAC;IACnC,CAAC,CAAC;IAEFsC,EAAE,CAAC,sFAAsF,EAAE,MAAK;MAC9F,MAAM+B,qBAAqB,GAAmC;QAC5DnE,gBAAgB,EAAE,YAAY;QAC9BC,iBAAiB,EAAE,YAAY;QAC/BC,eAAe,EAAE,YAAY;QAC7BC,QAAQ,EAAE,KAAK;QACfC,cAAc,EAAE,CAAC;QACjBC,cAAc,EAAE,CAAC;QACjBC,YAAY,EAAE,CAAC;QACfC,UAAU,EAAE;OACb;MAEDd,cAAc,CAAC2E,cAAc,CAACC,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;MAEnD/E,OAAO,CAACiF,MAAM,CAACL,qBAAqB,CAAC,CAAC1B,SAAS,CAAEe,kBAAkB,IAAI;QACrEnB,MAAM,CAACmB,kBAAkB,CAAC,CAACb,OAAO,CAAC7C,sBAAsB,CAAC;QAC1DuC,MAAM,CAAC5C,cAAc,CAAC2E,cAAc,CAAC,CAACK,gBAAgB,EAAE;QACxDpC,MAAM,CAACzC,oBAAoB,CAAC2D,OAAO,CAAC,CAACqB,GAAG,CAACH,gBAAgB,EAAE;QAC3DpC,MAAM,CAAC1C,6BAA6B,CAAC4E,SAAS,CAAC,CAACK,GAAG,CAACH,gBAAgB,EAAE;QACtEpC,MAAM,CAAC3C,8BAA8B,CAAC8E,MAAM,CAAC,CAACI,GAAG,CAACH,gBAAgB,EAAE;MACtE,CAAC,CAAC;MAEF,MAAM7B,GAAG,GAAGpD,QAAQ,CAACqD,SAAS,CAAChD,MAAM,CAAC;MACtCwC,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAAC6B,IAAI,CAAC,CAAChC,OAAO,CAACwB,qBAAqB,CAAC;MACvDvB,GAAG,CAACK,KAAK,CAACnD,sBAAsB,CAAC;IACnC,CAAC,CAAC;IAEFsC,EAAE,CAAC,6DAA6D,EAAE,MAAK;MACrE,MAAM+B,qBAAqB,GAAmC;QAC5DnE,gBAAgB,EAAE,YAAY;QAC9BC,iBAAiB,EAAE,YAAY;QAC/BC,eAAe,EAAE,YAAY;QAC7BC,QAAQ,EAAE,KAAK;QACfC,cAAc,EAAE,CAAC;QACjBC,cAAc,EAAE,CAAC;QACjBC,YAAY,EAAE,CAAC;QACfC,UAAU,EAAE;OACb;MAEDd,cAAc,CAAC2E,cAAc,CAACC,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;MAEnD/E,OAAO,CAACiF,MAAM,CAACL,qBAAqB,CAAC,CAAC1B,SAAS,CAAC;QAC9CS,IAAI,EAAEA,CAAA,KAAMC,IAAI,CAAC,oBAAoB,CAAC;QACtCC,KAAK,EAAGA,KAAK,IAAI;UACff,MAAM,CAACe,KAAK,CAACC,MAAM,CAAC,CAACL,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpD,QAAQ,CAACqD,SAAS,CAAChD,MAAM,CAAC;MACtC+C,GAAG,CAACK,KAAK,CAAC,aAAa,EAAE;QAAEI,MAAM,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAa,CAAE,CAAC;IACtE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFhE,QAAQ,CAAC,wBAAwB,EAAE,MAAK;IACtC8C,EAAE,CAAC,yEAAyE,EAAE,MAAK;MACjF,MAAMyC,eAAe,GAA6B;QAChDvE,YAAY,EAAE,CAAC;QACfC,UAAU,EAAE,CAAC;QACbuE,OAAO,EAAE,YAAY;QACrBC,iBAAiB,EAAE;OACpB;MAEDtF,cAAc,CAAC2E,cAAc,CAACC,GAAG,CAACC,WAAW,CAAC9D,QAAQ,CAAC;MACvDb,6BAA6B,CAAC4E,SAAS,CAACF,GAAG,CAACC,WAAW,CACrDvF,EAAE,CAACmC,qBAAqB,CAAC,CAC1B;MACDxB,8BAA8B,CAAC8E,MAAM,CAACH,GAAG,CAACC,WAAW,CACnDvF,EAAE,CAACoC,gBAAgB,CAAC,CACrB;MAED5B,OAAO,CACJyF,sBAAsB,CAACH,eAAe,CAAC,CACvCpC,SAAS,CAAEe,kBAAkB,IAAI;QAChCnB,MAAM,CAACmB,kBAAkB,CAAC,CAACb,OAAO,CAAC7C,sBAAsB,CAAC;QAC1DuC,MAAM,CAAC5C,cAAc,CAAC2E,cAAc,CAAC,CAACK,gBAAgB,EAAE;QACxDpC,MAAM,CAAC1C,6BAA6B,CAAC4E,SAAS,CAAC,CAACG,oBAAoB,CAClE,wBAAwB,CACzB;QACDrC,MAAM,CAAC3C,8BAA8B,CAAC8E,MAAM,CAAC,CAACC,gBAAgB,EAAE;MAClE,CAAC,CAAC;MAEJ,MAAM7B,GAAG,GAAGpD,QAAQ,CAACqD,SAAS,CAAC,GAAGhD,MAAM,oBAAoB,CAAC;MAC7DwC,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAAC6B,IAAI,CAAC,CAAChC,OAAO,CAACkC,eAAe,CAAC;MACjDjC,GAAG,CAACK,KAAK,CAACnD,sBAAsB,CAAC;IACnC,CAAC,CAAC;IAEFsC,EAAE,CAAC,gFAAgF,EAAE,MAAK;MACxF,MAAMyC,eAAe,GAA6B;QAChDvE,YAAY,EAAE,CAAC;QACfC,UAAU,EAAE,CAAC;QACbuE,OAAO,EAAE,YAAY;QACrBC,iBAAiB,EAAE;OACpB;MAEDtF,cAAc,CAAC2E,cAAc,CAACC,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;MAEnD/E,OAAO,CACJyF,sBAAsB,CAACH,eAAe,CAAC,CACvCpC,SAAS,CAAEe,kBAAkB,IAAI;QAChCnB,MAAM,CAACmB,kBAAkB,CAAC,CAACb,OAAO,CAAC7C,sBAAsB,CAAC;QAC1DuC,MAAM,CAAC5C,cAAc,CAAC2E,cAAc,CAAC,CAACK,gBAAgB,EAAE;QACxDpC,MAAM,CACJ1C,6BAA6B,CAAC4E,SAAS,CACxC,CAACK,GAAG,CAACH,gBAAgB,EAAE;QACxBpC,MAAM,CAAC3C,8BAA8B,CAAC8E,MAAM,CAAC,CAACI,GAAG,CAACH,gBAAgB,EAAE;MACtE,CAAC,CAAC;MAEJ,MAAM7B,GAAG,GAAGpD,QAAQ,CAACqD,SAAS,CAAC,GAAGhD,MAAM,oBAAoB,CAAC;MAC7DwC,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAAC6B,IAAI,CAAC,CAAChC,OAAO,CAACkC,eAAe,CAAC;MACjDjC,GAAG,CAACK,KAAK,CAACnD,sBAAsB,CAAC;IACnC,CAAC,CAAC;IAEFsC,EAAE,CAAC,uDAAuD,EAAE,MAAK;MAC/D,MAAMyC,eAAe,GAA6B;QAChDvE,YAAY,EAAE,CAAC;QACfC,UAAU,EAAE,CAAC;QACbuE,OAAO,EAAE,YAAY;QACrBC,iBAAiB,EAAE;OACpB;MAEDtF,cAAc,CAAC2E,cAAc,CAACC,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;MAEnD/E,OAAO,CAACyF,sBAAsB,CAACH,eAAe,CAAC,CAACpC,SAAS,CAAC;QACxDS,IAAI,EAAEA,CAAA,KAAMC,IAAI,CAAC,oBAAoB,CAAC;QACtCC,KAAK,EAAGA,KAAK,IAAI;UACff,MAAM,CAACe,KAAK,CAACC,MAAM,CAAC,CAACL,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpD,QAAQ,CAACqD,SAAS,CAAC,GAAGhD,MAAM,oBAAoB,CAAC;MAC7D+C,GAAG,CAACK,KAAK,CAAC,aAAa,EAAE;QAAEI,MAAM,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAa,CAAE,CAAC;IACtE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFhE,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB8C,EAAE,CAAC,qCAAqC,EAAE,MAAK;MAC7C,MAAMrC,EAAE,GAAG,CAAC;MACZ,MAAMkF,UAAU,GAAgC;QAC9C/E,eAAe,EAAE;OAClB;MAEDX,OAAO,CAAC2F,MAAM,CAACnF,EAAE,EAAEkF,UAAU,CAAC,CAACxC,SAAS,CAAEe,kBAAkB,IAAI;QAC9DnB,MAAM,CAACmB,kBAAkB,CAAC,CAACb,OAAO,CAAC7C,sBAAsB,CAAC;MAC5D,CAAC,CAAC;MAEF,MAAM8C,GAAG,GAAGpD,QAAQ,CAACqD,SAAS,CAAC,GAAGhD,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDsC,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAAC6B,IAAI,CAAC,CAAChC,OAAO,CAACsC,UAAU,CAAC;MAC5CrC,GAAG,CAACK,KAAK,CAACnD,sBAAsB,CAAC;IACnC,CAAC,CAAC;IAEFsC,EAAE,CAAC,yDAAyD,EAAE,MAAK;MACjE,MAAMrC,EAAE,GAAG,GAAG;MACd,MAAMkF,UAAU,GAAgC;QAC9C/E,eAAe,EAAE;OAClB;MAEDX,OAAO,CAAC2F,MAAM,CAACnF,EAAE,EAAEkF,UAAU,CAAC,CAACxC,SAAS,CAAC;QACvCS,IAAI,EAAEA,CAAA,KAAMC,IAAI,CAAC,oBAAoB,CAAC;QACtCC,KAAK,EAAGA,KAAK,IAAI;UACff,MAAM,CAACe,KAAK,CAACC,MAAM,CAAC,CAACL,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpD,QAAQ,CAACqD,SAAS,CAAC,GAAGhD,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjD6C,GAAG,CAACK,KAAK,CAAC,WAAW,EAAE;QAAEI,MAAM,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAW,CAAE,CAAC;IAClE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFhE,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB8C,EAAE,CAAC,qCAAqC,EAAE,MAAK;MAC7C,MAAMrC,EAAE,GAAG,CAAC;MAEZR,OAAO,CAAC4F,MAAM,CAACpF,EAAE,CAAC,CAAC0C,SAAS,CAAC,MAAK;QAChCJ,MAAM,EAAE,CAAC+C,OAAO,EAAE;MACpB,CAAC,CAAC;MAEF,MAAMxC,GAAG,GAAGpD,QAAQ,CAACqD,SAAS,CAAC,GAAGhD,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDsC,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;IAEFb,EAAE,CAAC,yDAAyD,EAAE,MAAK;MACjE,MAAMrC,EAAE,GAAG,GAAG;MAEdR,OAAO,CAAC4F,MAAM,CAACpF,EAAE,CAAC,CAAC0C,SAAS,CAAC;QAC3BS,IAAI,EAAEA,CAAA,KAAMC,IAAI,CAAC,oBAAoB,CAAC;QACtCC,KAAK,EAAGA,KAAK,IAAI;UACff,MAAM,CAACe,KAAK,CAACC,MAAM,CAAC,CAACL,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpD,QAAQ,CAACqD,SAAS,CAAC,GAAGhD,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjD6C,GAAG,CAACK,KAAK,CAAC,WAAW,EAAE;QAAEI,MAAM,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAW,CAAE,CAAC;IAClE,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}