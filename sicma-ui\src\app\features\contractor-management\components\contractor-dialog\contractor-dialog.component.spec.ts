import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { Contractor } from '@contractor-management/models/contractor.model';
import { ContractorService } from '@contractor-management/services/contractor.service';
import { EducationLevelService } from '@contractor-management/services/education-level.service';
import { EpsService } from '@contractor-management/services/eps.service';
import { GenderService } from '@contractor-management/services/gender.service';
import { LegalNatureService } from '@contractor-management/services/legal-nature.service';
import { ProfessionService } from '@contractor-management/services/profession.service';
import { Municipality } from '@shared/models/municipality.model';
import { AlertService } from '@shared/services/alert.service';
import { DepartmentService } from '@shared/services/department.service';
import { IDTypeService } from '@shared/services/id-type.service';
import { MunicipalityService } from '@shared/services/municipality.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { of, throwError } from 'rxjs';
import { ContractorDialogComponent } from './contractor-dialog.component';

describe('ContractorDialogComponent', () => {
  let component: ContractorDialogComponent;
  let fixture: ComponentFixture<ContractorDialogComponent>;
  let dialogRef: jasmine.SpyObj<MatDialogRef<ContractorDialogComponent>>;
  let idTypeService: jasmine.SpyObj<IDTypeService>;
  let epsService: jasmine.SpyObj<EpsService>;
  let genderService: jasmine.SpyObj<GenderService>;
  let educationLevelService: jasmine.SpyObj<EducationLevelService>;
  let professionService: jasmine.SpyObj<ProfessionService>;
  let departmentService: jasmine.SpyObj<DepartmentService>;
  let municipalityService: jasmine.SpyObj<MunicipalityService>;
  let legalNatureService: jasmine.SpyObj<LegalNatureService>;
  let contractorService: jasmine.SpyObj<ContractorService>;
  let alertService: jasmine.SpyObj<AlertService>;
  let spinnerService: jasmine.SpyObj<NgxSpinnerService>;

  const mockIdTypes = [
    { id: 1, name: 'CC' },
    { id: 2, name: 'CE' },
  ];

  const mockEpss = [
    { id: 1, name: 'EPS 1' },
    { id: 2, name: 'EPS 2' },
  ];

  const mockGenders = [
    { id: 1, name: 'Male' },
    { id: 2, name: 'Female' },
  ];

  const mockEducationLevels = [
    { id: 1, name: 'BACHILLER' },
    { id: 2, name: 'PROFESIONAL' },
    { id: 3, name: 'TÉCNICO' },
    { id: 4, name: 'TECNÓLOGO' },
    { id: 5, name: 'OTRO' },
  ];

  const mockProfessions = [
    { id: 1, name: 'BACHILLER' },
    { id: 2, name: 'Engineer' },
  ];

  const mockDepartments = [
    { id: 1, name: 'Department 1' },
    { id: 2, name: 'Department 2' },
  ];

  const mockMunicipalities: Municipality[] = [
    {
      id: 1,
      name: 'Municipality 1',
      departmentId: 1,
      department: mockDepartments[0],
    },
    {
      id: 2,
      name: 'Municipality 2',
      departmentId: 1,
      department: mockDepartments[0],
    },
  ];

  const mockLegalNatures = [
    { id: 1, name: 'Nature 1' },
    { id: 2, name: 'Nature 2' },
  ];

  const mockContractor: Contractor = {
    id: 1,
    fullName: 'Test Contractor',
    idType: mockIdTypes[0],
    idNumber: 123456789,
    personalEmail: '<EMAIL>',
    corporateEmail: '<EMAIL>',
    phone: 1234567890,
    eps: mockEpss[0],
    birthDate: '2000-01-01',
    gender: mockGenders[0],
    educationLevel: mockEducationLevels[0],
    profession: mockProfessions[0],
    lastObtainedDegree: '',
    department: mockDepartments[0],
    municipality: mockMunicipalities[0],
    legalNature: mockLegalNatures[0],
  };

  beforeEach(() => {
    dialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
    idTypeService = jasmine.createSpyObj('IDTypeService', ['getAll']);
    epsService = jasmine.createSpyObj('EpsService', ['getAll']);
    genderService = jasmine.createSpyObj('GenderService', ['getAll']);
    educationLevelService = jasmine.createSpyObj('EducationLevelService', [
      'getAll',
    ]);
    professionService = jasmine.createSpyObj('ProfessionService', ['getAll']);
    departmentService = jasmine.createSpyObj('DepartmentService', ['getAll']);
    municipalityService = jasmine.createSpyObj('MunicipalityService', [
      'getAllByDepartmentId',
    ]);
    legalNatureService = jasmine.createSpyObj('LegalNatureService', ['getAll']);
    contractorService = jasmine.createSpyObj('ContractorService', [
      'create',
      'update',
    ]);
    alertService = jasmine.createSpyObj('AlertService', ['success', 'error']);
    spinnerService = jasmine.createSpyObj('NgxSpinnerService', [
      'show',
      'hide',
    ]);

    idTypeService.getAll.and.returnValue(of(mockIdTypes));
    epsService.getAll.and.returnValue(of(mockEpss));
    genderService.getAll.and.returnValue(of(mockGenders));
    educationLevelService.getAll.and.returnValue(of(mockEducationLevels));
    professionService.getAll.and.returnValue(of(mockProfessions));
    departmentService.getAll.and.returnValue(of(mockDepartments));
    municipalityService.getAllByDepartmentId.and.returnValue(
      of(mockMunicipalities),
    );
    legalNatureService.getAll.and.returnValue(of(mockLegalNatures));

    TestBed.configureTestingModule({
      imports: [
        ContractorDialogComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        ReactiveFormsModule,
      ],
      providers: [
        { provide: MatDialogRef, useValue: dialogRef },
        { provide: MAT_DIALOG_DATA, useValue: {} },
        { provide: IDTypeService, useValue: idTypeService },
        { provide: EpsService, useValue: epsService },
        { provide: GenderService, useValue: genderService },
        { provide: EducationLevelService, useValue: educationLevelService },
        { provide: ProfessionService, useValue: professionService },
        { provide: DepartmentService, useValue: departmentService },
        { provide: MunicipalityService, useValue: municipalityService },
        { provide: LegalNatureService, useValue: legalNatureService },
        { provide: ContractorService, useValue: contractorService },
        { provide: AlertService, useValue: alertService },
        { provide: NgxSpinnerService, useValue: spinnerService },
        FormBuilder,
        provideNativeDateAdapter(),
      ],
    });

    fixture = TestBed.createComponent(ContractorDialogComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load all required data on init', () => {
    component.ngOnInit();
    expect(spinnerService.show).toHaveBeenCalled();
    expect(idTypeService.getAll).toHaveBeenCalled();
    expect(epsService.getAll).toHaveBeenCalled();
    expect(genderService.getAll).toHaveBeenCalled();
    expect(educationLevelService.getAll).toHaveBeenCalled();
    expect(professionService.getAll).toHaveBeenCalled();
    expect(departmentService.getAll).toHaveBeenCalled();
    expect(legalNatureService.getAll).toHaveBeenCalled();
    expect(spinnerService.hide).toHaveBeenCalled();
  });

  it('should handle error when loading initial data', () => {
    idTypeService.getAll.and.returnValue(
      throwError(() => ({ error: 'Error' })),
    );
    component.ngOnInit();
    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar los datos del formulario',
    );
    expect(dialogRef.close).toHaveBeenCalled();
  });

  it('should load municipalities when department is selected', () => {
    component.ngOnInit();
    component.contractorForm.get('departmentId')?.setValue(1);
    expect(municipalityService.getAllByDepartmentId).toHaveBeenCalledWith(1);
    expect(component.municipalities).toEqual(mockMunicipalities);
  });

  it('should clear municipalities when department is cleared', () => {
    component.ngOnInit();
    component.contractorForm.get('departmentId')?.setValue(1);
    component.contractorForm.get('departmentId')?.setValue(null);
    expect(component.municipalities).toEqual([]);
    expect(component.contractorForm.get('municipalityId')?.value).toBeNull();
  });

  it('should handle education level change for BACHILLER', () => {
    component.ngOnInit();
    const bachillerLevel = mockEducationLevels.find(
      (el) => el.name === 'BACHILLER',
    );
    component.contractorForm
      .get('educationLevelId')
      ?.setValue(bachillerLevel?.id);

    expect(component.isProfessionEditable).toBeFalse();
    expect(
      component.contractorForm.get('lastObtainedDegree')?.disabled,
    ).toBeTrue();
    expect(component.contractorForm.get('professionId')?.disabled).toBeTrue();

    const bachillerProfession = mockProfessions.find(
      (p) => p.name === 'BACHILLER',
    );
    expect(component.contractorForm.get('professionId')?.value).toBe(
      bachillerProfession?.id,
    );
  });

  it('should handle education level change for PROFESIONAL', () => {
    component.ngOnInit();
    const profesionalLevel = mockEducationLevels.find(
      (el) => el.name === 'PROFESIONAL',
    );
    component.contractorForm
      .get('educationLevelId')
      ?.setValue(profesionalLevel?.id);

    expect(component.isProfessionEditable).toBeTrue();
    expect(
      component.contractorForm.get('lastObtainedDegree')?.disabled,
    ).toBeTrue();
    expect(component.contractorForm.get('professionId')?.enabled).toBeTrue();
    expect(
      component.contractorForm
        .get('professionId')
        ?.hasValidator(Validators.required),
    ).toBeTrue();
  });

  it('should handle education level change for TÉCNICO', () => {
    component.ngOnInit();
    const tecnicoLevel = mockEducationLevels.find(
      (el) => el.name === 'TÉCNICO',
    );

    component.onEducationLevelChange(tecnicoLevel!);

    expect(component.isProfessionEditable).toBeFalse();
    expect(component.isLastObtainedDegreeEnabled).toBeFalse();
  });

  it('should handle education level change for TECNÓLOGO', () => {
    component.ngOnInit();
    const tecnologoLevel = mockEducationLevels.find(
      (el) => el.name === 'TECNÓLOGO',
    );

    component.onEducationLevelChange(tecnologoLevel!);

    expect(component.isProfessionEditable).toBeFalse();
    expect(component.isLastObtainedDegreeEnabled).toBeFalse();
  });

  it('should handle education level change for other education levels', () => {
    component.ngOnInit();
    const otherLevel = mockEducationLevels.find((el) => el.name === 'OTRO');
    component.contractorForm.get('educationLevelId')?.setValue(otherLevel?.id);

    expect(component.isProfessionEditable).toBeTrue();
    expect(component.isLastObtainedDegreeEnabled).toBeFalse();
    expect(
      component.contractorForm.get('lastObtainedDegree')?.disabled,
    ).toBeTrue();
    expect(component.contractorForm.get('professionId')?.enabled).toBeTrue();
    expect(
      component.contractorForm
        .get('professionId')
        ?.hasValidator(Validators.required),
    ).toBeFalse();
  });

  it('should handle education level change to null', () => {
    component.ngOnInit();

    component.contractorForm.get('educationLevelId')?.setValue(1);
    component.contractorForm.get('educationLevelId')?.setValue(null);

    expect(component.isProfessionEditable).toBeTrue();
    expect(component.isLastObtainedDegreeEnabled).toBeFalse();
    expect(
      component.contractorForm.get('lastObtainedDegree')?.disabled,
    ).toBeTrue();
    expect(component.contractorForm.get('professionId')?.enabled).toBeTrue();
    expect(component.contractorForm.get('professionId')?.value).toBeNull();
  });

  it('should patch form values when editing existing contractor', () => {
    TestBed.resetTestingModule();
    TestBed.configureTestingModule({
      imports: [
        ContractorDialogComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        ReactiveFormsModule,
      ],
      providers: [
        { provide: MatDialogRef, useValue: dialogRef },
        { provide: MAT_DIALOG_DATA, useValue: mockContractor },
        { provide: IDTypeService, useValue: idTypeService },
        { provide: EpsService, useValue: epsService },
        { provide: GenderService, useValue: genderService },
        { provide: EducationLevelService, useValue: educationLevelService },
        { provide: ProfessionService, useValue: professionService },
        { provide: DepartmentService, useValue: departmentService },
        { provide: MunicipalityService, useValue: municipalityService },
        { provide: LegalNatureService, useValue: legalNatureService },
        { provide: ContractorService, useValue: contractorService },
        { provide: AlertService, useValue: alertService },
        { provide: NgxSpinnerService, useValue: spinnerService },
        FormBuilder,
        provideNativeDateAdapter(),
      ],
    });

    fixture = TestBed.createComponent(ContractorDialogComponent);
    component = fixture.componentInstance;
    component.ngOnInit();

    expect(component.contractorForm.get('fullName')?.value).toBe(
      mockContractor.fullName,
    );
    expect(component.contractorForm.get('idTypeId')?.value).toBe(
      mockContractor.idType?.id,
    );
    expect(component.contractorForm.get('idNumber')?.value).toBe(
      mockContractor.idNumber,
    );
    expect(component.contractorForm.get('personalEmail')?.value).toBe(
      mockContractor.personalEmail,
    );
  });

  it('should load municipalities when editing a contractor with department', () => {
    TestBed.resetTestingModule();
    TestBed.configureTestingModule({
      imports: [
        ContractorDialogComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        ReactiveFormsModule,
      ],
      providers: [
        { provide: MatDialogRef, useValue: dialogRef },
        { provide: MAT_DIALOG_DATA, useValue: mockContractor },
        { provide: IDTypeService, useValue: idTypeService },
        { provide: EpsService, useValue: epsService },
        { provide: GenderService, useValue: genderService },
        { provide: EducationLevelService, useValue: educationLevelService },
        { provide: ProfessionService, useValue: professionService },
        { provide: DepartmentService, useValue: departmentService },
        { provide: MunicipalityService, useValue: municipalityService },
        { provide: LegalNatureService, useValue: legalNatureService },
        { provide: ContractorService, useValue: contractorService },
        { provide: AlertService, useValue: alertService },
        { provide: NgxSpinnerService, useValue: spinnerService },
        FormBuilder,
        provideNativeDateAdapter(),
      ],
    });

    fixture = TestBed.createComponent(ContractorDialogComponent);
    component = fixture.componentInstance;
    component.ngOnInit();

    expect(municipalityService.getAllByDepartmentId).toHaveBeenCalledWith(
      mockContractor.department!.id,
    );
  });

  it('should validate required fields', () => {
    component.ngOnInit();
    expect(component.contractorForm.valid).toBeFalse();

    component.contractorForm.patchValue({
      fullName: 'Test Name',
      idTypeId: 1,
      idNumber: '123456789',
      personalEmail: '<EMAIL>',
      legalNatureId: 1,
    });

    expect(component.contractorForm.valid).toBeTrue();
  });

  it('should validate email format', () => {
    component.ngOnInit();
    const personalEmailControl = component.contractorForm.get('personalEmail');
    const corporateEmailControl =
      component.contractorForm.get('corporateEmail');

    personalEmailControl?.setValue('invalid-email');
    expect(personalEmailControl?.errors?.['email']).toBeTruthy();

    personalEmailControl?.setValue('<EMAIL>');
    expect(personalEmailControl?.errors).toBeNull();

    corporateEmailControl?.setValue('invalid-email');
    expect(corporateEmailControl?.errors?.['email']).toBeTruthy();

    corporateEmailControl?.setValue('<EMAIL>');
    expect(corporateEmailControl?.errors).toBeNull();
  });

  it('should validate phone number format', () => {
    component.ngOnInit();
    const phoneControl = component.contractorForm.get('phone');

    phoneControl?.setValue('abc123');
    expect(phoneControl?.errors?.['pattern']).toBeTruthy();

    phoneControl?.setValue('123456789');
    expect(phoneControl?.errors).toBeNull();
  });

  it('should handle error when loading municipalities', () => {
    component.ngOnInit();
    municipalityService.getAllByDepartmentId.and.returnValue(
      throwError(() => ({ error: 'Error loading municipalities' })),
    );

    component.contractorForm.get('departmentId')?.setValue(1);

    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar municipios',
    );
  });

  describe('onSubmit', () => {
    it('should validate form before submission', () => {
      component.ngOnInit();

      component.contractorForm.patchValue({ fullName: '' });
      expect(component.contractorForm.valid).toBeFalse();

      component.contractorForm.patchValue({
        fullName: 'Test Name',
        idTypeId: 1,
        idNumber: '123456789',
        personalEmail: '<EMAIL>',
        legalNatureId: 1,
      });
      expect(component.contractorForm.valid).toBeTrue();
    });

    it('should format date for API submission', () => {
      component.ngOnInit();
      const testDate = new Date('2023-05-15');
      const formattedDate = testDate.toISOString().split('T')[0];
      expect(formattedDate).toBe('2023-05-15');
    });

    it('should handle empty phone as undefined', () => {
      component.ngOnInit();
      const emptyPhone = '';
      const processedPhone = emptyPhone || undefined;
      expect(processedPhone).toBeUndefined();
    });
  });

  describe('Error handling', () => {
    it('should show appropriate error messages', () => {
      component.ngOnInit();

      alertService.error.calls.reset();

      alertService.error('Número de cédula ya se encuentra registrado');
      expect(alertService.error).toHaveBeenCalledWith(
        'Número de cédula ya se encuentra registrado',
      );

      alertService.error.calls.reset();

      alertService.error('El correo institucional ya se encuentra registrado');
      expect(alertService.error).toHaveBeenCalledWith(
        'El correo institucional ya se encuentra registrado',
      );

      alertService.error.calls.reset();

      alertService.error('Error al procesar la solicitud');
      expect(alertService.error).toHaveBeenCalledWith(
        'Error al procesar la solicitud',
      );

      alertService.error('Error al editar contratista');
      expect(alertService.error).toHaveBeenCalledWith(
        'Error al editar contratista',
      );

      alertService.error('Error al crear contratista');
      expect(alertService.error).toHaveBeenCalledWith(
        'Error al crear contratista',
      );
    });
  });

  describe('Success handling', () => {
    it('should show success messages', () => {
      component.ngOnInit();

      alertService.success.calls.reset();

      alertService.success('Contratista creado exitosamente');
      expect(alertService.success).toHaveBeenCalledWith(
        'Contratista creado exitosamente',
      );

      alertService.success.calls.reset();

      alertService.success('Contratista editado exitosamente');
      expect(alertService.success).toHaveBeenCalledWith(
        'Contratista editado exitosamente',
      );
    });
  });

  it('should have a method to close dialog', () => {
    expect(dialogRef.close).toBeDefined();
  });

  describe('Date formatting', () => {
    it('should correctly format date for API submission', () => {
      component.ngOnInit();

      const testDate = new Date('2023-05-15');
      component.contractorForm.patchValue({
        fullName: 'Test Name',
        idTypeId: 1,
        idNumber: '123456789',
        personalEmail: '<EMAIL>',
        legalNatureId: 1,
        birthDate: testDate,
      });

      const formattedDate = testDate.toISOString().split('T')[0];

      expect(formattedDate).toBe('2023-05-15');
    });
  });

  describe('Error handling', () => {
    it('should show appropriate error messages', () => {
      alertService.error.calls.reset();

      alertService.error('Número de cédula ya se encuentra registrado');
      expect(alertService.error).toHaveBeenCalledWith(
        'Número de cédula ya se encuentra registrado',
      );

      alertService.error.calls.reset();

      alertService.error('El correo institucional ya se encuentra registrado');
      expect(alertService.error).toHaveBeenCalledWith(
        'El correo institucional ya se encuentra registrado',
      );

      alertService.error.calls.reset();

      alertService.error('Error al procesar la solicitud');
      expect(alertService.error).toHaveBeenCalledWith(
        'Error al procesar la solicitud',
      );
    });
  });
});
