import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { CDPEntity } from '@contract-management/models/cdp-entity.model';
import { environment } from '@env';
import { CdpEntityService } from './cdp-entity.service';

describe('CdpEntityService', () => {
  let service: CdpEntityService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/cdp-entities`;

  const mockCdpEntity: CDPEntity = {
    id: 1,
    name: 'Test CDP Entity',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [CdpEntityService],
    });
    service = TestBed.inject(CdpEntityService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all cdp entities', () => {
      const mockCdpEntities = [mockCdpEntity];

      service.getAll().subscribe((cdpEntities) => {
        expect(cdpEntities).toEqual(mockCdpEntities);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockCdpEntities);
    });
  });

  describe('getById', () => {
    it('should return a cdp entity by id', () => {
      const id = 1;

      service.getById(id).subscribe((cdpEntity) => {
        expect(cdpEntity).toEqual(mockCdpEntity);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockCdpEntity);
    });
  });

  describe('create', () => {
    it('should create a new cdp entity', () => {
      const newCdpEntity: Omit<CDPEntity, 'id'> = {
        name: 'New CDP Entity',
      };

      service.create(newCdpEntity).subscribe((cdpEntity) => {
        expect(cdpEntity).toEqual(mockCdpEntity);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newCdpEntity);
      req.flush(mockCdpEntity);
    });
  });

  describe('update', () => {
    it('should update a cdp entity', () => {
      const id = 1;
      const updateData: Partial<CDPEntity> = {
        name: 'Updated CDP Entity',
      };

      service.update(id, updateData).subscribe((cdpEntity) => {
        expect(cdpEntity).toEqual(mockCdpEntity);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockCdpEntity);
    });
  });

  describe('delete', () => {
    it('should delete a cdp entity', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });

  describe('getByName', () => {
    it('should return a cdp entity by name', () => {
      const name = 'Test CDP Entity';

      service.getByName(name).subscribe((cdpEntity) => {
        expect(cdpEntity).toEqual(mockCdpEntity);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockCdpEntity);
    });
  });
});