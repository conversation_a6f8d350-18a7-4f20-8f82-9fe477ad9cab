import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { InitialReportDocumentation } from '@contractor-dashboard/models/initial-report-documentation.model';
import { InitialReportDocumentationService } from '@contractor-dashboard/services/initial-report-documentation.service';
import { AlertService } from '@shared/services/alert.service';
import { of, throwError } from 'rxjs';
import { MonthlyReportInitialDocumentationComponent } from './monthly-report-initial-documentation.component';

describe('MonthlyReportInitialDocumentationComponent', () => {
  let component: MonthlyReportInitialDocumentationComponent;
  let fixture: ComponentFixture<MonthlyReportInitialDocumentationComponent>;
  let documentationService: jasmine.SpyObj<InitialReportDocumentationService>;
  let alertService: jasmine.SpyObj<AlertService>;

  const mockDocumentation: InitialReportDocumentation = {
    id: 1,
    contractorContractId: 1,
    bankId: 1,
    accountNumber: '123456',
    bankAccountTypeId: 1,
    taxRegimeId: 1,
    epsId: 1,
    arlId: 1,
    pensionFundId: 1,
    hasDependents: false,
    hasHousingInterest: false,
    housingInterestAnnualPayment: 0,
    hasPrepaidMedicine: false,
    prepaidMedicineAnnualPayment: 0,
    hasAfcAccount: false,
    hasVoluntarySavings: false,
    afcAccountAnnualPayment: 0,
    voluntarySavingsAnnualPayment: 0,
  };

  beforeEach(() => {
    const documentationServiceSpy = jasmine.createSpyObj(
      'InitialReportDocumentationService',
      ['getByContractorContractId', 'createWithFiles', 'updateWithFiles'],
    );
    const alertServiceSpy = jasmine.createSpyObj('AlertService', [
      'success',
      'error',
      'warning',
    ]);

    TestBed.configureTestingModule({
      imports: [
        MonthlyReportInitialDocumentationComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
      ],
      providers: [
        {
          provide: InitialReportDocumentationService,
          useValue: documentationServiceSpy,
        },
        { provide: AlertService, useValue: alertServiceSpy },
      ],
    });

    fixture = TestBed.createComponent(
      MonthlyReportInitialDocumentationComponent,
    );
    component = fixture.componentInstance;
    documentationService = TestBed.inject(
      InitialReportDocumentationService,
    ) as jasmine.SpyObj<InitialReportDocumentationService>;
    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;

    documentationService.getByContractorContractId.and.returnValue(
      of(mockDocumentation),
    );
    documentationService.createWithFiles.and.returnValue(of(mockDocumentation));
    documentationService.updateWithFiles.and.returnValue(of(mockDocumentation));

    component.contractorContractId = 1;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Form Validation', () => {
    it('should initialize forms after view init', () => {
      component.ngAfterViewInit();
      expect(component.validateForms()).toBeFalse();
    });

    it('should check form validity', () => {
      const validityEmitSpy = spyOn(component.formValidityChange, 'emit');
      component.checkFormValidity();
      expect(validityEmitSpy).toHaveBeenCalled();
    });
  });

  describe('Data Loading', () => {
    it('should load initial documentation', () => {
      component.loadInitialDocumentation();
      expect(
        documentationService.getByContractorContractId,
      ).toHaveBeenCalledWith(1);
      expect(component.initialDocumentation).toEqual(mockDocumentation);
    });

    it('should handle documentation loading error', () => {
      documentationService.getByContractorContractId.and.returnValue(
        throwError(() => new Error()),
      );
      component.loadInitialDocumentation();
      expect(alertService.error).toHaveBeenCalledWith(
        'Error al cargar la documentación inicial',
      );
    });

    it('should ignore 404 errors when loading documentation', () => {
      documentationService.getByContractorContractId.and.returnValue(
        throwError(() => ({ status: 404 })),
      );
      component.loadInitialDocumentation();
      expect(alertService.error).not.toHaveBeenCalled();
    });
  });

  describe('Data Saving', () => {
    let validateFormsSpy: jasmine.Spy;

    beforeEach(() => {
      validateFormsSpy = spyOn(component, 'validateForms').and.returnValue(
        true,
      );
    });

    it('should validate forms before saving', async () => {
      validateFormsSpy.and.returnValue(false);
      const result = await component.saveInitialDocumentation();
      expect(result).toBeFalse();
      expect(alertService.warning).toHaveBeenCalledWith(
        'Por favor, complete todos los campos requeridos.',
      );
    });

    it('should create new documentation', async () => {
      component.initialDocumentation = undefined;
      documentationService.createWithFiles.and.returnValue(
        of(mockDocumentation),
      );

      const result = await component.saveInitialDocumentation();

      expect(result).toBeTrue();
      expect(documentationService.createWithFiles).toHaveBeenCalled();
      expect(alertService.success).toHaveBeenCalledWith(
        'Documentación inicial guardada con éxito',
      );
    });

    it('should update existing documentation', async () => {
      component.initialDocumentation = mockDocumentation;
      const result = await component.saveInitialDocumentation();
      expect(result).toBeTrue();
      expect(documentationService.updateWithFiles).toHaveBeenCalled();
      expect(alertService.success).toHaveBeenCalledWith(
        'Documentación inicial actualizada con éxito',
      );
    });

    it('should handle save error', async () => {
      component.initialDocumentation = undefined;
      documentationService.createWithFiles.and.returnValue(
        throwError(() => new Error('Error')),
      );

      const result = await component.saveInitialDocumentation();

      expect(result).toBeFalse();
      expect(alertService.error).toHaveBeenCalledWith(
        'Error al guardar la documentación inicial',
      );
    });
  });
});