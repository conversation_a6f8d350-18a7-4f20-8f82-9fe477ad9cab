import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { environment } from '@env';
import { ContractAuditStatus } from '../models/contract-audit-status.model';
import { ContractAuditStatusService } from './contract-audit-status.service';

describe('ContractAuditStatusService', () => {
  let service: ContractAuditStatusService;
  let httpTestingController: HttpTestingController;
  const API_URL = `${environment.apiUrl}/contract-audit-statuses`;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ContractAuditStatusService],
    });
    service = TestBed.inject(ContractAuditStatusService);
    httpTestingController = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all contract audit statuses', () => {
      const mockStatuses: ContractAuditStatus[] = [
        { id: 1, name: 'Pending', description: 'Audit pending' },
        { id: 2, name: 'Approved', description: 'Audit approved' },
        { id: 3, name: 'Rejected', description: 'Audit rejected' },
      ];

      service.getAll().subscribe((statuses) => {
        expect(statuses).toEqual(mockStatuses);
      });

      const req = httpTestingController.expectOne(API_URL);
      expect(req.request.method).toBe('GET');
      req.flush(mockStatuses);
    });
  });

  describe('getById', () => {
    it('should return a single contract audit status by id', () => {
      const mockStatus: ContractAuditStatus = {
        id: 1,
        name: 'Pending',
        description: 'Audit pending',
      };

      service.getById(1).subscribe((status) => {
        expect(status).toEqual(mockStatus);
      });

      const req = httpTestingController.expectOne(`${API_URL}/1`);
      expect(req.request.method).toBe('GET');
      req.flush(mockStatus);
    });
  });

  describe('getByName', () => {
    it('should return a contract audit status by name', () => {
      const statusName = 'Pending';
      const mockStatus: ContractAuditStatus = {
        id: 1,
        name: statusName,
        description: 'Audit pending',
      };

      service.getByName(statusName).subscribe((status) => {
        expect(status).toEqual(mockStatus);
      });

      const req = httpTestingController.expectOne(
        `${API_URL}/name/${statusName}`,
      );
      expect(req.request.method).toBe('GET');
      req.flush(mockStatus);
    });
  });

  describe('create', () => {
    it('should create a new contract audit status', () => {
      const newStatus: Omit<ContractAuditStatus, 'id'> = {
        name: 'In Progress',
        description: 'Audit in progress',
      };

      const mockResponse: ContractAuditStatus = {
        id: 4,
        ...newStatus,
      };

      service.create(newStatus).subscribe((status) => {
        expect(status).toEqual(mockResponse);
      });

      const req = httpTestingController.expectOne(API_URL);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newStatus);
      req.flush(mockResponse);
    });
  });

  describe('update', () => {
    it('should update an existing contract audit status', () => {
      const id = 1;
      const updateStatus: Partial<ContractAuditStatus> = {
        description: 'Updated description',
      };

      const mockResponse: ContractAuditStatus = {
        id: id,
        name: 'Pending',
        description: 'Updated description',
      };

      service.update(id, updateStatus).subscribe((status) => {
        expect(status).toEqual(mockResponse);
      });

      const req = httpTestingController.expectOne(`${API_URL}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateStatus);
      req.flush(mockResponse);
    });
  });

  describe('delete', () => {
    it('should delete a contract audit status', () => {
      const id = 1;

      service.delete(id).subscribe((response) => {
        expect(response).toBeNull();
      });

      const req = httpTestingController.expectOne(`${API_URL}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });
});