{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { SupervisorContractService } from './supervisor-contract.service';\ndescribe('SupervisorContractService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/supervisors_contract`;\n  const mockSupervisorContract = {\n    id: 1,\n    supervisorId: 1,\n    contractId: 1\n  };\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [SupervisorContractService]\n    });\n    service = TestBed.inject(SupervisorContractService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  it('should get all supervisor contracts', () => {\n    const mockSupervisorContracts = [mockSupervisorContract];\n    service.getAll().subscribe(supervisorContracts => {\n      expect(supervisorContracts).toEqual(mockSupervisorContracts);\n    });\n    const req = httpMock.expectOne(apiUrl);\n    expect(req.request.method).toBe('GET');\n    req.flush(mockSupervisorContracts);\n  });\n  it('should get supervisor contract by id', () => {\n    const id = 1;\n    service.getById(id).subscribe(supervisorContract => {\n      expect(supervisorContract).toEqual(mockSupervisorContract);\n    });\n    const req = httpMock.expectOne(`${apiUrl}/${id}`);\n    expect(req.request.method).toBe('GET');\n    req.flush(mockSupervisorContract);\n  });\n  it('should get supervisor contract by id number', () => {\n    const idNumber = '123456789';\n    service.getByIdNumber(idNumber).subscribe(supervisorContract => {\n      expect(supervisorContract).toEqual(mockSupervisorContract);\n    });\n    const req = httpMock.expectOne(`${apiUrl}/id-number/${idNumber}`);\n    expect(req.request.method).toBe('GET');\n    req.flush(mockSupervisorContract);\n  });\n  it('should get supervisor contracts by contract id', () => {\n    const contractId = 1;\n    const mockSupervisorContracts = [mockSupervisorContract];\n    service.getByContractId(contractId).subscribe(supervisorContracts => {\n      expect(supervisorContracts).toEqual(mockSupervisorContracts);\n    });\n    const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}`);\n    expect(req.request.method).toBe('GET');\n    req.flush(mockSupervisorContracts);\n  });\n  it('should create supervisor contract', () => {\n    const newSupervisorContract = {\n      supervisorId: 1,\n      contractId: 1\n    };\n    service.create(newSupervisorContract).subscribe(supervisorContract => {\n      expect(supervisorContract).toEqual(mockSupervisorContract);\n    });\n    const req = httpMock.expectOne(apiUrl);\n    expect(req.request.method).toBe('POST');\n    expect(req.request.body).toEqual(newSupervisorContract);\n    req.flush(mockSupervisorContract);\n  });\n  it('should update supervisor contract', () => {\n    const id = 1;\n    const updateData = {\n      supervisorId: 2\n    };\n    service.update(id, updateData).subscribe(supervisorContract => {\n      expect(supervisorContract).toEqual(mockSupervisorContract);\n    });\n    const req = httpMock.expectOne(`${apiUrl}/${id}`);\n    expect(req.request.method).toBe('PUT');\n    expect(req.request.body).toEqual(updateData);\n    req.flush(mockSupervisorContract);\n  });\n  it('should delete supervisor contract', () => {\n    const id = 1;\n    service.delete(id).subscribe(() => {\n      expect().nothing();\n    });\n    const req = httpMock.expectOne(`${apiUrl}/${id}`);\n    expect(req.request.method).toBe('DELETE');\n    req.flush(null);\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "SupervisorContractService", "describe", "service", "httpMock", "apiUrl", "mockSupervisorContract", "id", "supervisorId", "contractId", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockSupervisorContracts", "getAll", "subscribe", "supervisorContracts", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "getById", "supervisorContract", "idNumber", "getByIdNumber", "getByContractId", "newSupervisorContract", "create", "body", "updateData", "update", "delete", "nothing"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\services\\supervisor-contract.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { SupervisorContract } from '@contract-management/models/supervisor-contract.model';\nimport { environment } from '@env';\nimport { SupervisorContractService } from './supervisor-contract.service';\n\ndescribe('SupervisorContractService', () => {\n  let service: SupervisorContractService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/supervisors_contract`;\n\n  const mockSupervisorContract: SupervisorContract = {\n    id: 1,\n    supervisorId: 1,\n    contractId: 1,\n  };\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [SupervisorContractService],\n    });\n    service = TestBed.inject(SupervisorContractService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  it('should get all supervisor contracts', () => {\n    const mockSupervisorContracts = [mockSupervisorContract];\n\n    service.getAll().subscribe((supervisorContracts) => {\n      expect(supervisorContracts).toEqual(mockSupervisorContracts);\n    });\n\n    const req = httpMock.expectOne(apiUrl);\n    expect(req.request.method).toBe('GET');\n    req.flush(mockSupervisorContracts);\n  });\n\n  it('should get supervisor contract by id', () => {\n    const id = 1;\n\n    service.getById(id).subscribe((supervisorContract) => {\n      expect(supervisorContract).toEqual(mockSupervisorContract);\n    });\n\n    const req = httpMock.expectOne(`${apiUrl}/${id}`);\n    expect(req.request.method).toBe('GET');\n    req.flush(mockSupervisorContract);\n  });\n\n  it('should get supervisor contract by id number', () => {\n    const idNumber = '123456789';\n\n    service.getByIdNumber(idNumber).subscribe((supervisorContract) => {\n      expect(supervisorContract).toEqual(mockSupervisorContract);\n    });\n\n    const req = httpMock.expectOne(`${apiUrl}/id-number/${idNumber}`);\n    expect(req.request.method).toBe('GET');\n    req.flush(mockSupervisorContract);\n  });\n\n  it('should get supervisor contracts by contract id', () => {\n    const contractId = 1;\n    const mockSupervisorContracts = [mockSupervisorContract];\n\n    service.getByContractId(contractId).subscribe((supervisorContracts) => {\n      expect(supervisorContracts).toEqual(mockSupervisorContracts);\n    });\n\n    const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}`);\n    expect(req.request.method).toBe('GET');\n    req.flush(mockSupervisorContracts);\n  });\n\n  it('should create supervisor contract', () => {\n    const newSupervisorContract: Omit<SupervisorContract, 'id'> = {\n      supervisorId: 1,\n      contractId: 1,\n    };\n\n    service.create(newSupervisorContract).subscribe((supervisorContract) => {\n      expect(supervisorContract).toEqual(mockSupervisorContract);\n    });\n\n    const req = httpMock.expectOne(apiUrl);\n    expect(req.request.method).toBe('POST');\n    expect(req.request.body).toEqual(newSupervisorContract);\n    req.flush(mockSupervisorContract);\n  });\n\n  it('should update supervisor contract', () => {\n    const id = 1;\n    const updateData: Partial<SupervisorContract> = {\n      supervisorId: 2,\n    };\n\n    service.update(id, updateData).subscribe((supervisorContract) => {\n      expect(supervisorContract).toEqual(mockSupervisorContract);\n    });\n\n    const req = httpMock.expectOne(`${apiUrl}/${id}`);\n    expect(req.request.method).toBe('PUT');\n    expect(req.request.body).toEqual(updateData);\n    req.flush(mockSupervisorContract);\n  });\n\n  it('should delete supervisor contract', () => {\n    const id = 1;\n\n    service.delete(id).subscribe(() => {\n      expect().nothing();\n    });\n\n    const req = httpMock.expectOne(`${apiUrl}/${id}`);\n    expect(req.request.method).toBe('DELETE');\n    req.flush(null);\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,yBAAyB,QAAQ,+BAA+B;AAEzEC,QAAQ,CAAC,2BAA2B,EAAE,MAAK;EACzC,IAAIC,OAAkC;EACtC,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,uBAAuB;EAE3D,MAAMC,sBAAsB,GAAuB;IACjDC,EAAE,EAAE,CAAC;IACLC,YAAY,EAAE,CAAC;IACfC,UAAU,EAAE;GACb;EAEDC,UAAU,CAAC,MAAK;IACdX,OAAO,CAACY,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACf,uBAAuB,CAAC;MAClCgB,SAAS,EAAE,CAACZ,yBAAyB;KACtC,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACe,MAAM,CAACb,yBAAyB,CAAC;IACnDG,QAAQ,GAAGL,OAAO,CAACe,MAAM,CAAChB,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFiB,SAAS,CAAC,MAAK;IACbX,QAAQ,CAACY,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACf,OAAO,CAAC,CAACgB,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFF,EAAE,CAAC,qCAAqC,EAAE,MAAK;IAC7C,MAAMG,uBAAuB,GAAG,CAACd,sBAAsB,CAAC;IAExDH,OAAO,CAACkB,MAAM,EAAE,CAACC,SAAS,CAAEC,mBAAmB,IAAI;MACjDL,MAAM,CAACK,mBAAmB,CAAC,CAACC,OAAO,CAACJ,uBAAuB,CAAC;IAC9D,CAAC,CAAC;IAEF,MAAMK,GAAG,GAAGrB,QAAQ,CAACsB,SAAS,CAACrB,MAAM,CAAC;IACtCa,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;IACtCJ,GAAG,CAACK,KAAK,CAACV,uBAAuB,CAAC;EACpC,CAAC,CAAC;EAEFH,EAAE,CAAC,sCAAsC,EAAE,MAAK;IAC9C,MAAMV,EAAE,GAAG,CAAC;IAEZJ,OAAO,CAAC4B,OAAO,CAACxB,EAAE,CAAC,CAACe,SAAS,CAAEU,kBAAkB,IAAI;MACnDd,MAAM,CAACc,kBAAkB,CAAC,CAACR,OAAO,CAAClB,sBAAsB,CAAC;IAC5D,CAAC,CAAC;IAEF,MAAMmB,GAAG,GAAGrB,QAAQ,CAACsB,SAAS,CAAC,GAAGrB,MAAM,IAAIE,EAAE,EAAE,CAAC;IACjDW,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;IACtCJ,GAAG,CAACK,KAAK,CAACxB,sBAAsB,CAAC;EACnC,CAAC,CAAC;EAEFW,EAAE,CAAC,6CAA6C,EAAE,MAAK;IACrD,MAAMgB,QAAQ,GAAG,WAAW;IAE5B9B,OAAO,CAAC+B,aAAa,CAACD,QAAQ,CAAC,CAACX,SAAS,CAAEU,kBAAkB,IAAI;MAC/Dd,MAAM,CAACc,kBAAkB,CAAC,CAACR,OAAO,CAAClB,sBAAsB,CAAC;IAC5D,CAAC,CAAC;IAEF,MAAMmB,GAAG,GAAGrB,QAAQ,CAACsB,SAAS,CAAC,GAAGrB,MAAM,cAAc4B,QAAQ,EAAE,CAAC;IACjEf,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;IACtCJ,GAAG,CAACK,KAAK,CAACxB,sBAAsB,CAAC;EACnC,CAAC,CAAC;EAEFW,EAAE,CAAC,gDAAgD,EAAE,MAAK;IACxD,MAAMR,UAAU,GAAG,CAAC;IACpB,MAAMW,uBAAuB,GAAG,CAACd,sBAAsB,CAAC;IAExDH,OAAO,CAACgC,eAAe,CAAC1B,UAAU,CAAC,CAACa,SAAS,CAAEC,mBAAmB,IAAI;MACpEL,MAAM,CAACK,mBAAmB,CAAC,CAACC,OAAO,CAACJ,uBAAuB,CAAC;IAC9D,CAAC,CAAC;IAEF,MAAMK,GAAG,GAAGrB,QAAQ,CAACsB,SAAS,CAAC,GAAGrB,MAAM,aAAaI,UAAU,EAAE,CAAC;IAClES,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;IACtCJ,GAAG,CAACK,KAAK,CAACV,uBAAuB,CAAC;EACpC,CAAC,CAAC;EAEFH,EAAE,CAAC,mCAAmC,EAAE,MAAK;IAC3C,MAAMmB,qBAAqB,GAAmC;MAC5D5B,YAAY,EAAE,CAAC;MACfC,UAAU,EAAE;KACb;IAEDN,OAAO,CAACkC,MAAM,CAACD,qBAAqB,CAAC,CAACd,SAAS,CAAEU,kBAAkB,IAAI;MACrEd,MAAM,CAACc,kBAAkB,CAAC,CAACR,OAAO,CAAClB,sBAAsB,CAAC;IAC5D,CAAC,CAAC;IAEF,MAAMmB,GAAG,GAAGrB,QAAQ,CAACsB,SAAS,CAACrB,MAAM,CAAC;IACtCa,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;IACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACW,IAAI,CAAC,CAACd,OAAO,CAACY,qBAAqB,CAAC;IACvDX,GAAG,CAACK,KAAK,CAACxB,sBAAsB,CAAC;EACnC,CAAC,CAAC;EAEFW,EAAE,CAAC,mCAAmC,EAAE,MAAK;IAC3C,MAAMV,EAAE,GAAG,CAAC;IACZ,MAAMgC,UAAU,GAAgC;MAC9C/B,YAAY,EAAE;KACf;IAEDL,OAAO,CAACqC,MAAM,CAACjC,EAAE,EAAEgC,UAAU,CAAC,CAACjB,SAAS,CAAEU,kBAAkB,IAAI;MAC9Dd,MAAM,CAACc,kBAAkB,CAAC,CAACR,OAAO,CAAClB,sBAAsB,CAAC;IAC5D,CAAC,CAAC;IAEF,MAAMmB,GAAG,GAAGrB,QAAQ,CAACsB,SAAS,CAAC,GAAGrB,MAAM,IAAIE,EAAE,EAAE,CAAC;IACjDW,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;IACtCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACW,IAAI,CAAC,CAACd,OAAO,CAACe,UAAU,CAAC;IAC5Cd,GAAG,CAACK,KAAK,CAACxB,sBAAsB,CAAC;EACnC,CAAC,CAAC;EAEFW,EAAE,CAAC,mCAAmC,EAAE,MAAK;IAC3C,MAAMV,EAAE,GAAG,CAAC;IAEZJ,OAAO,CAACsC,MAAM,CAAClC,EAAE,CAAC,CAACe,SAAS,CAAC,MAAK;MAChCJ,MAAM,EAAE,CAACwB,OAAO,EAAE;IACpB,CAAC,CAAC;IAEF,MAAMjB,GAAG,GAAGrB,QAAQ,CAACsB,SAAS,CAAC,GAAGrB,MAAM,IAAIE,EAAE,EAAE,CAAC;IACjDW,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;IACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;EACjB,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}