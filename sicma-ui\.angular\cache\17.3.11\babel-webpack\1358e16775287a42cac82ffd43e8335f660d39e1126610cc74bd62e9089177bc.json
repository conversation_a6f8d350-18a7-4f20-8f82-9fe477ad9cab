{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { ProfileService } from './profile.service';\ndescribe('ProfileService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/profiles`;\n  const userProfileUrl = `${environment.apiUrl}/user-profiles`;\n  const mockProfile = {\n    id: 1,\n    name: 'Test Profile'\n  };\n  const mockUserProfileAssignment = {\n    userId: 1,\n    username: 'testuser',\n    profiles: [mockProfile]\n  };\n  const mockAssignProfileRequest = {\n    user_id: 1,\n    profile_id: 1\n  };\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [ProfileService]\n    });\n    service = TestBed.inject(ProfileService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAllProfiles', () => {\n    it('should return all profiles', () => {\n      const mockProfiles = [mockProfile];\n      service.getAllProfiles().subscribe(profiles => {\n        expect(profiles).toEqual(mockProfiles);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockProfiles);\n    });\n    it('should handle error when getting all profiles', () => {\n      service.getAllProfiles().subscribe({\n        error: error => {\n          expect(error.status).toBe(500);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Server error'), {\n        status: 500\n      });\n    });\n  });\n  describe('getUsersWithProfiles', () => {\n    it('should return all users with their profiles', () => {\n      const mockUsersWithProfiles = [mockUserProfileAssignment];\n      service.getUsersWithProfiles().subscribe(users => {\n        expect(users).toEqual(mockUsersWithProfiles);\n      });\n      const req = httpMock.expectOne(`${userProfileUrl}/users`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockUsersWithProfiles);\n    });\n    it('should handle error when getting users with profiles', () => {\n      service.getUsersWithProfiles().subscribe({\n        error: error => {\n          expect(error.status).toBe(500);\n        }\n      });\n      const req = httpMock.expectOne(`${userProfileUrl}/users`);\n      req.error(new ErrorEvent('Server error'), {\n        status: 500\n      });\n    });\n  });\n  describe('assignProfile', () => {\n    it('should assign a profile to a user', () => {\n      service.assignProfile(mockAssignProfileRequest).subscribe(() => {\n        expect().nothing();\n      });\n      const req = httpMock.expectOne(`${userProfileUrl}/assign`);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(mockAssignProfileRequest);\n      req.flush(null);\n    });\n    it('should handle error when assigning profile', () => {\n      service.assignProfile(mockAssignProfileRequest).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(`${userProfileUrl}/assign`);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n  describe('removeProfile', () => {\n    it('should remove a profile from a user', () => {\n      service.removeProfile(mockAssignProfileRequest).subscribe(() => {\n        expect().nothing();\n      });\n      const req = httpMock.expectOne(`${userProfileUrl}/remove`);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(mockAssignProfileRequest);\n      req.flush(null);\n    });\n    it('should handle error when removing profile', () => {\n      service.removeProfile(mockAssignProfileRequest).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(`${userProfileUrl}/remove`);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "ProfileService", "describe", "service", "httpMock", "apiUrl", "userProfileUrl", "mockProfile", "id", "name", "mockUserProfileAssignment", "userId", "username", "profiles", "mockAssignProfileRequest", "user_id", "profile_id", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockProfiles", "getAllProfiles", "subscribe", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "error", "status", "ErrorEvent", "mockUsersWithProfiles", "getUsersWithProfiles", "users", "assignProfile", "nothing", "body", "removeProfile"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\profile-management\\services\\profile.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport {\n  AssignProfileRequest,\n  Profile,\n  UserProfileAssignment,\n} from '../models/profile.model';\nimport { ProfileService } from './profile.service';\n\ndescribe('ProfileService', () => {\n  let service: ProfileService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/profiles`;\n  const userProfileUrl = `${environment.apiUrl}/user-profiles`;\n\n  const mockProfile: Profile = {\n    id: 1,\n    name: 'Test Profile',\n  };\n\n  const mockUserProfileAssignment: UserProfileAssignment = {\n    userId: 1,\n    username: 'testuser',\n    profiles: [mockProfile],\n  };\n\n  const mockAssignProfileRequest: AssignProfileRequest = {\n    user_id: 1,\n    profile_id: 1,\n  };\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [ProfileService],\n    });\n    service = TestBed.inject(ProfileService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAllProfiles', () => {\n    it('should return all profiles', () => {\n      const mockProfiles = [mockProfile];\n\n      service.getAllProfiles().subscribe((profiles) => {\n        expect(profiles).toEqual(mockProfiles);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockProfiles);\n    });\n\n    it('should handle error when getting all profiles', () => {\n      service.getAllProfiles().subscribe({\n        error: (error) => {\n          expect(error.status).toBe(500);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Server error'), { status: 500 });\n    });\n  });\n\n  describe('getUsersWithProfiles', () => {\n    it('should return all users with their profiles', () => {\n      const mockUsersWithProfiles = [mockUserProfileAssignment];\n\n      service.getUsersWithProfiles().subscribe((users) => {\n        expect(users).toEqual(mockUsersWithProfiles);\n      });\n\n      const req = httpMock.expectOne(`${userProfileUrl}/users`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockUsersWithProfiles);\n    });\n\n    it('should handle error when getting users with profiles', () => {\n      service.getUsersWithProfiles().subscribe({\n        error: (error) => {\n          expect(error.status).toBe(500);\n        },\n      });\n\n      const req = httpMock.expectOne(`${userProfileUrl}/users`);\n      req.error(new ErrorEvent('Server error'), { status: 500 });\n    });\n  });\n\n  describe('assignProfile', () => {\n    it('should assign a profile to a user', () => {\n      service.assignProfile(mockAssignProfileRequest).subscribe(() => {\n        expect().nothing();\n      });\n\n      const req = httpMock.expectOne(`${userProfileUrl}/assign`);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(mockAssignProfileRequest);\n      req.flush(null);\n    });\n\n    it('should handle error when assigning profile', () => {\n      service.assignProfile(mockAssignProfileRequest).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(`${userProfileUrl}/assign`);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n\n  describe('removeProfile', () => {\n    it('should remove a profile from a user', () => {\n      service.removeProfile(mockAssignProfileRequest).subscribe(() => {\n        expect().nothing();\n      });\n\n      const req = httpMock.expectOne(`${userProfileUrl}/remove`);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(mockAssignProfileRequest);\n      req.flush(null);\n    });\n\n    it('should handle error when removing profile', () => {\n      service.removeProfile(mockAssignProfileRequest).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(`${userProfileUrl}/remove`);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,WAAW,QAAQ,MAAM;AAMlC,SAASC,cAAc,QAAQ,mBAAmB;AAElDC,QAAQ,CAAC,gBAAgB,EAAE,MAAK;EAC9B,IAAIC,OAAuB;EAC3B,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,WAAW;EAC/C,MAAMC,cAAc,GAAG,GAAGN,WAAW,CAACK,MAAM,gBAAgB;EAE5D,MAAME,WAAW,GAAY;IAC3BC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE;GACP;EAED,MAAMC,yBAAyB,GAA0B;IACvDC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,CAACN,WAAW;GACvB;EAED,MAAMO,wBAAwB,GAAyB;IACrDC,OAAO,EAAE,CAAC;IACVC,UAAU,EAAE;GACb;EAEDC,UAAU,CAAC,MAAK;IACdlB,OAAO,CAACmB,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACtB,uBAAuB,CAAC;MAClCuB,SAAS,EAAE,CAACnB,cAAc;KAC3B,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACsB,MAAM,CAACpB,cAAc,CAAC;IACxCG,QAAQ,GAAGL,OAAO,CAACsB,MAAM,CAACvB,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFwB,SAAS,CAAC,MAAK;IACblB,QAAQ,CAACmB,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACtB,OAAO,CAAC,CAACuB,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFxB,QAAQ,CAAC,gBAAgB,EAAE,MAAK;IAC9BsB,EAAE,CAAC,4BAA4B,EAAE,MAAK;MACpC,MAAMG,YAAY,GAAG,CAACpB,WAAW,CAAC;MAElCJ,OAAO,CAACyB,cAAc,EAAE,CAACC,SAAS,CAAEhB,QAAQ,IAAI;QAC9CY,MAAM,CAACZ,QAAQ,CAAC,CAACiB,OAAO,CAACH,YAAY,CAAC;MACxC,CAAC,CAAC;MAEF,MAAMI,GAAG,GAAG3B,QAAQ,CAAC4B,SAAS,CAAC3B,MAAM,CAAC;MACtCoB,MAAM,CAACM,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACT,YAAY,CAAC;IACzB,CAAC,CAAC;IAEFH,EAAE,CAAC,+CAA+C,EAAE,MAAK;MACvDrB,OAAO,CAACyB,cAAc,EAAE,CAACC,SAAS,CAAC;QACjCQ,KAAK,EAAGA,KAAK,IAAI;UACfZ,MAAM,CAACY,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAG3B,QAAQ,CAAC4B,SAAS,CAAC3B,MAAM,CAAC;MACtC0B,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,cAAc,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC5D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFpC,QAAQ,CAAC,sBAAsB,EAAE,MAAK;IACpCsB,EAAE,CAAC,6CAA6C,EAAE,MAAK;MACrD,MAAMgB,qBAAqB,GAAG,CAAC9B,yBAAyB,CAAC;MAEzDP,OAAO,CAACsC,oBAAoB,EAAE,CAACZ,SAAS,CAAEa,KAAK,IAAI;QACjDjB,MAAM,CAACiB,KAAK,CAAC,CAACZ,OAAO,CAACU,qBAAqB,CAAC;MAC9C,CAAC,CAAC;MAEF,MAAMT,GAAG,GAAG3B,QAAQ,CAAC4B,SAAS,CAAC,GAAG1B,cAAc,QAAQ,CAAC;MACzDmB,MAAM,CAACM,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACI,qBAAqB,CAAC;IAClC,CAAC,CAAC;IAEFhB,EAAE,CAAC,sDAAsD,EAAE,MAAK;MAC9DrB,OAAO,CAACsC,oBAAoB,EAAE,CAACZ,SAAS,CAAC;QACvCQ,KAAK,EAAGA,KAAK,IAAI;UACfZ,MAAM,CAACY,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAG3B,QAAQ,CAAC4B,SAAS,CAAC,GAAG1B,cAAc,QAAQ,CAAC;MACzDyB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,cAAc,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC5D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFpC,QAAQ,CAAC,eAAe,EAAE,MAAK;IAC7BsB,EAAE,CAAC,mCAAmC,EAAE,MAAK;MAC3CrB,OAAO,CAACwC,aAAa,CAAC7B,wBAAwB,CAAC,CAACe,SAAS,CAAC,MAAK;QAC7DJ,MAAM,EAAE,CAACmB,OAAO,EAAE;MACpB,CAAC,CAAC;MAEF,MAAMb,GAAG,GAAG3B,QAAQ,CAAC4B,SAAS,CAAC,GAAG1B,cAAc,SAAS,CAAC;MAC1DmB,MAAM,CAACM,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCV,MAAM,CAACM,GAAG,CAACE,OAAO,CAACY,IAAI,CAAC,CAACf,OAAO,CAAChB,wBAAwB,CAAC;MAC1DiB,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;IAEFZ,EAAE,CAAC,4CAA4C,EAAE,MAAK;MACpDrB,OAAO,CAACwC,aAAa,CAAC7B,wBAAwB,CAAC,CAACe,SAAS,CAAC;QACxDQ,KAAK,EAAGA,KAAK,IAAI;UACfZ,MAAM,CAACY,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAG3B,QAAQ,CAAC4B,SAAS,CAAC,GAAG1B,cAAc,SAAS,CAAC;MAC1DyB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFpC,QAAQ,CAAC,eAAe,EAAE,MAAK;IAC7BsB,EAAE,CAAC,qCAAqC,EAAE,MAAK;MAC7CrB,OAAO,CAAC2C,aAAa,CAAChC,wBAAwB,CAAC,CAACe,SAAS,CAAC,MAAK;QAC7DJ,MAAM,EAAE,CAACmB,OAAO,EAAE;MACpB,CAAC,CAAC;MAEF,MAAMb,GAAG,GAAG3B,QAAQ,CAAC4B,SAAS,CAAC,GAAG1B,cAAc,SAAS,CAAC;MAC1DmB,MAAM,CAACM,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCV,MAAM,CAACM,GAAG,CAACE,OAAO,CAACY,IAAI,CAAC,CAACf,OAAO,CAAChB,wBAAwB,CAAC;MAC1DiB,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;IAEFZ,EAAE,CAAC,2CAA2C,EAAE,MAAK;MACnDrB,OAAO,CAAC2C,aAAa,CAAChC,wBAAwB,CAAC,CAACe,SAAS,CAAC;QACxDQ,KAAK,EAAGA,KAAK,IAAI;UACfZ,MAAM,CAACY,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAG3B,QAAQ,CAAC4B,SAAS,CAAC,GAAG1B,cAAc,SAAS,CAAC;MAC1DyB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}