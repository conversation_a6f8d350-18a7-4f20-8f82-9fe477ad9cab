import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ContractValues } from '@contract-management/models/contract-values.model';
import { Contract } from '@contract-management/models/contract.model';
import { CdpEntityService } from '@contract-management/services/cdp-entity.service';
import { ContractValuesService } from '@contract-management/services/contract-values.service';
import { EntityService } from '@contract-management/services/entity.service';
import { AlertService } from '@shared/services/alert.service';
import { NgxCurrencyDirective } from 'ngx-currency';
import { of, throwError } from 'rxjs';
import { ContractValuesFormComponent } from '@contract-management/components/contract-values-form/contract-values-form.component';
import { ContractValuesDialogComponent } from './contract-values-dialog.component';

describe('ContractValuesDialogComponent', () => {
  let component: ContractValuesDialogComponent;
  let fixture: ComponentFixture<ContractValuesDialogComponent>;
  let contractValuesService: jasmine.SpyObj<ContractValuesService>;
  let entityService: jasmine.SpyObj<EntityService>;
  let cdpEntityService: jasmine.SpyObj<CdpEntityService>;
  let alertService: jasmine.SpyObj<AlertService>;
  let dialogRef: jasmine.SpyObj<MatDialogRef<ContractValuesDialogComponent>>;

  const mockContractValues: Required<ContractValues> = {
    id: 1,
    numericValue: 1000000,
    madsValue: 0,
    isOtherEntity: false,
    subscriptionDate: '2024-02-20',
    cdp: 123,
    cdpEntityId: 1,
    cdpEntity: { id: 1, name: 'Test CDP Entity' },
    startDate: '',
    endDate: '',
    futureValidityValue: 0,
    otherValue: 0,
    rp: 0,
    entityId: 0,
    entity: { id: 0, name: 'Test Entity' },
    contractId: 0,
    isTimeOnlyMode: false,
    isMoneyOnlyMode: false,
  };

  const mockEntities = [
    { id: 1, name: 'Entity 1' },
    { id: 2, name: 'Entity 2' },
  ];

  const mockCdpEntities = [
    { id: 1, name: 'CDP Entity 1' },
    { id: 2, name: 'CDP Entity 2' },
  ];

  beforeEach(async () => {
    const contractValuesServiceSpy = jasmine.createSpyObj(
      'ContractValuesService',
      ['create', 'update', 'getLatestEndDateByContractId'],
    );
    const entityServiceSpy = jasmine.createSpyObj('EntityService', ['getAll']);
    const cdpEntityServiceSpy = jasmine.createSpyObj('CdpEntityService', [
      'getAll',
    ]);
    const alertServiceSpy = jasmine.createSpyObj('AlertService', [
      'success',
      'error',
    ]);
    const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close']);

    await TestBed.configureTestingModule({
      imports: [
        MatDialogModule,
        MatButtonModule,
        MatIconModule,
        BrowserAnimationsModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatInputModule,
        MatSelectModule,
        MatSlideToggleModule,
        MatDatepickerModule,
        MatNativeDateModule,
        ContractValuesFormComponent,
        NgxCurrencyDirective,
      ],
      providers: [
        { provide: MatDialogRef, useValue: dialogRefSpy },
        { provide: MAT_DIALOG_DATA, useValue: { contractId: 1 } },
        { provide: ContractValuesService, useValue: contractValuesServiceSpy },
        { provide: EntityService, useValue: entityServiceSpy },
        { provide: CdpEntityService, useValue: cdpEntityServiceSpy },
        { provide: AlertService, useValue: alertServiceSpy },
      ],
    }).compileComponents();

    contractValuesService = TestBed.inject(
      ContractValuesService,
    ) as jasmine.SpyObj<ContractValuesService>;
    entityService = TestBed.inject(
      EntityService,
    ) as jasmine.SpyObj<EntityService>;
    cdpEntityService = TestBed.inject(
      CdpEntityService,
    ) as jasmine.SpyObj<CdpEntityService>;
    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;
    dialogRef = TestBed.inject(MatDialogRef) as jasmine.SpyObj<
      MatDialogRef<ContractValuesDialogComponent>
    >;

    entityService.getAll.and.returnValue(of(mockEntities));
    cdpEntityService.getAll.and.returnValue(of(mockCdpEntities));
    contractValuesService.getLatestEndDateByContractId.and.returnValue(
      of(new Date()),
    );
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ContractValuesDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Form Submission', () => {
    it('should not submit when contract values are null', () => {
      component.onSubmit(null);
      expect(contractValuesService.create).not.toHaveBeenCalled();
      expect(contractValuesService.update).not.toHaveBeenCalled();
    });

    it('should handle empty data object', () => {
      component.data = { contract: { id: 1 } as Contract };
      const newValues: Omit<ContractValues, 'id'> = {
        numericValue: 1000000,
        madsValue: 0,
        isOtherEntity: false,
        subscriptionDate: '2024-02-20',
        cdp: 123,
        cdpEntityId: 1,
        cdpEntity: { id: 1, name: 'Test CDP Entity' },
      };

      contractValuesService.create.and.returnValue(of(mockContractValues));
      component.onSubmit(newValues);

      expect(contractValuesService.create).toHaveBeenCalledWith(newValues);
      expect(alertService.success).toHaveBeenCalled();
      expect(dialogRef.close).toHaveBeenCalledWith(mockContractValues);
    });
  });

  describe('Contract Values Creation', () => {
    it('should create new contract values successfully', () => {
      const newValues: Omit<ContractValues, 'id'> = {
        numericValue: 1000000,
        madsValue: 0,
        isOtherEntity: false,
        subscriptionDate: '2024-02-20',
        cdp: 123,
        cdpEntityId: 1,
        cdpEntity: { id: 1, name: 'Test CDP Entity' },
      };

      contractValuesService.create.and.returnValue(of(mockContractValues));
      component.onSubmit(newValues);

      expect(contractValuesService.create).toHaveBeenCalledWith(newValues);
      expect(alertService.success).toHaveBeenCalledWith(
        'Valores del Contrato guardados correctamente',
      );
      expect(dialogRef.close).toHaveBeenCalledWith(mockContractValues);
    });

    it('should handle creation error', () => {
      const newValues: Omit<ContractValues, 'id'> = {
        numericValue: 1000000,
        madsValue: 0,
        isOtherEntity: false,
        subscriptionDate: '2024-02-20',
        cdp: 123,
        cdpEntityId: 1,
        cdpEntity: { id: 1, name: 'Test CDP Entity' },
      };

      contractValuesService.create.and.returnValue(
        throwError(() => new Error('Error')),
      );
      component.onSubmit(newValues);

      expect(contractValuesService.create).toHaveBeenCalledWith(newValues);
      expect(alertService.error).toHaveBeenCalledWith(
        'Error al guardar los valores del contrato',
      );
      expect(dialogRef.close).not.toHaveBeenCalled();
    });
  });

  describe('Contract Values Update', () => {
    it('should update contract values successfully', () => {
      component.data = {
        contract: { id: 1 } as Contract,
        contractValue: mockContractValues,
      };

      const updateValues: Omit<ContractValues, 'id'> = {
        numericValue: 2000000,
        madsValue: 0,
        isOtherEntity: false,
        subscriptionDate: '2024-02-20',
        cdp: 123,
        cdpEntityId: 1,
        cdpEntity: { id: 1, name: 'Test CDP Entity' },
      };

      const updatedValues = { ...mockContractValues, numericValue: 2000000 };
      contractValuesService.update.and.returnValue(of(updatedValues));
      component.onSubmit(updateValues);

      expect(contractValuesService.update).toHaveBeenCalledWith(
        mockContractValues.id,
        updateValues,
      );
      expect(alertService.success).toHaveBeenCalledWith(
        'Valores del Contrato actualizados correctamente',
      );
      expect(dialogRef.close).toHaveBeenCalledWith(updatedValues);
    });

    it('should handle update error', () => {
      component.data = {
        contract: { id: 1 } as Contract,
        contractValue: mockContractValues,
      };

      const updateValues: Omit<ContractValues, 'id'> = {
        numericValue: 2000000,
        madsValue: 0,
        isOtherEntity: false,
        subscriptionDate: '2024-02-20',
        cdp: 123,
        cdpEntityId: 1,
        cdpEntity: { id: 1, name: 'Test CDP Entity' },
      };

      contractValuesService.update.and.returnValue(
        throwError(() => ({ error: 'Error' })),
      );
      component.onSubmit(updateValues);

      expect(contractValuesService.update).toHaveBeenCalledWith(
        mockContractValues.id,
        updateValues,
      );
      expect(alertService.error).toHaveBeenCalledWith(
        'Error al actualizar los valores del contrato',
      );
      expect(dialogRef.close).not.toHaveBeenCalled();
    });
  });
});
