import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ActivatedRoute } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { ContractDetails } from '@contract-management/models/contract-details.model';
import { ContractorContract } from '@contract-management/models/contractor-contract.model';
import { ContractService } from '@contract-management/services/contract.service';
import { ContractorContractService } from '@contract-management/services/contractor-contract.service';
import { MonthlyReport } from '@contractor-dashboard/models/monthly-report.model';
import { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';
import { AlertService } from '@shared/services/alert.service';
import { of, throwError } from 'rxjs';
import { ContractDetailsPageComponent } from './contract-details-page.component';

describe('ContractDetailsPageComponent', () => {
  let component: ContractDetailsPageComponent;
  let fixture: ComponentFixture<ContractDetailsPageComponent>;
  let contractService: jasmine.SpyObj<ContractService>;
  let contractorContractService: jasmine.SpyObj<ContractorContractService>;
  let monthlyReportService: jasmine.SpyObj<MonthlyReportService>;
  let alertService: jasmine.SpyObj<AlertService>;

  const mockContract: ContractDetails = {
    id: 1,
    contractNumber: 123,
    contractorId: 1,
    fullName: 'Test Contractor',
    contractorIdNumber: 12345,
    object: 'Test Contract',
    rup: true,
    addition: false,
    cession: false,
    settled: false,
    hasCcp: false,
    selectionModalityName: 'Test Modality',
    trackingTypeName: 'Test Tracking',
    contractTypeName: 'Test Type',
    statusName: 'Active',
    dependencyName: 'Test Dependency',
    groupName: 'Test Group',
    contractorEmail: '<EMAIL>',
    monthlyPayment: 1000,
    supervisorFullName: 'Test Supervisor',
    supervisorIdNumber: '98765',
    supervisorPosition: 'Test Position',
  };

  const mockContractorContract: ContractorContract = {
    id: 1,
    subscriptionDate: '2024-01-01',
    contractStartDate: '2024-01-01',
    contractId: 1,
    contractorId: 1,
  };

  const mockMonthlyReport: MonthlyReport = {
    id: 1,
    reportNumber: 1,
    startDate: '2024-01-01',
    endDate: '2024-01-31',
    creationDate: '2024-01-01',
    contractorContractId: 1,
  };

  beforeEach(() => {
    const contractServiceSpy = jasmine.createSpyObj('ContractService', [
      'getDetailsById',
    ]);
    const contractorContractServiceSpy = jasmine.createSpyObj(
      'ContractorContractService',
      ['getAllByContractId'],
    );
    const monthlyReportServiceSpy = jasmine.createSpyObj(
      'MonthlyReportService',
      ['getByContractorContractId'],
    );
    const alertServiceSpy = jasmine.createSpyObj('AlertService', ['error']);

    TestBed.configureTestingModule({
      imports: [
        ContractDetailsPageComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        RouterTestingModule,
      ],
      providers: [
        { provide: ContractService, useValue: contractServiceSpy },
        {
          provide: ContractorContractService,
          useValue: contractorContractServiceSpy,
        },
        { provide: MonthlyReportService, useValue: monthlyReportServiceSpy },
        { provide: AlertService, useValue: alertServiceSpy },
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              paramMap: {
                get: () => '1',
              },
            },
          },
        },
      ],
    });

    fixture = TestBed.createComponent(ContractDetailsPageComponent);
    component = fixture.componentInstance;
    contractService = TestBed.inject(
      ContractService,
    ) as jasmine.SpyObj<ContractService>;
    contractorContractService = TestBed.inject(
      ContractorContractService,
    ) as jasmine.SpyObj<ContractorContractService>;
    monthlyReportService = TestBed.inject(
      MonthlyReportService,
    ) as jasmine.SpyObj<MonthlyReportService>;
    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load contract details on init', () => {
    contractService.getDetailsById.and.returnValue(of(mockContract));
    contractorContractService.getAllByContractId.and.returnValue(
      of([mockContractorContract]),
    );
    monthlyReportService.getByContractorContractId.and.returnValue(
      of([mockMonthlyReport]),
    );

    component.ngOnInit();

    expect(contractService.getDetailsById).toHaveBeenCalledWith(1);
    expect(component.contract).toEqual(mockContract);
    expect(component.isLoading).toBeFalse();
  });

  it('should show error when contract ID is not provided', () => {
    TestBed.resetTestingModule();
    TestBed.configureTestingModule({
      imports: [
        ContractDetailsPageComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        RouterTestingModule,
      ],
      providers: [
        { provide: ContractService, useValue: contractService },
        {
          provide: ContractorContractService,
          useValue: contractorContractService,
        },
        { provide: MonthlyReportService, useValue: monthlyReportService },
        { provide: AlertService, useValue: alertService },
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              paramMap: {
                get: () => null,
              },
            },
          },
        },
      ],
    });

    fixture = TestBed.createComponent(ContractDetailsPageComponent);
    component = fixture.componentInstance;
    component.ngOnInit();

    expect(alertService.error).toHaveBeenCalledWith(
      'ID del contrato no proporcionado',
    );
  });

  it('should handle error when loading contract details', () => {
    contractService.getDetailsById.and.returnValue(
      throwError(() => new Error()),
    );

    component.ngOnInit();

    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar los detalles del contrato',
    );
    expect(component.isLoading).toBeFalse();
  });

  it('should load contractor contract after loading contract details', () => {
    contractService.getDetailsById.and.returnValue(of(mockContract));
    contractorContractService.getAllByContractId.and.returnValue(
      of([mockContractorContract]),
    );
    monthlyReportService.getByContractorContractId.and.returnValue(
      of([mockMonthlyReport]),
    );

    component.ngOnInit();

    expect(contractorContractService.getAllByContractId).toHaveBeenCalledWith(
      1,
    );
    expect(component.contractorContractId).toBe(1);
  });

  it('should show error when no contractor contract is found', () => {
    contractService.getDetailsById.and.returnValue(of(mockContract));
    contractorContractService.getAllByContractId.and.returnValue(of([]));

    component.ngOnInit();

    expect(alertService.error).toHaveBeenCalledWith(
      'No se encontró un contrato de contratista para este contrato',
    );
    expect(component.isLoading).toBeFalse();
  });

  it('should handle error when loading contractor contract', () => {
    contractService.getDetailsById.and.returnValue(of(mockContract));
    contractorContractService.getAllByContractId.and.returnValue(
      throwError(() => new Error()),
    );

    component.ngOnInit();

    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar el contrato del contratista',
    );
    expect(component.isLoading).toBeFalse();
  });

  it('should load monthly reports after loading contractor contract', () => {
    contractService.getDetailsById.and.returnValue(of(mockContract));
    contractorContractService.getAllByContractId.and.returnValue(
      of([mockContractorContract]),
    );
    monthlyReportService.getByContractorContractId.and.returnValue(
      of([mockMonthlyReport]),
    );

    component.ngOnInit();

    expect(monthlyReportService.getByContractorContractId).toHaveBeenCalledWith(
      1,
    );
    expect(component.monthlyReports).toEqual([mockMonthlyReport]);
    expect(component.isLoading).toBeFalse();
  });

  it('should handle error when loading monthly reports', () => {
    contractService.getDetailsById.and.returnValue(of(mockContract));
    contractorContractService.getAllByContractId.and.returnValue(
      of([mockContractorContract]),
    );
    monthlyReportService.getByContractorContractId.and.returnValue(
      throwError(() => new Error()),
    );

    component.ngOnInit();

    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar informes mensuales',
    );
    expect(component.isLoading).toBeFalse();
  });

  it('should show error when trying to load monthly reports without contractor contract ID', () => {
    component.loadMonthlyReportsAndPayments();

    expect(alertService.error).toHaveBeenCalledWith(
      'ID de contrato de contratista no disponible',
    );
    expect(component.isLoading).toBeFalse();
  });
});