{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { ContractService } from './contract.service';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { ContractAuditHistoryService } from './contract-audit-history.service';\nimport { ContractAuditStatusService } from './contract-audit-status.service';\nimport { of } from 'rxjs';\ndescribe('ContractService', () => {\n  let service;\n  let httpMock;\n  let authServiceSpy;\n  let contractAuditHistoryServiceSpy;\n  let contractAuditStatusServiceSpy;\n  const apiUrl = `${environment.apiUrl}/contracts`;\n  const mockContract = {\n    id: 1,\n    contractNumber: 123,\n    object: 'Test Object',\n    contractTypeId: 1,\n    contractClassId: 1,\n    contractYearId: 1,\n    statusId: 1,\n    rup: true,\n    secopCode: 1,\n    addition: false,\n    cession: false,\n    settled: false,\n    monthlyPayment: 1000000,\n    causesSelectionId: 1,\n    managementSupportId: 1\n  };\n  const mockUser = {\n    id: 1,\n    username: 'testuser',\n    profiles: []\n  };\n  const mockAuditStatus = {\n    id: 1,\n    name: 'Creación de contrato',\n    description: 'Contract creation status'\n  };\n  const mockContractDetails = {\n    id: 1,\n    contractNumber: 123,\n    object: 'Test Object',\n    contractTypeName: 'Test Type',\n    statusName: 'Active',\n    contractorId: 1,\n    fullName: 'Test Contractor',\n    contractorIdNumber: 123456789,\n    rup: true,\n    addition: false,\n    cession: false,\n    settled: false,\n    hasCcp: false,\n    selectionModalityName: 'Test Modality',\n    trackingTypeName: 'Test Tracking',\n    dependencyName: 'Test Dependency',\n    groupName: 'Test Group',\n    contractorEmail: '<EMAIL>',\n    monthlyPayment: 1000000,\n    supervisorFullName: 'Test Supervisor',\n    supervisorIdNumber: '987654321',\n    supervisorPosition: 'Test Position'\n  };\n  beforeEach(() => {\n    authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);\n    contractAuditHistoryServiceSpy = jasmine.createSpyObj('ContractAuditHistoryService', ['create']);\n    contractAuditStatusServiceSpy = jasmine.createSpyObj('ContractAuditStatusService', ['getByName']);\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [ContractService, {\n        provide: AuthService,\n        useValue: authServiceSpy\n      }, {\n        provide: ContractAuditHistoryService,\n        useValue: contractAuditHistoryServiceSpy\n      }, {\n        provide: ContractAuditStatusService,\n        useValue: contractAuditStatusServiceSpy\n      }]\n    });\n    service = TestBed.inject(ContractService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all contracts', () => {\n      const mockContracts = [mockContract];\n      service.getAll().subscribe(contracts => {\n        expect(contracts).toEqual(mockContracts);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContracts);\n    });\n  });\n  describe('getById', () => {\n    it('should return contract by id', () => {\n      service.getById(1).subscribe(result => {\n        expect(result).toEqual(mockContract);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContract);\n    });\n  });\n  describe('getAllDetails', () => {\n    it('should return all contract details', () => {\n      const mockContractDetailsArray = [mockContractDetails];\n      service.getAllDetails().subscribe(result => {\n        expect(result).toEqual(mockContractDetailsArray);\n      });\n      const req = httpMock.expectOne(`${apiUrl}-details`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractDetailsArray);\n    });\n  });\n  describe('getDetailsById', () => {\n    it('should return contract details by id', () => {\n      service.getDetailsById(1).subscribe(result => {\n        expect(result).toEqual(mockContractDetails);\n      });\n      const req = httpMock.expectOne(`${apiUrl}-details/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractDetails);\n    });\n  });\n  describe('getByContractorIdNumber', () => {\n    it('should return contracts by contractor id number', () => {\n      const mockContracts = [mockContract];\n      service.getByContractorIdNumber(123456789).subscribe(result => {\n        expect(result).toEqual(mockContracts);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/contractor/123456789`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContracts);\n    });\n  });\n  describe('create', () => {\n    it('should create contract', () => {\n      const mockContractRequest = {\n        contractNumber: 123,\n        object: 'Test Object',\n        contractTypeId: 1,\n        contractClassId: 1,\n        contractYearId: 1,\n        statusId: 1,\n        rup: true,\n        secopCode: 1,\n        addition: false,\n        cession: false,\n        settled: false,\n        monthlyPayment: 1000000,\n        causesSelectionId: 1,\n        managementSupportId: 1\n      };\n      service.create(mockContractRequest).subscribe(result => {\n        expect(result).toEqual(mockContract);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(mockContractRequest);\n      req.flush(mockContract);\n    });\n  });\n  describe('createCompleteContract', () => {\n    it('should create a complete contract with audit history when user is logged in', () => {\n      const mockContractValues = {\n        numericValue: 1000000,\n        madsValue: 0,\n        isOtherEntity: false,\n        subscriptionDate: new Date().toISOString(),\n        cdp: 123,\n        cdpEntityId: 1,\n        cdpEntity: {\n          id: 1,\n          name: 'Test CDP Entity'\n        }\n      };\n      const mockContractContractor = {\n        warranty: false,\n        supervisorId: 1\n      };\n      const mockCompleteContract = {\n        contractorIdNumber: 123456789,\n        contract: mockContract,\n        contractValues: mockContractValues,\n        contractContractor: mockContractContractor\n      };\n      const data = {\n        contractorIdNumber: 123456789,\n        contract: mockContract,\n        contractContractor: mockContractContractor,\n        contractValues: mockContractValues\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n      contractAuditStatusServiceSpy.getByName.and.returnValue(of(mockAuditStatus));\n      const mockAuditHistory = {\n        id: 1,\n        contractId: 1,\n        auditStatusId: 1,\n        auditDate: new Date(),\n        comment: 'Test comment',\n        auditorId: 1\n      };\n      contractAuditHistoryServiceSpy.create.and.returnValue(of(mockAuditHistory));\n      service.createCompleteContract(data).subscribe(result => {\n        expect(result).toEqual(mockCompleteContract);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith('Creación de contrato');\n        expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/complete`);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(data);\n      req.flush(mockCompleteContract);\n      const contractorReq = httpMock.expectOne('/api/contractors/id-number/123456789');\n      expect(contractorReq.request.method).toBe('GET');\n      contractorReq.flush({\n        id: 1,\n        fullName: 'Test Contractor'\n      });\n      const emailReq = httpMock.expectOne('/api/email-templates/send/new_contract_notification');\n      expect(emailReq.request.method).toBe('POST');\n      emailReq.flush({\n        success: true\n      });\n    });\n    it('should create a complete contract without audit history when user is not logged in', () => {\n      const mockContractValues = {\n        numericValue: 1000000,\n        madsValue: 0,\n        isOtherEntity: false,\n        subscriptionDate: new Date().toISOString(),\n        cdp: 123,\n        cdpEntityId: 1,\n        cdpEntity: {\n          id: 1,\n          name: 'Test CDP Entity'\n        }\n      };\n      const mockContractContractor = {\n        warranty: false,\n        supervisorId: 1\n      };\n      const mockCompleteContract = {\n        contractorIdNumber: 123456789,\n        contract: mockContract,\n        contractValues: mockContractValues,\n        contractContractor: mockContractContractor\n      };\n      const data = {\n        contractorIdNumber: 123456789,\n        contract: mockContract,\n        contractContractor: mockContractContractor,\n        contractValues: mockContractValues\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n      service.createCompleteContract(data).subscribe(result => {\n        expect(result).toEqual(mockCompleteContract);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n        expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/complete`);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(data);\n      req.flush(mockCompleteContract);\n    });\n  });\n  describe('update', () => {\n    it('should update contract', () => {\n      const mockContractRequest = {\n        object: 'Updated Object'\n      };\n      service.update(1, mockContractRequest).subscribe(result => {\n        expect(result).toEqual({\n          ...mockContract,\n          object: 'Updated Object'\n        });\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(mockContractRequest);\n      req.flush({\n        ...mockContract,\n        object: 'Updated Object'\n      });\n    });\n  });\n  describe('delete', () => {\n    it('should delete contract', () => {\n      service.delete(1).subscribe(result => {\n        expect(result).toBeDefined();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush({});\n    });\n  });\n  describe('validateContractNumber', () => {\n    it('should validate contract number', () => {\n      service.validateContractNumber(123, 1).subscribe(result => {\n        expect(result).toBe(true);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/validate/123/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(true);\n    });\n  });\n  describe('exportContracts', () => {\n    it('should export contracts as blob', () => {\n      const blob = new Blob(['test data'], {\n        type: 'application/vnd.ms-excel'\n      });\n      service.exportContracts(2024).subscribe(result => {\n        expect(result instanceof Blob).toBe(true);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/export/2024`);\n      expect(req.request.method).toBe('GET');\n      expect(req.request.responseType).toBe('blob');\n      req.flush(blob);\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "ContractService", "AuthService", "ContractAuditHistoryService", "ContractAuditStatusService", "of", "describe", "service", "httpMock", "authServiceSpy", "contractAuditHistoryServiceSpy", "contractAuditStatusServiceSpy", "apiUrl", "mockContract", "id", "contractNumber", "object", "contractTypeId", "contractClassId", "contractYearId", "statusId", "rup", "secopCode", "addition", "cession", "settled", "monthlyPayment", "causesSelectionId", "managementSupportId", "mockUser", "username", "profiles", "mockAuditStatus", "name", "description", "mockContractDetails", "contractTypeName", "statusName", "contractorId", "fullName", "contractorIdNumber", "hasCcp", "selectionModalityName", "trackingTypeName", "dependencyName", "groupName", "contractorEmail", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supervisorIdNumber", "supervisorPosition", "beforeEach", "jasmine", "createSpyObj", "configureTestingModule", "imports", "providers", "provide", "useValue", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockContracts", "getAll", "subscribe", "contracts", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "getById", "result", "mockContractDetailsArray", "getAllDetails", "getDetailsById", "getByContractorIdNumber", "mockContractRequest", "create", "body", "mockContractValues", "numericValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isOtherEntity", "subscriptionDate", "Date", "toISOString", "cdp", "cdpEntityId", "cdpEntity", "mockContractContractor", "warranty", "supervisorId", "mockCompleteContract", "contract", "contractValues", "contractContractor", "data", "getCurrentUser", "and", "returnValue", "getByName", "mockAuditHistory", "contractId", "auditStatusId", "auditDate", "comment", "auditorId", "createCompleteContract", "toHaveBeenCalled", "toHaveBeenCalledWith", "contractorReq", "emailReq", "success", "not", "update", "delete", "toBeDefined", "validateContractNumber", "blob", "Blob", "type", "exportContracts", "responseType"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\services\\contract.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { CompleteContract } from '@contract-management/models/complete-contract.model';\nimport { ContractDetails } from '@contract-management/models/contract-details.model';\nimport { ContractValues } from '@contract-management/models/contract-values.model';\nimport { Contract } from '@contract-management/models/contract.model';\nimport { ContractContractor } from '@contract-management/models/contract_contractor.model';\nimport { environment } from '@env';\nimport { ContractService } from './contract.service';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { ContractAuditHistoryService } from './contract-audit-history.service';\nimport { ContractAuditStatusService } from './contract-audit-status.service';\nimport { of } from 'rxjs';\nimport { User } from '@core/auth/models/user.model';\nimport { ContractAuditStatus } from '../models/contract-audit-status.model';\nimport { ContractAuditHistory } from '../models/contract-audit-history.model';\n\ndescribe('ContractService', () => {\n  let service: ContractService;\n  let httpMock: HttpTestingController;\n  let authServiceSpy: jasmine.SpyObj<AuthService>;\n  let contractAuditHistoryServiceSpy: jasmine.SpyObj<ContractAuditHistoryService>;\n  let contractAuditStatusServiceSpy: jasmine.SpyObj<ContractAuditStatusService>;\n  const apiUrl = `${environment.apiUrl}/contracts`;\n\n  const mockContract: Contract = {\n    id: 1,\n    contractNumber: 123,\n    object: 'Test Object',\n    contractTypeId: 1,\n    contractClassId: 1,\n    contractYearId: 1,\n    statusId: 1,\n    rup: true,\n    secopCode: 1,\n    addition: false,\n    cession: false,\n    settled: false,\n    monthlyPayment: 1000000,\n    causesSelectionId: 1,\n    managementSupportId: 1,\n  };\n\n  const mockUser: User = {\n    id: 1,\n    username: 'testuser',\n    profiles: [],\n  };\n\n  const mockAuditStatus: ContractAuditStatus = {\n    id: 1,\n    name: 'Creación de contrato',\n    description: 'Contract creation status',\n  };\n\n  const mockContractDetails: ContractDetails = {\n    id: 1,\n    contractNumber: 123,\n    object: 'Test Object',\n    contractTypeName: 'Test Type',\n    statusName: 'Active',\n    contractorId: 1,\n    fullName: 'Test Contractor',\n    contractorIdNumber: 123456789,\n    rup: true,\n    addition: false,\n    cession: false,\n    settled: false,\n    hasCcp: false,\n    selectionModalityName: 'Test Modality',\n    trackingTypeName: 'Test Tracking',\n    dependencyName: 'Test Dependency',\n    groupName: 'Test Group',\n    contractorEmail: '<EMAIL>',\n    monthlyPayment: 1000000,\n    supervisorFullName: 'Test Supervisor',\n    supervisorIdNumber: '987654321',\n    supervisorPosition: 'Test Position',\n  };\n\n  beforeEach(() => {\n    authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);\n    contractAuditHistoryServiceSpy = jasmine.createSpyObj(\n      'ContractAuditHistoryService',\n      ['create'],\n    );\n    contractAuditStatusServiceSpy = jasmine.createSpyObj(\n      'ContractAuditStatusService',\n      ['getByName'],\n    );\n\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [\n        ContractService,\n        { provide: AuthService, useValue: authServiceSpy },\n        {\n          provide: ContractAuditHistoryService,\n          useValue: contractAuditHistoryServiceSpy,\n        },\n        {\n          provide: ContractAuditStatusService,\n          useValue: contractAuditStatusServiceSpy,\n        },\n      ],\n    });\n    service = TestBed.inject(ContractService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all contracts', () => {\n      const mockContracts: Contract[] = [mockContract];\n\n      service.getAll().subscribe((contracts) => {\n        expect(contracts).toEqual(mockContracts);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContracts);\n    });\n  });\n\n  describe('getById', () => {\n    it('should return contract by id', () => {\n      service.getById(1).subscribe((result) => {\n        expect(result).toEqual(mockContract);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContract);\n    });\n  });\n\n  describe('getAllDetails', () => {\n    it('should return all contract details', () => {\n      const mockContractDetailsArray: ContractDetails[] = [mockContractDetails];\n\n      service.getAllDetails().subscribe((result) => {\n        expect(result).toEqual(mockContractDetailsArray);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}-details`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractDetailsArray);\n    });\n  });\n\n  describe('getDetailsById', () => {\n    it('should return contract details by id', () => {\n      service.getDetailsById(1).subscribe((result) => {\n        expect(result).toEqual(mockContractDetails);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}-details/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractDetails);\n    });\n  });\n\n  describe('getByContractorIdNumber', () => {\n    it('should return contracts by contractor id number', () => {\n      const mockContracts: Contract[] = [mockContract];\n\n      service.getByContractorIdNumber(123456789).subscribe((result) => {\n        expect(result).toEqual(mockContracts);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/contractor/123456789`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContracts);\n    });\n  });\n\n  describe('create', () => {\n    it('should create contract', () => {\n      const mockContractRequest: Omit<Contract, 'id'> = {\n        contractNumber: 123,\n        object: 'Test Object',\n        contractTypeId: 1,\n        contractClassId: 1,\n        contractYearId: 1,\n        statusId: 1,\n        rup: true,\n        secopCode: 1,\n        addition: false,\n        cession: false,\n        settled: false,\n        monthlyPayment: 1000000,\n        causesSelectionId: 1,\n        managementSupportId: 1,\n      };\n\n      service.create(mockContractRequest).subscribe((result) => {\n        expect(result).toEqual(mockContract);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(mockContractRequest);\n      req.flush(mockContract);\n    });\n  });\n\n  describe('createCompleteContract', () => {\n    it('should create a complete contract with audit history when user is logged in', () => {\n      const mockContractValues: Omit<ContractValues, 'id'> = {\n        numericValue: 1000000,\n        madsValue: 0,\n        isOtherEntity: false,\n        subscriptionDate: new Date().toISOString(),\n        cdp: 123,\n        cdpEntityId: 1,\n        cdpEntity: { id: 1, name: 'Test CDP Entity' },\n      };\n\n      const mockContractContractor: Omit<ContractContractor, 'id'> = {\n        warranty: false,\n        supervisorId: 1,\n      };\n\n      const mockCompleteContract: CompleteContract = {\n        contractorIdNumber: 123456789,\n        contract: mockContract,\n        contractValues: mockContractValues,\n        contractContractor: mockContractContractor,\n      };\n\n      const data = {\n        contractorIdNumber: 123456789,\n        contract: mockContract,\n        contractContractor: mockContractContractor,\n        contractValues: mockContractValues,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n      contractAuditStatusServiceSpy.getByName.and.returnValue(\n        of(mockAuditStatus),\n      );\n\n      const mockAuditHistory: ContractAuditHistory = {\n        id: 1,\n        contractId: 1,\n        auditStatusId: 1,\n        auditDate: new Date(),\n        comment: 'Test comment',\n        auditorId: 1,\n      };\n\n      contractAuditHistoryServiceSpy.create.and.returnValue(\n        of(mockAuditHistory),\n      );\n\n      service.createCompleteContract(data).subscribe((result) => {\n        expect(result).toEqual(mockCompleteContract);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith(\n          'Creación de contrato',\n        );\n        expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/complete`);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(data);\n      req.flush(mockCompleteContract);\n\n      const contractorReq = httpMock.expectOne(\n        '/api/contractors/id-number/123456789',\n      );\n      expect(contractorReq.request.method).toBe('GET');\n      contractorReq.flush({ id: 1, fullName: 'Test Contractor' });\n\n      const emailReq = httpMock.expectOne(\n        '/api/email-templates/send/new_contract_notification',\n      );\n      expect(emailReq.request.method).toBe('POST');\n      emailReq.flush({ success: true });\n    });\n\n    it('should create a complete contract without audit history when user is not logged in', () => {\n      const mockContractValues: Omit<ContractValues, 'id'> = {\n        numericValue: 1000000,\n        madsValue: 0,\n        isOtherEntity: false,\n        subscriptionDate: new Date().toISOString(),\n        cdp: 123,\n        cdpEntityId: 1,\n        cdpEntity: { id: 1, name: 'Test CDP Entity' },\n      };\n\n      const mockContractContractor: Omit<ContractContractor, 'id'> = {\n        warranty: false,\n        supervisorId: 1,\n      };\n\n      const mockCompleteContract: CompleteContract = {\n        contractorIdNumber: 123456789,\n        contract: mockContract,\n        contractValues: mockContractValues,\n        contractContractor: mockContractContractor,\n      };\n\n      const data = {\n        contractorIdNumber: 123456789,\n        contract: mockContract,\n        contractContractor: mockContractContractor,\n        contractValues: mockContractValues,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n\n      service.createCompleteContract(data).subscribe((result) => {\n        expect(result).toEqual(mockCompleteContract);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n        expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/complete`);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(data);\n      req.flush(mockCompleteContract);\n    });\n  });\n\n  describe('update', () => {\n    it('should update contract', () => {\n      const mockContractRequest: Partial<Contract> = {\n        object: 'Updated Object',\n      };\n\n      service.update(1, mockContractRequest).subscribe((result) => {\n        expect(result).toEqual({ ...mockContract, object: 'Updated Object' });\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(mockContractRequest);\n      req.flush({ ...mockContract, object: 'Updated Object' });\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete contract', () => {\n      service.delete(1).subscribe((result) => {\n        expect(result).toBeDefined();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush({});\n    });\n  });\n\n  describe('validateContractNumber', () => {\n    it('should validate contract number', () => {\n      service.validateContractNumber(123, 1).subscribe((result) => {\n        expect(result).toBe(true);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/validate/123/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(true);\n    });\n  });\n\n  describe('exportContracts', () => {\n    it('should export contracts as blob', () => {\n      const blob = new Blob(['test data'], {\n        type: 'application/vnd.ms-excel',\n      });\n\n      service.exportContracts(2024).subscribe((result) => {\n        expect(result instanceof Blob).toBe(true);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/export/2024`);\n      expect(req.request.method).toBe('GET');\n      expect(req.request.responseType).toBe('blob');\n      req.flush(blob);\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAM/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,0BAA0B,QAAQ,iCAAiC;AAC5E,SAASC,EAAE,QAAQ,MAAM;AAKzBC,QAAQ,CAAC,iBAAiB,EAAE,MAAK;EAC/B,IAAIC,OAAwB;EAC5B,IAAIC,QAA+B;EACnC,IAAIC,cAA2C;EAC/C,IAAIC,8BAA2E;EAC/E,IAAIC,6BAAyE;EAC7E,MAAMC,MAAM,GAAG,GAAGZ,WAAW,CAACY,MAAM,YAAY;EAEhD,MAAMC,YAAY,GAAa;IAC7BC,EAAE,EAAE,CAAC;IACLC,cAAc,EAAE,GAAG;IACnBC,MAAM,EAAE,aAAa;IACrBC,cAAc,EAAE,CAAC;IACjBC,eAAe,EAAE,CAAC;IAClBC,cAAc,EAAE,CAAC;IACjBC,QAAQ,EAAE,CAAC;IACXC,GAAG,EAAE,IAAI;IACTC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,KAAK;IACdC,cAAc,EAAE,OAAO;IACvBC,iBAAiB,EAAE,CAAC;IACpBC,mBAAmB,EAAE;GACtB;EAED,MAAMC,QAAQ,GAAS;IACrBf,EAAE,EAAE,CAAC;IACLgB,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE;GACX;EAED,MAAMC,eAAe,GAAwB;IAC3ClB,EAAE,EAAE,CAAC;IACLmB,IAAI,EAAE,sBAAsB;IAC5BC,WAAW,EAAE;GACd;EAED,MAAMC,mBAAmB,GAAoB;IAC3CrB,EAAE,EAAE,CAAC;IACLC,cAAc,EAAE,GAAG;IACnBC,MAAM,EAAE,aAAa;IACrBoB,gBAAgB,EAAE,WAAW;IAC7BC,UAAU,EAAE,QAAQ;IACpBC,YAAY,EAAE,CAAC;IACfC,QAAQ,EAAE,iBAAiB;IAC3BC,kBAAkB,EAAE,SAAS;IAC7BnB,GAAG,EAAE,IAAI;IACTE,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,KAAK;IACdgB,MAAM,EAAE,KAAK;IACbC,qBAAqB,EAAE,eAAe;IACtCC,gBAAgB,EAAE,eAAe;IACjCC,cAAc,EAAE,iBAAiB;IACjCC,SAAS,EAAE,YAAY;IACvBC,eAAe,EAAE,qBAAqB;IACtCpB,cAAc,EAAE,OAAO;IACvBqB,kBAAkB,EAAE,iBAAiB;IACrCC,kBAAkB,EAAE,WAAW;IAC/BC,kBAAkB,EAAE;GACrB;EAEDC,UAAU,CAAC,MAAK;IACdzC,cAAc,GAAG0C,OAAO,CAACC,YAAY,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,CAAC;IACxE1C,8BAA8B,GAAGyC,OAAO,CAACC,YAAY,CACnD,6BAA6B,EAC7B,CAAC,QAAQ,CAAC,CACX;IACDzC,6BAA6B,GAAGwC,OAAO,CAACC,YAAY,CAClD,4BAA4B,EAC5B,CAAC,WAAW,CAAC,CACd;IAEDrD,OAAO,CAACsD,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACzD,uBAAuB,CAAC;MAClC0D,SAAS,EAAE,CACTtD,eAAe,EACf;QAAEuD,OAAO,EAAEtD,WAAW;QAAEuD,QAAQ,EAAEhD;MAAc,CAAE,EAClD;QACE+C,OAAO,EAAErD,2BAA2B;QACpCsD,QAAQ,EAAE/C;OACX,EACD;QACE8C,OAAO,EAAEpD,0BAA0B;QACnCqD,QAAQ,EAAE9C;OACX;KAEJ,CAAC;IACFJ,OAAO,GAAGR,OAAO,CAAC2D,MAAM,CAACzD,eAAe,CAAC;IACzCO,QAAQ,GAAGT,OAAO,CAAC2D,MAAM,CAAC5D,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEF6D,SAAS,CAAC,MAAK;IACbnD,QAAQ,CAACoD,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACvD,OAAO,CAAC,CAACwD,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFzD,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBuD,EAAE,CAAC,6BAA6B,EAAE,MAAK;MACrC,MAAMG,aAAa,GAAe,CAACnD,YAAY,CAAC;MAEhDN,OAAO,CAAC0D,MAAM,EAAE,CAACC,SAAS,CAAEC,SAAS,IAAI;QACvCL,MAAM,CAACK,SAAS,CAAC,CAACC,OAAO,CAACJ,aAAa,CAAC;MAC1C,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAG7D,QAAQ,CAAC8D,SAAS,CAAC1D,MAAM,CAAC;MACtCkD,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,aAAa,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF1D,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBuD,EAAE,CAAC,8BAA8B,EAAE,MAAK;MACtCtD,OAAO,CAACoE,OAAO,CAAC,CAAC,CAAC,CAACT,SAAS,CAAEU,MAAM,IAAI;QACtCd,MAAM,CAACc,MAAM,CAAC,CAACR,OAAO,CAACvD,YAAY,CAAC;MACtC,CAAC,CAAC;MAEF,MAAMwD,GAAG,GAAG7D,QAAQ,CAAC8D,SAAS,CAAC,GAAG1D,MAAM,IAAI,CAAC;MAC7CkD,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAAC7D,YAAY,CAAC;IACzB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFP,QAAQ,CAAC,eAAe,EAAE,MAAK;IAC7BuD,EAAE,CAAC,oCAAoC,EAAE,MAAK;MAC5C,MAAMgB,wBAAwB,GAAsB,CAAC1C,mBAAmB,CAAC;MAEzE5B,OAAO,CAACuE,aAAa,EAAE,CAACZ,SAAS,CAAEU,MAAM,IAAI;QAC3Cd,MAAM,CAACc,MAAM,CAAC,CAACR,OAAO,CAACS,wBAAwB,CAAC;MAClD,CAAC,CAAC;MAEF,MAAMR,GAAG,GAAG7D,QAAQ,CAAC8D,SAAS,CAAC,GAAG1D,MAAM,UAAU,CAAC;MACnDkD,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACG,wBAAwB,CAAC;IACrC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFvE,QAAQ,CAAC,gBAAgB,EAAE,MAAK;IAC9BuD,EAAE,CAAC,sCAAsC,EAAE,MAAK;MAC9CtD,OAAO,CAACwE,cAAc,CAAC,CAAC,CAAC,CAACb,SAAS,CAAEU,MAAM,IAAI;QAC7Cd,MAAM,CAACc,MAAM,CAAC,CAACR,OAAO,CAACjC,mBAAmB,CAAC;MAC7C,CAAC,CAAC;MAEF,MAAMkC,GAAG,GAAG7D,QAAQ,CAAC8D,SAAS,CAAC,GAAG1D,MAAM,YAAY,CAAC;MACrDkD,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvC,mBAAmB,CAAC;IAChC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,yBAAyB,EAAE,MAAK;IACvCuD,EAAE,CAAC,iDAAiD,EAAE,MAAK;MACzD,MAAMG,aAAa,GAAe,CAACnD,YAAY,CAAC;MAEhDN,OAAO,CAACyE,uBAAuB,CAAC,SAAS,CAAC,CAACd,SAAS,CAAEU,MAAM,IAAI;QAC9Dd,MAAM,CAACc,MAAM,CAAC,CAACR,OAAO,CAACJ,aAAa,CAAC;MACvC,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAG7D,QAAQ,CAAC8D,SAAS,CAAC,GAAG1D,MAAM,uBAAuB,CAAC;MAChEkD,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,aAAa,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF1D,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBuD,EAAE,CAAC,wBAAwB,EAAE,MAAK;MAChC,MAAMoB,mBAAmB,GAAyB;QAChDlE,cAAc,EAAE,GAAG;QACnBC,MAAM,EAAE,aAAa;QACrBC,cAAc,EAAE,CAAC;QACjBC,eAAe,EAAE,CAAC;QAClBC,cAAc,EAAE,CAAC;QACjBC,QAAQ,EAAE,CAAC;QACXC,GAAG,EAAE,IAAI;QACTC,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE,KAAK;QACfC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,KAAK;QACdC,cAAc,EAAE,OAAO;QACvBC,iBAAiB,EAAE,CAAC;QACpBC,mBAAmB,EAAE;OACtB;MAEDrB,OAAO,CAAC2E,MAAM,CAACD,mBAAmB,CAAC,CAACf,SAAS,CAAEU,MAAM,IAAI;QACvDd,MAAM,CAACc,MAAM,CAAC,CAACR,OAAO,CAACvD,YAAY,CAAC;MACtC,CAAC,CAAC;MAEF,MAAMwD,GAAG,GAAG7D,QAAQ,CAAC8D,SAAS,CAAC1D,MAAM,CAAC;MACtCkD,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACY,IAAI,CAAC,CAACf,OAAO,CAACa,mBAAmB,CAAC;MACrDZ,GAAG,CAACK,KAAK,CAAC7D,YAAY,CAAC;IACzB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFP,QAAQ,CAAC,wBAAwB,EAAE,MAAK;IACtCuD,EAAE,CAAC,6EAA6E,EAAE,MAAK;MACrF,MAAMuB,kBAAkB,GAA+B;QACrDC,YAAY,EAAE,OAAO;QACrBC,SAAS,EAAE,CAAC;QACZC,aAAa,EAAE,KAAK;QACpBC,gBAAgB,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;QAC1CC,GAAG,EAAE,GAAG;QACRC,WAAW,EAAE,CAAC;QACdC,SAAS,EAAE;UAAE/E,EAAE,EAAE,CAAC;UAAEmB,IAAI,EAAE;QAAiB;OAC5C;MAED,MAAM6D,sBAAsB,GAAmC;QAC7DC,QAAQ,EAAE,KAAK;QACfC,YAAY,EAAE;OACf;MAED,MAAMC,oBAAoB,GAAqB;QAC7CzD,kBAAkB,EAAE,SAAS;QAC7B0D,QAAQ,EAAErF,YAAY;QACtBsF,cAAc,EAAEf,kBAAkB;QAClCgB,kBAAkB,EAAEN;OACrB;MAED,MAAMO,IAAI,GAAG;QACX7D,kBAAkB,EAAE,SAAS;QAC7B0D,QAAQ,EAAErF,YAAY;QACtBuF,kBAAkB,EAAEN,sBAAsB;QAC1CK,cAAc,EAAEf;OACjB;MAED3E,cAAc,CAAC6F,cAAc,CAACC,GAAG,CAACC,WAAW,CAAC3E,QAAQ,CAAC;MACvDlB,6BAA6B,CAAC8F,SAAS,CAACF,GAAG,CAACC,WAAW,CACrDnG,EAAE,CAAC2B,eAAe,CAAC,CACpB;MAED,MAAM0E,gBAAgB,GAAyB;QAC7C5F,EAAE,EAAE,CAAC;QACL6F,UAAU,EAAE,CAAC;QACbC,aAAa,EAAE,CAAC;QAChBC,SAAS,EAAE,IAAIpB,IAAI,EAAE;QACrBqB,OAAO,EAAE,cAAc;QACvBC,SAAS,EAAE;OACZ;MAEDrG,8BAA8B,CAACwE,MAAM,CAACqB,GAAG,CAACC,WAAW,CACnDnG,EAAE,CAACqG,gBAAgB,CAAC,CACrB;MAEDnG,OAAO,CAACyG,sBAAsB,CAACX,IAAI,CAAC,CAACnC,SAAS,CAAEU,MAAM,IAAI;QACxDd,MAAM,CAACc,MAAM,CAAC,CAACR,OAAO,CAAC6B,oBAAoB,CAAC;QAC5CnC,MAAM,CAACrD,cAAc,CAAC6F,cAAc,CAAC,CAACW,gBAAgB,EAAE;QACxDnD,MAAM,CAACnD,6BAA6B,CAAC8F,SAAS,CAAC,CAACS,oBAAoB,CAClE,sBAAsB,CACvB;QACDpD,MAAM,CAACpD,8BAA8B,CAACwE,MAAM,CAAC,CAAC+B,gBAAgB,EAAE;MAClE,CAAC,CAAC;MAEF,MAAM5C,GAAG,GAAG7D,QAAQ,CAAC8D,SAAS,CAAC,GAAG1D,MAAM,WAAW,CAAC;MACpDkD,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACY,IAAI,CAAC,CAACf,OAAO,CAACiC,IAAI,CAAC;MACtChC,GAAG,CAACK,KAAK,CAACuB,oBAAoB,CAAC;MAE/B,MAAMkB,aAAa,GAAG3G,QAAQ,CAAC8D,SAAS,CACtC,sCAAsC,CACvC;MACDR,MAAM,CAACqD,aAAa,CAAC5C,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MAChD0C,aAAa,CAACzC,KAAK,CAAC;QAAE5D,EAAE,EAAE,CAAC;QAAEyB,QAAQ,EAAE;MAAiB,CAAE,CAAC;MAE3D,MAAM6E,QAAQ,GAAG5G,QAAQ,CAAC8D,SAAS,CACjC,qDAAqD,CACtD;MACDR,MAAM,CAACsD,QAAQ,CAAC7C,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MAC5C2C,QAAQ,CAAC1C,KAAK,CAAC;QAAE2C,OAAO,EAAE;MAAI,CAAE,CAAC;IACnC,CAAC,CAAC;IAEFxD,EAAE,CAAC,oFAAoF,EAAE,MAAK;MAC5F,MAAMuB,kBAAkB,GAA+B;QACrDC,YAAY,EAAE,OAAO;QACrBC,SAAS,EAAE,CAAC;QACZC,aAAa,EAAE,KAAK;QACpBC,gBAAgB,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;QAC1CC,GAAG,EAAE,GAAG;QACRC,WAAW,EAAE,CAAC;QACdC,SAAS,EAAE;UAAE/E,EAAE,EAAE,CAAC;UAAEmB,IAAI,EAAE;QAAiB;OAC5C;MAED,MAAM6D,sBAAsB,GAAmC;QAC7DC,QAAQ,EAAE,KAAK;QACfC,YAAY,EAAE;OACf;MAED,MAAMC,oBAAoB,GAAqB;QAC7CzD,kBAAkB,EAAE,SAAS;QAC7B0D,QAAQ,EAAErF,YAAY;QACtBsF,cAAc,EAAEf,kBAAkB;QAClCgB,kBAAkB,EAAEN;OACrB;MAED,MAAMO,IAAI,GAAG;QACX7D,kBAAkB,EAAE,SAAS;QAC7B0D,QAAQ,EAAErF,YAAY;QACtBuF,kBAAkB,EAAEN,sBAAsB;QAC1CK,cAAc,EAAEf;OACjB;MAED3E,cAAc,CAAC6F,cAAc,CAACC,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;MAEnDjG,OAAO,CAACyG,sBAAsB,CAACX,IAAI,CAAC,CAACnC,SAAS,CAAEU,MAAM,IAAI;QACxDd,MAAM,CAACc,MAAM,CAAC,CAACR,OAAO,CAAC6B,oBAAoB,CAAC;QAC5CnC,MAAM,CAACrD,cAAc,CAAC6F,cAAc,CAAC,CAACW,gBAAgB,EAAE;QACxDnD,MAAM,CAACnD,6BAA6B,CAAC8F,SAAS,CAAC,CAACa,GAAG,CAACL,gBAAgB,EAAE;QACtEnD,MAAM,CAACpD,8BAA8B,CAACwE,MAAM,CAAC,CAACoC,GAAG,CAACL,gBAAgB,EAAE;MACtE,CAAC,CAAC;MAEF,MAAM5C,GAAG,GAAG7D,QAAQ,CAAC8D,SAAS,CAAC,GAAG1D,MAAM,WAAW,CAAC;MACpDkD,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACY,IAAI,CAAC,CAACf,OAAO,CAACiC,IAAI,CAAC;MACtChC,GAAG,CAACK,KAAK,CAACuB,oBAAoB,CAAC;IACjC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF3F,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBuD,EAAE,CAAC,wBAAwB,EAAE,MAAK;MAChC,MAAMoB,mBAAmB,GAAsB;QAC7CjE,MAAM,EAAE;OACT;MAEDT,OAAO,CAACgH,MAAM,CAAC,CAAC,EAAEtC,mBAAmB,CAAC,CAACf,SAAS,CAAEU,MAAM,IAAI;QAC1Dd,MAAM,CAACc,MAAM,CAAC,CAACR,OAAO,CAAC;UAAE,GAAGvD,YAAY;UAAEG,MAAM,EAAE;QAAgB,CAAE,CAAC;MACvE,CAAC,CAAC;MAEF,MAAMqD,GAAG,GAAG7D,QAAQ,CAAC8D,SAAS,CAAC,GAAG1D,MAAM,IAAI,CAAC;MAC7CkD,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACY,IAAI,CAAC,CAACf,OAAO,CAACa,mBAAmB,CAAC;MACrDZ,GAAG,CAACK,KAAK,CAAC;QAAE,GAAG7D,YAAY;QAAEG,MAAM,EAAE;MAAgB,CAAE,CAAC;IAC1D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFV,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBuD,EAAE,CAAC,wBAAwB,EAAE,MAAK;MAChCtD,OAAO,CAACiH,MAAM,CAAC,CAAC,CAAC,CAACtD,SAAS,CAAEU,MAAM,IAAI;QACrCd,MAAM,CAACc,MAAM,CAAC,CAAC6C,WAAW,EAAE;MAC9B,CAAC,CAAC;MAEF,MAAMpD,GAAG,GAAG7D,QAAQ,CAAC8D,SAAS,CAAC,GAAG1D,MAAM,IAAI,CAAC;MAC7CkD,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,EAAE,CAAC;IACf,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFpE,QAAQ,CAAC,wBAAwB,EAAE,MAAK;IACtCuD,EAAE,CAAC,iCAAiC,EAAE,MAAK;MACzCtD,OAAO,CAACmH,sBAAsB,CAAC,GAAG,EAAE,CAAC,CAAC,CAACxD,SAAS,CAAEU,MAAM,IAAI;QAC1Dd,MAAM,CAACc,MAAM,CAAC,CAACH,IAAI,CAAC,IAAI,CAAC;MAC3B,CAAC,CAAC;MAEF,MAAMJ,GAAG,GAAG7D,QAAQ,CAAC8D,SAAS,CAAC,GAAG1D,MAAM,iBAAiB,CAAC;MAC1DkD,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFpE,QAAQ,CAAC,iBAAiB,EAAE,MAAK;IAC/BuD,EAAE,CAAC,iCAAiC,EAAE,MAAK;MACzC,MAAM8D,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC,WAAW,CAAC,EAAE;QACnCC,IAAI,EAAE;OACP,CAAC;MAEFtH,OAAO,CAACuH,eAAe,CAAC,IAAI,CAAC,CAAC5D,SAAS,CAAEU,MAAM,IAAI;QACjDd,MAAM,CAACc,MAAM,YAAYgD,IAAI,CAAC,CAACnD,IAAI,CAAC,IAAI,CAAC;MAC3C,CAAC,CAAC;MAEF,MAAMJ,GAAG,GAAG7D,QAAQ,CAAC8D,SAAS,CAAC,GAAG1D,MAAM,cAAc,CAAC;MACvDkD,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACwD,YAAY,CAAC,CAACtD,IAAI,CAAC,MAAM,CAAC;MAC7CJ,GAAG,CAACK,KAAK,CAACiD,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}