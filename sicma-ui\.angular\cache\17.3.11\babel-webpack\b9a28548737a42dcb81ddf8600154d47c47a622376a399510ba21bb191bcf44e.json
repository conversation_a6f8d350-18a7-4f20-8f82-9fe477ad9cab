{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { AuthStatus } from '@core/auth/enums/auth-status.enum';\nimport { environment } from '@env';\nimport { AuthService } from './auth.service';\nimport { UserService } from './user.service';\ndescribe('AuthService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/users`;\n  const mockUserProfile = {\n    profile_id: 1,\n    profile_name: 'admin'\n  };\n  const mockUser = {\n    id: 1,\n    username: 'testuser',\n    profiles: [mockUserProfile]\n  };\n  const mockAuthResponse = {\n    token: 'mock-jwt-token',\n    user: mockUser\n  };\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [AuthService, UserService]\n    });\n    service = TestBed.inject(AuthService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n    localStorage.clear();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('login', () => {\n    it('should authenticate user and store token', () => {\n      const token = 'test-token';\n      service.login(token).subscribe(success => {\n        expect(success).toBeTrue();\n        expect(localStorage.getItem('token')).toBe(mockAuthResponse.token);\n        expect(localStorage.getItem('user')).toBe(JSON.stringify(mockAuthResponse.user));\n        expect(localStorage.getItem('status')).toBe(AuthStatus.authenticated);\n        expect(localStorage.getItem('user_profiles')).toBe(JSON.stringify(mockUser.profiles));\n      });\n      const req = httpMock.expectOne(`${apiUrl}/authenticate`);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual({\n        token\n      });\n      req.flush(mockAuthResponse);\n    });\n    it('should handle login error', () => {\n      const token = 'invalid-token';\n      service.login(token).subscribe(success => {\n        expect(success).toBeFalse();\n        expect(localStorage.getItem('token')).toBeNull();\n        expect(localStorage.getItem('user')).toBeNull();\n        expect(localStorage.getItem('status')).toBeNull();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/authenticate`);\n      req.error(new ErrorEvent('Unauthorized'));\n    });\n  });\n  describe('getUserProfiles', () => {\n    it('should return user profiles from localStorage', () => {\n      localStorage.setItem('user_profiles', JSON.stringify([mockUserProfile]));\n      const profiles = service.getUserProfiles();\n      expect(profiles).toEqual([mockUserProfile]);\n    });\n    it('should return empty array when no profiles in localStorage', () => {\n      const profiles = service.getUserProfiles();\n      expect(profiles).toEqual([]);\n    });\n  });\n  describe('hasProfile', () => {\n    it('should return true when user has profile', () => {\n      localStorage.setItem('user_profiles', JSON.stringify([mockUserProfile]));\n      expect(service.hasProfile('admin')).toBeTrue();\n    });\n    it('should return false when user does not have profile', () => {\n      localStorage.setItem('user_profiles', JSON.stringify([mockUserProfile]));\n      expect(service.hasProfile('user')).toBeFalse();\n    });\n  });\n  describe('getCurrentUser', () => {\n    it('should return current user from localStorage', () => {\n      const mockStoredUser = {\n        id: 1,\n        username: 'testuser',\n        profiles: []\n      };\n      localStorage.setItem('user', JSON.stringify(mockStoredUser));\n      expect(service.getCurrentUser()).toEqual(mockStoredUser);\n    });\n    it('should return null when no user in localStorage', () => {\n      expect(service.getCurrentUser()).toBeNull();\n    });\n  });\n  describe('setCurrentUser', () => {\n    it('should store user in localStorage', () => {\n      const user = {\n        email: '<EMAIL>'\n      };\n      service.setCurrentUser(user);\n      expect(localStorage.getItem('user')).toBe(JSON.stringify(user));\n    });\n  });\n  describe('logout', () => {\n    it('should clear storage and update status', () => {\n      localStorage.setItem('token', 'test-token');\n      localStorage.setItem('user', JSON.stringify(mockUser));\n      localStorage.setItem('status', AuthStatus.authenticated);\n      service.logout();\n      expect(localStorage.getItem('token')).toBeNull();\n      expect(service.currentUser()).toBeNull();\n      expect(service.authStatus()).toBe(AuthStatus.notAuthenticated);\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "AuthStatus", "environment", "AuthService", "UserService", "describe", "service", "httpMock", "apiUrl", "mockUserProfile", "profile_id", "profile_name", "mockUser", "id", "username", "profiles", "mockAuthResponse", "token", "user", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "localStorage", "clear", "it", "expect", "toBeTruthy", "login", "subscribe", "success", "toBeTrue", "getItem", "toBe", "JSON", "stringify", "authenticated", "req", "expectOne", "request", "method", "body", "toEqual", "flush", "toBeFalse", "toBeNull", "error", "ErrorEvent", "setItem", "getUserProfiles", "hasProfile", "mockStoredUser", "getCurrentUser", "email", "setCurrentUser", "logout", "currentUser", "authStatus", "notAuthenticated"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\core\\auth\\services\\auth.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { AuthStatus } from '@core/auth/enums/auth-status.enum';\nimport { User } from '@core/auth/models/user.model';\nimport { UserProfile } from '@core/auth/models/user_profile.model';\nimport { environment } from '@env';\nimport { AuthService } from './auth.service';\nimport { UserService } from './user.service';\n\ndescribe('AuthService', () => {\n  let service: AuthService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/users`;\n\n  const mockUserProfile: UserProfile = {\n    profile_id: 1,\n    profile_name: 'admin',\n  };\n\n  const mockUser: User = {\n    id: 1,\n    username: 'testuser',\n    profiles: [mockUserProfile],\n  };\n\n  const mockAuthResponse = {\n    token: 'mock-jwt-token',\n    user: mockUser,\n  };\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [AuthService, UserService],\n    });\n    service = TestBed.inject(AuthService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n    localStorage.clear();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('login', () => {\n    it('should authenticate user and store token', () => {\n      const token = 'test-token';\n\n      service.login(token).subscribe((success) => {\n        expect(success).toBeTrue();\n        expect(localStorage.getItem('token')).toBe(mockAuthResponse.token);\n        expect(localStorage.getItem('user')).toBe(\n          JSON.stringify(mockAuthResponse.user),\n        );\n        expect(localStorage.getItem('status')).toBe(AuthStatus.authenticated);\n        expect(localStorage.getItem('user_profiles')).toBe(\n          JSON.stringify(mockUser.profiles),\n        );\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/authenticate`);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual({ token });\n      req.flush(mockAuthResponse);\n    });\n\n    it('should handle login error', () => {\n      const token = 'invalid-token';\n\n      service.login(token).subscribe((success) => {\n        expect(success).toBeFalse();\n        expect(localStorage.getItem('token')).toBeNull();\n        expect(localStorage.getItem('user')).toBeNull();\n        expect(localStorage.getItem('status')).toBeNull();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/authenticate`);\n      req.error(new ErrorEvent('Unauthorized'));\n    });\n  });\n\n  describe('getUserProfiles', () => {\n    it('should return user profiles from localStorage', () => {\n      localStorage.setItem('user_profiles', JSON.stringify([mockUserProfile]));\n      const profiles = service.getUserProfiles();\n      expect(profiles).toEqual([mockUserProfile]);\n    });\n\n    it('should return empty array when no profiles in localStorage', () => {\n      const profiles = service.getUserProfiles();\n      expect(profiles).toEqual([]);\n    });\n  });\n\n  describe('hasProfile', () => {\n    it('should return true when user has profile', () => {\n      localStorage.setItem('user_profiles', JSON.stringify([mockUserProfile]));\n      expect(service.hasProfile('admin')).toBeTrue();\n    });\n\n    it('should return false when user does not have profile', () => {\n      localStorage.setItem('user_profiles', JSON.stringify([mockUserProfile]));\n      expect(service.hasProfile('user')).toBeFalse();\n    });\n  });\n\n  describe('getCurrentUser', () => {\n    it('should return current user from localStorage', () => {\n      const mockStoredUser = {\n        id: 1,\n        username: 'testuser',\n        profiles: [],\n      };\n      localStorage.setItem('user', JSON.stringify(mockStoredUser));\n      expect(service.getCurrentUser()).toEqual(mockStoredUser);\n    });\n\n    it('should return null when no user in localStorage', () => {\n      expect(service.getCurrentUser()).toBeNull();\n    });\n  });\n\n  describe('setCurrentUser', () => {\n    it('should store user in localStorage', () => {\n      const user = { email: '<EMAIL>' };\n      service.setCurrentUser(user);\n      expect(localStorage.getItem('user')).toBe(JSON.stringify(user));\n    });\n  });\n\n  describe('logout', () => {\n    it('should clear storage and update status', () => {\n      localStorage.setItem('token', 'test-token');\n      localStorage.setItem('user', JSON.stringify(mockUser));\n      localStorage.setItem('status', AuthStatus.authenticated);\n\n      service.logout();\n\n      expect(localStorage.getItem('token')).toBeNull();\n      expect(service.currentUser()).toBeNull();\n      expect(service.authStatus()).toBe(AuthStatus.notAuthenticated);\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,UAAU,QAAQ,mCAAmC;AAG9D,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,gBAAgB;AAE5CC,QAAQ,CAAC,aAAa,EAAE,MAAK;EAC3B,IAAIC,OAAoB;EACxB,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGN,WAAW,CAACM,MAAM,QAAQ;EAE5C,MAAMC,eAAe,GAAgB;IACnCC,UAAU,EAAE,CAAC;IACbC,YAAY,EAAE;GACf;EAED,MAAMC,QAAQ,GAAS;IACrBC,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,CAACN,eAAe;GAC3B;EAED,MAAMO,gBAAgB,GAAG;IACvBC,KAAK,EAAE,gBAAgB;IACvBC,IAAI,EAAEN;GACP;EAEDO,UAAU,CAAC,MAAK;IACdnB,OAAO,CAACoB,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACvB,uBAAuB,CAAC;MAClCwB,SAAS,EAAE,CAACnB,WAAW,EAAEC,WAAW;KACrC,CAAC;IACFE,OAAO,GAAGN,OAAO,CAACuB,MAAM,CAACpB,WAAW,CAAC;IACrCI,QAAQ,GAAGP,OAAO,CAACuB,MAAM,CAACxB,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFyB,SAAS,CAAC,MAAK;IACbjB,QAAQ,CAACkB,MAAM,EAAE;IACjBC,YAAY,CAACC,KAAK,EAAE;EACtB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACvB,OAAO,CAAC,CAACwB,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFzB,QAAQ,CAAC,OAAO,EAAE,MAAK;IACrBuB,EAAE,CAAC,0CAA0C,EAAE,MAAK;MAClD,MAAMX,KAAK,GAAG,YAAY;MAE1BX,OAAO,CAACyB,KAAK,CAACd,KAAK,CAAC,CAACe,SAAS,CAAEC,OAAO,IAAI;QACzCJ,MAAM,CAACI,OAAO,CAAC,CAACC,QAAQ,EAAE;QAC1BL,MAAM,CAACH,YAAY,CAACS,OAAO,CAAC,OAAO,CAAC,CAAC,CAACC,IAAI,CAACpB,gBAAgB,CAACC,KAAK,CAAC;QAClEY,MAAM,CAACH,YAAY,CAACS,OAAO,CAAC,MAAM,CAAC,CAAC,CAACC,IAAI,CACvCC,IAAI,CAACC,SAAS,CAACtB,gBAAgB,CAACE,IAAI,CAAC,CACtC;QACDW,MAAM,CAACH,YAAY,CAACS,OAAO,CAAC,QAAQ,CAAC,CAAC,CAACC,IAAI,CAACnC,UAAU,CAACsC,aAAa,CAAC;QACrEV,MAAM,CAACH,YAAY,CAACS,OAAO,CAAC,eAAe,CAAC,CAAC,CAACC,IAAI,CAChDC,IAAI,CAACC,SAAS,CAAC1B,QAAQ,CAACG,QAAQ,CAAC,CAClC;MACH,CAAC,CAAC;MAEF,MAAMyB,GAAG,GAAGjC,QAAQ,CAACkC,SAAS,CAAC,GAAGjC,MAAM,eAAe,CAAC;MACxDqB,MAAM,CAACW,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACP,IAAI,CAAC,MAAM,CAAC;MACvCP,MAAM,CAACW,GAAG,CAACE,OAAO,CAACE,IAAI,CAAC,CAACC,OAAO,CAAC;QAAE5B;MAAK,CAAE,CAAC;MAC3CuB,GAAG,CAACM,KAAK,CAAC9B,gBAAgB,CAAC;IAC7B,CAAC,CAAC;IAEFY,EAAE,CAAC,2BAA2B,EAAE,MAAK;MACnC,MAAMX,KAAK,GAAG,eAAe;MAE7BX,OAAO,CAACyB,KAAK,CAACd,KAAK,CAAC,CAACe,SAAS,CAAEC,OAAO,IAAI;QACzCJ,MAAM,CAACI,OAAO,CAAC,CAACc,SAAS,EAAE;QAC3BlB,MAAM,CAACH,YAAY,CAACS,OAAO,CAAC,OAAO,CAAC,CAAC,CAACa,QAAQ,EAAE;QAChDnB,MAAM,CAACH,YAAY,CAACS,OAAO,CAAC,MAAM,CAAC,CAAC,CAACa,QAAQ,EAAE;QAC/CnB,MAAM,CAACH,YAAY,CAACS,OAAO,CAAC,QAAQ,CAAC,CAAC,CAACa,QAAQ,EAAE;MACnD,CAAC,CAAC;MAEF,MAAMR,GAAG,GAAGjC,QAAQ,CAACkC,SAAS,CAAC,GAAGjC,MAAM,eAAe,CAAC;MACxDgC,GAAG,CAACS,KAAK,CAAC,IAAIC,UAAU,CAAC,cAAc,CAAC,CAAC;IAC3C,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7C,QAAQ,CAAC,iBAAiB,EAAE,MAAK;IAC/BuB,EAAE,CAAC,+CAA+C,EAAE,MAAK;MACvDF,YAAY,CAACyB,OAAO,CAAC,eAAe,EAAEd,IAAI,CAACC,SAAS,CAAC,CAAC7B,eAAe,CAAC,CAAC,CAAC;MACxE,MAAMM,QAAQ,GAAGT,OAAO,CAAC8C,eAAe,EAAE;MAC1CvB,MAAM,CAACd,QAAQ,CAAC,CAAC8B,OAAO,CAAC,CAACpC,eAAe,CAAC,CAAC;IAC7C,CAAC,CAAC;IAEFmB,EAAE,CAAC,4DAA4D,EAAE,MAAK;MACpE,MAAMb,QAAQ,GAAGT,OAAO,CAAC8C,eAAe,EAAE;MAC1CvB,MAAM,CAACd,QAAQ,CAAC,CAAC8B,OAAO,CAAC,EAAE,CAAC;IAC9B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFxC,QAAQ,CAAC,YAAY,EAAE,MAAK;IAC1BuB,EAAE,CAAC,0CAA0C,EAAE,MAAK;MAClDF,YAAY,CAACyB,OAAO,CAAC,eAAe,EAAEd,IAAI,CAACC,SAAS,CAAC,CAAC7B,eAAe,CAAC,CAAC,CAAC;MACxEoB,MAAM,CAACvB,OAAO,CAAC+C,UAAU,CAAC,OAAO,CAAC,CAAC,CAACnB,QAAQ,EAAE;IAChD,CAAC,CAAC;IAEFN,EAAE,CAAC,qDAAqD,EAAE,MAAK;MAC7DF,YAAY,CAACyB,OAAO,CAAC,eAAe,EAAEd,IAAI,CAACC,SAAS,CAAC,CAAC7B,eAAe,CAAC,CAAC,CAAC;MACxEoB,MAAM,CAACvB,OAAO,CAAC+C,UAAU,CAAC,MAAM,CAAC,CAAC,CAACN,SAAS,EAAE;IAChD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF1C,QAAQ,CAAC,gBAAgB,EAAE,MAAK;IAC9BuB,EAAE,CAAC,8CAA8C,EAAE,MAAK;MACtD,MAAM0B,cAAc,GAAG;QACrBzC,EAAE,EAAE,CAAC;QACLC,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE;OACX;MACDW,YAAY,CAACyB,OAAO,CAAC,MAAM,EAAEd,IAAI,CAACC,SAAS,CAACgB,cAAc,CAAC,CAAC;MAC5DzB,MAAM,CAACvB,OAAO,CAACiD,cAAc,EAAE,CAAC,CAACV,OAAO,CAACS,cAAc,CAAC;IAC1D,CAAC,CAAC;IAEF1B,EAAE,CAAC,iDAAiD,EAAE,MAAK;MACzDC,MAAM,CAACvB,OAAO,CAACiD,cAAc,EAAE,CAAC,CAACP,QAAQ,EAAE;IAC7C,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF3C,QAAQ,CAAC,gBAAgB,EAAE,MAAK;IAC9BuB,EAAE,CAAC,mCAAmC,EAAE,MAAK;MAC3C,MAAMV,IAAI,GAAG;QAAEsC,KAAK,EAAE;MAAkB,CAAE;MAC1ClD,OAAO,CAACmD,cAAc,CAACvC,IAAI,CAAC;MAC5BW,MAAM,CAACH,YAAY,CAACS,OAAO,CAAC,MAAM,CAAC,CAAC,CAACC,IAAI,CAACC,IAAI,CAACC,SAAS,CAACpB,IAAI,CAAC,CAAC;IACjE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFb,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBuB,EAAE,CAAC,wCAAwC,EAAE,MAAK;MAChDF,YAAY,CAACyB,OAAO,CAAC,OAAO,EAAE,YAAY,CAAC;MAC3CzB,YAAY,CAACyB,OAAO,CAAC,MAAM,EAAEd,IAAI,CAACC,SAAS,CAAC1B,QAAQ,CAAC,CAAC;MACtDc,YAAY,CAACyB,OAAO,CAAC,QAAQ,EAAElD,UAAU,CAACsC,aAAa,CAAC;MAExDjC,OAAO,CAACoD,MAAM,EAAE;MAEhB7B,MAAM,CAACH,YAAY,CAACS,OAAO,CAAC,OAAO,CAAC,CAAC,CAACa,QAAQ,EAAE;MAChDnB,MAAM,CAACvB,OAAO,CAACqD,WAAW,EAAE,CAAC,CAACX,QAAQ,EAAE;MACxCnB,MAAM,CAACvB,OAAO,CAACsD,UAAU,EAAE,CAAC,CAACxB,IAAI,CAACnC,UAAU,CAAC4D,gBAAgB,CAAC;IAChE,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}