import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { ReductionsListComponent } from './reductions-list.component';

describe('ReductionsListComponent', () => {
  let component: ReductionsListComponent;
  let fixture: ComponentFixture<ReductionsListComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReductionsListComponent, NoopAnimationsModule],
    }).compileComponents();

    fixture = TestBed.createComponent(ReductionsListComponent);
    component = fixture.componentInstance;

    // Set required input
    component.contract = {
      id: 1,
      contractNumber: 123,
      monthlyPayment: 1000000,
      object: 'Test Contract',
      rup: true,
      secopCode: 123456,
      addition: false,
      cession: false,
      settled: false,
      contractTypeId: 1,
      statusId: 1,
      causesSelectionId: 1,
      managementSupportId: 1,
      contractClassId: 1,
      reduction: [],
    };

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
