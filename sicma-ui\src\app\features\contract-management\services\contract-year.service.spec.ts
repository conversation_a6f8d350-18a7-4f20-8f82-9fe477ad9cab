import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { ContractYear } from '@contract-management/models/contract-year.model';
import { environment } from '@env';
import { ContractYearService } from './contract-year.service';

describe('ContractYearService', () => {
  let service: ContractYearService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/contract-year`;

  const mockContractYear: ContractYear = {
    id: 1,
    year: 2024,
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ContractYearService],
    });
    service = TestBed.inject(ContractYearService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all contract years', () => {
      const mockContractYears = [mockContractYear];

      service.getAll().subscribe((contractYears) => {
        expect(contractYears).toEqual(mockContractYears);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockContractYears);
    });
  });

  describe('getById', () => {
    it('should return a contract year by id', () => {
      const id = 1;

      service.getById(id).subscribe((contractYear) => {
        expect(contractYear).toEqual(mockContractYear);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockContractYear);
    });
  });

  describe('create', () => {
    it('should create a new contract year', () => {
      const newContractYear: Omit<ContractYear, 'id'> = {
        year: 2024,
      };

      service.create(newContractYear).subscribe((contractYear) => {
        expect(contractYear).toEqual(mockContractYear);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newContractYear);
      req.flush(mockContractYear);
    });
  });

  describe('update', () => {
    it('should update a contract year', () => {
      const id = 1;
      const updateData: Partial<ContractYear> = {
        year: 2025,
      };

      service.update(id, updateData).subscribe((contractYear) => {
        expect(contractYear).toEqual(mockContractYear);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockContractYear);
    });
  });

  describe('delete', () => {
    it('should delete a contract year', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });

  describe('getByYear', () => {
    it('should return a contract year by year', () => {
      const year = 2024;

      service.getByYear(year).subscribe((contractYear) => {
        expect(contractYear).toEqual(mockContractYear);
      });

      const req = httpMock.expectOne(`${apiUrl}/year/${year}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockContractYear);
    });
  });
});