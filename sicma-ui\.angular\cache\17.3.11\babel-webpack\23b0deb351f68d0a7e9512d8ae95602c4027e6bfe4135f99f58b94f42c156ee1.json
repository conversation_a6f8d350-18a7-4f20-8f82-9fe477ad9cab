{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { TestBed, fakeAsync, tick, flush, discardPeriodicTasks } from '@angular/core/testing';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { provideNativeDateAdapter } from '@angular/material/core';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { CdpEntityService } from '@contract-management/services/cdp-entity.service';\nimport { ContractValuesService } from '@contract-management/services/contract-values.service';\nimport { EntityService } from '@contract-management/services/entity.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { of, throwError } from 'rxjs';\nimport { ContractValuesFormComponent } from './contract-values-form.component';\nimport { NO_ERRORS_SCHEMA, Directive, Input } from '@angular/core';\nimport { NgxCurrencyDirective } from 'ngx-currency';\nlet MockCurrencyDirective = class MockCurrencyDirective {\n  constructor() {\n    this.currencyMask = {};\n    this.onChange = _value => {};\n    this.onTouched = () => {};\n  }\n  writeValue(value) {}\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {}\n  static {\n    this.propDecorators = {\n      currencyMask: [{\n        type: Input\n      }]\n    };\n  }\n};\nMockCurrencyDirective = __decorate([Directive({\n  selector: '[appCurrencyMask]',\n  standalone: true\n})], MockCurrencyDirective);\nexport { MockCurrencyDirective };\ndescribe('ContractValuesFormComponent', () => {\n  let component;\n  let fixture;\n  let entityServiceSpy;\n  let cdpEntityServiceSpy;\n  let contractValuesServiceSpy;\n  let alertServiceSpy;\n  const mockEntities = [{\n    id: 1,\n    name: 'Entity 1'\n  }, {\n    id: 2,\n    name: 'Entity 2'\n  }];\n  const mockCdpEntities = [{\n    id: 1,\n    name: 'CDP Entity 1'\n  }, {\n    id: 2,\n    name: 'CDP Entity 2'\n  }];\n  const mockContract = {\n    id: 1,\n    contractNumber: 123,\n    monthlyPayment: 1000,\n    object: 'Test Contract',\n    rup: true,\n    secopCode: 456,\n    addition: false,\n    cession: false,\n    settled: false,\n    causesSelectionId: 1,\n    managementSupportId: 1,\n    contractClassId: 1\n  };\n  const mockContractValue = {\n    id: 1,\n    numericValue: 5000,\n    madsValue: 3000,\n    otherValue: 2000,\n    futureValidityValue: 0,\n    startDate: '2023-01-01',\n    endDate: '2023-12-31',\n    subscriptionDate: '2023-01-01',\n    cdp: 789,\n    cdpEntityId: 1,\n    isOtherEntity: false,\n    cdpEntity: mockCdpEntities[0],\n    contractId: 1\n  };\n  beforeEach(() => {\n    entityServiceSpy = jasmine.createSpyObj('EntityService', ['getAll']);\n    cdpEntityServiceSpy = jasmine.createSpyObj('CdpEntityService', ['getAll']);\n    contractValuesServiceSpy = jasmine.createSpyObj('ContractValuesService', ['getLatestEndDateByContractId']);\n    alertServiceSpy = jasmine.createSpyObj('AlertService', ['error']);\n    entityServiceSpy.getAll.and.returnValue(of(mockEntities));\n    cdpEntityServiceSpy.getAll.and.returnValue(of(mockCdpEntities));\n    contractValuesServiceSpy.getLatestEndDateByContractId.and.returnValue(of(null));\n    TestBed.configureTestingModule({\n      imports: [ContractValuesFormComponent, HttpClientTestingModule, BrowserAnimationsModule, ReactiveFormsModule, FormsModule, MatDatepickerModule, MatFormFieldModule, MatIconModule, MatInputModule, MatSelectModule, MatSlideToggleModule],\n      providers: [provideNativeDateAdapter(), {\n        provide: EntityService,\n        useValue: entityServiceSpy\n      }, {\n        provide: CdpEntityService,\n        useValue: cdpEntityServiceSpy\n      }, {\n        provide: ContractValuesService,\n        useValue: contractValuesServiceSpy\n      }, {\n        provide: AlertService,\n        useValue: alertServiceSpy\n      }],\n      schemas: [NO_ERRORS_SCHEMA]\n    }).overrideDirective(NgxCurrencyDirective, {\n      set: {\n        providers: []\n      }\n    }).compileComponents();\n    fixture = TestBed.createComponent(ContractValuesFormComponent);\n    component = fixture.componentInstance;\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  describe('Form initialization', () => {\n    it('should initialize form with default values', fakeAsync(() => {\n      component.contract = mockContract;\n      fixture.detectChanges();\n      tick();\n      flush();\n      discardPeriodicTasks();\n      component.valuesFormGroup.patchValue({\n        numericValue: 0,\n        madsValue: 0,\n        otherValue: 0,\n        futureValidityValue: 0\n      }, {\n        emitEvent: false\n      });\n      expect(component.valuesFormGroup).toBeDefined();\n      expect(component.valuesFormGroup.get('numericValue')?.value).toBe(0);\n      expect(component.valuesFormGroup.get('madsValue')?.value).toBe(0);\n      expect(component.valuesFormGroup.get('otherValue')?.value).toBe(0);\n      expect(component.valuesFormGroup.get('futureValidityValue')?.value).toBe(0);\n      expect(component.valuesFormGroup.get('cdp')?.value).toBe('');\n      expect(component.valuesFormGroup.get('startDate')?.value).toBeNull();\n    }));\n    it('should load initial data on ngOnInit', fakeAsync(() => {\n      component.contract = mockContract;\n      fixture.detectChanges();\n      tick();\n      flush();\n      discardPeriodicTasks();\n      expect(entityServiceSpy.getAll).toHaveBeenCalled();\n      expect(cdpEntityServiceSpy.getAll).toHaveBeenCalled();\n      expect(contractValuesServiceSpy.getLatestEndDateByContractId).toHaveBeenCalledWith(mockContract.id);\n      expect(component.entities).toEqual(mockEntities);\n      expect(component.cdpEntities).toEqual(mockCdpEntities);\n    }));\n    it('should show alert on error loading initial data', fakeAsync(() => {\n      spyOn(console, 'error').and.callFake(() => {});\n      entityServiceSpy.getAll.and.returnValue(throwError(() => new Error('Test error')));\n      component.contract = mockContract;\n      fixture.detectChanges();\n      tick();\n      flush();\n      discardPeriodicTasks();\n      expect(alertServiceSpy.error).toHaveBeenCalledWith('Error al cargar los datos iniciales');\n    }));\n    it('should patch form with contract value when provided', fakeAsync(() => {\n      component.contract = mockContract;\n      component.contractValue = mockContractValue;\n      fixture.detectChanges();\n      tick();\n      flush();\n      discardPeriodicTasks();\n      expect(component.valuesFormGroup.get('numericValue')?.value).toBe(mockContractValue.numericValue);\n      expect(component.valuesFormGroup.get('madsValue')?.value).toBe(mockContractValue.madsValue);\n      expect(component.valuesFormGroup.get('otherValue')?.value).toBe(mockContractValue.otherValue);\n      expect(component.valuesFormGroup.get('cdp')?.value).toBe(mockContractValue.cdp);\n      expect(component.valuesFormGroup.get('cdpEntityId')?.value).toBe(mockContractValue.cdpEntityId);\n    }));\n  });\n  describe('Form validation', () => {\n    beforeEach(fakeAsync(() => {\n      component.contract = mockContract;\n      fixture.detectChanges();\n      tick();\n      flush();\n      discardPeriodicTasks();\n    }));\n    it('should validate required fields', () => {\n      const startDateControl = component.valuesFormGroup.get('startDate');\n      const subscriptionDateControl = component.valuesFormGroup.get('subscriptionDate');\n      const cdpControl = component.valuesFormGroup.get('cdp');\n      const cdpEntityIdControl = component.valuesFormGroup.get('cdpEntityId');\n      expect(startDateControl?.hasError('required')).toBeTruthy();\n      expect(subscriptionDateControl?.hasError('required')).toBeTruthy();\n      expect(cdpControl?.hasError('required')).toBeTruthy();\n      expect(cdpEntityIdControl?.hasError('required')).toBeTruthy();\n    });\n    it('should validate minimum value for numeric fields', () => {\n      component.valuesFormGroup.get('numericValue')?.setValue(-1);\n      component.valuesFormGroup.get('madsValue')?.setValue(-1);\n      component.valuesFormGroup.get('otherValue')?.setValue(-1);\n      component.valuesFormGroup.get('futureValidityValue')?.setValue(-1);\n      expect(component.valuesFormGroup.get('numericValue')?.hasError('min')).toBeTruthy();\n      expect(component.valuesFormGroup.get('madsValue')?.hasError('min')).toBeTruthy();\n      expect(component.valuesFormGroup.get('otherValue')?.hasError('min')).toBeTruthy();\n      expect(component.valuesFormGroup.get('futureValidityValue')?.hasError('min')).toBeTruthy();\n    });\n  });\n  describe('Form functionality', () => {\n    beforeEach(fakeAsync(() => {\n      component.contract = mockContract;\n      fixture.detectChanges();\n      tick();\n      flush();\n      discardPeriodicTasks();\n    }));\n    it('should calculate numeric value from other value inputs', () => {\n      component.valuesFormGroup.get('madsValue')?.setValue(100);\n      component.valuesFormGroup.get('otherValue')?.setValue(200);\n      component.valuesFormGroup.get('futureValidityValue')?.setValue(300);\n      fixture.detectChanges();\n      expect(component.valuesFormGroup.get('numericValue')?.value).toBe(600);\n    });\n    it('should enable entityId field when otherValue is greater than 0', () => {\n      const isOtherEntityControl = component.valuesFormGroup.get('isOtherEntity');\n      const entityIdControl = component.valuesFormGroup.get('entityId');\n      component.valuesFormGroup.get('otherValue')?.setValue(500);\n      component.onOtherValueChange();\n      fixture.detectChanges();\n      expect(isOtherEntityControl?.value).toBe(true);\n      expect(entityIdControl?.disabled).toBe(false);\n    });\n    it('should disable entityId field when otherValue is 0', () => {\n      const isOtherEntityControl = component.valuesFormGroup.get('isOtherEntity');\n      const entityIdControl = component.valuesFormGroup.get('entityId');\n      component.valuesFormGroup.get('otherValue')?.setValue(0);\n      component.onOtherValueChange();\n      fixture.detectChanges();\n      expect(isOtherEntityControl?.value).toBe(false);\n      expect(entityIdControl?.disabled).toBe(true);\n    });\n    it('should update contract duration when dates change', () => {\n      const startDate = new Date(2023, 0, 1);\n      const endDate = new Date(2023, 2, 31);\n      component.valuesFormGroup.get('startDate')?.setValue(startDate);\n      component.valuesFormGroup.get('endDate')?.setValue(endDate);\n      fixture.detectChanges();\n      component['validateDates']();\n      expect(component.contractDuration.days).toBeGreaterThan(0);\n      expect(component.contractDuration.months).toBeGreaterThan(0);\n    });\n  });\n  describe('Public methods', () => {\n    beforeEach(fakeAsync(() => {\n      component.contract = mockContract;\n      fixture.detectChanges();\n      tick();\n      flush();\n      discardPeriodicTasks();\n    }));\n    it('should check if form is valid', () => {\n      expect(component.isValid()).toBeFalse();\n      component.valuesFormGroup.get('startDate')?.setValue(new Date());\n      component.valuesFormGroup.get('subscriptionDate')?.setValue(new Date());\n      component.valuesFormGroup.get('cdp')?.setValue('123');\n      component.valuesFormGroup.get('cdpEntityId')?.setValue(1);\n      component.valuesFormGroup.get('madsValue')?.setValue(1000);\n      expect(component.isValid()).toBeTrue();\n    });\n    it('should get contract values from form', () => {\n      const testDate = new Date(2023, 0, 1);\n      component.valuesFormGroup.patchValue({\n        numericValue: 5000,\n        madsValue: 3000,\n        otherValue: 2000,\n        futureValidityValue: 0,\n        startDate: testDate,\n        endDate: testDate,\n        subscriptionDate: testDate,\n        cdp: 789,\n        cdpEntityId: 1,\n        isOtherEntity: false\n      });\n      const result = component.getContractValuesFromForm();\n      expect(result.numericValue).toBe(5000);\n      expect(result.madsValue).toBe(3000);\n      expect(result.otherValue).toBe(2000);\n      expect(result.cdp).toBe(789);\n      expect(result.cdpEntityId).toBe(1);\n      expect(result.contractId).toBe(mockContract.id);\n    });\n    it('should emit form values when form is valid', fakeAsync(() => {\n      spyOn(component.valuesSubmitted, 'emit');\n      component.valuesFormGroup.get('startDate')?.setValue(new Date());\n      component.valuesFormGroup.get('subscriptionDate')?.setValue(new Date());\n      component.valuesFormGroup.get('cdp')?.setValue('123');\n      component.valuesFormGroup.get('cdpEntityId')?.setValue(1);\n      component.valuesFormGroup.get('madsValue')?.setValue(1000);\n      component.valuesFormGroup.updateValueAndValidity();\n      tick(300);\n      flush();\n      discardPeriodicTasks();\n      expect(component.valuesSubmitted.emit).toHaveBeenCalled();\n    }));\n    it('should emit null when form is invalid', fakeAsync(() => {\n      spyOn(component.valuesSubmitted, 'emit');\n      component.valuesFormGroup.updateValueAndValidity();\n      tick(300);\n      flush();\n      discardPeriodicTasks();\n      expect(component.valuesSubmitted.emit).toHaveBeenCalledWith(null);\n    }));\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "TestBed", "fakeAsync", "tick", "flush", "discardPeriodicTasks", "ReactiveFormsModule", "FormsModule", "MatDatepickerModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatSelectModule", "MatSlideToggleModule", "provideNativeDateAdapter", "BrowserAnimationsModule", "CdpEntityService", "ContractValuesService", "EntityService", "AlertService", "of", "throwError", "ContractValuesFormComponent", "NO_ERRORS_SCHEMA", "Directive", "Input", "NgxCurrencyDirective", "MockCurrencyDirective", "constructor", "currencyMask", "onChange", "_value", "onTouched", "writeValue", "value", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "__decorate", "selector", "standalone", "describe", "component", "fixture", "entityServiceSpy", "cdpEntityServiceSpy", "contractValuesServiceSpy", "alertServiceSpy", "mockEntities", "id", "name", "mockCdpEntities", "mockContract", "contractNumber", "monthlyPayment", "object", "rup", "secopCode", "addition", "cession", "settled", "causesSelectionId", "managementSupportId", "contractClassId", "mockContractValue", "numericValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "otherValue", "futureValidityValue", "startDate", "endDate", "subscriptionDate", "cdp", "cdpEntityId", "isOtherEntity", "cdpEntity", "contractId", "beforeEach", "jasmine", "createSpyObj", "getAll", "and", "returnValue", "getLatestEndDateByContractId", "configureTestingModule", "imports", "providers", "provide", "useValue", "schemas", "overrideDirective", "set", "compileComponents", "createComponent", "componentInstance", "it", "expect", "toBeTruthy", "contract", "detectChanges", "valuesFormGroup", "patchValue", "emitEvent", "toBeDefined", "get", "toBe", "toBeNull", "toHaveBeenCalled", "toHaveBeenCalledWith", "entities", "toEqual", "cdpEntities", "spyOn", "console", "callFake", "Error", "error", "contractValue", "startDateControl", "subscriptionDateControl", "cdpControl", "cdpEntityIdControl", "<PERSON><PERSON><PERSON><PERSON>", "setValue", "isOtherEntityControl", "entityIdControl", "onOtherValueChange", "disabled", "Date", "contractDuration", "days", "toBeGreaterThan", "months", "<PERSON><PERSON><PERSON><PERSON>", "toBeFalse", "toBeTrue", "testDate", "result", "getContractValuesFromForm", "valuesSubmitted", "updateValueAndValidity", "emit"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\components\\contract-values-form\\contract-values-form.component.spec.ts"], "sourcesContent": ["import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport {\n  ComponentFixture,\n  TestBed,\n  fakeAsync,\n  tick,\n  flush,\n  discardPeriodicTasks,\n} from '@angular/core/testing';\nimport {\n  ReactiveFormsModule,\n  FormsModule,\n  ControlValueAccessor,\n} from '@angular/forms';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { provideNativeDateAdapter } from '@angular/material/core';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { CDPEntity } from '@contract-management/models/cdp-entity.model';\nimport { ContractValues } from '@contract-management/models/contract-values.model';\nimport { Contract } from '@contract-management/models/contract.model';\nimport { Entity } from '@contract-management/models/entity.model';\nimport { CdpEntityService } from '@contract-management/services/cdp-entity.service';\nimport { ContractValuesService } from '@contract-management/services/contract-values.service';\nimport { EntityService } from '@contract-management/services/entity.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { of, throwError } from 'rxjs';\nimport { ContractValuesFormComponent } from './contract-values-form.component';\nimport { NO_ERRORS_SCHEMA, Directive, Input } from '@angular/core';\nimport { NgxCurrencyDirective } from 'ngx-currency';\n\n@Directive({\n  selector: '[appCurrencyMask]',\n  standalone: true,\n})\nexport class MockCurrencyDirective implements ControlValueAccessor {\n  @Input() currencyMask: unknown = {};\n\n  onChange = (_value: unknown): void => {};\n\n  onTouched = (): void => {};\n\n  writeValue(value: unknown): void {}\n\n  registerOnChange(fn: (_: unknown) => void): void {\n    this.onChange = fn;\n  }\n\n  registerOnTouched(fn: () => void): void {\n    this.onTouched = fn;\n  }\n\n  setDisabledState(isDisabled: boolean): void {}\n}\n\ndescribe('ContractValuesFormComponent', () => {\n  let component: ContractValuesFormComponent;\n  let fixture: ComponentFixture<ContractValuesFormComponent>;\n  let entityServiceSpy: jasmine.SpyObj<EntityService>;\n  let cdpEntityServiceSpy: jasmine.SpyObj<CdpEntityService>;\n  let contractValuesServiceSpy: jasmine.SpyObj<ContractValuesService>;\n  let alertServiceSpy: jasmine.SpyObj<AlertService>;\n\n  const mockEntities: Entity[] = [\n    { id: 1, name: 'Entity 1' },\n    { id: 2, name: 'Entity 2' },\n  ];\n\n  const mockCdpEntities: CDPEntity[] = [\n    { id: 1, name: 'CDP Entity 1' },\n    { id: 2, name: 'CDP Entity 2' },\n  ];\n\n  const mockContract: Contract = {\n    id: 1,\n    contractNumber: 123,\n    monthlyPayment: 1000,\n    object: 'Test Contract',\n    rup: true,\n    secopCode: 456,\n    addition: false,\n    cession: false,\n    settled: false,\n    causesSelectionId: 1,\n    managementSupportId: 1,\n    contractClassId: 1,\n  };\n\n  const mockContractValue: ContractValues = {\n    id: 1,\n    numericValue: 5000,\n    madsValue: 3000,\n    otherValue: 2000,\n    futureValidityValue: 0,\n    startDate: '2023-01-01',\n    endDate: '2023-12-31',\n    subscriptionDate: '2023-01-01',\n    cdp: 789,\n    cdpEntityId: 1,\n    isOtherEntity: false,\n    cdpEntity: mockCdpEntities[0],\n    contractId: 1,\n  };\n\n  beforeEach(() => {\n    entityServiceSpy = jasmine.createSpyObj<EntityService>('EntityService', [\n      'getAll',\n    ]);\n    cdpEntityServiceSpy = jasmine.createSpyObj<CdpEntityService>(\n      'CdpEntityService',\n      ['getAll'],\n    );\n    contractValuesServiceSpy = jasmine.createSpyObj<ContractValuesService>(\n      'ContractValuesService',\n      ['getLatestEndDateByContractId'],\n    );\n    alertServiceSpy = jasmine.createSpyObj<AlertService>('AlertService', [\n      'error',\n    ]);\n\n    entityServiceSpy.getAll.and.returnValue(of(mockEntities));\n    cdpEntityServiceSpy.getAll.and.returnValue(of(mockCdpEntities));\n    contractValuesServiceSpy.getLatestEndDateByContractId.and.returnValue(\n      of(null),\n    );\n\n    TestBed.configureTestingModule({\n      imports: [\n        ContractValuesFormComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n        ReactiveFormsModule,\n        FormsModule,\n        MatDatepickerModule,\n        MatFormFieldModule,\n        MatIconModule,\n        MatInputModule,\n        MatSelectModule,\n        MatSlideToggleModule,\n      ],\n      providers: [\n        provideNativeDateAdapter(),\n        { provide: EntityService, useValue: entityServiceSpy },\n        { provide: CdpEntityService, useValue: cdpEntityServiceSpy },\n        { provide: ContractValuesService, useValue: contractValuesServiceSpy },\n        { provide: AlertService, useValue: alertServiceSpy },\n      ],\n      schemas: [NO_ERRORS_SCHEMA],\n    })\n      .overrideDirective(NgxCurrencyDirective, {\n        set: {\n          providers: [],\n        },\n      })\n      .compileComponents();\n\n    fixture = TestBed.createComponent(ContractValuesFormComponent);\n    component = fixture.componentInstance;\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  describe('Form initialization', () => {\n    it('should initialize form with default values', fakeAsync(() => {\n      component.contract = mockContract;\n      fixture.detectChanges();\n      tick();\n      flush();\n      discardPeriodicTasks();\n\n      component.valuesFormGroup.patchValue(\n        {\n          numericValue: 0,\n          madsValue: 0,\n          otherValue: 0,\n          futureValidityValue: 0,\n        },\n        { emitEvent: false },\n      );\n\n      expect(component.valuesFormGroup).toBeDefined();\n\n      expect(component.valuesFormGroup.get('numericValue')?.value).toBe(0);\n      expect(component.valuesFormGroup.get('madsValue')?.value).toBe(0);\n      expect(component.valuesFormGroup.get('otherValue')?.value).toBe(0);\n      expect(component.valuesFormGroup.get('futureValidityValue')?.value).toBe(\n        0,\n      );\n      expect(component.valuesFormGroup.get('cdp')?.value).toBe('');\n      expect(component.valuesFormGroup.get('startDate')?.value).toBeNull();\n    }));\n\n    it('should load initial data on ngOnInit', fakeAsync(() => {\n      component.contract = mockContract;\n      fixture.detectChanges();\n      tick();\n      flush();\n      discardPeriodicTasks();\n\n      expect(entityServiceSpy.getAll).toHaveBeenCalled();\n      expect(cdpEntityServiceSpy.getAll).toHaveBeenCalled();\n      expect(\n        contractValuesServiceSpy.getLatestEndDateByContractId,\n      ).toHaveBeenCalledWith(mockContract.id);\n      expect(component.entities).toEqual(mockEntities);\n      expect(component.cdpEntities).toEqual(mockCdpEntities);\n    }));\n\n    it('should show alert on error loading initial data', fakeAsync(() => {\n\n      spyOn(console, 'error').and.callFake(() => {});\n\n      entityServiceSpy.getAll.and.returnValue(\n        throwError(() => new Error('Test error')),\n      );\n      component.contract = mockContract;\n      fixture.detectChanges();\n      tick();\n      flush();\n      discardPeriodicTasks();\n\n      expect(alertServiceSpy.error).toHaveBeenCalledWith(\n        'Error al cargar los datos iniciales',\n      );\n    }));\n\n    it('should patch form with contract value when provided', fakeAsync(() => {\n      component.contract = mockContract;\n      component.contractValue = mockContractValue;\n      fixture.detectChanges();\n      tick();\n      flush();\n      discardPeriodicTasks();\n\n      expect(component.valuesFormGroup.get('numericValue')?.value).toBe(\n        mockContractValue.numericValue,\n      );\n      expect(component.valuesFormGroup.get('madsValue')?.value).toBe(\n        mockContractValue.madsValue,\n      );\n      expect(component.valuesFormGroup.get('otherValue')?.value).toBe(\n        mockContractValue.otherValue,\n      );\n      expect(component.valuesFormGroup.get('cdp')?.value).toBe(\n        mockContractValue.cdp,\n      );\n      expect(component.valuesFormGroup.get('cdpEntityId')?.value).toBe(\n        mockContractValue.cdpEntityId,\n      );\n    }));\n  });\n\n  describe('Form validation', () => {\n    beforeEach(fakeAsync(() => {\n      component.contract = mockContract;\n      fixture.detectChanges();\n      tick();\n      flush();\n      discardPeriodicTasks();\n    }));\n\n    it('should validate required fields', () => {\n      const startDateControl = component.valuesFormGroup.get('startDate');\n      const subscriptionDateControl =\n        component.valuesFormGroup.get('subscriptionDate');\n      const cdpControl = component.valuesFormGroup.get('cdp');\n      const cdpEntityIdControl = component.valuesFormGroup.get('cdpEntityId');\n\n      expect(startDateControl?.hasError('required')).toBeTruthy();\n      expect(subscriptionDateControl?.hasError('required')).toBeTruthy();\n      expect(cdpControl?.hasError('required')).toBeTruthy();\n      expect(cdpEntityIdControl?.hasError('required')).toBeTruthy();\n    });\n\n    it('should validate minimum value for numeric fields', () => {\n      component.valuesFormGroup.get('numericValue')?.setValue(-1);\n      component.valuesFormGroup.get('madsValue')?.setValue(-1);\n      component.valuesFormGroup.get('otherValue')?.setValue(-1);\n      component.valuesFormGroup.get('futureValidityValue')?.setValue(-1);\n\n      expect(\n        component.valuesFormGroup.get('numericValue')?.hasError('min'),\n      ).toBeTruthy();\n      expect(\n        component.valuesFormGroup.get('madsValue')?.hasError('min'),\n      ).toBeTruthy();\n      expect(\n        component.valuesFormGroup.get('otherValue')?.hasError('min'),\n      ).toBeTruthy();\n      expect(\n        component.valuesFormGroup.get('futureValidityValue')?.hasError('min'),\n      ).toBeTruthy();\n    });\n  });\n\n  describe('Form functionality', () => {\n    beforeEach(fakeAsync(() => {\n      component.contract = mockContract;\n      fixture.detectChanges();\n      tick();\n      flush();\n      discardPeriodicTasks();\n    }));\n\n    it('should calculate numeric value from other value inputs', () => {\n      component.valuesFormGroup.get('madsValue')?.setValue(100);\n      component.valuesFormGroup.get('otherValue')?.setValue(200);\n      component.valuesFormGroup.get('futureValidityValue')?.setValue(300);\n      fixture.detectChanges();\n\n      expect(component.valuesFormGroup.get('numericValue')?.value).toBe(600);\n    });\n\n    it('should enable entityId field when otherValue is greater than 0', () => {\n      const isOtherEntityControl =\n        component.valuesFormGroup.get('isOtherEntity');\n      const entityIdControl = component.valuesFormGroup.get('entityId');\n\n      component.valuesFormGroup.get('otherValue')?.setValue(500);\n      component.onOtherValueChange();\n      fixture.detectChanges();\n\n      expect(isOtherEntityControl?.value).toBe(true);\n      expect(entityIdControl?.disabled).toBe(false);\n    });\n\n    it('should disable entityId field when otherValue is 0', () => {\n      const isOtherEntityControl =\n        component.valuesFormGroup.get('isOtherEntity');\n      const entityIdControl = component.valuesFormGroup.get('entityId');\n\n      component.valuesFormGroup.get('otherValue')?.setValue(0);\n      component.onOtherValueChange();\n      fixture.detectChanges();\n\n      expect(isOtherEntityControl?.value).toBe(false);\n      expect(entityIdControl?.disabled).toBe(true);\n    });\n\n    it('should update contract duration when dates change', () => {\n      const startDate = new Date(2023, 0, 1);\n      const endDate = new Date(2023, 2, 31);\n\n      component.valuesFormGroup.get('startDate')?.setValue(startDate);\n      component.valuesFormGroup.get('endDate')?.setValue(endDate);\n      fixture.detectChanges();\n\n      component['validateDates']();\n\n      expect(component.contractDuration.days).toBeGreaterThan(0);\n      expect(component.contractDuration.months).toBeGreaterThan(0);\n    });\n  });\n\n  describe('Public methods', () => {\n    beforeEach(fakeAsync(() => {\n      component.contract = mockContract;\n      fixture.detectChanges();\n      tick();\n      flush();\n      discardPeriodicTasks();\n    }));\n\n    it('should check if form is valid', () => {\n      expect(component.isValid()).toBeFalse();\n\n      component.valuesFormGroup.get('startDate')?.setValue(new Date());\n      component.valuesFormGroup.get('subscriptionDate')?.setValue(new Date());\n      component.valuesFormGroup.get('cdp')?.setValue('123');\n      component.valuesFormGroup.get('cdpEntityId')?.setValue(1);\n      component.valuesFormGroup.get('madsValue')?.setValue(1000);\n\n      expect(component.isValid()).toBeTrue();\n    });\n\n    it('should get contract values from form', () => {\n      const testDate = new Date(2023, 0, 1);\n\n      component.valuesFormGroup.patchValue({\n        numericValue: 5000,\n        madsValue: 3000,\n        otherValue: 2000,\n        futureValidityValue: 0,\n        startDate: testDate,\n        endDate: testDate,\n        subscriptionDate: testDate,\n        cdp: 789,\n        cdpEntityId: 1,\n        isOtherEntity: false,\n      });\n\n      const result = component.getContractValuesFromForm();\n\n      expect(result.numericValue).toBe(5000);\n      expect(result.madsValue).toBe(3000);\n      expect(result.otherValue).toBe(2000);\n      expect(result.cdp).toBe(789);\n      expect(result.cdpEntityId).toBe(1);\n      expect(result.contractId).toBe(mockContract.id);\n    });\n\n    it('should emit form values when form is valid', fakeAsync(() => {\n      spyOn(component.valuesSubmitted, 'emit');\n\n      component.valuesFormGroup.get('startDate')?.setValue(new Date());\n      component.valuesFormGroup.get('subscriptionDate')?.setValue(new Date());\n      component.valuesFormGroup.get('cdp')?.setValue('123');\n      component.valuesFormGroup.get('cdpEntityId')?.setValue(1);\n      component.valuesFormGroup.get('madsValue')?.setValue(1000);\n\n      component.valuesFormGroup.updateValueAndValidity();\n      tick(300);\n      flush();\n      discardPeriodicTasks();\n\n      expect(component.valuesSubmitted.emit).toHaveBeenCalled();\n    }));\n\n    it('should emit null when form is invalid', fakeAsync(() => {\n      spyOn(component.valuesSubmitted, 'emit');\n\n      component.valuesFormGroup.updateValueAndValidity();\n      tick(300);\n      flush();\n      discardPeriodicTasks();\n\n      expect(component.valuesSubmitted.emit).toHaveBeenCalledWith(null);\n    }));\n  });\n});"], "mappings": ";AAAA,SAASA,uBAAuB,QAAQ,8BAA8B;AACtE,SAEEC,OAAO,EACPC,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,oBAAoB,QACf,uBAAuB;AAC9B,SACEC,mBAAmB,EACnBC,WAAW,QAEN,gBAAgB;AACvB,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,wBAAwB,QAAQ,wBAAwB;AACjE,SAASC,uBAAuB,QAAQ,sCAAsC;AAK9E,SAASC,gBAAgB,QAAQ,kDAAkD;AACnF,SAASC,qBAAqB,QAAQ,uDAAuD;AAC7F,SAASC,aAAa,QAAQ,8CAA8C;AAC5E,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,EAAE,EAAEC,UAAU,QAAQ,MAAM;AACrC,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,gBAAgB,EAAEC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAClE,SAASC,oBAAoB,QAAQ,cAAc;AAM5C,IAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAA3BC,YAAA;IACI,KAAAC,YAAY,GAAY,EAAE;IAEnC,KAAAC,QAAQ,GAAIC,MAAe,IAAU,CAAE,CAAC;IAExC,KAAAC,SAAS,GAAG,MAAW,CAAE,CAAC;EAa5B;EAXEC,UAAUA,CAACC,KAAc,GAAS;EAElCC,gBAAgBA,CAACC,EAAwB;IACvC,IAAI,CAACN,QAAQ,GAAGM,EAAE;EACpB;EAEAC,iBAAiBA,CAACD,EAAc;IAC9B,IAAI,CAACJ,SAAS,GAAGI,EAAE;EACrB;EAEAE,gBAAgBA,CAACC,UAAmB,GAAS;;;;cAhB5Cd;MAAK;;;;AADKE,qBAAqB,GAAAa,UAAA,EAJjChB,SAAS,CAAC;EACTiB,QAAQ,EAAE,mBAAmB;EAC7BC,UAAU,EAAE;CACb,CAAC,C,EACWf,qBAAqB,CAkBjC;;AAEDgB,QAAQ,CAAC,6BAA6B,EAAE,MAAK;EAC3C,IAAIC,SAAsC;EAC1C,IAAIC,OAAsD;EAC1D,IAAIC,gBAA+C;EACnD,IAAIC,mBAAqD;EACzD,IAAIC,wBAA+D;EACnE,IAAIC,eAA6C;EAEjD,MAAMC,YAAY,GAAa,CAC7B;IAAEC,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAU,CAAE,EAC3B;IAAED,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAU,CAAE,CAC5B;EAED,MAAMC,eAAe,GAAgB,CACnC;IAAEF,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAc,CAAE,EAC/B;IAAED,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAc,CAAE,CAChC;EAED,MAAME,YAAY,GAAa;IAC7BH,EAAE,EAAE,CAAC;IACLI,cAAc,EAAE,GAAG;IACnBC,cAAc,EAAE,IAAI;IACpBC,MAAM,EAAE,eAAe;IACvBC,GAAG,EAAE,IAAI;IACTC,SAAS,EAAE,GAAG;IACdC,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,KAAK;IACdC,iBAAiB,EAAE,CAAC;IACpBC,mBAAmB,EAAE,CAAC;IACtBC,eAAe,EAAE;GAClB;EAED,MAAMC,iBAAiB,GAAmB;IACxCf,EAAE,EAAE,CAAC;IACLgB,YAAY,EAAE,IAAI;IAClBC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE,IAAI;IAChBC,mBAAmB,EAAE,CAAC;IACtBC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,YAAY;IACrBC,gBAAgB,EAAE,YAAY;IAC9BC,GAAG,EAAE,GAAG;IACRC,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,KAAK;IACpBC,SAAS,EAAExB,eAAe,CAAC,CAAC,CAAC;IAC7ByB,UAAU,EAAE;GACb;EAEDC,UAAU,CAAC,MAAK;IACdjC,gBAAgB,GAAGkC,OAAO,CAACC,YAAY,CAAgB,eAAe,EAAE,CACtE,QAAQ,CACT,CAAC;IACFlC,mBAAmB,GAAGiC,OAAO,CAACC,YAAY,CACxC,kBAAkB,EAClB,CAAC,QAAQ,CAAC,CACX;IACDjC,wBAAwB,GAAGgC,OAAO,CAACC,YAAY,CAC7C,uBAAuB,EACvB,CAAC,8BAA8B,CAAC,CACjC;IACDhC,eAAe,GAAG+B,OAAO,CAACC,YAAY,CAAe,cAAc,EAAE,CACnE,OAAO,CACR,CAAC;IAEFnC,gBAAgB,CAACoC,MAAM,CAACC,GAAG,CAACC,WAAW,CAAChE,EAAE,CAAC8B,YAAY,CAAC,CAAC;IACzDH,mBAAmB,CAACmC,MAAM,CAACC,GAAG,CAACC,WAAW,CAAChE,EAAE,CAACiC,eAAe,CAAC,CAAC;IAC/DL,wBAAwB,CAACqC,4BAA4B,CAACF,GAAG,CAACC,WAAW,CACnEhE,EAAE,CAAC,IAAI,CAAC,CACT;IAEDnB,OAAO,CAACqF,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPjE,2BAA2B,EAC3BtB,uBAAuB,EACvBe,uBAAuB,EACvBT,mBAAmB,EACnBC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,aAAa,EACbC,cAAc,EACdC,eAAe,EACfC,oBAAoB,CACrB;MACD2E,SAAS,EAAE,CACT1E,wBAAwB,EAAE,EAC1B;QAAE2E,OAAO,EAAEvE,aAAa;QAAEwE,QAAQ,EAAE5C;MAAgB,CAAE,EACtD;QAAE2C,OAAO,EAAEzE,gBAAgB;QAAE0E,QAAQ,EAAE3C;MAAmB,CAAE,EAC5D;QAAE0C,OAAO,EAAExE,qBAAqB;QAAEyE,QAAQ,EAAE1C;MAAwB,CAAE,EACtE;QAAEyC,OAAO,EAAEtE,YAAY;QAAEuE,QAAQ,EAAEzC;MAAe,CAAE,CACrD;MACD0C,OAAO,EAAE,CAACpE,gBAAgB;KAC3B,CAAC,CACCqE,iBAAiB,CAAClE,oBAAoB,EAAE;MACvCmE,GAAG,EAAE;QACHL,SAAS,EAAE;;KAEd,CAAC,CACDM,iBAAiB,EAAE;IAEtBjD,OAAO,GAAG5C,OAAO,CAAC8F,eAAe,CAACzE,2BAA2B,CAAC;IAC9DsB,SAAS,GAAGC,OAAO,CAACmD,iBAAiB;EACvC,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACtD,SAAS,CAAC,CAACuD,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFxD,QAAQ,CAAC,qBAAqB,EAAE,MAAK;IACnCsD,EAAE,CAAC,4CAA4C,EAAE/F,SAAS,CAAC,MAAK;MAC9D0C,SAAS,CAACwD,QAAQ,GAAG9C,YAAY;MACjCT,OAAO,CAACwD,aAAa,EAAE;MACvBlG,IAAI,EAAE;MACNC,KAAK,EAAE;MACPC,oBAAoB,EAAE;MAEtBuC,SAAS,CAAC0D,eAAe,CAACC,UAAU,CAClC;QACEpC,YAAY,EAAE,CAAC;QACfC,SAAS,EAAE,CAAC;QACZC,UAAU,EAAE,CAAC;QACbC,mBAAmB,EAAE;OACtB,EACD;QAAEkC,SAAS,EAAE;MAAK,CAAE,CACrB;MAEDN,MAAM,CAACtD,SAAS,CAAC0D,eAAe,CAAC,CAACG,WAAW,EAAE;MAE/CP,MAAM,CAACtD,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,cAAc,CAAC,EAAExE,KAAK,CAAC,CAACyE,IAAI,CAAC,CAAC,CAAC;MACpET,MAAM,CAACtD,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,WAAW,CAAC,EAAExE,KAAK,CAAC,CAACyE,IAAI,CAAC,CAAC,CAAC;MACjET,MAAM,CAACtD,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,YAAY,CAAC,EAAExE,KAAK,CAAC,CAACyE,IAAI,CAAC,CAAC,CAAC;MAClET,MAAM,CAACtD,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,qBAAqB,CAAC,EAAExE,KAAK,CAAC,CAACyE,IAAI,CACtE,CAAC,CACF;MACDT,MAAM,CAACtD,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,KAAK,CAAC,EAAExE,KAAK,CAAC,CAACyE,IAAI,CAAC,EAAE,CAAC;MAC5DT,MAAM,CAACtD,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,WAAW,CAAC,EAAExE,KAAK,CAAC,CAAC0E,QAAQ,EAAE;IACtE,CAAC,CAAC,CAAC;IAEHX,EAAE,CAAC,sCAAsC,EAAE/F,SAAS,CAAC,MAAK;MACxD0C,SAAS,CAACwD,QAAQ,GAAG9C,YAAY;MACjCT,OAAO,CAACwD,aAAa,EAAE;MACvBlG,IAAI,EAAE;MACNC,KAAK,EAAE;MACPC,oBAAoB,EAAE;MAEtB6F,MAAM,CAACpD,gBAAgB,CAACoC,MAAM,CAAC,CAAC2B,gBAAgB,EAAE;MAClDX,MAAM,CAACnD,mBAAmB,CAACmC,MAAM,CAAC,CAAC2B,gBAAgB,EAAE;MACrDX,MAAM,CACJlD,wBAAwB,CAACqC,4BAA4B,CACtD,CAACyB,oBAAoB,CAACxD,YAAY,CAACH,EAAE,CAAC;MACvC+C,MAAM,CAACtD,SAAS,CAACmE,QAAQ,CAAC,CAACC,OAAO,CAAC9D,YAAY,CAAC;MAChDgD,MAAM,CAACtD,SAAS,CAACqE,WAAW,CAAC,CAACD,OAAO,CAAC3D,eAAe,CAAC;IACxD,CAAC,CAAC,CAAC;IAEH4C,EAAE,CAAC,iDAAiD,EAAE/F,SAAS,CAAC,MAAK;MAEnEgH,KAAK,CAACC,OAAO,EAAE,OAAO,CAAC,CAAChC,GAAG,CAACiC,QAAQ,CAAC,MAAK,CAAE,CAAC,CAAC;MAE9CtE,gBAAgB,CAACoC,MAAM,CAACC,GAAG,CAACC,WAAW,CACrC/D,UAAU,CAAC,MAAM,IAAIgG,KAAK,CAAC,YAAY,CAAC,CAAC,CAC1C;MACDzE,SAAS,CAACwD,QAAQ,GAAG9C,YAAY;MACjCT,OAAO,CAACwD,aAAa,EAAE;MACvBlG,IAAI,EAAE;MACNC,KAAK,EAAE;MACPC,oBAAoB,EAAE;MAEtB6F,MAAM,CAACjD,eAAe,CAACqE,KAAK,CAAC,CAACR,oBAAoB,CAChD,qCAAqC,CACtC;IACH,CAAC,CAAC,CAAC;IAEHb,EAAE,CAAC,qDAAqD,EAAE/F,SAAS,CAAC,MAAK;MACvE0C,SAAS,CAACwD,QAAQ,GAAG9C,YAAY;MACjCV,SAAS,CAAC2E,aAAa,GAAGrD,iBAAiB;MAC3CrB,OAAO,CAACwD,aAAa,EAAE;MACvBlG,IAAI,EAAE;MACNC,KAAK,EAAE;MACPC,oBAAoB,EAAE;MAEtB6F,MAAM,CAACtD,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,cAAc,CAAC,EAAExE,KAAK,CAAC,CAACyE,IAAI,CAC/DzC,iBAAiB,CAACC,YAAY,CAC/B;MACD+B,MAAM,CAACtD,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,WAAW,CAAC,EAAExE,KAAK,CAAC,CAACyE,IAAI,CAC5DzC,iBAAiB,CAACE,SAAS,CAC5B;MACD8B,MAAM,CAACtD,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,YAAY,CAAC,EAAExE,KAAK,CAAC,CAACyE,IAAI,CAC7DzC,iBAAiB,CAACG,UAAU,CAC7B;MACD6B,MAAM,CAACtD,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,KAAK,CAAC,EAAExE,KAAK,CAAC,CAACyE,IAAI,CACtDzC,iBAAiB,CAACQ,GAAG,CACtB;MACDwB,MAAM,CAACtD,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,aAAa,CAAC,EAAExE,KAAK,CAAC,CAACyE,IAAI,CAC9DzC,iBAAiB,CAACS,WAAW,CAC9B;IACH,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EAEFhC,QAAQ,CAAC,iBAAiB,EAAE,MAAK;IAC/BoC,UAAU,CAAC7E,SAAS,CAAC,MAAK;MACxB0C,SAAS,CAACwD,QAAQ,GAAG9C,YAAY;MACjCT,OAAO,CAACwD,aAAa,EAAE;MACvBlG,IAAI,EAAE;MACNC,KAAK,EAAE;MACPC,oBAAoB,EAAE;IACxB,CAAC,CAAC,CAAC;IAEH4F,EAAE,CAAC,iCAAiC,EAAE,MAAK;MACzC,MAAMuB,gBAAgB,GAAG5E,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,WAAW,CAAC;MACnE,MAAMe,uBAAuB,GAC3B7E,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,kBAAkB,CAAC;MACnD,MAAMgB,UAAU,GAAG9E,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,KAAK,CAAC;MACvD,MAAMiB,kBAAkB,GAAG/E,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,aAAa,CAAC;MAEvER,MAAM,CAACsB,gBAAgB,EAAEI,QAAQ,CAAC,UAAU,CAAC,CAAC,CAACzB,UAAU,EAAE;MAC3DD,MAAM,CAACuB,uBAAuB,EAAEG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAACzB,UAAU,EAAE;MAClED,MAAM,CAACwB,UAAU,EAAEE,QAAQ,CAAC,UAAU,CAAC,CAAC,CAACzB,UAAU,EAAE;MACrDD,MAAM,CAACyB,kBAAkB,EAAEC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAACzB,UAAU,EAAE;IAC/D,CAAC,CAAC;IAEFF,EAAE,CAAC,kDAAkD,EAAE,MAAK;MAC1DrD,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,cAAc,CAAC,EAAEmB,QAAQ,CAAC,CAAC,CAAC,CAAC;MAC3DjF,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,WAAW,CAAC,EAAEmB,QAAQ,CAAC,CAAC,CAAC,CAAC;MACxDjF,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,YAAY,CAAC,EAAEmB,QAAQ,CAAC,CAAC,CAAC,CAAC;MACzDjF,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,qBAAqB,CAAC,EAAEmB,QAAQ,CAAC,CAAC,CAAC,CAAC;MAElE3B,MAAM,CACJtD,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,cAAc,CAAC,EAAEkB,QAAQ,CAAC,KAAK,CAAC,CAC/D,CAACzB,UAAU,EAAE;MACdD,MAAM,CACJtD,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,WAAW,CAAC,EAAEkB,QAAQ,CAAC,KAAK,CAAC,CAC5D,CAACzB,UAAU,EAAE;MACdD,MAAM,CACJtD,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,YAAY,CAAC,EAAEkB,QAAQ,CAAC,KAAK,CAAC,CAC7D,CAACzB,UAAU,EAAE;MACdD,MAAM,CACJtD,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,qBAAqB,CAAC,EAAEkB,QAAQ,CAAC,KAAK,CAAC,CACtE,CAACzB,UAAU,EAAE;IAChB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFxD,QAAQ,CAAC,oBAAoB,EAAE,MAAK;IAClCoC,UAAU,CAAC7E,SAAS,CAAC,MAAK;MACxB0C,SAAS,CAACwD,QAAQ,GAAG9C,YAAY;MACjCT,OAAO,CAACwD,aAAa,EAAE;MACvBlG,IAAI,EAAE;MACNC,KAAK,EAAE;MACPC,oBAAoB,EAAE;IACxB,CAAC,CAAC,CAAC;IAEH4F,EAAE,CAAC,wDAAwD,EAAE,MAAK;MAChErD,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,WAAW,CAAC,EAAEmB,QAAQ,CAAC,GAAG,CAAC;MACzDjF,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,YAAY,CAAC,EAAEmB,QAAQ,CAAC,GAAG,CAAC;MAC1DjF,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,qBAAqB,CAAC,EAAEmB,QAAQ,CAAC,GAAG,CAAC;MACnEhF,OAAO,CAACwD,aAAa,EAAE;MAEvBH,MAAM,CAACtD,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,cAAc,CAAC,EAAExE,KAAK,CAAC,CAACyE,IAAI,CAAC,GAAG,CAAC;IACxE,CAAC,CAAC;IAEFV,EAAE,CAAC,gEAAgE,EAAE,MAAK;MACxE,MAAM6B,oBAAoB,GACxBlF,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,eAAe,CAAC;MAChD,MAAMqB,eAAe,GAAGnF,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,UAAU,CAAC;MAEjE9D,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,YAAY,CAAC,EAAEmB,QAAQ,CAAC,GAAG,CAAC;MAC1DjF,SAAS,CAACoF,kBAAkB,EAAE;MAC9BnF,OAAO,CAACwD,aAAa,EAAE;MAEvBH,MAAM,CAAC4B,oBAAoB,EAAE5F,KAAK,CAAC,CAACyE,IAAI,CAAC,IAAI,CAAC;MAC9CT,MAAM,CAAC6B,eAAe,EAAEE,QAAQ,CAAC,CAACtB,IAAI,CAAC,KAAK,CAAC;IAC/C,CAAC,CAAC;IAEFV,EAAE,CAAC,oDAAoD,EAAE,MAAK;MAC5D,MAAM6B,oBAAoB,GACxBlF,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,eAAe,CAAC;MAChD,MAAMqB,eAAe,GAAGnF,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,UAAU,CAAC;MAEjE9D,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,YAAY,CAAC,EAAEmB,QAAQ,CAAC,CAAC,CAAC;MACxDjF,SAAS,CAACoF,kBAAkB,EAAE;MAC9BnF,OAAO,CAACwD,aAAa,EAAE;MAEvBH,MAAM,CAAC4B,oBAAoB,EAAE5F,KAAK,CAAC,CAACyE,IAAI,CAAC,KAAK,CAAC;MAC/CT,MAAM,CAAC6B,eAAe,EAAEE,QAAQ,CAAC,CAACtB,IAAI,CAAC,IAAI,CAAC;IAC9C,CAAC,CAAC;IAEFV,EAAE,CAAC,mDAAmD,EAAE,MAAK;MAC3D,MAAM1B,SAAS,GAAG,IAAI2D,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;MACtC,MAAM1D,OAAO,GAAG,IAAI0D,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;MAErCtF,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,WAAW,CAAC,EAAEmB,QAAQ,CAACtD,SAAS,CAAC;MAC/D3B,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,SAAS,CAAC,EAAEmB,QAAQ,CAACrD,OAAO,CAAC;MAC3D3B,OAAO,CAACwD,aAAa,EAAE;MAEvBzD,SAAS,CAAC,eAAe,CAAC,EAAE;MAE5BsD,MAAM,CAACtD,SAAS,CAACuF,gBAAgB,CAACC,IAAI,CAAC,CAACC,eAAe,CAAC,CAAC,CAAC;MAC1DnC,MAAM,CAACtD,SAAS,CAACuF,gBAAgB,CAACG,MAAM,CAAC,CAACD,eAAe,CAAC,CAAC,CAAC;IAC9D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF1F,QAAQ,CAAC,gBAAgB,EAAE,MAAK;IAC9BoC,UAAU,CAAC7E,SAAS,CAAC,MAAK;MACxB0C,SAAS,CAACwD,QAAQ,GAAG9C,YAAY;MACjCT,OAAO,CAACwD,aAAa,EAAE;MACvBlG,IAAI,EAAE;MACNC,KAAK,EAAE;MACPC,oBAAoB,EAAE;IACxB,CAAC,CAAC,CAAC;IAEH4F,EAAE,CAAC,+BAA+B,EAAE,MAAK;MACvCC,MAAM,CAACtD,SAAS,CAAC2F,OAAO,EAAE,CAAC,CAACC,SAAS,EAAE;MAEvC5F,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,WAAW,CAAC,EAAEmB,QAAQ,CAAC,IAAIK,IAAI,EAAE,CAAC;MAChEtF,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,kBAAkB,CAAC,EAAEmB,QAAQ,CAAC,IAAIK,IAAI,EAAE,CAAC;MACvEtF,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,KAAK,CAAC,EAAEmB,QAAQ,CAAC,KAAK,CAAC;MACrDjF,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,aAAa,CAAC,EAAEmB,QAAQ,CAAC,CAAC,CAAC;MACzDjF,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,WAAW,CAAC,EAAEmB,QAAQ,CAAC,IAAI,CAAC;MAE1D3B,MAAM,CAACtD,SAAS,CAAC2F,OAAO,EAAE,CAAC,CAACE,QAAQ,EAAE;IACxC,CAAC,CAAC;IAEFxC,EAAE,CAAC,sCAAsC,EAAE,MAAK;MAC9C,MAAMyC,QAAQ,GAAG,IAAIR,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;MAErCtF,SAAS,CAAC0D,eAAe,CAACC,UAAU,CAAC;QACnCpC,YAAY,EAAE,IAAI;QAClBC,SAAS,EAAE,IAAI;QACfC,UAAU,EAAE,IAAI;QAChBC,mBAAmB,EAAE,CAAC;QACtBC,SAAS,EAAEmE,QAAQ;QACnBlE,OAAO,EAAEkE,QAAQ;QACjBjE,gBAAgB,EAAEiE,QAAQ;QAC1BhE,GAAG,EAAE,GAAG;QACRC,WAAW,EAAE,CAAC;QACdC,aAAa,EAAE;OAChB,CAAC;MAEF,MAAM+D,MAAM,GAAG/F,SAAS,CAACgG,yBAAyB,EAAE;MAEpD1C,MAAM,CAACyC,MAAM,CAACxE,YAAY,CAAC,CAACwC,IAAI,CAAC,IAAI,CAAC;MACtCT,MAAM,CAACyC,MAAM,CAACvE,SAAS,CAAC,CAACuC,IAAI,CAAC,IAAI,CAAC;MACnCT,MAAM,CAACyC,MAAM,CAACtE,UAAU,CAAC,CAACsC,IAAI,CAAC,IAAI,CAAC;MACpCT,MAAM,CAACyC,MAAM,CAACjE,GAAG,CAAC,CAACiC,IAAI,CAAC,GAAG,CAAC;MAC5BT,MAAM,CAACyC,MAAM,CAAChE,WAAW,CAAC,CAACgC,IAAI,CAAC,CAAC,CAAC;MAClCT,MAAM,CAACyC,MAAM,CAAC7D,UAAU,CAAC,CAAC6B,IAAI,CAACrD,YAAY,CAACH,EAAE,CAAC;IACjD,CAAC,CAAC;IAEF8C,EAAE,CAAC,4CAA4C,EAAE/F,SAAS,CAAC,MAAK;MAC9DgH,KAAK,CAACtE,SAAS,CAACiG,eAAe,EAAE,MAAM,CAAC;MAExCjG,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,WAAW,CAAC,EAAEmB,QAAQ,CAAC,IAAIK,IAAI,EAAE,CAAC;MAChEtF,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,kBAAkB,CAAC,EAAEmB,QAAQ,CAAC,IAAIK,IAAI,EAAE,CAAC;MACvEtF,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,KAAK,CAAC,EAAEmB,QAAQ,CAAC,KAAK,CAAC;MACrDjF,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,aAAa,CAAC,EAAEmB,QAAQ,CAAC,CAAC,CAAC;MACzDjF,SAAS,CAAC0D,eAAe,CAACI,GAAG,CAAC,WAAW,CAAC,EAAEmB,QAAQ,CAAC,IAAI,CAAC;MAE1DjF,SAAS,CAAC0D,eAAe,CAACwC,sBAAsB,EAAE;MAClD3I,IAAI,CAAC,GAAG,CAAC;MACTC,KAAK,EAAE;MACPC,oBAAoB,EAAE;MAEtB6F,MAAM,CAACtD,SAAS,CAACiG,eAAe,CAACE,IAAI,CAAC,CAAClC,gBAAgB,EAAE;IAC3D,CAAC,CAAC,CAAC;IAEHZ,EAAE,CAAC,uCAAuC,EAAE/F,SAAS,CAAC,MAAK;MACzDgH,KAAK,CAACtE,SAAS,CAACiG,eAAe,EAAE,MAAM,CAAC;MAExCjG,SAAS,CAAC0D,eAAe,CAACwC,sBAAsB,EAAE;MAClD3I,IAAI,CAAC,GAAG,CAAC;MACTC,KAAK,EAAE;MACPC,oBAAoB,EAAE;MAEtB6F,MAAM,CAACtD,SAAS,CAACiG,eAAe,CAACE,IAAI,CAAC,CAACjC,oBAAoB,CAAC,IAAI,CAAC;IACnE,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}