{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/Github/SICMA/sicma-ui/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { ContractorDetailFormComponent } from './contractor-detail-form.component';\ndescribe('ContractorDetailFormComponent', () => {\n  let component;\n  let fixture;\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    yield TestBed.configureTestingModule({\n      imports: [ContractorDetailFormComponent, HttpClientTestingModule, BrowserAnimationsModule]\n    }).compileComponents();\n    fixture = TestBed.createComponent(ContractorDetailFormComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  }));\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "TestBed", "BrowserAnimationsModule", "ContractorDetailFormComponent", "describe", "component", "fixture", "beforeEach", "_asyncToGenerator", "configureTestingModule", "imports", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\components\\contractor-detail-form\\contractor-detail-form.component.spec.ts"], "sourcesContent": ["import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { ComponentFixture, TestBed } from '@angular/core/testing';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { ContractorDetailFormComponent } from './contractor-detail-form.component';\n\ndescribe('ContractorDetailFormComponent', () => {\n  let component: ContractorDetailFormComponent;\n  let fixture: ComponentFixture<ContractorDetailFormComponent>;\n\n  beforeEach(async () => {\n    await TestBed.configureTestingModule({\n      imports: [\n        ContractorDetailFormComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n      ],\n    }).compileComponents();\n\n    fixture = TestBed.createComponent(ContractorDetailFormComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});"], "mappings": ";AAAA,SAASA,uBAAuB,QAAQ,8BAA8B;AACtE,SAA2BC,OAAO,QAAQ,uBAAuB;AACjE,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,6BAA6B,QAAQ,oCAAoC;AAElFC,QAAQ,CAAC,+BAA+B,EAAE,MAAK;EAC7C,IAAIC,SAAwC;EAC5C,IAAIC,OAAwD;EAE5DC,UAAU,eAAAC,iBAAA,CAAC,aAAW;IACpB,MAAMP,OAAO,CAACQ,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CACPP,6BAA6B,EAC7BH,uBAAuB,EACvBE,uBAAuB;KAE1B,CAAC,CAACS,iBAAiB,EAAE;IAEtBL,OAAO,GAAGL,OAAO,CAACW,eAAe,CAACT,6BAA6B,CAAC;IAChEE,SAAS,GAAGC,OAAO,CAACO,iBAAiB;IACrCP,OAAO,CAACQ,aAAa,EAAE;EACzB,CAAC,EAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACX,SAAS,CAAC,CAACY,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}