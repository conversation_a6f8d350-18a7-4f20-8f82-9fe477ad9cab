
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for app/features/contractor-dashboard/components/monthly-reports-tab/monthly-report-dialog/monthly-report-obligations</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../../../../index.html">All files</a> app/features/contractor-dashboard/components/monthly-reports-tab/monthly-report-dialog/monthly-report-obligations</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">92.98% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>53/57</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">63.63% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>14/22</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">95.45% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>21/22</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">92.3% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>48/52</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="monthly-report-obligations.component.ts"><a href="monthly-report-obligations.component.ts.html">monthly-report-obligations.component.ts</a></td>
	<td data-value="92.98" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 92%"></div><div class="cover-empty" style="width: 8%"></div></div>
	</td>
	<td data-value="92.98" class="pct high">92.98%</td>
	<td data-value="57" class="abs high">53/57</td>
	<td data-value="63.63" class="pct medium">63.63%</td>
	<td data-value="22" class="abs medium">14/22</td>
	<td data-value="95.45" class="pct high">95.45%</td>
	<td data-value="22" class="abs high">21/22</td>
	<td data-value="92.3" class="pct high">92.3%</td>
	<td data-value="52" class="abs high">48/52</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-15T19:39:21.965Z
            </div>
        <script src="../../../../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../../../../sorter.js"></script>
        <script src="../../../../../../../block-navigation.js"></script>
    </body>
</html>
    