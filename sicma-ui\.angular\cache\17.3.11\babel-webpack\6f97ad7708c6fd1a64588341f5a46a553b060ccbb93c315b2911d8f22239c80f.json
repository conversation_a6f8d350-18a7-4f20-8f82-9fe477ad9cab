{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/Github/SICMA/sicma-ui/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { NoopAnimationsModule } from '@angular/platform-browser/animations';\nimport { ContractAuditHistoryComponent } from './contract-audit-history.component';\nimport { ContractAuditHistoryService } from '@contract-management/services/contract-audit-history.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { of } from 'rxjs';\ndescribe('ContractAuditHistoryComponent', () => {\n  let component;\n  let fixture;\n  let contractAuditHistoryServiceSpy;\n  let alertServiceSpy;\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    contractAuditHistoryServiceSpy = jasmine.createSpyObj('ContractAuditHistoryService', ['getByContractId']);\n    alertServiceSpy = jasmine.createSpyObj('AlertService', ['error']);\n    contractAuditHistoryServiceSpy.getByContractId.and.returnValue(of([]));\n    yield TestBed.configureTestingModule({\n      imports: [ContractAuditHistoryComponent, HttpClientTestingModule, MatCardModule, MatTableModule, MatIconModule, MatExpansionModule, MatProgressSpinnerModule, NoopAnimationsModule],\n      providers: [{\n        provide: ContractAuditHistoryService,\n        useValue: contractAuditHistoryServiceSpy\n      }, {\n        provide: AlertService,\n        useValue: alertServiceSpy\n      }]\n    }).compileComponents();\n    fixture = TestBed.createComponent(ContractAuditHistoryComponent);\n    component = fixture.componentInstance;\n    component.contract = {\n      id: 1\n    };\n    fixture.detectChanges();\n  }));\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should load audit history', () => {\n    component.loadAuditHistory();\n    expect(contractAuditHistoryServiceSpy.getByContractId).toHaveBeenCalledWith(1);\n  });\n});", "map": {"version": 3, "names": ["TestBed", "HttpClientTestingModule", "MatCardModule", "MatTableModule", "MatIconModule", "MatExpansionModule", "MatProgressSpinnerModule", "NoopAnimationsModule", "ContractAuditHistoryComponent", "ContractAuditHistoryService", "AlertService", "of", "describe", "component", "fixture", "contractAuditHistoryServiceSpy", "alertServiceSpy", "beforeEach", "_asyncToGenerator", "jasmine", "createSpyObj", "getByContractId", "and", "returnValue", "configureTestingModule", "imports", "providers", "provide", "useValue", "compileComponents", "createComponent", "componentInstance", "contract", "id", "detectChanges", "it", "expect", "toBeTruthy", "loadAuditHistory", "toHaveBeenCalledWith"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\components\\contract-audit-history\\contract-audit-history.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { NoopAnimationsModule } from '@angular/platform-browser/animations';\n\nimport { ContractAuditHistoryComponent } from './contract-audit-history.component';\nimport { ContractAuditHistoryService } from '@contract-management/services/contract-audit-history.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { of } from 'rxjs';\nimport { Contract } from '@contract-management/models/contract.model';\n\ndescribe('ContractAuditHistoryComponent', () => {\n  let component: ContractAuditHistoryComponent;\n  let fixture: ComponentFixture<ContractAuditHistoryComponent>;\n  let contractAuditHistoryServiceSpy: jasmine.SpyObj<ContractAuditHistoryService>;\n  let alertServiceSpy: jasmine.SpyObj<AlertService>;\n\n  beforeEach(async () => {\n    contractAuditHistoryServiceSpy = jasmine.createSpyObj(\n      'ContractAuditHistoryService',\n      ['getByContractId'],\n    );\n    alertServiceSpy = jasmine.createSpyObj('AlertService', ['error']);\n\n    contractAuditHistoryServiceSpy.getByContractId.and.returnValue(of([]));\n\n    await TestBed.configureTestingModule({\n      imports: [\n        ContractAuditHistoryComponent,\n        HttpClientTestingModule,\n        MatCardModule,\n        MatTableModule,\n        MatIconModule,\n        MatExpansionModule,\n        MatProgressSpinnerModule,\n        NoopAnimationsModule,\n      ],\n      providers: [\n        {\n          provide: ContractAuditHistoryService,\n          useValue: contractAuditHistoryServiceSpy,\n        },\n        { provide: AlertService, useValue: alertServiceSpy },\n      ],\n    }).compileComponents();\n\n    fixture = TestBed.createComponent(ContractAuditHistoryComponent);\n    component = fixture.componentInstance;\n    component.contract = { id: 1 } as Contract;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should load audit history', () => {\n    component.loadAuditHistory();\n    expect(contractAuditHistoryServiceSpy.getByContractId).toHaveBeenCalledWith(\n      1,\n    );\n  });\n});"], "mappings": ";AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AACjE,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,oBAAoB,QAAQ,sCAAsC;AAE3E,SAASC,6BAA6B,QAAQ,oCAAoC;AAClF,SAASC,2BAA2B,QAAQ,8DAA8D;AAC1G,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,EAAE,QAAQ,MAAM;AAGzBC,QAAQ,CAAC,+BAA+B,EAAE,MAAK;EAC7C,IAAIC,SAAwC;EAC5C,IAAIC,OAAwD;EAC5D,IAAIC,8BAA2E;EAC/E,IAAIC,eAA6C;EAEjDC,UAAU,eAAAC,iBAAA,CAAC,aAAW;IACpBH,8BAA8B,GAAGI,OAAO,CAACC,YAAY,CACnD,6BAA6B,EAC7B,CAAC,iBAAiB,CAAC,CACpB;IACDJ,eAAe,GAAGG,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC;IAEjEL,8BAA8B,CAACM,eAAe,CAACC,GAAG,CAACC,WAAW,CAACZ,EAAE,CAAC,EAAE,CAAC,CAAC;IAEtE,MAAMX,OAAO,CAACwB,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CACPjB,6BAA6B,EAC7BP,uBAAuB,EACvBC,aAAa,EACbC,cAAc,EACdC,aAAa,EACbC,kBAAkB,EAClBC,wBAAwB,EACxBC,oBAAoB,CACrB;MACDmB,SAAS,EAAE,CACT;QACEC,OAAO,EAAElB,2BAA2B;QACpCmB,QAAQ,EAAEb;OACX,EACD;QAAEY,OAAO,EAAEjB,YAAY;QAAEkB,QAAQ,EAAEZ;MAAe,CAAE;KAEvD,CAAC,CAACa,iBAAiB,EAAE;IAEtBf,OAAO,GAAGd,OAAO,CAAC8B,eAAe,CAACtB,6BAA6B,CAAC;IAChEK,SAAS,GAAGC,OAAO,CAACiB,iBAAiB;IACrClB,SAAS,CAACmB,QAAQ,GAAG;MAAEC,EAAE,EAAE;IAAC,CAAc;IAC1CnB,OAAO,CAACoB,aAAa,EAAE;EACzB,CAAC,EAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACvB,SAAS,CAAC,CAACwB,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,2BAA2B,EAAE,MAAK;IACnCtB,SAAS,CAACyB,gBAAgB,EAAE;IAC5BF,MAAM,CAACrB,8BAA8B,CAACM,eAAe,CAAC,CAACkB,oBAAoB,CACzE,CAAC,CACF;EACH,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}