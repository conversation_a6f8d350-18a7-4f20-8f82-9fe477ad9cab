import { Directive, ElementRef, HostListener } from '@angular/core';
import { NgControl } from '@angular/forms';

@Directive({
  selector:
    'input[appUppercase][type="text"],textarea[appUppercase],input:not([noUppercase])[type="text"],textarea:not([noUppercase])',
  standalone: true,
})
export class UppercaseDirective {
  constructor(
    private el: ElementRef,
    private control: NgControl,
  ) {}

  @HostListener('input') onInput() {
    const input = this.el.nativeElement as HTMLInputElement;
    const start = input.selectionStart;
    const end = input.selectionEnd;

    const uppercase = input.value.toUpperCase();

    input.value = uppercase;

    if (this.control && this.control.control) {
      this.control.control.setValue(uppercase, { emitEvent: false });
    }

    input.setSelectionRange(start, end);
  }
}
