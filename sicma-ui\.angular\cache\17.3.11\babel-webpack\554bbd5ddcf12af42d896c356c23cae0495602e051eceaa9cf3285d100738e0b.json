{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/Github/SICMA/sicma-ui/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { NoopAnimationsModule } from '@angular/platform-browser/animations';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { NgxSpinnerModule } from 'ngx-spinner';\nimport { ReportReviewHistoryService } from '@contractor-dashboard/services/report-review-history.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { of } from 'rxjs';\nimport { MonthlyReportReviewHistoryDialogComponent } from './monthly-report-review-history-dialog.component';\ndescribe('MonthlyReportReviewHistoryDialogComponent', () => {\n  let component;\n  let fixture;\n  let mockDialogRef;\n  let mockReviewHistoryService;\n  let mockAlertService;\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);\n    mockReviewHistoryService = jasmine.createSpyObj('ReportReviewHistoryService', ['getByMonthlyReportId']);\n    mockAlertService = jasmine.createSpyObj('AlertService', ['error']);\n    mockReviewHistoryService.getByMonthlyReportId.and.returnValue(of([]));\n    yield TestBed.configureTestingModule({\n      imports: [MonthlyReportReviewHistoryDialogComponent, MatDialogModule, HttpClientTestingModule, NoopAnimationsModule, MatTableModule, MatPaginatorModule, MatSortModule, MatProgressSpinnerModule, NgxSpinnerModule],\n      providers: [{\n        provide: MatDialogRef,\n        useValue: mockDialogRef\n      }, {\n        provide: MAT_DIALOG_DATA,\n        useValue: {\n          monthlyReportId: 1\n        }\n      }, {\n        provide: ReportReviewHistoryService,\n        useValue: mockReviewHistoryService\n      }, {\n        provide: AlertService,\n        useValue: mockAlertService\n      }]\n    }).compileComponents();\n    fixture = TestBed.createComponent(MonthlyReportReviewHistoryDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  }));\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should load review history on init', () => {\n    component.ngOnInit();\n    expect(mockReviewHistoryService.getByMonthlyReportId).toHaveBeenCalledWith(1);\n  });\n  it('should close dialog', () => {\n    component.close();\n    expect(mockDialogRef.close).toHaveBeenCalled();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "MAT_DIALOG_DATA", "MatDialogModule", "MatDialogRef", "HttpClientTestingModule", "NoopAnimationsModule", "MatTableModule", "MatPaginatorModule", "MatSortModule", "MatProgressSpinnerModule", "NgxSpinnerModule", "ReportReviewHistoryService", "AlertService", "of", "MonthlyReportReviewHistoryDialogComponent", "describe", "component", "fixture", "mockDialogRef", "mockReviewHistoryService", "mockAlertService", "beforeEach", "_asyncToGenerator", "jasmine", "createSpyObj", "getByMonthlyReportId", "and", "returnValue", "configureTestingModule", "imports", "providers", "provide", "useValue", "monthlyReportId", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy", "ngOnInit", "toHaveBeenCalledWith", "close", "toHaveBeenCalled"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\components\\monthly-reports-tab\\monthly-report-review-history-dialog\\monthly-report-review-history-dialog.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\nimport {\n  MAT_DIALOG_DATA,\n  MatDialogModule,\n  MatDialogRef,\n} from '@angular/material/dialog';\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { NoopAnimationsModule } from '@angular/platform-browser/animations';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { NgxSpinnerModule } from 'ngx-spinner';\nimport { ReportReviewHistoryService } from '@contractor-dashboard/services/report-review-history.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { of } from 'rxjs';\n\nimport { MonthlyReportReviewHistoryDialogComponent } from './monthly-report-review-history-dialog.component';\n\ndescribe('MonthlyReportReviewHistoryDialogComponent', () => {\n  let component: MonthlyReportReviewHistoryDialogComponent;\n  let fixture: ComponentFixture<MonthlyReportReviewHistoryDialogComponent>;\n  let mockDialogRef: jasmine.SpyObj<\n    MatDialogRef<MonthlyReportReviewHistoryDialogComponent>\n  >;\n  let mockReviewHistoryService: jasmine.SpyObj<ReportReviewHistoryService>;\n  let mockAlertService: jasmine.SpyObj<AlertService>;\n\n  beforeEach(async () => {\n    mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);\n    mockReviewHistoryService = jasmine.createSpyObj(\n      'ReportReviewHistoryService',\n      ['getByMonthlyReportId'],\n    );\n    mockAlertService = jasmine.createSpyObj('AlertService', ['error']);\n\n    mockReviewHistoryService.getByMonthlyReportId.and.returnValue(of([]));\n\n    await TestBed.configureTestingModule({\n      imports: [\n        MonthlyReportReviewHistoryDialogComponent,\n        MatDialogModule,\n        HttpClientTestingModule,\n        NoopAnimationsModule,\n        MatTableModule,\n        MatPaginatorModule,\n        MatSortModule,\n        MatProgressSpinnerModule,\n        NgxSpinnerModule,\n      ],\n      providers: [\n        { provide: MatDialogRef, useValue: mockDialogRef },\n        { provide: MAT_DIALOG_DATA, useValue: { monthlyReportId: 1 } },\n        {\n          provide: ReportReviewHistoryService,\n          useValue: mockReviewHistoryService,\n        },\n        { provide: AlertService, useValue: mockAlertService },\n      ],\n    }).compileComponents();\n\n    fixture = TestBed.createComponent(\n      MonthlyReportReviewHistoryDialogComponent,\n    );\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should load review history on init', () => {\n    component.ngOnInit();\n    expect(mockReviewHistoryService.getByMonthlyReportId).toHaveBeenCalledWith(\n      1,\n    );\n  });\n\n  it('should close dialog', () => {\n    component.close();\n    expect(mockDialogRef.close).toHaveBeenCalled();\n  });\n});"], "mappings": ";AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AACjE,SACEC,eAAe,EACfC,eAAe,EACfC,YAAY,QACP,0BAA0B;AACjC,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,oBAAoB,QAAQ,sCAAsC;AAC3E,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,gBAAgB,QAAQ,aAAa;AAC9C,SAASC,0BAA0B,QAAQ,8DAA8D;AACzG,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,EAAE,QAAQ,MAAM;AAEzB,SAASC,yCAAyC,QAAQ,kDAAkD;AAE5GC,QAAQ,CAAC,2CAA2C,EAAE,MAAK;EACzD,IAAIC,SAAoD;EACxD,IAAIC,OAAoE;EACxE,IAAIC,aAEH;EACD,IAAIC,wBAAoE;EACxE,IAAIC,gBAA8C;EAElDC,UAAU,eAAAC,iBAAA,CAAC,aAAW;IACpBJ,aAAa,GAAGK,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC;IAC/DL,wBAAwB,GAAGI,OAAO,CAACC,YAAY,CAC7C,4BAA4B,EAC5B,CAAC,sBAAsB,CAAC,CACzB;IACDJ,gBAAgB,GAAGG,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC;IAElEL,wBAAwB,CAACM,oBAAoB,CAACC,GAAG,CAACC,WAAW,CAACd,EAAE,CAAC,EAAE,CAAC,CAAC;IAErE,MAAMb,OAAO,CAAC4B,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CACPf,yCAAyC,EACzCZ,eAAe,EACfE,uBAAuB,EACvBC,oBAAoB,EACpBC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,wBAAwB,EACxBC,gBAAgB,CACjB;MACDoB,SAAS,EAAE,CACT;QAAEC,OAAO,EAAE5B,YAAY;QAAE6B,QAAQ,EAAEd;MAAa,CAAE,EAClD;QAAEa,OAAO,EAAE9B,eAAe;QAAE+B,QAAQ,EAAE;UAAEC,eAAe,EAAE;QAAC;MAAE,CAAE,EAC9D;QACEF,OAAO,EAAEpB,0BAA0B;QACnCqB,QAAQ,EAAEb;OACX,EACD;QAAEY,OAAO,EAAEnB,YAAY;QAAEoB,QAAQ,EAAEZ;MAAgB,CAAE;KAExD,CAAC,CAACc,iBAAiB,EAAE;IAEtBjB,OAAO,GAAGjB,OAAO,CAACmC,eAAe,CAC/BrB,yCAAyC,CAC1C;IACDE,SAAS,GAAGC,OAAO,CAACmB,iBAAiB;IACrCnB,OAAO,CAACoB,aAAa,EAAE;EACzB,CAAC,EAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACvB,SAAS,CAAC,CAACwB,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,oCAAoC,EAAE,MAAK;IAC5CtB,SAAS,CAACyB,QAAQ,EAAE;IACpBF,MAAM,CAACpB,wBAAwB,CAACM,oBAAoB,CAAC,CAACiB,oBAAoB,CACxE,CAAC,CACF;EACH,CAAC,CAAC;EAEFJ,EAAE,CAAC,qBAAqB,EAAE,MAAK;IAC7BtB,SAAS,CAAC2B,KAAK,EAAE;IACjBJ,MAAM,CAACrB,aAAa,CAACyB,KAAK,CAAC,CAACC,gBAAgB,EAAE;EAChD,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}