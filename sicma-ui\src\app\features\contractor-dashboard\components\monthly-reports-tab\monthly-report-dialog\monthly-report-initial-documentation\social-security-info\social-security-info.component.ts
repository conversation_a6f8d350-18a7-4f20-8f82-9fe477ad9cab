import { ARL } from '@contractor-dashboard/models/arl.model';
import { PensionFund } from '@contractor-dashboard/models/pension-fund.model';
import { ArlService } from '@contractor-dashboard/services/arl.service';
import { PensionFundService } from '@contractor-dashboard/services/pension-fund.service';
import { Eps } from '@contractor-management/models/eps.model';
import { EpsService } from '@contractor-management/services/eps.service';
import { AlertService } from '@shared/services/alert.service';

import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButton, MatIconButton } from '@angular/material/button';
import { MatOption } from '@angular/material/core';
import { MatError, MatFormField, MatLabel } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput } from '@angular/material/input';
import { MatSelect } from '@angular/material/select';
import { MatTooltip } from '@angular/material/tooltip';
import { InitialReportDocumentation } from '@contractor-dashboard/models/initial-report-documentation.model';
import {
  fileSizeValidator,
  pdfFileValidator,
} from '@shared/validators/file.validators';
import { forkJoin } from 'rxjs';

@Component({
  selector: 'app-social-security-info',
  templateUrl: './social-security-info.component.html',
  styleUrl: './social-security-info.component.scss',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatIcon,
    MatFormField,
    MatLabel,
    MatSelect,
    MatOption,
    MatInput,
    MatError,
    MatButton,
    MatIconButton,
    MatTooltip,
  ],
})
export class SocialSecurityInfoComponent implements OnInit, OnChanges {
  @Input() initialData?: InitialReportDocumentation;
  @Input() isSupervisor = false;
  @Output() formChange = new EventEmitter<void>();

  form: FormGroup = this.fb.group({
    eps: ['', Validators.required],
    epsSupportFile: [null, [fileSizeValidator(), pdfFileValidator()]],
    arl: ['', Validators.required],
    arlSupportFile: [null, [fileSizeValidator(), pdfFileValidator()]],
    pensionFund: ['', Validators.required],
    pensionFundSupportFile: [null, [fileSizeValidator(), pdfFileValidator()]],
  });

  epsList: Eps[] = [];
  arlList: ARL[] = [];
  pensionFundList: PensionFund[] = [];

  epsCertificateFile: File | null = null;
  arlCertificateFile: File | null = null;
  pensionCertificateFile: File | null = null;

  epsCertificateFileName: string | null = null;
  arlCertificateFileName: string | null = null;
  pensionCertificateFileName: string | null = null;

  constructor(
    private readonly fb: FormBuilder,
    private readonly epsService: EpsService,
    private readonly arlService: ArlService,
    private readonly pensionFundService: PensionFundService,
    private readonly alert: AlertService,
  ) {}

  ngOnInit(): void {
    forkJoin({
      eps: this.epsService.getAll(),
      arl: this.arlService.getAll(),
      pensionFund: this.pensionFundService.getAll(),
    }).subscribe({
      next: ({ eps, arl, pensionFund }) => {
        this.epsList = eps;
        this.arlList = arl;
        this.pensionFundList = pensionFund;
      },
      error: (error) => {
        this.alert.error(error.error?.detail ?? 'Error al cargar los datos del formulario');
      },
    });

    this.form.valueChanges.subscribe(() => {
      this.formChange.emit();
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['initialData']?.currentValue) {
      const data = changes['initialData'].currentValue;
      this.form.patchValue({
        eps: data.epsId,
        arl: data.arlId,
        pensionFund: data.pensionFundId,
      });
    }
  }

  isFileValid(controlName: string): boolean {
    const hasNewFile = Boolean(this.form.get(controlName)?.value);
    const hasExistingFile =
      this.initialData &&
      ((controlName === 'epsSupportFile' &&
        this.initialData.epsCertificateFileUrl) ||
        (controlName === 'arlSupportFile' &&
          this.initialData.arlCertificateFileUrl) ||
        (controlName === 'pensionFundSupportFile' &&
          this.initialData.pensionCertificateFileUrl));
    return hasNewFile || Boolean(hasExistingFile);
  }

  get isValid(): boolean {
    const basicFieldsValid = Boolean(
      this.form.get('eps')?.valid &&
        this.form.get('arl')?.valid &&
        this.form.get('pensionFund')?.valid,
    );

    const epsFileValid = Boolean(
      this.form.get('epsSupportFile')?.value ||
        this.initialData?.epsCertificateFileUrl,
    );

    const arlFileValid = Boolean(
      this.form.get('arlSupportFile')?.value ||
        this.initialData?.arlCertificateFileUrl,
    );

    const pensionFileValid = Boolean(
      this.form.get('pensionFundSupportFile')?.value ||
        this.initialData?.pensionCertificateFileUrl,
    );

    const isValid =
      basicFieldsValid && epsFileValid && arlFileValid && pensionFileValid;

    return isValid;
  }

  onFileSelected(event: Event, controlName: string): void {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (file) {
      if (this.isPdfFile(file)) {
        if (file.size <= 1024 * 1024) {
          this.form.patchValue({ [controlName]: file });
          switch (controlName) {
            case 'epsSupportFile':
              this.epsCertificateFile = file;
              this.epsCertificateFileName = file.name;
              break;
            case 'arlSupportFile':
              this.arlCertificateFile = file;
              this.arlCertificateFileName = file.name;
              break;
            case 'pensionFundSupportFile':
              this.pensionCertificateFile = file;
              this.pensionCertificateFileName = file.name;
              break;
          }
        } else {
          this.alert.error('El archivo no debe superar 1MB');
        }
      } else {
        this.alert.error('Solo se permiten archivos PDF');
      }
      this.form.get(controlName)?.updateValueAndValidity();
    }
  }

  downloadFile(fileType: 'eps' | 'arl' | 'pension'): void {
    switch (fileType) {
      case 'eps':
        if (this.initialData?.epsCertificateFileUrl) {
          window.open(this.initialData.epsCertificateFileUrl, '_blank');
        }
        break;
      case 'arl':
        if (this.initialData?.arlCertificateFileUrl) {
          window.open(this.initialData.arlCertificateFileUrl, '_blank');
        }
        break;
      case 'pension':
        if (this.initialData?.pensionCertificateFileUrl) {
          window.open(this.initialData.pensionCertificateFileUrl, '_blank');
        }
        break;
    }
  }

  private isPdfFile(file: File): boolean {
    return file.type === 'application/pdf';
  }

  getEpsName(id: number): string {
    return this.epsList.find((eps) => eps.id === id)?.name || '';
  }

  getArlName(id: number): string {
    return this.arlList.find((arl) => arl.id === id)?.name || '';
  }

  getPensionFundName(id: number): string {
    return this.pensionFundList.find((fund) => fund.id === id)?.name || '';
  }
}
