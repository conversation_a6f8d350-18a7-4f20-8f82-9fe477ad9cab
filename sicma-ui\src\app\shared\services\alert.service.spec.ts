import { TestBed } from '@angular/core/testing';
import Swal, { SweetAlertResult } from 'sweetalert2';
import { AlertService } from './alert.service';

describe('AlertService', () => {
  let service: AlertService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [AlertService],
    });
    service = TestBed.inject(AlertService);
    spyOn(Swal, 'fire').and.returnValue(
      Promise.resolve({ isConfirmed: true } as SweetAlertResult<unknown>),
    );
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('success', () => {
    it('should call Swal.fire with success parameters', () => {
      const title = 'Success Title';
      const text = 'Success Message';

      service.success(title, text);

      expect(Swal.fire).toHaveBeenCalledWith(
        jasmine.objectContaining({
          title,
          text,
          icon: 'success',
          timer: 3000,
          position: 'top-end',
          showConfirmButton: false,
        }),
      );
    });
  });

  describe('info', () => {
    it('should call Swal.fire with info parameters', () => {
      const title = 'Info Title';
      const text = 'Info Message';

      service.info(title, text);

      expect(Swal.fire).toHaveBeenCalledWith(
        jasmine.objectContaining({
          title,
          text,
          icon: 'info',
          position: 'center',
          confirmButtonColor: '#5f9af3',
        }),
      );
    });
  });

  describe('warning', () => {
    it('should call Swal.fire with warning parameters', () => {
      const title = 'Warning Title';
      const text = 'Warning Message';

      service.warning(title, text);

      expect(Swal.fire).toHaveBeenCalledWith(
        jasmine.objectContaining({
          title,
          text,
          icon: 'warning',
          position: 'center',
          confirmButtonColor: '#5f9af3',
        }),
      );
    });
  });

  describe('error', () => {
    it('should call Swal.fire with error parameters and default text', () => {
      const title = 'Error Title';

      service.error(title);

      expect(Swal.fire).toHaveBeenCalledWith(
        jasmine.objectContaining({
          title,
          text: 'Por favor, intente nuevamente.',
          icon: 'error',
          position: 'center',
          confirmButtonColor: '#5f9af3',
        }),
      );
    });

    it('should call Swal.fire with error parameters and custom text', () => {
      const title = 'Error Title';
      const text = 'Custom Error Message';

      service.error(title, text);

      expect(Swal.fire).toHaveBeenCalledWith(
        jasmine.objectContaining({
          title,
          text,
          icon: 'error',
          position: 'center',
          confirmButtonColor: '#5f9af3',
        }),
      );
    });
  });

  describe('confirm', () => {
    it('should call Swal.fire with confirm parameters and return true when confirmed', async () => {
      const title = 'Confirm Title';
      const text = 'Custom Confirm Message';

      const result = await service.confirm(title, text);

      expect(Swal.fire).toHaveBeenCalledWith(
        jasmine.objectContaining({
          title,
          text,
          icon: 'question',
          showCancelButton: true,
          confirmButtonText: 'Sí',
          cancelButtonText: 'No',
          confirmButtonColor: '#5f9af3',
          cancelButtonColor: '#d33',
        }),
      );
      expect(result).toBe(true);
    });

    it('should call Swal.fire with confirm parameters and default text', async () => {
      const title = 'Confirm Title';

      const result = await service.confirm(title);

      expect(Swal.fire).toHaveBeenCalledWith(
        jasmine.objectContaining({
          title,
          text: 'Esta acción no se puede deshacer',
          icon: 'question',
          showCancelButton: true,
          confirmButtonText: 'Sí',
          cancelButtonText: 'No',
          confirmButtonColor: '#5f9af3',
          cancelButtonColor: '#d33',
        }),
      );
      expect(result).toBe(true);
    });

    it('should return false when confirmation is cancelled', async () => {
      (Swal.fire as jasmine.Spy).and.returnValue(
        Promise.resolve({ isConfirmed: false } as SweetAlertResult<unknown>),
      );
      const title = 'Confirm Title';

      const result = await service.confirm(title);

      expect(result).toBe(false);
    });
  });
});