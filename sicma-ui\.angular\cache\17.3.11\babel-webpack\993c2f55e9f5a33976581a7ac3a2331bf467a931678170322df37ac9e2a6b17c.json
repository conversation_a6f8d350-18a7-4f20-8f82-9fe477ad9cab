{"ast": null, "code": "import { FormBuilder } from '@angular/forms';\nimport { createDateValidator } from './date.validator';\ndescribe('DateValidator', () => {\n  let fb;\n  let form;\n  let config;\n  beforeEach(() => {\n    fb = new FormBuilder();\n    form = fb.group({\n      startDate: [null],\n      endDate: [null]\n    });\n    config = {\n      lastSuspensionEndDate: null,\n      contractStartDate: null,\n      contractEndDate: null,\n      suspensionForm: form,\n      isEdit: false\n    };\n  });\n  it('should return required error when value is null', () => {\n    const validator = createDateValidator(config);\n    const result = validator(form.get('startDate'));\n    expect(result).toEqual({\n      required: true\n    });\n  });\n  describe('Start Date Validation', () => {\n    it('should return startDateBeforeLastSuspension error when start date is before last suspension', () => {\n      config.lastSuspensionEndDate = new Date('2024-06-01');\n      const validator = createDateValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-05-01'));\n      const result = validator(form.get('startDate'));\n      expect(result).toEqual({\n        startDateBeforeLastSuspension: true\n      });\n    });\n    it('should not return startDateBeforeLastSuspension error in edit mode', () => {\n      config.lastSuspensionEndDate = new Date('2024-06-01');\n      config.isEdit = true;\n      const validator = createDateValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-05-01'));\n      const result = validator(form.get('startDate'));\n      expect(result).not.toEqual({\n        startDateBeforeLastSuspension: true\n      });\n    });\n    it('should return startDateBeforeContractStart error when start date is before contract start', () => {\n      config.contractStartDate = new Date('2024-06-01');\n      const validator = createDateValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-05-01'));\n      const result = validator(form.get('startDate'));\n      expect(result).toEqual({\n        startDateBeforeContractStart: true\n      });\n    });\n    it('should return null when start date is valid', () => {\n      config.contractStartDate = new Date('2024-06-01');\n      config.lastSuspensionEndDate = new Date('2024-06-15');\n      const validator = createDateValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-07-01'));\n      const result = validator(form.get('startDate'));\n      expect(result).toBeNull();\n    });\n  });\n  describe('End Date Validation', () => {\n    it('should return endDateBeforeStartDate error when end date is before start date', () => {\n      const validator = createDateValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-06-01'));\n      form.get('endDate')?.setValue(new Date('2024-05-01'));\n      const result = validator(form.get('endDate'));\n      expect(result).toEqual({\n        endDateBeforeStartDate: true\n      });\n    });\n    it('should return endDateAfterContractEnd error when end date is after contract end', () => {\n      config.contractEndDate = new Date('2024-12-31');\n      const validator = createDateValidator(config);\n      form.get('endDate')?.setValue(new Date('2025-01-02'));\n      const result = validator(form.get('endDate'));\n      expect(result).toEqual({\n        endDateAfterContractEnd: true\n      });\n    });\n    it('should return null when end date is valid', () => {\n      config.contractEndDate = new Date('2024-12-31');\n      const validator = createDateValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-06-01'));\n      form.get('endDate')?.setValue(new Date('2024-07-01'));\n      const result = validator(form.get('endDate'));\n      expect(result).toBeNull();\n    });\n  });\n  describe('Edge Cases', () => {\n    it('should handle undefined contract dates', () => {\n      config.contractStartDate = undefined;\n      config.contractEndDate = undefined;\n      const validator = createDateValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-06-01'));\n      form.get('endDate')?.setValue(new Date('2024-07-01'));\n      expect(validator(form.get('startDate'))).toBeNull();\n      expect(validator(form.get('endDate'))).toBeNull();\n    });\n    it('should handle null contract dates', () => {\n      config.contractStartDate = null;\n      config.contractEndDate = null;\n      const validator = createDateValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-06-01'));\n      form.get('endDate')?.setValue(new Date('2024-07-01'));\n      expect(validator(form.get('startDate'))).toBeNull();\n      expect(validator(form.get('endDate'))).toBeNull();\n    });\n  });\n});", "map": {"version": 3, "names": ["FormBuilder", "createDateValidator", "describe", "fb", "form", "config", "beforeEach", "group", "startDate", "endDate", "lastSuspensionEndDate", "contractStartDate", "contractEndDate", "suspensionForm", "isEdit", "it", "validator", "result", "get", "expect", "toEqual", "required", "Date", "setValue", "startDateBeforeLastSuspension", "not", "startDateBeforeContractStart", "toBeNull", "endDateBeforeStartDate", "endDateAfterContractEnd", "undefined"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\components\\suspensions-list\\suspension-dialog\\validators\\date.validator.spec.ts"], "sourcesContent": ["import { FormBuilder, FormGroup } from '@angular/forms';\nimport { createDateValidator, DateValidatorConfig } from './date.validator';\n\ndescribe('DateValidator', () => {\n  let fb: FormBuilder;\n  let form: FormGroup;\n  let config: DateValidatorConfig;\n\n  beforeEach(() => {\n    fb = new FormBuilder();\n    form = fb.group({\n      startDate: [null],\n      endDate: [null],\n    });\n\n    config = {\n      lastSuspensionEndDate: null,\n      contractStartDate: null,\n      contractEndDate: null,\n      suspensionForm: form,\n      isEdit: false,\n    };\n  });\n\n  it('should return required error when value is null', () => {\n    const validator = createDateValidator(config);\n    const result = validator(form.get('startDate')!);\n    expect(result).toEqual({ required: true });\n  });\n\n  describe('Start Date Validation', () => {\n    it('should return startDateBeforeLastSuspension error when start date is before last suspension', () => {\n      config.lastSuspensionEndDate = new Date('2024-06-01');\n      const validator = createDateValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-05-01'));\n      const result = validator(form.get('startDate')!);\n      expect(result).toEqual({ startDateBeforeLastSuspension: true });\n    });\n\n    it('should not return startDateBeforeLastSuspension error in edit mode', () => {\n      config.lastSuspensionEndDate = new Date('2024-06-01');\n      config.isEdit = true;\n      const validator = createDateValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-05-01'));\n      const result = validator(form.get('startDate')!);\n      expect(result).not.toEqual({ startDateBeforeLastSuspension: true });\n    });\n\n    it('should return startDateBeforeContractStart error when start date is before contract start', () => {\n      config.contractStartDate = new Date('2024-06-01');\n      const validator = createDateValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-05-01'));\n      const result = validator(form.get('startDate')!);\n      expect(result).toEqual({ startDateBeforeContractStart: true });\n    });\n\n    it('should return null when start date is valid', () => {\n      config.contractStartDate = new Date('2024-06-01');\n      config.lastSuspensionEndDate = new Date('2024-06-15');\n      const validator = createDateValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-07-01'));\n      const result = validator(form.get('startDate')!);\n      expect(result).toBeNull();\n    });\n  });\n\n  describe('End Date Validation', () => {\n    it('should return endDateBeforeStartDate error when end date is before start date', () => {\n      const validator = createDateValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-06-01'));\n      form.get('endDate')?.setValue(new Date('2024-05-01'));\n      const result = validator(form.get('endDate')!);\n      expect(result).toEqual({ endDateBeforeStartDate: true });\n    });\n\n    it('should return endDateAfterContractEnd error when end date is after contract end', () => {\n      config.contractEndDate = new Date('2024-12-31');\n      const validator = createDateValidator(config);\n      form.get('endDate')?.setValue(new Date('2025-01-02'));\n      const result = validator(form.get('endDate')!);\n      expect(result).toEqual({ endDateAfterContractEnd: true });\n    });\n\n    it('should return null when end date is valid', () => {\n      config.contractEndDate = new Date('2024-12-31');\n      const validator = createDateValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-06-01'));\n      form.get('endDate')?.setValue(new Date('2024-07-01'));\n      const result = validator(form.get('endDate')!);\n      expect(result).toBeNull();\n    });\n  });\n\n  describe('Edge Cases', () => {\n    it('should handle undefined contract dates', () => {\n      config.contractStartDate = undefined;\n      config.contractEndDate = undefined;\n      const validator = createDateValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-06-01'));\n      form.get('endDate')?.setValue(new Date('2024-07-01'));\n      expect(validator(form.get('startDate')!)).toBeNull();\n      expect(validator(form.get('endDate')!)).toBeNull();\n    });\n\n    it('should handle null contract dates', () => {\n      config.contractStartDate = null;\n      config.contractEndDate = null;\n      const validator = createDateValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-06-01'));\n      form.get('endDate')?.setValue(new Date('2024-07-01'));\n      expect(validator(form.get('startDate')!)).toBeNull();\n      expect(validator(form.get('endDate')!)).toBeNull();\n    });\n  });\n});"], "mappings": "AAAA,SAASA,WAAW,QAAmB,gBAAgB;AACvD,SAASC,mBAAmB,QAA6B,kBAAkB;AAE3EC,QAAQ,CAAC,eAAe,EAAE,MAAK;EAC7B,IAAIC,EAAe;EACnB,IAAIC,IAAe;EACnB,IAAIC,MAA2B;EAE/BC,UAAU,CAAC,MAAK;IACdH,EAAE,GAAG,IAAIH,WAAW,EAAE;IACtBI,IAAI,GAAGD,EAAE,CAACI,KAAK,CAAC;MACdC,SAAS,EAAE,CAAC,IAAI,CAAC;MACjBC,OAAO,EAAE,CAAC,IAAI;KACf,CAAC;IAEFJ,MAAM,GAAG;MACPK,qBAAqB,EAAE,IAAI;MAC3BC,iBAAiB,EAAE,IAAI;MACvBC,eAAe,EAAE,IAAI;MACrBC,cAAc,EAAET,IAAI;MACpBU,MAAM,EAAE;KACT;EACH,CAAC,CAAC;EAEFC,EAAE,CAAC,iDAAiD,EAAE,MAAK;IACzD,MAAMC,SAAS,GAAGf,mBAAmB,CAACI,MAAM,CAAC;IAC7C,MAAMY,MAAM,GAAGD,SAAS,CAACZ,IAAI,CAACc,GAAG,CAAC,WAAW,CAAE,CAAC;IAChDC,MAAM,CAACF,MAAM,CAAC,CAACG,OAAO,CAAC;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;EAC5C,CAAC,CAAC;EAEFnB,QAAQ,CAAC,uBAAuB,EAAE,MAAK;IACrCa,EAAE,CAAC,6FAA6F,EAAE,MAAK;MACrGV,MAAM,CAACK,qBAAqB,GAAG,IAAIY,IAAI,CAAC,YAAY,CAAC;MACrD,MAAMN,SAAS,GAAGf,mBAAmB,CAACI,MAAM,CAAC;MAC7CD,IAAI,CAACc,GAAG,CAAC,WAAW,CAAC,EAAEK,QAAQ,CAAC,IAAID,IAAI,CAAC,YAAY,CAAC,CAAC;MACvD,MAAML,MAAM,GAAGD,SAAS,CAACZ,IAAI,CAACc,GAAG,CAAC,WAAW,CAAE,CAAC;MAChDC,MAAM,CAACF,MAAM,CAAC,CAACG,OAAO,CAAC;QAAEI,6BAA6B,EAAE;MAAI,CAAE,CAAC;IACjE,CAAC,CAAC;IAEFT,EAAE,CAAC,oEAAoE,EAAE,MAAK;MAC5EV,MAAM,CAACK,qBAAqB,GAAG,IAAIY,IAAI,CAAC,YAAY,CAAC;MACrDjB,MAAM,CAACS,MAAM,GAAG,IAAI;MACpB,MAAME,SAAS,GAAGf,mBAAmB,CAACI,MAAM,CAAC;MAC7CD,IAAI,CAACc,GAAG,CAAC,WAAW,CAAC,EAAEK,QAAQ,CAAC,IAAID,IAAI,CAAC,YAAY,CAAC,CAAC;MACvD,MAAML,MAAM,GAAGD,SAAS,CAACZ,IAAI,CAACc,GAAG,CAAC,WAAW,CAAE,CAAC;MAChDC,MAAM,CAACF,MAAM,CAAC,CAACQ,GAAG,CAACL,OAAO,CAAC;QAAEI,6BAA6B,EAAE;MAAI,CAAE,CAAC;IACrE,CAAC,CAAC;IAEFT,EAAE,CAAC,2FAA2F,EAAE,MAAK;MACnGV,MAAM,CAACM,iBAAiB,GAAG,IAAIW,IAAI,CAAC,YAAY,CAAC;MACjD,MAAMN,SAAS,GAAGf,mBAAmB,CAACI,MAAM,CAAC;MAC7CD,IAAI,CAACc,GAAG,CAAC,WAAW,CAAC,EAAEK,QAAQ,CAAC,IAAID,IAAI,CAAC,YAAY,CAAC,CAAC;MACvD,MAAML,MAAM,GAAGD,SAAS,CAACZ,IAAI,CAACc,GAAG,CAAC,WAAW,CAAE,CAAC;MAChDC,MAAM,CAACF,MAAM,CAAC,CAACG,OAAO,CAAC;QAAEM,4BAA4B,EAAE;MAAI,CAAE,CAAC;IAChE,CAAC,CAAC;IAEFX,EAAE,CAAC,6CAA6C,EAAE,MAAK;MACrDV,MAAM,CAACM,iBAAiB,GAAG,IAAIW,IAAI,CAAC,YAAY,CAAC;MACjDjB,MAAM,CAACK,qBAAqB,GAAG,IAAIY,IAAI,CAAC,YAAY,CAAC;MACrD,MAAMN,SAAS,GAAGf,mBAAmB,CAACI,MAAM,CAAC;MAC7CD,IAAI,CAACc,GAAG,CAAC,WAAW,CAAC,EAAEK,QAAQ,CAAC,IAAID,IAAI,CAAC,YAAY,CAAC,CAAC;MACvD,MAAML,MAAM,GAAGD,SAAS,CAACZ,IAAI,CAACc,GAAG,CAAC,WAAW,CAAE,CAAC;MAChDC,MAAM,CAACF,MAAM,CAAC,CAACU,QAAQ,EAAE;IAC3B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFzB,QAAQ,CAAC,qBAAqB,EAAE,MAAK;IACnCa,EAAE,CAAC,+EAA+E,EAAE,MAAK;MACvF,MAAMC,SAAS,GAAGf,mBAAmB,CAACI,MAAM,CAAC;MAC7CD,IAAI,CAACc,GAAG,CAAC,WAAW,CAAC,EAAEK,QAAQ,CAAC,IAAID,IAAI,CAAC,YAAY,CAAC,CAAC;MACvDlB,IAAI,CAACc,GAAG,CAAC,SAAS,CAAC,EAAEK,QAAQ,CAAC,IAAID,IAAI,CAAC,YAAY,CAAC,CAAC;MACrD,MAAML,MAAM,GAAGD,SAAS,CAACZ,IAAI,CAACc,GAAG,CAAC,SAAS,CAAE,CAAC;MAC9CC,MAAM,CAACF,MAAM,CAAC,CAACG,OAAO,CAAC;QAAEQ,sBAAsB,EAAE;MAAI,CAAE,CAAC;IAC1D,CAAC,CAAC;IAEFb,EAAE,CAAC,iFAAiF,EAAE,MAAK;MACzFV,MAAM,CAACO,eAAe,GAAG,IAAIU,IAAI,CAAC,YAAY,CAAC;MAC/C,MAAMN,SAAS,GAAGf,mBAAmB,CAACI,MAAM,CAAC;MAC7CD,IAAI,CAACc,GAAG,CAAC,SAAS,CAAC,EAAEK,QAAQ,CAAC,IAAID,IAAI,CAAC,YAAY,CAAC,CAAC;MACrD,MAAML,MAAM,GAAGD,SAAS,CAACZ,IAAI,CAACc,GAAG,CAAC,SAAS,CAAE,CAAC;MAC9CC,MAAM,CAACF,MAAM,CAAC,CAACG,OAAO,CAAC;QAAES,uBAAuB,EAAE;MAAI,CAAE,CAAC;IAC3D,CAAC,CAAC;IAEFd,EAAE,CAAC,2CAA2C,EAAE,MAAK;MACnDV,MAAM,CAACO,eAAe,GAAG,IAAIU,IAAI,CAAC,YAAY,CAAC;MAC/C,MAAMN,SAAS,GAAGf,mBAAmB,CAACI,MAAM,CAAC;MAC7CD,IAAI,CAACc,GAAG,CAAC,WAAW,CAAC,EAAEK,QAAQ,CAAC,IAAID,IAAI,CAAC,YAAY,CAAC,CAAC;MACvDlB,IAAI,CAACc,GAAG,CAAC,SAAS,CAAC,EAAEK,QAAQ,CAAC,IAAID,IAAI,CAAC,YAAY,CAAC,CAAC;MACrD,MAAML,MAAM,GAAGD,SAAS,CAACZ,IAAI,CAACc,GAAG,CAAC,SAAS,CAAE,CAAC;MAC9CC,MAAM,CAACF,MAAM,CAAC,CAACU,QAAQ,EAAE;IAC3B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFzB,QAAQ,CAAC,YAAY,EAAE,MAAK;IAC1Ba,EAAE,CAAC,wCAAwC,EAAE,MAAK;MAChDV,MAAM,CAACM,iBAAiB,GAAGmB,SAAS;MACpCzB,MAAM,CAACO,eAAe,GAAGkB,SAAS;MAClC,MAAMd,SAAS,GAAGf,mBAAmB,CAACI,MAAM,CAAC;MAC7CD,IAAI,CAACc,GAAG,CAAC,WAAW,CAAC,EAAEK,QAAQ,CAAC,IAAID,IAAI,CAAC,YAAY,CAAC,CAAC;MACvDlB,IAAI,CAACc,GAAG,CAAC,SAAS,CAAC,EAAEK,QAAQ,CAAC,IAAID,IAAI,CAAC,YAAY,CAAC,CAAC;MACrDH,MAAM,CAACH,SAAS,CAACZ,IAAI,CAACc,GAAG,CAAC,WAAW,CAAE,CAAC,CAAC,CAACS,QAAQ,EAAE;MACpDR,MAAM,CAACH,SAAS,CAACZ,IAAI,CAACc,GAAG,CAAC,SAAS,CAAE,CAAC,CAAC,CAACS,QAAQ,EAAE;IACpD,CAAC,CAAC;IAEFZ,EAAE,CAAC,mCAAmC,EAAE,MAAK;MAC3CV,MAAM,CAACM,iBAAiB,GAAG,IAAI;MAC/BN,MAAM,CAACO,eAAe,GAAG,IAAI;MAC7B,MAAMI,SAAS,GAAGf,mBAAmB,CAACI,MAAM,CAAC;MAC7CD,IAAI,CAACc,GAAG,CAAC,WAAW,CAAC,EAAEK,QAAQ,CAAC,IAAID,IAAI,CAAC,YAAY,CAAC,CAAC;MACvDlB,IAAI,CAACc,GAAG,CAAC,SAAS,CAAC,EAAEK,QAAQ,CAAC,IAAID,IAAI,CAAC,YAAY,CAAC,CAAC;MACrDH,MAAM,CAACH,SAAS,CAACZ,IAAI,CAACc,GAAG,CAAC,WAAW,CAAE,CAAC,CAAC,CAACS,QAAQ,EAAE;MACpDR,MAAM,CAACH,SAAS,CAACZ,IAAI,CAACc,GAAG,CAAC,SAAS,CAAE,CAAC,CAAC,CAACS,QAAQ,EAAE;IACpD,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}