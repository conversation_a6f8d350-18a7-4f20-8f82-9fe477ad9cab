import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { Profession } from '@contractor-management/models/profession.model';
import { environment } from '@env';
import { ProfessionService } from './profession.service';

describe('ProfessionService', () => {
  let service: ProfessionService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/professions`;

  const mockProfession: Profession = {
    id: 1,
    name: 'Test Profession',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ProfessionService],
    });
    service = TestBed.inject(ProfessionService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all professions', () => {
      const mockProfessions = [mockProfession];

      service.getAll().subscribe((professions) => {
        expect(professions).toEqual(mockProfessions);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockProfessions);
    });
  });

  describe('getById', () => {
    it('should return a profession by id', () => {
      const id = 1;

      service.getById(id).subscribe((profession) => {
        expect(profession).toEqual(mockProfession);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockProfession);
    });
  });

  describe('create', () => {
    it('should create a new profession', () => {
      const newProfession: Omit<Profession, 'id'> = {
        name: 'New Profession',
      };

      service.create(newProfession).subscribe((profession) => {
        expect(profession).toEqual(mockProfession);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newProfession);
      req.flush(mockProfession);
    });
  });

  describe('update', () => {
    it('should update a profession', () => {
      const id = 1;
      const updateData: Partial<Profession> = {
        name: 'Updated Profession',
      };

      service.update(id, updateData).subscribe((profession) => {
        expect(profession).toEqual(mockProfession);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockProfession);
    });
  });

  describe('delete', () => {
    it('should delete a profession', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });

  describe('getByName', () => {
    it('should return a profession by name', () => {
      const name = 'Test Profession';

      service.getByName(name).subscribe((profession) => {
        expect(profession).toEqual(mockProfession);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockProfession);
    });
  });
});