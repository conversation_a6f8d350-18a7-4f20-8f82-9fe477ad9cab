import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { InitialReportDocumentation } from '@contractor-dashboard/models/initial-report-documentation.model';
import { AlertService } from '@shared/services/alert.service';
import { TaxBenefitsComponent } from './tax-benefits.component';

describe('TaxBenefitsComponent', () => {
  let component: TaxBenefitsComponent;
  let fixture: ComponentFixture<TaxBenefitsComponent>;
  let alertService: jasmine.SpyObj<AlertService>;

  const mockInitialData: InitialReportDocumentation = {
    id: 1,
    contractorContractId: 1,
    bankId: 1,
    accountNumber: '123456',
    bankAccountTypeId: 1,
    taxRegimeId: 1,
    epsId: 1,
    arlId: 1,
    pensionFundId: 1,
    hasDependents: true,
    hasHousingInterest: true,
    housingInterestAnnualPayment: 1000000,
    hasPrepaidMedicine: true,
    prepaidMedicineAnnualPayment: 500000,
    hasAfcAccount: true,
    hasVoluntarySavings: true,
    afcAccountAnnualPayment: 300000,
    voluntarySavingsAnnualPayment: 200000,
  };

  beforeEach(() => {
    const alertServiceSpy = jasmine.createSpyObj('AlertService', ['error']);

    TestBed.configureTestingModule({
      imports: [
        TaxBenefitsComponent,
        ReactiveFormsModule,
        BrowserAnimationsModule,
      ],
      providers: [
        FormBuilder,
        { provide: AlertService, useValue: alertServiceSpy },
      ],
    });

    fixture = TestBed.createComponent(TaxBenefitsComponent);
    component = fixture.componentInstance;
    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Form Initialization', () => {
    it('should initialize form with default values', () => {
      expect(component.form.get('hasDependents')?.value).toBeFalse();
      expect(component.form.get('hasHousingInterest')?.value).toBeFalse();
      expect(component.form.get('hasPrepaidMedicine')?.value).toBeFalse();
      expect(component.form.get('hasAfcAccount')?.value).toBeFalse();
      expect(component.form.get('hasVoluntarySavings')?.value).toBeFalse();
    });

    it('should initialize form with disabled payment controls', () => {
      expect(
        component.form.get('housingInterestAnnualPayment')?.disabled,
      ).toBeTrue();
      expect(
        component.form.get('prepaidMedicineAnnualPayment')?.disabled,
      ).toBeTrue();
      expect(
        component.form.get('afcAccountAnnualPayment')?.disabled,
      ).toBeTrue();
      expect(
        component.form.get('voluntarySavingsAnnualPayment')?.disabled,
      ).toBeTrue();
    });

    it('should disable controls in supervisor mode', () => {
      component.isSupervisor = true;
      component.ngOnChanges({
        isSupervisor: {
          currentValue: true,
          previousValue: false,
          firstChange: false,
          isFirstChange: () => false,
        },
      });

      expect(component.form.get('hasDependents')?.disabled).toBeTrue();
      expect(component.form.get('hasHousingInterest')?.disabled).toBeTrue();
      expect(component.form.get('hasPrepaidMedicine')?.disabled).toBeTrue();
      expect(component.form.get('hasAfcAccount')?.disabled).toBeTrue();
      expect(component.form.get('hasVoluntarySavings')?.disabled).toBeTrue();
    });
  });

  describe('Form Data Loading', () => {
    it('should load initial data correctly', () => {
      component.initialData = mockInitialData;
      component.ngOnChanges({
        initialData: {
          currentValue: mockInitialData,
          previousValue: undefined,
          firstChange: true,
          isFirstChange: () => true,
        },
      });

      expect(component.form.get('hasDependents')?.value).toBeTrue();
      expect(component.form.get('hasHousingInterest')?.value).toBeTrue();
      expect(component.form.get('housingInterestAnnualPayment')?.value).toBe(
        1000000,
      );
      expect(component.form.get('hasPrepaidMedicine')?.value).toBeTrue();
      expect(component.form.get('prepaidMedicineAnnualPayment')?.value).toBe(
        500000,
      );
      expect(component.form.get('hasAfcAccount')?.value).toBeTrue();
      expect(component.form.get('afcAccountAnnualPayment')?.value).toBe(300000);
      expect(component.form.get('hasVoluntarySavings')?.value).toBeTrue();
      expect(component.form.get('voluntarySavingsAnnualPayment')?.value).toBe(
        200000,
      );
    });
  });

  describe('Form Validation', () => {
    it('should validate payment amounts when enabled', () => {
      component.form.get('hasHousingInterest')?.setValue(true);
      component.form.get('housingInterestAnnualPayment')?.setValue(-1);
      expect(
        component.form.get('housingInterestAnnualPayment')?.errors?.['min'],
      ).toBeTruthy();

      component.form.get('housingInterestAnnualPayment')?.setValue(1000);
      expect(
        component.form.get('housingInterestAnnualPayment')?.valid,
      ).toBeTrue();
    });

    it('should clear and disable payment amounts when checkbox is unchecked', () => {
      component.form.get('hasHousingInterest')?.setValue(true);
      component.form.get('housingInterestAnnualPayment')?.setValue(1000);

      component.form.get('hasHousingInterest')?.setValue(false);
      expect(
        component.form.get('housingInterestAnnualPayment')?.disabled,
      ).toBeTrue();
      expect(
        component.form.get('housingInterestAnnualPayment')?.value,
      ).toBeNull();
    });
  });

  describe('File Handling', () => {
    it('should validate PDF file type', () => {
      const pdfFile = new File([''], 'test.pdf', { type: 'application/pdf' });
      const event = { target: { files: [pdfFile] } } as unknown as Event;

      component.onFileSelected(event, 'dependents');
      expect(component.fileNames.dependents).toBe('test.pdf');
      expect(alertService.error).not.toHaveBeenCalled();
    });

    it('should reject non-PDF files', () => {
      const jpgFile = new File([''], 'test.jpg', { type: 'image/jpeg' });
      const event = { target: { files: [jpgFile] } } as unknown as Event;

      component.onFileSelected(event, 'dependents');
      expect(component.fileNames.dependents).toBe('');
      expect(alertService.error).toHaveBeenCalledWith(
        'Solo se permiten archivos PDF',
      );
    });

    it('should reject files larger than 1MB', () => {
      const largeFile = new File(['x'.repeat(1024 * 1024 + 1)], 'large.pdf', {
        type: 'application/pdf',
      });
      const event = { target: { files: [largeFile] } } as unknown as Event;

      component.onFileSelected(event, 'dependents');
      expect(component.fileNames.dependents).toBe('');
      expect(alertService.error).toHaveBeenCalledWith(
        'El archivo no debe superar 1MB',
      );
    });
  });

  describe('Form Changes', () => {
    it('should emit form changes', () => {
      const emitSpy = spyOn(component.formChange, 'emit');
      component.form.get('hasDependents')?.setValue(true);
      expect(emitSpy).toHaveBeenCalled();
    });
  });

  describe('Form Validity', () => {
    it('should validate form when all required fields are filled', () => {
      component.form.get('hasDependents')?.setValue(true);
      const pdfFile = new File([''], 'test.pdf', { type: 'application/pdf' });
      component.form.get('dependentsFile')?.setValue(pdfFile);

      expect(component.isValid).toBeTrue();
    });

    it('should invalidate form when required files are missing', () => {
      component.form.get('hasDependents')?.setValue(true);
      expect(component.isValid).toBeFalse();
    });
  });
});