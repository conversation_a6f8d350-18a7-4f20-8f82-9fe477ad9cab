import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ViewChild,
} from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatStepper, MatStepperModule } from '@angular/material/stepper';

import { Contract } from '@contract-management/models/contract.model';
import { ContractContractor } from '@contract-management/models/contract_contractor.model';
import { ContractService } from '@contract-management/services/contract.service';
import { ContractorContractService } from '@contract-management/services/contractor-contract.service';
import { AlertService } from '@shared/services/alert.service';

import { ContractDetailFormComponent } from '../contract-detail-form/contract-detail-form.component';
import { ContractValuesFormComponent } from '../contract-values-form/contract-values-form.component';
import { ContractorDetailFormComponent } from '../contractor-detail-form/contractor-detail-form.component';

@Component({
  selector: 'app-contract-dialog',
  templateUrl: 'contract-dialog.component.html',
  styleUrl: 'contract-dialog.component.scss',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatSelectModule,
    MatAutocompleteModule,
    MatStepperModule,
    ContractDetailFormComponent,
    ContractValuesFormComponent,
    ContractorDetailFormComponent,
  ],
})
export class ContractDialogComponent implements AfterViewInit {
  @ViewChild(MatStepper) stepper!: MatStepper;
  @ViewChild(ContractDetailFormComponent)
  contractDetailForm!: ContractDetailFormComponent;
  @ViewChild(ContractValuesFormComponent)
  contractValuesForm!: ContractValuesFormComponent;
  @ViewChild(ContractorDetailFormComponent)
  contractorDetailForm!: ContractorDetailFormComponent;

  constructor(
    private readonly dialogRef: MatDialogRef<ContractDialogComponent>,
    private readonly contractService: ContractService,
    private readonly contractorContractService: ContractorContractService,
    private readonly cdr: ChangeDetectorRef,
    private readonly alert: AlertService,
  ) {}

  ngAfterViewInit() {
    this.stepper.selectionChange.subscribe(() => {
      this.cdr.detectChanges();
    });
  }

  validateAndProceedToNextStep(): void {
    if (!this.contractorDetailForm.isValid()) {
      this.contractorDetailForm.getValue();
      return;
    }

    this.checkActiveContract();
  }

  validateAndProceedToContractValues(): void {
    if (!this.contractDetailForm.isValid()) {
      this.contractDetailForm.getValue();
      return;
    }

    this.stepper.next();
  }

  validateAndCreateCompleteContract(): void {
    if (!this.contractValuesForm.isValid()) {
      this.contractValuesForm.getValue();
      return;
    }

    this.createCompleteContract();
  }

  checkActiveContract(): void {
    const contractor = this.contractorDetailForm.getValue();
    if (contractor) {
      this.contractorContractService
        .hasActiveContract(contractor.id)
        .subscribe({
          next: (hasActiveContract) => {
            if (hasActiveContract) {
              this.alert.warning(
                'Este contratista tiene un contrato activo y no puede proceder.',
              );
            } else {
              this.stepper.next();
            }
          },
          error: (error) => {
            this.alert.error(error.error?.detail ?? 'Error al verificar contratos activos');
          },
        });
    }
  }

  async createCompleteContract(): Promise<void> {
    const formValues = this.contractDetailForm.getValue();
    const contractor = this.contractorDetailForm.getValue();
    const contractValues = this.contractValuesForm.getValue();

    if (
      contractValues.futureValidityValue &&
      contractValues.futureValidityValue > 0
    ) {
      const confirmed = await this.alert.confirm(
        '¿Está seguro de agregar este valor de vigencia futura?',
        `El valor de vigencia futura es de ${new Intl.NumberFormat('es-CO', { style: 'currency', currency: 'COP' }).format(contractValues.futureValidityValue)}`,
      );

      if (!confirmed) {
        return;
      }
    }

    const contract: Omit<Contract, 'id'> = {
      contractNumber: Number(formValues.contractNumber),
      contractYearId: Number(formValues.contractYear),
      object: formValues.object ?? '',
      rup: Boolean(formValues.rup ?? false),
      sigepLink: formValues.sigepLink ?? '',
      secopLink: formValues.secopLink ?? '',
      addition: formValues.addition ?? false,
      cession: formValues.cession ?? false,
      settled: formValues.settled ?? false,
      selectionModalityId: Number(formValues.selectionModality),
      trackingTypeId: Number(formValues.trackingType),
      contractTypeId: Number(formValues.contractType),
      statusId: 1,
      dependencyId: Number(formValues.dependency),
      groupId: Number(formValues.group),
      monthlyPayment: Number(formValues.monthlyPayment),
      municipalityId: Number(formValues.municipalityId),
      departmentId: Number(formValues.departmentId),
      secopCode: Number(formValues.secopCode),
      causesSelectionId: Number(formValues.causesSelectionId),
      managementSupportId: Number(formValues.managementSupportId),
      contractClassId: Number(formValues.causesSelectionId),
    };

    const contractContractor: Omit<ContractContractor, 'id'> = {
      warranty: Boolean(formValues.warranty ?? false),
      dateExpeditionWarranty: formValues.dateExpeditionWarranty
        ? new Date(formValues.dateExpeditionWarranty).toISOString().slice(0, 10)
        : undefined,
      typeWarrantyId: formValues.warranty
        ? Number(formValues.typeWarrantyId)
        : undefined,
      insuredRisksId: formValues.warranty
        ? Number(formValues.insuredRisksId)
        : undefined,
      supervisorId: Number(formValues.supervisorId),
    };

    if (contractor) {
      this.contractService
        .createCompleteContract({
          contractorIdNumber: contractor.idNumber,
          contract,
          contractContractor,
          contractValues,
        })
        .subscribe({
          next: () => {
            this.alert.success('¡Contrato creado correctamente!');
            this.dialogRef.close('created');
          },
          error: (error) => {
            const errorMessage = error.error?.detail ?? 'Error al crear el contrato';
            this.alert.error(errorMessage);
          },
        });
    }
  }
}
