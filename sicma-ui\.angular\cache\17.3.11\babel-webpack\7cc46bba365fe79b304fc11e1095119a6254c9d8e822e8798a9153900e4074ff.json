{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/Github/SICMA/sicma-ui/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { TestBed, discardPeriodicTasks, fakeAsync, tick } from '@angular/core/testing';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { Router } from '@angular/router';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { of, throwError } from 'rxjs';\nimport { KeycloackComponent } from './keycloack.component';\nimport { UserService } from '@core/auth/services/user.service';\ndescribe('KeycloackComponent', () => {\n  let component;\n  let fixture;\n  let authService;\n  let router;\n  let spinner;\n  let alertService;\n  let userService;\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    authService = jasmine.createSpyObj('AuthService', ['getUserProfiles', 'hasProfile', 'logout', 'login']);\n    router = jasmine.createSpyObj('Router', ['navigate']);\n    spinner = jasmine.createSpyObj('NgxSpinnerService', ['show', 'hide']);\n    alertService = jasmine.createSpyObj('AlertService', ['error', 'success']);\n    userService = jasmine.createSpyObj('UserService', ['getCurrentSession', 'createLogin']);\n    yield TestBed.configureTestingModule({\n      imports: [KeycloackComponent, HttpClientTestingModule, BrowserAnimationsModule],\n      providers: [{\n        provide: AuthService,\n        useValue: authService\n      }, {\n        provide: Router,\n        useValue: router\n      }, {\n        provide: NgxSpinnerService,\n        useValue: spinner\n      }, {\n        provide: AlertService,\n        useValue: alertService\n      }, {\n        provide: UserService,\n        useValue: userService\n      }]\n    }).compileComponents();\n    fixture = TestBed.createComponent(KeycloackComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  }));\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should handle failed login', () => {\n    authService.login.and.returnValue(of(false));\n    const mockResponse = {\n      token: 'test-token'\n    };\n    userService.createLogin.and.returnValue(of(mockResponse));\n    const event = {\n      detail: 'token'\n    };\n    component.onTokenResolved(event);\n    expect(authService.login).toHaveBeenCalledWith('token');\n    expect(alertService.error).toHaveBeenCalledWith('Credenciales inválidas');\n  });\n  it('should handle login error', () => {\n    authService.login.and.returnValue(throwError(() => new Error('Error')));\n    const event = {\n      detail: 'token'\n    };\n    component.onTokenResolved(event);\n    expect(authService.login).toHaveBeenCalledWith('token');\n    expect(alertService.error).toHaveBeenCalledWith('Error al intentar autenticarse');\n  });\n  it('should start token monitoring on init', fakeAsync(() => {\n    spyOn(component, 'monitorToken');\n    component.ngOnInit();\n    expect(component.monitorToken).toHaveBeenCalled();\n  }));\n  it('should handle successful login with administrator profile', () => {\n    authService.login.and.returnValue(of(true));\n    authService.getUserProfiles.and.returnValue([{\n      profile_id: 1,\n      profile_name: 'ADMINISTRATOR'\n    }]);\n    const event = {\n      detail: 'token'\n    };\n    component.onTokenResolved(event);\n    expect(authService.login).toHaveBeenCalledWith('token');\n    expect(router.navigate).toHaveBeenCalledWith(['/contratistas']);\n    expect(alertService.success).toHaveBeenCalledWith('Autenticación exitosa');\n  });\n  it('should handle successful login for SUPERVISOR', () => {\n    authService.login.and.returnValue(of(true));\n    authService.getUserProfiles.and.returnValue([{\n      profile_id: 2,\n      profile_name: 'SUPERVISOR'\n    }]);\n    const event = {\n      detail: 'token'\n    };\n    component.onTokenResolved(event);\n    expect(authService.login).toHaveBeenCalledWith('token');\n    expect(router.navigate).toHaveBeenCalledWith(['/revisar-informes']);\n    expect(alertService.success).toHaveBeenCalledWith('Autenticación exitosa');\n  });\n  it('should handle successful login for CONTRACTOR', () => {\n    authService.login.and.returnValue(of(true));\n    authService.getUserProfiles.and.returnValue([{\n      profile_id: 3,\n      profile_name: 'CONTRACTOR'\n    }]);\n    const event = {\n      detail: 'token'\n    };\n    component.onTokenResolved(event);\n    expect(authService.login).toHaveBeenCalledWith('token');\n    expect(router.navigate).toHaveBeenCalledWith(['/mis-contratos']);\n    expect(alertService.success).toHaveBeenCalledWith('Autenticación exitosa');\n  });\n  it('should handle successful login for CONTRACT-MANAGER', () => {\n    authService.login.and.returnValue(of(true));\n    authService.getUserProfiles.and.returnValue([{\n      profile_id: 4,\n      profile_name: 'CONTRACT-MANAGER'\n    }]);\n    const event = {\n      detail: 'token'\n    };\n    component.onTokenResolved(event);\n    expect(authService.login).toHaveBeenCalledWith('token');\n    expect(router.navigate).toHaveBeenCalledWith(['/contratos']);\n    expect(alertService.success).toHaveBeenCalledWith('Autenticación exitosa');\n  });\n  it('should handle successful login for unknown profile', () => {\n    authService.login.and.returnValue(of(true));\n    authService.getUserProfiles.and.returnValue([{\n      profile_id: 5,\n      profile_name: 'UNKNOWN'\n    }]);\n    const event = {\n      detail: 'token'\n    };\n    component.onTokenResolved(event);\n    expect(authService.login).toHaveBeenCalledWith('token');\n    expect(router.navigate).toHaveBeenCalledWith(['/']);\n    expect(alertService.success).toHaveBeenCalledWith('Autenticación exitosa');\n  });\n  it('should navigate to login on logout', () => {\n    component.getUrlLogOut();\n    expect(router.navigate).toHaveBeenCalledWith(['/login']);\n  });\n  it('should monitor token and navigate to login when token is missing', fakeAsync(() => {\n    spyOn(localStorage, 'getItem').and.returnValue(null);\n    component.monitorToken();\n    tick(60000);\n    expect(router.navigate).toHaveBeenCalledWith(['/login']);\n    discardPeriodicTasks();\n  }));\n  it('should not navigate to login when token exists', fakeAsync(() => {\n    spyOn(localStorage, 'getItem').and.returnValue('valid-token');\n    component.monitorToken();\n    tick(60000);\n    expect(router.navigate).not.toHaveBeenCalled();\n    discardPeriodicTasks();\n  }));\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "TestBed", "discardPeriodicTasks", "fakeAsync", "tick", "BrowserAnimationsModule", "Router", "AuthService", "AlertService", "NgxSpinnerService", "of", "throwError", "KeycloackComponent", "UserService", "describe", "component", "fixture", "authService", "router", "spinner", "alertService", "userService", "beforeEach", "_asyncToGenerator", "jasmine", "createSpyObj", "configureTestingModule", "imports", "providers", "provide", "useValue", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy", "login", "and", "returnValue", "mockResponse", "token", "createLogin", "event", "detail", "onTokenResolved", "toHaveBeenCalledWith", "error", "Error", "spyOn", "ngOnInit", "monitorToken", "toHaveBeenCalled", "getUserProfiles", "profile_id", "profile_name", "navigate", "success", "getUrlLogOut", "localStorage", "not"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\core\\layout\\keycloack\\keycloack.component.spec.ts"], "sourcesContent": ["import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport {\n  ComponentFixture,\n  TestBed,\n  discardPeriodicTasks,\n  fakeAsync,\n  tick,\n} from '@angular/core/testing';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { Router } from '@angular/router';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { of, throwError } from 'rxjs';\nimport { KeycloackComponent } from './keycloack.component';\nimport { UserService } from '@core/auth/services/user.service';\nimport { ContractorToken } from '@core/auth/models/contractorToken.model';\n\ninterface CustomEvent {\n  detail: string;\n}\n\ndescribe('KeycloackComponent', () => {\n  let component: KeycloackComponent;\n  let fixture: ComponentFixture<KeycloackComponent>;\n  let authService: jasmine.SpyObj<AuthService>;\n  let router: jasmine.SpyObj<Router>;\n  let spinner: jasmine.SpyObj<NgxSpinnerService>;\n  let alertService: jasmine.SpyObj<AlertService>;\n  let userService: jasmine.SpyObj<UserService>;\n\n  beforeEach(async () => {\n    authService = jasmine.createSpyObj('AuthService', [\n      'getUserProfiles',\n      'hasProfile',\n      'logout',\n      'login',\n    ]);\n    router = jasmine.createSpyObj('Router', ['navigate']);\n    spinner = jasmine.createSpyObj('NgxSpinnerService', ['show', 'hide']);\n    alertService = jasmine.createSpyObj('AlertService', ['error', 'success']);\n    userService = jasmine.createSpyObj('UserService', [\n      'getCurrentSession',\n      'createLogin',\n    ]);\n\n    await TestBed.configureTestingModule({\n      imports: [\n        KeycloackComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n      ],\n      providers: [\n        { provide: AuthService, useValue: authService },\n        { provide: Router, useValue: router },\n        { provide: NgxSpinnerService, useValue: spinner },\n        { provide: AlertService, useValue: alertService },\n        { provide: UserService, useValue: userService },\n      ],\n    }).compileComponents();\n\n    fixture = TestBed.createComponent(KeycloackComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should handle failed login', () => {\n    authService.login.and.returnValue(of(false));\n    const mockResponse: ContractorToken = { token: 'test-token' };\n    userService.createLogin.and.returnValue(of(mockResponse));\n\n    const event = { detail: 'token' } as CustomEvent;\n    component.onTokenResolved(event);\n\n    expect(authService.login).toHaveBeenCalledWith('token');\n    expect(alertService.error).toHaveBeenCalledWith('Credenciales inválidas');\n  });\n\n  it('should handle login error', () => {\n    authService.login.and.returnValue(throwError(() => new Error('Error')));\n\n    const event = { detail: 'token' } as CustomEvent;\n    component.onTokenResolved(event);\n\n    expect(authService.login).toHaveBeenCalledWith('token');\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al intentar autenticarse',\n    );\n  });\n\n  it('should start token monitoring on init', fakeAsync(() => {\n    spyOn(component, 'monitorToken');\n    component.ngOnInit();\n    expect(component.monitorToken).toHaveBeenCalled();\n  }));\n\n  it('should handle successful login with administrator profile', () => {\n    authService.login.and.returnValue(of(true));\n    authService.getUserProfiles.and.returnValue([\n      { profile_id: 1, profile_name: 'ADMINISTRATOR' },\n    ]);\n\n    const event = { detail: 'token' } as CustomEvent;\n    component.onTokenResolved(event);\n\n    expect(authService.login).toHaveBeenCalledWith('token');\n    expect(router.navigate).toHaveBeenCalledWith(['/contratistas']);\n    expect(alertService.success).toHaveBeenCalledWith('Autenticación exitosa');\n  });\n\n  it('should handle successful login for SUPERVISOR', () => {\n    authService.login.and.returnValue(of(true));\n    authService.getUserProfiles.and.returnValue([\n      { profile_id: 2, profile_name: 'SUPERVISOR' },\n    ]);\n\n    const event = { detail: 'token' } as CustomEvent;\n    component.onTokenResolved(event);\n\n    expect(authService.login).toHaveBeenCalledWith('token');\n    expect(router.navigate).toHaveBeenCalledWith(['/revisar-informes']);\n    expect(alertService.success).toHaveBeenCalledWith('Autenticación exitosa');\n  });\n\n  it('should handle successful login for CONTRACTOR', () => {\n    authService.login.and.returnValue(of(true));\n    authService.getUserProfiles.and.returnValue([\n      { profile_id: 3, profile_name: 'CONTRACTOR' },\n    ]);\n\n    const event = { detail: 'token' } as CustomEvent;\n    component.onTokenResolved(event);\n\n    expect(authService.login).toHaveBeenCalledWith('token');\n    expect(router.navigate).toHaveBeenCalledWith(['/mis-contratos']);\n    expect(alertService.success).toHaveBeenCalledWith('Autenticación exitosa');\n  });\n\n  it('should handle successful login for CONTRACT-MANAGER', () => {\n    authService.login.and.returnValue(of(true));\n    authService.getUserProfiles.and.returnValue([\n      { profile_id: 4, profile_name: 'CONTRACT-MANAGER' },\n    ]);\n\n    const event = { detail: 'token' } as CustomEvent;\n    component.onTokenResolved(event);\n\n    expect(authService.login).toHaveBeenCalledWith('token');\n    expect(router.navigate).toHaveBeenCalledWith(['/contratos']);\n    expect(alertService.success).toHaveBeenCalledWith('Autenticación exitosa');\n  });\n\n  it('should handle successful login for unknown profile', () => {\n    authService.login.and.returnValue(of(true));\n    authService.getUserProfiles.and.returnValue([\n      { profile_id: 5, profile_name: 'UNKNOWN' },\n    ]);\n\n    const event = { detail: 'token' } as CustomEvent;\n    component.onTokenResolved(event);\n\n    expect(authService.login).toHaveBeenCalledWith('token');\n    expect(router.navigate).toHaveBeenCalledWith(['/']);\n    expect(alertService.success).toHaveBeenCalledWith('Autenticación exitosa');\n  });\n\n  it('should navigate to login on logout', () => {\n    component.getUrlLogOut();\n    expect(router.navigate).toHaveBeenCalledWith(['/login']);\n  });\n\n  it('should monitor token and navigate to login when token is missing', fakeAsync(() => {\n    spyOn(localStorage, 'getItem').and.returnValue(null);\n    component.monitorToken();\n    tick(60000);\n    expect(router.navigate).toHaveBeenCalledWith(['/login']);\n    discardPeriodicTasks();\n  }));\n\n  it('should not navigate to login when token exists', fakeAsync(() => {\n    spyOn(localStorage, 'getItem').and.returnValue('valid-token');\n    component.monitorToken();\n    tick(60000);\n    expect(router.navigate).not.toHaveBeenCalled();\n    discardPeriodicTasks();\n  }));\n});"], "mappings": ";AAAA,SAASA,uBAAuB,QAAQ,8BAA8B;AACtE,SAEEC,OAAO,EACPC,oBAAoB,EACpBC,SAAS,EACTC,IAAI,QACC,uBAAuB;AAC9B,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,iBAAiB,QAAQ,aAAa;AAC/C,SAASC,EAAE,EAAEC,UAAU,QAAQ,MAAM;AACrC,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,WAAW,QAAQ,kCAAkC;AAO9DC,QAAQ,CAAC,oBAAoB,EAAE,MAAK;EAClC,IAAIC,SAA6B;EACjC,IAAIC,OAA6C;EACjD,IAAIC,WAAwC;EAC5C,IAAIC,MAA8B;EAClC,IAAIC,OAA0C;EAC9C,IAAIC,YAA0C;EAC9C,IAAIC,WAAwC;EAE5CC,UAAU,eAAAC,iBAAA,CAAC,aAAW;IACpBN,WAAW,GAAGO,OAAO,CAACC,YAAY,CAAC,aAAa,EAAE,CAChD,iBAAiB,EACjB,YAAY,EACZ,QAAQ,EACR,OAAO,CACR,CAAC;IACFP,MAAM,GAAGM,OAAO,CAACC,YAAY,CAAC,QAAQ,EAAE,CAAC,UAAU,CAAC,CAAC;IACrDN,OAAO,GAAGK,OAAO,CAACC,YAAY,CAAC,mBAAmB,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACrEL,YAAY,GAAGI,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IACzEJ,WAAW,GAAGG,OAAO,CAACC,YAAY,CAAC,aAAa,EAAE,CAChD,mBAAmB,EACnB,aAAa,CACd,CAAC;IAEF,MAAMxB,OAAO,CAACyB,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CACPf,kBAAkB,EAClBZ,uBAAuB,EACvBK,uBAAuB,CACxB;MACDuB,SAAS,EAAE,CACT;QAAEC,OAAO,EAAEtB,WAAW;QAAEuB,QAAQ,EAAEb;MAAW,CAAE,EAC/C;QAAEY,OAAO,EAAEvB,MAAM;QAAEwB,QAAQ,EAAEZ;MAAM,CAAE,EACrC;QAAEW,OAAO,EAAEpB,iBAAiB;QAAEqB,QAAQ,EAAEX;MAAO,CAAE,EACjD;QAAEU,OAAO,EAAErB,YAAY;QAAEsB,QAAQ,EAAEV;MAAY,CAAE,EACjD;QAAES,OAAO,EAAEhB,WAAW;QAAEiB,QAAQ,EAAET;MAAW,CAAE;KAElD,CAAC,CAACU,iBAAiB,EAAE;IAEtBf,OAAO,GAAGf,OAAO,CAAC+B,eAAe,CAACpB,kBAAkB,CAAC;IACrDG,SAAS,GAAGC,OAAO,CAACiB,iBAAiB;IACrCjB,OAAO,CAACkB,aAAa,EAAE;EACzB,CAAC,EAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACrB,SAAS,CAAC,CAACsB,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,4BAA4B,EAAE,MAAK;IACpClB,WAAW,CAACqB,KAAK,CAACC,GAAG,CAACC,WAAW,CAAC9B,EAAE,CAAC,KAAK,CAAC,CAAC;IAC5C,MAAM+B,YAAY,GAAoB;MAAEC,KAAK,EAAE;IAAY,CAAE;IAC7DrB,WAAW,CAACsB,WAAW,CAACJ,GAAG,CAACC,WAAW,CAAC9B,EAAE,CAAC+B,YAAY,CAAC,CAAC;IAEzD,MAAMG,KAAK,GAAG;MAAEC,MAAM,EAAE;IAAO,CAAiB;IAChD9B,SAAS,CAAC+B,eAAe,CAACF,KAAK,CAAC;IAEhCR,MAAM,CAACnB,WAAW,CAACqB,KAAK,CAAC,CAACS,oBAAoB,CAAC,OAAO,CAAC;IACvDX,MAAM,CAAChB,YAAY,CAAC4B,KAAK,CAAC,CAACD,oBAAoB,CAAC,wBAAwB,CAAC;EAC3E,CAAC,CAAC;EAEFZ,EAAE,CAAC,2BAA2B,EAAE,MAAK;IACnClB,WAAW,CAACqB,KAAK,CAACC,GAAG,CAACC,WAAW,CAAC7B,UAAU,CAAC,MAAM,IAAIsC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;IAEvE,MAAML,KAAK,GAAG;MAAEC,MAAM,EAAE;IAAO,CAAiB;IAChD9B,SAAS,CAAC+B,eAAe,CAACF,KAAK,CAAC;IAEhCR,MAAM,CAACnB,WAAW,CAACqB,KAAK,CAAC,CAACS,oBAAoB,CAAC,OAAO,CAAC;IACvDX,MAAM,CAAChB,YAAY,CAAC4B,KAAK,CAAC,CAACD,oBAAoB,CAC7C,gCAAgC,CACjC;EACH,CAAC,CAAC;EAEFZ,EAAE,CAAC,uCAAuC,EAAEhC,SAAS,CAAC,MAAK;IACzD+C,KAAK,CAACnC,SAAS,EAAE,cAAc,CAAC;IAChCA,SAAS,CAACoC,QAAQ,EAAE;IACpBf,MAAM,CAACrB,SAAS,CAACqC,YAAY,CAAC,CAACC,gBAAgB,EAAE;EACnD,CAAC,CAAC,CAAC;EAEHlB,EAAE,CAAC,2DAA2D,EAAE,MAAK;IACnElB,WAAW,CAACqB,KAAK,CAACC,GAAG,CAACC,WAAW,CAAC9B,EAAE,CAAC,IAAI,CAAC,CAAC;IAC3CO,WAAW,CAACqC,eAAe,CAACf,GAAG,CAACC,WAAW,CAAC,CAC1C;MAAEe,UAAU,EAAE,CAAC;MAAEC,YAAY,EAAE;IAAe,CAAE,CACjD,CAAC;IAEF,MAAMZ,KAAK,GAAG;MAAEC,MAAM,EAAE;IAAO,CAAiB;IAChD9B,SAAS,CAAC+B,eAAe,CAACF,KAAK,CAAC;IAEhCR,MAAM,CAACnB,WAAW,CAACqB,KAAK,CAAC,CAACS,oBAAoB,CAAC,OAAO,CAAC;IACvDX,MAAM,CAAClB,MAAM,CAACuC,QAAQ,CAAC,CAACV,oBAAoB,CAAC,CAAC,eAAe,CAAC,CAAC;IAC/DX,MAAM,CAAChB,YAAY,CAACsC,OAAO,CAAC,CAACX,oBAAoB,CAAC,uBAAuB,CAAC;EAC5E,CAAC,CAAC;EAEFZ,EAAE,CAAC,+CAA+C,EAAE,MAAK;IACvDlB,WAAW,CAACqB,KAAK,CAACC,GAAG,CAACC,WAAW,CAAC9B,EAAE,CAAC,IAAI,CAAC,CAAC;IAC3CO,WAAW,CAACqC,eAAe,CAACf,GAAG,CAACC,WAAW,CAAC,CAC1C;MAAEe,UAAU,EAAE,CAAC;MAAEC,YAAY,EAAE;IAAY,CAAE,CAC9C,CAAC;IAEF,MAAMZ,KAAK,GAAG;MAAEC,MAAM,EAAE;IAAO,CAAiB;IAChD9B,SAAS,CAAC+B,eAAe,CAACF,KAAK,CAAC;IAEhCR,MAAM,CAACnB,WAAW,CAACqB,KAAK,CAAC,CAACS,oBAAoB,CAAC,OAAO,CAAC;IACvDX,MAAM,CAAClB,MAAM,CAACuC,QAAQ,CAAC,CAACV,oBAAoB,CAAC,CAAC,mBAAmB,CAAC,CAAC;IACnEX,MAAM,CAAChB,YAAY,CAACsC,OAAO,CAAC,CAACX,oBAAoB,CAAC,uBAAuB,CAAC;EAC5E,CAAC,CAAC;EAEFZ,EAAE,CAAC,+CAA+C,EAAE,MAAK;IACvDlB,WAAW,CAACqB,KAAK,CAACC,GAAG,CAACC,WAAW,CAAC9B,EAAE,CAAC,IAAI,CAAC,CAAC;IAC3CO,WAAW,CAACqC,eAAe,CAACf,GAAG,CAACC,WAAW,CAAC,CAC1C;MAAEe,UAAU,EAAE,CAAC;MAAEC,YAAY,EAAE;IAAY,CAAE,CAC9C,CAAC;IAEF,MAAMZ,KAAK,GAAG;MAAEC,MAAM,EAAE;IAAO,CAAiB;IAChD9B,SAAS,CAAC+B,eAAe,CAACF,KAAK,CAAC;IAEhCR,MAAM,CAACnB,WAAW,CAACqB,KAAK,CAAC,CAACS,oBAAoB,CAAC,OAAO,CAAC;IACvDX,MAAM,CAAClB,MAAM,CAACuC,QAAQ,CAAC,CAACV,oBAAoB,CAAC,CAAC,gBAAgB,CAAC,CAAC;IAChEX,MAAM,CAAChB,YAAY,CAACsC,OAAO,CAAC,CAACX,oBAAoB,CAAC,uBAAuB,CAAC;EAC5E,CAAC,CAAC;EAEFZ,EAAE,CAAC,qDAAqD,EAAE,MAAK;IAC7DlB,WAAW,CAACqB,KAAK,CAACC,GAAG,CAACC,WAAW,CAAC9B,EAAE,CAAC,IAAI,CAAC,CAAC;IAC3CO,WAAW,CAACqC,eAAe,CAACf,GAAG,CAACC,WAAW,CAAC,CAC1C;MAAEe,UAAU,EAAE,CAAC;MAAEC,YAAY,EAAE;IAAkB,CAAE,CACpD,CAAC;IAEF,MAAMZ,KAAK,GAAG;MAAEC,MAAM,EAAE;IAAO,CAAiB;IAChD9B,SAAS,CAAC+B,eAAe,CAACF,KAAK,CAAC;IAEhCR,MAAM,CAACnB,WAAW,CAACqB,KAAK,CAAC,CAACS,oBAAoB,CAAC,OAAO,CAAC;IACvDX,MAAM,CAAClB,MAAM,CAACuC,QAAQ,CAAC,CAACV,oBAAoB,CAAC,CAAC,YAAY,CAAC,CAAC;IAC5DX,MAAM,CAAChB,YAAY,CAACsC,OAAO,CAAC,CAACX,oBAAoB,CAAC,uBAAuB,CAAC;EAC5E,CAAC,CAAC;EAEFZ,EAAE,CAAC,oDAAoD,EAAE,MAAK;IAC5DlB,WAAW,CAACqB,KAAK,CAACC,GAAG,CAACC,WAAW,CAAC9B,EAAE,CAAC,IAAI,CAAC,CAAC;IAC3CO,WAAW,CAACqC,eAAe,CAACf,GAAG,CAACC,WAAW,CAAC,CAC1C;MAAEe,UAAU,EAAE,CAAC;MAAEC,YAAY,EAAE;IAAS,CAAE,CAC3C,CAAC;IAEF,MAAMZ,KAAK,GAAG;MAAEC,MAAM,EAAE;IAAO,CAAiB;IAChD9B,SAAS,CAAC+B,eAAe,CAACF,KAAK,CAAC;IAEhCR,MAAM,CAACnB,WAAW,CAACqB,KAAK,CAAC,CAACS,oBAAoB,CAAC,OAAO,CAAC;IACvDX,MAAM,CAAClB,MAAM,CAACuC,QAAQ,CAAC,CAACV,oBAAoB,CAAC,CAAC,GAAG,CAAC,CAAC;IACnDX,MAAM,CAAChB,YAAY,CAACsC,OAAO,CAAC,CAACX,oBAAoB,CAAC,uBAAuB,CAAC;EAC5E,CAAC,CAAC;EAEFZ,EAAE,CAAC,oCAAoC,EAAE,MAAK;IAC5CpB,SAAS,CAAC4C,YAAY,EAAE;IACxBvB,MAAM,CAAClB,MAAM,CAACuC,QAAQ,CAAC,CAACV,oBAAoB,CAAC,CAAC,QAAQ,CAAC,CAAC;EAC1D,CAAC,CAAC;EAEFZ,EAAE,CAAC,kEAAkE,EAAEhC,SAAS,CAAC,MAAK;IACpF+C,KAAK,CAACU,YAAY,EAAE,SAAS,CAAC,CAACrB,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;IACpDzB,SAAS,CAACqC,YAAY,EAAE;IACxBhD,IAAI,CAAC,KAAK,CAAC;IACXgC,MAAM,CAAClB,MAAM,CAACuC,QAAQ,CAAC,CAACV,oBAAoB,CAAC,CAAC,QAAQ,CAAC,CAAC;IACxD7C,oBAAoB,EAAE;EACxB,CAAC,CAAC,CAAC;EAEHiC,EAAE,CAAC,gDAAgD,EAAEhC,SAAS,CAAC,MAAK;IAClE+C,KAAK,CAACU,YAAY,EAAE,SAAS,CAAC,CAACrB,GAAG,CAACC,WAAW,CAAC,aAAa,CAAC;IAC7DzB,SAAS,CAACqC,YAAY,EAAE;IACxBhD,IAAI,CAAC,KAAK,CAAC;IACXgC,MAAM,CAAClB,MAAM,CAACuC,QAAQ,CAAC,CAACI,GAAG,CAACR,gBAAgB,EAAE;IAC9CnD,oBAAoB,EAAE;EACxB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}