import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterTestingModule } from '@angular/router/testing';
import { User } from '@core/auth/models/user.model';
import { UserProfile } from '@core/auth/models/user_profile.model';
import { AuthService } from '@core/auth/services/auth.service';
import { HeaderComponent } from './header.component';

describe('HeaderComponent', () => {
  let component: HeaderComponent;
  let fixture: ComponentFixture<HeaderComponent>;
  let authService: jasmine.SpyObj<AuthService>;
  let localStorageSpy: jasmine.Spy;

  const mockProfiles: UserProfile[] = [
    { profile_id: 1, profile_name: 'ADMIN' },
    { profile_id: 2, profile_name: 'USER' },
  ];

  const mockUser: User = {
    id: 1,
    username: '<EMAIL>',
    profiles: mockProfiles,
  };

  beforeEach(() => {
    const authServiceSpy = jasmine.createSpyObj('AuthService', [
      'getUserProfiles',
      'hasProfile',
    ]);
    authServiceSpy.getUserProfiles.and.returnValue(mockProfiles);
    authServiceSpy.hasProfile.and.returnValue(true);

    TestBed.configureTestingModule({
      imports: [
        HeaderComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        RouterTestingModule,
      ],
      providers: [{ provide: AuthService, useValue: authServiceSpy }],
    });

    fixture = TestBed.createComponent(HeaderComponent);
    component = fixture.componentInstance;
    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    localStorageSpy = spyOn(localStorage, 'getItem');
  });

  it('should create', () => {
    fixture.detectChanges();
    expect(component).toBeTruthy();
  });

  it('should initialize with user from localStorage', () => {
    localStorageSpy.and.returnValue(JSON.stringify(mockUser));

    fixture.detectChanges();

    expect(component.user).toEqual(mockUser);
    expect(component.userProfiles).toEqual(mockProfiles);
    expect(authService.getUserProfiles).toHaveBeenCalled();
  });

  it('should initialize without user when localStorage is empty', () => {
    localStorageSpy.and.returnValue(null);

    fixture.detectChanges();

    expect(component.user).toBeUndefined();
    expect(component.userProfiles).toEqual(mockProfiles);
    expect(authService.getUserProfiles).toHaveBeenCalled();
  });

  it('should handle invalid JSON in localStorage', () => {
    localStorageSpy.and.returnValue('invalid-json');

    fixture.detectChanges();

    expect(component.user).toBeUndefined();
    expect(component.userProfiles).toEqual(mockProfiles);
    expect(authService.getUserProfiles).toHaveBeenCalled();
  });

  it('should check if user can view profile', () => {
    const profile = 'ADMIN';
    authService.hasProfile.and.returnValue(true);

    fixture.detectChanges();
    const result = component.canView(profile);

    expect(result).toBe(true);
    expect(authService.hasProfile).toHaveBeenCalledWith(profile);
  });

  it('should check if user cannot view profile', () => {
    const profile = 'SUPER_ADMIN';
    authService.hasProfile.and.returnValue(false);

    fixture.detectChanges();
    const result = component.canView(profile);

    expect(result).toBe(false);
    expect(authService.hasProfile).toHaveBeenCalledWith(profile);
  });
});