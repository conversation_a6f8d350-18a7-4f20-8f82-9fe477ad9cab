{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { TaxBenefitService } from './tax-benefit.service';\ndescribe('TaxBenefitService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/tax-benefits`;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [TaxBenefitService]\n    });\n    service = TestBed.inject(TaxBenefitService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  const mockTaxBenefit = {\n    id: 1,\n    prepaidMedicine: true,\n    dependents: false,\n    homeInterest: true,\n    afcAccount: false,\n    voluntaryPension: true,\n    monthlyReportId: 1\n  };\n  describe('getAll', () => {\n    it('should return all tax benefits', () => {\n      const mockTaxBenefits = [mockTaxBenefit];\n      service.getAll().subscribe(taxBenefits => {\n        expect(taxBenefits).toEqual(mockTaxBenefits);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockTaxBenefits);\n    });\n    it('should handle error when getting all tax benefits', () => {\n      service.getAll().subscribe({\n        error: error => {\n          expect(error.status).toBe(500);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Server error'), {\n        status: 500\n      });\n    });\n  });\n  describe('getById', () => {\n    it('should return a tax benefit by id', () => {\n      service.getById(1).subscribe(taxBenefit => {\n        expect(taxBenefit).toEqual(mockTaxBenefit);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockTaxBenefit);\n    });\n    it('should handle error when getting tax benefit by id', () => {\n      service.getById(999).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/999`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n  describe('create', () => {\n    const newTaxBenefit = {\n      prepaidMedicine: true,\n      dependents: false,\n      homeInterest: true,\n      afcAccount: false,\n      voluntaryPension: true,\n      monthlyReportId: 1\n    };\n    it('should create a new tax benefit', () => {\n      service.create(newTaxBenefit).subscribe(taxBenefit => {\n        expect(taxBenefit).toEqual(mockTaxBenefit);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newTaxBenefit);\n      req.flush(mockTaxBenefit);\n    });\n    it('should handle error when creating tax benefit', () => {\n      service.create(newTaxBenefit).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n  describe('update', () => {\n    const updateData = {\n      prepaidMedicine: false,\n      dependents: true\n    };\n    it('should update a tax benefit', () => {\n      service.update(1, updateData).subscribe(taxBenefit => {\n        expect(taxBenefit).toEqual({\n          ...mockTaxBenefit,\n          ...updateData\n        });\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush({\n        ...mockTaxBenefit,\n        ...updateData\n      });\n    });\n    it('should handle error when updating tax benefit', () => {\n      service.update(1, updateData).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n  describe('delete', () => {\n    it('should delete a tax benefit', () => {\n      service.delete(1).subscribe(response => {\n        expect(response).toBeNull();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n    it('should handle error when deleting tax benefit', () => {\n      service.delete(1).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n  describe('getByMonthlyReportId', () => {\n    it('should return a tax benefit by monthly report id', () => {\n      const monthlyReportId = 1;\n      service.getByMonthlyReportId(monthlyReportId).subscribe(taxBenefit => {\n        expect(taxBenefit).toEqual(mockTaxBenefit);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/monthly-report/${monthlyReportId}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockTaxBenefit);\n    });\n    it('should handle error when getting tax benefit by monthly report id', () => {\n      const monthlyReportId = 999;\n      service.getByMonthlyReportId(monthlyReportId).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/monthly-report/${monthlyReportId}`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "TaxBenefitService", "describe", "service", "httpMock", "apiUrl", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockTaxBenefit", "id", "prepaidMedicine", "dependents", "homeInterest", "afcAccount", "voluntaryPension", "monthlyReportId", "mockTaxBenefits", "getAll", "subscribe", "taxBenefits", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "error", "status", "ErrorEvent", "getById", "taxBenefit", "newTaxBenefit", "create", "body", "updateData", "update", "delete", "response", "toBeNull", "getByMonthlyReportId"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\services\\tax-benefit.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { TaxBenefit } from '@contractor-dashboard/models/tax-benefit.model';\nimport { environment } from '@env';\nimport { TaxBenefitService } from './tax-benefit.service';\n\ndescribe('TaxBenefitService', () => {\n  let service: TaxBenefitService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/tax-benefits`;\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [TaxBenefitService],\n    });\n    service = TestBed.inject(TaxBenefitService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  const mockTaxBenefit: TaxBenefit = {\n    id: 1,\n    prepaidMedicine: true,\n    dependents: false,\n    homeInterest: true,\n    afcAccount: false,\n    voluntaryPension: true,\n    monthlyReportId: 1,\n  };\n\n  describe('getAll', () => {\n    it('should return all tax benefits', () => {\n      const mockTaxBenefits = [mockTaxBenefit];\n\n      service.getAll().subscribe((taxBenefits) => {\n        expect(taxBenefits).toEqual(mockTaxBenefits);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockTaxBenefits);\n    });\n\n    it('should handle error when getting all tax benefits', () => {\n      service.getAll().subscribe({\n        error: (error) => {\n          expect(error.status).toBe(500);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Server error'), { status: 500 });\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a tax benefit by id', () => {\n      service.getById(1).subscribe((taxBenefit) => {\n        expect(taxBenefit).toEqual(mockTaxBenefit);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockTaxBenefit);\n    });\n\n    it('should handle error when getting tax benefit by id', () => {\n      service.getById(999).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/999`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n\n  describe('create', () => {\n    const newTaxBenefit: Omit<TaxBenefit, 'id'> = {\n      prepaidMedicine: true,\n      dependents: false,\n      homeInterest: true,\n      afcAccount: false,\n      voluntaryPension: true,\n      monthlyReportId: 1,\n    };\n\n    it('should create a new tax benefit', () => {\n      service.create(newTaxBenefit).subscribe((taxBenefit) => {\n        expect(taxBenefit).toEqual(mockTaxBenefit);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newTaxBenefit);\n      req.flush(mockTaxBenefit);\n    });\n\n    it('should handle error when creating tax benefit', () => {\n      service.create(newTaxBenefit).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n\n  describe('update', () => {\n    const updateData: Partial<TaxBenefit> = {\n      prepaidMedicine: false,\n      dependents: true,\n    };\n\n    it('should update a tax benefit', () => {\n      service.update(1, updateData).subscribe((taxBenefit) => {\n        expect(taxBenefit).toEqual({ ...mockTaxBenefit, ...updateData });\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush({ ...mockTaxBenefit, ...updateData });\n    });\n\n    it('should handle error when updating tax benefit', () => {\n      service.update(1, updateData).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete a tax benefit', () => {\n      service.delete(1).subscribe((response) => {\n        expect(response).toBeNull();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n\n    it('should handle error when deleting tax benefit', () => {\n      service.delete(1).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n\n  describe('getByMonthlyReportId', () => {\n    it('should return a tax benefit by monthly report id', () => {\n      const monthlyReportId = 1;\n\n      service.getByMonthlyReportId(monthlyReportId).subscribe((taxBenefit) => {\n        expect(taxBenefit).toEqual(mockTaxBenefit);\n      });\n\n      const req = httpMock.expectOne(\n        `${apiUrl}/monthly-report/${monthlyReportId}`,\n      );\n      expect(req.request.method).toBe('GET');\n      req.flush(mockTaxBenefit);\n    });\n\n    it('should handle error when getting tax benefit by monthly report id', () => {\n      const monthlyReportId = 999;\n\n      service.getByMonthlyReportId(monthlyReportId).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(\n        `${apiUrl}/monthly-report/${monthlyReportId}`,\n      );\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,iBAAiB,QAAQ,uBAAuB;AAEzDC,QAAQ,CAAC,mBAAmB,EAAE,MAAK;EACjC,IAAIC,OAA0B;EAC9B,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,eAAe;EAEnDC,UAAU,CAAC,MAAK;IACdP,OAAO,CAACQ,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACX,uBAAuB,CAAC;MAClCY,SAAS,EAAE,CAACR,iBAAiB;KAC9B,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACW,MAAM,CAACT,iBAAiB,CAAC;IAC3CG,QAAQ,GAAGL,OAAO,CAACW,MAAM,CAACZ,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFa,SAAS,CAAC,MAAK;IACbP,QAAQ,CAACQ,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACX,OAAO,CAAC,CAACY,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEF,MAAMC,cAAc,GAAe;IACjCC,EAAE,EAAE,CAAC;IACLC,eAAe,EAAE,IAAI;IACrBC,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE,KAAK;IACjBC,gBAAgB,EAAE,IAAI;IACtBC,eAAe,EAAE;GAClB;EAEDrB,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBW,EAAE,CAAC,gCAAgC,EAAE,MAAK;MACxC,MAAMW,eAAe,GAAG,CAACR,cAAc,CAAC;MAExCb,OAAO,CAACsB,MAAM,EAAE,CAACC,SAAS,CAAEC,WAAW,IAAI;QACzCb,MAAM,CAACa,WAAW,CAAC,CAACC,OAAO,CAACJ,eAAe,CAAC;MAC9C,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAACzB,MAAM,CAAC;MACtCS,MAAM,CAACe,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,eAAe,CAAC;IAC5B,CAAC,CAAC;IAEFX,EAAE,CAAC,mDAAmD,EAAE,MAAK;MAC3DV,OAAO,CAACsB,MAAM,EAAE,CAACC,SAAS,CAAC;QACzBS,KAAK,EAAGA,KAAK,IAAI;UACfrB,MAAM,CAACqB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAACzB,MAAM,CAAC;MACtCwB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,cAAc,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC5D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFlC,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBW,EAAE,CAAC,mCAAmC,EAAE,MAAK;MAC3CV,OAAO,CAACmC,OAAO,CAAC,CAAC,CAAC,CAACZ,SAAS,CAAEa,UAAU,IAAI;QAC1CzB,MAAM,CAACyB,UAAU,CAAC,CAACX,OAAO,CAACZ,cAAc,CAAC;MAC5C,CAAC,CAAC;MAEF,MAAMa,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAAC,GAAGzB,MAAM,IAAI,CAAC;MAC7CS,MAAM,CAACe,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAAClB,cAAc,CAAC;IAC3B,CAAC,CAAC;IAEFH,EAAE,CAAC,oDAAoD,EAAE,MAAK;MAC5DV,OAAO,CAACmC,OAAO,CAAC,GAAG,CAAC,CAACZ,SAAS,CAAC;QAC7BS,KAAK,EAAGA,KAAK,IAAI;UACfrB,MAAM,CAACqB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAAC,GAAGzB,MAAM,MAAM,CAAC;MAC/CwB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFlC,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB,MAAMsC,aAAa,GAA2B;MAC5CtB,eAAe,EAAE,IAAI;MACrBC,UAAU,EAAE,KAAK;MACjBC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,KAAK;MACjBC,gBAAgB,EAAE,IAAI;MACtBC,eAAe,EAAE;KAClB;IAEDV,EAAE,CAAC,iCAAiC,EAAE,MAAK;MACzCV,OAAO,CAACsC,MAAM,CAACD,aAAa,CAAC,CAACd,SAAS,CAAEa,UAAU,IAAI;QACrDzB,MAAM,CAACyB,UAAU,CAAC,CAACX,OAAO,CAACZ,cAAc,CAAC;MAC5C,CAAC,CAAC;MAEF,MAAMa,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAACzB,MAAM,CAAC;MACtCS,MAAM,CAACe,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCnB,MAAM,CAACe,GAAG,CAACE,OAAO,CAACW,IAAI,CAAC,CAACd,OAAO,CAACY,aAAa,CAAC;MAC/CX,GAAG,CAACK,KAAK,CAAClB,cAAc,CAAC;IAC3B,CAAC,CAAC;IAEFH,EAAE,CAAC,+CAA+C,EAAE,MAAK;MACvDV,OAAO,CAACsC,MAAM,CAACD,aAAa,CAAC,CAACd,SAAS,CAAC;QACtCS,KAAK,EAAGA,KAAK,IAAI;UACfrB,MAAM,CAACqB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAACzB,MAAM,CAAC;MACtCwB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFlC,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB,MAAMyC,UAAU,GAAwB;MACtCzB,eAAe,EAAE,KAAK;MACtBC,UAAU,EAAE;KACb;IAEDN,EAAE,CAAC,6BAA6B,EAAE,MAAK;MACrCV,OAAO,CAACyC,MAAM,CAAC,CAAC,EAAED,UAAU,CAAC,CAACjB,SAAS,CAAEa,UAAU,IAAI;QACrDzB,MAAM,CAACyB,UAAU,CAAC,CAACX,OAAO,CAAC;UAAE,GAAGZ,cAAc;UAAE,GAAG2B;QAAU,CAAE,CAAC;MAClE,CAAC,CAAC;MAEF,MAAMd,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAAC,GAAGzB,MAAM,IAAI,CAAC;MAC7CS,MAAM,CAACe,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCnB,MAAM,CAACe,GAAG,CAACE,OAAO,CAACW,IAAI,CAAC,CAACd,OAAO,CAACe,UAAU,CAAC;MAC5Cd,GAAG,CAACK,KAAK,CAAC;QAAE,GAAGlB,cAAc;QAAE,GAAG2B;MAAU,CAAE,CAAC;IACjD,CAAC,CAAC;IAEF9B,EAAE,CAAC,+CAA+C,EAAE,MAAK;MACvDV,OAAO,CAACyC,MAAM,CAAC,CAAC,EAAED,UAAU,CAAC,CAACjB,SAAS,CAAC;QACtCS,KAAK,EAAGA,KAAK,IAAI;UACfrB,MAAM,CAACqB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAAC,GAAGzB,MAAM,IAAI,CAAC;MAC7CwB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFlC,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBW,EAAE,CAAC,6BAA6B,EAAE,MAAK;MACrCV,OAAO,CAAC0C,MAAM,CAAC,CAAC,CAAC,CAACnB,SAAS,CAAEoB,QAAQ,IAAI;QACvChC,MAAM,CAACgC,QAAQ,CAAC,CAACC,QAAQ,EAAE;MAC7B,CAAC,CAAC;MAEF,MAAMlB,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAAC,GAAGzB,MAAM,IAAI,CAAC;MAC7CS,MAAM,CAACe,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;IAEFrB,EAAE,CAAC,+CAA+C,EAAE,MAAK;MACvDV,OAAO,CAAC0C,MAAM,CAAC,CAAC,CAAC,CAACnB,SAAS,CAAC;QAC1BS,KAAK,EAAGA,KAAK,IAAI;UACfrB,MAAM,CAACqB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAAC,GAAGzB,MAAM,IAAI,CAAC;MAC7CwB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFlC,QAAQ,CAAC,sBAAsB,EAAE,MAAK;IACpCW,EAAE,CAAC,kDAAkD,EAAE,MAAK;MAC1D,MAAMU,eAAe,GAAG,CAAC;MAEzBpB,OAAO,CAAC6C,oBAAoB,CAACzB,eAAe,CAAC,CAACG,SAAS,CAAEa,UAAU,IAAI;QACrEzB,MAAM,CAACyB,UAAU,CAAC,CAACX,OAAO,CAACZ,cAAc,CAAC;MAC5C,CAAC,CAAC;MAEF,MAAMa,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAC5B,GAAGzB,MAAM,mBAAmBkB,eAAe,EAAE,CAC9C;MACDT,MAAM,CAACe,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAAClB,cAAc,CAAC;IAC3B,CAAC,CAAC;IAEFH,EAAE,CAAC,mEAAmE,EAAE,MAAK;MAC3E,MAAMU,eAAe,GAAG,GAAG;MAE3BpB,OAAO,CAAC6C,oBAAoB,CAACzB,eAAe,CAAC,CAACG,SAAS,CAAC;QACtDS,KAAK,EAAGA,KAAK,IAAI;UACfrB,MAAM,CAACqB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAC5B,GAAGzB,MAAM,mBAAmBkB,eAAe,EAAE,CAC9C;MACDM,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}