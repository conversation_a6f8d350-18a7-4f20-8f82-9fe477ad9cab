{"ast": null, "code": "import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { FormBuilder, ReactiveFormsModule } from '@angular/forms';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { MonthlyReportObligationsComponent } from './monthly-report-obligations.component';\ndescribe('MonthlyReportObligationsComponent', () => {\n  let component;\n  let fixture;\n  const mockObligations = [{\n    id: 1,\n    name: 'Obligation 1',\n    contractId: 1,\n    number: 1\n  }, {\n    id: 2,\n    name: 'Obligation 2',\n    contractId: 1,\n    number: 2\n  }, {\n    id: 3,\n    name: 'Obligation 3',\n    contractId: 1,\n    number: 3\n  }];\n  const mockReportObligations = [{\n    id: 1,\n    monthlyReportId: 1,\n    obligationId: 1,\n    description: 'Activity 1',\n    evidence: 'Evidence 1',\n    filePath: ''\n  }, {\n    id: 2,\n    monthlyReportId: 1,\n    obligationId: 3,\n    description: 'Activity 3',\n    evidence: 'Evidence 3',\n    filePath: ''\n  }, {\n    id: 3,\n    monthlyReportId: 1,\n    obligationId: 2,\n    description: 'Activity 2',\n    evidence: 'Evidence 2',\n    filePath: ''\n  }];\n  const mockMonthlyReport = {\n    id: 1,\n    reportNumber: 1,\n    startDate: new Date(),\n    endDate: new Date(),\n    creationDate: new Date(),\n    contractorContractId: 1\n  };\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [MonthlyReportObligationsComponent, HttpClientTestingModule, BrowserAnimationsModule, ReactiveFormsModule],\n      providers: [FormBuilder]\n    });\n    fixture = TestBed.createComponent(MonthlyReportObligationsComponent);\n    component = fixture.componentInstance;\n    component.monthlyReport = mockMonthlyReport;\n    component.obligations = [...mockObligations];\n    component.reportObligations = [...mockReportObligations];\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should initialize with obligations', () => {\n    expect(component.obligations).toEqual(mockObligations);\n    expect(component.reportObligations.length).toBe(3);\n  });\n  it('should get obligation name correctly', () => {\n    const name = component.getObligationName(1);\n    expect(name).toBe('Obligation 1');\n  });\n  it('should get obligation number correctly', () => {\n    const num = component.getObligationNumber(2);\n    expect(num).toBe(2);\n    const numUnknown = component.getObligationNumber(999);\n    expect(numUnknown).toBe('N/A');\n  });\n  it('should handle unknown obligation name', () => {\n    const name = component.getObligationName(999);\n    expect(name).toBe('Unknown Obligation');\n  });\n  it('should start editing row', () => {\n    component.startEditing(0);\n    expect(component.editingRow).toBe(0);\n    expect(component.editForm.get('activity')?.value).toBe('Activity 1');\n    expect(component.editForm.get('evidence')?.value).toBe('Evidence 1');\n  });\n  it('should cancel editing', () => {\n    component.startEditing(0);\n    component.cancelEditing();\n    expect(component.editingRow).toBeNull();\n    expect(component.editForm.get('activity')?.value).toBeNull();\n    expect(component.editForm.get('evidence')?.value).toBeNull();\n  });\n  it('should save editing', () => {\n    const saveEditingSpy = spyOn(component.saveEditing, 'emit');\n    component.startEditing(0);\n    component.editForm.patchValue({\n      activity: 'Updated Activity',\n      evidence: 'Updated Evidence'\n    });\n    component.onSaveEditing(0);\n    expect(saveEditingSpy).toHaveBeenCalledWith({\n      ...mockReportObligations[0],\n      description: 'Updated Activity',\n      evidence: 'Updated Evidence'\n    });\n    expect(component.editingRow).toBeNull();\n  });\n  it('should emit next step', () => {\n    const nextStepSpy = spyOn(component.nextStep, 'emit');\n    component.onNextStep();\n    expect(nextStepSpy).toHaveBeenCalled();\n  });\n  it('should emit previous step', () => {\n    const previousStepSpy = spyOn(component.previousStep, 'emit');\n    component.onPreviousStep();\n    expect(previousStepSpy).toHaveBeenCalled();\n  });\n  it('should emit open obligations dialog', () => {\n    const openDialogSpy = spyOn(component.openObligationsDialog, 'emit');\n    component.onOpenObligationsDialog();\n    expect(openDialogSpy).toHaveBeenCalled();\n  });\n  it('should handle changes in obligations', () => {\n    const currentObligations = [...mockObligations];\n    const newObligationToAdd = {\n      id: 4,\n      name: 'New Obligation',\n      contractId: 1,\n      number: 4\n    };\n    component.obligations = [...currentObligations, newObligationToAdd];\n    const initialReportObligations = component.reportObligations.filter(ro => ro.obligationId !== 4);\n    component.reportObligations = [...initialReportObligations];\n    component.ngOnChanges({\n      obligations: {\n        currentValue: component.obligations,\n        previousValue: currentObligations,\n        firstChange: false,\n        isFirstChange: () => false\n      },\n      reportObligations: {\n        currentValue: component.reportObligations,\n        previousValue: initialReportObligations,\n        firstChange: false,\n        isFirstChange: () => false\n      }\n    });\n    fixture.detectChanges();\n    expect(component.reportObligations.some(ro => ro.obligationId === 4)).toBeTrue();\n    expect(component.reportObligations.map(ro => ro.obligationId)).toEqual([1, 2, 3, 4]);\n  });\n  it('should display correct columns based on supervisor status', () => {\n    expect(component.displayedColumns).toContain('number');\n    expect(component.displayedColumns).toContain('actions');\n    component.isSupervisor = true;\n    fixture.detectChanges();\n    expect(component.displayedColumns).toContain('number');\n    expect(component.displayedColumns).not.toContain('actions');\n  });\n  describe('form validation', () => {\n    it('should validate required fields', () => {\n      component.startEditing(0);\n      component.editForm.patchValue({\n        activity: 'Test Activity',\n        evidence: 'Test Evidence'\n      });\n      expect(component.editForm.valid).toBeTrue();\n    });\n  });\n  describe('data loading and sorting', () => {\n    it('should handle loading states and sort correctly initially', () => {\n      expect(component.reportObligations.length).toBe(3);\n      expect(component.reportObligations.map(ro => ro.obligationId)).toEqual([1, 3, 2]);\n    });\n    it('should sort obligations correctly after ngOnChanges if obligation numbers change order', () => {\n      const newObligationsOrder = [{\n        id: 2,\n        name: 'Obligation 2',\n        contractId: 1,\n        number: 20\n      }, {\n        id: 3,\n        name: 'Obligation 3',\n        contractId: 1,\n        number: 30\n      }, {\n        id: 1,\n        name: 'Obligation 1',\n        contractId: 1,\n        number: 10\n      }];\n      component.obligations = [...newObligationsOrder];\n      component.reportObligations = [...mockReportObligations];\n      component.ngOnChanges({\n        obligations: {\n          currentValue: component.obligations,\n          previousValue: mockObligations,\n          firstChange: false,\n          isFirstChange: () => false\n        }\n      });\n      fixture.detectChanges();\n      expect(component.reportObligations.map(ro => ro.obligationId)).toEqual([1, 2, 3]);\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "TestBed", "FormBuilder", "ReactiveFormsModule", "BrowserAnimationsModule", "MonthlyReportObligationsComponent", "describe", "component", "fixture", "mockObligations", "id", "name", "contractId", "number", "mockReportObligations", "monthlyReportId", "obligationId", "description", "evidence", "filePath", "mockMonthlyReport", "reportNumber", "startDate", "Date", "endDate", "creationDate", "contractorContractId", "beforeEach", "configureTestingModule", "imports", "providers", "createComponent", "componentInstance", "monthlyReport", "obligations", "reportObligations", "detectChanges", "it", "expect", "toBeTruthy", "toEqual", "length", "toBe", "getObligationName", "num", "getObligationNumber", "numUnknown", "startEditing", "editingRow", "editForm", "get", "value", "cancelEditing", "toBeNull", "saveEditingSpy", "spyOn", "saveEditing", "patchValue", "activity", "onSaveEditing", "toHaveBeenCalledWith", "nextStepSpy", "nextStep", "onNextStep", "toHaveBeenCalled", "previousStepSpy", "previousStep", "onPreviousStep", "openDialogSpy", "openObligationsDialog", "onOpenObligationsDialog", "currentObligations", "newObligationToAdd", "initialReportObligations", "filter", "ro", "ngOnChanges", "currentValue", "previousValue", "firstChange", "isFirstChange", "some", "toBeTrue", "map", "displayedColumns", "toContain", "isSupervisor", "not", "valid", "newObligationsOrder"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\components\\monthly-reports-tab\\monthly-report-dialog\\monthly-report-obligations\\monthly-report-obligations.component.spec.ts"], "sourcesContent": ["import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { ComponentFixture, TestBed } from '@angular/core/testing';\nimport { FormBuilder, ReactiveFormsModule } from '@angular/forms';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { Obligation } from '@contract-management/models/obligation.model';\nimport { MonthlyReport } from '@contractor-dashboard/models/monthly-report.model';\nimport { ReportObligation } from '@contractor-dashboard/models/report-obligation.model';\nimport { MonthlyReportObligationsComponent } from './monthly-report-obligations.component';\n\ndescribe('MonthlyReportObligationsComponent', () => {\n  let component: MonthlyReportObligationsComponent;\n  let fixture: ComponentFixture<MonthlyReportObligationsComponent>;\n\n  const mockObligations: Obligation[] = [\n    { id: 1, name: 'Obligation 1', contractId: 1, number: 1 },\n    { id: 2, name: 'Obligation 2', contractId: 1, number: 2 },\n    { id: 3, name: 'Obligation 3', contractId: 1, number: 3 },\n  ];\n\n  const mockReportObligations: ReportObligation[] = [\n    {\n      id: 1,\n      monthlyReportId: 1,\n      obligationId: 1,\n      description: 'Activity 1',\n      evidence: 'Evidence 1',\n      filePath: '',\n    },\n    {\n      id: 2,\n      monthlyReportId: 1,\n      obligationId: 3,\n      description: 'Activity 3',\n      evidence: 'Evidence 3',\n      filePath: '',\n    },\n    {\n      id: 3,\n      monthlyReportId: 1,\n      obligationId: 2,\n      description: 'Activity 2',\n      evidence: 'Evidence 2',\n      filePath: '',\n    },\n  ];\n\n  const mockMonthlyReport: MonthlyReport = {\n    id: 1,\n    reportNumber: 1,\n    startDate: new Date(),\n    endDate: new Date(),\n    creationDate: new Date(),\n    contractorContractId: 1,\n  };\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [\n        MonthlyReportObligationsComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n        ReactiveFormsModule,\n      ],\n      providers: [FormBuilder],\n    });\n\n    fixture = TestBed.createComponent(MonthlyReportObligationsComponent);\n    component = fixture.componentInstance;\n    component.monthlyReport = mockMonthlyReport;\n    component.obligations = [...mockObligations];\n    component.reportObligations = [...mockReportObligations];\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should initialize with obligations', () => {\n    expect(component.obligations).toEqual(mockObligations);\n    expect(component.reportObligations.length).toBe(3);\n  });\n\n  it('should get obligation name correctly', () => {\n    const name = component.getObligationName(1);\n    expect(name).toBe('Obligation 1');\n  });\n\n  it('should get obligation number correctly', () => {\n    const num = component.getObligationNumber(2);\n    expect(num).toBe(2);\n    const numUnknown = component.getObligationNumber(999);\n    expect(numUnknown).toBe('N/A');\n  });\n\n  it('should handle unknown obligation name', () => {\n    const name = component.getObligationName(999);\n    expect(name).toBe('Unknown Obligation');\n  });\n\n  it('should start editing row', () => {\n    component.startEditing(0);\n    expect(component.editingRow).toBe(0);\n    expect(component.editForm.get('activity')?.value).toBe('Activity 1');\n    expect(component.editForm.get('evidence')?.value).toBe('Evidence 1');\n  });\n\n  it('should cancel editing', () => {\n    component.startEditing(0);\n    component.cancelEditing();\n    expect(component.editingRow).toBeNull();\n    expect(component.editForm.get('activity')?.value).toBeNull();\n    expect(component.editForm.get('evidence')?.value).toBeNull();\n  });\n\n  it('should save editing', () => {\n    const saveEditingSpy = spyOn(component.saveEditing, 'emit');\n    component.startEditing(0);\n    component.editForm.patchValue({\n      activity: 'Updated Activity',\n      evidence: 'Updated Evidence',\n    });\n    component.onSaveEditing(0);\n\n    expect(saveEditingSpy).toHaveBeenCalledWith({\n      ...mockReportObligations[0],\n      description: 'Updated Activity',\n      evidence: 'Updated Evidence',\n    });\n    expect(component.editingRow).toBeNull();\n  });\n\n  it('should emit next step', () => {\n    const nextStepSpy = spyOn(component.nextStep, 'emit');\n    component.onNextStep();\n    expect(nextStepSpy).toHaveBeenCalled();\n  });\n\n  it('should emit previous step', () => {\n    const previousStepSpy = spyOn(component.previousStep, 'emit');\n    component.onPreviousStep();\n    expect(previousStepSpy).toHaveBeenCalled();\n  });\n\n  it('should emit open obligations dialog', () => {\n    const openDialogSpy = spyOn(component.openObligationsDialog, 'emit');\n    component.onOpenObligationsDialog();\n    expect(openDialogSpy).toHaveBeenCalled();\n  });\n\n  it('should handle changes in obligations', () => {\n    const currentObligations = [...mockObligations];\n    const newObligationToAdd: Obligation = {\n      id: 4,\n      name: 'New Obligation',\n      contractId: 1,\n      number: 4,\n    };\n\n    component.obligations = [...currentObligations, newObligationToAdd];\n\n    const initialReportObligations = component.reportObligations.filter(\n      (ro) => ro.obligationId !== 4,\n    );\n    component.reportObligations = [...initialReportObligations];\n\n    component.ngOnChanges({\n      obligations: {\n        currentValue: component.obligations,\n        previousValue: currentObligations,\n        firstChange: false,\n        isFirstChange: () => false,\n      },\n      reportObligations: {\n        currentValue: component.reportObligations,\n        previousValue: initialReportObligations,\n        firstChange: false,\n        isFirstChange: () => false,\n      },\n    });\n    fixture.detectChanges();\n\n    expect(\n      component.reportObligations.some((ro) => ro.obligationId === 4),\n    ).toBeTrue();\n    expect(component.reportObligations.map((ro) => ro.obligationId)).toEqual([\n      1, 2, 3, 4,\n    ]);\n  });\n\n  it('should display correct columns based on supervisor status', () => {\n    expect(component.displayedColumns).toContain('number');\n    expect(component.displayedColumns).toContain('actions');\n    component.isSupervisor = true;\n    fixture.detectChanges();\n    expect(component.displayedColumns).toContain('number');\n    expect(component.displayedColumns).not.toContain('actions');\n  });\n\n  describe('form validation', () => {\n    it('should validate required fields', () => {\n      component.startEditing(0);\n      component.editForm.patchValue({\n        activity: 'Test Activity',\n        evidence: 'Test Evidence',\n      });\n      expect(component.editForm.valid).toBeTrue();\n    });\n  });\n\n  describe('data loading and sorting', () => {\n    it('should handle loading states and sort correctly initially', () => {\n      expect(component.reportObligations.length).toBe(3);\n      expect(component.reportObligations.map((ro) => ro.obligationId)).toEqual([\n        1, 3, 2,\n      ]);\n    });\n\n    it('should sort obligations correctly after ngOnChanges if obligation numbers change order', () => {\n      const newObligationsOrder: Obligation[] = [\n        { id: 2, name: 'Obligation 2', contractId: 1, number: 20 },\n        { id: 3, name: 'Obligation 3', contractId: 1, number: 30 },\n        { id: 1, name: 'Obligation 1', contractId: 1, number: 10 },\n      ];\n      component.obligations = [...newObligationsOrder];\n      component.reportObligations = [...mockReportObligations];\n\n      component.ngOnChanges({\n        obligations: {\n          currentValue: component.obligations,\n          previousValue: mockObligations,\n          firstChange: false,\n          isFirstChange: () => false,\n        },\n      });\n      fixture.detectChanges();\n      expect(component.reportObligations.map((ro) => ro.obligationId)).toEqual([\n        1, 2, 3,\n      ]);\n    });\n  });\n});\n"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,8BAA8B;AACtE,SAA2BC,OAAO,QAAQ,uBAAuB;AACjE,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,uBAAuB,QAAQ,sCAAsC;AAI9E,SAASC,iCAAiC,QAAQ,wCAAwC;AAE1FC,QAAQ,CAAC,mCAAmC,EAAE,MAAK;EACjD,IAAIC,SAA4C;EAChD,IAAIC,OAA4D;EAEhE,MAAMC,eAAe,GAAiB,CACpC;IAAEC,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,cAAc;IAAEC,UAAU,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAC,CAAE,EACzD;IAAEH,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,cAAc;IAAEC,UAAU,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAC,CAAE,EACzD;IAAEH,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,cAAc;IAAEC,UAAU,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAC,CAAE,CAC1D;EAED,MAAMC,qBAAqB,GAAuB,CAChD;IACEJ,EAAE,EAAE,CAAC;IACLK,eAAe,EAAE,CAAC;IAClBC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,YAAY;IACzBC,QAAQ,EAAE,YAAY;IACtBC,QAAQ,EAAE;GACX,EACD;IACET,EAAE,EAAE,CAAC;IACLK,eAAe,EAAE,CAAC;IAClBC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,YAAY;IACzBC,QAAQ,EAAE,YAAY;IACtBC,QAAQ,EAAE;GACX,EACD;IACET,EAAE,EAAE,CAAC;IACLK,eAAe,EAAE,CAAC;IAClBC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,YAAY;IACzBC,QAAQ,EAAE,YAAY;IACtBC,QAAQ,EAAE;GACX,CACF;EAED,MAAMC,iBAAiB,GAAkB;IACvCV,EAAE,EAAE,CAAC;IACLW,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,IAAIC,IAAI,EAAE;IACrBC,OAAO,EAAE,IAAID,IAAI,EAAE;IACnBE,YAAY,EAAE,IAAIF,IAAI,EAAE;IACxBG,oBAAoB,EAAE;GACvB;EAEDC,UAAU,CAAC,MAAK;IACd1B,OAAO,CAAC2B,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPxB,iCAAiC,EACjCL,uBAAuB,EACvBI,uBAAuB,EACvBD,mBAAmB,CACpB;MACD2B,SAAS,EAAE,CAAC5B,WAAW;KACxB,CAAC;IAEFM,OAAO,GAAGP,OAAO,CAAC8B,eAAe,CAAC1B,iCAAiC,CAAC;IACpEE,SAAS,GAAGC,OAAO,CAACwB,iBAAiB;IACrCzB,SAAS,CAAC0B,aAAa,GAAGb,iBAAiB;IAC3Cb,SAAS,CAAC2B,WAAW,GAAG,CAAC,GAAGzB,eAAe,CAAC;IAC5CF,SAAS,CAAC4B,iBAAiB,GAAG,CAAC,GAAGrB,qBAAqB,CAAC;IACxDN,OAAO,CAAC4B,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAAC/B,SAAS,CAAC,CAACgC,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,oCAAoC,EAAE,MAAK;IAC5CC,MAAM,CAAC/B,SAAS,CAAC2B,WAAW,CAAC,CAACM,OAAO,CAAC/B,eAAe,CAAC;IACtD6B,MAAM,CAAC/B,SAAS,CAAC4B,iBAAiB,CAACM,MAAM,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;EACpD,CAAC,CAAC;EAEFL,EAAE,CAAC,sCAAsC,EAAE,MAAK;IAC9C,MAAM1B,IAAI,GAAGJ,SAAS,CAACoC,iBAAiB,CAAC,CAAC,CAAC;IAC3CL,MAAM,CAAC3B,IAAI,CAAC,CAAC+B,IAAI,CAAC,cAAc,CAAC;EACnC,CAAC,CAAC;EAEFL,EAAE,CAAC,wCAAwC,EAAE,MAAK;IAChD,MAAMO,GAAG,GAAGrC,SAAS,CAACsC,mBAAmB,CAAC,CAAC,CAAC;IAC5CP,MAAM,CAACM,GAAG,CAAC,CAACF,IAAI,CAAC,CAAC,CAAC;IACnB,MAAMI,UAAU,GAAGvC,SAAS,CAACsC,mBAAmB,CAAC,GAAG,CAAC;IACrDP,MAAM,CAACQ,UAAU,CAAC,CAACJ,IAAI,CAAC,KAAK,CAAC;EAChC,CAAC,CAAC;EAEFL,EAAE,CAAC,uCAAuC,EAAE,MAAK;IAC/C,MAAM1B,IAAI,GAAGJ,SAAS,CAACoC,iBAAiB,CAAC,GAAG,CAAC;IAC7CL,MAAM,CAAC3B,IAAI,CAAC,CAAC+B,IAAI,CAAC,oBAAoB,CAAC;EACzC,CAAC,CAAC;EAEFL,EAAE,CAAC,0BAA0B,EAAE,MAAK;IAClC9B,SAAS,CAACwC,YAAY,CAAC,CAAC,CAAC;IACzBT,MAAM,CAAC/B,SAAS,CAACyC,UAAU,CAAC,CAACN,IAAI,CAAC,CAAC,CAAC;IACpCJ,MAAM,CAAC/B,SAAS,CAAC0C,QAAQ,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACT,IAAI,CAAC,YAAY,CAAC;IACpEJ,MAAM,CAAC/B,SAAS,CAAC0C,QAAQ,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACT,IAAI,CAAC,YAAY,CAAC;EACtE,CAAC,CAAC;EAEFL,EAAE,CAAC,uBAAuB,EAAE,MAAK;IAC/B9B,SAAS,CAACwC,YAAY,CAAC,CAAC,CAAC;IACzBxC,SAAS,CAAC6C,aAAa,EAAE;IACzBd,MAAM,CAAC/B,SAAS,CAACyC,UAAU,CAAC,CAACK,QAAQ,EAAE;IACvCf,MAAM,CAAC/B,SAAS,CAAC0C,QAAQ,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACE,QAAQ,EAAE;IAC5Df,MAAM,CAAC/B,SAAS,CAAC0C,QAAQ,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,CAAC,CAACE,QAAQ,EAAE;EAC9D,CAAC,CAAC;EAEFhB,EAAE,CAAC,qBAAqB,EAAE,MAAK;IAC7B,MAAMiB,cAAc,GAAGC,KAAK,CAAChD,SAAS,CAACiD,WAAW,EAAE,MAAM,CAAC;IAC3DjD,SAAS,CAACwC,YAAY,CAAC,CAAC,CAAC;IACzBxC,SAAS,CAAC0C,QAAQ,CAACQ,UAAU,CAAC;MAC5BC,QAAQ,EAAE,kBAAkB;MAC5BxC,QAAQ,EAAE;KACX,CAAC;IACFX,SAAS,CAACoD,aAAa,CAAC,CAAC,CAAC;IAE1BrB,MAAM,CAACgB,cAAc,CAAC,CAACM,oBAAoB,CAAC;MAC1C,GAAG9C,qBAAqB,CAAC,CAAC,CAAC;MAC3BG,WAAW,EAAE,kBAAkB;MAC/BC,QAAQ,EAAE;KACX,CAAC;IACFoB,MAAM,CAAC/B,SAAS,CAACyC,UAAU,CAAC,CAACK,QAAQ,EAAE;EACzC,CAAC,CAAC;EAEFhB,EAAE,CAAC,uBAAuB,EAAE,MAAK;IAC/B,MAAMwB,WAAW,GAAGN,KAAK,CAAChD,SAAS,CAACuD,QAAQ,EAAE,MAAM,CAAC;IACrDvD,SAAS,CAACwD,UAAU,EAAE;IACtBzB,MAAM,CAACuB,WAAW,CAAC,CAACG,gBAAgB,EAAE;EACxC,CAAC,CAAC;EAEF3B,EAAE,CAAC,2BAA2B,EAAE,MAAK;IACnC,MAAM4B,eAAe,GAAGV,KAAK,CAAChD,SAAS,CAAC2D,YAAY,EAAE,MAAM,CAAC;IAC7D3D,SAAS,CAAC4D,cAAc,EAAE;IAC1B7B,MAAM,CAAC2B,eAAe,CAAC,CAACD,gBAAgB,EAAE;EAC5C,CAAC,CAAC;EAEF3B,EAAE,CAAC,qCAAqC,EAAE,MAAK;IAC7C,MAAM+B,aAAa,GAAGb,KAAK,CAAChD,SAAS,CAAC8D,qBAAqB,EAAE,MAAM,CAAC;IACpE9D,SAAS,CAAC+D,uBAAuB,EAAE;IACnChC,MAAM,CAAC8B,aAAa,CAAC,CAACJ,gBAAgB,EAAE;EAC1C,CAAC,CAAC;EAEF3B,EAAE,CAAC,sCAAsC,EAAE,MAAK;IAC9C,MAAMkC,kBAAkB,GAAG,CAAC,GAAG9D,eAAe,CAAC;IAC/C,MAAM+D,kBAAkB,GAAe;MACrC9D,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,gBAAgB;MACtBC,UAAU,EAAE,CAAC;MACbC,MAAM,EAAE;KACT;IAEDN,SAAS,CAAC2B,WAAW,GAAG,CAAC,GAAGqC,kBAAkB,EAAEC,kBAAkB,CAAC;IAEnE,MAAMC,wBAAwB,GAAGlE,SAAS,CAAC4B,iBAAiB,CAACuC,MAAM,CAChEC,EAAE,IAAKA,EAAE,CAAC3D,YAAY,KAAK,CAAC,CAC9B;IACDT,SAAS,CAAC4B,iBAAiB,GAAG,CAAC,GAAGsC,wBAAwB,CAAC;IAE3DlE,SAAS,CAACqE,WAAW,CAAC;MACpB1C,WAAW,EAAE;QACX2C,YAAY,EAAEtE,SAAS,CAAC2B,WAAW;QACnC4C,aAAa,EAAEP,kBAAkB;QACjCQ,WAAW,EAAE,KAAK;QAClBC,aAAa,EAAEA,CAAA,KAAM;OACtB;MACD7C,iBAAiB,EAAE;QACjB0C,YAAY,EAAEtE,SAAS,CAAC4B,iBAAiB;QACzC2C,aAAa,EAAEL,wBAAwB;QACvCM,WAAW,EAAE,KAAK;QAClBC,aAAa,EAAEA,CAAA,KAAM;;KAExB,CAAC;IACFxE,OAAO,CAAC4B,aAAa,EAAE;IAEvBE,MAAM,CACJ/B,SAAS,CAAC4B,iBAAiB,CAAC8C,IAAI,CAAEN,EAAE,IAAKA,EAAE,CAAC3D,YAAY,KAAK,CAAC,CAAC,CAChE,CAACkE,QAAQ,EAAE;IACZ5C,MAAM,CAAC/B,SAAS,CAAC4B,iBAAiB,CAACgD,GAAG,CAAER,EAAE,IAAKA,EAAE,CAAC3D,YAAY,CAAC,CAAC,CAACwB,OAAO,CAAC,CACvE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CACX,CAAC;EACJ,CAAC,CAAC;EAEFH,EAAE,CAAC,2DAA2D,EAAE,MAAK;IACnEC,MAAM,CAAC/B,SAAS,CAAC6E,gBAAgB,CAAC,CAACC,SAAS,CAAC,QAAQ,CAAC;IACtD/C,MAAM,CAAC/B,SAAS,CAAC6E,gBAAgB,CAAC,CAACC,SAAS,CAAC,SAAS,CAAC;IACvD9E,SAAS,CAAC+E,YAAY,GAAG,IAAI;IAC7B9E,OAAO,CAAC4B,aAAa,EAAE;IACvBE,MAAM,CAAC/B,SAAS,CAAC6E,gBAAgB,CAAC,CAACC,SAAS,CAAC,QAAQ,CAAC;IACtD/C,MAAM,CAAC/B,SAAS,CAAC6E,gBAAgB,CAAC,CAACG,GAAG,CAACF,SAAS,CAAC,SAAS,CAAC;EAC7D,CAAC,CAAC;EAEF/E,QAAQ,CAAC,iBAAiB,EAAE,MAAK;IAC/B+B,EAAE,CAAC,iCAAiC,EAAE,MAAK;MACzC9B,SAAS,CAACwC,YAAY,CAAC,CAAC,CAAC;MACzBxC,SAAS,CAAC0C,QAAQ,CAACQ,UAAU,CAAC;QAC5BC,QAAQ,EAAE,eAAe;QACzBxC,QAAQ,EAAE;OACX,CAAC;MACFoB,MAAM,CAAC/B,SAAS,CAAC0C,QAAQ,CAACuC,KAAK,CAAC,CAACN,QAAQ,EAAE;IAC7C,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF5E,QAAQ,CAAC,0BAA0B,EAAE,MAAK;IACxC+B,EAAE,CAAC,2DAA2D,EAAE,MAAK;MACnEC,MAAM,CAAC/B,SAAS,CAAC4B,iBAAiB,CAACM,MAAM,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;MAClDJ,MAAM,CAAC/B,SAAS,CAAC4B,iBAAiB,CAACgD,GAAG,CAAER,EAAE,IAAKA,EAAE,CAAC3D,YAAY,CAAC,CAAC,CAACwB,OAAO,CAAC,CACvE,CAAC,EAAE,CAAC,EAAE,CAAC,CACR,CAAC;IACJ,CAAC,CAAC;IAEFH,EAAE,CAAC,wFAAwF,EAAE,MAAK;MAChG,MAAMoD,mBAAmB,GAAiB,CACxC;QAAE/E,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE,cAAc;QAAEC,UAAU,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAE,EAC1D;QAAEH,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE,cAAc;QAAEC,UAAU,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAE,EAC1D;QAAEH,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE,cAAc;QAAEC,UAAU,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAE,CAC3D;MACDN,SAAS,CAAC2B,WAAW,GAAG,CAAC,GAAGuD,mBAAmB,CAAC;MAChDlF,SAAS,CAAC4B,iBAAiB,GAAG,CAAC,GAAGrB,qBAAqB,CAAC;MAExDP,SAAS,CAACqE,WAAW,CAAC;QACpB1C,WAAW,EAAE;UACX2C,YAAY,EAAEtE,SAAS,CAAC2B,WAAW;UACnC4C,aAAa,EAAErE,eAAe;UAC9BsE,WAAW,EAAE,KAAK;UAClBC,aAAa,EAAEA,CAAA,KAAM;;OAExB,CAAC;MACFxE,OAAO,CAAC4B,aAAa,EAAE;MACvBE,MAAM,CAAC/B,SAAS,CAAC4B,iBAAiB,CAACgD,GAAG,CAAER,EAAE,IAAKA,EAAE,CAAC3D,YAAY,CAAC,CAAC,CAACwB,OAAO,CAAC,CACvE,CAAC,EAAE,CAAC,EAAE,CAAC,CACR,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}