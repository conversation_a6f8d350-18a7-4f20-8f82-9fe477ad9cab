import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { CompleteContract } from '@contract-management/models/complete-contract.model';
import { ContractDetails } from '@contract-management/models/contract-details.model';
import { ContractValues } from '@contract-management/models/contract-values.model';
import { Contract } from '@contract-management/models/contract.model';
import { ContractContractor } from '@contract-management/models/contract_contractor.model';
import { environment } from '@env';
import { ContractService } from './contract.service';
import { AuthService } from '@core/auth/services/auth.service';
import { ContractAuditHistoryService } from './contract-audit-history.service';
import { ContractAuditStatusService } from './contract-audit-status.service';
import { of } from 'rxjs';
import { User } from '@core/auth/models/user.model';
import { ContractAuditStatus } from '../models/contract-audit-status.model';
import { ContractAuditHistory } from '../models/contract-audit-history.model';

describe('ContractService', () => {
  let service: ContractService;
  let httpMock: HttpTestingController;
  let authServiceSpy: jasmine.SpyObj<AuthService>;
  let contractAuditHistoryServiceSpy: jasmine.SpyObj<ContractAuditHistoryService>;
  let contractAuditStatusServiceSpy: jasmine.SpyObj<ContractAuditStatusService>;
  const apiUrl = `${environment.apiUrl}/contracts`;

  const mockContract: Contract = {
    id: 1,
    contractNumber: 123,
    object: 'Test Object',
    contractTypeId: 1,
    contractClassId: 1,
    contractYearId: 1,
    statusId: 1,
    rup: true,
    secopCode: 1,
    addition: false,
    cession: false,
    settled: false,
    monthlyPayment: 1000000,
    causesSelectionId: 1,
    managementSupportId: 1,
  };

  const mockUser: User = {
    id: 1,
    username: 'testuser',
    profiles: [],
  };

  const mockAuditStatus: ContractAuditStatus = {
    id: 1,
    name: 'Creación de contrato',
    description: 'Contract creation status',
  };

  const mockContractDetails: ContractDetails = {
    id: 1,
    contractNumber: 123,
    object: 'Test Object',
    contractTypeName: 'Test Type',
    statusName: 'Active',
    contractorId: 1,
    fullName: 'Test Contractor',
    contractorIdNumber: 123456789,
    rup: true,
    addition: false,
    cession: false,
    settled: false,
    hasCcp: false,
    selectionModalityName: 'Test Modality',
    trackingTypeName: 'Test Tracking',
    dependencyName: 'Test Dependency',
    groupName: 'Test Group',
    contractorEmail: '<EMAIL>',
    monthlyPayment: 1000000,
    supervisorFullName: 'Test Supervisor',
    supervisorIdNumber: '987654321',
    supervisorPosition: 'Test Position',
  };

  beforeEach(() => {
    authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);
    contractAuditHistoryServiceSpy = jasmine.createSpyObj(
      'ContractAuditHistoryService',
      ['create'],
    );
    contractAuditStatusServiceSpy = jasmine.createSpyObj(
      'ContractAuditStatusService',
      ['getByName'],
    );

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        ContractService,
        { provide: AuthService, useValue: authServiceSpy },
        {
          provide: ContractAuditHistoryService,
          useValue: contractAuditHistoryServiceSpy,
        },
        {
          provide: ContractAuditStatusService,
          useValue: contractAuditStatusServiceSpy,
        },
      ],
    });
    service = TestBed.inject(ContractService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all contracts', () => {
      const mockContracts: Contract[] = [mockContract];

      service.getAll().subscribe((contracts) => {
        expect(contracts).toEqual(mockContracts);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockContracts);
    });
  });

  describe('getById', () => {
    it('should return contract by id', () => {
      service.getById(1).subscribe((result) => {
        expect(result).toEqual(mockContract);
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('GET');
      req.flush(mockContract);
    });
  });

  describe('getAllDetails', () => {
    it('should return all contract details', () => {
      const mockContractDetailsArray: ContractDetails[] = [mockContractDetails];

      service.getAllDetails().subscribe((result) => {
        expect(result).toEqual(mockContractDetailsArray);
      });

      const req = httpMock.expectOne(`${apiUrl}-details`);
      expect(req.request.method).toBe('GET');
      req.flush(mockContractDetailsArray);
    });
  });

  describe('getDetailsById', () => {
    it('should return contract details by id', () => {
      service.getDetailsById(1).subscribe((result) => {
        expect(result).toEqual(mockContractDetails);
      });

      const req = httpMock.expectOne(`${apiUrl}-details/1`);
      expect(req.request.method).toBe('GET');
      req.flush(mockContractDetails);
    });
  });

  describe('getByContractorIdNumber', () => {
    it('should return contracts by contractor id number', () => {
      const mockContracts: Contract[] = [mockContract];

      service.getByContractorIdNumber(123456789).subscribe((result) => {
        expect(result).toEqual(mockContracts);
      });

      const req = httpMock.expectOne(`${apiUrl}/contractor/123456789`);
      expect(req.request.method).toBe('GET');
      req.flush(mockContracts);
    });
  });

  describe('create', () => {
    it('should create contract', () => {
      const mockContractRequest: Omit<Contract, 'id'> = {
        contractNumber: 123,
        object: 'Test Object',
        contractTypeId: 1,
        contractClassId: 1,
        contractYearId: 1,
        statusId: 1,
        rup: true,
        secopCode: 1,
        addition: false,
        cession: false,
        settled: false,
        monthlyPayment: 1000000,
        causesSelectionId: 1,
        managementSupportId: 1,
      };

      service.create(mockContractRequest).subscribe((result) => {
        expect(result).toEqual(mockContract);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(mockContractRequest);
      req.flush(mockContract);
    });
  });

  describe('createCompleteContract', () => {
    it('should create a complete contract with audit history when user is logged in', () => {
      const mockContractValues: Omit<ContractValues, 'id'> = {
        numericValue: 1000000,
        madsValue: 0,
        isOtherEntity: false,
        subscriptionDate: new Date().toISOString(),
        cdp: 123,
        cdpEntityId: 1,
        cdpEntity: { id: 1, name: 'Test CDP Entity' },
      };

      const mockContractContractor: Omit<ContractContractor, 'id'> = {
        warranty: false,
        supervisorId: 1,
      };

      const mockCompleteContract: CompleteContract = {
        contractorIdNumber: 123456789,
        contract: mockContract,
        contractValues: mockContractValues,
        contractContractor: mockContractContractor,
      };

      const data = {
        contractorIdNumber: 123456789,
        contract: mockContract,
        contractContractor: mockContractContractor,
        contractValues: mockContractValues,
      };

      authServiceSpy.getCurrentUser.and.returnValue(mockUser);
      contractAuditStatusServiceSpy.getByName.and.returnValue(
        of(mockAuditStatus),
      );

      const mockAuditHistory: ContractAuditHistory = {
        id: 1,
        contractId: 1,
        auditStatusId: 1,
        auditDate: new Date(),
        comment: 'Test comment',
        auditorId: 1,
      };

      contractAuditHistoryServiceSpy.create.and.returnValue(
        of(mockAuditHistory),
      );

      service.createCompleteContract(data).subscribe((result) => {
        expect(result).toEqual(mockCompleteContract);
        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();
        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith(
          'Creación de contrato',
        );
        expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();
      });

      const req = httpMock.expectOne(`${apiUrl}/complete`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(data);
      req.flush(mockCompleteContract);

      const contractorReq = httpMock.expectOne(
        '/api/contractors/id-number/123456789',
      );
      expect(contractorReq.request.method).toBe('GET');
      contractorReq.flush({ id: 1, fullName: 'Test Contractor' });

      const emailReq = httpMock.expectOne(
        '/api/email-templates/send/new_contract_notification',
      );
      expect(emailReq.request.method).toBe('POST');
      emailReq.flush({ success: true });
    });

    it('should create a complete contract without audit history when user is not logged in', () => {
      const mockContractValues: Omit<ContractValues, 'id'> = {
        numericValue: 1000000,
        madsValue: 0,
        isOtherEntity: false,
        subscriptionDate: new Date().toISOString(),
        cdp: 123,
        cdpEntityId: 1,
        cdpEntity: { id: 1, name: 'Test CDP Entity' },
      };

      const mockContractContractor: Omit<ContractContractor, 'id'> = {
        warranty: false,
        supervisorId: 1,
      };

      const mockCompleteContract: CompleteContract = {
        contractorIdNumber: 123456789,
        contract: mockContract,
        contractValues: mockContractValues,
        contractContractor: mockContractContractor,
      };

      const data = {
        contractorIdNumber: 123456789,
        contract: mockContract,
        contractContractor: mockContractContractor,
        contractValues: mockContractValues,
      };

      authServiceSpy.getCurrentUser.and.returnValue(null);

      service.createCompleteContract(data).subscribe((result) => {
        expect(result).toEqual(mockCompleteContract);
        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();
        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();
        expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();
      });

      const req = httpMock.expectOne(`${apiUrl}/complete`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(data);
      req.flush(mockCompleteContract);
    });
  });

  describe('update', () => {
    it('should update contract', () => {
      const mockContractRequest: Partial<Contract> = {
        object: 'Updated Object',
      };

      service.update(1, mockContractRequest).subscribe((result) => {
        expect(result).toEqual({ ...mockContract, object: 'Updated Object' });
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(mockContractRequest);
      req.flush({ ...mockContract, object: 'Updated Object' });
    });
  });

  describe('delete', () => {
    it('should delete contract', () => {
      service.delete(1).subscribe((result) => {
        expect(result).toBeDefined();
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('DELETE');
      req.flush({});
    });
  });

  describe('validateContractNumber', () => {
    it('should validate contract number', () => {
      service.validateContractNumber(123, 1).subscribe((result) => {
        expect(result).toBe(true);
      });

      const req = httpMock.expectOne(`${apiUrl}/validate/123/1`);
      expect(req.request.method).toBe('GET');
      req.flush(true);
    });
  });

  describe('exportContracts', () => {
    it('should export contracts as blob', () => {
      const blob = new Blob(['test data'], {
        type: 'application/vnd.ms-excel',
      });

      service.exportContracts(2024).subscribe((result) => {
        expect(result instanceof Blob).toBe(true);
      });

      const req = httpMock.expectOne(`${apiUrl}/export/2024`);
      expect(req.request.method).toBe('GET');
      expect(req.request.responseType).toBe('blob');
      req.flush(blob);
    });
  });
});