{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/Github/SICMA/sicma-ui/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { InitialReportDocumentationService } from '@contractor-dashboard/services/initial-report-documentation.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { of, throwError } from 'rxjs';\nimport { MonthlyReportInitialDocumentationComponent } from './monthly-report-initial-documentation.component';\ndescribe('MonthlyReportInitialDocumentationComponent', () => {\n  let component;\n  let fixture;\n  let documentationService;\n  let alertService;\n  const mockDocumentation = {\n    id: 1,\n    contractorContractId: 1,\n    bankId: 1,\n    accountNumber: '123456',\n    bankAccountTypeId: 1,\n    taxRegimeId: 1,\n    epsId: 1,\n    arlId: 1,\n    pensionFundId: 1,\n    hasDependents: false,\n    hasHousingInterest: false,\n    housingInterestAnnualPayment: 0,\n    hasPrepaidMedicine: false,\n    prepaidMedicineAnnualPayment: 0,\n    hasAfcAccount: false,\n    hasVoluntarySavings: false,\n    afcAccountAnnualPayment: 0,\n    voluntarySavingsAnnualPayment: 0\n  };\n  beforeEach(() => {\n    const documentationServiceSpy = jasmine.createSpyObj('InitialReportDocumentationService', ['getByContractorContractId', 'createWithFiles', 'updateWithFiles']);\n    const alertServiceSpy = jasmine.createSpyObj('AlertService', ['success', 'error', 'warning']);\n    TestBed.configureTestingModule({\n      imports: [MonthlyReportInitialDocumentationComponent, HttpClientTestingModule, BrowserAnimationsModule],\n      providers: [{\n        provide: InitialReportDocumentationService,\n        useValue: documentationServiceSpy\n      }, {\n        provide: AlertService,\n        useValue: alertServiceSpy\n      }]\n    });\n    fixture = TestBed.createComponent(MonthlyReportInitialDocumentationComponent);\n    component = fixture.componentInstance;\n    documentationService = TestBed.inject(InitialReportDocumentationService);\n    alertService = TestBed.inject(AlertService);\n    documentationService.getByContractorContractId.and.returnValue(of(mockDocumentation));\n    documentationService.createWithFiles.and.returnValue(of(mockDocumentation));\n    documentationService.updateWithFiles.and.returnValue(of(mockDocumentation));\n    component.contractorContractId = 1;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  describe('Form Validation', () => {\n    it('should initialize forms after view init', () => {\n      component.ngAfterViewInit();\n      expect(component.validateForms()).toBeFalse();\n    });\n    it('should check form validity', () => {\n      const validityEmitSpy = spyOn(component.formValidityChange, 'emit');\n      component.checkFormValidity();\n      expect(validityEmitSpy).toHaveBeenCalled();\n    });\n  });\n  describe('Data Loading', () => {\n    it('should load initial documentation', () => {\n      component.loadInitialDocumentation();\n      expect(documentationService.getByContractorContractId).toHaveBeenCalledWith(1);\n      expect(component.initialDocumentation).toEqual(mockDocumentation);\n    });\n    it('should handle documentation loading error', () => {\n      documentationService.getByContractorContractId.and.returnValue(throwError(() => new Error()));\n      component.loadInitialDocumentation();\n      expect(alertService.error).toHaveBeenCalledWith('Error al cargar la documentación inicial');\n    });\n    it('should ignore 404 errors when loading documentation', () => {\n      documentationService.getByContractorContractId.and.returnValue(throwError(() => ({\n        status: 404\n      })));\n      component.loadInitialDocumentation();\n      expect(alertService.error).not.toHaveBeenCalled();\n    });\n  });\n  describe('Data Saving', () => {\n    let validateFormsSpy;\n    beforeEach(() => {\n      validateFormsSpy = spyOn(component, 'validateForms').and.returnValue(true);\n    });\n    it('should validate forms before saving', /*#__PURE__*/_asyncToGenerator(function* () {\n      validateFormsSpy.and.returnValue(false);\n      const result = yield component.saveInitialDocumentation();\n      expect(result).toBeFalse();\n      expect(alertService.warning).toHaveBeenCalledWith('Por favor, complete todos los campos requeridos.');\n    }));\n    it('should create new documentation', /*#__PURE__*/_asyncToGenerator(function* () {\n      component.initialDocumentation = undefined;\n      documentationService.createWithFiles.and.returnValue(of(mockDocumentation));\n      const result = yield component.saveInitialDocumentation();\n      expect(result).toBeTrue();\n      expect(documentationService.createWithFiles).toHaveBeenCalled();\n      expect(alertService.success).toHaveBeenCalledWith('Documentación inicial guardada con éxito');\n    }));\n    it('should update existing documentation', /*#__PURE__*/_asyncToGenerator(function* () {\n      component.initialDocumentation = mockDocumentation;\n      const result = yield component.saveInitialDocumentation();\n      expect(result).toBeTrue();\n      expect(documentationService.updateWithFiles).toHaveBeenCalled();\n      expect(alertService.success).toHaveBeenCalledWith('Documentación inicial actualizada con éxito');\n    }));\n    it('should handle save error', /*#__PURE__*/_asyncToGenerator(function* () {\n      component.initialDocumentation = undefined;\n      documentationService.createWithFiles.and.returnValue(throwError(() => new Error('Error')));\n      const result = yield component.saveInitialDocumentation();\n      expect(result).toBeFalse();\n      expect(alertService.error).toHaveBeenCalledWith('Error al guardar la documentación inicial');\n    }));\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "TestBed", "BrowserAnimationsModule", "InitialReportDocumentationService", "AlertService", "of", "throwError", "MonthlyReportInitialDocumentationComponent", "describe", "component", "fixture", "documentationService", "alertService", "mockDocumentation", "id", "contractorContractId", "bankId", "accountNumber", "bankAccountTypeId", "taxRegimeId", "epsId", "arlId", "pensionFundId", "hasDependents", "hasHousingInterest", "housingInterestAnnualPayment", "hasPrepaidMedicine", "prepaidMedicineAnnualPayment", "hasAfcAccount", "hasVoluntarySavings", "afcAccountAnnualPayment", "voluntarySavingsAnnualPayment", "beforeEach", "documentationServiceSpy", "jasmine", "createSpyObj", "alertServiceSpy", "configureTestingModule", "imports", "providers", "provide", "useValue", "createComponent", "componentInstance", "inject", "getByContractorContractId", "and", "returnValue", "createWithFiles", "updateWithFiles", "detectChanges", "it", "expect", "toBeTruthy", "ngAfterViewInit", "validateForms", "toBeFalse", "validityEmitSpy", "spyOn", "formValidityChange", "checkFormValidity", "toHaveBeenCalled", "loadInitialDocumentation", "toHaveBeenCalledWith", "initialDocumentation", "toEqual", "Error", "error", "status", "not", "validateFormsSpy", "_asyncToGenerator", "result", "saveInitialDocumentation", "warning", "undefined", "toBeTrue", "success"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\components\\monthly-reports-tab\\monthly-report-dialog\\monthly-report-initial-documentation\\monthly-report-initial-documentation.component.spec.ts"], "sourcesContent": ["import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { ComponentFixture, TestBed } from '@angular/core/testing';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { InitialReportDocumentation } from '@contractor-dashboard/models/initial-report-documentation.model';\nimport { InitialReportDocumentationService } from '@contractor-dashboard/services/initial-report-documentation.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { of, throwError } from 'rxjs';\nimport { MonthlyReportInitialDocumentationComponent } from './monthly-report-initial-documentation.component';\n\ndescribe('MonthlyReportInitialDocumentationComponent', () => {\n  let component: MonthlyReportInitialDocumentationComponent;\n  let fixture: ComponentFixture<MonthlyReportInitialDocumentationComponent>;\n  let documentationService: jasmine.SpyObj<InitialReportDocumentationService>;\n  let alertService: jasmine.SpyObj<AlertService>;\n\n  const mockDocumentation: InitialReportDocumentation = {\n    id: 1,\n    contractorContractId: 1,\n    bankId: 1,\n    accountNumber: '123456',\n    bankAccountTypeId: 1,\n    taxRegimeId: 1,\n    epsId: 1,\n    arlId: 1,\n    pensionFundId: 1,\n    hasDependents: false,\n    hasHousingInterest: false,\n    housingInterestAnnualPayment: 0,\n    hasPrepaidMedicine: false,\n    prepaidMedicineAnnualPayment: 0,\n    hasAfcAccount: false,\n    hasVoluntarySavings: false,\n    afcAccountAnnualPayment: 0,\n    voluntarySavingsAnnualPayment: 0,\n  };\n\n  beforeEach(() => {\n    const documentationServiceSpy = jasmine.createSpyObj(\n      'InitialReportDocumentationService',\n      ['getByContractorContractId', 'createWithFiles', 'updateWithFiles'],\n    );\n    const alertServiceSpy = jasmine.createSpyObj('AlertService', [\n      'success',\n      'error',\n      'warning',\n    ]);\n\n    TestBed.configureTestingModule({\n      imports: [\n        MonthlyReportInitialDocumentationComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n      ],\n      providers: [\n        {\n          provide: InitialReportDocumentationService,\n          useValue: documentationServiceSpy,\n        },\n        { provide: AlertService, useValue: alertServiceSpy },\n      ],\n    });\n\n    fixture = TestBed.createComponent(\n      MonthlyReportInitialDocumentationComponent,\n    );\n    component = fixture.componentInstance;\n    documentationService = TestBed.inject(\n      InitialReportDocumentationService,\n    ) as jasmine.SpyObj<InitialReportDocumentationService>;\n    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;\n\n    documentationService.getByContractorContractId.and.returnValue(\n      of(mockDocumentation),\n    );\n    documentationService.createWithFiles.and.returnValue(of(mockDocumentation));\n    documentationService.updateWithFiles.and.returnValue(of(mockDocumentation));\n\n    component.contractorContractId = 1;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  describe('Form Validation', () => {\n    it('should initialize forms after view init', () => {\n      component.ngAfterViewInit();\n      expect(component.validateForms()).toBeFalse();\n    });\n\n    it('should check form validity', () => {\n      const validityEmitSpy = spyOn(component.formValidityChange, 'emit');\n      component.checkFormValidity();\n      expect(validityEmitSpy).toHaveBeenCalled();\n    });\n  });\n\n  describe('Data Loading', () => {\n    it('should load initial documentation', () => {\n      component.loadInitialDocumentation();\n      expect(\n        documentationService.getByContractorContractId,\n      ).toHaveBeenCalledWith(1);\n      expect(component.initialDocumentation).toEqual(mockDocumentation);\n    });\n\n    it('should handle documentation loading error', () => {\n      documentationService.getByContractorContractId.and.returnValue(\n        throwError(() => new Error()),\n      );\n      component.loadInitialDocumentation();\n      expect(alertService.error).toHaveBeenCalledWith(\n        'Error al cargar la documentación inicial',\n      );\n    });\n\n    it('should ignore 404 errors when loading documentation', () => {\n      documentationService.getByContractorContractId.and.returnValue(\n        throwError(() => ({ status: 404 })),\n      );\n      component.loadInitialDocumentation();\n      expect(alertService.error).not.toHaveBeenCalled();\n    });\n  });\n\n  describe('Data Saving', () => {\n    let validateFormsSpy: jasmine.Spy;\n\n    beforeEach(() => {\n      validateFormsSpy = spyOn(component, 'validateForms').and.returnValue(\n        true,\n      );\n    });\n\n    it('should validate forms before saving', async () => {\n      validateFormsSpy.and.returnValue(false);\n      const result = await component.saveInitialDocumentation();\n      expect(result).toBeFalse();\n      expect(alertService.warning).toHaveBeenCalledWith(\n        'Por favor, complete todos los campos requeridos.',\n      );\n    });\n\n    it('should create new documentation', async () => {\n      component.initialDocumentation = undefined;\n      documentationService.createWithFiles.and.returnValue(\n        of(mockDocumentation),\n      );\n\n      const result = await component.saveInitialDocumentation();\n\n      expect(result).toBeTrue();\n      expect(documentationService.createWithFiles).toHaveBeenCalled();\n      expect(alertService.success).toHaveBeenCalledWith(\n        'Documentación inicial guardada con éxito',\n      );\n    });\n\n    it('should update existing documentation', async () => {\n      component.initialDocumentation = mockDocumentation;\n      const result = await component.saveInitialDocumentation();\n      expect(result).toBeTrue();\n      expect(documentationService.updateWithFiles).toHaveBeenCalled();\n      expect(alertService.success).toHaveBeenCalledWith(\n        'Documentación inicial actualizada con éxito',\n      );\n    });\n\n    it('should handle save error', async () => {\n      component.initialDocumentation = undefined;\n      documentationService.createWithFiles.and.returnValue(\n        throwError(() => new Error('Error')),\n      );\n\n      const result = await component.saveInitialDocumentation();\n\n      expect(result).toBeFalse();\n      expect(alertService.error).toHaveBeenCalledWith(\n        'Error al guardar la documentación inicial',\n      );\n    });\n  });\n});"], "mappings": ";AAAA,SAASA,uBAAuB,QAAQ,8BAA8B;AACtE,SAA2BC,OAAO,QAAQ,uBAAuB;AACjE,SAASC,uBAAuB,QAAQ,sCAAsC;AAE9E,SAASC,iCAAiC,QAAQ,qEAAqE;AACvH,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,EAAE,EAAEC,UAAU,QAAQ,MAAM;AACrC,SAASC,0CAA0C,QAAQ,kDAAkD;AAE7GC,QAAQ,CAAC,4CAA4C,EAAE,MAAK;EAC1D,IAAIC,SAAqD;EACzD,IAAIC,OAAqE;EACzE,IAAIC,oBAAuE;EAC3E,IAAIC,YAA0C;EAE9C,MAAMC,iBAAiB,GAA+B;IACpDC,EAAE,EAAE,CAAC;IACLC,oBAAoB,EAAE,CAAC;IACvBC,MAAM,EAAE,CAAC;IACTC,aAAa,EAAE,QAAQ;IACvBC,iBAAiB,EAAE,CAAC;IACpBC,WAAW,EAAE,CAAC;IACdC,KAAK,EAAE,CAAC;IACRC,KAAK,EAAE,CAAC;IACRC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,KAAK;IACpBC,kBAAkB,EAAE,KAAK;IACzBC,4BAA4B,EAAE,CAAC;IAC/BC,kBAAkB,EAAE,KAAK;IACzBC,4BAA4B,EAAE,CAAC;IAC/BC,aAAa,EAAE,KAAK;IACpBC,mBAAmB,EAAE,KAAK;IAC1BC,uBAAuB,EAAE,CAAC;IAC1BC,6BAA6B,EAAE;GAChC;EAEDC,UAAU,CAAC,MAAK;IACd,MAAMC,uBAAuB,GAAGC,OAAO,CAACC,YAAY,CAClD,mCAAmC,EACnC,CAAC,2BAA2B,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CACpE;IACD,MAAMC,eAAe,GAAGF,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAC3D,SAAS,EACT,OAAO,EACP,SAAS,CACV,CAAC;IAEFlC,OAAO,CAACoC,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACP/B,0CAA0C,EAC1CP,uBAAuB,EACvBE,uBAAuB,CACxB;MACDqC,SAAS,EAAE,CACT;QACEC,OAAO,EAAErC,iCAAiC;QAC1CsC,QAAQ,EAAER;OACX,EACD;QAAEO,OAAO,EAAEpC,YAAY;QAAEqC,QAAQ,EAAEL;MAAe,CAAE;KAEvD,CAAC;IAEF1B,OAAO,GAAGT,OAAO,CAACyC,eAAe,CAC/BnC,0CAA0C,CAC3C;IACDE,SAAS,GAAGC,OAAO,CAACiC,iBAAiB;IACrChC,oBAAoB,GAAGV,OAAO,CAAC2C,MAAM,CACnCzC,iCAAiC,CACmB;IACtDS,YAAY,GAAGX,OAAO,CAAC2C,MAAM,CAACxC,YAAY,CAAiC;IAE3EO,oBAAoB,CAACkC,yBAAyB,CAACC,GAAG,CAACC,WAAW,CAC5D1C,EAAE,CAACQ,iBAAiB,CAAC,CACtB;IACDF,oBAAoB,CAACqC,eAAe,CAACF,GAAG,CAACC,WAAW,CAAC1C,EAAE,CAACQ,iBAAiB,CAAC,CAAC;IAC3EF,oBAAoB,CAACsC,eAAe,CAACH,GAAG,CAACC,WAAW,CAAC1C,EAAE,CAACQ,iBAAiB,CAAC,CAAC;IAE3EJ,SAAS,CAACM,oBAAoB,GAAG,CAAC;IAClCL,OAAO,CAACwC,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAAC3C,SAAS,CAAC,CAAC4C,UAAU,EAAE;EAChC,CAAC,CAAC;EAEF7C,QAAQ,CAAC,iBAAiB,EAAE,MAAK;IAC/B2C,EAAE,CAAC,yCAAyC,EAAE,MAAK;MACjD1C,SAAS,CAAC6C,eAAe,EAAE;MAC3BF,MAAM,CAAC3C,SAAS,CAAC8C,aAAa,EAAE,CAAC,CAACC,SAAS,EAAE;IAC/C,CAAC,CAAC;IAEFL,EAAE,CAAC,4BAA4B,EAAE,MAAK;MACpC,MAAMM,eAAe,GAAGC,KAAK,CAACjD,SAAS,CAACkD,kBAAkB,EAAE,MAAM,CAAC;MACnElD,SAAS,CAACmD,iBAAiB,EAAE;MAC7BR,MAAM,CAACK,eAAe,CAAC,CAACI,gBAAgB,EAAE;IAC5C,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFrD,QAAQ,CAAC,cAAc,EAAE,MAAK;IAC5B2C,EAAE,CAAC,mCAAmC,EAAE,MAAK;MAC3C1C,SAAS,CAACqD,wBAAwB,EAAE;MACpCV,MAAM,CACJzC,oBAAoB,CAACkC,yBAAyB,CAC/C,CAACkB,oBAAoB,CAAC,CAAC,CAAC;MACzBX,MAAM,CAAC3C,SAAS,CAACuD,oBAAoB,CAAC,CAACC,OAAO,CAACpD,iBAAiB,CAAC;IACnE,CAAC,CAAC;IAEFsC,EAAE,CAAC,2CAA2C,EAAE,MAAK;MACnDxC,oBAAoB,CAACkC,yBAAyB,CAACC,GAAG,CAACC,WAAW,CAC5DzC,UAAU,CAAC,MAAM,IAAI4D,KAAK,EAAE,CAAC,CAC9B;MACDzD,SAAS,CAACqD,wBAAwB,EAAE;MACpCV,MAAM,CAACxC,YAAY,CAACuD,KAAK,CAAC,CAACJ,oBAAoB,CAC7C,0CAA0C,CAC3C;IACH,CAAC,CAAC;IAEFZ,EAAE,CAAC,qDAAqD,EAAE,MAAK;MAC7DxC,oBAAoB,CAACkC,yBAAyB,CAACC,GAAG,CAACC,WAAW,CAC5DzC,UAAU,CAAC,OAAO;QAAE8D,MAAM,EAAE;MAAG,CAAE,CAAC,CAAC,CACpC;MACD3D,SAAS,CAACqD,wBAAwB,EAAE;MACpCV,MAAM,CAACxC,YAAY,CAACuD,KAAK,CAAC,CAACE,GAAG,CAACR,gBAAgB,EAAE;IACnD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFrD,QAAQ,CAAC,aAAa,EAAE,MAAK;IAC3B,IAAI8D,gBAA6B;IAEjCtC,UAAU,CAAC,MAAK;MACdsC,gBAAgB,GAAGZ,KAAK,CAACjD,SAAS,EAAE,eAAe,CAAC,CAACqC,GAAG,CAACC,WAAW,CAClE,IAAI,CACL;IACH,CAAC,CAAC;IAEFI,EAAE,CAAC,qCAAqC,eAAAoB,iBAAA,CAAE,aAAW;MACnDD,gBAAgB,CAACxB,GAAG,CAACC,WAAW,CAAC,KAAK,CAAC;MACvC,MAAMyB,MAAM,SAAS/D,SAAS,CAACgE,wBAAwB,EAAE;MACzDrB,MAAM,CAACoB,MAAM,CAAC,CAAChB,SAAS,EAAE;MAC1BJ,MAAM,CAACxC,YAAY,CAAC8D,OAAO,CAAC,CAACX,oBAAoB,CAC/C,kDAAkD,CACnD;IACH,CAAC,EAAC;IAEFZ,EAAE,CAAC,iCAAiC,eAAAoB,iBAAA,CAAE,aAAW;MAC/C9D,SAAS,CAACuD,oBAAoB,GAAGW,SAAS;MAC1ChE,oBAAoB,CAACqC,eAAe,CAACF,GAAG,CAACC,WAAW,CAClD1C,EAAE,CAACQ,iBAAiB,CAAC,CACtB;MAED,MAAM2D,MAAM,SAAS/D,SAAS,CAACgE,wBAAwB,EAAE;MAEzDrB,MAAM,CAACoB,MAAM,CAAC,CAACI,QAAQ,EAAE;MACzBxB,MAAM,CAACzC,oBAAoB,CAACqC,eAAe,CAAC,CAACa,gBAAgB,EAAE;MAC/DT,MAAM,CAACxC,YAAY,CAACiE,OAAO,CAAC,CAACd,oBAAoB,CAC/C,0CAA0C,CAC3C;IACH,CAAC,EAAC;IAEFZ,EAAE,CAAC,sCAAsC,eAAAoB,iBAAA,CAAE,aAAW;MACpD9D,SAAS,CAACuD,oBAAoB,GAAGnD,iBAAiB;MAClD,MAAM2D,MAAM,SAAS/D,SAAS,CAACgE,wBAAwB,EAAE;MACzDrB,MAAM,CAACoB,MAAM,CAAC,CAACI,QAAQ,EAAE;MACzBxB,MAAM,CAACzC,oBAAoB,CAACsC,eAAe,CAAC,CAACY,gBAAgB,EAAE;MAC/DT,MAAM,CAACxC,YAAY,CAACiE,OAAO,CAAC,CAACd,oBAAoB,CAC/C,6CAA6C,CAC9C;IACH,CAAC,EAAC;IAEFZ,EAAE,CAAC,0BAA0B,eAAAoB,iBAAA,CAAE,aAAW;MACxC9D,SAAS,CAACuD,oBAAoB,GAAGW,SAAS;MAC1ChE,oBAAoB,CAACqC,eAAe,CAACF,GAAG,CAACC,WAAW,CAClDzC,UAAU,CAAC,MAAM,IAAI4D,KAAK,CAAC,OAAO,CAAC,CAAC,CACrC;MAED,MAAMM,MAAM,SAAS/D,SAAS,CAACgE,wBAAwB,EAAE;MAEzDrB,MAAM,CAACoB,MAAM,CAAC,CAAChB,SAAS,EAAE;MAC1BJ,MAAM,CAACxC,YAAY,CAACuD,KAAK,CAAC,CAACJ,oBAAoB,CAC7C,2CAA2C,CAC5C;IACH,CAAC,EAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}