import { CurrencyPipe, DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ContractValues } from '@contract-management/models/contract-values.model';
import { Contract } from '@contract-management/models/contract.model';
import { Reduction } from '@contract-management/models/reduction.model';
import { ContractValuesDialogComponent } from '@contract-management/components/contract-values-dialog/contract-values-dialog.component';

@Component({
  selector: 'app-contract-value-summary',
  standalone: true,
  imports: [
    MatCardModule,
    MatFormFieldModule,
    MatTableModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    CurrencyPipe,
    DatePipe
  ],
  templateUrl: './contract-value-summary.component.html',
  styleUrls: ['./contract-value-summary.component.scss']
})
export class ContractValueSummaryComponent implements OnInit, OnChanges {
  @Input() contract!: Contract;
  @Input() reductions: Reduction[] = [];
  @Input() isContractFinished = false;
  @Output() contractValuesChanged = new EventEmitter<ContractValues[]>();

  initialValueDataSource = new MatTableDataSource<ContractValues>();
  displayedColumns: string[] = [
    'valorTotal',
    'valorMADS',
    'valorOtros',
    'valorVigenciaFutura',
    'entidadCDP',
    'cdp',
    'rp',
    'fechaSuscripcion',
    'fechaInicio',
    'fechaFinalizacion',
    'actions'
  ];

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.updateInitialValueDataSource();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if ((changes['contract'] && !changes['contract'].firstChange) ||
        (changes['reductions'] && !changes['reductions'].firstChange)) {
      this.updateInitialValueDataSource();
      this.cdr.detectChanges();
    }
  }

  getInitialContractValue(): ContractValues | undefined {
    if (!this.contract?.contractValues?.length) {
      return undefined;
    }

    return this.contract.contractValues.reduce((lowest: ContractValues | undefined, current: ContractValues) => {
      if (!lowest || (current.id !== undefined && lowest.id !== undefined && current.id < lowest.id)) {
        return current;
      }
      return lowest;
    }, undefined);
  }

  private updateInitialValueDataSource(): void {
    const initialValue = this.getInitialContractValue();
    if (initialValue) {
      this.initialValueDataSource.data = [initialValue];
    } else {
      this.initialValueDataSource.data = [];
    }
  }

  getInitialValue(): number {
    const initialValue = this.getInitialContractValue();
    return initialValue ? initialValue.numericValue : 0;
  }

  getTotalAdditions(): number {
    if (!this.contract?.contractValues) {
      return 0;
    }

    const initialValue = this.getInitialContractValue();
    if (!initialValue) {
      return this.contract.contractValues.reduce((sum, value) => sum + value.numericValue, 0);
    }

    return this.contract.contractValues
      .filter(value => value.id !== initialValue.id)
      .reduce((sum, value) => sum + value.numericValue, 0);
  }

  getTotalReductions(): number {
    if (!this.reductions?.length) {
      return 0;
    }

    return this.reductions
      .reduce((sum, reduction) => sum + reduction.valueRedution, 0);
  }

  getFinalValue(): number {
    const initialValue = this.getInitialValue();
    const additions = this.getTotalAdditions();
    const reductions = this.getTotalReductions();

    return initialValue + additions - reductions;
  }

  openContractValuesDialog(contractValue: ContractValues): void {
    this.dialog
      .open(ContractValuesDialogComponent, {
        width: '1000px',
        data: {
          contractValue,
          contract: this.contract,
          isInitialValue: true,
        },
      })
      .afterClosed()
      .subscribe((result?: ContractValues) => {
        if (!result) return;

        if (this.contract.contractValues) {
          const index = this.contract.contractValues.findIndex(v => v.id === result.id);
          if (index !== -1) {
            this.contract.contractValues[index] = result;

            this.updateInitialValueDataSource();
            this.contractValuesChanged.emit(this.contract.contractValues);
            this.cdr.detectChanges();
          }
        }
      });
  }
}
