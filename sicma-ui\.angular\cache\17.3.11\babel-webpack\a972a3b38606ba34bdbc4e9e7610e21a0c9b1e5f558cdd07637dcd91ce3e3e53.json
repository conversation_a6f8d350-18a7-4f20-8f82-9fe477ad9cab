{"ast": null, "code": "function cov_15kqzuohtu() {\n  var path = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\core\\\\layout\\\\keycloack\\\\keycloack.component.ts\";\n  var hash = \"ef50e6061f2fbb169c2acd54a44d2b88c7abde94\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\core\\\\layout\\\\keycloack\\\\keycloack.component.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 11,\n          column: 25\n        },\n        end: {\n          line: 125,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 13,\n          column: 8\n        },\n        end: {\n          line: 13,\n          column: 39\n        }\n      },\n      \"2\": {\n        start: {\n          line: 14,\n          column: 8\n        },\n        end: {\n          line: 14,\n          column: 29\n        }\n      },\n      \"3\": {\n        start: {\n          line: 15,\n          column: 8\n        },\n        end: {\n          line: 15,\n          column: 31\n        }\n      },\n      \"4\": {\n        start: {\n          line: 16,\n          column: 8\n        },\n        end: {\n          line: 16,\n          column: 39\n        }\n      },\n      \"5\": {\n        start: {\n          line: 17,\n          column: 8\n        },\n        end: {\n          line: 17,\n          column: 27\n        }\n      },\n      \"6\": {\n        start: {\n          line: 20,\n          column: 8\n        },\n        end: {\n          line: 20,\n          column: 28\n        }\n      },\n      \"7\": {\n        start: {\n          line: 23,\n          column: 30\n        },\n        end: {\n          line: 23,\n          column: 35\n        }\n      },\n      \"8\": {\n        start: {\n          line: 24,\n          column: 8\n        },\n        end: {\n          line: 24,\n          column: 28\n        }\n      },\n      \"9\": {\n        start: {\n          line: 25,\n          column: 8\n        },\n        end: {\n          line: 40,\n          column: 11\n        }\n      },\n      \"10\": {\n        start: {\n          line: 27,\n          column: 33\n        },\n        end: {\n          line: 27,\n          column: 52\n        }\n      },\n      \"11\": {\n        start: {\n          line: 30,\n          column: 16\n        },\n        end: {\n          line: 35,\n          column: 17\n        }\n      },\n      \"12\": {\n        start: {\n          line: 31,\n          column: 20\n        },\n        end: {\n          line: 31,\n          column: 46\n        }\n      },\n      \"13\": {\n        start: {\n          line: 34,\n          column: 20\n        },\n        end: {\n          line: 34,\n          column: 69\n        }\n      },\n      \"14\": {\n        start: {\n          line: 38,\n          column: 16\n        },\n        end: {\n          line: 38,\n          column: 67\n        }\n      },\n      \"15\": {\n        start: {\n          line: 43,\n          column: 25\n        },\n        end: {\n          line: 43,\n          column: 59\n        }\n      },\n      \"16\": {\n        start: {\n          line: 44,\n          column: 8\n        },\n        end: {\n          line: 58,\n          column: 9\n        }\n      },\n      \"17\": {\n        start: {\n          line: 44,\n          column: 39\n        },\n        end: {\n          line: 44,\n          column: 79\n        }\n      },\n      \"18\": {\n        start: {\n          line: 45,\n          column: 12\n        },\n        end: {\n          line: 45,\n          column: 52\n        }\n      },\n      \"19\": {\n        start: {\n          line: 47,\n          column: 13\n        },\n        end: {\n          line: 58,\n          column: 9\n        }\n      },\n      \"20\": {\n        start: {\n          line: 47,\n          column: 44\n        },\n        end: {\n          line: 47,\n          column: 81\n        }\n      },\n      \"21\": {\n        start: {\n          line: 48,\n          column: 12\n        },\n        end: {\n          line: 48,\n          column: 56\n        }\n      },\n      \"22\": {\n        start: {\n          line: 50,\n          column: 13\n        },\n        end: {\n          line: 58,\n          column: 9\n        }\n      },\n      \"23\": {\n        start: {\n          line: 50,\n          column: 44\n        },\n        end: {\n          line: 50,\n          column: 81\n        }\n      },\n      \"24\": {\n        start: {\n          line: 51,\n          column: 12\n        },\n        end: {\n          line: 51,\n          column: 53\n        }\n      },\n      \"25\": {\n        start: {\n          line: 53,\n          column: 13\n        },\n        end: {\n          line: 58,\n          column: 9\n        }\n      },\n      \"26\": {\n        start: {\n          line: 53,\n          column: 44\n        },\n        end: {\n          line: 53,\n          column: 87\n        }\n      },\n      \"27\": {\n        start: {\n          line: 54,\n          column: 12\n        },\n        end: {\n          line: 54,\n          column: 49\n        }\n      },\n      \"28\": {\n        start: {\n          line: 57,\n          column: 12\n        },\n        end: {\n          line: 57,\n          column: 40\n        }\n      },\n      \"29\": {\n        start: {\n          line: 59,\n          column: 8\n        },\n        end: {\n          line: 59,\n          column: 52\n        }\n      },\n      \"30\": {\n        start: {\n          line: 62,\n          column: 32\n        },\n        end: {\n          line: 62,\n          column: 41\n        }\n      },\n      \"31\": {\n        start: {\n          line: 63,\n          column: 8\n        },\n        end: {\n          line: 104,\n          column: 11\n        }\n      },\n      \"32\": {\n        start: {\n          line: 65,\n          column: 33\n        },\n        end: {\n          line: 65,\n          column: 52\n        }\n      },\n      \"33\": {\n        start: {\n          line: 68,\n          column: 16\n        },\n        end: {\n          line: 89,\n          column: 17\n        }\n      },\n      \"34\": {\n        start: {\n          line: 69,\n          column: 20\n        },\n        end: {\n          line: 85,\n          column: 23\n        }\n      },\n      \"35\": {\n        start: {\n          line: 71,\n          column: 45\n        },\n        end: {\n          line: 71,\n          column: 64\n        }\n      },\n      \"36\": {\n        start: {\n          line: 74,\n          column: 28\n        },\n        end: {\n          line: 80,\n          column: 29\n        }\n      },\n      \"37\": {\n        start: {\n          line: 75,\n          column: 32\n        },\n        end: {\n          line: 75,\n          column: 73\n        }\n      },\n      \"38\": {\n        start: {\n          line: 76,\n          column: 32\n        },\n        end: {\n          line: 76,\n          column: 76\n        }\n      },\n      \"39\": {\n        start: {\n          line: 79,\n          column: 32\n        },\n        end: {\n          line: 79,\n          column: 75\n        }\n      },\n      \"40\": {\n        start: {\n          line: 83,\n          column: 28\n        },\n        end: {\n          line: 83,\n          column: 79\n        }\n      },\n      \"41\": {\n        start: {\n          line: 88,\n          column: 20\n        },\n        end: {\n          line: 88,\n          column: 63\n        }\n      },\n      \"42\": {\n        start: {\n          line: 92,\n          column: 16\n        },\n        end: {\n          line: 102,\n          column: 17\n        }\n      },\n      \"43\": {\n        start: {\n          line: 93,\n          column: 20\n        },\n        end: {\n          line: 95,\n          column: 21\n        }\n      },\n      \"44\": {\n        start: {\n          line: 94,\n          column: 24\n        },\n        end: {\n          line: 94,\n          column: 59\n        }\n      },\n      \"45\": {\n        start: {\n          line: 97,\n          column: 21\n        },\n        end: {\n          line: 102,\n          column: 17\n        }\n      },\n      \"46\": {\n        start: {\n          line: 98,\n          column: 20\n        },\n        end: {\n          line: 98,\n          column: 73\n        }\n      },\n      \"47\": {\n        start: {\n          line: 101,\n          column: 20\n        },\n        end: {\n          line: 101,\n          column: 92\n        }\n      },\n      \"48\": {\n        start: {\n          line: 107,\n          column: 8\n        },\n        end: {\n          line: 107,\n          column: 41\n        }\n      },\n      \"49\": {\n        start: {\n          line: 110,\n          column: 8\n        },\n        end: {\n          line: 116,\n          column: 17\n        }\n      },\n      \"50\": {\n        start: {\n          line: 111,\n          column: 26\n        },\n        end: {\n          line: 111,\n          column: 64\n        }\n      },\n      \"51\": {\n        start: {\n          line: 112,\n          column: 12\n        },\n        end: {\n          line: 115,\n          column: 13\n        }\n      },\n      \"52\": {\n        start: {\n          line: 113,\n          column: 16\n        },\n        end: {\n          line: 113,\n          column: 37\n        }\n      },\n      \"53\": {\n        start: {\n          line: 114,\n          column: 16\n        },\n        end: {\n          line: 114,\n          column: 49\n        }\n      },\n      \"54\": {\n        start: {\n          line: 118,\n          column: 13\n        },\n        end: {\n          line: 124,\n          column: 6\n        }\n      },\n      \"55\": {\n        start: {\n          line: 118,\n          column: 41\n        },\n        end: {\n          line: 124,\n          column: 5\n        }\n      },\n      \"56\": {\n        start: {\n          line: 126,\n          column: 0\n        },\n        end: {\n          line: 135,\n          column: 23\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 12,\n            column: 4\n          },\n          end: {\n            line: 12,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 12,\n            column: 66\n          },\n          end: {\n            line: 18,\n            column: 5\n          }\n        },\n        line: 12\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 19,\n            column: 4\n          },\n          end: {\n            line: 19,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 19,\n            column: 15\n          },\n          end: {\n            line: 21,\n            column: 5\n          }\n        },\n        line: 19\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 22,\n            column: 4\n          },\n          end: {\n            line: 22,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 22,\n            column: 27\n          },\n          end: {\n            line: 41,\n            column: 5\n          }\n        },\n        line: 22\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 27,\n            column: 27\n          },\n          end: {\n            line: 27,\n            column: 28\n          }\n        },\n        loc: {\n          start: {\n            line: 27,\n            column: 33\n          },\n          end: {\n            line: 27,\n            column: 52\n          }\n        },\n        line: 27\n      },\n      \"4\": {\n        name: \"(anonymous_4)\",\n        decl: {\n          start: {\n            line: 29,\n            column: 18\n          },\n          end: {\n            line: 29,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 29,\n            column: 31\n          },\n          end: {\n            line: 36,\n            column: 13\n          }\n        },\n        line: 29\n      },\n      \"5\": {\n        name: \"(anonymous_5)\",\n        decl: {\n          start: {\n            line: 37,\n            column: 19\n          },\n          end: {\n            line: 37,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 37,\n            column: 25\n          },\n          end: {\n            line: 39,\n            column: 13\n          }\n        },\n        line: 37\n      },\n      \"6\": {\n        name: \"(anonymous_6)\",\n        decl: {\n          start: {\n            line: 42,\n            column: 4\n          },\n          end: {\n            line: 42,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 42,\n            column: 25\n          },\n          end: {\n            line: 60,\n            column: 5\n          }\n        },\n        line: 42\n      },\n      \"7\": {\n        name: \"(anonymous_7)\",\n        decl: {\n          start: {\n            line: 44,\n            column: 26\n          },\n          end: {\n            line: 44,\n            column: 27\n          }\n        },\n        loc: {\n          start: {\n            line: 44,\n            column: 39\n          },\n          end: {\n            line: 44,\n            column: 79\n          }\n        },\n        line: 44\n      },\n      \"8\": {\n        name: \"(anonymous_8)\",\n        decl: {\n          start: {\n            line: 47,\n            column: 31\n          },\n          end: {\n            line: 47,\n            column: 32\n          }\n        },\n        loc: {\n          start: {\n            line: 47,\n            column: 44\n          },\n          end: {\n            line: 47,\n            column: 81\n          }\n        },\n        line: 47\n      },\n      \"9\": {\n        name: \"(anonymous_9)\",\n        decl: {\n          start: {\n            line: 50,\n            column: 31\n          },\n          end: {\n            line: 50,\n            column: 32\n          }\n        },\n        loc: {\n          start: {\n            line: 50,\n            column: 44\n          },\n          end: {\n            line: 50,\n            column: 81\n          }\n        },\n        line: 50\n      },\n      \"10\": {\n        name: \"(anonymous_10)\",\n        decl: {\n          start: {\n            line: 53,\n            column: 31\n          },\n          end: {\n            line: 53,\n            column: 32\n          }\n        },\n        loc: {\n          start: {\n            line: 53,\n            column: 44\n          },\n          end: {\n            line: 53,\n            column: 87\n          }\n        },\n        line: 53\n      },\n      \"11\": {\n        name: \"(anonymous_11)\",\n        decl: {\n          start: {\n            line: 61,\n            column: 4\n          },\n          end: {\n            line: 61,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 61,\n            column: 33\n          },\n          end: {\n            line: 105,\n            column: 5\n          }\n        },\n        line: 61\n      },\n      \"12\": {\n        name: \"(anonymous_12)\",\n        decl: {\n          start: {\n            line: 65,\n            column: 27\n          },\n          end: {\n            line: 65,\n            column: 28\n          }\n        },\n        loc: {\n          start: {\n            line: 65,\n            column: 33\n          },\n          end: {\n            line: 65,\n            column: 52\n          }\n        },\n        line: 65\n      },\n      \"13\": {\n        name: \"(anonymous_13)\",\n        decl: {\n          start: {\n            line: 67,\n            column: 18\n          },\n          end: {\n            line: 67,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 67,\n            column: 31\n          },\n          end: {\n            line: 90,\n            column: 13\n          }\n        },\n        line: 67\n      },\n      \"14\": {\n        name: \"(anonymous_14)\",\n        decl: {\n          start: {\n            line: 71,\n            column: 39\n          },\n          end: {\n            line: 71,\n            column: 40\n          }\n        },\n        loc: {\n          start: {\n            line: 71,\n            column: 45\n          },\n          end: {\n            line: 71,\n            column: 64\n          }\n        },\n        line: 71\n      },\n      \"15\": {\n        name: \"(anonymous_15)\",\n        decl: {\n          start: {\n            line: 73,\n            column: 30\n          },\n          end: {\n            line: 73,\n            column: 31\n          }\n        },\n        loc: {\n          start: {\n            line: 73,\n            column: 43\n          },\n          end: {\n            line: 81,\n            column: 25\n          }\n        },\n        line: 73\n      },\n      \"16\": {\n        name: \"(anonymous_16)\",\n        decl: {\n          start: {\n            line: 82,\n            column: 31\n          },\n          end: {\n            line: 82,\n            column: 32\n          }\n        },\n        loc: {\n          start: {\n            line: 82,\n            column: 37\n          },\n          end: {\n            line: 84,\n            column: 25\n          }\n        },\n        line: 82\n      },\n      \"17\": {\n        name: \"(anonymous_17)\",\n        decl: {\n          start: {\n            line: 91,\n            column: 19\n          },\n          end: {\n            line: 91,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 91,\n            column: 28\n          },\n          end: {\n            line: 103,\n            column: 13\n          }\n        },\n        line: 91\n      },\n      \"18\": {\n        name: \"(anonymous_18)\",\n        decl: {\n          start: {\n            line: 106,\n            column: 4\n          },\n          end: {\n            line: 106,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 106,\n            column: 19\n          },\n          end: {\n            line: 108,\n            column: 5\n          }\n        },\n        line: 106\n      },\n      \"19\": {\n        name: \"(anonymous_19)\",\n        decl: {\n          start: {\n            line: 109,\n            column: 4\n          },\n          end: {\n            line: 109,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 109,\n            column: 19\n          },\n          end: {\n            line: 117,\n            column: 5\n          }\n        },\n        line: 109\n      },\n      \"20\": {\n        name: \"(anonymous_20)\",\n        decl: {\n          start: {\n            line: 110,\n            column: 20\n          },\n          end: {\n            line: 110,\n            column: 21\n          }\n        },\n        loc: {\n          start: {\n            line: 110,\n            column: 26\n          },\n          end: {\n            line: 116,\n            column: 9\n          }\n        },\n        line: 110\n      },\n      \"21\": {\n        name: \"(anonymous_21)\",\n        decl: {\n          start: {\n            line: 118,\n            column: 35\n          },\n          end: {\n            line: 118,\n            column: 36\n          }\n        },\n        loc: {\n          start: {\n            line: 118,\n            column: 41\n          },\n          end: {\n            line: 124,\n            column: 5\n          }\n        },\n        line: 118\n      }\n    },\n    branchMap: {\n      \"0\": {\n        loc: {\n          start: {\n            line: 30,\n            column: 16\n          },\n          end: {\n            line: 35,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 30,\n            column: 16\n          },\n          end: {\n            line: 35,\n            column: 17\n          }\n        }, {\n          start: {\n            line: 33,\n            column: 21\n          },\n          end: {\n            line: 35,\n            column: 17\n          }\n        }],\n        line: 30\n      },\n      \"1\": {\n        loc: {\n          start: {\n            line: 44,\n            column: 8\n          },\n          end: {\n            line: 58,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 44,\n            column: 8\n          },\n          end: {\n            line: 58,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 47,\n            column: 13\n          },\n          end: {\n            line: 58,\n            column: 9\n          }\n        }],\n        line: 44\n      },\n      \"2\": {\n        loc: {\n          start: {\n            line: 47,\n            column: 13\n          },\n          end: {\n            line: 58,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 47,\n            column: 13\n          },\n          end: {\n            line: 58,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 50,\n            column: 13\n          },\n          end: {\n            line: 58,\n            column: 9\n          }\n        }],\n        line: 47\n      },\n      \"3\": {\n        loc: {\n          start: {\n            line: 50,\n            column: 13\n          },\n          end: {\n            line: 58,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 50,\n            column: 13\n          },\n          end: {\n            line: 58,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 53,\n            column: 13\n          },\n          end: {\n            line: 58,\n            column: 9\n          }\n        }],\n        line: 50\n      },\n      \"4\": {\n        loc: {\n          start: {\n            line: 53,\n            column: 13\n          },\n          end: {\n            line: 58,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 53,\n            column: 13\n          },\n          end: {\n            line: 58,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 56,\n            column: 13\n          },\n          end: {\n            line: 58,\n            column: 9\n          }\n        }],\n        line: 53\n      },\n      \"5\": {\n        loc: {\n          start: {\n            line: 68,\n            column: 16\n          },\n          end: {\n            line: 89,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 68,\n            column: 16\n          },\n          end: {\n            line: 89,\n            column: 17\n          }\n        }, {\n          start: {\n            line: 87,\n            column: 21\n          },\n          end: {\n            line: 89,\n            column: 17\n          }\n        }],\n        line: 68\n      },\n      \"6\": {\n        loc: {\n          start: {\n            line: 74,\n            column: 28\n          },\n          end: {\n            line: 80,\n            column: 29\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 74,\n            column: 28\n          },\n          end: {\n            line: 80,\n            column: 29\n          }\n        }, {\n          start: {\n            line: 78,\n            column: 33\n          },\n          end: {\n            line: 80,\n            column: 29\n          }\n        }],\n        line: 74\n      },\n      \"7\": {\n        loc: {\n          start: {\n            line: 92,\n            column: 16\n          },\n          end: {\n            line: 102,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 92,\n            column: 16\n          },\n          end: {\n            line: 102,\n            column: 17\n          }\n        }, {\n          start: {\n            line: 97,\n            column: 21\n          },\n          end: {\n            line: 102,\n            column: 17\n          }\n        }],\n        line: 92\n      },\n      \"8\": {\n        loc: {\n          start: {\n            line: 93,\n            column: 20\n          },\n          end: {\n            line: 95,\n            column: 21\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 93,\n            column: 20\n          },\n          end: {\n            line: 95,\n            column: 21\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 93\n      },\n      \"9\": {\n        loc: {\n          start: {\n            line: 97,\n            column: 21\n          },\n          end: {\n            line: 102,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 97,\n            column: 21\n          },\n          end: {\n            line: 102,\n            column: 17\n          }\n        }, {\n          start: {\n            line: 100,\n            column: 21\n          },\n          end: {\n            line: 102,\n            column: 17\n          }\n        }],\n        line: 97\n      },\n      \"10\": {\n        loc: {\n          start: {\n            line: 101,\n            column: 37\n          },\n          end: {\n            line: 101,\n            column: 90\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 101,\n            column: 37\n          },\n          end: {\n            line: 101,\n            column: 54\n          }\n        }, {\n          start: {\n            line: 101,\n            column: 58\n          },\n          end: {\n            line: 101,\n            column: 90\n          }\n        }],\n        line: 101\n      },\n      \"11\": {\n        loc: {\n          start: {\n            line: 112,\n            column: 12\n          },\n          end: {\n            line: 115,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 112,\n            column: 12\n          },\n          end: {\n            line: 115,\n            column: 13\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 112\n      }\n    },\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0,\n      \"36\": 0,\n      \"37\": 0,\n      \"38\": 0,\n      \"39\": 0,\n      \"40\": 0,\n      \"41\": 0,\n      \"42\": 0,\n      \"43\": 0,\n      \"44\": 0,\n      \"45\": 0,\n      \"46\": 0,\n      \"47\": 0,\n      \"48\": 0,\n      \"49\": 0,\n      \"50\": 0,\n      \"51\": 0,\n      \"52\": 0,\n      \"53\": 0,\n      \"54\": 0,\n      \"55\": 0,\n      \"56\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0\n    },\n    b: {\n      \"0\": [0, 0],\n      \"1\": [0, 0],\n      \"2\": [0, 0],\n      \"3\": [0, 0],\n      \"4\": [0, 0],\n      \"5\": [0, 0],\n      \"6\": [0, 0],\n      \"7\": [0, 0],\n      \"8\": [0, 0],\n      \"9\": [0, 0],\n      \"10\": [0, 0],\n      \"11\": [0, 0]\n    },\n    inputSourceMap: {\n      version: 3,\n      file: \"keycloack.component.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\core\\\\layout\\\\keycloack\\\\keycloack.component.ts\"],\n      names: [],\n      mappings: \";;;AAAA,OAAO,EACL,SAAS,EACT,sBAAsB,EAEtB,iBAAiB,GAClB,MAAM,eAAe,CAAC;AACvB,OAAO,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAC;AACzC,OAAO,EAAE,WAAW,EAAE,MAAM,kCAAkC,CAAC;AAC/D,OAAO,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAC9D,OAAO,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AAChD,OAAO,EAAE,WAAW,EAAE,MAAM,kCAAkC,CAAC;AAC/D,OAAO,EAAE,QAAQ,EAAE,MAAM,MAAM,CAAC;AAkBzB,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IAC7B,YACU,WAAwB,EACxB,MAAc,EACd,OAA0B,EAC1B,WAAwB,EACxB,KAAmB;QAJnB,gBAAW,GAAX,WAAW,CAAa;QACxB,WAAM,GAAN,MAAM,CAAQ;QACd,YAAO,GAAP,OAAO,CAAmB;QAC1B,gBAAW,GAAX,WAAW,CAAa;QACxB,UAAK,GAAL,KAAK,CAAc;IAC1B,CAAC;IAEJ,QAAQ;QACN,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAED,eAAe,CAAC,KAA0B;QACxC,MAAM,aAAa,GAAG,KAAoB,CAAC;QAC3C,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QAEpB,IAAI,CAAC,WAAW;aACb,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC;aAC3B,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;aACzC,SAAS,CAAC;YACT,IAAI,EAAE,CAAC,OAAO,EAAE,EAAE;gBAChB,IAAI,OAAO,EAAE,CAAC;oBACZ,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC5B,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;gBACnD,CAAC;YACH,CAAC;YACD,KAAK,EAAE,GAAG,EAAE;gBACV,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACrD,CAAC;SACF,CAAC,CAAC;IACP,CAAC;IAEO,kBAAkB;QACxB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC;QAEpD,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,YAAY,KAAK,eAAe,CAAC,EAAE,CAAC;YACzE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC;QAC1C,CAAC;aAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,YAAY,KAAK,YAAY,CAAC,EAAE,CAAC;YAC7E,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC;QAC9C,CAAC;aAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,YAAY,KAAK,YAAY,CAAC,EAAE,CAAC;YAC7E,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAC3C,CAAC;aAAM,IACL,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,YAAY,KAAK,kBAAkB,CAAC,EACvE,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;QACvC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC9B,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;IAC9C,CAAC;IAEO,qBAAqB,CAAC,KAAa;QACzC,MAAM,eAAe,GAAoB,EAAE,KAAK,EAAE,CAAC;QAEnD,IAAI,CAAC,WAAW;aACb,WAAW,CAAC,eAAe,CAAC;aAC5B,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;aACzC,SAAS,CAAC;YACT,IAAI,EAAE,CAAC,OAAO,EAAE,EAAE;gBAChB,IAAI,OAAO,EAAE,CAAC;oBACZ,IAAI,CAAC,WAAW;yBACb,KAAK,CAAC,KAAK,CAAC;yBACZ,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;yBACzC,SAAS,CAAC;wBACT,IAAI,EAAE,CAAC,OAAO,EAAE,EAAE;4BAChB,IAAI,OAAO,EAAE,CAAC;gCACZ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC;gCACzC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;4BAC9C,CAAC;iCAAM,CAAC;gCACN,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;4BAC7C,CAAC;wBACH,CAAC;wBACD,KAAK,EAAE,GAAG,EAAE;4BACV,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;wBACrD,CAAC;qBACF,CAAC,CAAC;gBACP,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;gBAC7C,CAAC;YACH,CAAC;YACD,KAAK,EAAE,CAAC,GAAG,EAAE,EAAE;gBACb,IAAI,GAAG,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;oBACvB,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,KAAK,+BAA+B,EAAE,CAAC;wBAC3D,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;oBACnC,CAAC;gBACH,CAAC;qBAAM,IAAI,GAAG,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;oBAC9B,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;gBACvD,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,IAAI,gCAAgC,CAAC,CAAC;gBAC1E,CAAC;YACH,CAAC;SACF,CAAC,CAAC;IACP,CAAC;IAED,YAAY;QACV,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;IACnC,CAAC;IAED,YAAY;QACV,WAAW,CAAC,GAAG,EAAE;YACf,MAAM,KAAK,GAAG,YAAY,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YACrD,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,YAAY,CAAC,KAAK,EAAE,CAAC;gBACrB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;YACnC,CAAC;QACH,CAAC,EAAE,IAAI,CAAC,CAAC;IACX,CAAC;;;;;;;;;AA7GU,kBAAkB;IAR9B,SAAS,CAAC;QACT,QAAQ,EAAE,eAAe;QACzB,8BAAyC;QAEzC,UAAU,EAAE,IAAI;QAChB,OAAO,EAAE,CAAC,sBAAsB,CAAC;QACjC,aAAa,EAAE,iBAAiB,CAAC,IAAI;;KACtC,CAAC;GACW,kBAAkB,CA8G9B\",\n      sourcesContent: [\"import {\\n  Component,\\n  CUSTOM_ELEMENTS_SCHEMA,\\n  OnInit,\\n  ViewEncapsulation,\\n} from '@angular/core';\\nimport { Router } from '@angular/router';\\nimport { AuthService } from '@core/auth/services/auth.service';\\nimport { AlertService } from '@shared/services/alert.service';\\nimport { NgxSpinnerService } from 'ngx-spinner';\\nimport { UserService } from '@core/auth/services/user.service';\\nimport { finalize } from 'rxjs';\\n\\ninterface CustomEvent {\\n  detail: string;\\n}\\n\\ninterface ContractorToken {\\n  token: string;\\n}\\n\\n@Component({\\n  selector: 'app-keycloack',\\n  templateUrl: './keycloack.component.html',\\n  styleUrl: './keycloack.component.scss',\\n  standalone: true,\\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\\n  encapsulation: ViewEncapsulation.None,\\n})\\nexport class KeycloackComponent implements OnInit {\\n  constructor(\\n    private authService: AuthService,\\n    private router: Router,\\n    private spinner: NgxSpinnerService,\\n    private userService: UserService,\\n    private alert: AlertService,\\n  ) {}\\n\\n  ngOnInit(): void {\\n    this.monitorToken();\\n  }\\n\\n  onTokenResolved(event: CustomEvent | Event): void {\\n    const keycloakEvent = event as CustomEvent;\\n    this.spinner.show();\\n\\n    this.authService\\n      .login(keycloakEvent.detail)\\n      .pipe(finalize(() => this.spinner.hide()))\\n      .subscribe({\\n        next: (success) => {\\n          if (success) {\\n            this.handleUserProfiles();\\n          } else {\\n            this.handleContractorLogin(keycloakEvent.detail);\\n          }\\n        },\\n        error: () => {\\n          this.alert.error('Error al intentar autenticarse');\\n        },\\n      });\\n  }\\n\\n  private handleUserProfiles(): void {\\n    const profiles = this.authService.getUserProfiles();\\n\\n    if (profiles.some((profile) => profile.profile_name === 'ADMINISTRATOR')) {\\n      this.router.navigate(['/contratistas']);\\n    } else if (profiles.some((profile) => profile.profile_name === 'SUPERVISOR')) {\\n      this.router.navigate(['/revisar-informes']);\\n    } else if (profiles.some((profile) => profile.profile_name === 'CONTRACTOR')) {\\n      this.router.navigate(['/mis-contratos']);\\n    } else if (\\n      profiles.some((profile) => profile.profile_name === 'CONTRACT-MANAGER')\\n    ) {\\n      this.router.navigate(['/contratos']);\\n    } else {\\n      this.router.navigate(['/']);\\n    }\\n\\n    this.alert.success('Autenticaci\\xF3n exitosa');\\n  }\\n\\n  private handleContractorLogin(token: string): void {\\n    const contractorToken: ContractorToken = { token };\\n\\n    this.userService\\n      .createLogin(contractorToken)\\n      .pipe(finalize(() => this.spinner.hide()))\\n      .subscribe({\\n        next: (success) => {\\n          if (success) {\\n            this.authService\\n              .login(token)\\n              .pipe(finalize(() => this.spinner.hide()))\\n              .subscribe({\\n                next: (success) => {\\n                  if (success) {\\n                    this.router.navigate(['/mis-contratos']);\\n                    this.alert.success('Autenticaci\\xF3n exitosa');\\n                  } else {\\n                    this.alert.error('Credenciales inv\\xE1lidas');\\n                  }\\n                },\\n                error: () => {\\n                  this.alert.error('Error al intentar autenticarse');\\n                },\\n              });\\n          } else {\\n            this.alert.error('Credenciales inv\\xE1lidas');\\n          }\\n        },\\n        error: (err) => {\\n          if (err.status === 400) {\\n            if (err.error.detail !== \\\"Error: Autenticacion key(401)\\\") {\\n            this.alert.error(err.error.detail);\\n            }\\n          } else if (err.status === 404) {\\n            this.alert.error('El contratista no fue encontrado');\\n          } else {\\n            this.alert.error(err.error?.detail ?? 'Error al intentar autenticarse');\\n          }\\n        },\\n      });\\n  }\\n\\n  getUrlLogOut(): void {\\n    this.router.navigate(['/login']);\\n  }\\n\\n  monitorToken(): void {\\n    setInterval(() => {\\n      const token = localStorage.getItem('keycloak-token');\\n      if (!token) {\\n        localStorage.clear();\\n        this.router.navigate(['/login']);\\n      }\\n    }, 1000);\\n  }\\n}\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"ef50e6061f2fbb169c2acd54a44d2b88c7abde94\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_15kqzuohtu = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_15kqzuohtu();\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./keycloack.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./keycloack.component.scss?ngResource\";\nimport { Component, CUSTOM_ELEMENTS_SCHEMA, ViewEncapsulation } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { UserService } from '@core/auth/services/user.service';\nimport { finalize } from 'rxjs';\ncov_15kqzuohtu().s[0]++;\nlet KeycloackComponent = class KeycloackComponent {\n  constructor(authService, router, spinner, userService, alert) {\n    cov_15kqzuohtu().f[0]++;\n    cov_15kqzuohtu().s[1]++;\n    this.authService = authService;\n    cov_15kqzuohtu().s[2]++;\n    this.router = router;\n    cov_15kqzuohtu().s[3]++;\n    this.spinner = spinner;\n    cov_15kqzuohtu().s[4]++;\n    this.userService = userService;\n    cov_15kqzuohtu().s[5]++;\n    this.alert = alert;\n  }\n  ngOnInit() {\n    cov_15kqzuohtu().f[1]++;\n    cov_15kqzuohtu().s[6]++;\n    this.monitorToken();\n  }\n  onTokenResolved(event) {\n    cov_15kqzuohtu().f[2]++;\n    const keycloakEvent = (cov_15kqzuohtu().s[7]++, event);\n    cov_15kqzuohtu().s[8]++;\n    this.spinner.show();\n    cov_15kqzuohtu().s[9]++;\n    this.authService.login(keycloakEvent.detail).pipe(finalize(() => {\n      cov_15kqzuohtu().f[3]++;\n      cov_15kqzuohtu().s[10]++;\n      return this.spinner.hide();\n    })).subscribe({\n      next: success => {\n        cov_15kqzuohtu().f[4]++;\n        cov_15kqzuohtu().s[11]++;\n        if (success) {\n          cov_15kqzuohtu().b[0][0]++;\n          cov_15kqzuohtu().s[12]++;\n          this.handleUserProfiles();\n        } else {\n          cov_15kqzuohtu().b[0][1]++;\n          cov_15kqzuohtu().s[13]++;\n          this.handleContractorLogin(keycloakEvent.detail);\n        }\n      },\n      error: () => {\n        cov_15kqzuohtu().f[5]++;\n        cov_15kqzuohtu().s[14]++;\n        this.alert.error('Error al intentar autenticarse');\n      }\n    });\n  }\n  handleUserProfiles() {\n    cov_15kqzuohtu().f[6]++;\n    const profiles = (cov_15kqzuohtu().s[15]++, this.authService.getUserProfiles());\n    cov_15kqzuohtu().s[16]++;\n    if (profiles.some(profile => {\n      cov_15kqzuohtu().f[7]++;\n      cov_15kqzuohtu().s[17]++;\n      return profile.profile_name === 'ADMINISTRATOR';\n    })) {\n      cov_15kqzuohtu().b[1][0]++;\n      cov_15kqzuohtu().s[18]++;\n      this.router.navigate(['/contratistas']);\n    } else {\n      cov_15kqzuohtu().b[1][1]++;\n      cov_15kqzuohtu().s[19]++;\n      if (profiles.some(profile => {\n        cov_15kqzuohtu().f[8]++;\n        cov_15kqzuohtu().s[20]++;\n        return profile.profile_name === 'SUPERVISOR';\n      })) {\n        cov_15kqzuohtu().b[2][0]++;\n        cov_15kqzuohtu().s[21]++;\n        this.router.navigate(['/revisar-informes']);\n      } else {\n        cov_15kqzuohtu().b[2][1]++;\n        cov_15kqzuohtu().s[22]++;\n        if (profiles.some(profile => {\n          cov_15kqzuohtu().f[9]++;\n          cov_15kqzuohtu().s[23]++;\n          return profile.profile_name === 'CONTRACTOR';\n        })) {\n          cov_15kqzuohtu().b[3][0]++;\n          cov_15kqzuohtu().s[24]++;\n          this.router.navigate(['/mis-contratos']);\n        } else {\n          cov_15kqzuohtu().b[3][1]++;\n          cov_15kqzuohtu().s[25]++;\n          if (profiles.some(profile => {\n            cov_15kqzuohtu().f[10]++;\n            cov_15kqzuohtu().s[26]++;\n            return profile.profile_name === 'CONTRACT-MANAGER';\n          })) {\n            cov_15kqzuohtu().b[4][0]++;\n            cov_15kqzuohtu().s[27]++;\n            this.router.navigate(['/contratos']);\n          } else {\n            cov_15kqzuohtu().b[4][1]++;\n            cov_15kqzuohtu().s[28]++;\n            this.router.navigate(['/']);\n          }\n        }\n      }\n    }\n    cov_15kqzuohtu().s[29]++;\n    this.alert.success('Autenticación exitosa');\n  }\n  handleContractorLogin(token) {\n    cov_15kqzuohtu().f[11]++;\n    const contractorToken = (cov_15kqzuohtu().s[30]++, {\n      token\n    });\n    cov_15kqzuohtu().s[31]++;\n    this.userService.createLogin(contractorToken).pipe(finalize(() => {\n      cov_15kqzuohtu().f[12]++;\n      cov_15kqzuohtu().s[32]++;\n      return this.spinner.hide();\n    })).subscribe({\n      next: success => {\n        cov_15kqzuohtu().f[13]++;\n        cov_15kqzuohtu().s[33]++;\n        if (success) {\n          cov_15kqzuohtu().b[5][0]++;\n          cov_15kqzuohtu().s[34]++;\n          this.authService.login(token).pipe(finalize(() => {\n            cov_15kqzuohtu().f[14]++;\n            cov_15kqzuohtu().s[35]++;\n            return this.spinner.hide();\n          })).subscribe({\n            next: success => {\n              cov_15kqzuohtu().f[15]++;\n              cov_15kqzuohtu().s[36]++;\n              if (success) {\n                cov_15kqzuohtu().b[6][0]++;\n                cov_15kqzuohtu().s[37]++;\n                this.router.navigate(['/mis-contratos']);\n                cov_15kqzuohtu().s[38]++;\n                this.alert.success('Autenticación exitosa');\n              } else {\n                cov_15kqzuohtu().b[6][1]++;\n                cov_15kqzuohtu().s[39]++;\n                this.alert.error('Credenciales inválidas');\n              }\n            },\n            error: () => {\n              cov_15kqzuohtu().f[16]++;\n              cov_15kqzuohtu().s[40]++;\n              this.alert.error('Error al intentar autenticarse');\n            }\n          });\n        } else {\n          cov_15kqzuohtu().b[5][1]++;\n          cov_15kqzuohtu().s[41]++;\n          this.alert.error('Credenciales inválidas');\n        }\n      },\n      error: err => {\n        cov_15kqzuohtu().f[17]++;\n        cov_15kqzuohtu().s[42]++;\n        if (err.status === 400) {\n          cov_15kqzuohtu().b[7][0]++;\n          cov_15kqzuohtu().s[43]++;\n          if (err.error.detail !== \"Error: Autenticacion key(401)\") {\n            cov_15kqzuohtu().b[8][0]++;\n            cov_15kqzuohtu().s[44]++;\n            this.alert.error(err.error.detail);\n          } else {\n            cov_15kqzuohtu().b[8][1]++;\n          }\n        } else {\n          cov_15kqzuohtu().b[7][1]++;\n          cov_15kqzuohtu().s[45]++;\n          if (err.status === 404) {\n            cov_15kqzuohtu().b[9][0]++;\n            cov_15kqzuohtu().s[46]++;\n            this.alert.error('El contratista no fue encontrado');\n          } else {\n            cov_15kqzuohtu().b[9][1]++;\n            cov_15kqzuohtu().s[47]++;\n            this.alert.error((cov_15kqzuohtu().b[10][0]++, err.error?.detail) ?? (cov_15kqzuohtu().b[10][1]++, 'Error al intentar autenticarse'));\n          }\n        }\n      }\n    });\n  }\n  getUrlLogOut() {\n    cov_15kqzuohtu().f[18]++;\n    cov_15kqzuohtu().s[48]++;\n    this.router.navigate(['/login']);\n  }\n  monitorToken() {\n    cov_15kqzuohtu().f[19]++;\n    cov_15kqzuohtu().s[49]++;\n    setInterval(() => {\n      cov_15kqzuohtu().f[20]++;\n      const token = (cov_15kqzuohtu().s[50]++, localStorage.getItem('keycloak-token'));\n      cov_15kqzuohtu().s[51]++;\n      if (!token) {\n        cov_15kqzuohtu().b[11][0]++;\n        cov_15kqzuohtu().s[52]++;\n        localStorage.clear();\n        cov_15kqzuohtu().s[53]++;\n        this.router.navigate(['/login']);\n      } else {\n        cov_15kqzuohtu().b[11][1]++;\n      }\n    }, 1000);\n  }\n  static {\n    cov_15kqzuohtu().s[54]++;\n    this.ctorParameters = () => {\n      cov_15kqzuohtu().f[21]++;\n      cov_15kqzuohtu().s[55]++;\n      return [{\n        type: AuthService\n      }, {\n        type: Router\n      }, {\n        type: NgxSpinnerService\n      }, {\n        type: UserService\n      }, {\n        type: AlertService\n      }];\n    };\n  }\n};\ncov_15kqzuohtu().s[56]++;\nKeycloackComponent = __decorate([Component({\n  selector: 'app-keycloack',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: true,\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\n  encapsulation: ViewEncapsulation.None,\n  styles: [__NG_CLI_RESOURCE__1]\n})], KeycloackComponent);\nexport { KeycloackComponent };", "map": {"version": 3, "names": ["cov_15k<PERSON><PERSON><PERSON><PERSON>", "actualCoverage", "Component", "CUSTOM_ELEMENTS_SCHEMA", "ViewEncapsulation", "Router", "AuthService", "AlertService", "NgxSpinnerService", "UserService", "finalize", "s", "KeycloackComponent", "constructor", "authService", "router", "spinner", "userService", "alert", "f", "ngOnInit", "monitorToken", "onTokenResolved", "event", "keycloakEvent", "show", "login", "detail", "pipe", "hide", "subscribe", "next", "success", "b", "handleUserProfiles", "handleContractorLogin", "error", "profiles", "getUserProfiles", "some", "profile", "profile_name", "navigate", "token", "contractorToken", "createLogin", "err", "status", "getUrlLogOut", "setInterval", "localStorage", "getItem", "clear", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone", "schemas", "encapsulation", "None"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\core\\layout\\keycloack\\keycloack.component.ts"], "sourcesContent": ["import {\n  Component,\n  CUSTOM_ELEMENTS_SCHEMA,\n  OnInit,\n  ViewEncapsulation,\n} from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { UserService } from '@core/auth/services/user.service';\nimport { finalize } from 'rxjs';\n\ninterface CustomEvent {\n  detail: string;\n}\n\ninterface ContractorToken {\n  token: string;\n}\n\n@Component({\n  selector: 'app-keycloack',\n  templateUrl: './keycloack.component.html',\n  styleUrl: './keycloack.component.scss',\n  standalone: true,\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\n  encapsulation: ViewEncapsulation.None,\n})\nexport class KeycloackComponent implements OnInit {\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n    private spinner: NgxSpinnerService,\n    private userService: UserService,\n    private alert: AlertService,\n  ) {}\n\n  ngOnInit(): void {\n    this.monitorToken();\n  }\n\n  onTokenResolved(event: CustomEvent | Event): void {\n    const keycloakEvent = event as CustomEvent;\n    this.spinner.show();\n\n    this.authService\n      .login(keycloakEvent.detail)\n      .pipe(finalize(() => this.spinner.hide()))\n      .subscribe({\n        next: (success) => {\n          if (success) {\n            this.handleUserProfiles();\n          } else {\n            this.handleContractorLogin(keycloakEvent.detail);\n          }\n        },\n        error: () => {\n          this.alert.error('Error al intentar autenticarse');\n        },\n      });\n  }\n\n  private handleUserProfiles(): void {\n    const profiles = this.authService.getUserProfiles();\n\n    if (profiles.some((profile) => profile.profile_name === 'ADMINISTRATOR')) {\n      this.router.navigate(['/contratistas']);\n    } else if (profiles.some((profile) => profile.profile_name === 'SUPERVISOR')) {\n      this.router.navigate(['/revisar-informes']);\n    } else if (profiles.some((profile) => profile.profile_name === 'CONTRACTOR')) {\n      this.router.navigate(['/mis-contratos']);\n    } else if (\n      profiles.some((profile) => profile.profile_name === 'CONTRACT-MANAGER')\n    ) {\n      this.router.navigate(['/contratos']);\n    } else {\n      this.router.navigate(['/']);\n    }\n\n    this.alert.success('Autenticación exitosa');\n  }\n\n  private handleContractorLogin(token: string): void {\n    const contractorToken: ContractorToken = { token };\n\n    this.userService\n      .createLogin(contractorToken)\n      .pipe(finalize(() => this.spinner.hide()))\n      .subscribe({\n        next: (success) => {\n          if (success) {\n            this.authService\n              .login(token)\n              .pipe(finalize(() => this.spinner.hide()))\n              .subscribe({\n                next: (success) => {\n                  if (success) {\n                    this.router.navigate(['/mis-contratos']);\n                    this.alert.success('Autenticación exitosa');\n                  } else {\n                    this.alert.error('Credenciales inválidas');\n                  }\n                },\n                error: () => {\n                  this.alert.error('Error al intentar autenticarse');\n                },\n              });\n          } else {\n            this.alert.error('Credenciales inválidas');\n          }\n        },\n        error: (err) => {\n          if (err.status === 400) {\n            if (err.error.detail !== \"Error: Autenticacion key(401)\") {\n            this.alert.error(err.error.detail);\n            }\n          } else if (err.status === 404) {\n            this.alert.error('El contratista no fue encontrado');\n          } else {\n            this.alert.error(err.error?.detail ?? 'Error al intentar autenticarse');\n          }\n        },\n      });\n  }\n\n  getUrlLogOut(): void {\n    this.router.navigate(['/login']);\n  }\n\n  monitorToken(): void {\n    setInterval(() => {\n      const token = localStorage.getItem('keycloak-token');\n      if (!token) {\n        localStorage.clear();\n        this.router.navigate(['/login']);\n      }\n    }, 1000);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAkCY;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;AAlCZ,SACEE,SAAS,EACTC,sBAAsB,EAEtBC,iBAAiB,QACZ,eAAe;AACtB,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,iBAAiB,QAAQ,aAAa;AAC/C,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,SAASC,QAAQ,QAAQ,MAAM;AAACV,cAAA,GAAAW,CAAA;AAkBzB,IAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EAC7BC,YACUC,WAAwB,EACxBC,MAAc,EACdC,OAA0B,EAC1BC,WAAwB,EACxBC,KAAmB;IAAAlB,cAAA,GAAAmB,CAAA;IAAAnB,cAAA,GAAAW,CAAA;IAJnB,KAAAG,WAAW,GAAXA,WAAW;IAAad,cAAA,GAAAW,CAAA;IACxB,KAAAI,MAAM,GAANA,MAAM;IAAQf,cAAA,GAAAW,CAAA;IACd,KAAAK,OAAO,GAAPA,OAAO;IAAmBhB,cAAA,GAAAW,CAAA;IAC1B,KAAAM,WAAW,GAAXA,WAAW;IAAajB,cAAA,GAAAW,CAAA;IACxB,KAAAO,KAAK,GAALA,KAAK;EACZ;EAEHE,QAAQA,CAAA;IAAApB,cAAA,GAAAmB,CAAA;IAAAnB,cAAA,GAAAW,CAAA;IACN,IAAI,CAACU,YAAY,EAAE;EACrB;EAEAC,eAAeA,CAACC,KAA0B;IAAAvB,cAAA,GAAAmB,CAAA;IACxC,MAAMK,aAAa,IAAAxB,cAAA,GAAAW,CAAA,OAAGY,KAAoB;IAACvB,cAAA,GAAAW,CAAA;IAC3C,IAAI,CAACK,OAAO,CAACS,IAAI,EAAE;IAACzB,cAAA,GAAAW,CAAA;IAEpB,IAAI,CAACG,WAAW,CACbY,KAAK,CAACF,aAAa,CAACG,MAAM,CAAC,CAC3BC,IAAI,CAAClB,QAAQ,CAAC,MAAM;MAAAV,cAAA,GAAAmB,CAAA;MAAAnB,cAAA,GAAAW,CAAA;MAAA,WAAI,CAACK,OAAO,CAACa,IAAI,EAAE;IAAF,CAAE,CAAC,CAAC,CACzCC,SAAS,CAAC;MACTC,IAAI,EAAGC,OAAO,IAAI;QAAAhC,cAAA,GAAAmB,CAAA;QAAAnB,cAAA,GAAAW,CAAA;QAChB,IAAIqB,OAAO,EAAE;UAAAhC,cAAA,GAAAiC,CAAA;UAAAjC,cAAA,GAAAW,CAAA;UACX,IAAI,CAACuB,kBAAkB,EAAE;QAC3B,CAAC,MAAM;UAAAlC,cAAA,GAAAiC,CAAA;UAAAjC,cAAA,GAAAW,CAAA;UACL,IAAI,CAACwB,qBAAqB,CAACX,aAAa,CAACG,MAAM,CAAC;QAClD;MACF,CAAC;MACDS,KAAK,EAAEA,CAAA,KAAK;QAAApC,cAAA,GAAAmB,CAAA;QAAAnB,cAAA,GAAAW,CAAA;QACV,IAAI,CAACO,KAAK,CAACkB,KAAK,CAAC,gCAAgC,CAAC;MACpD;KACD,CAAC;EACN;EAEQF,kBAAkBA,CAAA;IAAAlC,cAAA,GAAAmB,CAAA;IACxB,MAAMkB,QAAQ,IAAArC,cAAA,GAAAW,CAAA,QAAG,IAAI,CAACG,WAAW,CAACwB,eAAe,EAAE;IAACtC,cAAA,GAAAW,CAAA;IAEpD,IAAI0B,QAAQ,CAACE,IAAI,CAAEC,OAAO,IAAK;MAAAxC,cAAA,GAAAmB,CAAA;MAAAnB,cAAA,GAAAW,CAAA;MAAA,OAAA6B,OAAO,CAACC,YAAY,KAAK,eAAe;IAAf,CAAe,CAAC,EAAE;MAAAzC,cAAA,GAAAiC,CAAA;MAAAjC,cAAA,GAAAW,CAAA;MACxE,IAAI,CAACI,MAAM,CAAC2B,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;IACzC,CAAC,MAAM;MAAA1C,cAAA,GAAAiC,CAAA;MAAAjC,cAAA,GAAAW,CAAA;MAAA,IAAI0B,QAAQ,CAACE,IAAI,CAAEC,OAAO,IAAK;QAAAxC,cAAA,GAAAmB,CAAA;QAAAnB,cAAA,GAAAW,CAAA;QAAA,OAAA6B,OAAO,CAACC,YAAY,KAAK,YAAY;MAAZ,CAAY,CAAC,EAAE;QAAAzC,cAAA,GAAAiC,CAAA;QAAAjC,cAAA,GAAAW,CAAA;QAC5E,IAAI,CAACI,MAAM,CAAC2B,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;MAC7C,CAAC,MAAM;QAAA1C,cAAA,GAAAiC,CAAA;QAAAjC,cAAA,GAAAW,CAAA;QAAA,IAAI0B,QAAQ,CAACE,IAAI,CAAEC,OAAO,IAAK;UAAAxC,cAAA,GAAAmB,CAAA;UAAAnB,cAAA,GAAAW,CAAA;UAAA,OAAA6B,OAAO,CAACC,YAAY,KAAK,YAAY;QAAZ,CAAY,CAAC,EAAE;UAAAzC,cAAA,GAAAiC,CAAA;UAAAjC,cAAA,GAAAW,CAAA;UAC5E,IAAI,CAACI,MAAM,CAAC2B,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;QAC1C,CAAC,MAAM;UAAA1C,cAAA,GAAAiC,CAAA;UAAAjC,cAAA,GAAAW,CAAA;UAAA,IACL0B,QAAQ,CAACE,IAAI,CAAEC,OAAO,IAAK;YAAAxC,cAAA,GAAAmB,CAAA;YAAAnB,cAAA,GAAAW,CAAA;YAAA,OAAA6B,OAAO,CAACC,YAAY,KAAK,kBAAkB;UAAlB,CAAkB,CAAC,EACvE;YAAAzC,cAAA,GAAAiC,CAAA;YAAAjC,cAAA,GAAAW,CAAA;YACA,IAAI,CAACI,MAAM,CAAC2B,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;UACtC,CAAC,MAAM;YAAA1C,cAAA,GAAAiC,CAAA;YAAAjC,cAAA,GAAAW,CAAA;YACL,IAAI,CAACI,MAAM,CAAC2B,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;UAC7B;QAAA;MAAA;IAAA;IAAC1C,cAAA,GAAAW,CAAA;IAED,IAAI,CAACO,KAAK,CAACc,OAAO,CAAC,uBAAuB,CAAC;EAC7C;EAEQG,qBAAqBA,CAACQ,KAAa;IAAA3C,cAAA,GAAAmB,CAAA;IACzC,MAAMyB,eAAe,IAAA5C,cAAA,GAAAW,CAAA,QAAoB;MAAEgC;IAAK,CAAE;IAAC3C,cAAA,GAAAW,CAAA;IAEnD,IAAI,CAACM,WAAW,CACb4B,WAAW,CAACD,eAAe,CAAC,CAC5BhB,IAAI,CAAClB,QAAQ,CAAC,MAAM;MAAAV,cAAA,GAAAmB,CAAA;MAAAnB,cAAA,GAAAW,CAAA;MAAA,WAAI,CAACK,OAAO,CAACa,IAAI,EAAE;IAAF,CAAE,CAAC,CAAC,CACzCC,SAAS,CAAC;MACTC,IAAI,EAAGC,OAAO,IAAI;QAAAhC,cAAA,GAAAmB,CAAA;QAAAnB,cAAA,GAAAW,CAAA;QAChB,IAAIqB,OAAO,EAAE;UAAAhC,cAAA,GAAAiC,CAAA;UAAAjC,cAAA,GAAAW,CAAA;UACX,IAAI,CAACG,WAAW,CACbY,KAAK,CAACiB,KAAK,CAAC,CACZf,IAAI,CAAClB,QAAQ,CAAC,MAAM;YAAAV,cAAA,GAAAmB,CAAA;YAAAnB,cAAA,GAAAW,CAAA;YAAA,WAAI,CAACK,OAAO,CAACa,IAAI,EAAE;UAAF,CAAE,CAAC,CAAC,CACzCC,SAAS,CAAC;YACTC,IAAI,EAAGC,OAAO,IAAI;cAAAhC,cAAA,GAAAmB,CAAA;cAAAnB,cAAA,GAAAW,CAAA;cAChB,IAAIqB,OAAO,EAAE;gBAAAhC,cAAA,GAAAiC,CAAA;gBAAAjC,cAAA,GAAAW,CAAA;gBACX,IAAI,CAACI,MAAM,CAAC2B,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;gBAAC1C,cAAA,GAAAW,CAAA;gBACzC,IAAI,CAACO,KAAK,CAACc,OAAO,CAAC,uBAAuB,CAAC;cAC7C,CAAC,MAAM;gBAAAhC,cAAA,GAAAiC,CAAA;gBAAAjC,cAAA,GAAAW,CAAA;gBACL,IAAI,CAACO,KAAK,CAACkB,KAAK,CAAC,wBAAwB,CAAC;cAC5C;YACF,CAAC;YACDA,KAAK,EAAEA,CAAA,KAAK;cAAApC,cAAA,GAAAmB,CAAA;cAAAnB,cAAA,GAAAW,CAAA;cACV,IAAI,CAACO,KAAK,CAACkB,KAAK,CAAC,gCAAgC,CAAC;YACpD;WACD,CAAC;QACN,CAAC,MAAM;UAAApC,cAAA,GAAAiC,CAAA;UAAAjC,cAAA,GAAAW,CAAA;UACL,IAAI,CAACO,KAAK,CAACkB,KAAK,CAAC,wBAAwB,CAAC;QAC5C;MACF,CAAC;MACDA,KAAK,EAAGU,GAAG,IAAI;QAAA9C,cAAA,GAAAmB,CAAA;QAAAnB,cAAA,GAAAW,CAAA;QACb,IAAImC,GAAG,CAACC,MAAM,KAAK,GAAG,EAAE;UAAA/C,cAAA,GAAAiC,CAAA;UAAAjC,cAAA,GAAAW,CAAA;UACtB,IAAImC,GAAG,CAACV,KAAK,CAACT,MAAM,KAAK,+BAA+B,EAAE;YAAA3B,cAAA,GAAAiC,CAAA;YAAAjC,cAAA,GAAAW,CAAA;YAC1D,IAAI,CAACO,KAAK,CAACkB,KAAK,CAACU,GAAG,CAACV,KAAK,CAACT,MAAM,CAAC;UAClC,CAAC;YAAA3B,cAAA,GAAAiC,CAAA;UAAA;QACH,CAAC,MAAM;UAAAjC,cAAA,GAAAiC,CAAA;UAAAjC,cAAA,GAAAW,CAAA;UAAA,IAAImC,GAAG,CAACC,MAAM,KAAK,GAAG,EAAE;YAAA/C,cAAA,GAAAiC,CAAA;YAAAjC,cAAA,GAAAW,CAAA;YAC7B,IAAI,CAACO,KAAK,CAACkB,KAAK,CAAC,kCAAkC,CAAC;UACtD,CAAC,MAAM;YAAApC,cAAA,GAAAiC,CAAA;YAAAjC,cAAA,GAAAW,CAAA;YACL,IAAI,CAACO,KAAK,CAACkB,KAAK,CAAC,CAAApC,cAAA,GAAAiC,CAAA,WAAAa,GAAG,CAACV,KAAK,EAAET,MAAM,MAAA3B,cAAA,GAAAiC,CAAA,WAAI,gCAAgC,EAAC;UACzE;QAAA;MACF;KACD,CAAC;EACN;EAEAe,YAAYA,CAAA;IAAAhD,cAAA,GAAAmB,CAAA;IAAAnB,cAAA,GAAAW,CAAA;IACV,IAAI,CAACI,MAAM,CAAC2B,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEArB,YAAYA,CAAA;IAAArB,cAAA,GAAAmB,CAAA;IAAAnB,cAAA,GAAAW,CAAA;IACVsC,WAAW,CAAC,MAAK;MAAAjD,cAAA,GAAAmB,CAAA;MACf,MAAMwB,KAAK,IAAA3C,cAAA,GAAAW,CAAA,QAAGuC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;MAACnD,cAAA,GAAAW,CAAA;MACrD,IAAI,CAACgC,KAAK,EAAE;QAAA3C,cAAA,GAAAiC,CAAA;QAAAjC,cAAA,GAAAW,CAAA;QACVuC,YAAY,CAACE,KAAK,EAAE;QAACpD,cAAA,GAAAW,CAAA;QACrB,IAAI,CAACI,MAAM,CAAC2B,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAClC,CAAC;QAAA1C,cAAA,GAAAiC,CAAA;MAAA;IACH,CAAC,EAAE,IAAI,CAAC;EACV;;;;;;;;;;;;;;;;;;;;;AA7GWrB,kBAAkB,GAAAyC,UAAA,EAR9BnD,SAAS,CAAC;EACToD,QAAQ,EAAE,eAAe;EACzBC,QAAA,EAAAC,oBAAyC;EAEzCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACvD,sBAAsB,CAAC;EACjCwD,aAAa,EAAEvD,iBAAiB,CAACwD,IAAI;;CACtC,CAAC,C,EACWhD,kBAAkB,CA8G9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}