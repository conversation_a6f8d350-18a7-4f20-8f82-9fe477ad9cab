import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { EducationLevel } from '@contractor-management/models/education-level.model';
import { environment } from '@env';
import { EducationLevelService } from './education-level.service';

describe('EducationLevelService', () => {
  let service: EducationLevelService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/education-levels`;

  const mockEducationLevel: EducationLevel = {
    id: 1,
    name: 'Test Education Level',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [EducationLevelService],
    });
    service = TestBed.inject(EducationLevelService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all education levels', () => {
      const mockEducationLevels = [mockEducationLevel];

      service.getAll().subscribe((educationLevels) => {
        expect(educationLevels).toEqual(mockEducationLevels);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockEducationLevels);
    });
  });

  describe('getById', () => {
    it('should return an education level by id', () => {
      const id = 1;

      service.getById(id).subscribe((educationLevel) => {
        expect(educationLevel).toEqual(mockEducationLevel);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockEducationLevel);
    });
  });

  describe('create', () => {
    it('should create a new education level', () => {
      const newEducationLevel: Omit<EducationLevel, 'id'> = {
        name: 'New Education Level',
      };

      service.create(newEducationLevel).subscribe((educationLevel) => {
        expect(educationLevel).toEqual(mockEducationLevel);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newEducationLevel);
      req.flush(mockEducationLevel);
    });
  });

  describe('update', () => {
    it('should update an education level', () => {
      const id = 1;
      const updateData: Partial<EducationLevel> = {
        name: 'Updated Education Level',
      };

      service.update(id, updateData).subscribe((educationLevel) => {
        expect(educationLevel).toEqual(mockEducationLevel);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockEducationLevel);
    });
  });

  describe('delete', () => {
    it('should delete an education level', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });

  describe('getByName', () => {
    it('should return an education level by name', () => {
      const name = 'Test Education Level';

      service.getByName(name).subscribe((educationLevel) => {
        expect(educationLevel).toEqual(mockEducationLevel);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockEducationLevel);
    });
  });
});