{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { ContractAuditHistoryService } from './contract-audit-history.service';\ndescribe('ContractAuditHistoryService', () => {\n  let service;\n  let httpTestingController;\n  const API_URL = `${environment.apiUrl}/contract-audit-histories`;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [ContractAuditHistoryService]\n    });\n    service = TestBed.inject(ContractAuditHistoryService);\n    httpTestingController = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpTestingController.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all contract audit histories', () => {\n      const mockAuditHistories = [{\n        id: 1,\n        contractId: 101,\n        auditDate: new Date(),\n        auditorId: 1,\n        comment: 'Initial audit',\n        auditStatusId: 1\n      }, {\n        id: 2,\n        contractId: 102,\n        auditDate: new Date(),\n        auditorId: 2,\n        comment: 'Follow-up audit',\n        auditStatusId: 2\n      }];\n      service.getAll().subscribe(histories => {\n        expect(histories).toEqual(mockAuditHistories);\n      });\n      const req = httpTestingController.expectOne(API_URL);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockAuditHistories);\n    });\n  });\n  describe('getById', () => {\n    it('should return a single contract audit history by id', () => {\n      const mockAuditHistory = {\n        id: 1,\n        contractId: 101,\n        auditDate: new Date(),\n        auditorId: 1,\n        comment: 'Initial audit',\n        auditStatusId: 1\n      };\n      service.getById(1).subscribe(history => {\n        expect(history).toEqual(mockAuditHistory);\n      });\n      const req = httpTestingController.expectOne(`${API_URL}/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockAuditHistory);\n    });\n  });\n  describe('getByContractId', () => {\n    it('should return contract audit histories by contract id', () => {\n      const contractId = 101;\n      const mockAuditHistories = [{\n        id: 1,\n        contractId: contractId,\n        auditDate: new Date(),\n        auditorId: 1,\n        comment: 'Initial audit',\n        auditStatusId: 1\n      }, {\n        id: 3,\n        contractId: contractId,\n        auditDate: new Date(),\n        auditorId: 2,\n        comment: 'Follow-up audit',\n        auditStatusId: 2\n      }];\n      service.getByContractId(contractId).subscribe(histories => {\n        expect(histories).toEqual(mockAuditHistories);\n      });\n      const req = httpTestingController.expectOne(`${API_URL}/contract/${contractId}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockAuditHistories);\n    });\n  });\n  describe('create', () => {\n    it('should create a new contract audit history', () => {\n      const newAuditHistory = {\n        contractId: 101,\n        auditDate: new Date(),\n        auditorId: 1,\n        comment: 'Initial audit',\n        auditStatusId: 1\n      };\n      const mockResponse = {\n        id: 1,\n        ...newAuditHistory\n      };\n      service.create(newAuditHistory).subscribe(history => {\n        expect(history).toEqual(mockResponse);\n      });\n      const req = httpTestingController.expectOne(API_URL);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newAuditHistory);\n      req.flush(mockResponse);\n    });\n  });\n  describe('update', () => {\n    it('should update an existing contract audit history', () => {\n      const id = 1;\n      const updateAuditHistory = {\n        comment: 'Updated audit comment',\n        auditStatusId: 2\n      };\n      const mockResponse = {\n        id: id,\n        contractId: 101,\n        auditDate: new Date(),\n        auditorId: 1,\n        comment: 'Updated audit comment',\n        auditStatusId: 2\n      };\n      service.update(id, updateAuditHistory).subscribe(history => {\n        expect(history).toEqual(mockResponse);\n      });\n      const req = httpTestingController.expectOne(`${API_URL}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateAuditHistory);\n      req.flush(mockResponse);\n    });\n  });\n  describe('delete', () => {\n    it('should delete a contract audit history', () => {\n      const id = 1;\n      service.delete(id).subscribe(response => {\n        expect(response).toBeNull();\n      });\n      const req = httpTestingController.expectOne(`${API_URL}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "ContractAuditHistoryService", "describe", "service", "httpTestingController", "API_URL", "apiUrl", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockAuditHistories", "id", "contractId", "auditDate", "Date", "auditorId", "comment", "auditStatusId", "getAll", "subscribe", "histories", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "mockAuditHistory", "getById", "history", "getByContractId", "newAuditHistory", "mockResponse", "create", "body", "updateAuditHistory", "update", "delete", "response", "toBeNull"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\services\\contract-audit-history.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { ContractAuditHistory } from '../models/contract-audit-history.model';\nimport { ContractAuditHistoryService } from './contract-audit-history.service';\n\ndescribe('ContractAuditHistoryService', () => {\n  let service: ContractAuditHistoryService;\n  let httpTestingController: HttpTestingController;\n  const API_URL = `${environment.apiUrl}/contract-audit-histories`;\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [ContractAuditHistoryService],\n    });\n    service = TestBed.inject(ContractAuditHistoryService);\n    httpTestingController = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpTestingController.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all contract audit histories', () => {\n      const mockAuditHistories: ContractAuditHistory[] = [\n        {\n          id: 1,\n          contractId: 101,\n          auditDate: new Date(),\n          auditorId: 1,\n          comment: 'Initial audit',\n          auditStatusId: 1,\n        },\n        {\n          id: 2,\n          contractId: 102,\n          auditDate: new Date(),\n          auditorId: 2,\n          comment: 'Follow-up audit',\n          auditStatusId: 2,\n        },\n      ];\n\n      service.getAll().subscribe((histories) => {\n        expect(histories).toEqual(mockAuditHistories);\n      });\n\n      const req = httpTestingController.expectOne(API_URL);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockAuditHistories);\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a single contract audit history by id', () => {\n      const mockAuditHistory: ContractAuditHistory = {\n        id: 1,\n        contractId: 101,\n        auditDate: new Date(),\n        auditorId: 1,\n        comment: 'Initial audit',\n        auditStatusId: 1,\n      };\n\n      service.getById(1).subscribe((history) => {\n        expect(history).toEqual(mockAuditHistory);\n      });\n\n      const req = httpTestingController.expectOne(`${API_URL}/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockAuditHistory);\n    });\n  });\n\n  describe('getByContractId', () => {\n    it('should return contract audit histories by contract id', () => {\n      const contractId = 101;\n      const mockAuditHistories: ContractAuditHistory[] = [\n        {\n          id: 1,\n          contractId: contractId,\n          auditDate: new Date(),\n          auditorId: 1,\n          comment: 'Initial audit',\n          auditStatusId: 1,\n        },\n        {\n          id: 3,\n          contractId: contractId,\n          auditDate: new Date(),\n          auditorId: 2,\n          comment: 'Follow-up audit',\n          auditStatusId: 2,\n        },\n      ];\n\n      service.getByContractId(contractId).subscribe((histories) => {\n        expect(histories).toEqual(mockAuditHistories);\n      });\n\n      const req = httpTestingController.expectOne(\n        `${API_URL}/contract/${contractId}`,\n      );\n      expect(req.request.method).toBe('GET');\n      req.flush(mockAuditHistories);\n    });\n  });\n\n  describe('create', () => {\n    it('should create a new contract audit history', () => {\n      const newAuditHistory: Omit<ContractAuditHistory, 'id'> = {\n        contractId: 101,\n        auditDate: new Date(),\n        auditorId: 1,\n        comment: 'Initial audit',\n        auditStatusId: 1,\n      };\n\n      const mockResponse: ContractAuditHistory = {\n        id: 1,\n        ...newAuditHistory,\n      };\n\n      service.create(newAuditHistory).subscribe((history) => {\n        expect(history).toEqual(mockResponse);\n      });\n\n      const req = httpTestingController.expectOne(API_URL);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newAuditHistory);\n      req.flush(mockResponse);\n    });\n  });\n\n  describe('update', () => {\n    it('should update an existing contract audit history', () => {\n      const id = 1;\n      const updateAuditHistory: Partial<ContractAuditHistory> = {\n        comment: 'Updated audit comment',\n        auditStatusId: 2,\n      };\n\n      const mockResponse: ContractAuditHistory = {\n        id: id,\n        contractId: 101,\n        auditDate: new Date(),\n        auditorId: 1,\n        comment: 'Updated audit comment',\n        auditStatusId: 2,\n      };\n\n      service.update(id, updateAuditHistory).subscribe((history) => {\n        expect(history).toEqual(mockResponse);\n      });\n\n      const req = httpTestingController.expectOne(`${API_URL}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateAuditHistory);\n      req.flush(mockResponse);\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete a contract audit history', () => {\n      const id = 1;\n\n      service.delete(id).subscribe((response) => {\n        expect(response).toBeNull();\n      });\n\n      const req = httpTestingController.expectOne(`${API_URL}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,WAAW,QAAQ,MAAM;AAElC,SAASC,2BAA2B,QAAQ,kCAAkC;AAE9EC,QAAQ,CAAC,6BAA6B,EAAE,MAAK;EAC3C,IAAIC,OAAoC;EACxC,IAAIC,qBAA4C;EAChD,MAAMC,OAAO,GAAG,GAAGL,WAAW,CAACM,MAAM,2BAA2B;EAEhEC,UAAU,CAAC,MAAK;IACdR,OAAO,CAACS,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACZ,uBAAuB,CAAC;MAClCa,SAAS,EAAE,CAACT,2BAA2B;KACxC,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACY,MAAM,CAACV,2BAA2B,CAAC;IACrDG,qBAAqB,GAAGL,OAAO,CAACY,MAAM,CAACb,qBAAqB,CAAC;EAC/D,CAAC,CAAC;EAEFc,SAAS,CAAC,MAAK;IACbR,qBAAqB,CAACS,MAAM,EAAE;EAChC,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACZ,OAAO,CAAC,CAACa,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFd,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBY,EAAE,CAAC,4CAA4C,EAAE,MAAK;MACpD,MAAMG,kBAAkB,GAA2B,CACjD;QACEC,EAAE,EAAE,CAAC;QACLC,UAAU,EAAE,GAAG;QACfC,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBC,SAAS,EAAE,CAAC;QACZC,OAAO,EAAE,eAAe;QACxBC,aAAa,EAAE;OAChB,EACD;QACEN,EAAE,EAAE,CAAC;QACLC,UAAU,EAAE,GAAG;QACfC,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBC,SAAS,EAAE,CAAC;QACZC,OAAO,EAAE,iBAAiB;QAC1BC,aAAa,EAAE;OAChB,CACF;MAEDrB,OAAO,CAACsB,MAAM,EAAE,CAACC,SAAS,CAAEC,SAAS,IAAI;QACvCZ,MAAM,CAACY,SAAS,CAAC,CAACC,OAAO,CAACX,kBAAkB,CAAC;MAC/C,CAAC,CAAC;MAEF,MAAMY,GAAG,GAAGzB,qBAAqB,CAAC0B,SAAS,CAACzB,OAAO,CAAC;MACpDU,MAAM,CAACc,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACjB,kBAAkB,CAAC;IAC/B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFf,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBY,EAAE,CAAC,qDAAqD,EAAE,MAAK;MAC7D,MAAMqB,gBAAgB,GAAyB;QAC7CjB,EAAE,EAAE,CAAC;QACLC,UAAU,EAAE,GAAG;QACfC,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBC,SAAS,EAAE,CAAC;QACZC,OAAO,EAAE,eAAe;QACxBC,aAAa,EAAE;OAChB;MAEDrB,OAAO,CAACiC,OAAO,CAAC,CAAC,CAAC,CAACV,SAAS,CAAEW,OAAO,IAAI;QACvCtB,MAAM,CAACsB,OAAO,CAAC,CAACT,OAAO,CAACO,gBAAgB,CAAC;MAC3C,CAAC,CAAC;MAEF,MAAMN,GAAG,GAAGzB,qBAAqB,CAAC0B,SAAS,CAAC,GAAGzB,OAAO,IAAI,CAAC;MAC3DU,MAAM,CAACc,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACC,gBAAgB,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjC,QAAQ,CAAC,iBAAiB,EAAE,MAAK;IAC/BY,EAAE,CAAC,uDAAuD,EAAE,MAAK;MAC/D,MAAMK,UAAU,GAAG,GAAG;MACtB,MAAMF,kBAAkB,GAA2B,CACjD;QACEC,EAAE,EAAE,CAAC;QACLC,UAAU,EAAEA,UAAU;QACtBC,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBC,SAAS,EAAE,CAAC;QACZC,OAAO,EAAE,eAAe;QACxBC,aAAa,EAAE;OAChB,EACD;QACEN,EAAE,EAAE,CAAC;QACLC,UAAU,EAAEA,UAAU;QACtBC,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBC,SAAS,EAAE,CAAC;QACZC,OAAO,EAAE,iBAAiB;QAC1BC,aAAa,EAAE;OAChB,CACF;MAEDrB,OAAO,CAACmC,eAAe,CAACnB,UAAU,CAAC,CAACO,SAAS,CAAEC,SAAS,IAAI;QAC1DZ,MAAM,CAACY,SAAS,CAAC,CAACC,OAAO,CAACX,kBAAkB,CAAC;MAC/C,CAAC,CAAC;MAEF,MAAMY,GAAG,GAAGzB,qBAAqB,CAAC0B,SAAS,CACzC,GAAGzB,OAAO,aAAac,UAAU,EAAE,CACpC;MACDJ,MAAM,CAACc,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACjB,kBAAkB,CAAC;IAC/B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFf,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBY,EAAE,CAAC,4CAA4C,EAAE,MAAK;MACpD,MAAMyB,eAAe,GAAqC;QACxDpB,UAAU,EAAE,GAAG;QACfC,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBC,SAAS,EAAE,CAAC;QACZC,OAAO,EAAE,eAAe;QACxBC,aAAa,EAAE;OAChB;MAED,MAAMgB,YAAY,GAAyB;QACzCtB,EAAE,EAAE,CAAC;QACL,GAAGqB;OACJ;MAEDpC,OAAO,CAACsC,MAAM,CAACF,eAAe,CAAC,CAACb,SAAS,CAAEW,OAAO,IAAI;QACpDtB,MAAM,CAACsB,OAAO,CAAC,CAACT,OAAO,CAACY,YAAY,CAAC;MACvC,CAAC,CAAC;MAEF,MAAMX,GAAG,GAAGzB,qBAAqB,CAAC0B,SAAS,CAACzB,OAAO,CAAC;MACpDU,MAAM,CAACc,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvClB,MAAM,CAACc,GAAG,CAACE,OAAO,CAACW,IAAI,CAAC,CAACd,OAAO,CAACW,eAAe,CAAC;MACjDV,GAAG,CAACK,KAAK,CAACM,YAAY,CAAC;IACzB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFtC,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBY,EAAE,CAAC,kDAAkD,EAAE,MAAK;MAC1D,MAAMI,EAAE,GAAG,CAAC;MACZ,MAAMyB,kBAAkB,GAAkC;QACxDpB,OAAO,EAAE,uBAAuB;QAChCC,aAAa,EAAE;OAChB;MAED,MAAMgB,YAAY,GAAyB;QACzCtB,EAAE,EAAEA,EAAE;QACNC,UAAU,EAAE,GAAG;QACfC,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBC,SAAS,EAAE,CAAC;QACZC,OAAO,EAAE,uBAAuB;QAChCC,aAAa,EAAE;OAChB;MAEDrB,OAAO,CAACyC,MAAM,CAAC1B,EAAE,EAAEyB,kBAAkB,CAAC,CAACjB,SAAS,CAAEW,OAAO,IAAI;QAC3DtB,MAAM,CAACsB,OAAO,CAAC,CAACT,OAAO,CAACY,YAAY,CAAC;MACvC,CAAC,CAAC;MAEF,MAAMX,GAAG,GAAGzB,qBAAqB,CAAC0B,SAAS,CAAC,GAAGzB,OAAO,IAAIa,EAAE,EAAE,CAAC;MAC/DH,MAAM,CAACc,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtClB,MAAM,CAACc,GAAG,CAACE,OAAO,CAACW,IAAI,CAAC,CAACd,OAAO,CAACe,kBAAkB,CAAC;MACpDd,GAAG,CAACK,KAAK,CAACM,YAAY,CAAC;IACzB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFtC,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBY,EAAE,CAAC,wCAAwC,EAAE,MAAK;MAChD,MAAMI,EAAE,GAAG,CAAC;MAEZf,OAAO,CAAC0C,MAAM,CAAC3B,EAAE,CAAC,CAACQ,SAAS,CAAEoB,QAAQ,IAAI;QACxC/B,MAAM,CAAC+B,QAAQ,CAAC,CAACC,QAAQ,EAAE;MAC7B,CAAC,CAAC;MAEF,MAAMlB,GAAG,GAAGzB,qBAAqB,CAAC0B,SAAS,CAAC,GAAGzB,OAAO,IAAIa,EAAE,EAAE,CAAC;MAC/DH,MAAM,CAACc,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}