import { Form<PERSON><PERSON>er, FormControl, FormGroup } from '@angular/forms';
import {
  createDateComparisonValidator,
  DateComparisonValidatorConfig,
} from './date-comparison.validator';

describe('DateComparisonValidator', () => {
  let fb: FormBuilder;
  let form: FormGroup;
  let config: DateComparisonValidatorConfig;

  beforeEach(() => {
    fb = new FormBuilder();
    form = fb.group({
      startDate: [null],
      subscriptionDate: [null],
    });

    config = {
      minStartDate: null,
      latestContractEndDate: null,
    };
  });

  it('should return null when value is null', () => {
    const validator = createDateComparisonValidator(config);
    const result = validator(form.get('startDate')!);
    expect(result).toBeNull();
  });

  describe('Subscription Date Validation', () => {
    it('should return subscriptionDateNotBeforeStartDate error when subscription date is after start date', () => {
      const validator = createDateComparisonValidator(config);
      form.get('startDate')?.setValue(new Date('2024-01-01'));
      form.get('subscriptionDate')?.setValue(new Date('2024-02-01'));
      const result = validator(form.get('subscriptionDate')!);
      expect(result).toEqual({ subscriptionDateNotBeforeStartDate: true });
    });

    it('should return null when subscription date is before start date', () => {
      const validator = createDateComparisonValidator(config);
      form.get('startDate')?.setValue(new Date('2024-02-01'));
      form.get('subscriptionDate')?.setValue(new Date('2024-01-01'));
      const result = validator(form.get('subscriptionDate')!);
      expect(result).toBeNull();
    });
  });

  describe('Start Date Validation', () => {
    it('should return startDateNotAfterSubscriptionDate error when start date is before subscription date', () => {
      const validator = createDateComparisonValidator(config);
      form.get('subscriptionDate')?.setValue(new Date('2024-06-01'));
      form.get('startDate')?.setValue(new Date('2024-05-01'));
      const result = validator(form.get('startDate')!);
      expect(result).toEqual({ startDateNotAfterSubscriptionDate: true });
    });

    it('should return startDateTooEarly error when start date is before minStartDate', () => {
      config.minStartDate = new Date('2024-06-01');
      const validator = createDateComparisonValidator(config);
      form.get('startDate')?.setValue(new Date('2024-05-01'));
      const result = validator(form.get('startDate')!);
      expect(result).toEqual({ startDateTooEarly: true });
    });

    it('should return startDateAfterContractEnd error when start date is after latestContractEndDate', () => {
      config.latestContractEndDate = new Date('2024-12-31');
      const validator = createDateComparisonValidator(config);
      form.get('startDate')?.setValue(new Date('2025-01-01'));
      const result = validator(form.get('startDate')!);
      expect(result).toEqual({ startDateAfterContractEnd: true });
    });

    it('should return null when start date is valid', () => {
      config.minStartDate = new Date('2024-01-01');
      config.latestContractEndDate = new Date('2024-12-31');
      const validator = createDateComparisonValidator(config);
      form.get('startDate')?.setValue(new Date('2024-06-01'));
      form.get('subscriptionDate')?.setValue(new Date('2024-05-01'));
      const result = validator(form.get('startDate')!);
      expect(result).toBeNull();
    });
  });

  describe('Edge Cases', () => {
    it('should return null when form is null', () => {
      const validator = createDateComparisonValidator(config);
      const mockControl = new FormControl(null);
      const result = validator(mockControl);
      expect(result).toBeNull();
    });
  });
});