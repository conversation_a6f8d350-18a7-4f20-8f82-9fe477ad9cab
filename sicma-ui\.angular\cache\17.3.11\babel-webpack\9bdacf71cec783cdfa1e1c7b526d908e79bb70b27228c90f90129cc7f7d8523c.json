{"ast": null, "code": "function cov_1i2s5zln2m() {\n  var path = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-dashboard\\\\components\\\\monthly-reports-tab\\\\monthly-report-dialog\\\\monthly-report-social-security-information\\\\monthly-report-social-security-information.component.ts\";\n  var hash = \"28bf8d402a8a2bc8791119e5d45e28ed0484cb0d\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-dashboard\\\\components\\\\monthly-reports-tab\\\\monthly-report-dialog\\\\monthly-report-social-security-information\\\\monthly-report-social-security-information.component.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 24,\n          column: 54\n        },\n        end: {\n          line: 337,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 26,\n          column: 8\n        },\n        end: {\n          line: 26,\n          column: 21\n        }\n      },\n      \"2\": {\n        start: {\n          line: 27,\n          column: 8\n        },\n        end: {\n          line: 27,\n          column: 47\n        }\n      },\n      \"3\": {\n        start: {\n          line: 28,\n          column: 8\n        },\n        end: {\n          line: 28,\n          column: 83\n        }\n      },\n      \"4\": {\n        start: {\n          line: 29,\n          column: 8\n        },\n        end: {\n          line: 29,\n          column: 27\n        }\n      },\n      \"5\": {\n        start: {\n          line: 30,\n          column: 8\n        },\n        end: {\n          line: 30,\n          column: 69\n        }\n      },\n      \"6\": {\n        start: {\n          line: 31,\n          column: 8\n        },\n        end: {\n          line: 31,\n          column: 63\n        }\n      },\n      \"7\": {\n        start: {\n          line: 32,\n          column: 8\n        },\n        end: {\n          line: 32,\n          column: 83\n        }\n      },\n      \"8\": {\n        start: {\n          line: 33,\n          column: 8\n        },\n        end: {\n          line: 33,\n          column: 43\n        }\n      },\n      \"9\": {\n        start: {\n          line: 34,\n          column: 8\n        },\n        end: {\n          line: 34,\n          column: 57\n        }\n      },\n      \"10\": {\n        start: {\n          line: 35,\n          column: 8\n        },\n        end: {\n          line: 35,\n          column: 47\n        }\n      },\n      \"11\": {\n        start: {\n          line: 36,\n          column: 8\n        },\n        end: {\n          line: 36,\n          column: 47\n        }\n      },\n      \"12\": {\n        start: {\n          line: 37,\n          column: 8\n        },\n        end: {\n          line: 37,\n          column: 34\n        }\n      },\n      \"13\": {\n        start: {\n          line: 38,\n          column: 8\n        },\n        end: {\n          line: 38,\n          column: 53\n        }\n      },\n      \"14\": {\n        start: {\n          line: 39,\n          column: 8\n        },\n        end: {\n          line: 39,\n          column: 53\n        }\n      },\n      \"15\": {\n        start: {\n          line: 40,\n          column: 8\n        },\n        end: {\n          line: 56,\n          column: 11\n        }\n      },\n      \"16\": {\n        start: {\n          line: 57,\n          column: 8\n        },\n        end: {\n          line: 57,\n          column: 27\n        }\n      },\n      \"17\": {\n        start: {\n          line: 58,\n          column: 8\n        },\n        end: {\n          line: 58,\n          column: 29\n        }\n      },\n      \"18\": {\n        start: {\n          line: 59,\n          column: 8\n        },\n        end: {\n          line: 59,\n          column: 38\n        }\n      },\n      \"19\": {\n        start: {\n          line: 60,\n          column: 8\n        },\n        end: {\n          line: 60,\n          column: 21\n        }\n      },\n      \"20\": {\n        start: {\n          line: 61,\n          column: 8\n        },\n        end: {\n          line: 61,\n          column: 36\n        }\n      },\n      \"21\": {\n        start: {\n          line: 62,\n          column: 8\n        },\n        end: {\n          line: 62,\n          column: 37\n        }\n      },\n      \"22\": {\n        start: {\n          line: 63,\n          column: 8\n        },\n        end: {\n          line: 63,\n          column: 40\n        }\n      },\n      \"23\": {\n        start: {\n          line: 64,\n          column: 8\n        },\n        end: {\n          line: 64,\n          column: 36\n        }\n      },\n      \"24\": {\n        start: {\n          line: 65,\n          column: 8\n        },\n        end: {\n          line: 65,\n          column: 33\n        }\n      },\n      \"25\": {\n        start: {\n          line: 66,\n          column: 8\n        },\n        end: {\n          line: 68,\n          column: 9\n        }\n      },\n      \"26\": {\n        start: {\n          line: 67,\n          column: 12\n        },\n        end: {\n          line: 67,\n          column: 74\n        }\n      },\n      \"27\": {\n        start: {\n          line: 71,\n          column: 40\n        },\n        end: {\n          line: 71,\n          column: 87\n        }\n      },\n      \"28\": {\n        start: {\n          line: 72,\n          column: 52\n        },\n        end: {\n          line: 72,\n          column: 111\n        }\n      },\n      \"29\": {\n        start: {\n          line: 73,\n          column: 8\n        },\n        end: {\n          line: 85,\n          column: 9\n        }\n      },\n      \"30\": {\n        start: {\n          line: 74,\n          column: 12\n        },\n        end: {\n          line: 74,\n          column: 72\n        }\n      },\n      \"31\": {\n        start: {\n          line: 75,\n          column: 12\n        },\n        end: {\n          line: 78,\n          column: 15\n        }\n      },\n      \"32\": {\n        start: {\n          line: 81,\n          column: 12\n        },\n        end: {\n          line: 81,\n          column: 55\n        }\n      },\n      \"33\": {\n        start: {\n          line: 82,\n          column: 12\n        },\n        end: {\n          line: 82,\n          column: 67\n        }\n      },\n      \"34\": {\n        start: {\n          line: 83,\n          column: 12\n        },\n        end: {\n          line: 83,\n          column: 52\n        }\n      },\n      \"35\": {\n        start: {\n          line: 84,\n          column: 12\n        },\n        end: {\n          line: 84,\n          column: 64\n        }\n      },\n      \"36\": {\n        start: {\n          line: 86,\n          column: 8\n        },\n        end: {\n          line: 86,\n          column: 78\n        }\n      },\n      \"37\": {\n        start: {\n          line: 87,\n          column: 8\n        },\n        end: {\n          line: 89,\n          column: 11\n        }\n      },\n      \"38\": {\n        start: {\n          line: 90,\n          column: 8\n        },\n        end: {\n          line: 90,\n          column: 57\n        }\n      },\n      \"39\": {\n        start: {\n          line: 93,\n          column: 8\n        },\n        end: {\n          line: 93,\n          column: 30\n        }\n      },\n      \"40\": {\n        start: {\n          line: 94,\n          column: 8\n        },\n        end: {\n          line: 94,\n          column: 24\n        }\n      },\n      \"41\": {\n        start: {\n          line: 97,\n          column: 8\n        },\n        end: {\n          line: 99,\n          column: 9\n        }\n      },\n      \"42\": {\n        start: {\n          line: 98,\n          column: 12\n        },\n        end: {\n          line: 98,\n          column: 34\n        }\n      },\n      \"43\": {\n        start: {\n          line: 102,\n          column: 41\n        },\n        end: {\n          line: 104,\n          column: 35\n        }\n      },\n      \"44\": {\n        start: {\n          line: 105,\n          column: 8\n        },\n        end: {\n          line: 108,\n          column: 9\n        }\n      },\n      \"45\": {\n        start: {\n          line: 107,\n          column: 12\n        },\n        end: {\n          line: 107,\n          column: 48\n        }\n      },\n      \"46\": {\n        start: {\n          line: 109,\n          column: 8\n        },\n        end: {\n          line: 137,\n          column: 11\n        }\n      },\n      \"47\": {\n        start: {\n          line: 138,\n          column: 8\n        },\n        end: {\n          line: 138,\n          column: 34\n        }\n      },\n      \"48\": {\n        start: {\n          line: 141,\n          column: 8\n        },\n        end: {\n          line: 141,\n          column: 46\n        }\n      },\n      \"49\": {\n        start: {\n          line: 142,\n          column: 8\n        },\n        end: {\n          line: 142,\n          column: 41\n        }\n      },\n      \"50\": {\n        start: {\n          line: 143,\n          column: 8\n        },\n        end: {\n          line: 143,\n          column: 37\n        }\n      },\n      \"51\": {\n        start: {\n          line: 144,\n          column: 8\n        },\n        end: {\n          line: 144,\n          column: 46\n        }\n      },\n      \"52\": {\n        start: {\n          line: 145,\n          column: 8\n        },\n        end: {\n          line: 145,\n          column: 33\n        }\n      },\n      \"53\": {\n        start: {\n          line: 146,\n          column: 8\n        },\n        end: {\n          line: 148,\n          column: 11\n        }\n      },\n      \"54\": {\n        start: {\n          line: 147,\n          column: 12\n        },\n        end: {\n          line: 147,\n          column: 99\n        }\n      },\n      \"55\": {\n        start: {\n          line: 149,\n          column: 8\n        },\n        end: {\n          line: 155,\n          column: 11\n        }\n      },\n      \"56\": {\n        start: {\n          line: 152,\n          column: 12\n        },\n        end: {\n          line: 154,\n          column: 13\n        }\n      },\n      \"57\": {\n        start: {\n          line: 153,\n          column: 16\n        },\n        end: {\n          line: 153,\n          column: 63\n        }\n      },\n      \"58\": {\n        start: {\n          line: 158,\n          column: 8\n        },\n        end: {\n          line: 167,\n          column: 11\n        }\n      },\n      \"59\": {\n        start: {\n          line: 161,\n          column: 29\n        },\n        end: {\n          line: 161,\n          column: 67\n        }\n      },\n      \"60\": {\n        start: {\n          line: 163,\n          column: 16\n        },\n        end: {\n          line: 165,\n          column: 17\n        }\n      },\n      \"61\": {\n        start: {\n          line: 164,\n          column: 20\n        },\n        end: {\n          line: 164,\n          column: 104\n        }\n      },\n      \"62\": {\n        start: {\n          line: 170,\n          column: 8\n        },\n        end: {\n          line: 170,\n          column: 51\n        }\n      },\n      \"63\": {\n        start: {\n          line: 171,\n          column: 8\n        },\n        end: {\n          line: 171,\n          column: 58\n        }\n      },\n      \"64\": {\n        start: {\n          line: 172,\n          column: 8\n        },\n        end: {\n          line: 172,\n          column: 57\n        }\n      },\n      \"65\": {\n        start: {\n          line: 175,\n          column: 21\n        },\n        end: {\n          line: 175,\n          column: 44\n        }\n      },\n      \"66\": {\n        start: {\n          line: 176,\n          column: 8\n        },\n        end: {\n          line: 195,\n          column: 9\n        }\n      },\n      \"67\": {\n        start: {\n          line: 177,\n          column: 12\n        },\n        end: {\n          line: 192,\n          column: 13\n        }\n      },\n      \"68\": {\n        start: {\n          line: 178,\n          column: 16\n        },\n        end: {\n          line: 188,\n          column: 17\n        }\n      },\n      \"69\": {\n        start: {\n          line: 179,\n          column: 20\n        },\n        end: {\n          line: 179,\n          column: 45\n        }\n      },\n      \"70\": {\n        start: {\n          line: 180,\n          column: 20\n        },\n        end: {\n          line: 180,\n          column: 46\n        }\n      },\n      \"71\": {\n        start: {\n          line: 181,\n          column: 20\n        },\n        end: {\n          line: 181,\n          column: 82\n        }\n      },\n      \"72\": {\n        start: {\n          line: 182,\n          column: 20\n        },\n        end: {\n          line: 184,\n          column: 63\n        }\n      },\n      \"73\": {\n        start: {\n          line: 187,\n          column: 20\n        },\n        end: {\n          line: 187,\n          column: 71\n        }\n      },\n      \"74\": {\n        start: {\n          line: 191,\n          column: 16\n        },\n        end: {\n          line: 191,\n          column: 66\n        }\n      },\n      \"75\": {\n        start: {\n          line: 193,\n          column: 12\n        },\n        end: {\n          line: 193,\n          column: 85\n        }\n      },\n      \"76\": {\n        start: {\n          line: 194,\n          column: 12\n        },\n        end: {\n          line: 194,\n          column: 38\n        }\n      },\n      \"77\": {\n        start: {\n          line: 198,\n          column: 26\n        },\n        end: {\n          line: 201,\n          column: 77\n        }\n      },\n      \"78\": {\n        start: {\n          line: 202,\n          column: 8\n        },\n        end: {\n          line: 202,\n          column: 48\n        }\n      },\n      \"79\": {\n        start: {\n          line: 205,\n          column: 38\n        },\n        end: {\n          line: 210,\n          column: 9\n        }\n      },\n      \"80\": {\n        start: {\n          line: 211,\n          column: 8\n        },\n        end: {\n          line: 211,\n          column: 60\n        }\n      },\n      \"81\": {\n        start: {\n          line: 214,\n          column: 8\n        },\n        end: {\n          line: 219,\n          column: 11\n        }\n      },\n      \"82\": {\n        start: {\n          line: 215,\n          column: 32\n        },\n        end: {\n          line: 215,\n          column: 68\n        }\n      },\n      \"83\": {\n        start: {\n          line: 217,\n          column: 16\n        },\n        end: {\n          line: 217,\n          column: 104\n        }\n      },\n      \"84\": {\n        start: {\n          line: 222,\n          column: 8\n        },\n        end: {\n          line: 227,\n          column: 11\n        }\n      },\n      \"85\": {\n        start: {\n          line: 223,\n          column: 30\n        },\n        end: {\n          line: 223,\n          column: 60\n        }\n      },\n      \"86\": {\n        start: {\n          line: 225,\n          column: 16\n        },\n        end: {\n          line: 225,\n          column: 101\n        }\n      },\n      \"87\": {\n        start: {\n          line: 230,\n          column: 8\n        },\n        end: {\n          line: 244,\n          column: 9\n        }\n      },\n      \"88\": {\n        start: {\n          line: 231,\n          column: 12\n        },\n        end: {\n          line: 243,\n          column: 15\n        }\n      },\n      \"89\": {\n        start: {\n          line: 235,\n          column: 20\n        },\n        end: {\n          line: 235,\n          column: 69\n        }\n      },\n      \"90\": {\n        start: {\n          line: 236,\n          column: 20\n        },\n        end: {\n          line: 236,\n          column: 39\n        }\n      },\n      \"91\": {\n        start: {\n          line: 239,\n          column: 20\n        },\n        end: {\n          line: 241,\n          column: 21\n        }\n      },\n      \"92\": {\n        start: {\n          line: 240,\n          column: 24\n        },\n        end: {\n          line: 240,\n          column: 118\n        }\n      },\n      \"93\": {\n        start: {\n          line: 247,\n          column: 8\n        },\n        end: {\n          line: 271,\n          column: 9\n        }\n      },\n      \"94\": {\n        start: {\n          line: 248,\n          column: 40\n        },\n        end: {\n          line: 248,\n          column: 90\n        }\n      },\n      \"95\": {\n        start: {\n          line: 249,\n          column: 12\n        },\n        end: {\n          line: 257,\n          column: 13\n        }\n      },\n      \"96\": {\n        start: {\n          line: 250,\n          column: 48\n        },\n        end: {\n          line: 250,\n          column: 95\n        }\n      },\n      \"97\": {\n        start: {\n          line: 251,\n          column: 60\n        },\n        end: {\n          line: 251,\n          column: 119\n        }\n      },\n      \"98\": {\n        start: {\n          line: 252,\n          column: 16\n        },\n        end: {\n          line: 252,\n          column: 76\n        }\n      },\n      \"99\": {\n        start: {\n          line: 253,\n          column: 16\n        },\n        end: {\n          line: 256,\n          column: 19\n        }\n      },\n      \"100\": {\n        start: {\n          line: 258,\n          column: 12\n        },\n        end: {\n          line: 265,\n          column: 37\n        }\n      },\n      \"101\": {\n        start: {\n          line: 266,\n          column: 12\n        },\n        end: {\n          line: 269,\n          column: 15\n        }\n      },\n      \"102\": {\n        start: {\n          line: 267,\n          column: 32\n        },\n        end: {\n          line: 267,\n          column: 64\n        }\n      },\n      \"103\": {\n        start: {\n          line: 268,\n          column: 16\n        },\n        end: {\n          line: 268,\n          column: 70\n        }\n      },\n      \"104\": {\n        start: {\n          line: 270,\n          column: 12\n        },\n        end: {\n          line: 270,\n          column: 61\n        }\n      },\n      \"105\": {\n        start: {\n          line: 274,\n          column: 26\n        },\n        end: {\n          line: 274,\n          column: 55\n        }\n      },\n      \"106\": {\n        start: {\n          line: 275,\n          column: 8\n        },\n        end: {\n          line: 290,\n          column: 10\n        }\n      },\n      \"107\": {\n        start: {\n          line: 293,\n          column: 8\n        },\n        end: {\n          line: 303,\n          column: 9\n        }\n      },\n      \"108\": {\n        start: {\n          line: 294,\n          column: 12\n        },\n        end: {\n          line: 302,\n          column: 15\n        }\n      },\n      \"109\": {\n        start: {\n          line: 296,\n          column: 20\n        },\n        end: {\n          line: 296,\n          column: 61\n        }\n      },\n      \"110\": {\n        start: {\n          line: 297,\n          column: 20\n        },\n        end: {\n          line: 297,\n          column: 50\n        }\n      },\n      \"111\": {\n        start: {\n          line: 300,\n          column: 20\n        },\n        end: {\n          line: 300,\n          column: 118\n        }\n      },\n      \"112\": {\n        start: {\n          line: 306,\n          column: 8\n        },\n        end: {\n          line: 308,\n          column: 9\n        }\n      },\n      \"113\": {\n        start: {\n          line: 307,\n          column: 12\n        },\n        end: {\n          line: 307,\n          column: 86\n        }\n      },\n      \"114\": {\n        start: {\n          line: 311,\n          column: 8\n        },\n        end: {\n          line: 312,\n          column: 16\n        }\n      },\n      \"115\": {\n        start: {\n          line: 311,\n          column: 62\n        },\n        end: {\n          line: 311,\n          column: 80\n        }\n      },\n      \"116\": {\n        start: {\n          line: 315,\n          column: 8\n        },\n        end: {\n          line: 315,\n          column: 81\n        }\n      },\n      \"117\": {\n        start: {\n          line: 315,\n          column: 53\n        },\n        end: {\n          line: 315,\n          column: 67\n        }\n      },\n      \"118\": {\n        start: {\n          line: 317,\n          column: 13\n        },\n        end: {\n          line: 327,\n          column: 6\n        }\n      },\n      \"119\": {\n        start: {\n          line: 317,\n          column: 41\n        },\n        end: {\n          line: 327,\n          column: 5\n        }\n      },\n      \"120\": {\n        start: {\n          line: 328,\n          column: 13\n        },\n        end: {\n          line: 336,\n          column: 6\n        }\n      },\n      \"121\": {\n        start: {\n          line: 338,\n          column: 0\n        },\n        end: {\n          line: 361,\n          column: 52\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 25,\n            column: 4\n          },\n          end: {\n            line: 25,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 25,\n            column: 204\n          },\n          end: {\n            line: 69,\n            column: 5\n          }\n        },\n        line: 25\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 70,\n            column: 4\n          },\n          end: {\n            line: 70,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 70,\n            column: 46\n          },\n          end: {\n            line: 91,\n            column: 5\n          }\n        },\n        line: 70\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 92,\n            column: 4\n          },\n          end: {\n            line: 92,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 92,\n            column: 15\n          },\n          end: {\n            line: 95,\n            column: 5\n          }\n        },\n        line: 92\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 96,\n            column: 4\n          },\n          end: {\n            line: 96,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 96,\n            column: 25\n          },\n          end: {\n            line: 100,\n            column: 5\n          }\n        },\n        line: 96\n      },\n      \"4\": {\n        name: \"(anonymous_4)\",\n        decl: {\n          start: {\n            line: 101,\n            column: 4\n          },\n          end: {\n            line: 101,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 101,\n            column: 21\n          },\n          end: {\n            line: 139,\n            column: 5\n          }\n        },\n        line: 101\n      },\n      \"5\": {\n        name: \"(anonymous_5)\",\n        decl: {\n          start: {\n            line: 140,\n            column: 4\n          },\n          end: {\n            line: 140,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 140,\n            column: 15\n          },\n          end: {\n            line: 156,\n            column: 5\n          }\n        },\n        line: 140\n      },\n      \"6\": {\n        name: \"(anonymous_6)\",\n        decl: {\n          start: {\n            line: 146,\n            column: 56\n          },\n          end: {\n            line: 146,\n            column: 57\n          }\n        },\n        loc: {\n          start: {\n            line: 146,\n            column: 62\n          },\n          end: {\n            line: 148,\n            column: 9\n          }\n        },\n        line: 146\n      },\n      \"7\": {\n        name: \"(anonymous_7)\",\n        decl: {\n          start: {\n            line: 151,\n            column: 37\n          },\n          end: {\n            line: 151,\n            column: 38\n          }\n        },\n        loc: {\n          start: {\n            line: 151,\n            column: 50\n          },\n          end: {\n            line: 155,\n            column: 9\n          }\n        },\n        line: 151\n      },\n      \"8\": {\n        name: \"(anonymous_8)\",\n        decl: {\n          start: {\n            line: 157,\n            column: 4\n          },\n          end: {\n            line: 157,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 157,\n            column: 37\n          },\n          end: {\n            line: 168,\n            column: 5\n          }\n        },\n        line: 157\n      },\n      \"9\": {\n        name: \"(anonymous_9)\",\n        decl: {\n          start: {\n            line: 161,\n            column: 18\n          },\n          end: {\n            line: 161,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 161,\n            column: 29\n          },\n          end: {\n            line: 161,\n            column: 67\n          }\n        },\n        line: 161\n      },\n      \"10\": {\n        name: \"(anonymous_10)\",\n        decl: {\n          start: {\n            line: 162,\n            column: 19\n          },\n          end: {\n            line: 162,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 162,\n            column: 30\n          },\n          end: {\n            line: 166,\n            column: 13\n          }\n        },\n        line: 162\n      },\n      \"11\": {\n        name: \"(anonymous_11)\",\n        decl: {\n          start: {\n            line: 169,\n            column: 4\n          },\n          end: {\n            line: 169,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 169,\n            column: 29\n          },\n          end: {\n            line: 173,\n            column: 5\n          }\n        },\n        line: 169\n      },\n      \"12\": {\n        name: \"(anonymous_12)\",\n        decl: {\n          start: {\n            line: 174,\n            column: 4\n          },\n          end: {\n            line: 174,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 174,\n            column: 26\n          },\n          end: {\n            line: 196,\n            column: 5\n          }\n        },\n        line: 174\n      },\n      \"13\": {\n        name: \"(anonymous_13)\",\n        decl: {\n          start: {\n            line: 197,\n            column: 4\n          },\n          end: {\n            line: 197,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 197,\n            column: 25\n          },\n          end: {\n            line: 203,\n            column: 5\n          }\n        },\n        line: 197\n      },\n      \"14\": {\n        name: \"(anonymous_14)\",\n        decl: {\n          start: {\n            line: 204,\n            column: 4\n          },\n          end: {\n            line: 204,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 204,\n            column: 13\n          },\n          end: {\n            line: 212,\n            column: 5\n          }\n        },\n        line: 204\n      },\n      \"15\": {\n        name: \"(anonymous_15)\",\n        decl: {\n          start: {\n            line: 213,\n            column: 4\n          },\n          end: {\n            line: 213,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 213,\n            column: 32\n          },\n          end: {\n            line: 220,\n            column: 5\n          }\n        },\n        line: 213\n      },\n      \"16\": {\n        name: \"(anonymous_16)\",\n        decl: {\n          start: {\n            line: 215,\n            column: 18\n          },\n          end: {\n            line: 215,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 215,\n            column: 32\n          },\n          end: {\n            line: 215,\n            column: 68\n          }\n        },\n        line: 215\n      },\n      \"17\": {\n        name: \"(anonymous_17)\",\n        decl: {\n          start: {\n            line: 216,\n            column: 19\n          },\n          end: {\n            line: 216,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 216,\n            column: 30\n          },\n          end: {\n            line: 218,\n            column: 13\n          }\n        },\n        line: 216\n      },\n      \"18\": {\n        name: \"(anonymous_18)\",\n        decl: {\n          start: {\n            line: 221,\n            column: 4\n          },\n          end: {\n            line: 221,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 221,\n            column: 28\n          },\n          end: {\n            line: 228,\n            column: 5\n          }\n        },\n        line: 221\n      },\n      \"19\": {\n        name: \"(anonymous_19)\",\n        decl: {\n          start: {\n            line: 223,\n            column: 18\n          },\n          end: {\n            line: 223,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 223,\n            column: 30\n          },\n          end: {\n            line: 223,\n            column: 60\n          }\n        },\n        line: 223\n      },\n      \"20\": {\n        name: \"(anonymous_20)\",\n        decl: {\n          start: {\n            line: 224,\n            column: 19\n          },\n          end: {\n            line: 224,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 224,\n            column: 30\n          },\n          end: {\n            line: 226,\n            column: 13\n          }\n        },\n        line: 224\n      },\n      \"21\": {\n        name: \"(anonymous_21)\",\n        decl: {\n          start: {\n            line: 229,\n            column: 4\n          },\n          end: {\n            line: 229,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 229,\n            column: 37\n          },\n          end: {\n            line: 245,\n            column: 5\n          }\n        },\n        line: 229\n      },\n      \"22\": {\n        name: \"(anonymous_22)\",\n        decl: {\n          start: {\n            line: 234,\n            column: 22\n          },\n          end: {\n            line: 234,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 234,\n            column: 42\n          },\n          end: {\n            line: 237,\n            column: 17\n          }\n        },\n        line: 234\n      },\n      \"23\": {\n        name: \"(anonymous_23)\",\n        decl: {\n          start: {\n            line: 238,\n            column: 23\n          },\n          end: {\n            line: 238,\n            column: 24\n          }\n        },\n        loc: {\n          start: {\n            line: 238,\n            column: 34\n          },\n          end: {\n            line: 242,\n            column: 17\n          }\n        },\n        line: 238\n      },\n      \"24\": {\n        name: \"(anonymous_24)\",\n        decl: {\n          start: {\n            line: 246,\n            column: 4\n          },\n          end: {\n            line: 246,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 246,\n            column: 18\n          },\n          end: {\n            line: 272,\n            column: 5\n          }\n        },\n        line: 246\n      },\n      \"25\": {\n        name: \"(anonymous_25)\",\n        decl: {\n          start: {\n            line: 266,\n            column: 66\n          },\n          end: {\n            line: 266,\n            column: 67\n          }\n        },\n        loc: {\n          start: {\n            line: 266,\n            column: 75\n          },\n          end: {\n            line: 269,\n            column: 13\n          }\n        },\n        line: 266\n      },\n      \"26\": {\n        name: \"(anonymous_26)\",\n        decl: {\n          start: {\n            line: 273,\n            column: 4\n          },\n          end: {\n            line: 273,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 273,\n            column: 28\n          },\n          end: {\n            line: 291,\n            column: 5\n          }\n        },\n        line: 273\n      },\n      \"27\": {\n        name: \"(anonymous_27)\",\n        decl: {\n          start: {\n            line: 292,\n            column: 4\n          },\n          end: {\n            line: 292,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 292,\n            column: 24\n          },\n          end: {\n            line: 304,\n            column: 5\n          }\n        },\n        line: 292\n      },\n      \"28\": {\n        name: \"(anonymous_28)\",\n        decl: {\n          start: {\n            line: 295,\n            column: 22\n          },\n          end: {\n            line: 295,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 295,\n            column: 35\n          },\n          end: {\n            line: 298,\n            column: 17\n          }\n        },\n        line: 295\n      },\n      \"29\": {\n        name: \"(anonymous_29)\",\n        decl: {\n          start: {\n            line: 299,\n            column: 23\n          },\n          end: {\n            line: 299,\n            column: 24\n          }\n        },\n        loc: {\n          start: {\n            line: 299,\n            column: 34\n          },\n          end: {\n            line: 301,\n            column: 17\n          }\n        },\n        line: 299\n      },\n      \"30\": {\n        name: \"(anonymous_30)\",\n        decl: {\n          start: {\n            line: 305,\n            column: 4\n          },\n          end: {\n            line: 305,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 305,\n            column: 26\n          },\n          end: {\n            line: 309,\n            column: 5\n          }\n        },\n        line: 305\n      },\n      \"31\": {\n        name: \"(anonymous_31)\",\n        decl: {\n          start: {\n            line: 310,\n            column: 4\n          },\n          end: {\n            line: 310,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 310,\n            column: 35\n          },\n          end: {\n            line: 313,\n            column: 5\n          }\n        },\n        line: 310\n      },\n      \"32\": {\n        name: \"(anonymous_32)\",\n        decl: {\n          start: {\n            line: 311,\n            column: 48\n          },\n          end: {\n            line: 311,\n            column: 49\n          }\n        },\n        loc: {\n          start: {\n            line: 311,\n            column: 62\n          },\n          end: {\n            line: 311,\n            column: 80\n          }\n        },\n        line: 311\n      },\n      \"33\": {\n        name: \"(anonymous_33)\",\n        decl: {\n          start: {\n            line: 314,\n            column: 4\n          },\n          end: {\n            line: 314,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 314,\n            column: 32\n          },\n          end: {\n            line: 316,\n            column: 5\n          }\n        },\n        line: 314\n      },\n      \"34\": {\n        name: \"(anonymous_34)\",\n        decl: {\n          start: {\n            line: 315,\n            column: 43\n          },\n          end: {\n            line: 315,\n            column: 44\n          }\n        },\n        loc: {\n          start: {\n            line: 315,\n            column: 53\n          },\n          end: {\n            line: 315,\n            column: 67\n          }\n        },\n        line: 315\n      },\n      \"35\": {\n        name: \"(anonymous_35)\",\n        decl: {\n          start: {\n            line: 317,\n            column: 35\n          },\n          end: {\n            line: 317,\n            column: 36\n          }\n        },\n        loc: {\n          start: {\n            line: 317,\n            column: 41\n          },\n          end: {\n            line: 327,\n            column: 5\n          }\n        },\n        line: 317\n      }\n    },\n    branchMap: {\n      \"0\": {\n        loc: {\n          start: {\n            line: 43,\n            column: 16\n          },\n          end: {\n            line: 43,\n            column: 81\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 43,\n            column: 36\n          },\n          end: {\n            line: 43,\n            column: 38\n          }\n        }, {\n          start: {\n            line: 43,\n            column: 41\n          },\n          end: {\n            line: 43,\n            column: 81\n          }\n        }],\n        line: 43\n      },\n      \"1\": {\n        loc: {\n          start: {\n            line: 45,\n            column: 36\n          },\n          end: {\n            line: 45,\n            column: 82\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 45,\n            column: 56\n          },\n          end: {\n            line: 45,\n            column: 58\n          }\n        }, {\n          start: {\n            line: 45,\n            column: 61\n          },\n          end: {\n            line: 45,\n            column: 82\n          }\n        }],\n        line: 45\n      },\n      \"2\": {\n        loc: {\n          start: {\n            line: 46,\n            column: 40\n          },\n          end: {\n            line: 46,\n            column: 86\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 46,\n            column: 60\n          },\n          end: {\n            line: 46,\n            column: 62\n          }\n        }, {\n          start: {\n            line: 46,\n            column: 65\n          },\n          end: {\n            line: 46,\n            column: 86\n          }\n        }],\n        line: 46\n      },\n      \"3\": {\n        loc: {\n          start: {\n            line: 49,\n            column: 16\n          },\n          end: {\n            line: 49,\n            column: 81\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 49,\n            column: 36\n          },\n          end: {\n            line: 49,\n            column: 38\n          }\n        }, {\n          start: {\n            line: 49,\n            column: 41\n          },\n          end: {\n            line: 49,\n            column: 81\n          }\n        }],\n        line: 49\n      },\n      \"4\": {\n        loc: {\n          start: {\n            line: 66,\n            column: 8\n          },\n          end: {\n            line: 68,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 66,\n            column: 8\n          },\n          end: {\n            line: 68,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 66\n      },\n      \"5\": {\n        loc: {\n          start: {\n            line: 73,\n            column: 8\n          },\n          end: {\n            line: 85,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 73,\n            column: 8\n          },\n          end: {\n            line: 85,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 80,\n            column: 13\n          },\n          end: {\n            line: 85,\n            column: 9\n          }\n        }],\n        line: 73\n      },\n      \"6\": {\n        loc: {\n          start: {\n            line: 97,\n            column: 8\n          },\n          end: {\n            line: 99,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 97,\n            column: 8\n          },\n          end: {\n            line: 99,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 97\n      },\n      \"7\": {\n        loc: {\n          start: {\n            line: 102,\n            column: 41\n          },\n          end: {\n            line: 104,\n            column: 35\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 103,\n            column: 14\n          },\n          end: {\n            line: 103,\n            column: 16\n          }\n        }, {\n          start: {\n            line: 104,\n            column: 14\n          },\n          end: {\n            line: 104,\n            column: 35\n          }\n        }],\n        line: 102\n      },\n      \"8\": {\n        loc: {\n          start: {\n            line: 105,\n            column: 8\n          },\n          end: {\n            line: 108,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 105,\n            column: 8\n          },\n          end: {\n            line: 108,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 105\n      },\n      \"9\": {\n        loc: {\n          start: {\n            line: 105,\n            column: 12\n          },\n          end: {\n            line: 106,\n            column: 63\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 105,\n            column: 12\n          },\n          end: {\n            line: 105,\n            column: 30\n          }\n        }, {\n          start: {\n            line: 106,\n            column: 12\n          },\n          end: {\n            line: 106,\n            column: 63\n          }\n        }],\n        line: 105\n      },\n      \"10\": {\n        loc: {\n          start: {\n            line: 111,\n            column: 16\n          },\n          end: {\n            line: 111,\n            column: 74\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 111,\n            column: 16\n          },\n          end: {\n            line: 111,\n            column: 66\n          }\n        }, {\n          start: {\n            line: 111,\n            column: 70\n          },\n          end: {\n            line: 111,\n            column: 74\n          }\n        }],\n        line: 111\n      },\n      \"11\": {\n        loc: {\n          start: {\n            line: 112,\n            column: 16\n          },\n          end: {\n            line: 112,\n            column: 81\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 112,\n            column: 36\n          },\n          end: {\n            line: 112,\n            column: 38\n          }\n        }, {\n          start: {\n            line: 112,\n            column: 41\n          },\n          end: {\n            line: 112,\n            column: 81\n          }\n        }],\n        line: 112\n      },\n      \"12\": {\n        loc: {\n          start: {\n            line: 116,\n            column: 16\n          },\n          end: {\n            line: 116,\n            column: 78\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 116,\n            column: 16\n          },\n          end: {\n            line: 116,\n            column: 70\n          }\n        }, {\n          start: {\n            line: 116,\n            column: 74\n          },\n          end: {\n            line: 116,\n            column: 78\n          }\n        }],\n        line: 116\n      },\n      \"13\": {\n        loc: {\n          start: {\n            line: 117,\n            column: 16\n          },\n          end: {\n            line: 117,\n            column: 62\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 117,\n            column: 36\n          },\n          end: {\n            line: 117,\n            column: 38\n          }\n        }, {\n          start: {\n            line: 117,\n            column: 41\n          },\n          end: {\n            line: 117,\n            column: 62\n          }\n        }],\n        line: 117\n      },\n      \"14\": {\n        loc: {\n          start: {\n            line: 120,\n            column: 16\n          },\n          end: {\n            line: 120,\n            column: 72\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 120,\n            column: 16\n          },\n          end: {\n            line: 120,\n            column: 64\n          }\n        }, {\n          start: {\n            line: 120,\n            column: 68\n          },\n          end: {\n            line: 120,\n            column: 72\n          }\n        }],\n        line: 120\n      },\n      \"15\": {\n        loc: {\n          start: {\n            line: 121,\n            column: 16\n          },\n          end: {\n            line: 121,\n            column: 81\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 121,\n            column: 36\n          },\n          end: {\n            line: 121,\n            column: 38\n          }\n        }, {\n          start: {\n            line: 121,\n            column: 41\n          },\n          end: {\n            line: 121,\n            column: 81\n          }\n        }],\n        line: 121\n      },\n      \"16\": {\n        loc: {\n          start: {\n            line: 130,\n            column: 16\n          },\n          end: {\n            line: 130,\n            column: 75\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 130,\n            column: 16\n          },\n          end: {\n            line: 130,\n            column: 67\n          }\n        }, {\n          start: {\n            line: 130,\n            column: 71\n          },\n          end: {\n            line: 130,\n            column: 75\n          }\n        }],\n        line: 130\n      },\n      \"17\": {\n        loc: {\n          start: {\n            line: 134,\n            column: 16\n          },\n          end: {\n            line: 134,\n            column: 85\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 134,\n            column: 16\n          },\n          end: {\n            line: 134,\n            column: 77\n          }\n        }, {\n          start: {\n            line: 134,\n            column: 81\n          },\n          end: {\n            line: 134,\n            column: 85\n          }\n        }],\n        line: 134\n      },\n      \"18\": {\n        loc: {\n          start: {\n            line: 147,\n            column: 41\n          },\n          end: {\n            line: 147,\n            column: 97\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 147,\n            column: 61\n          },\n          end: {\n            line: 147,\n            column: 65\n          }\n        }, {\n          start: {\n            line: 147,\n            column: 68\n          },\n          end: {\n            line: 147,\n            column: 97\n          }\n        }],\n        line: 147\n      },\n      \"19\": {\n        loc: {\n          start: {\n            line: 152,\n            column: 12\n          },\n          end: {\n            line: 154,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 152,\n            column: 12\n          },\n          end: {\n            line: 154,\n            column: 13\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 152\n      },\n      \"20\": {\n        loc: {\n          start: {\n            line: 163,\n            column: 16\n          },\n          end: {\n            line: 165,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 163,\n            column: 16\n          },\n          end: {\n            line: 165,\n            column: 17\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 163\n      },\n      \"21\": {\n        loc: {\n          start: {\n            line: 164,\n            column: 37\n          },\n          end: {\n            line: 164,\n            column: 102\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 164,\n            column: 37\n          },\n          end: {\n            line: 164,\n            column: 56\n          }\n        }, {\n          start: {\n            line: 164,\n            column: 60\n          },\n          end: {\n            line: 164,\n            column: 102\n          }\n        }],\n        line: 164\n      },\n      \"22\": {\n        loc: {\n          start: {\n            line: 176,\n            column: 8\n          },\n          end: {\n            line: 195,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 176,\n            column: 8\n          },\n          end: {\n            line: 195,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 176\n      },\n      \"23\": {\n        loc: {\n          start: {\n            line: 177,\n            column: 12\n          },\n          end: {\n            line: 192,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 177,\n            column: 12\n          },\n          end: {\n            line: 192,\n            column: 13\n          }\n        }, {\n          start: {\n            line: 190,\n            column: 17\n          },\n          end: {\n            line: 192,\n            column: 13\n          }\n        }],\n        line: 177\n      },\n      \"24\": {\n        loc: {\n          start: {\n            line: 178,\n            column: 16\n          },\n          end: {\n            line: 188,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 178,\n            column: 16\n          },\n          end: {\n            line: 188,\n            column: 17\n          }\n        }, {\n          start: {\n            line: 186,\n            column: 21\n          },\n          end: {\n            line: 188,\n            column: 17\n          }\n        }],\n        line: 178\n      },\n      \"25\": {\n        loc: {\n          start: {\n            line: 198,\n            column: 26\n          },\n          end: {\n            line: 201,\n            column: 77\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 198,\n            column: 26\n          },\n          end: {\n            line: 198,\n            column: 43\n          }\n        }, {\n          start: {\n            line: 199,\n            column: 12\n          },\n          end: {\n            line: 199,\n            column: 41\n          }\n        }, {\n          start: {\n            line: 200,\n            column: 13\n          },\n          end: {\n            line: 200,\n            column: 75\n          }\n        }, {\n          start: {\n            line: 201,\n            column: 16\n          },\n          end: {\n            line: 201,\n            column: 76\n          }\n        }],\n        line: 198\n      },\n      \"26\": {\n        loc: {\n          start: {\n            line: 217,\n            column: 33\n          },\n          end: {\n            line: 217,\n            column: 102\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 217,\n            column: 33\n          },\n          end: {\n            line: 217,\n            column: 52\n          }\n        }, {\n          start: {\n            line: 217,\n            column: 56\n          },\n          end: {\n            line: 217,\n            column: 102\n          }\n        }],\n        line: 217\n      },\n      \"27\": {\n        loc: {\n          start: {\n            line: 225,\n            column: 33\n          },\n          end: {\n            line: 225,\n            column: 99\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 225,\n            column: 33\n          },\n          end: {\n            line: 225,\n            column: 52\n          }\n        }, {\n          start: {\n            line: 225,\n            column: 56\n          },\n          end: {\n            line: 225,\n            column: 99\n          }\n        }],\n        line: 225\n      },\n      \"28\": {\n        loc: {\n          start: {\n            line: 230,\n            column: 8\n          },\n          end: {\n            line: 244,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 230,\n            column: 8\n          },\n          end: {\n            line: 244,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 230\n      },\n      \"29\": {\n        loc: {\n          start: {\n            line: 230,\n            column: 12\n          },\n          end: {\n            line: 230,\n            column: 41\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 230,\n            column: 12\n          },\n          end: {\n            line: 230,\n            column: 23\n          }\n        }, {\n          start: {\n            line: 230,\n            column: 27\n          },\n          end: {\n            line: 230,\n            column: 41\n          }\n        }],\n        line: 230\n      },\n      \"30\": {\n        loc: {\n          start: {\n            line: 239,\n            column: 20\n          },\n          end: {\n            line: 241,\n            column: 21\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 239,\n            column: 20\n          },\n          end: {\n            line: 241,\n            column: 21\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 239\n      },\n      \"31\": {\n        loc: {\n          start: {\n            line: 240,\n            column: 41\n          },\n          end: {\n            line: 240,\n            column: 116\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 240,\n            column: 41\n          },\n          end: {\n            line: 240,\n            column: 60\n          }\n        }, {\n          start: {\n            line: 240,\n            column: 64\n          },\n          end: {\n            line: 240,\n            column: 116\n          }\n        }],\n        line: 240\n      },\n      \"32\": {\n        loc: {\n          start: {\n            line: 247,\n            column: 8\n          },\n          end: {\n            line: 271,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 247,\n            column: 8\n          },\n          end: {\n            line: 271,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 247\n      },\n      \"33\": {\n        loc: {\n          start: {\n            line: 249,\n            column: 12\n          },\n          end: {\n            line: 257,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 249,\n            column: 12\n          },\n          end: {\n            line: 257,\n            column: 13\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 249\n      },\n      \"34\": {\n        loc: {\n          start: {\n            line: 278,\n            column: 29\n          },\n          end: {\n            line: 278,\n            column: 48\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 278,\n            column: 29\n          },\n          end: {\n            line: 278,\n            column: 43\n          }\n        }, {\n          start: {\n            line: 278,\n            column: 47\n          },\n          end: {\n            line: 278,\n            column: 48\n          }\n        }],\n        line: 278\n      },\n      \"35\": {\n        loc: {\n          start: {\n            line: 281,\n            column: 29\n          },\n          end: {\n            line: 281,\n            column: 59\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 281,\n            column: 29\n          },\n          end: {\n            line: 281,\n            column: 54\n          }\n        }, {\n          start: {\n            line: 281,\n            column: 58\n          },\n          end: {\n            line: 281,\n            column: 59\n          }\n        }],\n        line: 281\n      },\n      \"36\": {\n        loc: {\n          start: {\n            line: 284,\n            column: 32\n          },\n          end: {\n            line: 286,\n            column: 22\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 285,\n            column: 18\n          },\n          end: {\n            line: 285,\n            column: 44\n          }\n        }, {\n          start: {\n            line: 286,\n            column: 18\n          },\n          end: {\n            line: 286,\n            column: 22\n          }\n        }],\n        line: 284\n      },\n      \"37\": {\n        loc: {\n          start: {\n            line: 287,\n            column: 42\n          },\n          end: {\n            line: 289,\n            column: 22\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 288,\n            column: 18\n          },\n          end: {\n            line: 288,\n            column: 56\n          }\n        }, {\n          start: {\n            line: 289,\n            column: 18\n          },\n          end: {\n            line: 289,\n            column: 22\n          }\n        }],\n        line: 287\n      },\n      \"38\": {\n        loc: {\n          start: {\n            line: 293,\n            column: 8\n          },\n          end: {\n            line: 303,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 293,\n            column: 8\n          },\n          end: {\n            line: 303,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 293\n      },\n      \"39\": {\n        loc: {\n          start: {\n            line: 293,\n            column: 12\n          },\n          end: {\n            line: 293,\n            column: 52\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 293,\n            column: 12\n          },\n          end: {\n            line: 293,\n            column: 23\n          }\n        }, {\n          start: {\n            line: 293,\n            column: 27\n          },\n          end: {\n            line: 293,\n            column: 52\n          }\n        }],\n        line: 293\n      },\n      \"40\": {\n        loc: {\n          start: {\n            line: 296,\n            column: 48\n          },\n          end: {\n            line: 296,\n            column: 60\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 296,\n            column: 48\n          },\n          end: {\n            line: 296,\n            column: 55\n          }\n        }, {\n          start: {\n            line: 296,\n            column: 59\n          },\n          end: {\n            line: 296,\n            column: 60\n          }\n        }],\n        line: 296\n      },\n      \"41\": {\n        loc: {\n          start: {\n            line: 300,\n            column: 37\n          },\n          end: {\n            line: 300,\n            column: 116\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 300,\n            column: 37\n          },\n          end: {\n            line: 300,\n            column: 56\n          }\n        }, {\n          start: {\n            line: 300,\n            column: 60\n          },\n          end: {\n            line: 300,\n            column: 116\n          }\n        }],\n        line: 300\n      },\n      \"42\": {\n        loc: {\n          start: {\n            line: 306,\n            column: 8\n          },\n          end: {\n            line: 308,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 306,\n            column: 8\n          },\n          end: {\n            line: 308,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 306\n      },\n      \"43\": {\n        loc: {\n          start: {\n            line: 311,\n            column: 16\n          },\n          end: {\n            line: 312,\n            column: 14\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 311,\n            column: 16\n          },\n          end: {\n            line: 311,\n            column: 87\n          }\n        }, {\n          start: {\n            line: 312,\n            column: 12\n          },\n          end: {\n            line: 312,\n            column: 14\n          }\n        }],\n        line: 311\n      },\n      \"44\": {\n        loc: {\n          start: {\n            line: 315,\n            column: 15\n          },\n          end: {\n            line: 315,\n            column: 80\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 315,\n            column: 15\n          },\n          end: {\n            line: 315,\n            column: 74\n          }\n        }, {\n          start: {\n            line: 315,\n            column: 78\n          },\n          end: {\n            line: 315,\n            column: 80\n          }\n        }],\n        line: 315\n      }\n    },\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0,\n      \"36\": 0,\n      \"37\": 0,\n      \"38\": 0,\n      \"39\": 0,\n      \"40\": 0,\n      \"41\": 0,\n      \"42\": 0,\n      \"43\": 0,\n      \"44\": 0,\n      \"45\": 0,\n      \"46\": 0,\n      \"47\": 0,\n      \"48\": 0,\n      \"49\": 0,\n      \"50\": 0,\n      \"51\": 0,\n      \"52\": 0,\n      \"53\": 0,\n      \"54\": 0,\n      \"55\": 0,\n      \"56\": 0,\n      \"57\": 0,\n      \"58\": 0,\n      \"59\": 0,\n      \"60\": 0,\n      \"61\": 0,\n      \"62\": 0,\n      \"63\": 0,\n      \"64\": 0,\n      \"65\": 0,\n      \"66\": 0,\n      \"67\": 0,\n      \"68\": 0,\n      \"69\": 0,\n      \"70\": 0,\n      \"71\": 0,\n      \"72\": 0,\n      \"73\": 0,\n      \"74\": 0,\n      \"75\": 0,\n      \"76\": 0,\n      \"77\": 0,\n      \"78\": 0,\n      \"79\": 0,\n      \"80\": 0,\n      \"81\": 0,\n      \"82\": 0,\n      \"83\": 0,\n      \"84\": 0,\n      \"85\": 0,\n      \"86\": 0,\n      \"87\": 0,\n      \"88\": 0,\n      \"89\": 0,\n      \"90\": 0,\n      \"91\": 0,\n      \"92\": 0,\n      \"93\": 0,\n      \"94\": 0,\n      \"95\": 0,\n      \"96\": 0,\n      \"97\": 0,\n      \"98\": 0,\n      \"99\": 0,\n      \"100\": 0,\n      \"101\": 0,\n      \"102\": 0,\n      \"103\": 0,\n      \"104\": 0,\n      \"105\": 0,\n      \"106\": 0,\n      \"107\": 0,\n      \"108\": 0,\n      \"109\": 0,\n      \"110\": 0,\n      \"111\": 0,\n      \"112\": 0,\n      \"113\": 0,\n      \"114\": 0,\n      \"115\": 0,\n      \"116\": 0,\n      \"117\": 0,\n      \"118\": 0,\n      \"119\": 0,\n      \"120\": 0,\n      \"121\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0\n    },\n    b: {\n      \"0\": [0, 0],\n      \"1\": [0, 0],\n      \"2\": [0, 0],\n      \"3\": [0, 0],\n      \"4\": [0, 0],\n      \"5\": [0, 0],\n      \"6\": [0, 0],\n      \"7\": [0, 0],\n      \"8\": [0, 0],\n      \"9\": [0, 0],\n      \"10\": [0, 0],\n      \"11\": [0, 0],\n      \"12\": [0, 0],\n      \"13\": [0, 0],\n      \"14\": [0, 0],\n      \"15\": [0, 0],\n      \"16\": [0, 0],\n      \"17\": [0, 0],\n      \"18\": [0, 0],\n      \"19\": [0, 0],\n      \"20\": [0, 0],\n      \"21\": [0, 0],\n      \"22\": [0, 0],\n      \"23\": [0, 0],\n      \"24\": [0, 0],\n      \"25\": [0, 0, 0, 0],\n      \"26\": [0, 0],\n      \"27\": [0, 0],\n      \"28\": [0, 0],\n      \"29\": [0, 0],\n      \"30\": [0, 0],\n      \"31\": [0, 0],\n      \"32\": [0, 0],\n      \"33\": [0, 0],\n      \"34\": [0, 0],\n      \"35\": [0, 0],\n      \"36\": [0, 0],\n      \"37\": [0, 0],\n      \"38\": [0, 0],\n      \"39\": [0, 0],\n      \"40\": [0, 0],\n      \"41\": [0, 0],\n      \"42\": [0, 0],\n      \"43\": [0, 0],\n      \"44\": [0, 0]\n    },\n    inputSourceMap: {\n      version: 3,\n      file: \"monthly-report-social-security-information.component.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-dashboard\\\\components\\\\monthly-reports-tab\\\\monthly-report-dialog\\\\monthly-report-social-security-information\\\\monthly-report-social-security-information.component.ts\"],\n      names: [],\n      mappings: \";;;AACA,OAAO,EAAE,eAAe,EAAE,MAAM,gDAAgD,CAAC;AAMjF,OAAO,EAAE,0BAA0B,EAAE,MAAM,8DAA8D,CAAC;AAC1G,OAAO,EAAE,uBAAuB,EAAE,MAAM,0DAA0D,CAAC;AACnG,OAAO,EAAE,iCAAiC,EAAE,MAAM,qEAAqE,CAAC;AACxH,OAAO,EAAE,aAAa,EAAE,MAAM,+CAA+C,CAAC;AAC9E,OAAO,EAAE,iCAAiC,EAAE,MAAM,qEAAqE,CAAC;AACxH,OAAO,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAE9D,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAC/C,OAAO,EACL,SAAS,EACT,YAAY,EACZ,KAAK,EAGL,MAAM,GAEP,MAAM,eAAe,CAAC;AACvB,OAAO,EACL,WAAW,EAEX,mBAAmB,EACnB,UAAU,GACX,MAAM,gBAAgB,CAAC;AACxB,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AACpE,OAAO,EAAE,WAAW,EAAE,MAAM,4BAA4B,CAAC;AACzD,OAAO,EAAE,SAAS,EAAE,MAAM,wBAAwB,CAAC;AACnD,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,8BAA8B,CAAC;AAChF,OAAO,EAAE,OAAO,EAAE,MAAM,wBAAwB,CAAC;AACjD,OAAO,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AACnD,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AACrD,OAAO,EAAE,UAAU,EAAE,MAAM,2BAA2B,CAAC;AACvD,OAAO,EAAE,oBAAoB,EAAE,MAAM,cAAc,CAAC;AAEpD,OAAO,EAAE,oBAAoB,EAAE,MAAM,uDAAuD,CAAC;AAwBtF,IAAM,+CAA+C,GAArD,MAAM,+CAA+C;IAsC1D,YACmB,EAAe,EACf,eAAgC,EAChC,iCAAoE,EACpE,KAAmB,EACnB,0BAAsD,EACtD,uBAAgD,EAChD,iCAAoE,EACpE,aAA4B,EAC5B,oBAA0C;QAR1C,OAAE,GAAF,EAAE,CAAa;QACf,oBAAe,GAAf,eAAe,CAAiB;QAChC,sCAAiC,GAAjC,iCAAiC,CAAmC;QACpE,UAAK,GAAL,KAAK,CAAc;QACnB,+BAA0B,GAA1B,0BAA0B,CAA4B;QACtD,4BAAuB,GAAvB,uBAAuB,CAAyB;QAChD,sCAAiC,GAAjC,iCAAiC,CAAmC;QACpE,kBAAa,GAAb,aAAa,CAAe;QAC5B,yBAAoB,GAApB,oBAAoB,CAAsB;QA5CpD,+BAA0B,GAAsC,IAAI;QAGpE,+BAA0B,GAAsC,IAAI;QACpE,iBAAY,GAAG,KAAK;QACnB,uBAAkB,GAAG,IAAI,YAAY,EAA8B;QACnE,uBAAkB,GAAG,IAAI,YAAY,EAAW;QAE1D,uBAAkB,GAAc,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;YAC5C,iBAAiB,EAAE;gBACjB,IAAI;gBACJ,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aAClE;YACD,eAAe,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YACvE,mBAAmB,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAC3E,eAAe,EAAE;gBACf,IAAI;gBACJ,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aAClE;YACD,mBAAmB,EAAE,CAAC,KAAK,CAAC;YAC5B,gBAAgB,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC;YAChE,4BAA4B,EAAE;gBAC5B,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAE;aAC7C;SACF,CAAC,CAAC;QACH,aAAQ,GAAG,EAAE,CAAC;QACd,aAAQ,GAAoB,IAAI,CAAC;QACjC,yBAAoB,GAAG,CAAC,CAAC;QACzB,QAAG,GAAG,CAAC,CAAC;QACR,uBAAkB,GAAG,CAAC,CAAC;QACvB,wBAAmB,GAAG,CAAC,CAAC;QACxB,0BAAqB,GAA0B,EAAE,CAAC;QAClD,sBAAiB,GAAuB,EAAE,CAAC;QAC3C,iBAAY,GAAgB,IAAI,CAAC;QAa/B,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,qBAAqB,CAAC,EAAE,OAAO,EAAE,CAAC;QAChE,CAAC;IACH,CAAC;IAEO,gCAAgC,CAAC,OAAgB;QACvD,MAAM,uBAAuB,GAC3B,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAClD,MAAM,mCAAmC,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CACrE,8BAA8B,CAC/B,CAAC;QAEF,IAAI,OAAO,EAAE,CAAC;YACZ,uBAAuB,EAAE,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAC5D,mCAAmC,EAAE,aAAa,CAAC;gBACjD,UAAU,CAAC,QAAQ;gBACnB,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;aAClB,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,uBAAuB,EAAE,eAAe,EAAE,CAAC;YAC3C,mCAAmC,EAAE,eAAe,EAAE,CAAC;YACvD,uBAAuB,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;YACxC,mCAAmC,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC;QAED,uBAAuB,EAAE,sBAAsB,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;QACtE,mCAAmC,EAAE,sBAAsB,CAAC;YAC1D,SAAS,EAAE,KAAK;SACjB,CAAC,CAAC;QACH,IAAI,CAAC,kBAAkB,CAAC,sBAAsB,EAAE,CAAC;IACnD,CAAC;IAED,QAAQ;QACN,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,QAAQ,EAAE,CAAC;IAClB,CAAC;IAED,WAAW,CAAC,OAAsB;QAChC,IAAI,OAAO,CAAC,4BAA4B,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,cAAc,EAAE,CAAC;QACxB,CAAC;IACH,CAAC;IAEO,cAAc;QACpB,MAAM,wBAAwB,GAAG,IAAI,CAAC,YAAY;YAChD,CAAC,CAAC,EAAE;YACJ,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAE1B,IACE,CAAC,IAAI,CAAC,YAAY;YAClB,IAAI,CAAC,0BAA0B,EAAE,kBAAkB,EACnD,CAAC;YACD,wBAAwB,CAAC,MAAM,GAAG,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;YACtC,iBAAiB,EAAE;gBACjB,IAAI,CAAC,0BAA0B,EAAE,iBAAiB,IAAI,IAAI;gBAC1D,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aAClE;YACD,eAAe,EAAE,CAAC,IAAI,EAAE,wBAAwB,CAAC;YACjD,mBAAmB,EAAE;gBACnB,IAAI,CAAC,0BAA0B,EAAE,qBAAqB,IAAI,IAAI;gBAC9D,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC;aAC/C;YACD,eAAe,EAAE;gBACf,IAAI,CAAC,0BAA0B,EAAE,eAAe,IAAI,IAAI;gBACxD,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aAClE;YACD,mBAAmB,EAAE;gBACnB;oBACE,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,0BAA0B,EAAE,kBAAkB,CAAC;oBACnE,QAAQ,EAAE,IAAI,CAAC,YAAY;iBAC5B;aACF;YACD,gBAAgB,EAAE;gBAChB,IAAI,CAAC,0BAA0B,EAAE,kBAAkB,IAAI,IAAI;gBAC3D,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAE;aAC7C;YACD,4BAA4B,EAAE;gBAC5B,IAAI,CAAC,0BAA0B,EAAE,4BAA4B,IAAI,IAAI;gBACrE,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAE;aAC7C;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED,QAAQ;QACN,IAAI,CAAC,8BAA8B,EAAE,CAAC;QACtC,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,8BAA8B,EAAE,CAAC;QACtC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,EAAE;YACnD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAC1B,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CACzD,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB;aACpB,GAAG,CAAC,qBAAqB,CAAC;YAC3B,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,EAAE;YACnC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;gBACvB,IAAI,CAAC,gCAAgC,CAAC,OAAO,CAAC,CAAC;YACjD,CAAC;QACH,CAAC,CAAC,CAAC;IACP,CAAC;IAED,8BAA8B;QAC5B,IAAI,CAAC,iCAAiC;aACnC,yBAAyB,CAAC,IAAI,CAAC,oBAAoB,CAAC;aACpD,SAAS,CAAC;YACT,IAAI,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;YACxD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,KAAK,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;oBACzB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,0CAA0C,CAAC,CAAC;gBACtF,CAAC;YACH,CAAC;SACF,CAAC,CAAC;IACP,CAAC;IAED,sBAAsB;QACpB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,oBAAoB,GAAG,GAAG,CAAC;QAC3C,IAAI,CAAC,kBAAkB,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QAClD,IAAI,CAAC,mBAAmB,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC;IACnD,CAAC;IAED,cAAc,CAAC,KAAY;QACzB,MAAM,IAAI,GAAI,KAAK,CAAC,MAA2B,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;QAC3D,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,IAAI,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;gBACpC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC;oBAC7B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;oBACzB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;oBAC1B,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC;oBAC9D,IAAI,CAAC,kBAAkB;yBACpB,GAAG,CAAC,iBAAiB,CAAC;wBACvB,EAAE,aAAa,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAC3C,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;gBACrD,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;YACpD,CAAC;YACD,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,sBAAsB,EAAE,CAAC;YACzE,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC5B,CAAC;IACH,CAAC;IAEO,kBAAkB;QACxB,MAAM,SAAS,GACb,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,kBAAkB,CAAC,KAAK;YAC7B,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,KAAK,KAAK,IAAI;gBAC7D,OAAO,CAAC,IAAI,CAAC,0BAA0B,EAAE,kBAAkB,CAAC,CAAC,CAAC;QAElE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC1C,CAAC;IAED,MAAM;QACJ,MAAM,qBAAqB,GAAG;YAC5B,GAAG,IAAI,CAAC,0BAA0B;YAClC,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK;YAChC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;SAC9C,CAAC;QACF,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;IACtD,CAAC;IAED,yBAAyB;QACvB,IAAI,CAAC,0BAA0B,CAAC,MAAM,EAAE,CAAC,SAAS,CAAC;YACjD,IAAI,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC;YACzD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,8CAA8C,CAAC,CAAC;YAC1F,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAED,qBAAqB;QACnB,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,CAAC,SAAS,CAAC;YAC9C,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;YACjD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,2CAA2C,CAAC,CAAC;YACvF,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAED,8BAA8B;QAC5B,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;YAClC,IAAI,CAAC,iCAAiC;iBACnC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;iBACpC,SAAS,CAAC;gBACT,IAAI,EAAE,CAAC,cAAc,EAAE,EAAE;oBACvB,IAAI,CAAC,0BAA0B,GAAG,cAAc,CAAC;oBACjD,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,CAAC;gBACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;oBACf,IAAI,KAAK,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;wBACzB,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,oDAAoD,CAC5E,CAAC;oBACJ,CAAC;gBACH,CAAC;aACF,CAAC,CAAC;QACP,CAAC;IACH,CAAC;IAED,WAAW;QACT,IAAI,IAAI,CAAC,0BAA0B,EAAE,CAAC;YACpC,MAAM,mBAAmB,GACvB,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,gBAAgB,CAAC;YAErD,IAAI,mBAAmB,EAAE,CAAC;gBACxB,MAAM,uBAAuB,GAC3B,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;gBAClD,MAAM,mCAAmC,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CACrE,8BAA8B,CAC/B,CAAC;gBAEF,uBAAuB,EAAE,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBAC5D,mCAAmC,EAAE,aAAa,CAAC;oBACjD,UAAU,CAAC,QAAQ;oBACnB,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;iBAClB,CAAC,CAAC;YACL,CAAC;YAED,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAChC;gBACE,iBAAiB,EAAE,IAAI,CAAC,0BAA0B,CAAC,iBAAiB;gBACpE,mBAAmB,EACjB,IAAI,CAAC,0BAA0B,CAAC,mBAAmB,EAAE,EAAE;gBACzD,eAAe,EAAE,IAAI,CAAC,0BAA0B,CAAC,eAAe;gBAChE,mBAAmB,EAAE,mBAAmB;gBACxC,gBAAgB,EACd,IAAI,CAAC,0BAA0B,CAAC,gBAAgB,EAAE,EAAE;gBACtD,4BAA4B,EAC1B,IAAI,CAAC,0BAA0B,CAAC,4BAA4B;aAC/D,EACD,EAAE,SAAS,EAAE,KAAK,EAAE,CACrB,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;gBAC5D,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACjD,OAAO,EAAE,sBAAsB,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,kBAAkB,CAAC,sBAAsB,EAAE,CAAC;QACnD,CAAC;IACH,CAAC;IAED,qBAAqB;QACnB,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;QAChD,OAAO;YACL,GAAG,IAAI,CAAC,0BAA0B;YAClC,GAAG,SAAS;YACZ,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC;YACpC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,eAAe,EAAE,SAAS,CAAC,eAAe,IAAI,CAAC;YAC/C,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,qBAAqB,EAAE,SAAS,CAAC,mBAAmB;YACpD,kBAAkB,EAAE,SAAS,CAAC,mBAAmB;gBAC/C,CAAC,CAAC,SAAS,CAAC,gBAAgB;gBAC5B,CAAC,CAAC,IAAI;YACR,4BAA4B,EAAE,SAAS,CAAC,mBAAmB;gBACzD,CAAC,CAAC,SAAS,CAAC,4BAA4B;gBACxC,CAAC,CAAC,IAAI;SACT,CAAC;IACJ,CAAC;IAED,iBAAiB;QACf,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC7C,IAAI,CAAC,oBAAoB,CAAC,wBAAwB,CAChD,IAAI,CAAC,oBAAoB,EACzB,IAAI,CAAC,MAAM,CAAC,YAAY,CACzB,CAAC,SAAS,CAAC;gBACV,IAAI,EAAE,CAAC,OAAO,EAAE,EAAE;oBAChB,IAAI,CAAC,oBAAoB,GAAG,OAAO,IAAI,CAAC,CAAC;oBACzC,IAAI,CAAC,sBAAsB,EAAE,CAAC;gBAChC,CAAC;gBACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;oBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,wDAAwD,CAChF,CAAC;gBACJ,CAAC;aACF,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,mBAAmB;QACjB,IAAI,IAAI,CAAC,0BAA0B,EAAE,kBAAkB,EAAE,CAAC;YACxD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAED,0BAA0B,CAAC,EAAU;QACnC,OAAO,CACL,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI;YACvE,EAAE,CACH,CAAC;IACJ,CAAC;IAED,uBAAuB,CAAC,EAAU;QAChC,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC;IAC3E,CAAC;;;;;;;;;;;;;6CAhWA,KAAK;uCACL,KAAK;yBACL,KAAK;6CACL,KAAK;+BACL,KAAK;qCACL,MAAM;qCACN,MAAM;;;AATI,+CAA+C;IAtB3D,SAAS,CAAC;QACT,QAAQ,EAAE,gDAAgD;QAC1D,8BAA0E;QAE1E,UAAU,EAAE,IAAI;QAChB,OAAO,EAAE;YACP,mBAAmB;YACnB,OAAO;YACP,YAAY;YACZ,QAAQ;YACR,QAAQ;YACR,SAAS;YACT,SAAS;YACT,QAAQ;YACR,WAAW;YACX,SAAS;YACT,aAAa;YACb,UAAU;YACV,YAAY;YACZ,oBAAoB;SACrB;;KACF,CAAC;GACW,+CAA+C,CAoW3D\",\n      sourcesContent: [\"import { Contract } from '@contract-management/models/contract.model';\\nimport { ContractService } from '@contract-management/services/contract.service';\\nimport { ARLAffiliationClass } from '@contractor-dashboard/models/arl-affiliation-class.model';\\nimport { CompensationFund } from '@contractor-dashboard/models/compensation-fund.model';\\nimport { InitialReportDocumentation } from '@contractor-dashboard/models/initial-report-documentation.model';\\nimport { MonthlyReport } from '@contractor-dashboard/models/monthly-report.model';\\nimport { SocialSecurityContribution } from '@contractor-dashboard/models/social-security-contribution.model';\\nimport { ArlAffiliationClassService } from '@contractor-dashboard/services/arl-affiliation-class.service';\\nimport { CompensationFundService } from '@contractor-dashboard/services/compensation-fund.service';\\nimport { InitialReportDocumentationService } from '@contractor-dashboard/services/initial-report-documentation.service';\\nimport { PeriodService } from '@contractor-dashboard/services/period.service';\\nimport { SocialSecurityContributionService } from '@contractor-dashboard/services/social-security-contribution.service';\\nimport { AlertService } from '@shared/services/alert.service';\\n\\nimport { CurrencyPipe } from '@angular/common';\\nimport {\\n  Component,\\n  EventEmitter,\\n  Input,\\n  OnChanges,\\n  OnInit,\\n  Output,\\n  SimpleChanges,\\n} from '@angular/core';\\nimport {\\n  FormBuilder,\\n  FormGroup,\\n  ReactiveFormsModule,\\n  Validators,\\n} from '@angular/forms';\\nimport { MatButton, MatIconButton } from '@angular/material/button';\\nimport { MatCheckbox } from '@angular/material/checkbox';\\nimport { MatOption } from '@angular/material/core';\\nimport { MatError, MatFormField, MatLabel } from '@angular/material/form-field';\\nimport { MatIcon } from '@angular/material/icon';\\nimport { MatInput } from '@angular/material/input';\\nimport { MatSelect } from '@angular/material/select';\\nimport { MatTooltip } from '@angular/material/tooltip';\\nimport { NgxCurrencyDirective } from 'ngx-currency';\\n\\nimport { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';\\n\\n@Component({\\n  selector: 'app-monthly-report-social-security-information',\\n  templateUrl: './monthly-report-social-security-information.component.html',\\n  styleUrl: './monthly-report-social-security-information.component.scss',\\n  standalone: true,\\n  imports: [\\n    ReactiveFormsModule,\\n    MatIcon,\\n    MatFormField,\\n    MatLabel,\\n    MatInput,\\n    MatSelect,\\n    MatOption,\\n    MatError,\\n    MatCheckbox,\\n    MatButton,\\n    MatIconButton,\\n    MatTooltip,\\n    CurrencyPipe,\\n    NgxCurrencyDirective,\\n  ],\\n})\\nexport class MonthlyReportSocialSecurityInformationComponent\\n  implements OnInit, OnChanges\\n{\\n  @Input() socialSecurityContribution: SocialSecurityContribution | null = null;\\n  @Input() contractorContractId!: number;\\n  @Input() report!: MonthlyReport;\\n  @Input() initialReportDocumentation: InitialReportDocumentation | null = null;\\n  @Input() isSupervisor = false;\\n  @Output() saveSocialSecurity = new EventEmitter<SocialSecurityContribution>();\\n  @Output() formValidityChange = new EventEmitter<boolean>();\\n\\n  socialSecurityForm: FormGroup = this.fb.group({\\n    paymentFormNumber: [\\n      null,\\n      this.isSupervisor ? [] : [Validators.required, Validators.min(0)],\\n    ],\\n    certificateFile: [null, this.isSupervisor ? [] : [Validators.required]],\\n    arlAffiliationClass: [null, this.isSupervisor ? [] : [Validators.required]],\\n    arlContribution: [\\n      null,\\n      this.isSupervisor ? [] : [Validators.required, Validators.min(0)],\\n    ],\\n    hasCompensationFund: [false],\\n    compensationFund: [{ value: null, disabled: this.isSupervisor }],\\n    compensationFundContribution: [\\n      { value: null, disabled: this.isSupervisor },\\n    ],\\n  });\\n  fileName = '';\\n  contract: Contract | null = null;\\n  previousPaymentValue = 0;\\n  ibc = 0;\\n  healthContribution = 0;\\n  pensionContribution = 0;\\n  arlAffiliationClasses: ARLAffiliationClass[] = [];\\n  compensationFunds: CompensationFund[] = [];\\n  selectedFile: File | null = null;\\n\\n  constructor(\\n    private readonly fb: FormBuilder,\\n    private readonly contractService: ContractService,\\n    private readonly initialReportDocumentationService: InitialReportDocumentationService,\\n    private readonly alert: AlertService,\\n    private readonly arlAffiliationClassService: ArlAffiliationClassService,\\n    private readonly compensationFundService: CompensationFundService,\\n    private readonly socialSecurityContributionService: SocialSecurityContributionService,\\n    private readonly periodService: PeriodService,\\n    private readonly MonthlyReportService: MonthlyReportService,\\n  ) {\\n    if (this.isSupervisor) {\\n      this.socialSecurityForm.get('hasCompensationFund')?.disable();\\n    }\\n  }\\n\\n  private updateCompensationFundValidation(checked: boolean): void {\\n    const compensationFundControl =\\n      this.socialSecurityForm.get('compensationFund');\\n    const compensationFundContributionControl = this.socialSecurityForm.get(\\n      'compensationFundContribution',\\n    );\\n\\n    if (checked) {\\n      compensationFundControl?.setValidators(Validators.required);\\n      compensationFundContributionControl?.setValidators([\\n        Validators.required,\\n        Validators.min(0),\\n      ]);\\n    } else {\\n      compensationFundControl?.clearValidators();\\n      compensationFundContributionControl?.clearValidators();\\n      compensationFundControl?.setValue(null);\\n      compensationFundContributionControl?.setValue(null);\\n    }\\n\\n    compensationFundControl?.updateValueAndValidity({ emitEvent: false });\\n    compensationFundContributionControl?.updateValueAndValidity({\\n      emitEvent: false,\\n    });\\n    this.socialSecurityForm.updateValueAndValidity();\\n  }\\n\\n  ngOnInit(): void {\\n    this.initializeForm();\\n    this.loadData();\\n  }\\n\\n  ngOnChanges(changes: SimpleChanges): void {\\n    if (changes['socialSecurityContribution']) {\\n      this.initializeForm();\\n    }\\n  }\\n\\n  private initializeForm(): void {\\n    const certificateFileValidator = this.isSupervisor\\n      ? []\\n      : [Validators.required];\\n\\n    if (\\n      !this.isSupervisor &&\\n      this.socialSecurityContribution?.certificateFileUrl\\n    ) {\\n      certificateFileValidator.length = 0;\\n    }\\n\\n    this.socialSecurityForm = this.fb.group({\\n      paymentFormNumber: [\\n        this.socialSecurityContribution?.paymentFormNumber || null,\\n        this.isSupervisor ? [] : [Validators.required, Validators.min(0)],\\n      ],\\n      certificateFile: [null, certificateFileValidator],\\n      arlAffiliationClass: [\\n        this.socialSecurityContribution?.arlAffiliationClassId || null,\\n        this.isSupervisor ? [] : [Validators.required],\\n      ],\\n      arlContribution: [\\n        this.socialSecurityContribution?.arlContribution || null,\\n        this.isSupervisor ? [] : [Validators.required, Validators.min(0)],\\n      ],\\n      hasCompensationFund: [\\n        {\\n          value: Boolean(this.socialSecurityContribution?.compensationFundId),\\n          disabled: this.isSupervisor,\\n        },\\n      ],\\n      compensationFund: [\\n        this.socialSecurityContribution?.compensationFundId || null,\\n        { value: null, disabled: this.isSupervisor },\\n      ],\\n      compensationFundContribution: [\\n        this.socialSecurityContribution?.compensationFundContribution || null,\\n        { value: null, disabled: this.isSupervisor },\\n      ],\\n    });\\n\\n    this.updateFormValidity();\\n  }\\n\\n  loadData(): void {\\n    this.loadInitialReportDocumentation();\\n    this.loadARLAffiliationClasses();\\n    this.loadCompensationFunds();\\n    this.loadSocialSecurityContribution();\\n    this.loadPeriodPayment();\\n\\n    this.socialSecurityForm.statusChanges.subscribe(() => {\\n      this.formValidityChange.emit(\\n        this.isSupervisor ? true : this.socialSecurityForm.valid,\\n      );\\n    });\\n\\n    this.socialSecurityForm\\n      .get('hasCompensationFund')\\n      ?.valueChanges.subscribe((checked) => {\\n        if (!this.isSupervisor) {\\n          this.updateCompensationFundValidation(checked);\\n        }\\n      });\\n  }\\n\\n  loadInitialReportDocumentation(): void {\\n    this.initialReportDocumentationService\\n      .getByContractorContractId(this.contractorContractId)\\n      .subscribe({\\n        next: (data) => (this.initialReportDocumentation = data),\\n        error: (error) => {\\n          if (error.status !== 404) {\\n            this.alert.error(error.error?.detail ?? 'Error al cargar la documentaci\\xF3n inicial');\\n          }\\n        },\\n      });\\n  }\\n\\n  calculateContributions(): void {\\n    this.ibc = this.previousPaymentValue * 0.4;\\n    this.healthContribution = (this.ibc * 12.5) / 100;\\n    this.pensionContribution = (this.ibc * 16) / 100;\\n  }\\n\\n  onFileSelected(event: Event): void {\\n    const file = (event.target as HTMLInputElement).files?.[0];\\n    if (file) {\\n      if (file.type === 'application/pdf') {\\n        if (file.size <= 1024 * 1024) {\\n          this.selectedFile = file;\\n          this.fileName = file.name;\\n          this.socialSecurityForm.patchValue({ certificateFile: file });\\n          this.socialSecurityForm\\n            .get('certificateFile')\\n            ?.setValidators([Validators.required]);\\n        } else {\\n          this.alert.error('El archivo no debe superar 1MB');\\n        }\\n      } else {\\n        this.alert.error('Solo se permiten archivos PDF');\\n      }\\n      this.socialSecurityForm.get('certificateFile')?.updateValueAndValidity();\\n      this.updateFormValidity();\\n    }\\n  }\\n\\n  private updateFormValidity(): void {\\n    const formValid =\\n      this.isSupervisor ||\\n      this.socialSecurityForm.valid ||\\n      (this.socialSecurityForm.get('certificateFile')?.value === null &&\\n        Boolean(this.socialSecurityContribution?.certificateFileUrl));\\n\\n    this.formValidityChange.emit(formValid);\\n  }\\n\\n  onSave(): void {\\n    const updatedSocialSecurity = {\\n      ...this.socialSecurityContribution,\\n      ...this.socialSecurityForm.value,\\n      healthContribution: this.healthContribution,\\n      pensionContribution: this.pensionContribution,\\n    };\\n    this.saveSocialSecurity.emit(updatedSocialSecurity);\\n  }\\n\\n  loadARLAffiliationClasses(): void {\\n    this.arlAffiliationClassService.getAll().subscribe({\\n      next: (classes) => (this.arlAffiliationClasses = classes),\\n      error: (error) => {\\n        this.alert.error(error.error?.detail ?? 'Error al cargar las clases de afiliaci\\xF3n ARL');\\n      },\\n    });\\n  }\\n\\n  loadCompensationFunds(): void {\\n    this.compensationFundService.getAll().subscribe({\\n      next: (funds) => (this.compensationFunds = funds),\\n      error: (error) => {\\n        this.alert.error(error.error?.detail ?? 'Error al cargar las cajas de compensaci\\xF3n');\\n      },\\n    });\\n  }\\n\\n  loadSocialSecurityContribution(): void {\\n    if (this.report && this.report.id) {\\n      this.socialSecurityContributionService\\n        .getByMonthlyReportId(this.report.id)\\n        .subscribe({\\n          next: (socialSecurity) => {\\n            this.socialSecurityContribution = socialSecurity;\\n            this.prefillForm();\\n          },\\n          error: (error) => {\\n            if (error.status !== 404) {\\n              this.alert.error(\\n                error.error?.detail ?? 'Error al cargar la informaci\\xF3n de seguridad social',\\n              );\\n            }\\n          },\\n        });\\n    }\\n  }\\n\\n  prefillForm(): void {\\n    if (this.socialSecurityContribution) {\\n      const hasCompensationFund =\\n        !!this.socialSecurityContribution.compensationFund;\\n\\n      if (hasCompensationFund) {\\n        const compensationFundControl =\\n          this.socialSecurityForm.get('compensationFund');\\n        const compensationFundContributionControl = this.socialSecurityForm.get(\\n          'compensationFundContribution',\\n        );\\n\\n        compensationFundControl?.setValidators(Validators.required);\\n        compensationFundContributionControl?.setValidators([\\n          Validators.required,\\n          Validators.min(0),\\n        ]);\\n      }\\n\\n      this.socialSecurityForm.patchValue(\\n        {\\n          paymentFormNumber: this.socialSecurityContribution.paymentFormNumber,\\n          arlAffiliationClass:\\n            this.socialSecurityContribution.arlAffiliationClass?.id,\\n          arlContribution: this.socialSecurityContribution.arlContribution,\\n          hasCompensationFund: hasCompensationFund,\\n          compensationFund:\\n            this.socialSecurityContribution.compensationFund?.id,\\n          compensationFundContribution:\\n            this.socialSecurityContribution.compensationFundContribution,\\n        },\\n        { emitEvent: false },\\n      );\\n\\n      Object.keys(this.socialSecurityForm.controls).forEach((key) => {\\n        const control = this.socialSecurityForm.get(key);\\n        control?.updateValueAndValidity({ emitEvent: false });\\n      });\\n\\n      this.socialSecurityForm.updateValueAndValidity();\\n    }\\n  }\\n\\n  getSocialSecurityData(): SocialSecurityContribution {\\n    const formValue = this.socialSecurityForm.value;\\n    return {\\n      ...this.socialSecurityContribution,\\n      ...formValue,\\n      monthlyReportId: this.report.id || 0,\\n      healthContribution: this.healthContribution,\\n      pensionContribution: this.pensionContribution,\\n      arlContribution: formValue.arlContribution || 0,\\n      ibc: this.ibc,\\n      arlAffiliationClassId: formValue.arlAffiliationClass,\\n      compensationFundId: formValue.hasCompensationFund\\n        ? formValue.compensationFund\\n        : null,\\n      compensationFundContribution: formValue.hasCompensationFund\\n        ? formValue.compensationFundContribution\\n        : null,\\n    };\\n  }\\n\\n  loadPeriodPayment(): void {\\n    if (this.report && this.contractorContractId) {\\n      this.MonthlyReportService.getPreviousPeriodPayment(\\n        this.contractorContractId,\\n        this.report.reportNumber,\\n      ).subscribe({\\n        next: (payment) => {\\n          this.previousPaymentValue = payment || 0;\\n          this.calculateContributions();\\n        },\\n        error: (error) => {\\n          this.alert.error(\\n            error.error?.detail ?? 'Error al cargar el valor a cobrar del periodo anterior',\\n          );\\n        },\\n      });\\n    }\\n  }\\n\\n  downloadCertificate(): void {\\n    if (this.socialSecurityContribution?.certificateFileUrl) {\\n      window.open(this.socialSecurityContribution.certificateFileUrl, '_blank');\\n    }\\n  }\\n\\n  getArlAffiliationClassName(id: number): string {\\n    return (\\n      this.arlAffiliationClasses.find((arlClass) => arlClass.id === id)?.name ||\\n      ''\\n    );\\n  }\\n\\n  getCompensationFundName(id: number): string {\\n    return this.compensationFunds.find((fund) => fund.id === id)?.name || '';\\n  }\\n}\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"28bf8d402a8a2bc8791119e5d45e28ed0484cb0d\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_1i2s5zln2m = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_1i2s5zln2m();\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./monthly-report-social-security-information.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./monthly-report-social-security-information.component.scss?ngResource\";\nimport { ContractService } from '@contract-management/services/contract.service';\nimport { ArlAffiliationClassService } from '@contractor-dashboard/services/arl-affiliation-class.service';\nimport { CompensationFundService } from '@contractor-dashboard/services/compensation-fund.service';\nimport { InitialReportDocumentationService } from '@contractor-dashboard/services/initial-report-documentation.service';\nimport { PeriodService } from '@contractor-dashboard/services/period.service';\nimport { SocialSecurityContributionService } from '@contractor-dashboard/services/social-security-contribution.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { CurrencyPipe } from '@angular/common';\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatButton, MatIconButton } from '@angular/material/button';\nimport { MatCheckbox } from '@angular/material/checkbox';\nimport { MatOption } from '@angular/material/core';\nimport { MatError, MatFormField, MatLabel } from '@angular/material/form-field';\nimport { MatIcon } from '@angular/material/icon';\nimport { MatInput } from '@angular/material/input';\nimport { MatSelect } from '@angular/material/select';\nimport { MatTooltip } from '@angular/material/tooltip';\nimport { NgxCurrencyDirective } from 'ngx-currency';\nimport { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';\ncov_1i2s5zln2m().s[0]++;\nlet MonthlyReportSocialSecurityInformationComponent = class MonthlyReportSocialSecurityInformationComponent {\n  constructor(fb, contractService, initialReportDocumentationService, alert, arlAffiliationClassService, compensationFundService, socialSecurityContributionService, periodService, MonthlyReportService) {\n    cov_1i2s5zln2m().f[0]++;\n    cov_1i2s5zln2m().s[1]++;\n    this.fb = fb;\n    cov_1i2s5zln2m().s[2]++;\n    this.contractService = contractService;\n    cov_1i2s5zln2m().s[3]++;\n    this.initialReportDocumentationService = initialReportDocumentationService;\n    cov_1i2s5zln2m().s[4]++;\n    this.alert = alert;\n    cov_1i2s5zln2m().s[5]++;\n    this.arlAffiliationClassService = arlAffiliationClassService;\n    cov_1i2s5zln2m().s[6]++;\n    this.compensationFundService = compensationFundService;\n    cov_1i2s5zln2m().s[7]++;\n    this.socialSecurityContributionService = socialSecurityContributionService;\n    cov_1i2s5zln2m().s[8]++;\n    this.periodService = periodService;\n    cov_1i2s5zln2m().s[9]++;\n    this.MonthlyReportService = MonthlyReportService;\n    cov_1i2s5zln2m().s[10]++;\n    this.socialSecurityContribution = null;\n    cov_1i2s5zln2m().s[11]++;\n    this.initialReportDocumentation = null;\n    cov_1i2s5zln2m().s[12]++;\n    this.isSupervisor = false;\n    cov_1i2s5zln2m().s[13]++;\n    this.saveSocialSecurity = new EventEmitter();\n    cov_1i2s5zln2m().s[14]++;\n    this.formValidityChange = new EventEmitter();\n    cov_1i2s5zln2m().s[15]++;\n    this.socialSecurityForm = this.fb.group({\n      paymentFormNumber: [null, this.isSupervisor ? (cov_1i2s5zln2m().b[0][0]++, []) : (cov_1i2s5zln2m().b[0][1]++, [Validators.required, Validators.min(0)])],\n      certificateFile: [null, this.isSupervisor ? (cov_1i2s5zln2m().b[1][0]++, []) : (cov_1i2s5zln2m().b[1][1]++, [Validators.required])],\n      arlAffiliationClass: [null, this.isSupervisor ? (cov_1i2s5zln2m().b[2][0]++, []) : (cov_1i2s5zln2m().b[2][1]++, [Validators.required])],\n      arlContribution: [null, this.isSupervisor ? (cov_1i2s5zln2m().b[3][0]++, []) : (cov_1i2s5zln2m().b[3][1]++, [Validators.required, Validators.min(0)])],\n      hasCompensationFund: [false],\n      compensationFund: [{\n        value: null,\n        disabled: this.isSupervisor\n      }],\n      compensationFundContribution: [{\n        value: null,\n        disabled: this.isSupervisor\n      }]\n    });\n    cov_1i2s5zln2m().s[16]++;\n    this.fileName = '';\n    cov_1i2s5zln2m().s[17]++;\n    this.contract = null;\n    cov_1i2s5zln2m().s[18]++;\n    this.previousPaymentValue = 0;\n    cov_1i2s5zln2m().s[19]++;\n    this.ibc = 0;\n    cov_1i2s5zln2m().s[20]++;\n    this.healthContribution = 0;\n    cov_1i2s5zln2m().s[21]++;\n    this.pensionContribution = 0;\n    cov_1i2s5zln2m().s[22]++;\n    this.arlAffiliationClasses = [];\n    cov_1i2s5zln2m().s[23]++;\n    this.compensationFunds = [];\n    cov_1i2s5zln2m().s[24]++;\n    this.selectedFile = null;\n    cov_1i2s5zln2m().s[25]++;\n    if (this.isSupervisor) {\n      cov_1i2s5zln2m().b[4][0]++;\n      cov_1i2s5zln2m().s[26]++;\n      this.socialSecurityForm.get('hasCompensationFund')?.disable();\n    } else {\n      cov_1i2s5zln2m().b[4][1]++;\n    }\n  }\n  updateCompensationFundValidation(checked) {\n    cov_1i2s5zln2m().f[1]++;\n    const compensationFundControl = (cov_1i2s5zln2m().s[27]++, this.socialSecurityForm.get('compensationFund'));\n    const compensationFundContributionControl = (cov_1i2s5zln2m().s[28]++, this.socialSecurityForm.get('compensationFundContribution'));\n    cov_1i2s5zln2m().s[29]++;\n    if (checked) {\n      cov_1i2s5zln2m().b[5][0]++;\n      cov_1i2s5zln2m().s[30]++;\n      compensationFundControl?.setValidators(Validators.required);\n      cov_1i2s5zln2m().s[31]++;\n      compensationFundContributionControl?.setValidators([Validators.required, Validators.min(0)]);\n    } else {\n      cov_1i2s5zln2m().b[5][1]++;\n      cov_1i2s5zln2m().s[32]++;\n      compensationFundControl?.clearValidators();\n      cov_1i2s5zln2m().s[33]++;\n      compensationFundContributionControl?.clearValidators();\n      cov_1i2s5zln2m().s[34]++;\n      compensationFundControl?.setValue(null);\n      cov_1i2s5zln2m().s[35]++;\n      compensationFundContributionControl?.setValue(null);\n    }\n    cov_1i2s5zln2m().s[36]++;\n    compensationFundControl?.updateValueAndValidity({\n      emitEvent: false\n    });\n    cov_1i2s5zln2m().s[37]++;\n    compensationFundContributionControl?.updateValueAndValidity({\n      emitEvent: false\n    });\n    cov_1i2s5zln2m().s[38]++;\n    this.socialSecurityForm.updateValueAndValidity();\n  }\n  ngOnInit() {\n    cov_1i2s5zln2m().f[2]++;\n    cov_1i2s5zln2m().s[39]++;\n    this.initializeForm();\n    cov_1i2s5zln2m().s[40]++;\n    this.loadData();\n  }\n  ngOnChanges(changes) {\n    cov_1i2s5zln2m().f[3]++;\n    cov_1i2s5zln2m().s[41]++;\n    if (changes['socialSecurityContribution']) {\n      cov_1i2s5zln2m().b[6][0]++;\n      cov_1i2s5zln2m().s[42]++;\n      this.initializeForm();\n    } else {\n      cov_1i2s5zln2m().b[6][1]++;\n    }\n  }\n  initializeForm() {\n    cov_1i2s5zln2m().f[4]++;\n    const certificateFileValidator = (cov_1i2s5zln2m().s[43]++, this.isSupervisor ? (cov_1i2s5zln2m().b[7][0]++, []) : (cov_1i2s5zln2m().b[7][1]++, [Validators.required]));\n    cov_1i2s5zln2m().s[44]++;\n    if ((cov_1i2s5zln2m().b[9][0]++, !this.isSupervisor) && (cov_1i2s5zln2m().b[9][1]++, this.socialSecurityContribution?.certificateFileUrl)) {\n      cov_1i2s5zln2m().b[8][0]++;\n      cov_1i2s5zln2m().s[45]++;\n      certificateFileValidator.length = 0;\n    } else {\n      cov_1i2s5zln2m().b[8][1]++;\n    }\n    cov_1i2s5zln2m().s[46]++;\n    this.socialSecurityForm = this.fb.group({\n      paymentFormNumber: [(cov_1i2s5zln2m().b[10][0]++, this.socialSecurityContribution?.paymentFormNumber) || (cov_1i2s5zln2m().b[10][1]++, null), this.isSupervisor ? (cov_1i2s5zln2m().b[11][0]++, []) : (cov_1i2s5zln2m().b[11][1]++, [Validators.required, Validators.min(0)])],\n      certificateFile: [null, certificateFileValidator],\n      arlAffiliationClass: [(cov_1i2s5zln2m().b[12][0]++, this.socialSecurityContribution?.arlAffiliationClassId) || (cov_1i2s5zln2m().b[12][1]++, null), this.isSupervisor ? (cov_1i2s5zln2m().b[13][0]++, []) : (cov_1i2s5zln2m().b[13][1]++, [Validators.required])],\n      arlContribution: [(cov_1i2s5zln2m().b[14][0]++, this.socialSecurityContribution?.arlContribution) || (cov_1i2s5zln2m().b[14][1]++, null), this.isSupervisor ? (cov_1i2s5zln2m().b[15][0]++, []) : (cov_1i2s5zln2m().b[15][1]++, [Validators.required, Validators.min(0)])],\n      hasCompensationFund: [{\n        value: Boolean(this.socialSecurityContribution?.compensationFundId),\n        disabled: this.isSupervisor\n      }],\n      compensationFund: [(cov_1i2s5zln2m().b[16][0]++, this.socialSecurityContribution?.compensationFundId) || (cov_1i2s5zln2m().b[16][1]++, null), {\n        value: null,\n        disabled: this.isSupervisor\n      }],\n      compensationFundContribution: [(cov_1i2s5zln2m().b[17][0]++, this.socialSecurityContribution?.compensationFundContribution) || (cov_1i2s5zln2m().b[17][1]++, null), {\n        value: null,\n        disabled: this.isSupervisor\n      }]\n    });\n    cov_1i2s5zln2m().s[47]++;\n    this.updateFormValidity();\n  }\n  loadData() {\n    cov_1i2s5zln2m().f[5]++;\n    cov_1i2s5zln2m().s[48]++;\n    this.loadInitialReportDocumentation();\n    cov_1i2s5zln2m().s[49]++;\n    this.loadARLAffiliationClasses();\n    cov_1i2s5zln2m().s[50]++;\n    this.loadCompensationFunds();\n    cov_1i2s5zln2m().s[51]++;\n    this.loadSocialSecurityContribution();\n    cov_1i2s5zln2m().s[52]++;\n    this.loadPeriodPayment();\n    cov_1i2s5zln2m().s[53]++;\n    this.socialSecurityForm.statusChanges.subscribe(() => {\n      cov_1i2s5zln2m().f[6]++;\n      cov_1i2s5zln2m().s[54]++;\n      this.formValidityChange.emit(this.isSupervisor ? (cov_1i2s5zln2m().b[18][0]++, true) : (cov_1i2s5zln2m().b[18][1]++, this.socialSecurityForm.valid));\n    });\n    cov_1i2s5zln2m().s[55]++;\n    this.socialSecurityForm.get('hasCompensationFund')?.valueChanges.subscribe(checked => {\n      cov_1i2s5zln2m().f[7]++;\n      cov_1i2s5zln2m().s[56]++;\n      if (!this.isSupervisor) {\n        cov_1i2s5zln2m().b[19][0]++;\n        cov_1i2s5zln2m().s[57]++;\n        this.updateCompensationFundValidation(checked);\n      } else {\n        cov_1i2s5zln2m().b[19][1]++;\n      }\n    });\n  }\n  loadInitialReportDocumentation() {\n    cov_1i2s5zln2m().f[8]++;\n    cov_1i2s5zln2m().s[58]++;\n    this.initialReportDocumentationService.getByContractorContractId(this.contractorContractId).subscribe({\n      next: data => {\n        cov_1i2s5zln2m().f[9]++;\n        cov_1i2s5zln2m().s[59]++;\n        return this.initialReportDocumentation = data;\n      },\n      error: error => {\n        cov_1i2s5zln2m().f[10]++;\n        cov_1i2s5zln2m().s[60]++;\n        if (error.status !== 404) {\n          cov_1i2s5zln2m().b[20][0]++;\n          cov_1i2s5zln2m().s[61]++;\n          this.alert.error((cov_1i2s5zln2m().b[21][0]++, error.error?.detail) ?? (cov_1i2s5zln2m().b[21][1]++, 'Error al cargar la documentación inicial'));\n        } else {\n          cov_1i2s5zln2m().b[20][1]++;\n        }\n      }\n    });\n  }\n  calculateContributions() {\n    cov_1i2s5zln2m().f[11]++;\n    cov_1i2s5zln2m().s[62]++;\n    this.ibc = this.previousPaymentValue * 0.4;\n    cov_1i2s5zln2m().s[63]++;\n    this.healthContribution = this.ibc * 12.5 / 100;\n    cov_1i2s5zln2m().s[64]++;\n    this.pensionContribution = this.ibc * 16 / 100;\n  }\n  onFileSelected(event) {\n    cov_1i2s5zln2m().f[12]++;\n    const file = (cov_1i2s5zln2m().s[65]++, event.target.files?.[0]);\n    cov_1i2s5zln2m().s[66]++;\n    if (file) {\n      cov_1i2s5zln2m().b[22][0]++;\n      cov_1i2s5zln2m().s[67]++;\n      if (file.type === 'application/pdf') {\n        cov_1i2s5zln2m().b[23][0]++;\n        cov_1i2s5zln2m().s[68]++;\n        if (file.size <= 1024 * 1024) {\n          cov_1i2s5zln2m().b[24][0]++;\n          cov_1i2s5zln2m().s[69]++;\n          this.selectedFile = file;\n          cov_1i2s5zln2m().s[70]++;\n          this.fileName = file.name;\n          cov_1i2s5zln2m().s[71]++;\n          this.socialSecurityForm.patchValue({\n            certificateFile: file\n          });\n          cov_1i2s5zln2m().s[72]++;\n          this.socialSecurityForm.get('certificateFile')?.setValidators([Validators.required]);\n        } else {\n          cov_1i2s5zln2m().b[24][1]++;\n          cov_1i2s5zln2m().s[73]++;\n          this.alert.error('El archivo no debe superar 1MB');\n        }\n      } else {\n        cov_1i2s5zln2m().b[23][1]++;\n        cov_1i2s5zln2m().s[74]++;\n        this.alert.error('Solo se permiten archivos PDF');\n      }\n      cov_1i2s5zln2m().s[75]++;\n      this.socialSecurityForm.get('certificateFile')?.updateValueAndValidity();\n      cov_1i2s5zln2m().s[76]++;\n      this.updateFormValidity();\n    } else {\n      cov_1i2s5zln2m().b[22][1]++;\n    }\n  }\n  updateFormValidity() {\n    cov_1i2s5zln2m().f[13]++;\n    const formValid = (cov_1i2s5zln2m().s[77]++, (cov_1i2s5zln2m().b[25][0]++, this.isSupervisor) || (cov_1i2s5zln2m().b[25][1]++, this.socialSecurityForm.valid) || (cov_1i2s5zln2m().b[25][2]++, this.socialSecurityForm.get('certificateFile')?.value === null) && (cov_1i2s5zln2m().b[25][3]++, Boolean(this.socialSecurityContribution?.certificateFileUrl)));\n    cov_1i2s5zln2m().s[78]++;\n    this.formValidityChange.emit(formValid);\n  }\n  onSave() {\n    cov_1i2s5zln2m().f[14]++;\n    const updatedSocialSecurity = (cov_1i2s5zln2m().s[79]++, {\n      ...this.socialSecurityContribution,\n      ...this.socialSecurityForm.value,\n      healthContribution: this.healthContribution,\n      pensionContribution: this.pensionContribution\n    });\n    cov_1i2s5zln2m().s[80]++;\n    this.saveSocialSecurity.emit(updatedSocialSecurity);\n  }\n  loadARLAffiliationClasses() {\n    cov_1i2s5zln2m().f[15]++;\n    cov_1i2s5zln2m().s[81]++;\n    this.arlAffiliationClassService.getAll().subscribe({\n      next: classes => {\n        cov_1i2s5zln2m().f[16]++;\n        cov_1i2s5zln2m().s[82]++;\n        return this.arlAffiliationClasses = classes;\n      },\n      error: error => {\n        cov_1i2s5zln2m().f[17]++;\n        cov_1i2s5zln2m().s[83]++;\n        this.alert.error((cov_1i2s5zln2m().b[26][0]++, error.error?.detail) ?? (cov_1i2s5zln2m().b[26][1]++, 'Error al cargar las clases de afiliación ARL'));\n      }\n    });\n  }\n  loadCompensationFunds() {\n    cov_1i2s5zln2m().f[18]++;\n    cov_1i2s5zln2m().s[84]++;\n    this.compensationFundService.getAll().subscribe({\n      next: funds => {\n        cov_1i2s5zln2m().f[19]++;\n        cov_1i2s5zln2m().s[85]++;\n        return this.compensationFunds = funds;\n      },\n      error: error => {\n        cov_1i2s5zln2m().f[20]++;\n        cov_1i2s5zln2m().s[86]++;\n        this.alert.error((cov_1i2s5zln2m().b[27][0]++, error.error?.detail) ?? (cov_1i2s5zln2m().b[27][1]++, 'Error al cargar las cajas de compensación'));\n      }\n    });\n  }\n  loadSocialSecurityContribution() {\n    cov_1i2s5zln2m().f[21]++;\n    cov_1i2s5zln2m().s[87]++;\n    if ((cov_1i2s5zln2m().b[29][0]++, this.report) && (cov_1i2s5zln2m().b[29][1]++, this.report.id)) {\n      cov_1i2s5zln2m().b[28][0]++;\n      cov_1i2s5zln2m().s[88]++;\n      this.socialSecurityContributionService.getByMonthlyReportId(this.report.id).subscribe({\n        next: socialSecurity => {\n          cov_1i2s5zln2m().f[22]++;\n          cov_1i2s5zln2m().s[89]++;\n          this.socialSecurityContribution = socialSecurity;\n          cov_1i2s5zln2m().s[90]++;\n          this.prefillForm();\n        },\n        error: error => {\n          cov_1i2s5zln2m().f[23]++;\n          cov_1i2s5zln2m().s[91]++;\n          if (error.status !== 404) {\n            cov_1i2s5zln2m().b[30][0]++;\n            cov_1i2s5zln2m().s[92]++;\n            this.alert.error((cov_1i2s5zln2m().b[31][0]++, error.error?.detail) ?? (cov_1i2s5zln2m().b[31][1]++, 'Error al cargar la información de seguridad social'));\n          } else {\n            cov_1i2s5zln2m().b[30][1]++;\n          }\n        }\n      });\n    } else {\n      cov_1i2s5zln2m().b[28][1]++;\n    }\n  }\n  prefillForm() {\n    cov_1i2s5zln2m().f[24]++;\n    cov_1i2s5zln2m().s[93]++;\n    if (this.socialSecurityContribution) {\n      cov_1i2s5zln2m().b[32][0]++;\n      const hasCompensationFund = (cov_1i2s5zln2m().s[94]++, !!this.socialSecurityContribution.compensationFund);\n      cov_1i2s5zln2m().s[95]++;\n      if (hasCompensationFund) {\n        cov_1i2s5zln2m().b[33][0]++;\n        const compensationFundControl = (cov_1i2s5zln2m().s[96]++, this.socialSecurityForm.get('compensationFund'));\n        const compensationFundContributionControl = (cov_1i2s5zln2m().s[97]++, this.socialSecurityForm.get('compensationFundContribution'));\n        cov_1i2s5zln2m().s[98]++;\n        compensationFundControl?.setValidators(Validators.required);\n        cov_1i2s5zln2m().s[99]++;\n        compensationFundContributionControl?.setValidators([Validators.required, Validators.min(0)]);\n      } else {\n        cov_1i2s5zln2m().b[33][1]++;\n      }\n      cov_1i2s5zln2m().s[100]++;\n      this.socialSecurityForm.patchValue({\n        paymentFormNumber: this.socialSecurityContribution.paymentFormNumber,\n        arlAffiliationClass: this.socialSecurityContribution.arlAffiliationClass?.id,\n        arlContribution: this.socialSecurityContribution.arlContribution,\n        hasCompensationFund: hasCompensationFund,\n        compensationFund: this.socialSecurityContribution.compensationFund?.id,\n        compensationFundContribution: this.socialSecurityContribution.compensationFundContribution\n      }, {\n        emitEvent: false\n      });\n      cov_1i2s5zln2m().s[101]++;\n      Object.keys(this.socialSecurityForm.controls).forEach(key => {\n        cov_1i2s5zln2m().f[25]++;\n        const control = (cov_1i2s5zln2m().s[102]++, this.socialSecurityForm.get(key));\n        cov_1i2s5zln2m().s[103]++;\n        control?.updateValueAndValidity({\n          emitEvent: false\n        });\n      });\n      cov_1i2s5zln2m().s[104]++;\n      this.socialSecurityForm.updateValueAndValidity();\n    } else {\n      cov_1i2s5zln2m().b[32][1]++;\n    }\n  }\n  getSocialSecurityData() {\n    cov_1i2s5zln2m().f[26]++;\n    const formValue = (cov_1i2s5zln2m().s[105]++, this.socialSecurityForm.value);\n    cov_1i2s5zln2m().s[106]++;\n    return {\n      ...this.socialSecurityContribution,\n      ...formValue,\n      monthlyReportId: (cov_1i2s5zln2m().b[34][0]++, this.report.id) || (cov_1i2s5zln2m().b[34][1]++, 0),\n      healthContribution: this.healthContribution,\n      pensionContribution: this.pensionContribution,\n      arlContribution: (cov_1i2s5zln2m().b[35][0]++, formValue.arlContribution) || (cov_1i2s5zln2m().b[35][1]++, 0),\n      ibc: this.ibc,\n      arlAffiliationClassId: formValue.arlAffiliationClass,\n      compensationFundId: formValue.hasCompensationFund ? (cov_1i2s5zln2m().b[36][0]++, formValue.compensationFund) : (cov_1i2s5zln2m().b[36][1]++, null),\n      compensationFundContribution: formValue.hasCompensationFund ? (cov_1i2s5zln2m().b[37][0]++, formValue.compensationFundContribution) : (cov_1i2s5zln2m().b[37][1]++, null)\n    };\n  }\n  loadPeriodPayment() {\n    cov_1i2s5zln2m().f[27]++;\n    cov_1i2s5zln2m().s[107]++;\n    if ((cov_1i2s5zln2m().b[39][0]++, this.report) && (cov_1i2s5zln2m().b[39][1]++, this.contractorContractId)) {\n      cov_1i2s5zln2m().b[38][0]++;\n      cov_1i2s5zln2m().s[108]++;\n      this.MonthlyReportService.getPreviousPeriodPayment(this.contractorContractId, this.report.reportNumber).subscribe({\n        next: payment => {\n          cov_1i2s5zln2m().f[28]++;\n          cov_1i2s5zln2m().s[109]++;\n          this.previousPaymentValue = (cov_1i2s5zln2m().b[40][0]++, payment) || (cov_1i2s5zln2m().b[40][1]++, 0);\n          cov_1i2s5zln2m().s[110]++;\n          this.calculateContributions();\n        },\n        error: error => {\n          cov_1i2s5zln2m().f[29]++;\n          cov_1i2s5zln2m().s[111]++;\n          this.alert.error((cov_1i2s5zln2m().b[41][0]++, error.error?.detail) ?? (cov_1i2s5zln2m().b[41][1]++, 'Error al cargar el valor a cobrar del periodo anterior'));\n        }\n      });\n    } else {\n      cov_1i2s5zln2m().b[38][1]++;\n    }\n  }\n  downloadCertificate() {\n    cov_1i2s5zln2m().f[30]++;\n    cov_1i2s5zln2m().s[112]++;\n    if (this.socialSecurityContribution?.certificateFileUrl) {\n      cov_1i2s5zln2m().b[42][0]++;\n      cov_1i2s5zln2m().s[113]++;\n      window.open(this.socialSecurityContribution.certificateFileUrl, '_blank');\n    } else {\n      cov_1i2s5zln2m().b[42][1]++;\n    }\n  }\n  getArlAffiliationClassName(id) {\n    cov_1i2s5zln2m().f[31]++;\n    cov_1i2s5zln2m().s[114]++;\n    return (cov_1i2s5zln2m().b[43][0]++, this.arlAffiliationClasses.find(arlClass => {\n      cov_1i2s5zln2m().f[32]++;\n      cov_1i2s5zln2m().s[115]++;\n      return arlClass.id === id;\n    })?.name) || (cov_1i2s5zln2m().b[43][1]++, '');\n  }\n  getCompensationFundName(id) {\n    cov_1i2s5zln2m().f[33]++;\n    cov_1i2s5zln2m().s[116]++;\n    return (cov_1i2s5zln2m().b[44][0]++, this.compensationFunds.find(fund => {\n      cov_1i2s5zln2m().f[34]++;\n      cov_1i2s5zln2m().s[117]++;\n      return fund.id === id;\n    })?.name) || (cov_1i2s5zln2m().b[44][1]++, '');\n  }\n  static {\n    cov_1i2s5zln2m().s[118]++;\n    this.ctorParameters = () => {\n      cov_1i2s5zln2m().f[35]++;\n      cov_1i2s5zln2m().s[119]++;\n      return [{\n        type: FormBuilder\n      }, {\n        type: ContractService\n      }, {\n        type: InitialReportDocumentationService\n      }, {\n        type: AlertService\n      }, {\n        type: ArlAffiliationClassService\n      }, {\n        type: CompensationFundService\n      }, {\n        type: SocialSecurityContributionService\n      }, {\n        type: PeriodService\n      }, {\n        type: MonthlyReportService\n      }];\n    };\n  }\n  static {\n    cov_1i2s5zln2m().s[120]++;\n    this.propDecorators = {\n      socialSecurityContribution: [{\n        type: Input\n      }],\n      contractorContractId: [{\n        type: Input\n      }],\n      report: [{\n        type: Input\n      }],\n      initialReportDocumentation: [{\n        type: Input\n      }],\n      isSupervisor: [{\n        type: Input\n      }],\n      saveSocialSecurity: [{\n        type: Output\n      }],\n      formValidityChange: [{\n        type: Output\n      }]\n    };\n  }\n};\ncov_1i2s5zln2m().s[121]++;\nMonthlyReportSocialSecurityInformationComponent = __decorate([Component({\n  selector: 'app-monthly-report-social-security-information',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: true,\n  imports: [ReactiveFormsModule, MatIcon, MatFormField, MatLabel, MatInput, MatSelect, MatOption, MatError, MatCheckbox, MatButton, MatIconButton, MatTooltip, CurrencyPipe, NgxCurrencyDirective],\n  styles: [__NG_CLI_RESOURCE__1]\n})], MonthlyReportSocialSecurityInformationComponent);\nexport { MonthlyReportSocialSecurityInformationComponent };", "map": {"version": 3, "names": ["cov_1i2s5zln2m", "actualCoverage", "ContractService", "ArlAffiliationClassService", "CompensationFundService", "InitialReportDocumentationService", "PeriodService", "SocialSecurityContributionService", "AlertService", "C<PERSON><PERSON>cyPipe", "Component", "EventEmitter", "Input", "Output", "FormBuilder", "ReactiveFormsModule", "Validators", "MatButton", "MatIconButton", "MatCheckbox", "MatOption", "<PERSON><PERSON><PERSON><PERSON>", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatIcon", "MatInput", "MatSelect", "MatTooltip", "NgxCurrencyDirective", "MonthlyReportService", "s", "MonthlyReportSocialSecurityInformationComponent", "constructor", "fb", "contractService", "initialReportDocumentationService", "alert", "arlAffiliationClassService", "compensationFundService", "socialSecurityContributionService", "periodService", "f", "socialSecurityContribution", "initialReportDocumentation", "isSupervisor", "saveSocialSecurity", "formValidityChange", "socialSecurityForm", "group", "paymentFormNumber", "b", "required", "min", "certificateFile", "arlAffiliationClass", "arlContribution", "hasCompensationFund", "compensationFund", "value", "disabled", "compensationFundContribution", "fileName", "contract", "previousPaymentValue", "ibc", "healthContribution", "pensionContribution", "arlAffiliationClasses", "compensationFunds", "selectedFile", "get", "disable", "updateCompensationFundValidation", "checked", "compensationFundControl", "compensationFundContributionControl", "setValidators", "clearValidators", "setValue", "updateValueAndValidity", "emitEvent", "ngOnInit", "initializeForm", "loadData", "ngOnChanges", "changes", "certificateFileValidator", "certificateFileUrl", "length", "arlAffiliationClassId", "Boolean", "compensationFundId", "updateFormValidity", "loadInitialReportDocumentation", "loadARLAffiliationClasses", "loadCompensationFunds", "loadSocialSecurityContribution", "loadPeriodPayment", "statusChanges", "subscribe", "emit", "valid", "valueChanges", "getByContractorContractId", "contractorContractId", "next", "data", "error", "status", "detail", "calculateContributions", "onFileSelected", "event", "file", "target", "files", "type", "size", "name", "patchValue", "formValid", "onSave", "updatedSocialSecurity", "getAll", "classes", "funds", "report", "id", "getByMonthlyReportId", "socialSecurity", "prefillForm", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "getSocialSecurityData", "formValue", "monthlyReportId", "getPreviousPeriodPayment", "reportNumber", "payment", "downloadCertificate", "window", "open", "getArlAffiliationClassName", "find", "arlClass", "getCompensationFundName", "fund", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\components\\monthly-reports-tab\\monthly-report-dialog\\monthly-report-social-security-information\\monthly-report-social-security-information.component.ts"], "sourcesContent": ["import { Contract } from '@contract-management/models/contract.model';\nimport { ContractService } from '@contract-management/services/contract.service';\nimport { ARLAffiliationClass } from '@contractor-dashboard/models/arl-affiliation-class.model';\nimport { CompensationFund } from '@contractor-dashboard/models/compensation-fund.model';\nimport { InitialReportDocumentation } from '@contractor-dashboard/models/initial-report-documentation.model';\nimport { MonthlyReport } from '@contractor-dashboard/models/monthly-report.model';\nimport { SocialSecurityContribution } from '@contractor-dashboard/models/social-security-contribution.model';\nimport { ArlAffiliationClassService } from '@contractor-dashboard/services/arl-affiliation-class.service';\nimport { CompensationFundService } from '@contractor-dashboard/services/compensation-fund.service';\nimport { InitialReportDocumentationService } from '@contractor-dashboard/services/initial-report-documentation.service';\nimport { PeriodService } from '@contractor-dashboard/services/period.service';\nimport { SocialSecurityContributionService } from '@contractor-dashboard/services/social-security-contribution.service';\nimport { AlertService } from '@shared/services/alert.service';\n\nimport { CurrencyPipe } from '@angular/common';\nimport {\n  Component,\n  EventEmitter,\n  Input,\n  OnChanges,\n  OnInit,\n  Output,\n  SimpleChanges,\n} from '@angular/core';\nimport {\n  FormBuilder,\n  FormGroup,\n  ReactiveFormsModule,\n  Validators,\n} from '@angular/forms';\nimport { MatButton, MatIconButton } from '@angular/material/button';\nimport { MatCheckbox } from '@angular/material/checkbox';\nimport { MatOption } from '@angular/material/core';\nimport { MatError, MatFormField, MatLabel } from '@angular/material/form-field';\nimport { MatIcon } from '@angular/material/icon';\nimport { MatInput } from '@angular/material/input';\nimport { MatSelect } from '@angular/material/select';\nimport { MatTooltip } from '@angular/material/tooltip';\nimport { NgxCurrencyDirective } from 'ngx-currency';\n\nimport { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';\n\n@Component({\n  selector: 'app-monthly-report-social-security-information',\n  templateUrl: './monthly-report-social-security-information.component.html',\n  styleUrl: './monthly-report-social-security-information.component.scss',\n  standalone: true,\n  imports: [\n    ReactiveFormsModule,\n    MatIcon,\n    MatFormField,\n    MatLabel,\n    MatInput,\n    MatSelect,\n    MatOption,\n    MatError,\n    MatCheckbox,\n    MatButton,\n    MatIconButton,\n    MatTooltip,\n    CurrencyPipe,\n    NgxCurrencyDirective,\n  ],\n})\nexport class MonthlyReportSocialSecurityInformationComponent\n  implements OnInit, OnChanges\n{\n  @Input() socialSecurityContribution: SocialSecurityContribution | null = null;\n  @Input() contractorContractId!: number;\n  @Input() report!: MonthlyReport;\n  @Input() initialReportDocumentation: InitialReportDocumentation | null = null;\n  @Input() isSupervisor = false;\n  @Output() saveSocialSecurity = new EventEmitter<SocialSecurityContribution>();\n  @Output() formValidityChange = new EventEmitter<boolean>();\n\n  socialSecurityForm: FormGroup = this.fb.group({\n    paymentFormNumber: [\n      null,\n      this.isSupervisor ? [] : [Validators.required, Validators.min(0)],\n    ],\n    certificateFile: [null, this.isSupervisor ? [] : [Validators.required]],\n    arlAffiliationClass: [null, this.isSupervisor ? [] : [Validators.required]],\n    arlContribution: [\n      null,\n      this.isSupervisor ? [] : [Validators.required, Validators.min(0)],\n    ],\n    hasCompensationFund: [false],\n    compensationFund: [{ value: null, disabled: this.isSupervisor }],\n    compensationFundContribution: [\n      { value: null, disabled: this.isSupervisor },\n    ],\n  });\n  fileName = '';\n  contract: Contract | null = null;\n  previousPaymentValue = 0;\n  ibc = 0;\n  healthContribution = 0;\n  pensionContribution = 0;\n  arlAffiliationClasses: ARLAffiliationClass[] = [];\n  compensationFunds: CompensationFund[] = [];\n  selectedFile: File | null = null;\n\n  constructor(\n    private readonly fb: FormBuilder,\n    private readonly contractService: ContractService,\n    private readonly initialReportDocumentationService: InitialReportDocumentationService,\n    private readonly alert: AlertService,\n    private readonly arlAffiliationClassService: ArlAffiliationClassService,\n    private readonly compensationFundService: CompensationFundService,\n    private readonly socialSecurityContributionService: SocialSecurityContributionService,\n    private readonly periodService: PeriodService,\n    private readonly MonthlyReportService: MonthlyReportService,\n  ) {\n    if (this.isSupervisor) {\n      this.socialSecurityForm.get('hasCompensationFund')?.disable();\n    }\n  }\n\n  private updateCompensationFundValidation(checked: boolean): void {\n    const compensationFundControl =\n      this.socialSecurityForm.get('compensationFund');\n    const compensationFundContributionControl = this.socialSecurityForm.get(\n      'compensationFundContribution',\n    );\n\n    if (checked) {\n      compensationFundControl?.setValidators(Validators.required);\n      compensationFundContributionControl?.setValidators([\n        Validators.required,\n        Validators.min(0),\n      ]);\n    } else {\n      compensationFundControl?.clearValidators();\n      compensationFundContributionControl?.clearValidators();\n      compensationFundControl?.setValue(null);\n      compensationFundContributionControl?.setValue(null);\n    }\n\n    compensationFundControl?.updateValueAndValidity({ emitEvent: false });\n    compensationFundContributionControl?.updateValueAndValidity({\n      emitEvent: false,\n    });\n    this.socialSecurityForm.updateValueAndValidity();\n  }\n\n  ngOnInit(): void {\n    this.initializeForm();\n    this.loadData();\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    if (changes['socialSecurityContribution']) {\n      this.initializeForm();\n    }\n  }\n\n  private initializeForm(): void {\n    const certificateFileValidator = this.isSupervisor\n      ? []\n      : [Validators.required];\n\n    if (\n      !this.isSupervisor &&\n      this.socialSecurityContribution?.certificateFileUrl\n    ) {\n      certificateFileValidator.length = 0;\n    }\n\n    this.socialSecurityForm = this.fb.group({\n      paymentFormNumber: [\n        this.socialSecurityContribution?.paymentFormNumber || null,\n        this.isSupervisor ? [] : [Validators.required, Validators.min(0)],\n      ],\n      certificateFile: [null, certificateFileValidator],\n      arlAffiliationClass: [\n        this.socialSecurityContribution?.arlAffiliationClassId || null,\n        this.isSupervisor ? [] : [Validators.required],\n      ],\n      arlContribution: [\n        this.socialSecurityContribution?.arlContribution || null,\n        this.isSupervisor ? [] : [Validators.required, Validators.min(0)],\n      ],\n      hasCompensationFund: [\n        {\n          value: Boolean(this.socialSecurityContribution?.compensationFundId),\n          disabled: this.isSupervisor,\n        },\n      ],\n      compensationFund: [\n        this.socialSecurityContribution?.compensationFundId || null,\n        { value: null, disabled: this.isSupervisor },\n      ],\n      compensationFundContribution: [\n        this.socialSecurityContribution?.compensationFundContribution || null,\n        { value: null, disabled: this.isSupervisor },\n      ],\n    });\n\n    this.updateFormValidity();\n  }\n\n  loadData(): void {\n    this.loadInitialReportDocumentation();\n    this.loadARLAffiliationClasses();\n    this.loadCompensationFunds();\n    this.loadSocialSecurityContribution();\n    this.loadPeriodPayment();\n\n    this.socialSecurityForm.statusChanges.subscribe(() => {\n      this.formValidityChange.emit(\n        this.isSupervisor ? true : this.socialSecurityForm.valid,\n      );\n    });\n\n    this.socialSecurityForm\n      .get('hasCompensationFund')\n      ?.valueChanges.subscribe((checked) => {\n        if (!this.isSupervisor) {\n          this.updateCompensationFundValidation(checked);\n        }\n      });\n  }\n\n  loadInitialReportDocumentation(): void {\n    this.initialReportDocumentationService\n      .getByContractorContractId(this.contractorContractId)\n      .subscribe({\n        next: (data) => (this.initialReportDocumentation = data),\n        error: (error) => {\n          if (error.status !== 404) {\n            this.alert.error(error.error?.detail ?? 'Error al cargar la documentación inicial');\n          }\n        },\n      });\n  }\n\n  calculateContributions(): void {\n    this.ibc = this.previousPaymentValue * 0.4;\n    this.healthContribution = (this.ibc * 12.5) / 100;\n    this.pensionContribution = (this.ibc * 16) / 100;\n  }\n\n  onFileSelected(event: Event): void {\n    const file = (event.target as HTMLInputElement).files?.[0];\n    if (file) {\n      if (file.type === 'application/pdf') {\n        if (file.size <= 1024 * 1024) {\n          this.selectedFile = file;\n          this.fileName = file.name;\n          this.socialSecurityForm.patchValue({ certificateFile: file });\n          this.socialSecurityForm\n            .get('certificateFile')\n            ?.setValidators([Validators.required]);\n        } else {\n          this.alert.error('El archivo no debe superar 1MB');\n        }\n      } else {\n        this.alert.error('Solo se permiten archivos PDF');\n      }\n      this.socialSecurityForm.get('certificateFile')?.updateValueAndValidity();\n      this.updateFormValidity();\n    }\n  }\n\n  private updateFormValidity(): void {\n    const formValid =\n      this.isSupervisor ||\n      this.socialSecurityForm.valid ||\n      (this.socialSecurityForm.get('certificateFile')?.value === null &&\n        Boolean(this.socialSecurityContribution?.certificateFileUrl));\n\n    this.formValidityChange.emit(formValid);\n  }\n\n  onSave(): void {\n    const updatedSocialSecurity = {\n      ...this.socialSecurityContribution,\n      ...this.socialSecurityForm.value,\n      healthContribution: this.healthContribution,\n      pensionContribution: this.pensionContribution,\n    };\n    this.saveSocialSecurity.emit(updatedSocialSecurity);\n  }\n\n  loadARLAffiliationClasses(): void {\n    this.arlAffiliationClassService.getAll().subscribe({\n      next: (classes) => (this.arlAffiliationClasses = classes),\n      error: (error) => {\n        this.alert.error(error.error?.detail ?? 'Error al cargar las clases de afiliación ARL');\n      },\n    });\n  }\n\n  loadCompensationFunds(): void {\n    this.compensationFundService.getAll().subscribe({\n      next: (funds) => (this.compensationFunds = funds),\n      error: (error) => {\n        this.alert.error(error.error?.detail ?? 'Error al cargar las cajas de compensación');\n      },\n    });\n  }\n\n  loadSocialSecurityContribution(): void {\n    if (this.report && this.report.id) {\n      this.socialSecurityContributionService\n        .getByMonthlyReportId(this.report.id)\n        .subscribe({\n          next: (socialSecurity) => {\n            this.socialSecurityContribution = socialSecurity;\n            this.prefillForm();\n          },\n          error: (error) => {\n            if (error.status !== 404) {\n              this.alert.error(\n                error.error?.detail ?? 'Error al cargar la información de seguridad social',\n              );\n            }\n          },\n        });\n    }\n  }\n\n  prefillForm(): void {\n    if (this.socialSecurityContribution) {\n      const hasCompensationFund =\n        !!this.socialSecurityContribution.compensationFund;\n\n      if (hasCompensationFund) {\n        const compensationFundControl =\n          this.socialSecurityForm.get('compensationFund');\n        const compensationFundContributionControl = this.socialSecurityForm.get(\n          'compensationFundContribution',\n        );\n\n        compensationFundControl?.setValidators(Validators.required);\n        compensationFundContributionControl?.setValidators([\n          Validators.required,\n          Validators.min(0),\n        ]);\n      }\n\n      this.socialSecurityForm.patchValue(\n        {\n          paymentFormNumber: this.socialSecurityContribution.paymentFormNumber,\n          arlAffiliationClass:\n            this.socialSecurityContribution.arlAffiliationClass?.id,\n          arlContribution: this.socialSecurityContribution.arlContribution,\n          hasCompensationFund: hasCompensationFund,\n          compensationFund:\n            this.socialSecurityContribution.compensationFund?.id,\n          compensationFundContribution:\n            this.socialSecurityContribution.compensationFundContribution,\n        },\n        { emitEvent: false },\n      );\n\n      Object.keys(this.socialSecurityForm.controls).forEach((key) => {\n        const control = this.socialSecurityForm.get(key);\n        control?.updateValueAndValidity({ emitEvent: false });\n      });\n\n      this.socialSecurityForm.updateValueAndValidity();\n    }\n  }\n\n  getSocialSecurityData(): SocialSecurityContribution {\n    const formValue = this.socialSecurityForm.value;\n    return {\n      ...this.socialSecurityContribution,\n      ...formValue,\n      monthlyReportId: this.report.id || 0,\n      healthContribution: this.healthContribution,\n      pensionContribution: this.pensionContribution,\n      arlContribution: formValue.arlContribution || 0,\n      ibc: this.ibc,\n      arlAffiliationClassId: formValue.arlAffiliationClass,\n      compensationFundId: formValue.hasCompensationFund\n        ? formValue.compensationFund\n        : null,\n      compensationFundContribution: formValue.hasCompensationFund\n        ? formValue.compensationFundContribution\n        : null,\n    };\n  }\n\n  loadPeriodPayment(): void {\n    if (this.report && this.contractorContractId) {\n      this.MonthlyReportService.getPreviousPeriodPayment(\n        this.contractorContractId,\n        this.report.reportNumber,\n      ).subscribe({\n        next: (payment) => {\n          this.previousPaymentValue = payment || 0;\n          this.calculateContributions();\n        },\n        error: (error) => {\n          this.alert.error(\n            error.error?.detail ?? 'Error al cargar el valor a cobrar del periodo anterior',\n          );\n        },\n      });\n    }\n  }\n\n  downloadCertificate(): void {\n    if (this.socialSecurityContribution?.certificateFileUrl) {\n      window.open(this.socialSecurityContribution.certificateFileUrl, '_blank');\n    }\n  }\n\n  getArlAffiliationClassName(id: number): string {\n    return (\n      this.arlAffiliationClasses.find((arlClass) => arlClass.id === id)?.name ||\n      ''\n    );\n  }\n\n  getCompensationFundName(id: number): string {\n    return this.compensationFunds.find((fund) => fund.id === id)?.name || '';\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAgCS;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;AA/BT,SAASE,eAAe,QAAQ,gDAAgD;AAMhF,SAASC,0BAA0B,QAAQ,8DAA8D;AACzG,SAASC,uBAAuB,QAAQ,0DAA0D;AAClG,SAASC,iCAAiC,QAAQ,qEAAqE;AACvH,SAASC,aAAa,QAAQ,+CAA+C;AAC7E,SAASC,iCAAiC,QAAQ,qEAAqE;AACvH,SAASC,YAAY,QAAQ,gCAAgC;AAE7D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SACEC,SAAS,EACTC,YAAY,EACZC,KAAK,EAGLC,MAAM,QAED,eAAe;AACtB,SACEC,WAAW,EAEXC,mBAAmB,EACnBC,UAAU,QACL,gBAAgB;AACvB,SAASC,SAAS,EAAEC,aAAa,QAAQ,0BAA0B;AACnE,SAASC,WAAW,QAAQ,4BAA4B;AACxD,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,QAAQ,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,8BAA8B;AAC/E,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,oBAAoB,QAAQ,cAAc;AAEnD,SAASC,oBAAoB,QAAQ,uDAAuD;AAAC7B,cAAA,GAAA8B,CAAA;AAwBtF,IAAMC,+CAA+C,GAArD,MAAMA,+CAA+C;EAsC1DC,YACmBC,EAAe,EACfC,eAAgC,EAChCC,iCAAoE,EACpEC,KAAmB,EACnBC,0BAAsD,EACtDC,uBAAgD,EAChDC,iCAAoE,EACpEC,aAA4B,EAC5BX,oBAA0C;IAAA7B,cAAA,GAAAyC,CAAA;IAAAzC,cAAA,GAAA8B,CAAA;IAR1C,KAAAG,EAAE,GAAFA,EAAE;IAAajC,cAAA,GAAA8B,CAAA;IACf,KAAAI,eAAe,GAAfA,eAAe;IAAiBlC,cAAA,GAAA8B,CAAA;IAChC,KAAAK,iCAAiC,GAAjCA,iCAAiC;IAAmCnC,cAAA,GAAA8B,CAAA;IACpE,KAAAM,KAAK,GAALA,KAAK;IAAcpC,cAAA,GAAA8B,CAAA;IACnB,KAAAO,0BAA0B,GAA1BA,0BAA0B;IAA4BrC,cAAA,GAAA8B,CAAA;IACtD,KAAAQ,uBAAuB,GAAvBA,uBAAuB;IAAyBtC,cAAA,GAAA8B,CAAA;IAChD,KAAAS,iCAAiC,GAAjCA,iCAAiC;IAAmCvC,cAAA,GAAA8B,CAAA;IACpE,KAAAU,aAAa,GAAbA,aAAa;IAAexC,cAAA,GAAA8B,CAAA;IAC5B,KAAAD,oBAAoB,GAApBA,oBAAoB;IAAsB7B,cAAA,GAAA8B,CAAA;IA5CpD,KAAAY,0BAA0B,GAAsC,IAAI;IAAA1C,cAAA,GAAA8B,CAAA;IAGpE,KAAAa,0BAA0B,GAAsC,IAAI;IAAA3C,cAAA,GAAA8B,CAAA;IACpE,KAAAc,YAAY,GAAG,KAAK;IAAA5C,cAAA,GAAA8B,CAAA;IACnB,KAAAe,kBAAkB,GAAG,IAAIlC,YAAY,EAA8B;IAAAX,cAAA,GAAA8B,CAAA;IACnE,KAAAgB,kBAAkB,GAAG,IAAInC,YAAY,EAAW;IAAAX,cAAA,GAAA8B,CAAA;IAE1D,KAAAiB,kBAAkB,GAAc,IAAI,CAACd,EAAE,CAACe,KAAK,CAAC;MAC5CC,iBAAiB,EAAE,CACjB,IAAI,EACJ,IAAI,CAACL,YAAY,IAAA5C,cAAA,GAAAkD,CAAA,UAAG,EAAE,KAAAlD,cAAA,GAAAkD,CAAA,UAAG,CAAClC,UAAU,CAACmC,QAAQ,EAAEnC,UAAU,CAACoC,GAAG,CAAC,CAAC,CAAC,CAAC,EAClE;MACDC,eAAe,EAAE,CAAC,IAAI,EAAE,IAAI,CAACT,YAAY,IAAA5C,cAAA,GAAAkD,CAAA,UAAG,EAAE,KAAAlD,cAAA,GAAAkD,CAAA,UAAG,CAAClC,UAAU,CAACmC,QAAQ,CAAC,EAAC;MACvEG,mBAAmB,EAAE,CAAC,IAAI,EAAE,IAAI,CAACV,YAAY,IAAA5C,cAAA,GAAAkD,CAAA,UAAG,EAAE,KAAAlD,cAAA,GAAAkD,CAAA,UAAG,CAAClC,UAAU,CAACmC,QAAQ,CAAC,EAAC;MAC3EI,eAAe,EAAE,CACf,IAAI,EACJ,IAAI,CAACX,YAAY,IAAA5C,cAAA,GAAAkD,CAAA,UAAG,EAAE,KAAAlD,cAAA,GAAAkD,CAAA,UAAG,CAAClC,UAAU,CAACmC,QAAQ,EAAEnC,UAAU,CAACoC,GAAG,CAAC,CAAC,CAAC,CAAC,EAClE;MACDI,mBAAmB,EAAE,CAAC,KAAK,CAAC;MAC5BC,gBAAgB,EAAE,CAAC;QAAEC,KAAK,EAAE,IAAI;QAAEC,QAAQ,EAAE,IAAI,CAACf;MAAY,CAAE,CAAC;MAChEgB,4BAA4B,EAAE,CAC5B;QAAEF,KAAK,EAAE,IAAI;QAAEC,QAAQ,EAAE,IAAI,CAACf;MAAY,CAAE;KAE/C,CAAC;IAAC5C,cAAA,GAAA8B,CAAA;IACH,KAAA+B,QAAQ,GAAG,EAAE;IAAC7D,cAAA,GAAA8B,CAAA;IACd,KAAAgC,QAAQ,GAAoB,IAAI;IAAC9D,cAAA,GAAA8B,CAAA;IACjC,KAAAiC,oBAAoB,GAAG,CAAC;IAAC/D,cAAA,GAAA8B,CAAA;IACzB,KAAAkC,GAAG,GAAG,CAAC;IAAChE,cAAA,GAAA8B,CAAA;IACR,KAAAmC,kBAAkB,GAAG,CAAC;IAACjE,cAAA,GAAA8B,CAAA;IACvB,KAAAoC,mBAAmB,GAAG,CAAC;IAAClE,cAAA,GAAA8B,CAAA;IACxB,KAAAqC,qBAAqB,GAA0B,EAAE;IAACnE,cAAA,GAAA8B,CAAA;IAClD,KAAAsC,iBAAiB,GAAuB,EAAE;IAACpE,cAAA,GAAA8B,CAAA;IAC3C,KAAAuC,YAAY,GAAgB,IAAI;IAACrE,cAAA,GAAA8B,CAAA;IAa/B,IAAI,IAAI,CAACc,YAAY,EAAE;MAAA5C,cAAA,GAAAkD,CAAA;MAAAlD,cAAA,GAAA8B,CAAA;MACrB,IAAI,CAACiB,kBAAkB,CAACuB,GAAG,CAAC,qBAAqB,CAAC,EAAEC,OAAO,EAAE;IAC/D,CAAC;MAAAvE,cAAA,GAAAkD,CAAA;IAAA;EACH;EAEQsB,gCAAgCA,CAACC,OAAgB;IAAAzE,cAAA,GAAAyC,CAAA;IACvD,MAAMiC,uBAAuB,IAAA1E,cAAA,GAAA8B,CAAA,QAC3B,IAAI,CAACiB,kBAAkB,CAACuB,GAAG,CAAC,kBAAkB,CAAC;IACjD,MAAMK,mCAAmC,IAAA3E,cAAA,GAAA8B,CAAA,QAAG,IAAI,CAACiB,kBAAkB,CAACuB,GAAG,CACrE,8BAA8B,CAC/B;IAACtE,cAAA,GAAA8B,CAAA;IAEF,IAAI2C,OAAO,EAAE;MAAAzE,cAAA,GAAAkD,CAAA;MAAAlD,cAAA,GAAA8B,CAAA;MACX4C,uBAAuB,EAAEE,aAAa,CAAC5D,UAAU,CAACmC,QAAQ,CAAC;MAACnD,cAAA,GAAA8B,CAAA;MAC5D6C,mCAAmC,EAAEC,aAAa,CAAC,CACjD5D,UAAU,CAACmC,QAAQ,EACnBnC,UAAU,CAACoC,GAAG,CAAC,CAAC,CAAC,CAClB,CAAC;IACJ,CAAC,MAAM;MAAApD,cAAA,GAAAkD,CAAA;MAAAlD,cAAA,GAAA8B,CAAA;MACL4C,uBAAuB,EAAEG,eAAe,EAAE;MAAC7E,cAAA,GAAA8B,CAAA;MAC3C6C,mCAAmC,EAAEE,eAAe,EAAE;MAAC7E,cAAA,GAAA8B,CAAA;MACvD4C,uBAAuB,EAAEI,QAAQ,CAAC,IAAI,CAAC;MAAC9E,cAAA,GAAA8B,CAAA;MACxC6C,mCAAmC,EAAEG,QAAQ,CAAC,IAAI,CAAC;IACrD;IAAC9E,cAAA,GAAA8B,CAAA;IAED4C,uBAAuB,EAAEK,sBAAsB,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAE,CAAC;IAAChF,cAAA,GAAA8B,CAAA;IACtE6C,mCAAmC,EAAEI,sBAAsB,CAAC;MAC1DC,SAAS,EAAE;KACZ,CAAC;IAAChF,cAAA,GAAA8B,CAAA;IACH,IAAI,CAACiB,kBAAkB,CAACgC,sBAAsB,EAAE;EAClD;EAEAE,QAAQA,CAAA;IAAAjF,cAAA,GAAAyC,CAAA;IAAAzC,cAAA,GAAA8B,CAAA;IACN,IAAI,CAACoD,cAAc,EAAE;IAAClF,cAAA,GAAA8B,CAAA;IACtB,IAAI,CAACqD,QAAQ,EAAE;EACjB;EAEAC,WAAWA,CAACC,OAAsB;IAAArF,cAAA,GAAAyC,CAAA;IAAAzC,cAAA,GAAA8B,CAAA;IAChC,IAAIuD,OAAO,CAAC,4BAA4B,CAAC,EAAE;MAAArF,cAAA,GAAAkD,CAAA;MAAAlD,cAAA,GAAA8B,CAAA;MACzC,IAAI,CAACoD,cAAc,EAAE;IACvB,CAAC;MAAAlF,cAAA,GAAAkD,CAAA;IAAA;EACH;EAEQgC,cAAcA,CAAA;IAAAlF,cAAA,GAAAyC,CAAA;IACpB,MAAM6C,wBAAwB,IAAAtF,cAAA,GAAA8B,CAAA,QAAG,IAAI,CAACc,YAAY,IAAA5C,cAAA,GAAAkD,CAAA,UAC9C,EAAE,KAAAlD,cAAA,GAAAkD,CAAA,UACF,CAAClC,UAAU,CAACmC,QAAQ,CAAC;IAACnD,cAAA,GAAA8B,CAAA;IAE1B,IACE,CAAA9B,cAAA,GAAAkD,CAAA,WAAC,IAAI,CAACN,YAAY,MAAA5C,cAAA,GAAAkD,CAAA,UAClB,IAAI,CAACR,0BAA0B,EAAE6C,kBAAkB,GACnD;MAAAvF,cAAA,GAAAkD,CAAA;MAAAlD,cAAA,GAAA8B,CAAA;MACAwD,wBAAwB,CAACE,MAAM,GAAG,CAAC;IACrC,CAAC;MAAAxF,cAAA,GAAAkD,CAAA;IAAA;IAAAlD,cAAA,GAAA8B,CAAA;IAED,IAAI,CAACiB,kBAAkB,GAAG,IAAI,CAACd,EAAE,CAACe,KAAK,CAAC;MACtCC,iBAAiB,EAAE,CACjB,CAAAjD,cAAA,GAAAkD,CAAA,eAAI,CAACR,0BAA0B,EAAEO,iBAAiB,MAAAjD,cAAA,GAAAkD,CAAA,WAAI,IAAI,GAC1D,IAAI,CAACN,YAAY,IAAA5C,cAAA,GAAAkD,CAAA,WAAG,EAAE,KAAAlD,cAAA,GAAAkD,CAAA,WAAG,CAAClC,UAAU,CAACmC,QAAQ,EAAEnC,UAAU,CAACoC,GAAG,CAAC,CAAC,CAAC,CAAC,EAClE;MACDC,eAAe,EAAE,CAAC,IAAI,EAAEiC,wBAAwB,CAAC;MACjDhC,mBAAmB,EAAE,CACnB,CAAAtD,cAAA,GAAAkD,CAAA,eAAI,CAACR,0BAA0B,EAAE+C,qBAAqB,MAAAzF,cAAA,GAAAkD,CAAA,WAAI,IAAI,GAC9D,IAAI,CAACN,YAAY,IAAA5C,cAAA,GAAAkD,CAAA,WAAG,EAAE,KAAAlD,cAAA,GAAAkD,CAAA,WAAG,CAAClC,UAAU,CAACmC,QAAQ,CAAC,EAC/C;MACDI,eAAe,EAAE,CACf,CAAAvD,cAAA,GAAAkD,CAAA,eAAI,CAACR,0BAA0B,EAAEa,eAAe,MAAAvD,cAAA,GAAAkD,CAAA,WAAI,IAAI,GACxD,IAAI,CAACN,YAAY,IAAA5C,cAAA,GAAAkD,CAAA,WAAG,EAAE,KAAAlD,cAAA,GAAAkD,CAAA,WAAG,CAAClC,UAAU,CAACmC,QAAQ,EAAEnC,UAAU,CAACoC,GAAG,CAAC,CAAC,CAAC,CAAC,EAClE;MACDI,mBAAmB,EAAE,CACnB;QACEE,KAAK,EAAEgC,OAAO,CAAC,IAAI,CAAChD,0BAA0B,EAAEiD,kBAAkB,CAAC;QACnEhC,QAAQ,EAAE,IAAI,CAACf;OAChB,CACF;MACDa,gBAAgB,EAAE,CAChB,CAAAzD,cAAA,GAAAkD,CAAA,eAAI,CAACR,0BAA0B,EAAEiD,kBAAkB,MAAA3F,cAAA,GAAAkD,CAAA,WAAI,IAAI,GAC3D;QAAEQ,KAAK,EAAE,IAAI;QAAEC,QAAQ,EAAE,IAAI,CAACf;MAAY,CAAE,CAC7C;MACDgB,4BAA4B,EAAE,CAC5B,CAAA5D,cAAA,GAAAkD,CAAA,eAAI,CAACR,0BAA0B,EAAEkB,4BAA4B,MAAA5D,cAAA,GAAAkD,CAAA,WAAI,IAAI,GACrE;QAAEQ,KAAK,EAAE,IAAI;QAAEC,QAAQ,EAAE,IAAI,CAACf;MAAY,CAAE;KAE/C,CAAC;IAAC5C,cAAA,GAAA8B,CAAA;IAEH,IAAI,CAAC8D,kBAAkB,EAAE;EAC3B;EAEAT,QAAQA,CAAA;IAAAnF,cAAA,GAAAyC,CAAA;IAAAzC,cAAA,GAAA8B,CAAA;IACN,IAAI,CAAC+D,8BAA8B,EAAE;IAAC7F,cAAA,GAAA8B,CAAA;IACtC,IAAI,CAACgE,yBAAyB,EAAE;IAAC9F,cAAA,GAAA8B,CAAA;IACjC,IAAI,CAACiE,qBAAqB,EAAE;IAAC/F,cAAA,GAAA8B,CAAA;IAC7B,IAAI,CAACkE,8BAA8B,EAAE;IAAChG,cAAA,GAAA8B,CAAA;IACtC,IAAI,CAACmE,iBAAiB,EAAE;IAACjG,cAAA,GAAA8B,CAAA;IAEzB,IAAI,CAACiB,kBAAkB,CAACmD,aAAa,CAACC,SAAS,CAAC,MAAK;MAAAnG,cAAA,GAAAyC,CAAA;MAAAzC,cAAA,GAAA8B,CAAA;MACnD,IAAI,CAACgB,kBAAkB,CAACsD,IAAI,CAC1B,IAAI,CAACxD,YAAY,IAAA5C,cAAA,GAAAkD,CAAA,WAAG,IAAI,KAAAlD,cAAA,GAAAkD,CAAA,WAAG,IAAI,CAACH,kBAAkB,CAACsD,KAAK,EACzD;IACH,CAAC,CAAC;IAACrG,cAAA,GAAA8B,CAAA;IAEH,IAAI,CAACiB,kBAAkB,CACpBuB,GAAG,CAAC,qBAAqB,CAAC,EACzBgC,YAAY,CAACH,SAAS,CAAE1B,OAAO,IAAI;MAAAzE,cAAA,GAAAyC,CAAA;MAAAzC,cAAA,GAAA8B,CAAA;MACnC,IAAI,CAAC,IAAI,CAACc,YAAY,EAAE;QAAA5C,cAAA,GAAAkD,CAAA;QAAAlD,cAAA,GAAA8B,CAAA;QACtB,IAAI,CAAC0C,gCAAgC,CAACC,OAAO,CAAC;MAChD,CAAC;QAAAzE,cAAA,GAAAkD,CAAA;MAAA;IACH,CAAC,CAAC;EACN;EAEA2C,8BAA8BA,CAAA;IAAA7F,cAAA,GAAAyC,CAAA;IAAAzC,cAAA,GAAA8B,CAAA;IAC5B,IAAI,CAACK,iCAAiC,CACnCoE,yBAAyB,CAAC,IAAI,CAACC,oBAAoB,CAAC,CACpDL,SAAS,CAAC;MACTM,IAAI,EAAGC,IAAI,IAAM;QAAA1G,cAAA,GAAAyC,CAAA;QAAAzC,cAAA,GAAA8B,CAAA;QAAA,WAAI,CAACa,0BAA0B,GAAG+D,IAAI;MAAJ,CAAK;MACxDC,KAAK,EAAGA,KAAK,IAAI;QAAA3G,cAAA,GAAAyC,CAAA;QAAAzC,cAAA,GAAA8B,CAAA;QACf,IAAI6E,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;UAAA5G,cAAA,GAAAkD,CAAA;UAAAlD,cAAA,GAAA8B,CAAA;UACxB,IAAI,CAACM,KAAK,CAACuE,KAAK,CAAC,CAAA3G,cAAA,GAAAkD,CAAA,WAAAyD,KAAK,CAACA,KAAK,EAAEE,MAAM,MAAA7G,cAAA,GAAAkD,CAAA,WAAI,0CAA0C,EAAC;QACrF,CAAC;UAAAlD,cAAA,GAAAkD,CAAA;QAAA;MACH;KACD,CAAC;EACN;EAEA4D,sBAAsBA,CAAA;IAAA9G,cAAA,GAAAyC,CAAA;IAAAzC,cAAA,GAAA8B,CAAA;IACpB,IAAI,CAACkC,GAAG,GAAG,IAAI,CAACD,oBAAoB,GAAG,GAAG;IAAC/D,cAAA,GAAA8B,CAAA;IAC3C,IAAI,CAACmC,kBAAkB,GAAI,IAAI,CAACD,GAAG,GAAG,IAAI,GAAI,GAAG;IAAChE,cAAA,GAAA8B,CAAA;IAClD,IAAI,CAACoC,mBAAmB,GAAI,IAAI,CAACF,GAAG,GAAG,EAAE,GAAI,GAAG;EAClD;EAEA+C,cAAcA,CAACC,KAAY;IAAAhH,cAAA,GAAAyC,CAAA;IACzB,MAAMwE,IAAI,IAAAjH,cAAA,GAAA8B,CAAA,QAAIkF,KAAK,CAACE,MAA2B,CAACC,KAAK,GAAG,CAAC,CAAC;IAACnH,cAAA,GAAA8B,CAAA;IAC3D,IAAImF,IAAI,EAAE;MAAAjH,cAAA,GAAAkD,CAAA;MAAAlD,cAAA,GAAA8B,CAAA;MACR,IAAImF,IAAI,CAACG,IAAI,KAAK,iBAAiB,EAAE;QAAApH,cAAA,GAAAkD,CAAA;QAAAlD,cAAA,GAAA8B,CAAA;QACnC,IAAImF,IAAI,CAACI,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;UAAArH,cAAA,GAAAkD,CAAA;UAAAlD,cAAA,GAAA8B,CAAA;UAC5B,IAAI,CAACuC,YAAY,GAAG4C,IAAI;UAACjH,cAAA,GAAA8B,CAAA;UACzB,IAAI,CAAC+B,QAAQ,GAAGoD,IAAI,CAACK,IAAI;UAACtH,cAAA,GAAA8B,CAAA;UAC1B,IAAI,CAACiB,kBAAkB,CAACwE,UAAU,CAAC;YAAElE,eAAe,EAAE4D;UAAI,CAAE,CAAC;UAACjH,cAAA,GAAA8B,CAAA;UAC9D,IAAI,CAACiB,kBAAkB,CACpBuB,GAAG,CAAC,iBAAiB,CAAC,EACrBM,aAAa,CAAC,CAAC5D,UAAU,CAACmC,QAAQ,CAAC,CAAC;QAC1C,CAAC,MAAM;UAAAnD,cAAA,GAAAkD,CAAA;UAAAlD,cAAA,GAAA8B,CAAA;UACL,IAAI,CAACM,KAAK,CAACuE,KAAK,CAAC,gCAAgC,CAAC;QACpD;MACF,CAAC,MAAM;QAAA3G,cAAA,GAAAkD,CAAA;QAAAlD,cAAA,GAAA8B,CAAA;QACL,IAAI,CAACM,KAAK,CAACuE,KAAK,CAAC,+BAA+B,CAAC;MACnD;MAAC3G,cAAA,GAAA8B,CAAA;MACD,IAAI,CAACiB,kBAAkB,CAACuB,GAAG,CAAC,iBAAiB,CAAC,EAAES,sBAAsB,EAAE;MAAC/E,cAAA,GAAA8B,CAAA;MACzE,IAAI,CAAC8D,kBAAkB,EAAE;IAC3B,CAAC;MAAA5F,cAAA,GAAAkD,CAAA;IAAA;EACH;EAEQ0C,kBAAkBA,CAAA;IAAA5F,cAAA,GAAAyC,CAAA;IACxB,MAAM+E,SAAS,IAAAxH,cAAA,GAAA8B,CAAA,QACb,CAAA9B,cAAA,GAAAkD,CAAA,eAAI,CAACN,YAAY,MAAA5C,cAAA,GAAAkD,CAAA,WACjB,IAAI,CAACH,kBAAkB,CAACsD,KAAK,KAC5B,CAAArG,cAAA,GAAAkD,CAAA,eAAI,CAACH,kBAAkB,CAACuB,GAAG,CAAC,iBAAiB,CAAC,EAAEZ,KAAK,KAAK,IAAI,MAAA1D,cAAA,GAAAkD,CAAA,WAC7DwC,OAAO,CAAC,IAAI,CAAChD,0BAA0B,EAAE6C,kBAAkB,CAAC,CAAC;IAACvF,cAAA,GAAA8B,CAAA;IAElE,IAAI,CAACgB,kBAAkB,CAACsD,IAAI,CAACoB,SAAS,CAAC;EACzC;EAEAC,MAAMA,CAAA;IAAAzH,cAAA,GAAAyC,CAAA;IACJ,MAAMiF,qBAAqB,IAAA1H,cAAA,GAAA8B,CAAA,QAAG;MAC5B,GAAG,IAAI,CAACY,0BAA0B;MAClC,GAAG,IAAI,CAACK,kBAAkB,CAACW,KAAK;MAChCO,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;MAC3CC,mBAAmB,EAAE,IAAI,CAACA;KAC3B;IAAClE,cAAA,GAAA8B,CAAA;IACF,IAAI,CAACe,kBAAkB,CAACuD,IAAI,CAACsB,qBAAqB,CAAC;EACrD;EAEA5B,yBAAyBA,CAAA;IAAA9F,cAAA,GAAAyC,CAAA;IAAAzC,cAAA,GAAA8B,CAAA;IACvB,IAAI,CAACO,0BAA0B,CAACsF,MAAM,EAAE,CAACxB,SAAS,CAAC;MACjDM,IAAI,EAAGmB,OAAO,IAAM;QAAA5H,cAAA,GAAAyC,CAAA;QAAAzC,cAAA,GAAA8B,CAAA;QAAA,WAAI,CAACqC,qBAAqB,GAAGyD,OAAO;MAAP,CAAQ;MACzDjB,KAAK,EAAGA,KAAK,IAAI;QAAA3G,cAAA,GAAAyC,CAAA;QAAAzC,cAAA,GAAA8B,CAAA;QACf,IAAI,CAACM,KAAK,CAACuE,KAAK,CAAC,CAAA3G,cAAA,GAAAkD,CAAA,WAAAyD,KAAK,CAACA,KAAK,EAAEE,MAAM,MAAA7G,cAAA,GAAAkD,CAAA,WAAI,8CAA8C,EAAC;MACzF;KACD,CAAC;EACJ;EAEA6C,qBAAqBA,CAAA;IAAA/F,cAAA,GAAAyC,CAAA;IAAAzC,cAAA,GAAA8B,CAAA;IACnB,IAAI,CAACQ,uBAAuB,CAACqF,MAAM,EAAE,CAACxB,SAAS,CAAC;MAC9CM,IAAI,EAAGoB,KAAK,IAAM;QAAA7H,cAAA,GAAAyC,CAAA;QAAAzC,cAAA,GAAA8B,CAAA;QAAA,WAAI,CAACsC,iBAAiB,GAAGyD,KAAK;MAAL,CAAM;MACjDlB,KAAK,EAAGA,KAAK,IAAI;QAAA3G,cAAA,GAAAyC,CAAA;QAAAzC,cAAA,GAAA8B,CAAA;QACf,IAAI,CAACM,KAAK,CAACuE,KAAK,CAAC,CAAA3G,cAAA,GAAAkD,CAAA,WAAAyD,KAAK,CAACA,KAAK,EAAEE,MAAM,MAAA7G,cAAA,GAAAkD,CAAA,WAAI,2CAA2C,EAAC;MACtF;KACD,CAAC;EACJ;EAEA8C,8BAA8BA,CAAA;IAAAhG,cAAA,GAAAyC,CAAA;IAAAzC,cAAA,GAAA8B,CAAA;IAC5B,IAAI,CAAA9B,cAAA,GAAAkD,CAAA,eAAI,CAAC4E,MAAM,MAAA9H,cAAA,GAAAkD,CAAA,WAAI,IAAI,CAAC4E,MAAM,CAACC,EAAE,GAAE;MAAA/H,cAAA,GAAAkD,CAAA;MAAAlD,cAAA,GAAA8B,CAAA;MACjC,IAAI,CAACS,iCAAiC,CACnCyF,oBAAoB,CAAC,IAAI,CAACF,MAAM,CAACC,EAAE,CAAC,CACpC5B,SAAS,CAAC;QACTM,IAAI,EAAGwB,cAAc,IAAI;UAAAjI,cAAA,GAAAyC,CAAA;UAAAzC,cAAA,GAAA8B,CAAA;UACvB,IAAI,CAACY,0BAA0B,GAAGuF,cAAc;UAACjI,cAAA,GAAA8B,CAAA;UACjD,IAAI,CAACoG,WAAW,EAAE;QACpB,CAAC;QACDvB,KAAK,EAAGA,KAAK,IAAI;UAAA3G,cAAA,GAAAyC,CAAA;UAAAzC,cAAA,GAAA8B,CAAA;UACf,IAAI6E,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;YAAA5G,cAAA,GAAAkD,CAAA;YAAAlD,cAAA,GAAA8B,CAAA;YACxB,IAAI,CAACM,KAAK,CAACuE,KAAK,CACd,CAAA3G,cAAA,GAAAkD,CAAA,WAAAyD,KAAK,CAACA,KAAK,EAAEE,MAAM,MAAA7G,cAAA,GAAAkD,CAAA,WAAI,oDAAoD,EAC5E;UACH,CAAC;YAAAlD,cAAA,GAAAkD,CAAA;UAAA;QACH;OACD,CAAC;IACN,CAAC;MAAAlD,cAAA,GAAAkD,CAAA;IAAA;EACH;EAEAgF,WAAWA,CAAA;IAAAlI,cAAA,GAAAyC,CAAA;IAAAzC,cAAA,GAAA8B,CAAA;IACT,IAAI,IAAI,CAACY,0BAA0B,EAAE;MAAA1C,cAAA,GAAAkD,CAAA;MACnC,MAAMM,mBAAmB,IAAAxD,cAAA,GAAA8B,CAAA,QACvB,CAAC,CAAC,IAAI,CAACY,0BAA0B,CAACe,gBAAgB;MAACzD,cAAA,GAAA8B,CAAA;MAErD,IAAI0B,mBAAmB,EAAE;QAAAxD,cAAA,GAAAkD,CAAA;QACvB,MAAMwB,uBAAuB,IAAA1E,cAAA,GAAA8B,CAAA,QAC3B,IAAI,CAACiB,kBAAkB,CAACuB,GAAG,CAAC,kBAAkB,CAAC;QACjD,MAAMK,mCAAmC,IAAA3E,cAAA,GAAA8B,CAAA,QAAG,IAAI,CAACiB,kBAAkB,CAACuB,GAAG,CACrE,8BAA8B,CAC/B;QAACtE,cAAA,GAAA8B,CAAA;QAEF4C,uBAAuB,EAAEE,aAAa,CAAC5D,UAAU,CAACmC,QAAQ,CAAC;QAACnD,cAAA,GAAA8B,CAAA;QAC5D6C,mCAAmC,EAAEC,aAAa,CAAC,CACjD5D,UAAU,CAACmC,QAAQ,EACnBnC,UAAU,CAACoC,GAAG,CAAC,CAAC,CAAC,CAClB,CAAC;MACJ,CAAC;QAAApD,cAAA,GAAAkD,CAAA;MAAA;MAAAlD,cAAA,GAAA8B,CAAA;MAED,IAAI,CAACiB,kBAAkB,CAACwE,UAAU,CAChC;QACEtE,iBAAiB,EAAE,IAAI,CAACP,0BAA0B,CAACO,iBAAiB;QACpEK,mBAAmB,EACjB,IAAI,CAACZ,0BAA0B,CAACY,mBAAmB,EAAEyE,EAAE;QACzDxE,eAAe,EAAE,IAAI,CAACb,0BAA0B,CAACa,eAAe;QAChEC,mBAAmB,EAAEA,mBAAmB;QACxCC,gBAAgB,EACd,IAAI,CAACf,0BAA0B,CAACe,gBAAgB,EAAEsE,EAAE;QACtDnE,4BAA4B,EAC1B,IAAI,CAAClB,0BAA0B,CAACkB;OACnC,EACD;QAAEoB,SAAS,EAAE;MAAK,CAAE,CACrB;MAAChF,cAAA,GAAA8B,CAAA;MAEFqG,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrF,kBAAkB,CAACsF,QAAQ,CAAC,CAACC,OAAO,CAAEC,GAAG,IAAI;QAAAvI,cAAA,GAAAyC,CAAA;QAC5D,MAAM+F,OAAO,IAAAxI,cAAA,GAAA8B,CAAA,SAAG,IAAI,CAACiB,kBAAkB,CAACuB,GAAG,CAACiE,GAAG,CAAC;QAACvI,cAAA,GAAA8B,CAAA;QACjD0G,OAAO,EAAEzD,sBAAsB,CAAC;UAAEC,SAAS,EAAE;QAAK,CAAE,CAAC;MACvD,CAAC,CAAC;MAAChF,cAAA,GAAA8B,CAAA;MAEH,IAAI,CAACiB,kBAAkB,CAACgC,sBAAsB,EAAE;IAClD,CAAC;MAAA/E,cAAA,GAAAkD,CAAA;IAAA;EACH;EAEAuF,qBAAqBA,CAAA;IAAAzI,cAAA,GAAAyC,CAAA;IACnB,MAAMiG,SAAS,IAAA1I,cAAA,GAAA8B,CAAA,SAAG,IAAI,CAACiB,kBAAkB,CAACW,KAAK;IAAC1D,cAAA,GAAA8B,CAAA;IAChD,OAAO;MACL,GAAG,IAAI,CAACY,0BAA0B;MAClC,GAAGgG,SAAS;MACZC,eAAe,EAAE,CAAA3I,cAAA,GAAAkD,CAAA,eAAI,CAAC4E,MAAM,CAACC,EAAE,MAAA/H,cAAA,GAAAkD,CAAA,WAAI,CAAC;MACpCe,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;MAC3CC,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CX,eAAe,EAAE,CAAAvD,cAAA,GAAAkD,CAAA,WAAAwF,SAAS,CAACnF,eAAe,MAAAvD,cAAA,GAAAkD,CAAA,WAAI,CAAC;MAC/Cc,GAAG,EAAE,IAAI,CAACA,GAAG;MACbyB,qBAAqB,EAAEiD,SAAS,CAACpF,mBAAmB;MACpDqC,kBAAkB,EAAE+C,SAAS,CAAClF,mBAAmB,IAAAxD,cAAA,GAAAkD,CAAA,WAC7CwF,SAAS,CAACjF,gBAAgB,KAAAzD,cAAA,GAAAkD,CAAA,WAC1B,IAAI;MACRU,4BAA4B,EAAE8E,SAAS,CAAClF,mBAAmB,IAAAxD,cAAA,GAAAkD,CAAA,WACvDwF,SAAS,CAAC9E,4BAA4B,KAAA5D,cAAA,GAAAkD,CAAA,WACtC,IAAI;KACT;EACH;EAEA+C,iBAAiBA,CAAA;IAAAjG,cAAA,GAAAyC,CAAA;IAAAzC,cAAA,GAAA8B,CAAA;IACf,IAAI,CAAA9B,cAAA,GAAAkD,CAAA,eAAI,CAAC4E,MAAM,MAAA9H,cAAA,GAAAkD,CAAA,WAAI,IAAI,CAACsD,oBAAoB,GAAE;MAAAxG,cAAA,GAAAkD,CAAA;MAAAlD,cAAA,GAAA8B,CAAA;MAC5C,IAAI,CAACD,oBAAoB,CAAC+G,wBAAwB,CAChD,IAAI,CAACpC,oBAAoB,EACzB,IAAI,CAACsB,MAAM,CAACe,YAAY,CACzB,CAAC1C,SAAS,CAAC;QACVM,IAAI,EAAGqC,OAAO,IAAI;UAAA9I,cAAA,GAAAyC,CAAA;UAAAzC,cAAA,GAAA8B,CAAA;UAChB,IAAI,CAACiC,oBAAoB,GAAG,CAAA/D,cAAA,GAAAkD,CAAA,WAAA4F,OAAO,MAAA9I,cAAA,GAAAkD,CAAA,WAAI,CAAC;UAAClD,cAAA,GAAA8B,CAAA;UACzC,IAAI,CAACgF,sBAAsB,EAAE;QAC/B,CAAC;QACDH,KAAK,EAAGA,KAAK,IAAI;UAAA3G,cAAA,GAAAyC,CAAA;UAAAzC,cAAA,GAAA8B,CAAA;UACf,IAAI,CAACM,KAAK,CAACuE,KAAK,CACd,CAAA3G,cAAA,GAAAkD,CAAA,WAAAyD,KAAK,CAACA,KAAK,EAAEE,MAAM,MAAA7G,cAAA,GAAAkD,CAAA,WAAI,wDAAwD,EAChF;QACH;OACD,CAAC;IACJ,CAAC;MAAAlD,cAAA,GAAAkD,CAAA;IAAA;EACH;EAEA6F,mBAAmBA,CAAA;IAAA/I,cAAA,GAAAyC,CAAA;IAAAzC,cAAA,GAAA8B,CAAA;IACjB,IAAI,IAAI,CAACY,0BAA0B,EAAE6C,kBAAkB,EAAE;MAAAvF,cAAA,GAAAkD,CAAA;MAAAlD,cAAA,GAAA8B,CAAA;MACvDkH,MAAM,CAACC,IAAI,CAAC,IAAI,CAACvG,0BAA0B,CAAC6C,kBAAkB,EAAE,QAAQ,CAAC;IAC3E,CAAC;MAAAvF,cAAA,GAAAkD,CAAA;IAAA;EACH;EAEAgG,0BAA0BA,CAACnB,EAAU;IAAA/H,cAAA,GAAAyC,CAAA;IAAAzC,cAAA,GAAA8B,CAAA;IACnC,OACE,CAAA9B,cAAA,GAAAkD,CAAA,eAAI,CAACiB,qBAAqB,CAACgF,IAAI,CAAEC,QAAQ,IAAK;MAAApJ,cAAA,GAAAyC,CAAA;MAAAzC,cAAA,GAAA8B,CAAA;MAAA,OAAAsH,QAAQ,CAACrB,EAAE,KAAKA,EAAE;IAAF,CAAE,CAAC,EAAET,IAAI,MAAAtH,cAAA,GAAAkD,CAAA,WACvE,EAAE;EAEN;EAEAmG,uBAAuBA,CAACtB,EAAU;IAAA/H,cAAA,GAAAyC,CAAA;IAAAzC,cAAA,GAAA8B,CAAA;IAChC,OAAO,CAAA9B,cAAA,GAAAkD,CAAA,eAAI,CAACkB,iBAAiB,CAAC+E,IAAI,CAAEG,IAAI,IAAK;MAAAtJ,cAAA,GAAAyC,CAAA;MAAAzC,cAAA,GAAA8B,CAAA;MAAA,OAAAwH,IAAI,CAACvB,EAAE,KAAKA,EAAE;IAAF,CAAE,CAAC,EAAET,IAAI,MAAAtH,cAAA,GAAAkD,CAAA,WAAI,EAAE;EAC1E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;cAhWCtC;MAAK;;cACLA;MAAK;;cACLA;MAAK;;cACLA;MAAK;;cACLA;MAAK;;cACLC;MAAM;;cACNA;MAAM;;;;;AATIkB,+CAA+C,GAAAwH,UAAA,EAtB3D7I,SAAS,CAAC;EACT8I,QAAQ,EAAE,gDAAgD;EAC1DC,QAAA,EAAAC,oBAA0E;EAE1EC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP7I,mBAAmB,EACnBS,OAAO,EACPF,YAAY,EACZC,QAAQ,EACRE,QAAQ,EACRC,SAAS,EACTN,SAAS,EACTC,QAAQ,EACRF,WAAW,EACXF,SAAS,EACTC,aAAa,EACbS,UAAU,EACVlB,YAAY,EACZmB,oBAAoB,CACrB;;CACF,CAAC,C,EACWG,+CAA+C,CAoW3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}