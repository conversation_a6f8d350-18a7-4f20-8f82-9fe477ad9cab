import { HttpClientTestingModule } from '@angular/common/http/testing';
import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { MatDialog } from '@angular/material/dialog';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ContractorContractService } from '@contract-management/services/contractor-contract.service';
import { MonthlyReport } from '@contractor-dashboard/models/monthly-report.model';
import { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';
import { ReportReviewStatusService } from '@contractor-dashboard/services/report-review-status.service';
import { PeriodService } from '@contractor-dashboard/services/period.service';
import { AlertService } from '@shared/services/alert.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { of, throwError } from 'rxjs';
import { MonthlyReportDialogComponent } from './monthly-report-dialog/monthly-report-dialog.component';
import { MonthlyReportsTabComponent } from './monthly-reports-tab.component';
import { AuthService } from '@core/auth/services/auth.service';
import { User } from '@core/auth/models/user.model';
import { RouterTestingModule } from '@angular/router/testing';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { SimpleChanges, SimpleChange } from '@angular/core';
import { ReportReviewHistoryService } from '@contractor-dashboard/services/report-review-history.service';
import { ReportReviewHistory } from '@contractor-dashboard/models/report-review-history.model';

describe('MonthlyReportsTabComponent', () => {
  let component: MonthlyReportsTabComponent;
  let fixture: ComponentFixture<MonthlyReportsTabComponent>;
  let monthlyReportService: jasmine.SpyObj<MonthlyReportService>;
  let contractorContractService: jasmine.SpyObj<ContractorContractService>;
  let reportReviewStatusService: jasmine.SpyObj<ReportReviewStatusService>;
  let periodService: jasmine.SpyObj<PeriodService>;
  let reportReviewHistoryService: jasmine.SpyObj<ReportReviewHistoryService>;
  let spinner: jasmine.SpyObj<NgxSpinnerService>;
  let dialog: jasmine.SpyObj<MatDialog>;
  let alertService: jasmine.SpyObj<AlertService>;
  let authService: jasmine.SpyObj<AuthService>;

  const mockReport: MonthlyReport = {
    id: 1,
    reportNumber: 1,
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-01-31'),
    creationDate: new Date('2024-01-01'),
    contractorContractId: 1,
    contractorContract: {
      id: 1,
      subscriptionDate: '2024-01-01',
      contractStartDate: '2024-01-01',
      contract: {
        id: 1,
        contractNumber: 123,
        monthlyPayment: 1000000,
        object: 'Test contract',
        rup: true,
        secopCode: 123456,
        addition: false,
        cession: false,
        settled: false,
        status: {
          id: 1,
          name: 'Active',
        },
        causesSelectionId: 1,
        managementSupportId: 1,
        contractClassId: 1,
        contractYear: {
          id: 1,
          year: 2024,
        },
      },
    },
  };

  const mockReviewHistory: ReportReviewHistory = {
    id: 1,
    monthlyReportId: 1,
    reviewDate: new Date('2024-01-01'),
    reviewStatusId: 1,
    comment: 'Informe creado',
    reviewerId: 1,
    reviewStatus: {
      id: 1,
      name: 'Borrador',
    },
  };

  beforeEach(async () => {
    monthlyReportService = jasmine.createSpyObj('MonthlyReportService', [
      'getByContractorContractId',
      'create',
      'downloadPdf',
    ]);
    contractorContractService = jasmine.createSpyObj(
      'ContractorContractService',
      ['getById'],
    );
    reportReviewStatusService = jasmine.createSpyObj(
      'ReportReviewStatusService',
      ['getByName'],
    );
    periodService = jasmine.createSpyObj('PeriodService', ['create']);
    reportReviewHistoryService = jasmine.createSpyObj(
      'ReportReviewHistoryService',
      ['create'],
    );
    authService = jasmine.createSpyObj('AuthService', [
      'getUserProfiles',
      'getCurrentUser',
    ]);
    const mockUser: User = {
      id: 1,
      username: 'testuser',
      profiles: [],
    };
    authService.getCurrentUser.and.returnValue(mockUser);
    dialog = jasmine.createSpyObj('MatDialog', ['open']);
    alertService = jasmine.createSpyObj('AlertService', [
      'success',
      'error',
      'warning',
    ]);
    spinner = jasmine.createSpyObj('NgxSpinnerService', ['show', 'hide']);

    await TestBed.configureTestingModule({
      imports: [
        RouterTestingModule,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        MonthlyReportsTabComponent,
        MatTableModule,
        MatPaginatorModule,
        MatSortModule,
        MatButtonModule,
        MatIconModule,
      ],
      providers: [
        { provide: MonthlyReportService, useValue: monthlyReportService },
        {
          provide: ContractorContractService,
          useValue: contractorContractService,
        },
        {
          provide: ReportReviewStatusService,
          useValue: reportReviewStatusService,
        },
        { provide: PeriodService, useValue: periodService },
        {
          provide: ReportReviewHistoryService,
          useValue: reportReviewHistoryService,
        },
        { provide: AuthService, useValue: authService },
        { provide: MatDialog, useValue: dialog },
        { provide: AlertService, useValue: alertService },
        { provide: NgxSpinnerService, useValue: spinner },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(MonthlyReportsTabComponent);
    component = fixture.componentInstance;

    component.contractorContractId = 1;
    component.contractStartDate = new Date('2024-01-01');
    component.monthlyReports = [mockReport];

    contractorContractService.getById.and.returnValue(
      of({
        id: 1,
        subscriptionDate: '2024-01-01',
        contractStartDate: '2024-01-01',
        contract: {
          id: 1,
          contractNumber: 123,
          monthlyPayment: 1000000,
          object: 'Test contract',
          rup: true,
          secopCode: 123456,
          addition: false,
          cession: false,
          settled: false,
          status: {
            id: 1,
            name: 'Active',
          },
          causesSelectionId: 1,
          managementSupportId: 1,
          contractClassId: 1,
          contractYear: {
            id: 1,
            year: 2024,
          },
        },
      }),
    );

    reportReviewStatusService.getByName.and.returnValue(
      of({
        id: 1,
        name: 'Borrador',
      }),
    );

    periodService.create.and.returnValue(
      of({
        num_payment: 1,
        payment: 1000000,
        start_date: '2024-01-01',
        end_date: '2024-01-31',
        days_in_month: 31,
      }),
    );

    reportReviewHistoryService.create.and.returnValue(of(mockReviewHistory));

    monthlyReportService.getByContractorContractId.and.returnValue(
      of([mockReport]),
    );
    monthlyReportService.create.and.returnValue(of(mockReport));

    const dialogRef = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
    dialogRef.afterClosed.and.returnValue(of(true));
    dialog.open.and.returnValue(dialogRef);

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should update reports on changes', () => {
    const updatedReports = [{ ...mockReport, id: 2, reportNumber: 2 }];

    component.monthlyReports = updatedReports;

    const changes: SimpleChanges = {
      monthlyReports: new SimpleChange(null, updatedReports, true),
    };

    component.ngOnChanges(changes);

    expect(component.monthlyReports).toEqual(updatedReports);

    expect(component.dataSource.data).toEqual(updatedReports);
  });

  it('should open report details form', fakeAsync(() => {
    const dialogRef = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
    dialogRef.afterClosed.and.returnValue(of(true));
    dialog.open.and.returnValue(dialogRef);

    component.openReportDetailsForm(mockReport);
    tick();

    expect(dialog.open).toHaveBeenCalledWith(MonthlyReportDialogComponent, {
      width: '90vw',
      height: '90vh',
      maxWidth: '100vw',
      maxHeight: '100vh',
      data: { report: mockReport },
    });
    expect(monthlyReportService.getByContractorContractId).toHaveBeenCalled();
  }));

  it('should not create report without contractStartDate', fakeAsync(() => {
    component.contractStartDate = undefined;
    component.createNewReport();
    tick();

    expect(alertService.warning).toHaveBeenCalledWith(
      'No se puede crear un informe sin un contrato asociado',
    );
    expect(spinner.hide).toHaveBeenCalled();
  }));

  it('should create new report successfully', fakeAsync(() => {
    const mockCreatedReport: MonthlyReport = {
      ...mockReport,
      id: 2,
      reportNumber: 2,
    };

    contractorContractService.getById.and.returnValue(
      of(mockReport.contractorContract!),
    );
    reportReviewStatusService.getByName.and.returnValue(
      of({ id: 1, name: 'Borrador' }),
    );
    periodService.create.and.returnValue(
      of({
        num_payment: 2,
        payment: 1000000,
        start_date: '2024-02-01',
        end_date: '2024-02-29',
        days_in_month: 29,
      }),
    );
    reportReviewHistoryService.create.and.returnValue(of(mockReviewHistory));
    monthlyReportService.create.and.returnValue(of(mockCreatedReport));
    monthlyReportService.getByContractorContractId.and.returnValue(
      of([mockReport, mockCreatedReport]),
    );

    const dialogRef = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
    dialogRef.afterClosed.and.returnValue(of(true));
    dialog.open.and.returnValue(dialogRef);

    component.createNewReport();
    tick(2000);

    expect(spinner.show).toHaveBeenCalled();
    expect(contractorContractService.getById).toHaveBeenCalledWith(1);
    expect(periodService.create).toHaveBeenCalled();
    expect(reportReviewStatusService.getByName).toHaveBeenCalledWith(
      'Borrador',
    );
    expect(monthlyReportService.create).toHaveBeenCalled();
    expect(reportReviewHistoryService.create).toHaveBeenCalled();
    expect(monthlyReportService.getByContractorContractId).toHaveBeenCalledWith(
      1,
    );
  }));

  it('should handle error when getting contract', fakeAsync(() => {
    contractorContractService.getById.and.returnValue(
      throwError(() => new Error()),
    );

    component.createNewReport();
    tick();

    expect(alertService.error).toHaveBeenCalledWith(
      'Error al crear el nuevo informe.',
    );
    expect(spinner.hide).toHaveBeenCalled();
  }));

  it('should handle error when creating period', fakeAsync(() => {
    periodService.create.and.returnValue(throwError(() => new Error()));

    component.createNewReport();
    tick();

    expect(alertService.error).toHaveBeenCalledWith(
      'Error al crear el nuevo informe.',
    );
    expect(spinner.hide).toHaveBeenCalled();
  }));

  it('should handle error when getting review status', fakeAsync(() => {
    reportReviewStatusService.getByName.and.returnValue(
      throwError(() => new Error()),
    );

    component.createNewReport();
    tick();

    expect(alertService.error).toHaveBeenCalledWith(
      'Error al crear el nuevo informe.',
    );
    expect(spinner.hide).toHaveBeenCalled();
  }));

  it('should handle error when creating report', fakeAsync(() => {
    monthlyReportService.create.and.returnValue(throwError(() => new Error()));

    component.createNewReport();
    tick();

    expect(alertService.error).toHaveBeenCalledWith(
      'Error al crear el nuevo informe.',
    );
    expect(spinner.hide).toHaveBeenCalled();
  }));

  it('should handle error when creating review history', fakeAsync(() => {
    const mockCreatedReport: MonthlyReport = {
      ...mockReport,
      id: 2,
      reportNumber: 2,
    };

    monthlyReportService.create.and.returnValue(of(mockCreatedReport));

    reportReviewHistoryService.create.and.returnValue(
      throwError(() => new Error()),
    );

    component.createNewReport();
    tick(1000);

    expect(monthlyReportService.getByContractorContractId).toHaveBeenCalled();
  }));

  it('should apply filter to data source', () => {
    const event = { target: { value: 'test' } } as unknown as Event;
    component.applyFilter(event);
    expect(component.dataSource.filter).toBe('test');
  });

  it('should initialize paginator and sort after view init', () => {
    component.ngAfterViewInit();
    expect(component.dataSource.paginator).toBeTruthy();
    expect(component.dataSource.sort).toBeTruthy();
  });
});
