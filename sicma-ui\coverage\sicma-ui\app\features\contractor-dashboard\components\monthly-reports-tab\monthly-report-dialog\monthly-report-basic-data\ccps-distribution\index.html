
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for app/features/contractor-dashboard/components/monthly-reports-tab/monthly-report-dialog/monthly-report-basic-data/ccps-distribution</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../../../../../index.html">All files</a> app/features/contractor-dashboard/components/monthly-reports-tab/monthly-report-dialog/monthly-report-basic-data/ccps-distribution</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">92.5% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>74/80</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">75.75% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>25/33</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>27/27</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">93.5% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>72/77</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="ccps-distribution.component.ts"><a href="ccps-distribution.component.ts.html">ccps-distribution.component.ts</a></td>
	<td data-value="92.5" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 92%"></div><div class="cover-empty" style="width: 8%"></div></div>
	</td>
	<td data-value="92.5" class="pct high">92.5%</td>
	<td data-value="80" class="abs high">74/80</td>
	<td data-value="75.75" class="pct medium">75.75%</td>
	<td data-value="33" class="abs medium">25/33</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="27" class="abs high">27/27</td>
	<td data-value="93.5" class="pct high">93.5%</td>
	<td data-value="77" class="abs high">72/77</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-15T22:00:55.217Z
            </div>
        <script src="../../../../../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../../../../../sorter.js"></script>
        <script src="../../../../../../../../block-navigation.js"></script>
    </body>
</html>
    