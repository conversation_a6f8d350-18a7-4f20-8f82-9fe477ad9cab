import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { ManagementSupport } from '@contract-management/models/management-support.model';
import { environment } from '@env';
import { ManagementSupportService } from './management-support.service';

describe('ManagementSupportService', () => {
  let service: ManagementSupportService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/management_support`;

  const mockManagementSupport: ManagementSupport = {
    id: 1,
    name: 'Test Management Support',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ManagementSupportService],
    });
    service = TestBed.inject(ManagementSupportService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all management supports', () => {
      const mockManagementSupports = [mockManagementSupport];

      service.getAll().subscribe((managementSupports) => {
        expect(managementSupports).toEqual(mockManagementSupports);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockManagementSupports);
    });
  });

  describe('getById', () => {
    it('should return a management support by id', () => {
      const id = 1;

      service.getById(id).subscribe((managementSupport) => {
        expect(managementSupport).toEqual(mockManagementSupport);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockManagementSupport);
    });
  });

  describe('create', () => {
    it('should create a new management support', () => {
      const newManagementSupport: Omit<ManagementSupport, 'id'> = {
        name: 'New Management Support',
      };

      service.create(newManagementSupport).subscribe((managementSupport) => {
        expect(managementSupport).toEqual(mockManagementSupport);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newManagementSupport);
      req.flush(mockManagementSupport);
    });
  });

  describe('update', () => {
    it('should update a management support', () => {
      const id = 1;
      const updateData: Partial<ManagementSupport> = {
        name: 'Updated Management Support',
      };

      service.update(id, updateData).subscribe((managementSupport) => {
        expect(managementSupport).toEqual(mockManagementSupport);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockManagementSupport);
    });
  });

  describe('delete', () => {
    it('should delete a management support', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });

  describe('getByName', () => {
    it('should return a management support by name', () => {
      const name = 'Test Management Support';

      service.getByName(name).subscribe((managementSupport) => {
        expect(managementSupport).toEqual(mockManagementSupport);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockManagementSupport);
    });
  });
});