import { DatePipe } from '@angular/common';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import {
  ComponentFixture,
  TestBed,
  discardPeriodicTasks,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ContractValuesService } from '@contract-management/services/contract-values.service';
import { SuspensionService } from '@contract-management/services/suspension.service';
import { AlertService } from '@shared/services/alert.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { of, throwError } from 'rxjs';
import { SuspensionDialogComponent } from './suspension-dialog.component';

describe('SuspensionDialogComponent', () => {
  let component: SuspensionDialogComponent;
  let fixture: ComponentFixture<SuspensionDialogComponent>;
  let dialogRef: jasmine.SpyObj<MatDialogRef<SuspensionDialogComponent>>;
  let contractValuesService: jasmine.SpyObj<ContractValuesService>;
  let suspensionService: jasmine.SpyObj<SuspensionService>;
  let alertService: jasmine.SpyObj<AlertService>;
  let spinnerService: jasmine.SpyObj<NgxSpinnerService>;

  const mockDialogData = {
    contractId: 1,
    lastSuspensionEndDate: null,
  };

  beforeEach(async () => {
    dialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
    contractValuesService = jasmine.createSpyObj('ContractValuesService', [
      'getLatestEndDateByContractId',
      'getStartDateByContractId',
    ]);
    suspensionService = jasmine.createSpyObj('SuspensionService', [
      'create',
      'update',
    ]);
    alertService = jasmine.createSpyObj('AlertService', ['success', 'error']);
    spinnerService = jasmine.createSpyObj('NgxSpinnerService', [
      'show',
      'hide',
    ]);

    contractValuesService.getLatestEndDateByContractId.and.returnValue(
      of(new Date('2024-12-31')),
    );
    contractValuesService.getStartDateByContractId.and.returnValue(
      of(new Date('2024-01-01')),
    );

    await TestBed.configureTestingModule({
      imports: [
        SuspensionDialogComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        ReactiveFormsModule,
        MatDialogModule,
        MatButtonModule,
        MatFormFieldModule,
        MatInputModule,
        MatIconModule,
        MatDatepickerModule,
        DatePipe,
      ],
      providers: [
        FormBuilder,
        { provide: MatDialogRef, useValue: dialogRef },
        { provide: MAT_DIALOG_DATA, useValue: mockDialogData },
        { provide: ContractValuesService, useValue: contractValuesService },
        { provide: SuspensionService, useValue: suspensionService },
        { provide: AlertService, useValue: alertService },
        { provide: NgxSpinnerService, useValue: spinnerService },
        provideNativeDateAdapter(),
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(SuspensionDialogComponent);
    component = fixture.componentInstance;
  });

  it('should load contract dates on init', fakeAsync(() => {
    component.ngOnInit();
    tick();
    fixture.detectChanges();

    expect(
      contractValuesService.getLatestEndDateByContractId,
    ).toHaveBeenCalledWith(1);
    expect(contractValuesService.getStartDateByContractId).toHaveBeenCalledWith(
      1,
    );
    expect(component.contractEndDate).toEqual(new Date('2024-12-31'));
    expect(component.contractStartDate).toEqual(new Date('2024-01-01'));
  }));

  it('should handle error when loading contract dates', fakeAsync(() => {
    contractValuesService.getLatestEndDateByContractId.and.returnValue(
      throwError(() => new Error()),
    );
    contractValuesService.getStartDateByContractId.and.returnValue(
      throwError(() => new Error()),
    );

    component.ngOnInit();
    tick();
    fixture.detectChanges();

    expect(alertService.error).toHaveBeenCalledWith(
      'Error al obtener las fechas del contrato',
    );
  }));

  it('should validate form fields', fakeAsync(() => {
    component.ngOnInit();
    tick();
    fixture.detectChanges();

    expect(component.suspensionForm.valid).toBeFalse();

    component.suspensionForm.patchValue({
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-01-31'),
      reason: 'Test reason that is long enough',
    });

    tick();
    fixture.detectChanges();
    tick();
    fixture.detectChanges();

    expect(component.suspensionForm.valid).toBeTrue();
  }));

  it('should handle error when creating suspension', fakeAsync(() => {
    suspensionService.create.and.returnValue(throwError(() => new Error()));

    component.ngOnInit();
    tick();
    fixture.detectChanges();

    component.suspensionForm.patchValue({
      startDate: new Date('2024-02-20'),
      endDate: new Date('2024-03-20'),
      reason: 'New suspension reason',
    });

    component.updateSuspensionDays();
    tick();
    fixture.detectChanges();

    component.onSubmit();
    tick();

    expect(alertService.error).toHaveBeenCalledWith(
      'Error al crear la suspensión',
    );
    expect(spinnerService.hide).toHaveBeenCalled();
  }));

  afterEach(fakeAsync(() => {
    tick();
    discardPeriodicTasks();
  }));
});