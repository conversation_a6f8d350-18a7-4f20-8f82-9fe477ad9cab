
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for app/features/contractor-dashboard/components/monthly-reports-tab/monthly-report-dialog/contractor-basic-information</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../../../../index.html">All files</a> app/features/contractor-dashboard/components/monthly-reports-tab/monthly-report-dialog/contractor-basic-information</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">62.32% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>91/146</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">48.75% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>39/80</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">52.77% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>19/36</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">63.38% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>90/142</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="contractor-basic-information.component.ts"><a href="contractor-basic-information.component.ts.html">contractor-basic-information.component.ts</a></td>
	<td data-value="62.32" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 62%"></div><div class="cover-empty" style="width: 38%"></div></div>
	</td>
	<td data-value="62.32" class="pct medium">62.32%</td>
	<td data-value="146" class="abs medium">91/146</td>
	<td data-value="48.75" class="pct low">48.75%</td>
	<td data-value="80" class="abs low">39/80</td>
	<td data-value="52.77" class="pct medium">52.77%</td>
	<td data-value="36" class="abs medium">19/36</td>
	<td data-value="63.38" class="pct medium">63.38%</td>
	<td data-value="142" class="abs medium">90/142</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-15T22:07:42.555Z
            </div>
        <script src="../../../../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../../../../sorter.js"></script>
        <script src="../../../../../../../block-navigation.js"></script>
    </body>
</html>
    