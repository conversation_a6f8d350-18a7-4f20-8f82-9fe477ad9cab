
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for app/features/contract-management/pages/contracts-list-page</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../../index.html">All files</a> app/features/contract-management/pages/contracts-list-page</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">89.04% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>65/73</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">72.41% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>21/29</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">92.59% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>25/27</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">88.88% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>64/72</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="contracts-list-page.component.ts"><a href="contracts-list-page.component.ts.html">contracts-list-page.component.ts</a></td>
	<td data-value="89.04" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 89%"></div><div class="cover-empty" style="width: 11%"></div></div>
	</td>
	<td data-value="89.04" class="pct high">89.04%</td>
	<td data-value="73" class="abs high">65/73</td>
	<td data-value="72.41" class="pct medium">72.41%</td>
	<td data-value="29" class="abs medium">21/29</td>
	<td data-value="92.59" class="pct high">92.59%</td>
	<td data-value="27" class="abs high">25/27</td>
	<td data-value="88.88" class="pct high">88.88%</td>
	<td data-value="72" class="abs high">64/72</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-16T16:46:29.714Z
            </div>
        <script src="../../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../../sorter.js"></script>
        <script src="../../../../../block-navigation.js"></script>
    </body>
</html>
    