{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { TaxRegimeService } from './tax-regime.service';\ndescribe('TaxRegimeService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/tax-regimes`;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [TaxRegimeService]\n    });\n    service = TestBed.inject(TaxRegimeService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  const mockTaxRegime = {\n    id: 1,\n    name: 'Simple'\n  };\n  describe('getAll', () => {\n    it('should return all tax regimes', () => {\n      const mockTaxRegimes = [mockTaxRegime];\n      service.getAll().subscribe(taxRegimes => {\n        expect(taxRegimes).toEqual(mockTaxRegimes);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockTaxRegimes);\n    });\n    it('should handle error when getting all tax regimes', () => {\n      service.getAll().subscribe({\n        error: error => {\n          expect(error.status).toBe(500);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Server error'), {\n        status: 500\n      });\n    });\n  });\n  describe('getById', () => {\n    it('should return a tax regime by id', () => {\n      service.getById(1).subscribe(taxRegime => {\n        expect(taxRegime).toEqual(mockTaxRegime);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockTaxRegime);\n    });\n    it('should handle error when getting tax regime by id', () => {\n      service.getById(999).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/999`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n  describe('create', () => {\n    const newTaxRegime = {\n      name: 'New Regime'\n    };\n    it('should create a new tax regime', () => {\n      service.create(newTaxRegime).subscribe(taxRegime => {\n        expect(taxRegime).toEqual(mockTaxRegime);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newTaxRegime);\n      req.flush(mockTaxRegime);\n    });\n    it('should handle error when creating tax regime', () => {\n      service.create(newTaxRegime).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n  describe('update', () => {\n    const updateData = {\n      name: 'Updated Name'\n    };\n    it('should update a tax regime', () => {\n      service.update(1, updateData).subscribe(taxRegime => {\n        expect(taxRegime).toEqual({\n          ...mockTaxRegime,\n          ...updateData\n        });\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush({\n        ...mockTaxRegime,\n        ...updateData\n      });\n    });\n    it('should handle error when updating tax regime', () => {\n      service.update(1, updateData).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n  describe('delete', () => {\n    it('should delete a tax regime', () => {\n      service.delete(1).subscribe(response => {\n        expect(response).toBeNull();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n    it('should handle error when deleting tax regime', () => {\n      service.delete(1).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n  describe('getByName', () => {\n    it('should return a tax regime by name', () => {\n      const name = 'Simple';\n      service.getByName(name).subscribe(taxRegime => {\n        expect(taxRegime).toEqual(mockTaxRegime);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockTaxRegime);\n    });\n    it('should handle error when getting tax regime by name', () => {\n      const name = 'NonExistent';\n      service.getByName(name).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "TaxRegimeService", "describe", "service", "httpMock", "apiUrl", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockTaxRegime", "id", "name", "mockTaxRegimes", "getAll", "subscribe", "taxRegimes", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "error", "status", "ErrorEvent", "getById", "taxRegime", "newTaxRegime", "create", "body", "updateData", "update", "delete", "response", "toBeNull", "getByName"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\services\\tax-regime.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { TaxRegime } from '@contractor-dashboard/models/tax-regime.model';\nimport { environment } from '@env';\nimport { TaxRegimeService } from './tax-regime.service';\n\ndescribe('TaxRegimeService', () => {\n  let service: TaxRegimeService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/tax-regimes`;\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [TaxRegimeService],\n    });\n    service = TestBed.inject(TaxRegimeService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  const mockTaxRegime: TaxRegime = {\n    id: 1,\n    name: 'Simple',\n  };\n\n  describe('getAll', () => {\n    it('should return all tax regimes', () => {\n      const mockTaxRegimes = [mockTaxRegime];\n\n      service.getAll().subscribe((taxRegimes) => {\n        expect(taxRegimes).toEqual(mockTaxRegimes);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockTaxRegimes);\n    });\n\n    it('should handle error when getting all tax regimes', () => {\n      service.getAll().subscribe({\n        error: (error) => {\n          expect(error.status).toBe(500);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Server error'), { status: 500 });\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a tax regime by id', () => {\n      service.getById(1).subscribe((taxRegime) => {\n        expect(taxRegime).toEqual(mockTaxRegime);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockTaxRegime);\n    });\n\n    it('should handle error when getting tax regime by id', () => {\n      service.getById(999).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/999`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n\n  describe('create', () => {\n    const newTaxRegime = {\n      name: 'New Regime',\n    };\n\n    it('should create a new tax regime', () => {\n      service.create(newTaxRegime).subscribe((taxRegime) => {\n        expect(taxRegime).toEqual(mockTaxRegime);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newTaxRegime);\n      req.flush(mockTaxRegime);\n    });\n\n    it('should handle error when creating tax regime', () => {\n      service.create(newTaxRegime).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n\n  describe('update', () => {\n    const updateData = {\n      name: 'Updated Name',\n    };\n\n    it('should update a tax regime', () => {\n      service.update(1, updateData).subscribe((taxRegime) => {\n        expect(taxRegime).toEqual({ ...mockTaxRegime, ...updateData });\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush({ ...mockTaxRegime, ...updateData });\n    });\n\n    it('should handle error when updating tax regime', () => {\n      service.update(1, updateData).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete a tax regime', () => {\n      service.delete(1).subscribe((response) => {\n        expect(response).toBeNull();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n\n    it('should handle error when deleting tax regime', () => {\n      service.delete(1).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n\n  describe('getByName', () => {\n    it('should return a tax regime by name', () => {\n      const name = 'Simple';\n\n      service.getByName(name).subscribe((taxRegime) => {\n        expect(taxRegime).toEqual(mockTaxRegime);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockTaxRegime);\n    });\n\n    it('should handle error when getting tax regime by name', () => {\n      const name = 'NonExistent';\n\n      service.getByName(name).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,gBAAgB,QAAQ,sBAAsB;AAEvDC,QAAQ,CAAC,kBAAkB,EAAE,MAAK;EAChC,IAAIC,OAAyB;EAC7B,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,cAAc;EAElDC,UAAU,CAAC,MAAK;IACdP,OAAO,CAACQ,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACX,uBAAuB,CAAC;MAClCY,SAAS,EAAE,CAACR,gBAAgB;KAC7B,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACW,MAAM,CAACT,gBAAgB,CAAC;IAC1CG,QAAQ,GAAGL,OAAO,CAACW,MAAM,CAACZ,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFa,SAAS,CAAC,MAAK;IACbP,QAAQ,CAACQ,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACX,OAAO,CAAC,CAACY,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEF,MAAMC,aAAa,GAAc;IAC/BC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE;GACP;EAEDhB,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBW,EAAE,CAAC,+BAA+B,EAAE,MAAK;MACvC,MAAMM,cAAc,GAAG,CAACH,aAAa,CAAC;MAEtCb,OAAO,CAACiB,MAAM,EAAE,CAACC,SAAS,CAAEC,UAAU,IAAI;QACxCR,MAAM,CAACQ,UAAU,CAAC,CAACC,OAAO,CAACJ,cAAc,CAAC;MAC5C,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCS,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,cAAc,CAAC;IAC3B,CAAC,CAAC;IAEFN,EAAE,CAAC,kDAAkD,EAAE,MAAK;MAC1DV,OAAO,CAACiB,MAAM,EAAE,CAACC,SAAS,CAAC;QACzBS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,cAAc,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC5D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBW,EAAE,CAAC,kCAAkC,EAAE,MAAK;MAC1CV,OAAO,CAAC8B,OAAO,CAAC,CAAC,CAAC,CAACZ,SAAS,CAAEa,SAAS,IAAI;QACzCpB,MAAM,CAACoB,SAAS,CAAC,CAACX,OAAO,CAACP,aAAa,CAAC;MAC1C,CAAC,CAAC;MAEF,MAAMQ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAI,CAAC;MAC7CS,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACb,aAAa,CAAC;IAC1B,CAAC,CAAC;IAEFH,EAAE,CAAC,mDAAmD,EAAE,MAAK;MAC3DV,OAAO,CAAC8B,OAAO,CAAC,GAAG,CAAC,CAACZ,SAAS,CAAC;QAC7BS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,MAAM,CAAC;MAC/CmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB,MAAMiC,YAAY,GAAG;MACnBjB,IAAI,EAAE;KACP;IAEDL,EAAE,CAAC,gCAAgC,EAAE,MAAK;MACxCV,OAAO,CAACiC,MAAM,CAACD,YAAY,CAAC,CAACd,SAAS,CAAEa,SAAS,IAAI;QACnDpB,MAAM,CAACoB,SAAS,CAAC,CAACX,OAAO,CAACP,aAAa,CAAC;MAC1C,CAAC,CAAC;MAEF,MAAMQ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCS,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCd,MAAM,CAACU,GAAG,CAACE,OAAO,CAACW,IAAI,CAAC,CAACd,OAAO,CAACY,YAAY,CAAC;MAC9CX,GAAG,CAACK,KAAK,CAACb,aAAa,CAAC;IAC1B,CAAC,CAAC;IAEFH,EAAE,CAAC,8CAA8C,EAAE,MAAK;MACtDV,OAAO,CAACiC,MAAM,CAACD,YAAY,CAAC,CAACd,SAAS,CAAC;QACrCS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB,MAAMoC,UAAU,GAAG;MACjBpB,IAAI,EAAE;KACP;IAEDL,EAAE,CAAC,4BAA4B,EAAE,MAAK;MACpCV,OAAO,CAACoC,MAAM,CAAC,CAAC,EAAED,UAAU,CAAC,CAACjB,SAAS,CAAEa,SAAS,IAAI;QACpDpB,MAAM,CAACoB,SAAS,CAAC,CAACX,OAAO,CAAC;UAAE,GAAGP,aAAa;UAAE,GAAGsB;QAAU,CAAE,CAAC;MAChE,CAAC,CAAC;MAEF,MAAMd,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAI,CAAC;MAC7CS,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCd,MAAM,CAACU,GAAG,CAACE,OAAO,CAACW,IAAI,CAAC,CAACd,OAAO,CAACe,UAAU,CAAC;MAC5Cd,GAAG,CAACK,KAAK,CAAC;QAAE,GAAGb,aAAa;QAAE,GAAGsB;MAAU,CAAE,CAAC;IAChD,CAAC,CAAC;IAEFzB,EAAE,CAAC,8CAA8C,EAAE,MAAK;MACtDV,OAAO,CAACoC,MAAM,CAAC,CAAC,EAAED,UAAU,CAAC,CAACjB,SAAS,CAAC;QACtCS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAI,CAAC;MAC7CmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBW,EAAE,CAAC,4BAA4B,EAAE,MAAK;MACpCV,OAAO,CAACqC,MAAM,CAAC,CAAC,CAAC,CAACnB,SAAS,CAAEoB,QAAQ,IAAI;QACvC3B,MAAM,CAAC2B,QAAQ,CAAC,CAACC,QAAQ,EAAE;MAC7B,CAAC,CAAC;MAEF,MAAMlB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAI,CAAC;MAC7CS,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;IAEFhB,EAAE,CAAC,8CAA8C,EAAE,MAAK;MACtDV,OAAO,CAACqC,MAAM,CAAC,CAAC,CAAC,CAACnB,SAAS,CAAC;QAC1BS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAI,CAAC;MAC7CmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,WAAW,EAAE,MAAK;IACzBW,EAAE,CAAC,oCAAoC,EAAE,MAAK;MAC5C,MAAMK,IAAI,GAAG,QAAQ;MAErBf,OAAO,CAACwC,SAAS,CAACzB,IAAI,CAAC,CAACG,SAAS,CAAEa,SAAS,IAAI;QAC9CpB,MAAM,CAACoB,SAAS,CAAC,CAACX,OAAO,CAACP,aAAa,CAAC;MAC1C,CAAC,CAAC;MAEF,MAAMQ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,SAASa,IAAI,EAAE,CAAC;MACxDJ,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACb,aAAa,CAAC;IAC1B,CAAC,CAAC;IAEFH,EAAE,CAAC,qDAAqD,EAAE,MAAK;MAC7D,MAAMK,IAAI,GAAG,aAAa;MAE1Bf,OAAO,CAACwC,SAAS,CAACzB,IAAI,CAAC,CAACG,SAAS,CAAC;QAChCS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,SAASa,IAAI,EAAE,CAAC;MACxDM,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}