import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { SelectionModality } from '@contract-management/models/selection-modality.model';
import { environment } from '@env';
import { SelectionModalityService } from './selection-modality.service';

describe('SelectionModalityService', () => {
  let service: SelectionModalityService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/selection-modalities`;

  const mockSelectionModality: SelectionModality = {
    id: 1,
    name: 'Test Selection Modality',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [SelectionModalityService],
    });
    service = TestBed.inject(SelectionModalityService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all selection modalities', () => {
      const mockSelectionModalities = [mockSelectionModality];

      service.getAll().subscribe((selectionModalities) => {
        expect(selectionModalities).toEqual(mockSelectionModalities);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockSelectionModalities);
    });
  });

  describe('getById', () => {
    it('should return a selection modality by id', () => {
      const id = 1;

      service.getById(id).subscribe((selectionModality) => {
        expect(selectionModality).toEqual(mockSelectionModality);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockSelectionModality);
    });
  });

  describe('create', () => {
    it('should create a new selection modality', () => {
      const newSelectionModality: Omit<SelectionModality, 'id'> = {
        name: 'New Selection Modality',
      };

      service.create(newSelectionModality).subscribe((selectionModality) => {
        expect(selectionModality).toEqual(mockSelectionModality);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newSelectionModality);
      req.flush(mockSelectionModality);
    });
  });

  describe('update', () => {
    it('should update a selection modality', () => {
      const id = 1;
      const updateData: Partial<SelectionModality> = {
        name: 'Updated Selection Modality',
      };

      service.update(id, updateData).subscribe((selectionModality) => {
        expect(selectionModality).toEqual(mockSelectionModality);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockSelectionModality);
    });
  });

  describe('delete', () => {
    it('should delete a selection modality', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });

  describe('getByName', () => {
    it('should return a selection modality by name', () => {
      const name = 'Test Selection Modality';

      service.getByName(name).subscribe((selectionModality) => {
        expect(selectionModality).toEqual(mockSelectionModality);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockSelectionModality);
    });
  });
});