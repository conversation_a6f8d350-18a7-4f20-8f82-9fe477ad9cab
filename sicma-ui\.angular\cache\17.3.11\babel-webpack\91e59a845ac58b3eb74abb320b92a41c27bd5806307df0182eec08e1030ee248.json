{"ast": null, "code": "function cov_2jf0spsf15() {\n  var path = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-dashboard\\\\pages\\\\contractor-contracts-list-page\\\\contractor-contracts-list-page.component.ts\";\n  var hash = \"92f558d035c0697c6d4cba9c60c05cab25da923a\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-dashboard\\\\pages\\\\contractor-contracts-list-page\\\\contractor-contracts-list-page.component.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 23,\n          column: 43\n        },\n        end: {\n          line: 145,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 25,\n          column: 8\n        },\n        end: {\n          line: 25,\n          column: 47\n        }\n      },\n      \"2\": {\n        start: {\n          line: 26,\n          column: 8\n        },\n        end: {\n          line: 26,\n          column: 39\n        }\n      },\n      \"3\": {\n        start: {\n          line: 27,\n          column: 8\n        },\n        end: {\n          line: 27,\n          column: 59\n        }\n      },\n      \"4\": {\n        start: {\n          line: 28,\n          column: 8\n        },\n        end: {\n          line: 28,\n          column: 51\n        }\n      },\n      \"5\": {\n        start: {\n          line: 29,\n          column: 8\n        },\n        end: {\n          line: 29,\n          column: 27\n        }\n      },\n      \"6\": {\n        start: {\n          line: 30,\n          column: 8\n        },\n        end: {\n          line: 30,\n          column: 31\n        }\n      },\n      \"7\": {\n        start: {\n          line: 31,\n          column: 8\n        },\n        end: {\n          line: 31,\n          column: 29\n        }\n      },\n      \"8\": {\n        start: {\n          line: 32,\n          column: 8\n        },\n        end: {\n          line: 39,\n          column: 10\n        }\n      },\n      \"9\": {\n        start: {\n          line: 40,\n          column: 8\n        },\n        end: {\n          line: 40,\n          column: 37\n        }\n      },\n      \"10\": {\n        start: {\n          line: 41,\n          column: 8\n        },\n        end: {\n          line: 41,\n          column: 51\n        }\n      },\n      \"11\": {\n        start: {\n          line: 44,\n          column: 8\n        },\n        end: {\n          line: 44,\n          column: 39\n        }\n      },\n      \"12\": {\n        start: {\n          line: 47,\n          column: 8\n        },\n        end: {\n          line: 47,\n          column: 51\n        }\n      },\n      \"13\": {\n        start: {\n          line: 48,\n          column: 8\n        },\n        end: {\n          line: 48,\n          column: 41\n        }\n      },\n      \"14\": {\n        start: {\n          line: 51,\n          column: 28\n        },\n        end: {\n          line: 51,\n          column: 46\n        }\n      },\n      \"15\": {\n        start: {\n          line: 52,\n          column: 8\n        },\n        end: {\n          line: 52,\n          column: 66\n        }\n      },\n      \"16\": {\n        start: {\n          line: 53,\n          column: 8\n        },\n        end: {\n          line: 55,\n          column: 9\n        }\n      },\n      \"17\": {\n        start: {\n          line: 54,\n          column: 12\n        },\n        end: {\n          line: 54,\n          column: 50\n        }\n      },\n      \"18\": {\n        start: {\n          line: 58,\n          column: 8\n        },\n        end: {\n          line: 58,\n          column: 62\n        }\n      },\n      \"19\": {\n        start: {\n          line: 61,\n          column: 8\n        },\n        end: {\n          line: 61,\n          column: 28\n        }\n      },\n      \"20\": {\n        start: {\n          line: 62,\n          column: 28\n        },\n        end: {\n          line: 62,\n          column: 61\n        }\n      },\n      \"21\": {\n        start: {\n          line: 63,\n          column: 8\n        },\n        end: {\n          line: 89,\n          column: 9\n        }\n      },\n      \"22\": {\n        start: {\n          line: 64,\n          column: 12\n        },\n        end: {\n          line: 84,\n          column: 15\n        }\n      },\n      \"23\": {\n        start: {\n          line: 66,\n          column: 47\n        },\n        end: {\n          line: 66,\n          column: 66\n        }\n      },\n      \"24\": {\n        start: {\n          line: 67,\n          column: 20\n        },\n        end: {\n          line: 78,\n          column: 23\n        }\n      },\n      \"25\": {\n        start: {\n          line: 69,\n          column: 45\n        },\n        end: {\n          line: 69,\n          column: 64\n        }\n      },\n      \"26\": {\n        start: {\n          line: 72,\n          column: 28\n        },\n        end: {\n          line: 72,\n          column: 87\n        }\n      },\n      \"27\": {\n        start: {\n          line: 73,\n          column: 28\n        },\n        end: {\n          line: 73,\n          column: 87\n        }\n      },\n      \"28\": {\n        start: {\n          line: 76,\n          column: 28\n        },\n        end: {\n          line: 76,\n          column: 113\n        }\n      },\n      \"29\": {\n        start: {\n          line: 81,\n          column: 20\n        },\n        end: {\n          line: 81,\n          column: 40\n        }\n      },\n      \"30\": {\n        start: {\n          line: 82,\n          column: 20\n        },\n        end: {\n          line: 82,\n          column: 84\n        }\n      },\n      \"31\": {\n        start: {\n          line: 87,\n          column: 12\n        },\n        end: {\n          line: 87,\n          column: 32\n        }\n      },\n      \"32\": {\n        start: {\n          line: 88,\n          column: 12\n        },\n        end: {\n          line: 88,\n          column: 55\n        }\n      },\n      \"33\": {\n        start: {\n          line: 92,\n          column: 8\n        },\n        end: {\n          line: 99,\n          column: 11\n        }\n      },\n      \"34\": {\n        start: {\n          line: 94,\n          column: 16\n        },\n        end: {\n          line: 94,\n          column: 51\n        }\n      },\n      \"35\": {\n        start: {\n          line: 97,\n          column: 16\n        },\n        end: {\n          line: 97,\n          column: 89\n        }\n      },\n      \"36\": {\n        start: {\n          line: 102,\n          column: 28\n        },\n        end: {\n          line: 104,\n          column: 17\n        }\n      },\n      \"37\": {\n        start: {\n          line: 102,\n          column: 56\n        },\n        end: {\n          line: 104,\n          column: 16\n        }\n      },\n      \"38\": {\n        start: {\n          line: 105,\n          column: 8\n        },\n        end: {\n          line: 130,\n          column: 11\n        }\n      },\n      \"39\": {\n        start: {\n          line: 107,\n          column: 16\n        },\n        end: {\n          line: 125,\n          column: 19\n        }\n      },\n      \"40\": {\n        start: {\n          line: 108,\n          column: 43\n        },\n        end: {\n          line: 108,\n          column: 69\n        }\n      },\n      \"41\": {\n        start: {\n          line: 109,\n          column: 41\n        },\n        end: {\n          line: 111,\n          column: 30\n        }\n      },\n      \"42\": {\n        start: {\n          line: 112,\n          column: 20\n        },\n        end: {\n          line: 124,\n          column: 22\n        }\n      },\n      \"43\": {\n        start: {\n          line: 128,\n          column: 16\n        },\n        end: {\n          line: 128,\n          column: 104\n        }\n      },\n      \"44\": {\n        start: {\n          line: 132,\n          column: 13\n        },\n        end: {\n          line: 140,\n          column: 6\n        }\n      },\n      \"45\": {\n        start: {\n          line: 132,\n          column: 41\n        },\n        end: {\n          line: 140,\n          column: 5\n        }\n      },\n      \"46\": {\n        start: {\n          line: 141,\n          column: 13\n        },\n        end: {\n          line: 144,\n          column: 6\n        }\n      },\n      \"47\": {\n        start: {\n          line: 146,\n          column: 0\n        },\n        end: {\n          line: 166,\n          column: 41\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 24,\n            column: 4\n          },\n          end: {\n            line: 24,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 24,\n            column: 112\n          },\n          end: {\n            line: 42,\n            column: 5\n          }\n        },\n        line: 24\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 43,\n            column: 4\n          },\n          end: {\n            line: 43,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 43,\n            column: 15\n          },\n          end: {\n            line: 45,\n            column: 5\n          }\n        },\n        line: 43\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 46,\n            column: 4\n          },\n          end: {\n            line: 46,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 46,\n            column: 22\n          },\n          end: {\n            line: 49,\n            column: 5\n          }\n        },\n        line: 46\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 50,\n            column: 4\n          },\n          end: {\n            line: 50,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 50,\n            column: 23\n          },\n          end: {\n            line: 56,\n            column: 5\n          }\n        },\n        line: 50\n      },\n      \"4\": {\n        name: \"(anonymous_4)\",\n        decl: {\n          start: {\n            line: 57,\n            column: 4\n          },\n          end: {\n            line: 57,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 57,\n            column: 38\n          },\n          end: {\n            line: 59,\n            column: 5\n          }\n        },\n        line: 57\n      },\n      \"5\": {\n        name: \"(anonymous_5)\",\n        decl: {\n          start: {\n            line: 60,\n            column: 4\n          },\n          end: {\n            line: 60,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 60,\n            column: 30\n          },\n          end: {\n            line: 90,\n            column: 5\n          }\n        },\n        line: 60\n      },\n      \"6\": {\n        name: \"(anonymous_6)\",\n        decl: {\n          start: {\n            line: 65,\n            column: 22\n          },\n          end: {\n            line: 65,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 65,\n            column: 38\n          },\n          end: {\n            line: 79,\n            column: 17\n          }\n        },\n        line: 65\n      },\n      \"7\": {\n        name: \"(anonymous_7)\",\n        decl: {\n          start: {\n            line: 69,\n            column: 39\n          },\n          end: {\n            line: 69,\n            column: 40\n          }\n        },\n        loc: {\n          start: {\n            line: 69,\n            column: 45\n          },\n          end: {\n            line: 69,\n            column: 64\n          }\n        },\n        line: 69\n      },\n      \"8\": {\n        name: \"(anonymous_8)\",\n        decl: {\n          start: {\n            line: 71,\n            column: 30\n          },\n          end: {\n            line: 71,\n            column: 31\n          }\n        },\n        loc: {\n          start: {\n            line: 71,\n            column: 53\n          },\n          end: {\n            line: 74,\n            column: 25\n          }\n        },\n        line: 71\n      },\n      \"9\": {\n        name: \"(anonymous_9)\",\n        decl: {\n          start: {\n            line: 75,\n            column: 31\n          },\n          end: {\n            line: 75,\n            column: 32\n          }\n        },\n        loc: {\n          start: {\n            line: 75,\n            column: 42\n          },\n          end: {\n            line: 77,\n            column: 25\n          }\n        },\n        line: 75\n      },\n      \"10\": {\n        name: \"(anonymous_10)\",\n        decl: {\n          start: {\n            line: 80,\n            column: 23\n          },\n          end: {\n            line: 80,\n            column: 24\n          }\n        },\n        loc: {\n          start: {\n            line: 80,\n            column: 29\n          },\n          end: {\n            line: 83,\n            column: 17\n          }\n        },\n        line: 80\n      },\n      \"11\": {\n        name: \"(anonymous_11)\",\n        decl: {\n          start: {\n            line: 91,\n            column: 4\n          },\n          end: {\n            line: 91,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 91,\n            column: 58\n          },\n          end: {\n            line: 100,\n            column: 5\n          }\n        },\n        line: 91\n      },\n      \"12\": {\n        name: \"(anonymous_12)\",\n        decl: {\n          start: {\n            line: 93,\n            column: 18\n          },\n          end: {\n            line: 93,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 93,\n            column: 33\n          },\n          end: {\n            line: 95,\n            column: 13\n          }\n        },\n        line: 93\n      },\n      \"13\": {\n        name: \"(anonymous_13)\",\n        decl: {\n          start: {\n            line: 96,\n            column: 19\n          },\n          end: {\n            line: 96,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 96,\n            column: 30\n          },\n          end: {\n            line: 98,\n            column: 13\n          }\n        },\n        line: 96\n      },\n      \"14\": {\n        name: \"(anonymous_14)\",\n        decl: {\n          start: {\n            line: 101,\n            column: 4\n          },\n          end: {\n            line: 101,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 101,\n            column: 34\n          },\n          end: {\n            line: 131,\n            column: 5\n          }\n        },\n        line: 101\n      },\n      \"15\": {\n        name: \"(anonymous_15)\",\n        decl: {\n          start: {\n            line: 102,\n            column: 42\n          },\n          end: {\n            line: 102,\n            column: 43\n          }\n        },\n        loc: {\n          start: {\n            line: 102,\n            column: 56\n          },\n          end: {\n            line: 104,\n            column: 16\n          }\n        },\n        line: 102\n      },\n      \"16\": {\n        name: \"(anonymous_16)\",\n        decl: {\n          start: {\n            line: 106,\n            column: 18\n          },\n          end: {\n            line: 106,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 106,\n            column: 43\n          },\n          end: {\n            line: 126,\n            column: 13\n          }\n        },\n        line: 106\n      },\n      \"17\": {\n        name: \"(anonymous_17)\",\n        decl: {\n          start: {\n            line: 107,\n            column: 53\n          },\n          end: {\n            line: 107,\n            column: 54\n          }\n        },\n        loc: {\n          start: {\n            line: 107,\n            column: 74\n          },\n          end: {\n            line: 125,\n            column: 17\n          }\n        },\n        line: 107\n      },\n      \"18\": {\n        name: \"(anonymous_18)\",\n        decl: {\n          start: {\n            line: 127,\n            column: 19\n          },\n          end: {\n            line: 127,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 127,\n            column: 30\n          },\n          end: {\n            line: 129,\n            column: 13\n          }\n        },\n        line: 127\n      },\n      \"19\": {\n        name: \"(anonymous_19)\",\n        decl: {\n          start: {\n            line: 132,\n            column: 35\n          },\n          end: {\n            line: 132,\n            column: 36\n          }\n        },\n        loc: {\n          start: {\n            line: 132,\n            column: 41\n          },\n          end: {\n            line: 140,\n            column: 5\n          }\n        },\n        line: 132\n      }\n    },\n    branchMap: {\n      \"0\": {\n        loc: {\n          start: {\n            line: 53,\n            column: 8\n          },\n          end: {\n            line: 55,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 53,\n            column: 8\n          },\n          end: {\n            line: 55,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 53\n      },\n      \"1\": {\n        loc: {\n          start: {\n            line: 63,\n            column: 8\n          },\n          end: {\n            line: 89,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 63,\n            column: 8\n          },\n          end: {\n            line: 89,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 86,\n            column: 13\n          },\n          end: {\n            line: 89,\n            column: 9\n          }\n        }],\n        line: 63\n      },\n      \"2\": {\n        loc: {\n          start: {\n            line: 72,\n            column: 54\n          },\n          end: {\n            line: 72,\n            column: 86\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 72,\n            column: 54\n          },\n          end: {\n            line: 72,\n            column: 80\n          }\n        }, {\n          start: {\n            line: 72,\n            column: 84\n          },\n          end: {\n            line: 72,\n            column: 86\n          }\n        }],\n        line: 72\n      },\n      \"3\": {\n        loc: {\n          start: {\n            line: 76,\n            column: 45\n          },\n          end: {\n            line: 76,\n            column: 111\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 76,\n            column: 45\n          },\n          end: {\n            line: 76,\n            column: 64\n          }\n        }, {\n          start: {\n            line: 76,\n            column: 68\n          },\n          end: {\n            line: 76,\n            column: 111\n          }\n        }],\n        line: 76\n      },\n      \"4\": {\n        loc: {\n          start: {\n            line: 97,\n            column: 33\n          },\n          end: {\n            line: 97,\n            column: 87\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 97,\n            column: 33\n          },\n          end: {\n            line: 97,\n            column: 52\n          }\n        }, {\n          start: {\n            line: 97,\n            column: 56\n          },\n          end: {\n            line: 97,\n            column: 87\n          }\n        }],\n        line: 97\n      },\n      \"5\": {\n        loc: {\n          start: {\n            line: 102,\n            column: 56\n          },\n          end: {\n            line: 104,\n            column: 16\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 103,\n            column: 14\n          },\n          end: {\n            line: 103,\n            column: 72\n          }\n        }, {\n          start: {\n            line: 104,\n            column: 14\n          },\n          end: {\n            line: 104,\n            column: 16\n          }\n        }],\n        line: 102\n      },\n      \"6\": {\n        loc: {\n          start: {\n            line: 109,\n            column: 41\n          },\n          end: {\n            line: 111,\n            column: 30\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 110,\n            column: 26\n          },\n          end: {\n            line: 110,\n            column: 67\n          }\n        }, {\n          start: {\n            line: 111,\n            column: 26\n          },\n          end: {\n            line: 111,\n            column: 30\n          }\n        }],\n        line: 109\n      },\n      \"7\": {\n        loc: {\n          start: {\n            line: 113,\n            column: 28\n          },\n          end: {\n            line: 113,\n            column: 44\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 113,\n            column: 28\n          },\n          end: {\n            line: 113,\n            column: 39\n          }\n        }, {\n          start: {\n            line: 113,\n            column: 43\n          },\n          end: {\n            line: 113,\n            column: 44\n          }\n        }],\n        line: 113\n      },\n      \"8\": {\n        loc: {\n          start: {\n            line: 116,\n            column: 35\n          },\n          end: {\n            line: 118,\n            column: 39\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 117,\n            column: 30\n          },\n          end: {\n            line: 117,\n            column: 62\n          }\n        }, {\n          start: {\n            line: 118,\n            column: 30\n          },\n          end: {\n            line: 118,\n            column: 39\n          }\n        }],\n        line: 116\n      },\n      \"9\": {\n        loc: {\n          start: {\n            line: 119,\n            column: 33\n          },\n          end: {\n            line: 121,\n            column: 39\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 120,\n            column: 30\n          },\n          end: {\n            line: 120,\n            column: 60\n          }\n        }, {\n          start: {\n            line: 121,\n            column: 30\n          },\n          end: {\n            line: 121,\n            column: 39\n          }\n        }],\n        line: 119\n      },\n      \"10\": {\n        loc: {\n          start: {\n            line: 123,\n            column: 32\n          },\n          end: {\n            line: 123,\n            column: 70\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 123,\n            column: 32\n          },\n          end: {\n            line: 123,\n            column: 47\n          }\n        }, {\n          start: {\n            line: 123,\n            column: 51\n          },\n          end: {\n            line: 123,\n            column: 70\n          }\n        }],\n        line: 123\n      },\n      \"11\": {\n        loc: {\n          start: {\n            line: 128,\n            column: 33\n          },\n          end: {\n            line: 128,\n            column: 102\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 128,\n            column: 33\n          },\n          end: {\n            line: 128,\n            column: 52\n          }\n        }, {\n          start: {\n            line: 128,\n            column: 56\n          },\n          end: {\n            line: 128,\n            column: 102\n          }\n        }],\n        line: 128\n      }\n    },\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0,\n      \"36\": 0,\n      \"37\": 0,\n      \"38\": 0,\n      \"39\": 0,\n      \"40\": 0,\n      \"41\": 0,\n      \"42\": 0,\n      \"43\": 0,\n      \"44\": 0,\n      \"45\": 0,\n      \"46\": 0,\n      \"47\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0\n    },\n    b: {\n      \"0\": [0, 0],\n      \"1\": [0, 0],\n      \"2\": [0, 0],\n      \"3\": [0, 0],\n      \"4\": [0, 0],\n      \"5\": [0, 0],\n      \"6\": [0, 0],\n      \"7\": [0, 0],\n      \"8\": [0, 0],\n      \"9\": [0, 0],\n      \"10\": [0, 0],\n      \"11\": [0, 0]\n    },\n    inputSourceMap: {\n      version: 3,\n      file: \"contractor-contracts-list-page.component.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-dashboard\\\\pages\\\\contractor-contracts-list-page\\\\contractor-contracts-list-page.component.ts\"],\n      names: [],\n      mappings: \";;;AAAA,OAAO,EAAiB,SAAS,EAAU,SAAS,EAAE,MAAM,eAAe,CAAC;AAC5E,OAAO,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAC3D,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,kBAAkB,EAAE,MAAM,8BAA8B,CAAC;AAClE,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACzD,OAAO,EAAE,YAAY,EAAE,kBAAkB,EAAE,MAAM,6BAA6B,CAAC;AAC/E,OAAO,EAAE,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AAChE,OAAO,EAAE,kBAAkB,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AAC7E,OAAO,EAAE,gBAAgB,EAAE,MAAM,2BAA2B,CAAC;AAC7D,OAAO,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAC;AAGzC,OAAO,EAAE,qBAAqB,EAAE,MAAM,uDAAuD,CAAC;AAC9F,OAAO,EAAE,eAAe,EAAE,MAAM,gDAAgD,CAAC;AACjF,OAAO,EAAE,iBAAiB,EAAE,MAAM,oDAAoD,CAAC;AACvF,OAAO,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAC9D,OAAO,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AAChD,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,MAAM,CAAC;AAE1C,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,iBAAiB,CAAC;AACzD,OAAO,EAAE,WAAW,EAAE,MAAM,kCAAkC,CAAC;AAgCxD,IAAM,oCAAoC,GAA1C,MAAM,oCAAoC;IAiB/C,YACU,eAAgC,EAChC,WAAwB,EACxB,qBAA4C,EAC5C,iBAAoC,EACpC,KAAmB,EACnB,OAA0B,EAC1B,MAAc;QANd,oBAAe,GAAf,eAAe,CAAiB;QAChC,gBAAW,GAAX,WAAW,CAAa;QACxB,0BAAqB,GAArB,qBAAqB,CAAuB;QAC5C,sBAAiB,GAAjB,iBAAiB,CAAmB;QACpC,UAAK,GAAL,KAAK,CAAc;QACnB,YAAO,GAAP,OAAO,CAAmB;QAC1B,WAAM,GAAN,MAAM,CAAQ;QArBxB,qBAAgB,GAAa;YAC3B,gBAAgB;YAChB,WAAW;YACX,SAAS;YACT,cAAc;YACd,QAAQ;YACR,SAAS;SACV,CAAC;QAEF,uBAAkB,GAAG,EAAE,CAAC;QActB,IAAI,CAAC,UAAU,GAAG,IAAI,kBAAkB,EAAE,CAAC;IAC7C,CAAC;IAED,QAAQ;QACN,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAED,eAAe;QACb,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC3C,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;IACnC,CAAC;IAED,WAAW,CAAC,KAAY;QACtB,MAAM,WAAW,GAAI,KAAK,CAAC,MAA2B,CAAC,KAAK,CAAC;QAC7D,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAC1D,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;YAC9B,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;QACxC,CAAC;IACH,CAAC;IAED,uBAAuB,CAAC,QAA2B;QACjD,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,gBAAgB,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IACxD,CAAC;IAEO,uBAAuB;QAC7B,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACpB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;QACtD,IAAI,WAAW,EAAE,QAAQ,EAAE,CAAC;YAC1B,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC;gBAChE,IAAI,EAAE,CAAC,UAAU,EAAE,EAAE;oBACnB,MAAM,kBAAkB,GAAG,UAAU,CAAC,QAAQ,CAAC;oBAC/C,IAAI,CAAC,iBAAiB;yBACnB,aAAa,CAAC,kBAAkB,CAAC;yBACjC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;yBACzC,SAAS,CAAC;wBACT,IAAI,EAAE,CAAC,iBAAiB,EAAE,EAAE;4BAC1B,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC,QAAQ,IAAI,EAAE,CAAC;4BAC3D,IAAI,CAAC,iCAAiC,CAAC,kBAAkB,CAAC,CAAC;wBAC7D,CAAC;wBACD,KAAK,EAAE,CAAC,KAAwB,EAAE,EAAE;4BAClC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,2CAA2C,CAAC,CAAC;wBACvF,CAAC;qBACF,CAAC,CAAC;gBACP,CAAC;gBACD,KAAK,EAAE,GAAG,EAAE;oBACV,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;oBACpB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;gBAClE,CAAC;aACF,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YACpB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAEO,iCAAiC,CAAC,kBAA0B;QAClE,IAAI,CAAC,eAAe,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,CAAC,SAAS,CAAC;YACzE,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE;gBAClB,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;YACrC,CAAC;YACD,KAAK,EAAE,CAAC,KAAwB,EAAE,EAAE;gBAClC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,+BAA+B,CAAC,CAAC;YAC3E,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAEO,kBAAkB,CAAC,SAAqB;QAC9C,MAAM,WAAW,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAC7C,QAAQ,CAAC,EAAE,KAAK,SAAS;YACvB,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5D,CAAC,CAAC,EAAE,CACP,CAAC;QAEF,QAAQ,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC;YAC9B,IAAI,EAAE,CAAC,mBAAmB,EAAE,EAAE;gBAC5B,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;oBACvD,MAAM,cAAc,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC;oBAClD,MAAM,YAAY,GAChB,cAAc,CAAC,MAAM,GAAG,CAAC;wBACvB,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;wBAC3C,CAAC,CAAC,IAAI,CAAC;oBACX,OAAO;wBACL,EAAE,EAAE,QAAQ,CAAC,EAAE,IAAI,CAAC;wBACpB,cAAc,EAAE,QAAQ,CAAC,cAAc;wBACvC,MAAM,EAAE,QAAQ,CAAC,MAAM;wBACvB,SAAS,EAAE,YAAY,EAAE,SAAS;4BAChC,CAAC,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC;4BAClC,CAAC,CAAC,SAAS;wBACb,OAAO,EAAE,YAAY,EAAE,OAAO;4BAC5B,CAAC,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;4BAChC,CAAC,CAAC,SAAS;wBACb,YAAY,EAAE,YAAY,EAAE,YAAY;wBACxC,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE;qBAC/C,CAAC;gBACJ,CAAC,CAAC,CAAC;YACL,CAAC;YACD,KAAK,EAAE,CAAC,KAAwB,EAAE,EAAE;gBAClC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,8CAA8C,CAAC,CAAC;YAC1F,CAAC;SACF,CAAC,CAAC;IACL,CAAC;;;;;;;;;;;4BAhHA,SAAS,SAAC,YAAY;uBACtB,SAAS,SAAC,OAAO;;;AAfP,oCAAoC;IAnBhD,SAAS,CAAC;QACT,QAAQ,EAAE,oCAAoC;QAC9C,8BAA8D;QAE9D,UAAU,EAAE,IAAI;QAChB,OAAO,EAAE;YACP,cAAc;YACd,aAAa;YACb,kBAAkB;YAClB,kBAAkB;YAClB,cAAc;YACd,eAAe;YACf,aAAa;YACb,gBAAgB;YAChB,aAAa;YACb,QAAQ;YACR,YAAY;SACb;;KACF,CAAC;GACW,oCAAoC,CA+HhD\",\n      sourcesContent: [\"import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';\\nimport { MatButtonModule } from '@angular/material/button';\\nimport { MatCardModule } from '@angular/material/card';\\nimport { MatFormFieldModule } from '@angular/material/form-field';\\nimport { MatIconModule } from '@angular/material/icon';\\nimport { MatInputModule } from '@angular/material/input';\\nimport { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';\\nimport { MatSort, MatSortModule } from '@angular/material/sort';\\nimport { MatTableDataSource, MatTableModule } from '@angular/material/table';\\nimport { MatTooltipModule } from '@angular/material/tooltip';\\nimport { Router } from '@angular/router';\\nimport { Contract } from '@contract-management/models/contract.model';\\n\\nimport { ContractValuesService } from '@contract-management/services/contract-values.service';\\nimport { ContractService } from '@contract-management/services/contract.service';\\nimport { ContractorService } from '@contractor-management/services/contractor.service';\\nimport { AlertService } from '@shared/services/alert.service';\\nimport { NgxSpinnerService } from 'ngx-spinner';\\nimport { finalize, forkJoin } from 'rxjs';\\n\\nimport { CurrencyPipe, DatePipe } from '@angular/common';\\nimport { AuthService } from '@core/auth/services/auth.service';\\nimport { HttpErrorResponse } from '@angular/common/http';\\n\\ninterface ContractTableData {\\n  id: number;\\n  contractNumber: number;\\n  object: string;\\n  startDate?: Date;\\n  endDate?: Date;\\n  numericValue?: number;\\n  status: { name: string };\\n}\\n\\n@Component({\\n  selector: 'app-contractor-contracts-list-page',\\n  templateUrl: './contractor-contracts-list-page.component.html',\\n  styleUrl: './contractor-contracts-list-page.component.scss',\\n  standalone: true,\\n  imports: [\\n    MatTableModule,\\n    MatSortModule,\\n    MatPaginatorModule,\\n    MatFormFieldModule,\\n    MatInputModule,\\n    MatButtonModule,\\n    MatIconModule,\\n    MatTooltipModule,\\n    MatCardModule,\\n    DatePipe,\\n    CurrencyPipe,\\n  ],\\n})\\nexport class ContractorContractsListPageComponent\\n  implements OnInit, AfterViewInit\\n{\\n  displayedColumns: string[] = [\\n    'contractNumber',\\n    'startDate',\\n    'endDate',\\n    'numericValue',\\n    'status',\\n    'actions',\\n  ];\\n  dataSource: MatTableDataSource<ContractTableData>;\\n  contractorFullName = '';\\n  contractorIdNumber?: number;\\n  @ViewChild(MatPaginator) paginator!: MatPaginator;\\n  @ViewChild(MatSort) sort!: MatSort;\\n\\n  constructor(\\n    private contractService: ContractService,\\n    private authService: AuthService,\\n    private contractValuesService: ContractValuesService,\\n    private contractorService: ContractorService,\\n    private alert: AlertService,\\n    private spinner: NgxSpinnerService,\\n    private router: Router,\\n  ) {\\n    this.dataSource = new MatTableDataSource();\\n  }\\n\\n  ngOnInit(): void {\\n    this.loadContractorContracts();\\n  }\\n\\n  ngAfterViewInit(): void {\\n    this.dataSource.paginator = this.paginator;\\n    this.dataSource.sort = this.sort;\\n  }\\n\\n  applyFilter(event: Event): void {\\n    const filterValue = (event.target as HTMLInputElement).value;\\n    this.dataSource.filter = filterValue.trim().toLowerCase();\\n    if (this.dataSource.paginator) {\\n      this.dataSource.paginator.firstPage();\\n    }\\n  }\\n\\n  handleVisualizeContract(contract: ContractTableData): void {\\n    this.router.navigate(['/mis-contratos', contract.id]);\\n  }\\n\\n  private loadContractorContracts(): void {\\n    this.spinner.show();\\n    const currentUser = this.authService.getCurrentUser();\\n    if (currentUser?.username) {\\n      this.contractorService.getByEmail(currentUser.username).subscribe({\\n        next: (contractor) => {\\n          const contractorIdNumber = contractor.idNumber;\\n          this.contractorService\\n            .getByIdNumber(contractorIdNumber)\\n            .pipe(finalize(() => this.spinner.hide()))\\n            .subscribe({\\n              next: (contractorDetails) => {\\n                this.contractorFullName = contractorDetails.fullName || '';\\n                this.loadContractsByContractorIdNumber(contractorIdNumber);\\n              },\\n              error: (error: HttpErrorResponse) => {\\n                this.alert.error(error.error?.detail ?? 'Error al cargar los datos del contratista');\\n              },\\n            });\\n        },\\n        error: () => {\\n          this.spinner.hide();\\n          this.alert.info('El Contrastista no tiene contratos asignados');\\n        },\\n      });\\n    } else {\\n      this.spinner.hide();\\n      this.alert.error('Usuario no autenticado');\\n    }\\n  }\\n\\n  private loadContractsByContractorIdNumber(contractorIdNumber: number): void {\\n    this.contractService.getByContractorIdNumber(contractorIdNumber).subscribe({\\n      next: (contracts) => {\\n        this.loadContractValues(contracts);\\n      },\\n      error: (error: HttpErrorResponse) => {\\n        this.alert.error(error.error?.detail ?? 'Error al cargar los contratos');\\n      },\\n    });\\n  }\\n\\n  private loadContractValues(contracts: Contract[]): void {\\n    const observables = contracts.map((contract) =>\\n      contract.id !== undefined\\n        ? this.contractValuesService.getAllByContractId(contract.id)\\n        : [],\\n    );\\n\\n    forkJoin(observables).subscribe({\\n      next: (contractValuesArray) => {\\n        this.dataSource.data = contracts.map((contract, index) => {\\n          const contractValues = contractValuesArray[index];\\n          const latestValues =\\n            contractValues.length > 0\\n              ? contractValues[contractValues.length - 1]\\n              : null;\\n          return {\\n            id: contract.id ?? 0,\\n            contractNumber: contract.contractNumber,\\n            object: contract.object,\\n            startDate: latestValues?.startDate\\n              ? new Date(latestValues.startDate)\\n              : undefined,\\n            endDate: latestValues?.endDate\\n              ? new Date(latestValues.endDate)\\n              : undefined,\\n            numericValue: latestValues?.numericValue,\\n            status: contract.status ?? { name: 'Unknown' },\\n          };\\n        });\\n      },\\n      error: (error: HttpErrorResponse) => {\\n        this.alert.error(error.error?.detail ?? 'Error al cargar los valores de los contratos');\\n      },\\n    });\\n  }\\n}\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"92f558d035c0697c6d4cba9c60c05cab25da923a\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_2jf0spsf15 = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_2jf0spsf15();\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./contractor-contracts-list-page.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./contractor-contracts-list-page.component.scss?ngResource\";\nimport { Component, ViewChild } from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSort, MatSortModule } from '@angular/material/sort';\nimport { MatTableDataSource, MatTableModule } from '@angular/material/table';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { Router } from '@angular/router';\nimport { ContractValuesService } from '@contract-management/services/contract-values.service';\nimport { ContractService } from '@contract-management/services/contract.service';\nimport { ContractorService } from '@contractor-management/services/contractor.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { finalize, forkJoin } from 'rxjs';\nimport { CurrencyPipe, DatePipe } from '@angular/common';\nimport { AuthService } from '@core/auth/services/auth.service';\ncov_2jf0spsf15().s[0]++;\nlet ContractorContractsListPageComponent = class ContractorContractsListPageComponent {\n  constructor(contractService, authService, contractValuesService, contractorService, alert, spinner, router) {\n    cov_2jf0spsf15().f[0]++;\n    cov_2jf0spsf15().s[1]++;\n    this.contractService = contractService;\n    cov_2jf0spsf15().s[2]++;\n    this.authService = authService;\n    cov_2jf0spsf15().s[3]++;\n    this.contractValuesService = contractValuesService;\n    cov_2jf0spsf15().s[4]++;\n    this.contractorService = contractorService;\n    cov_2jf0spsf15().s[5]++;\n    this.alert = alert;\n    cov_2jf0spsf15().s[6]++;\n    this.spinner = spinner;\n    cov_2jf0spsf15().s[7]++;\n    this.router = router;\n    cov_2jf0spsf15().s[8]++;\n    this.displayedColumns = ['contractNumber', 'startDate', 'endDate', 'numericValue', 'status', 'actions'];\n    cov_2jf0spsf15().s[9]++;\n    this.contractorFullName = '';\n    cov_2jf0spsf15().s[10]++;\n    this.dataSource = new MatTableDataSource();\n  }\n  ngOnInit() {\n    cov_2jf0spsf15().f[1]++;\n    cov_2jf0spsf15().s[11]++;\n    this.loadContractorContracts();\n  }\n  ngAfterViewInit() {\n    cov_2jf0spsf15().f[2]++;\n    cov_2jf0spsf15().s[12]++;\n    this.dataSource.paginator = this.paginator;\n    cov_2jf0spsf15().s[13]++;\n    this.dataSource.sort = this.sort;\n  }\n  applyFilter(event) {\n    cov_2jf0spsf15().f[3]++;\n    const filterValue = (cov_2jf0spsf15().s[14]++, event.target.value);\n    cov_2jf0spsf15().s[15]++;\n    this.dataSource.filter = filterValue.trim().toLowerCase();\n    cov_2jf0spsf15().s[16]++;\n    if (this.dataSource.paginator) {\n      cov_2jf0spsf15().b[0][0]++;\n      cov_2jf0spsf15().s[17]++;\n      this.dataSource.paginator.firstPage();\n    } else {\n      cov_2jf0spsf15().b[0][1]++;\n    }\n  }\n  handleVisualizeContract(contract) {\n    cov_2jf0spsf15().f[4]++;\n    cov_2jf0spsf15().s[18]++;\n    this.router.navigate(['/mis-contratos', contract.id]);\n  }\n  loadContractorContracts() {\n    cov_2jf0spsf15().f[5]++;\n    cov_2jf0spsf15().s[19]++;\n    this.spinner.show();\n    const currentUser = (cov_2jf0spsf15().s[20]++, this.authService.getCurrentUser());\n    cov_2jf0spsf15().s[21]++;\n    if (currentUser?.username) {\n      cov_2jf0spsf15().b[1][0]++;\n      cov_2jf0spsf15().s[22]++;\n      this.contractorService.getByEmail(currentUser.username).subscribe({\n        next: contractor => {\n          cov_2jf0spsf15().f[6]++;\n          const contractorIdNumber = (cov_2jf0spsf15().s[23]++, contractor.idNumber);\n          cov_2jf0spsf15().s[24]++;\n          this.contractorService.getByIdNumber(contractorIdNumber).pipe(finalize(() => {\n            cov_2jf0spsf15().f[7]++;\n            cov_2jf0spsf15().s[25]++;\n            return this.spinner.hide();\n          })).subscribe({\n            next: contractorDetails => {\n              cov_2jf0spsf15().f[8]++;\n              cov_2jf0spsf15().s[26]++;\n              this.contractorFullName = (cov_2jf0spsf15().b[2][0]++, contractorDetails.fullName) || (cov_2jf0spsf15().b[2][1]++, '');\n              cov_2jf0spsf15().s[27]++;\n              this.loadContractsByContractorIdNumber(contractorIdNumber);\n            },\n            error: error => {\n              cov_2jf0spsf15().f[9]++;\n              cov_2jf0spsf15().s[28]++;\n              this.alert.error((cov_2jf0spsf15().b[3][0]++, error.error?.detail) ?? (cov_2jf0spsf15().b[3][1]++, 'Error al cargar los datos del contratista'));\n            }\n          });\n        },\n        error: () => {\n          cov_2jf0spsf15().f[10]++;\n          cov_2jf0spsf15().s[29]++;\n          this.spinner.hide();\n          cov_2jf0spsf15().s[30]++;\n          this.alert.info('El Contrastista no tiene contratos asignados');\n        }\n      });\n    } else {\n      cov_2jf0spsf15().b[1][1]++;\n      cov_2jf0spsf15().s[31]++;\n      this.spinner.hide();\n      cov_2jf0spsf15().s[32]++;\n      this.alert.error('Usuario no autenticado');\n    }\n  }\n  loadContractsByContractorIdNumber(contractorIdNumber) {\n    cov_2jf0spsf15().f[11]++;\n    cov_2jf0spsf15().s[33]++;\n    this.contractService.getByContractorIdNumber(contractorIdNumber).subscribe({\n      next: contracts => {\n        cov_2jf0spsf15().f[12]++;\n        cov_2jf0spsf15().s[34]++;\n        this.loadContractValues(contracts);\n      },\n      error: error => {\n        cov_2jf0spsf15().f[13]++;\n        cov_2jf0spsf15().s[35]++;\n        this.alert.error((cov_2jf0spsf15().b[4][0]++, error.error?.detail) ?? (cov_2jf0spsf15().b[4][1]++, 'Error al cargar los contratos'));\n      }\n    });\n  }\n  loadContractValues(contracts) {\n    cov_2jf0spsf15().f[14]++;\n    const observables = (cov_2jf0spsf15().s[36]++, contracts.map(contract => {\n      cov_2jf0spsf15().f[15]++;\n      cov_2jf0spsf15().s[37]++;\n      return contract.id !== undefined ? (cov_2jf0spsf15().b[5][0]++, this.contractValuesService.getAllByContractId(contract.id)) : (cov_2jf0spsf15().b[5][1]++, []);\n    }));\n    cov_2jf0spsf15().s[38]++;\n    forkJoin(observables).subscribe({\n      next: contractValuesArray => {\n        cov_2jf0spsf15().f[16]++;\n        cov_2jf0spsf15().s[39]++;\n        this.dataSource.data = contracts.map((contract, index) => {\n          cov_2jf0spsf15().f[17]++;\n          const contractValues = (cov_2jf0spsf15().s[40]++, contractValuesArray[index]);\n          const latestValues = (cov_2jf0spsf15().s[41]++, contractValues.length > 0 ? (cov_2jf0spsf15().b[6][0]++, contractValues[contractValues.length - 1]) : (cov_2jf0spsf15().b[6][1]++, null));\n          cov_2jf0spsf15().s[42]++;\n          return {\n            id: (cov_2jf0spsf15().b[7][0]++, contract.id) ?? (cov_2jf0spsf15().b[7][1]++, 0),\n            contractNumber: contract.contractNumber,\n            object: contract.object,\n            startDate: latestValues?.startDate ? (cov_2jf0spsf15().b[8][0]++, new Date(latestValues.startDate)) : (cov_2jf0spsf15().b[8][1]++, undefined),\n            endDate: latestValues?.endDate ? (cov_2jf0spsf15().b[9][0]++, new Date(latestValues.endDate)) : (cov_2jf0spsf15().b[9][1]++, undefined),\n            numericValue: latestValues?.numericValue,\n            status: (cov_2jf0spsf15().b[10][0]++, contract.status) ?? (cov_2jf0spsf15().b[10][1]++, {\n              name: 'Unknown'\n            })\n          };\n        });\n      },\n      error: error => {\n        cov_2jf0spsf15().f[18]++;\n        cov_2jf0spsf15().s[43]++;\n        this.alert.error((cov_2jf0spsf15().b[11][0]++, error.error?.detail) ?? (cov_2jf0spsf15().b[11][1]++, 'Error al cargar los valores de los contratos'));\n      }\n    });\n  }\n  static {\n    cov_2jf0spsf15().s[44]++;\n    this.ctorParameters = () => {\n      cov_2jf0spsf15().f[19]++;\n      cov_2jf0spsf15().s[45]++;\n      return [{\n        type: ContractService\n      }, {\n        type: AuthService\n      }, {\n        type: ContractValuesService\n      }, {\n        type: ContractorService\n      }, {\n        type: AlertService\n      }, {\n        type: NgxSpinnerService\n      }, {\n        type: Router\n      }];\n    };\n  }\n  static {\n    cov_2jf0spsf15().s[46]++;\n    this.propDecorators = {\n      paginator: [{\n        type: ViewChild,\n        args: [MatPaginator]\n      }],\n      sort: [{\n        type: ViewChild,\n        args: [MatSort]\n      }]\n    };\n  }\n};\ncov_2jf0spsf15().s[47]++;\nContractorContractsListPageComponent = __decorate([Component({\n  selector: 'app-contractor-contracts-list-page',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: true,\n  imports: [MatTableModule, MatSortModule, MatPaginatorModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatIconModule, MatTooltipModule, MatCardModule, DatePipe, CurrencyPipe],\n  styles: [__NG_CLI_RESOURCE__1]\n})], ContractorContractsListPageComponent);\nexport { ContractorContractsListPageComponent };", "map": {"version": 3, "names": ["cov_2jf0spsf15", "actualCoverage", "Component", "ViewChild", "MatButtonModule", "MatCardModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatPaginator", "MatPaginatorModule", "MatSort", "MatSortModule", "MatTableDataSource", "MatTableModule", "MatTooltipModule", "Router", "ContractValuesService", "ContractService", "ContractorService", "AlertService", "NgxSpinnerService", "finalize", "fork<PERSON><PERSON>n", "C<PERSON><PERSON>cyPipe", "DatePipe", "AuthService", "s", "ContractorContractsListPageComponent", "constructor", "contractService", "authService", "contractValuesService", "contractorService", "alert", "spinner", "router", "f", "displayedColumns", "contractorFullName", "dataSource", "ngOnInit", "loadContractorContracts", "ngAfterViewInit", "paginator", "sort", "applyFilter", "event", "filterValue", "target", "value", "filter", "trim", "toLowerCase", "b", "firstPage", "handleVisualizeContract", "contract", "navigate", "id", "show", "currentUser", "getCurrentUser", "username", "getByEmail", "subscribe", "next", "contractor", "contractorIdNumber", "idNumber", "getByIdNumber", "pipe", "hide", "contractorDetails", "fullName", "loadContractsByContractorIdNumber", "error", "detail", "info", "getByContractorIdNumber", "contracts", "loadContractValues", "observables", "map", "undefined", "getAllByContractId", "contractValuesArray", "data", "index", "contractValues", "latestValues", "length", "contractNumber", "object", "startDate", "Date", "endDate", "numericValue", "status", "name", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\pages\\contractor-contracts-list-page\\contractor-contracts-list-page.component.ts"], "sourcesContent": ["import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSort, MatSortModule } from '@angular/material/sort';\nimport { MatTableDataSource, MatTableModule } from '@angular/material/table';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { Router } from '@angular/router';\nimport { Contract } from '@contract-management/models/contract.model';\n\nimport { ContractValuesService } from '@contract-management/services/contract-values.service';\nimport { ContractService } from '@contract-management/services/contract.service';\nimport { ContractorService } from '@contractor-management/services/contractor.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { finalize, forkJoin } from 'rxjs';\n\nimport { CurrencyPipe, DatePipe } from '@angular/common';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { HttpErrorResponse } from '@angular/common/http';\n\ninterface ContractTableData {\n  id: number;\n  contractNumber: number;\n  object: string;\n  startDate?: Date;\n  endDate?: Date;\n  numericValue?: number;\n  status: { name: string };\n}\n\n@Component({\n  selector: 'app-contractor-contracts-list-page',\n  templateUrl: './contractor-contracts-list-page.component.html',\n  styleUrl: './contractor-contracts-list-page.component.scss',\n  standalone: true,\n  imports: [\n    MatTableModule,\n    MatSortModule,\n    MatPaginatorModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatIconModule,\n    MatTooltipModule,\n    MatCardModule,\n    DatePipe,\n    CurrencyPipe,\n  ],\n})\nexport class ContractorContractsListPageComponent\n  implements OnInit, AfterViewInit\n{\n  displayedColumns: string[] = [\n    'contractNumber',\n    'startDate',\n    'endDate',\n    'numericValue',\n    'status',\n    'actions',\n  ];\n  dataSource: MatTableDataSource<ContractTableData>;\n  contractorFullName = '';\n  contractorIdNumber?: number;\n  @ViewChild(MatPaginator) paginator!: MatPaginator;\n  @ViewChild(MatSort) sort!: MatSort;\n\n  constructor(\n    private contractService: ContractService,\n    private authService: AuthService,\n    private contractValuesService: ContractValuesService,\n    private contractorService: ContractorService,\n    private alert: AlertService,\n    private spinner: NgxSpinnerService,\n    private router: Router,\n  ) {\n    this.dataSource = new MatTableDataSource();\n  }\n\n  ngOnInit(): void {\n    this.loadContractorContracts();\n  }\n\n  ngAfterViewInit(): void {\n    this.dataSource.paginator = this.paginator;\n    this.dataSource.sort = this.sort;\n  }\n\n  applyFilter(event: Event): void {\n    const filterValue = (event.target as HTMLInputElement).value;\n    this.dataSource.filter = filterValue.trim().toLowerCase();\n    if (this.dataSource.paginator) {\n      this.dataSource.paginator.firstPage();\n    }\n  }\n\n  handleVisualizeContract(contract: ContractTableData): void {\n    this.router.navigate(['/mis-contratos', contract.id]);\n  }\n\n  private loadContractorContracts(): void {\n    this.spinner.show();\n    const currentUser = this.authService.getCurrentUser();\n    if (currentUser?.username) {\n      this.contractorService.getByEmail(currentUser.username).subscribe({\n        next: (contractor) => {\n          const contractorIdNumber = contractor.idNumber;\n          this.contractorService\n            .getByIdNumber(contractorIdNumber)\n            .pipe(finalize(() => this.spinner.hide()))\n            .subscribe({\n              next: (contractorDetails) => {\n                this.contractorFullName = contractorDetails.fullName || '';\n                this.loadContractsByContractorIdNumber(contractorIdNumber);\n              },\n              error: (error: HttpErrorResponse) => {\n                this.alert.error(error.error?.detail ?? 'Error al cargar los datos del contratista');\n              },\n            });\n        },\n        error: () => {\n          this.spinner.hide();\n          this.alert.info('El Contrastista no tiene contratos asignados');\n        },\n      });\n    } else {\n      this.spinner.hide();\n      this.alert.error('Usuario no autenticado');\n    }\n  }\n\n  private loadContractsByContractorIdNumber(contractorIdNumber: number): void {\n    this.contractService.getByContractorIdNumber(contractorIdNumber).subscribe({\n      next: (contracts) => {\n        this.loadContractValues(contracts);\n      },\n      error: (error: HttpErrorResponse) => {\n        this.alert.error(error.error?.detail ?? 'Error al cargar los contratos');\n      },\n    });\n  }\n\n  private loadContractValues(contracts: Contract[]): void {\n    const observables = contracts.map((contract) =>\n      contract.id !== undefined\n        ? this.contractValuesService.getAllByContractId(contract.id)\n        : [],\n    );\n\n    forkJoin(observables).subscribe({\n      next: (contractValuesArray) => {\n        this.dataSource.data = contracts.map((contract, index) => {\n          const contractValues = contractValuesArray[index];\n          const latestValues =\n            contractValues.length > 0\n              ? contractValues[contractValues.length - 1]\n              : null;\n          return {\n            id: contract.id ?? 0,\n            contractNumber: contract.contractNumber,\n            object: contract.object,\n            startDate: latestValues?.startDate\n              ? new Date(latestValues.startDate)\n              : undefined,\n            endDate: latestValues?.endDate\n              ? new Date(latestValues.endDate)\n              : undefined,\n            numericValue: latestValues?.numericValue,\n            status: contract.status ?? { name: 'Unknown' },\n          };\n        });\n      },\n      error: (error: HttpErrorResponse) => {\n        this.alert.error(error.error?.detail ?? 'Error al cargar los valores de los contratos');\n      },\n    });\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAcS;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;AAdT,SAAwBE,SAAS,EAAUC,SAAS,QAAQ,eAAe;AAC3E,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,6BAA6B;AAC9E,SAASC,OAAO,EAAEC,aAAa,QAAQ,wBAAwB;AAC/D,SAASC,kBAAkB,EAAEC,cAAc,QAAQ,yBAAyB;AAC5E,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,MAAM,QAAQ,iBAAiB;AAGxC,SAASC,qBAAqB,QAAQ,uDAAuD;AAC7F,SAASC,eAAe,QAAQ,gDAAgD;AAChF,SAASC,iBAAiB,QAAQ,oDAAoD;AACtF,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,iBAAiB,QAAQ,aAAa;AAC/C,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,MAAM;AAEzC,SAASC,YAAY,EAAEC,QAAQ,QAAQ,iBAAiB;AACxD,SAASC,WAAW,QAAQ,kCAAkC;AAAC1B,cAAA,GAAA2B,CAAA;AAgCxD,IAAMC,oCAAoC,GAA1C,MAAMA,oCAAoC;EAiB/CC,YACUC,eAAgC,EAChCC,WAAwB,EACxBC,qBAA4C,EAC5CC,iBAAoC,EACpCC,KAAmB,EACnBC,OAA0B,EAC1BC,MAAc;IAAApC,cAAA,GAAAqC,CAAA;IAAArC,cAAA,GAAA2B,CAAA;IANd,KAAAG,eAAe,GAAfA,eAAe;IAAiB9B,cAAA,GAAA2B,CAAA;IAChC,KAAAI,WAAW,GAAXA,WAAW;IAAa/B,cAAA,GAAA2B,CAAA;IACxB,KAAAK,qBAAqB,GAArBA,qBAAqB;IAAuBhC,cAAA,GAAA2B,CAAA;IAC5C,KAAAM,iBAAiB,GAAjBA,iBAAiB;IAAmBjC,cAAA,GAAA2B,CAAA;IACpC,KAAAO,KAAK,GAALA,KAAK;IAAclC,cAAA,GAAA2B,CAAA;IACnB,KAAAQ,OAAO,GAAPA,OAAO;IAAmBnC,cAAA,GAAA2B,CAAA;IAC1B,KAAAS,MAAM,GAANA,MAAM;IAAQpC,cAAA,GAAA2B,CAAA;IArBxB,KAAAW,gBAAgB,GAAa,CAC3B,gBAAgB,EAChB,WAAW,EACX,SAAS,EACT,cAAc,EACd,QAAQ,EACR,SAAS,CACV;IAACtC,cAAA,GAAA2B,CAAA;IAEF,KAAAY,kBAAkB,GAAG,EAAE;IAACvC,cAAA,GAAA2B,CAAA;IActB,IAAI,CAACa,UAAU,GAAG,IAAI3B,kBAAkB,EAAE;EAC5C;EAEA4B,QAAQA,CAAA;IAAAzC,cAAA,GAAAqC,CAAA;IAAArC,cAAA,GAAA2B,CAAA;IACN,IAAI,CAACe,uBAAuB,EAAE;EAChC;EAEAC,eAAeA,CAAA;IAAA3C,cAAA,GAAAqC,CAAA;IAAArC,cAAA,GAAA2B,CAAA;IACb,IAAI,CAACa,UAAU,CAACI,SAAS,GAAG,IAAI,CAACA,SAAS;IAAC5C,cAAA,GAAA2B,CAAA;IAC3C,IAAI,CAACa,UAAU,CAACK,IAAI,GAAG,IAAI,CAACA,IAAI;EAClC;EAEAC,WAAWA,CAACC,KAAY;IAAA/C,cAAA,GAAAqC,CAAA;IACtB,MAAMW,WAAW,IAAAhD,cAAA,GAAA2B,CAAA,QAAIoB,KAAK,CAACE,MAA2B,CAACC,KAAK;IAAClD,cAAA,GAAA2B,CAAA;IAC7D,IAAI,CAACa,UAAU,CAACW,MAAM,GAAGH,WAAW,CAACI,IAAI,EAAE,CAACC,WAAW,EAAE;IAACrD,cAAA,GAAA2B,CAAA;IAC1D,IAAI,IAAI,CAACa,UAAU,CAACI,SAAS,EAAE;MAAA5C,cAAA,GAAAsD,CAAA;MAAAtD,cAAA,GAAA2B,CAAA;MAC7B,IAAI,CAACa,UAAU,CAACI,SAAS,CAACW,SAAS,EAAE;IACvC,CAAC;MAAAvD,cAAA,GAAAsD,CAAA;IAAA;EACH;EAEAE,uBAAuBA,CAACC,QAA2B;IAAAzD,cAAA,GAAAqC,CAAA;IAAArC,cAAA,GAAA2B,CAAA;IACjD,IAAI,CAACS,MAAM,CAACsB,QAAQ,CAAC,CAAC,gBAAgB,EAAED,QAAQ,CAACE,EAAE,CAAC,CAAC;EACvD;EAEQjB,uBAAuBA,CAAA;IAAA1C,cAAA,GAAAqC,CAAA;IAAArC,cAAA,GAAA2B,CAAA;IAC7B,IAAI,CAACQ,OAAO,CAACyB,IAAI,EAAE;IACnB,MAAMC,WAAW,IAAA7D,cAAA,GAAA2B,CAAA,QAAG,IAAI,CAACI,WAAW,CAAC+B,cAAc,EAAE;IAAC9D,cAAA,GAAA2B,CAAA;IACtD,IAAIkC,WAAW,EAAEE,QAAQ,EAAE;MAAA/D,cAAA,GAAAsD,CAAA;MAAAtD,cAAA,GAAA2B,CAAA;MACzB,IAAI,CAACM,iBAAiB,CAAC+B,UAAU,CAACH,WAAW,CAACE,QAAQ,CAAC,CAACE,SAAS,CAAC;QAChEC,IAAI,EAAGC,UAAU,IAAI;UAAAnE,cAAA,GAAAqC,CAAA;UACnB,MAAM+B,kBAAkB,IAAApE,cAAA,GAAA2B,CAAA,QAAGwC,UAAU,CAACE,QAAQ;UAACrE,cAAA,GAAA2B,CAAA;UAC/C,IAAI,CAACM,iBAAiB,CACnBqC,aAAa,CAACF,kBAAkB,CAAC,CACjCG,IAAI,CAACjD,QAAQ,CAAC,MAAM;YAAAtB,cAAA,GAAAqC,CAAA;YAAArC,cAAA,GAAA2B,CAAA;YAAA,WAAI,CAACQ,OAAO,CAACqC,IAAI,EAAE;UAAF,CAAE,CAAC,CAAC,CACzCP,SAAS,CAAC;YACTC,IAAI,EAAGO,iBAAiB,IAAI;cAAAzE,cAAA,GAAAqC,CAAA;cAAArC,cAAA,GAAA2B,CAAA;cAC1B,IAAI,CAACY,kBAAkB,GAAG,CAAAvC,cAAA,GAAAsD,CAAA,UAAAmB,iBAAiB,CAACC,QAAQ,MAAA1E,cAAA,GAAAsD,CAAA,UAAI,EAAE;cAACtD,cAAA,GAAA2B,CAAA;cAC3D,IAAI,CAACgD,iCAAiC,CAACP,kBAAkB,CAAC;YAC5D,CAAC;YACDQ,KAAK,EAAGA,KAAwB,IAAI;cAAA5E,cAAA,GAAAqC,CAAA;cAAArC,cAAA,GAAA2B,CAAA;cAClC,IAAI,CAACO,KAAK,CAAC0C,KAAK,CAAC,CAAA5E,cAAA,GAAAsD,CAAA,UAAAsB,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAA7E,cAAA,GAAAsD,CAAA,UAAI,2CAA2C,EAAC;YACtF;WACD,CAAC;QACN,CAAC;QACDsB,KAAK,EAAEA,CAAA,KAAK;UAAA5E,cAAA,GAAAqC,CAAA;UAAArC,cAAA,GAAA2B,CAAA;UACV,IAAI,CAACQ,OAAO,CAACqC,IAAI,EAAE;UAACxE,cAAA,GAAA2B,CAAA;UACpB,IAAI,CAACO,KAAK,CAAC4C,IAAI,CAAC,8CAA8C,CAAC;QACjE;OACD,CAAC;IACJ,CAAC,MAAM;MAAA9E,cAAA,GAAAsD,CAAA;MAAAtD,cAAA,GAAA2B,CAAA;MACL,IAAI,CAACQ,OAAO,CAACqC,IAAI,EAAE;MAACxE,cAAA,GAAA2B,CAAA;MACpB,IAAI,CAACO,KAAK,CAAC0C,KAAK,CAAC,wBAAwB,CAAC;IAC5C;EACF;EAEQD,iCAAiCA,CAACP,kBAA0B;IAAApE,cAAA,GAAAqC,CAAA;IAAArC,cAAA,GAAA2B,CAAA;IAClE,IAAI,CAACG,eAAe,CAACiD,uBAAuB,CAACX,kBAAkB,CAAC,CAACH,SAAS,CAAC;MACzEC,IAAI,EAAGc,SAAS,IAAI;QAAAhF,cAAA,GAAAqC,CAAA;QAAArC,cAAA,GAAA2B,CAAA;QAClB,IAAI,CAACsD,kBAAkB,CAACD,SAAS,CAAC;MACpC,CAAC;MACDJ,KAAK,EAAGA,KAAwB,IAAI;QAAA5E,cAAA,GAAAqC,CAAA;QAAArC,cAAA,GAAA2B,CAAA;QAClC,IAAI,CAACO,KAAK,CAAC0C,KAAK,CAAC,CAAA5E,cAAA,GAAAsD,CAAA,UAAAsB,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAA7E,cAAA,GAAAsD,CAAA,UAAI,+BAA+B,EAAC;MAC1E;KACD,CAAC;EACJ;EAEQ2B,kBAAkBA,CAACD,SAAqB;IAAAhF,cAAA,GAAAqC,CAAA;IAC9C,MAAM6C,WAAW,IAAAlF,cAAA,GAAA2B,CAAA,QAAGqD,SAAS,CAACG,GAAG,CAAE1B,QAAQ,IACzC;MAAAzD,cAAA,GAAAqC,CAAA;MAAArC,cAAA,GAAA2B,CAAA;MAAA,OAAA8B,QAAQ,CAACE,EAAE,KAAKyB,SAAS,IAAApF,cAAA,GAAAsD,CAAA,UACrB,IAAI,CAACtB,qBAAqB,CAACqD,kBAAkB,CAAC5B,QAAQ,CAACE,EAAE,CAAC,KAAA3D,cAAA,GAAAsD,CAAA,UAC1D,EAAE;IAAF,CAAE,CACP;IAACtD,cAAA,GAAA2B,CAAA;IAEFJ,QAAQ,CAAC2D,WAAW,CAAC,CAACjB,SAAS,CAAC;MAC9BC,IAAI,EAAGoB,mBAAmB,IAAI;QAAAtF,cAAA,GAAAqC,CAAA;QAAArC,cAAA,GAAA2B,CAAA;QAC5B,IAAI,CAACa,UAAU,CAAC+C,IAAI,GAAGP,SAAS,CAACG,GAAG,CAAC,CAAC1B,QAAQ,EAAE+B,KAAK,KAAI;UAAAxF,cAAA,GAAAqC,CAAA;UACvD,MAAMoD,cAAc,IAAAzF,cAAA,GAAA2B,CAAA,QAAG2D,mBAAmB,CAACE,KAAK,CAAC;UACjD,MAAME,YAAY,IAAA1F,cAAA,GAAA2B,CAAA,QAChB8D,cAAc,CAACE,MAAM,GAAG,CAAC,IAAA3F,cAAA,GAAAsD,CAAA,UACrBmC,cAAc,CAACA,cAAc,CAACE,MAAM,GAAG,CAAC,CAAC,KAAA3F,cAAA,GAAAsD,CAAA,UACzC,IAAI;UAACtD,cAAA,GAAA2B,CAAA;UACX,OAAO;YACLgC,EAAE,EAAE,CAAA3D,cAAA,GAAAsD,CAAA,UAAAG,QAAQ,CAACE,EAAE,MAAA3D,cAAA,GAAAsD,CAAA,UAAI,CAAC;YACpBsC,cAAc,EAAEnC,QAAQ,CAACmC,cAAc;YACvCC,MAAM,EAAEpC,QAAQ,CAACoC,MAAM;YACvBC,SAAS,EAAEJ,YAAY,EAAEI,SAAS,IAAA9F,cAAA,GAAAsD,CAAA,UAC9B,IAAIyC,IAAI,CAACL,YAAY,CAACI,SAAS,CAAC,KAAA9F,cAAA,GAAAsD,CAAA,UAChC8B,SAAS;YACbY,OAAO,EAAEN,YAAY,EAAEM,OAAO,IAAAhG,cAAA,GAAAsD,CAAA,UAC1B,IAAIyC,IAAI,CAACL,YAAY,CAACM,OAAO,CAAC,KAAAhG,cAAA,GAAAsD,CAAA,UAC9B8B,SAAS;YACba,YAAY,EAAEP,YAAY,EAAEO,YAAY;YACxCC,MAAM,EAAE,CAAAlG,cAAA,GAAAsD,CAAA,WAAAG,QAAQ,CAACyC,MAAM,MAAAlG,cAAA,GAAAsD,CAAA,WAAI;cAAE6C,IAAI,EAAE;YAAS,CAAE;WAC/C;QACH,CAAC,CAAC;MACJ,CAAC;MACDvB,KAAK,EAAGA,KAAwB,IAAI;QAAA5E,cAAA,GAAAqC,CAAA;QAAArC,cAAA,GAAA2B,CAAA;QAClC,IAAI,CAACO,KAAK,CAAC0C,KAAK,CAAC,CAAA5E,cAAA,GAAAsD,CAAA,WAAAsB,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAA7E,cAAA,GAAAsD,CAAA,WAAI,8CAA8C,EAAC;MACzF;KACD,CAAC;EACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;cAhHCnD,SAAS;QAAAiG,IAAA,GAAC3F,YAAY;MAAA;;cACtBN,SAAS;QAAAiG,IAAA,GAACzF,OAAO;MAAA;;;;;AAfPiB,oCAAoC,GAAAyE,UAAA,EAnBhDnG,SAAS,CAAC;EACToG,QAAQ,EAAE,oCAAoC;EAC9CC,QAAA,EAAAC,oBAA8D;EAE9DC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP5F,cAAc,EACdF,aAAa,EACbF,kBAAkB,EAClBJ,kBAAkB,EAClBE,cAAc,EACdJ,eAAe,EACfG,aAAa,EACbQ,gBAAgB,EAChBV,aAAa,EACboB,QAAQ,EACRD,YAAY,CACb;;CACF,CAAC,C,EACWI,oCAAoC,CA+HhD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}