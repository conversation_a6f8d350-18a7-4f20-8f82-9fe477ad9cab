import { HttpClientTestingModule } from '@angular/common/http/testing';
import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
  flush,
  discardPeriodicTasks,
} from '@angular/core/testing';
import {
  ReactiveFormsModule,
  FormsModule,
  ControlValueAccessor,
} from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { provideNativeDateAdapter } from '@angular/material/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { CDPEntity } from '@contract-management/models/cdp-entity.model';
import { ContractValues } from '@contract-management/models/contract-values.model';
import { Contract } from '@contract-management/models/contract.model';
import { Entity } from '@contract-management/models/entity.model';
import { CdpEntityService } from '@contract-management/services/cdp-entity.service';
import { ContractValuesService } from '@contract-management/services/contract-values.service';
import { EntityService } from '@contract-management/services/entity.service';
import { AlertService } from '@shared/services/alert.service';
import { of, throwError } from 'rxjs';
import { ContractValuesFormComponent } from './contract-values-form.component';
import { NO_ERRORS_SCHEMA, Directive, Input } from '@angular/core';
import { NgxCurrencyDirective } from 'ngx-currency';

@Directive({
  selector: '[appCurrencyMask]',
  standalone: true,
})
export class MockCurrencyDirective implements ControlValueAccessor {
  @Input() currencyMask: unknown = {};

  onChange = (_value: unknown): void => {};

  onTouched = (): void => {};

  writeValue(value: unknown): void {}

  registerOnChange(fn: (_: unknown) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {}
}

describe('ContractValuesFormComponent', () => {
  let component: ContractValuesFormComponent;
  let fixture: ComponentFixture<ContractValuesFormComponent>;
  let entityServiceSpy: jasmine.SpyObj<EntityService>;
  let cdpEntityServiceSpy: jasmine.SpyObj<CdpEntityService>;
  let contractValuesServiceSpy: jasmine.SpyObj<ContractValuesService>;
  let alertServiceSpy: jasmine.SpyObj<AlertService>;

  const mockEntities: Entity[] = [
    { id: 1, name: 'Entity 1' },
    { id: 2, name: 'Entity 2' },
  ];

  const mockCdpEntities: CDPEntity[] = [
    { id: 1, name: 'CDP Entity 1' },
    { id: 2, name: 'CDP Entity 2' },
  ];

  const mockContract: Contract = {
    id: 1,
    contractNumber: 123,
    monthlyPayment: 1000,
    object: 'Test Contract',
    rup: true,
    secopCode: 456,
    addition: false,
    cession: false,
    settled: false,
    causesSelectionId: 1,
    managementSupportId: 1,
    contractClassId: 1,
  };

  const mockContractValue: ContractValues = {
    id: 1,
    numericValue: 5000,
    madsValue: 3000,
    otherValue: 2000,
    futureValidityValue: 0,
    startDate: '2023-01-01',
    endDate: '2023-12-31',
    subscriptionDate: '2023-01-01',
    cdp: 789,
    cdpEntityId: 1,
    isOtherEntity: false,
    cdpEntity: mockCdpEntities[0],
    contractId: 1,
  };

  beforeEach(() => {
    entityServiceSpy = jasmine.createSpyObj<EntityService>('EntityService', [
      'getAll',
    ]);
    cdpEntityServiceSpy = jasmine.createSpyObj<CdpEntityService>(
      'CdpEntityService',
      ['getAll'],
    );
    contractValuesServiceSpy = jasmine.createSpyObj<ContractValuesService>(
      'ContractValuesService',
      ['getLatestEndDateByContractId'],
    );
    alertServiceSpy = jasmine.createSpyObj<AlertService>('AlertService', [
      'error',
    ]);

    entityServiceSpy.getAll.and.returnValue(of(mockEntities));
    cdpEntityServiceSpy.getAll.and.returnValue(of(mockCdpEntities));
    contractValuesServiceSpy.getLatestEndDateByContractId.and.returnValue(
      of(null),
    );

    TestBed.configureTestingModule({
      imports: [
        ContractValuesFormComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        ReactiveFormsModule,
        FormsModule,
        MatDatepickerModule,
        MatFormFieldModule,
        MatIconModule,
        MatInputModule,
        MatSelectModule,
        MatSlideToggleModule,
      ],
      providers: [
        provideNativeDateAdapter(),
        { provide: EntityService, useValue: entityServiceSpy },
        { provide: CdpEntityService, useValue: cdpEntityServiceSpy },
        { provide: ContractValuesService, useValue: contractValuesServiceSpy },
        { provide: AlertService, useValue: alertServiceSpy },
      ],
      schemas: [NO_ERRORS_SCHEMA],
    })
      .overrideDirective(NgxCurrencyDirective, {
        set: {
          providers: [],
        },
      })
      .compileComponents();

    fixture = TestBed.createComponent(ContractValuesFormComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Form initialization', () => {
    it('should initialize form with default values', fakeAsync(() => {
      component.contract = mockContract;
      fixture.detectChanges();
      tick();
      flush();
      discardPeriodicTasks();

      component.valuesFormGroup.patchValue(
        {
          numericValue: 0,
          madsValue: 0,
          otherValue: 0,
          futureValidityValue: 0,
        },
        { emitEvent: false },
      );

      expect(component.valuesFormGroup).toBeDefined();

      expect(component.valuesFormGroup.get('numericValue')?.value).toBe(0);
      expect(component.valuesFormGroup.get('madsValue')?.value).toBe(0);
      expect(component.valuesFormGroup.get('otherValue')?.value).toBe(0);
      expect(component.valuesFormGroup.get('futureValidityValue')?.value).toBe(
        0,
      );
      expect(component.valuesFormGroup.get('cdp')?.value).toBe('');
      expect(component.valuesFormGroup.get('startDate')?.value).toBeNull();
    }));

    it('should load initial data on ngOnInit', fakeAsync(() => {
      component.contract = mockContract;
      fixture.detectChanges();
      component.ngOnInit();
      tick();
      flush();
      discardPeriodicTasks();

      expect(entityServiceSpy.getAll).toHaveBeenCalled();
      expect(cdpEntityServiceSpy.getAll).toHaveBeenCalled();
      expect(component.entities).toEqual(mockEntities);
      expect(component.cdpEntities).toEqual(mockCdpEntities);
    }));

    it('should show alert on error loading initial data', fakeAsync(() => {
      spyOn(console, 'error').and.callFake(() => {});

      entityServiceSpy.getAll.and.returnValue(
        throwError(() => new Error('Test error')),
      );
      component.contract = mockContract;
      fixture.detectChanges();
      tick();
      flush();
      discardPeriodicTasks();

      expect(alertServiceSpy.error).toHaveBeenCalledWith(
        'Error al cargar los datos iniciales',
      );
    }));

    it('should patch form with contract value when provided', fakeAsync(() => {
      component.contract = mockContract;
      component.contractValue = mockContractValue;
      fixture.detectChanges();
      tick();
      flush();
      discardPeriodicTasks();

      expect(component.valuesFormGroup.get('numericValue')?.value).toBe(
        mockContractValue.numericValue,
      );
      expect(component.valuesFormGroup.get('madsValue')?.value).toBe(
        mockContractValue.madsValue,
      );
      expect(component.valuesFormGroup.get('otherValue')?.value).toBe(
        mockContractValue.otherValue,
      );
      expect(component.valuesFormGroup.get('cdp')?.value).toBe(
        mockContractValue.cdp,
      );
      expect(component.valuesFormGroup.get('cdpEntityId')?.value).toBe(
        mockContractValue.cdpEntityId,
      );
    }));
  });

  describe('Form validation', () => {
    beforeEach(fakeAsync(() => {
      component.contract = mockContract;
      fixture.detectChanges();
      tick();
      flush();
      discardPeriodicTasks();
    }));

    it('should validate required fields', () => {
      const startDateControl = component.valuesFormGroup.get('startDate');
      const subscriptionDateControl =
        component.valuesFormGroup.get('subscriptionDate');
      const cdpControl = component.valuesFormGroup.get('cdp');
      const cdpEntityIdControl = component.valuesFormGroup.get('cdpEntityId');

      expect(startDateControl?.hasError('required')).toBeTruthy();
      expect(subscriptionDateControl?.hasError('required')).toBeTruthy();
      expect(cdpControl?.hasError('required')).toBeTruthy();
      expect(cdpEntityIdControl?.hasError('required')).toBeTruthy();
    });

    it('should validate minimum value for numeric fields', () => {
      component.valuesFormGroup.get('numericValue')?.setValue(-1);
      component.valuesFormGroup.get('madsValue')?.setValue(-1);
      component.valuesFormGroup.get('otherValue')?.setValue(-1);
      component.valuesFormGroup.get('futureValidityValue')?.setValue(-1);

      expect(
        component.valuesFormGroup.get('numericValue')?.hasError('min'),
      ).toBeTruthy();
      expect(
        component.valuesFormGroup.get('madsValue')?.hasError('min'),
      ).toBeTruthy();
      expect(
        component.valuesFormGroup.get('otherValue')?.hasError('min'),
      ).toBeTruthy();
      expect(
        component.valuesFormGroup.get('futureValidityValue')?.hasError('min'),
      ).toBeTruthy();
    });
  });

  describe('Form functionality', () => {
    beforeEach(fakeAsync(() => {
      component.contract = mockContract;
      fixture.detectChanges();
      tick();
      flush();
      discardPeriodicTasks();
    }));

    it('should calculate numeric value from other value inputs', () => {
      component.valuesFormGroup.get('madsValue')?.setValue(100);
      component.valuesFormGroup.get('otherValue')?.setValue(200);
      component.valuesFormGroup.get('futureValidityValue')?.setValue(300);

      fixture.detectChanges();

      expect(component.valuesFormGroup.get('numericValue')?.value).toBe(300);
    });

    it('should enable entityId field when otherValue is greater than 0', () => {
      const isOtherEntityControl =
        component.valuesFormGroup.get('isOtherEntity');
      const entityIdControl = component.valuesFormGroup.get('entityId');

      component.valuesFormGroup.get('otherValue')?.setValue(500);
      component.onOtherValueChange();
      fixture.detectChanges();

      expect(isOtherEntityControl?.value).toBe(true);
      expect(entityIdControl?.disabled).toBe(false);
    });

    it('should disable entityId field when otherValue is 0', () => {
      const isOtherEntityControl =
        component.valuesFormGroup.get('isOtherEntity');
      const entityIdControl = component.valuesFormGroup.get('entityId');

      component.valuesFormGroup.get('otherValue')?.setValue(0);
      component.onOtherValueChange();
      fixture.detectChanges();

      expect(isOtherEntityControl?.value).toBe(false);
      expect(entityIdControl?.disabled).toBe(true);
    });

    it('should update contract duration when dates change', () => {
      const startDate = new Date(2023, 0, 1);
      const endDate = new Date(2023, 2, 31);

      component.valuesFormGroup.get('startDate')?.setValue(startDate);
      component.valuesFormGroup.get('endDate')?.setValue(endDate);
      fixture.detectChanges();

      component['validateDates']();

      expect(component.contractDuration.days).toBeGreaterThan(0);
      expect(component.contractDuration.months).toBeGreaterThan(0);
    });
  });

  describe('Public methods', () => {
    beforeEach(fakeAsync(() => {
      component.contract = mockContract;
      fixture.detectChanges();
      tick();
      flush();
      discardPeriodicTasks();
    }));

    it('should check if form is valid', () => {
      expect(component.isValid()).toBeFalse();

      component.valuesFormGroup.get('startDate')?.setValue(new Date());
      component.valuesFormGroup.get('endDate')?.setValue(new Date());
      component.valuesFormGroup.get('subscriptionDate')?.setValue(new Date());
      component.valuesFormGroup.get('cdp')?.setValue('123');
      component.valuesFormGroup.get('rp')?.setValue('456');
      component.valuesFormGroup.get('cdpEntityId')?.setValue(1);
      component.valuesFormGroup.get('madsValue')?.setValue(1000);
      component.valuesFormGroup.get('numericValue')?.setValue(1000);

      expect(component.isValid()).toBeTrue();
    });

    it('should get contract values from form', () => {
      const testDate = new Date(2023, 0, 1);

      component.valuesFormGroup.patchValue({
        numericValue: 5000,
        madsValue: 3000,
        otherValue: 2000,
        futureValidityValue: 0,
        startDate: testDate,
        endDate: testDate,
        subscriptionDate: testDate,
        cdp: 789,
        cdpEntityId: 1,
        isOtherEntity: false,
      });

      const result = component.getContractValuesFromForm();

      expect(result.numericValue).toBe(5000);
      expect(result.madsValue).toBe(3000);
      expect(result.otherValue).toBe(2000);
      expect(result.cdp).toBe(789);
      expect(result.cdpEntityId).toBe(1);
      expect(result.contractId).toBe(mockContract.id);
    });

    it('should emit form values when form is valid', fakeAsync(() => {
      spyOn(component.valuesSubmitted, 'emit');

      component.valuesFormGroup.get('startDate')?.setValue(new Date());
      component.valuesFormGroup.get('subscriptionDate')?.setValue(new Date());
      component.valuesFormGroup.get('cdp')?.setValue('123');
      component.valuesFormGroup.get('cdpEntityId')?.setValue(1);
      component.valuesFormGroup.get('madsValue')?.setValue(1000);

      component.valuesFormGroup.updateValueAndValidity();
      tick(300);
      flush();
      discardPeriodicTasks();

      expect(component.valuesSubmitted.emit).toHaveBeenCalled();
    }));

    it('should emit null when form is invalid', fakeAsync(() => {
      spyOn(component.valuesSubmitted, 'emit');

      component.valuesFormGroup.updateValueAndValidity();
      tick(300);
      flush();
      discardPeriodicTasks();

      expect(component.valuesSubmitted.emit).toHaveBeenCalledWith(null);
    }));
  });
});
