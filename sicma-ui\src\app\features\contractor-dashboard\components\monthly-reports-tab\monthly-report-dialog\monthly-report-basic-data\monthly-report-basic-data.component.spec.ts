import { DatePipe } from '@angular/common';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ContractDetails } from '@contract-management/models/contract-details.model';
import { ContractService } from '@contract-management/services/contract.service';
import { SupervisorContractService } from '@contract-management/services/supervisor-contract.service';
import { MonthlyReport } from '@contractor-dashboard/models/monthly-report.model';
import { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';
import { PaymentService } from '@contractor-dashboard/services/payment.service';
import { AlertService } from '@shared/services/alert.service';
import { Supervisor } from '@supervisor-management/models/supervisor.model';
import { SupervisorService } from '@supervisor-management/services/supervisor.service';
import { of, throwError } from 'rxjs';
import { MonthlyReportBasicDataComponent } from './monthly-report-basic-data.component';

describe('MonthlyReportBasicDataComponent', () => {
  let component: MonthlyReportBasicDataComponent;
  let fixture: ComponentFixture<MonthlyReportBasicDataComponent>;
  let contractService: jasmine.SpyObj<ContractService>;
  let paymentService: jasmine.SpyObj<PaymentService>;
  let supervisorService: jasmine.SpyObj<SupervisorService>;
  let supervisorContractService: jasmine.SpyObj<SupervisorContractService>;
  let alertService: jasmine.SpyObj<AlertService>;
  let monthlyReportService: jasmine.SpyObj<MonthlyReportService>;

  const mockReport: MonthlyReport = {
    id: 1,
    reportNumber: 1,
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-01-31'),
    creationDate: new Date(),
    contractorContractId: 1,
    contractorContract: {
      id: 1,
      subscriptionDate: '2024-01-01',
      contractStartDate: '2024-01-01',
      contract: {
        id: 1,
        contractNumber: 1001,
        monthlyPayment: 1000000,
        object: 'Test contract',
        rup: true,
        secopCode: 123456,
        addition: false,
        cession: false,
        settled: false,
        status: { id: 1, name: 'Active' },
        causesSelectionId: 1,
        managementSupportId: 1,
        contractClassId: 1,
      },
    },
  };

  const mockContractDetails: ContractDetails = {
    id: 1,
    contractNumber: 1001,
    contractorId: 1,
    fullName: 'Test Contractor',
    contractorIdNumber: *********,
    initialValue: 1000000,
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-12-31'),
    object: 'Test contract',
    rup: true,
    selectionModalityName: 'Test Modality',
    trackingTypeName: 'Test Type',
    contractTypeName: 'Test Contract Type',
    statusName: 'Active',
    dependencyName: 'Test Dependency',
    groupName: 'Test Group',
    contractorEmail: '<EMAIL>',
    contractorPhone: *********0,
    accountNumber: '*********',
    bankName: 'Test Bank',
    accountTypeName: 'Savings',
    totalValue: ********,
    totalAdditionsValue: 0,
    monthlyPayment: 1000000,
    supervisorFullName: 'Test Supervisor',
    supervisorIdNumber: '*********',
    supervisorPosition: 'Test Position',
    addition: false,
    cession: false,
    settled: false,
    hasCcp: true,
  };

  beforeEach(async () => {
    const contractServiceSpy = jasmine.createSpyObj('ContractService', [
      'getDetailsById',
    ]);
    const paymentServiceSpy = jasmine.createSpyObj('PaymentService', [
      'getByMonthlyReportId',
    ]);
    const supervisorServiceSpy = jasmine.createSpyObj('SupervisorService', [
      'getAll',
    ]);
    const supervisorContractServiceSpy = jasmine.createSpyObj(
      'SupervisorContractService',
      ['getByContractId', 'create'],
    );
    const alertServiceSpy = jasmine.createSpyObj('AlertService', [
      'error',
      'success',
    ]);
    const monthlyReportServiceSpy = jasmine.createSpyObj(
      'MonthlyReportService',
      ['update', 'getByContractorContractId'],
    );

    await TestBed.configureTestingModule({
      imports: [
        MonthlyReportBasicDataComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        ReactiveFormsModule,
      ],
      providers: [
        { provide: ContractService, useValue: contractServiceSpy },
        { provide: PaymentService, useValue: paymentServiceSpy },
        { provide: SupervisorService, useValue: supervisorServiceSpy },
        {
          provide: SupervisorContractService,
          useValue: supervisorContractServiceSpy,
        },
        { provide: AlertService, useValue: alertServiceSpy },
        { provide: MonthlyReportService, useValue: monthlyReportServiceSpy },
        DatePipe,
        FormBuilder,
      ],
    }).compileComponents();

    contractService = TestBed.inject(
      ContractService,
    ) as jasmine.SpyObj<ContractService>;
    paymentService = TestBed.inject(
      PaymentService,
    ) as jasmine.SpyObj<PaymentService>;
    supervisorService = TestBed.inject(
      SupervisorService,
    ) as jasmine.SpyObj<SupervisorService>;
    supervisorContractService = TestBed.inject(
      SupervisorContractService,
    ) as jasmine.SpyObj<SupervisorContractService>;
    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;
    monthlyReportService = TestBed.inject(
      MonthlyReportService,
    ) as jasmine.SpyObj<MonthlyReportService>;

    contractService.getDetailsById.and.returnValue(of(mockContractDetails));
    paymentService.getByMonthlyReportId.and.returnValue(of([]));
    supervisorService.getAll.and.returnValue(of([]));
    supervisorContractService.getByContractId.and.returnValue(of([]));
    monthlyReportService.getByContractorContractId.and.returnValue(
      of([mockReport]),
    );
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(MonthlyReportBasicDataComponent);
    component = fixture.componentInstance;
    component.report = mockReport;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load contract details on init', () => {
    expect(contractService.getDetailsById).toHaveBeenCalledWith(1);
    expect(component.contractDetails).toEqual(mockContractDetails);
  });

  it('should handle error when loading contract details', () => {
    contractService.getDetailsById.and.returnValue(
      throwError(() => new Error()),
    );
    component.loadContractDetails();
    expect(alertService.error).toHaveBeenCalledWith(
      'Error al obtener detalles del contrato',
    );
  });

  it('should initialize with report data', () => {
    expect(component.report).toBeDefined();
    expect(component.report.id).toBe(1);
    expect(component.report.reportNumber).toBe(1);
  });

  it('should handle contract information correctly', () => {
    const contractorContract = component.report.contractorContract;
    expect(contractorContract).toBeDefined();
    if (contractorContract?.contract) {
      expect(contractorContract.contract.contractNumber).toBe(1001);
      expect(contractorContract.contract.monthlyPayment).toBe(1000000);
    }
  });

  it('should handle invoice form changes', () => {
    component.invoiceForm.patchValue({ hasElectronicInvoice: true });
    expect(component.invoiceForm.get('invoiceNumber')?.enabled).toBeTrue();

    component.invoiceForm.patchValue({ hasElectronicInvoice: false });
    expect(component.invoiceForm.get('invoiceNumber')?.disabled).toBeTrue();
  });

  it('should validate invoice number when electronic invoice is enabled', () => {
    component.invoiceForm.patchValue({
      hasElectronicInvoice: true,
      invoiceNumber: -1,
    });
    expect(component.invoiceForm.get('invoiceNumber')?.valid).toBeFalse();

    component.invoiceForm.patchValue({
      invoiceNumber: 12345,
    });
    expect(component.invoiceForm.get('invoiceNumber')?.valid).toBeTrue();
  });

  it('should load payments on init', () => {
    component.ngOnInit();
    expect(paymentService.getByMonthlyReportId).toHaveBeenCalledWith(
      mockReport.id,
    );
  });

  it('should load supervisors on init', () => {
    expect(supervisorService.getAll).toHaveBeenCalled();
  });

  it('should handle supervisor contract loading', () => {
    expect(supervisorContractService.getByContractId).toHaveBeenCalled();
  });

  it('should toggle supervisor edit mode', () => {
    component.toggleSupervisorEdit();
    expect(component.isEditingSupervisor).toBeTrue();

    component.toggleSupervisorEdit();
    expect(component.isEditingSupervisor).toBeFalse();
  });

  it('should update CCPs validity', () => {
    component.onCcpsValidityChange(true);
    expect(component.isCcpsValid).toBeTrue();

    component.onCcpsValidityChange(false);
    expect(component.isCcpsValid).toBeFalse();
  });

  it('should load payments correctly', () => {
    const mockPayment = {
      id: 1,
      paymentNumber: 1,
      paymentDate: new Date(),
      value: 1000000,
      initialValue: 1000000,
      totalValue: 1000000,
      paidValue: 1000000,
      additions: 0,
      monthlyReportId: 1,
      bankAccountTypeId: 1,
    };

    paymentService.getByMonthlyReportId.and.returnValue(of([mockPayment]));
    monthlyReportService.getByContractorContractId.and.returnValue(
      of([mockReport]),
    );

    component.loadPayments();

    expect(monthlyReportService.getByContractorContractId).toHaveBeenCalledWith(
      mockReport.contractorContractId,
    );
    expect(paymentService.getByMonthlyReportId).toHaveBeenCalledWith(
      mockReport.id,
    );
    expect(component.totalPaid).toBe(0);
  });

  it('should handle errors when loading payments', () => {
    monthlyReportService.getByContractorContractId.and.returnValue(
      throwError(() => new Error()),
    );

    component.loadPayments();

    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar los informes',
    );
  });

  it('should format currency correctly', () => {
    const value = 1000000;
    const formattedValue = component.formatCurrency(value);
    expect(formattedValue).toContain('1.000.000,00');
  });

  it('should handle null values in currency formatting', () => {
    expect(component.formatCurrency(undefined)).toBe('N/A');
  });

  it('should format date correctly', () => {
    const date = new Date('2024-06-15T12:00:00.000Z');
    const formattedDate = component.formatDate(date);
    expect(formattedDate).toBe('15/06/2024');
  });

  it('should handle null values in date formatting', () => {
    expect(component.formatDate(undefined)).toBe('N/A');
  });

  it('should update report when contract details are loaded', () => {
    spyOn(component.reportChange, 'emit');
    component.loadContractDetails();
    expect(component.reportChange.emit).toHaveBeenCalled();
  });

  it('should initialize invoice form correctly', () => {
    expect(
      component.invoiceForm.get('hasElectronicInvoice')?.value,
    ).toBeFalsy();
    expect(component.invoiceForm.get('invoiceNumber')?.disabled).toBeTrue();
  });

  it('should enable invoice number field when hasElectronicInvoice is true', () => {
    component.invoiceForm.patchValue({ hasElectronicInvoice: true });
    expect(component.invoiceForm.get('invoiceNumber')?.enabled).toBeTrue();
  });

  it('should disable invoice number field when hasElectronicInvoice is false', () => {
    component.invoiceForm.patchValue({ hasElectronicInvoice: false });
    expect(component.invoiceForm.get('invoiceNumber')?.disabled).toBeTrue();
  });

  it('should handle missing contractorContractId when loading payments', () => {
    const monthlyReportService = TestBed.inject(
      MonthlyReportService,
    ) as jasmine.SpyObj<MonthlyReportService>;
    monthlyReportService.getByContractorContractId.calls.reset();
    component.report = undefined as unknown as MonthlyReport;
    component.loadPayments();
    expect(
      monthlyReportService.getByContractorContractId,
    ).not.toHaveBeenCalled();
  });

  it('should handle errors when loading supervisors', () => {
    const alertService = TestBed.inject(
      AlertService,
    ) as jasmine.SpyObj<AlertService>;
    supervisorService.getAll.and.returnValue(
      throwError(() => new Error('Error')),
    );
    component.ngOnInit();
    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar los supervisores',
    );
  });

  it('should handle supervisor autocomplete filtering', () => {
    const mockSupervisors: Supervisor[] = [
      {
        id: 1,
        fullName: 'John Doe',
        idNumber: 123,
        position: 'Dev',
        email: '<EMAIL>',
        idType: { id: 1, name: 'CC' },
      },
      {
        id: 2,
        fullName: 'Jane Smith',
        idNumber: 456,
        position: 'QA',
        email: '<EMAIL>',
        idType: { id: 1, name: 'CC' },
      },
    ];
    component.supervisors = mockSupervisors;

    const filtered = component['_filterSupervisors']('john');
    expect(filtered.length).toBe(1);
    expect(filtered[0].fullName).toBe('John Doe');
  });

  it('should display supervisor correctly', () => {
    const mockSupervisor: Supervisor = {
      id: 1,
      fullName: 'John Doe',
      idNumber: 123,
      position: 'Dev',
      email: '<EMAIL>',
      idType: { id: 1, name: 'CC' },
    };
    const displayText = component.displaySupervisor(mockSupervisor);
    expect(displayText).toBe('John Doe (123)');

    const nullSupervisor = null as unknown as Supervisor;
    expect(component.displaySupervisor(nullSupervisor)).toBe('');
  });

  it('should handle CCPs save completion', () => {
    component.onCcpsSaveComplete();
    expect(alertService.success).toHaveBeenCalledWith(
      'Distribución de CCPs guardada exitosamente',
    );
  });

  it('should format month name correctly', () => {
    expect(component.getMonthName(0)).toBe('Enero');
    expect(component.getMonthName(11)).toBe('Diciembre');
  });

  it('should handle undefined values in formatters', () => {
    expect(component.formatDate(undefined)).toBe('N/A');
    expect(component.formatCurrency(undefined)).toBe('N/A');
  });

  it('should handle form initialization in supervisor mode', () => {
    component.isSupervisor = true;
    component['initializeForm']();
    expect(
      component.invoiceForm.get('hasElectronicInvoice')?.disabled,
    ).toBeTrue();
  });

  describe('Form Validation', () => {
    it('should validate invoice number when electronic invoice is enabled', () => {
      component.invoiceForm.patchValue({ hasElectronicInvoice: true });
      expect(component.invoiceForm.get('invoiceNumber')?.enabled).toBeTrue();

      component.invoiceForm.patchValue({ invoiceNumber: -1 });
      expect(component.invoiceForm.get('invoiceNumber')?.valid).toBeFalse();

      component.invoiceForm.patchValue({ invoiceNumber: 'abc' });
      expect(component.invoiceForm.get('invoiceNumber')?.valid).toBeFalse();

      component.invoiceForm.patchValue({ invoiceNumber: 123 });
      expect(component.invoiceForm.get('invoiceNumber')?.valid).toBeTrue();
    });

    it('should emit form validity changes', () => {
      const formValidityEmitSpy = spyOn(component.formValidityChange, 'emit');
      component.onCcpsValidityChange(true);
      component.invoiceForm.patchValue({
        hasElectronicInvoice: true,
        invoiceNumber: 123,
      });

      expect(formValidityEmitSpy).toHaveBeenCalledWith(true);
    });

    it('should handle form initialization in supervisor mode', () => {
      component.isSupervisor = true;
      component['initializeForm']();
      expect(
        component.invoiceForm.get('hasElectronicInvoice')?.disabled,
      ).toBeTrue();
    });
  });

  describe('Data Loading and Error Handling', () => {
    it('should handle errors when loading contract details', () => {
      contractService.getDetailsById.and.returnValue(
        throwError(() => new Error('Network error')),
      );
      component.loadContractDetails();
      expect(alertService.error).toHaveBeenCalledWith(
        'Error al obtener detalles del contrato',
      );
    });

    it('should handle errors when loading payments', () => {
      monthlyReportService.getByContractorContractId.and.returnValue(
        throwError(() => new Error('Network error')),
      );
      component.loadPayments();
      expect(alertService.error).toHaveBeenCalledWith(
        'Error al cargar los informes',
      );
    });

    it('should handle errors when loading supervisors', () => {
      supervisorService.getAll.and.returnValue(
        throwError(() => new Error('Network error')),
      );
      component.ngOnInit();
      expect(alertService.error).toHaveBeenCalledWith(
        'Error al cargar los supervisores',
      );
    });
  });

  describe('Supervisor Management', () => {
    beforeEach(() => {
      const mockSupervisor = {
        id: 1,
        idNumber: *********,
        fullName: 'Test Supervisor',
        email: '<EMAIL>',
        position: 'Test Position',
        idType: { id: 1, name: 'CC' },
      };
      component.supervisors = [mockSupervisor];
      component.supervisorForm.patchValue({ supervisor: mockSupervisor });
    });

    it('should handle supervisor contract creation success', () => {
      supervisorContractService.create.and.returnValue(
        of({ id: 1, supervisorId: 1, contractId: 1 }),
      );

      component.saveSupervisor();

      expect(supervisorContractService.create).toHaveBeenCalledWith({
        supervisorId: 1,
        contractId: 1,
      });
      expect(alertService.success).toHaveBeenCalledWith(
        'Supervisor asignado exitosamente',
      );
    });

    it('should handle supervisor contract creation error', () => {
      supervisorContractService.create.and.returnValue(
        throwError(() => new Error()),
      );

      component.saveSupervisor();

      expect(alertService.error).toHaveBeenCalledWith(
        'Error al asignar el supervisor',
      );
    });
  });

  describe('Data Formatting', () => {
    it('should format dates correctly', () => {
      const date = new Date('2024-01-01T00:00:00');
      expect(component.formatDate(date)).toBe('01/01/2024');
      expect(component.formatDate(undefined)).toBe('N/A');
      expect(component.formatDate('')).toBe('N/A');
    });

    it('should format currency values correctly', () => {
      const formattedValue = component.formatCurrency(1000000);
      expect(formattedValue).toContain('1.000.000,00');
      expect(formattedValue).toContain(',00');
      expect(component.formatCurrency(undefined)).toBe('N/A');
    });

    it('should get month names correctly', () => {
      expect(component.getMonthName(0)).toBe('Enero');
      expect(component.getMonthName(11)).toBe('Diciembre');
    });
  });

  describe('CCPs Management', () => {
    it('should handle CCPs validity changes', () => {
      const validityEmitSpy = spyOn(component.formValidityChange, 'emit');
      component.onCcpsValidityChange(true);
      expect(component.isCcpsValid).toBeTrue();
      expect(validityEmitSpy).toHaveBeenCalled();
    });

    it('should handle CCPs save completion', () => {
      component.onCcpsSaveComplete();
      expect(alertService.success).toHaveBeenCalledWith(
        'Distribución de CCPs guardada exitosamente',
      );
    });
  });
});