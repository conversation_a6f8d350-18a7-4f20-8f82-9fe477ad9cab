{"ast": null, "code": "import { FormControl } from '@angular/forms';\nimport { createTerminationDateValidator } from './termination-date.validator';\ndescribe('TerminationDateValidator', () => {\n  let control;\n  beforeEach(() => {\n    control = new FormControl(null);\n  });\n  it('should return null when value is null', () => {\n    const validator = createTerminationDateValidator(new Date());\n    const result = validator(control);\n    expect(result).toBeNull();\n  });\n  it('should return dateAfterContractEnd error when date is after contract end date', () => {\n    const contractEndDate = new Date('2024-12-31');\n    const validator = createTerminationDateValidator(contractEndDate);\n    control.setValue(new Date('2025-01-02'));\n    const result = validator(control);\n    expect(result).toEqual({\n      dateAfterContractEnd: true\n    });\n  });\n  it('should return null when date is within allowed range', () => {\n    const contractEndDate = new Date('2024-12-31');\n    const validator = createTerminationDateValidator(contractEndDate);\n    control.setValue(new Date('2024-12-31'));\n    const result = validator(control);\n    expect(result).toBeNull();\n  });\n  it('should return null when date is before contract end date', () => {\n    const contractEndDate = new Date('2024-12-31');\n    const validator = createTerminationDateValidator(contractEndDate);\n    control.setValue(new Date('2024-12-30'));\n    const result = validator(control);\n    expect(result).toBeNull();\n  });\n  it('should return null when latestContractEndDate is null', () => {\n    const validator = createTerminationDateValidator(null);\n    control.setValue(new Date('2024-12-31'));\n    const result = validator(control);\n    expect(result).toBeNull();\n  });\n});", "map": {"version": 3, "names": ["FormControl", "createTerminationDateValidator", "describe", "control", "beforeEach", "it", "validator", "Date", "result", "expect", "toBeNull", "contractEndDate", "setValue", "toEqual", "dateAfterContractEnd"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\components\\early-termination-dialog\\validators\\termination-date.validator.spec.ts"], "sourcesContent": ["import { FormControl } from '@angular/forms';\nimport { createTerminationDateValidator } from './termination-date.validator';\n\ndescribe('TerminationDateValidator', () => {\n  let control: FormControl;\n\n  beforeEach(() => {\n    control = new FormControl(null);\n  });\n\n  it('should return null when value is null', () => {\n    const validator = createTerminationDateValidator(new Date());\n    const result = validator(control);\n    expect(result).toBeNull();\n  });\n\n  it('should return dateAfterContractEnd error when date is after contract end date', () => {\n    const contractEndDate = new Date('2024-12-31');\n    const validator = createTerminationDateValidator(contractEndDate);\n    control.setValue(new Date('2025-01-02'));\n    const result = validator(control);\n    expect(result).toEqual({ dateAfterContractEnd: true });\n  });\n\n  it('should return null when date is within allowed range', () => {\n    const contractEndDate = new Date('2024-12-31');\n    const validator = createTerminationDateValidator(contractEndDate);\n    control.setValue(new Date('2024-12-31'));\n    const result = validator(control);\n    expect(result).toBeNull();\n  });\n\n  it('should return null when date is before contract end date', () => {\n    const contractEndDate = new Date('2024-12-31');\n    const validator = createTerminationDateValidator(contractEndDate);\n    control.setValue(new Date('2024-12-30'));\n    const result = validator(control);\n    expect(result).toBeNull();\n  });\n\n  it('should return null when latestContractEndDate is null', () => {\n    const validator = createTerminationDateValidator(null);\n    control.setValue(new Date('2024-12-31'));\n    const result = validator(control);\n    expect(result).toBeNull();\n  });\n});"], "mappings": "AAAA,SAASA,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,8BAA8B,QAAQ,8BAA8B;AAE7EC,QAAQ,CAAC,0BAA0B,EAAE,MAAK;EACxC,IAAIC,OAAoB;EAExBC,UAAU,CAAC,MAAK;IACdD,OAAO,GAAG,IAAIH,WAAW,CAAC,IAAI,CAAC;EACjC,CAAC,CAAC;EAEFK,EAAE,CAAC,uCAAuC,EAAE,MAAK;IAC/C,MAAMC,SAAS,GAAGL,8BAA8B,CAAC,IAAIM,IAAI,EAAE,CAAC;IAC5D,MAAMC,MAAM,GAAGF,SAAS,CAACH,OAAO,CAAC;IACjCM,MAAM,CAACD,MAAM,CAAC,CAACE,QAAQ,EAAE;EAC3B,CAAC,CAAC;EAEFL,EAAE,CAAC,+EAA+E,EAAE,MAAK;IACvF,MAAMM,eAAe,GAAG,IAAIJ,IAAI,CAAC,YAAY,CAAC;IAC9C,MAAMD,SAAS,GAAGL,8BAA8B,CAACU,eAAe,CAAC;IACjER,OAAO,CAACS,QAAQ,CAAC,IAAIL,IAAI,CAAC,YAAY,CAAC,CAAC;IACxC,MAAMC,MAAM,GAAGF,SAAS,CAACH,OAAO,CAAC;IACjCM,MAAM,CAACD,MAAM,CAAC,CAACK,OAAO,CAAC;MAAEC,oBAAoB,EAAE;IAAI,CAAE,CAAC;EACxD,CAAC,CAAC;EAEFT,EAAE,CAAC,sDAAsD,EAAE,MAAK;IAC9D,MAAMM,eAAe,GAAG,IAAIJ,IAAI,CAAC,YAAY,CAAC;IAC9C,MAAMD,SAAS,GAAGL,8BAA8B,CAACU,eAAe,CAAC;IACjER,OAAO,CAACS,QAAQ,CAAC,IAAIL,IAAI,CAAC,YAAY,CAAC,CAAC;IACxC,MAAMC,MAAM,GAAGF,SAAS,CAACH,OAAO,CAAC;IACjCM,MAAM,CAACD,MAAM,CAAC,CAACE,QAAQ,EAAE;EAC3B,CAAC,CAAC;EAEFL,EAAE,CAAC,0DAA0D,EAAE,MAAK;IAClE,MAAMM,eAAe,GAAG,IAAIJ,IAAI,CAAC,YAAY,CAAC;IAC9C,MAAMD,SAAS,GAAGL,8BAA8B,CAACU,eAAe,CAAC;IACjER,OAAO,CAACS,QAAQ,CAAC,IAAIL,IAAI,CAAC,YAAY,CAAC,CAAC;IACxC,MAAMC,MAAM,GAAGF,SAAS,CAACH,OAAO,CAAC;IACjCM,MAAM,CAACD,MAAM,CAAC,CAACE,QAAQ,EAAE;EAC3B,CAAC,CAAC;EAEFL,EAAE,CAAC,uDAAuD,EAAE,MAAK;IAC/D,MAAMC,SAAS,GAAGL,8BAA8B,CAAC,IAAI,CAAC;IACtDE,OAAO,CAACS,QAAQ,CAAC,IAAIL,IAAI,CAAC,YAAY,CAAC,CAAC;IACxC,MAAMC,MAAM,GAAGF,SAAS,CAACH,OAAO,CAAC;IACjCM,MAAM,CAACD,MAAM,CAAC,CAACE,QAAQ,EAAE;EAC3B,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}