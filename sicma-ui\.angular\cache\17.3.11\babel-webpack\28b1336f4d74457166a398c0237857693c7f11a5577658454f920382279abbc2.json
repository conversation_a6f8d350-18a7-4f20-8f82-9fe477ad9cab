{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { TrackingTypeService } from './tracking-type.service';\ndescribe('TrackingTypeService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/tracking-types`;\n  const mockTrackingType = {\n    id: 1,\n    name: 'Test Tracking Type'\n  };\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [TrackingTypeService]\n    });\n    service = TestBed.inject(TrackingTypeService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all tracking types', () => {\n      const mockTrackingTypes = [mockTrackingType];\n      service.getAll().subscribe(trackingTypes => {\n        expect(trackingTypes).toEqual(mockTrackingTypes);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockTrackingTypes);\n    });\n  });\n  describe('getById', () => {\n    it('should return a tracking type by id', () => {\n      const id = 1;\n      service.getById(id).subscribe(trackingType => {\n        expect(trackingType).toEqual(mockTrackingType);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockTrackingType);\n    });\n  });\n  describe('create', () => {\n    it('should create a new tracking type', () => {\n      const newTrackingType = {\n        name: 'New Tracking Type'\n      };\n      service.create(newTrackingType).subscribe(trackingType => {\n        expect(trackingType).toEqual(mockTrackingType);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newTrackingType);\n      req.flush(mockTrackingType);\n    });\n  });\n  describe('update', () => {\n    it('should update a tracking type', () => {\n      const id = 1;\n      const updateData = {\n        name: 'Updated Tracking Type'\n      };\n      service.update(id, updateData).subscribe(trackingType => {\n        expect(trackingType).toEqual(mockTrackingType);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockTrackingType);\n    });\n  });\n  describe('delete', () => {\n    it('should delete a tracking type', () => {\n      const id = 1;\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n  describe('getByName', () => {\n    it('should return a tracking type by name', () => {\n      const name = 'Test Tracking Type';\n      service.getByName(name).subscribe(trackingType => {\n        expect(trackingType).toEqual(mockTrackingType);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockTrackingType);\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "TrackingTypeService", "describe", "service", "httpMock", "apiUrl", "mockTrackingType", "id", "name", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockTrackingTypes", "getAll", "subscribe", "trackingTypes", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "getById", "trackingType", "newTrackingType", "create", "body", "updateData", "update", "delete", "nothing", "getByName"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\services\\tracking-type.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { TrackingType } from '@contract-management/models/tracking-type.model';\nimport { environment } from '@env';\nimport { TrackingTypeService } from './tracking-type.service';\n\ndescribe('TrackingTypeService', () => {\n  let service: TrackingTypeService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/tracking-types`;\n\n  const mockTrackingType: TrackingType = {\n    id: 1,\n    name: 'Test Tracking Type',\n  };\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [TrackingTypeService],\n    });\n    service = TestBed.inject(TrackingTypeService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all tracking types', () => {\n      const mockTrackingTypes = [mockTrackingType];\n\n      service.getAll().subscribe((trackingTypes) => {\n        expect(trackingTypes).toEqual(mockTrackingTypes);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockTrackingTypes);\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a tracking type by id', () => {\n      const id = 1;\n\n      service.getById(id).subscribe((trackingType) => {\n        expect(trackingType).toEqual(mockTrackingType);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockTrackingType);\n    });\n  });\n\n  describe('create', () => {\n    it('should create a new tracking type', () => {\n      const newTrackingType: Omit<TrackingType, 'id'> = {\n        name: 'New Tracking Type',\n      };\n\n      service.create(newTrackingType).subscribe((trackingType) => {\n        expect(trackingType).toEqual(mockTrackingType);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newTrackingType);\n      req.flush(mockTrackingType);\n    });\n  });\n\n  describe('update', () => {\n    it('should update a tracking type', () => {\n      const id = 1;\n      const updateData: Partial<TrackingType> = {\n        name: 'Updated Tracking Type',\n      };\n\n      service.update(id, updateData).subscribe((trackingType) => {\n        expect(trackingType).toEqual(mockTrackingType);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockTrackingType);\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete a tracking type', () => {\n      const id = 1;\n\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n\n  describe('getByName', () => {\n    it('should return a tracking type by name', () => {\n      const name = 'Test Tracking Type';\n\n      service.getByName(name).subscribe((trackingType) => {\n        expect(trackingType).toEqual(mockTrackingType);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockTrackingType);\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,mBAAmB,QAAQ,yBAAyB;AAE7DC,QAAQ,CAAC,qBAAqB,EAAE,MAAK;EACnC,IAAIC,OAA4B;EAChC,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,iBAAiB;EAErD,MAAMC,gBAAgB,GAAiB;IACrCC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE;GACP;EAEDC,UAAU,CAAC,MAAK;IACdV,OAAO,CAACW,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACd,uBAAuB,CAAC;MAClCe,SAAS,EAAE,CAACX,mBAAmB;KAChC,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACc,MAAM,CAACZ,mBAAmB,CAAC;IAC7CG,QAAQ,GAAGL,OAAO,CAACc,MAAM,CAACf,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFgB,SAAS,CAAC,MAAK;IACbV,QAAQ,CAACW,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACd,OAAO,CAAC,CAACe,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFhB,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,kCAAkC,EAAE,MAAK;MAC1C,MAAMG,iBAAiB,GAAG,CAACb,gBAAgB,CAAC;MAE5CH,OAAO,CAACiB,MAAM,EAAE,CAACC,SAAS,CAAEC,aAAa,IAAI;QAC3CL,MAAM,CAACK,aAAa,CAAC,CAACC,OAAO,CAACJ,iBAAiB,CAAC;MAClD,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,iBAAiB,CAAC;IAC9B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjB,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBc,EAAE,CAAC,qCAAqC,EAAE,MAAK;MAC7C,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAAC2B,OAAO,CAACvB,EAAE,CAAC,CAACc,SAAS,CAAEU,YAAY,IAAI;QAC7Cd,MAAM,CAACc,YAAY,CAAC,CAACR,OAAO,CAACjB,gBAAgB,CAAC;MAChD,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvB,gBAAgB,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,mCAAmC,EAAE,MAAK;MAC3C,MAAMgB,eAAe,GAA6B;QAChDxB,IAAI,EAAE;OACP;MAEDL,OAAO,CAAC8B,MAAM,CAACD,eAAe,CAAC,CAACX,SAAS,CAAEU,YAAY,IAAI;QACzDd,MAAM,CAACc,YAAY,CAAC,CAACR,OAAO,CAACjB,gBAAgB,CAAC;MAChD,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACS,eAAe,CAAC;MACjDR,GAAG,CAACK,KAAK,CAACvB,gBAAgB,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,+BAA+B,EAAE,MAAK;MACvC,MAAMT,EAAE,GAAG,CAAC;MACZ,MAAM4B,UAAU,GAA0B;QACxC3B,IAAI,EAAE;OACP;MAEDL,OAAO,CAACiC,MAAM,CAAC7B,EAAE,EAAE4B,UAAU,CAAC,CAACd,SAAS,CAAEU,YAAY,IAAI;QACxDd,MAAM,CAACc,YAAY,CAAC,CAACR,OAAO,CAACjB,gBAAgB,CAAC;MAChD,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACY,UAAU,CAAC;MAC5CX,GAAG,CAACK,KAAK,CAACvB,gBAAgB,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,+BAA+B,EAAE,MAAK;MACvC,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAACkC,MAAM,CAAC9B,EAAE,CAAC,CAACc,SAAS,CAAC,MAAK;QAChCJ,MAAM,EAAE,CAACqB,OAAO,EAAE;MACpB,CAAC,CAAC;MAEF,MAAMd,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF3B,QAAQ,CAAC,WAAW,EAAE,MAAK;IACzBc,EAAE,CAAC,uCAAuC,EAAE,MAAK;MAC/C,MAAMR,IAAI,GAAG,oBAAoB;MAEjCL,OAAO,CAACoC,SAAS,CAAC/B,IAAI,CAAC,CAACa,SAAS,CAAEU,YAAY,IAAI;QACjDd,MAAM,CAACc,YAAY,CAAC,CAACR,OAAO,CAACjB,gBAAgB,CAAC;MAChD,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,SAASG,IAAI,EAAE,CAAC;MACxDS,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvB,gBAAgB,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}