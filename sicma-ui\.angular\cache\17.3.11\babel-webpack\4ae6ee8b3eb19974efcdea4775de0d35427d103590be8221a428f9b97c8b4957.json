{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { CompensationFundService } from './compensation-fund.service';\ndescribe('CompensationFundService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/compensation-funds`;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [CompensationFundService]\n    });\n    service = TestBed.inject(CompensationFundService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  const mockCompensationFund = {\n    id: 1,\n    name: 'Comfama'\n  };\n  describe('getAll', () => {\n    it('should return all compensation funds', () => {\n      const mockCompensationFunds = [mockCompensationFund];\n      service.getAll().subscribe(compensationFunds => {\n        expect(compensationFunds).toEqual(mockCompensationFunds);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockCompensationFunds);\n    });\n    it('should handle error when getting all compensation funds', () => {\n      service.getAll().subscribe({\n        error: error => {\n          expect(error.status).toBe(500);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Server error'), {\n        status: 500\n      });\n    });\n  });\n  describe('getById', () => {\n    it('should return a compensation fund by id', () => {\n      service.getById(1).subscribe(compensationFund => {\n        expect(compensationFund).toEqual(mockCompensationFund);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockCompensationFund);\n    });\n    it('should handle error when getting compensation fund by id', () => {\n      service.getById(999).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/999`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n  describe('create', () => {\n    const newCompensationFund = {\n      name: 'New Fund'\n    };\n    it('should create a new compensation fund', () => {\n      service.create(newCompensationFund).subscribe(compensationFund => {\n        expect(compensationFund).toEqual(mockCompensationFund);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newCompensationFund);\n      req.flush(mockCompensationFund);\n    });\n    it('should handle error when creating compensation fund', () => {\n      service.create(newCompensationFund).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n  describe('update', () => {\n    const updateData = {\n      name: 'Updated Name'\n    };\n    it('should update a compensation fund', () => {\n      service.update(1, updateData).subscribe(compensationFund => {\n        expect(compensationFund).toEqual({\n          ...mockCompensationFund,\n          ...updateData\n        });\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush({\n        ...mockCompensationFund,\n        ...updateData\n      });\n    });\n    it('should handle error when updating compensation fund', () => {\n      service.update(1, updateData).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n  describe('delete', () => {\n    it('should delete a compensation fund', () => {\n      service.delete(1).subscribe(response => {\n        expect(response).toBeNull();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n    it('should handle error when deleting compensation fund', () => {\n      service.delete(1).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n  describe('getByName', () => {\n    it('should return a compensation fund by name', () => {\n      const name = 'Comfama';\n      service.getByName(name).subscribe(compensationFund => {\n        expect(compensationFund).toEqual(mockCompensationFund);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockCompensationFund);\n    });\n    it('should handle error when getting compensation fund by name', () => {\n      const name = 'NonExistent';\n      service.getByName(name).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "CompensationFundService", "describe", "service", "httpMock", "apiUrl", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockCompensationFund", "id", "name", "mockCompensationFunds", "getAll", "subscribe", "compensationFunds", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "error", "status", "ErrorEvent", "getById", "compensationFund", "newCompensationFund", "create", "body", "updateData", "update", "delete", "response", "toBeNull", "getByName"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\services\\compensation-fund.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { CompensationFund } from '@contractor-dashboard/models/compensation-fund.model';\nimport { environment } from '@env';\nimport { CompensationFundService } from './compensation-fund.service';\n\ndescribe('CompensationFundService', () => {\n  let service: CompensationFundService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/compensation-funds`;\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [CompensationFundService],\n    });\n    service = TestBed.inject(CompensationFundService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  const mockCompensationFund: CompensationFund = {\n    id: 1,\n    name: 'Comfama',\n  };\n\n  describe('getAll', () => {\n    it('should return all compensation funds', () => {\n      const mockCompensationFunds = [mockCompensationFund];\n\n      service.getAll().subscribe((compensationFunds) => {\n        expect(compensationFunds).toEqual(mockCompensationFunds);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockCompensationFunds);\n    });\n\n    it('should handle error when getting all compensation funds', () => {\n      service.getAll().subscribe({\n        error: (error) => {\n          expect(error.status).toBe(500);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Server error'), { status: 500 });\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a compensation fund by id', () => {\n      service.getById(1).subscribe((compensationFund) => {\n        expect(compensationFund).toEqual(mockCompensationFund);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockCompensationFund);\n    });\n\n    it('should handle error when getting compensation fund by id', () => {\n      service.getById(999).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/999`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n\n  describe('create', () => {\n    const newCompensationFund: Omit<CompensationFund, 'id'> = {\n      name: 'New Fund',\n    };\n\n    it('should create a new compensation fund', () => {\n      service.create(newCompensationFund).subscribe((compensationFund) => {\n        expect(compensationFund).toEqual(mockCompensationFund);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newCompensationFund);\n      req.flush(mockCompensationFund);\n    });\n\n    it('should handle error when creating compensation fund', () => {\n      service.create(newCompensationFund).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n\n  describe('update', () => {\n    const updateData: Partial<CompensationFund> = {\n      name: 'Updated Name',\n    };\n\n    it('should update a compensation fund', () => {\n      service.update(1, updateData).subscribe((compensationFund) => {\n        expect(compensationFund).toEqual({\n          ...mockCompensationFund,\n          ...updateData,\n        });\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush({ ...mockCompensationFund, ...updateData });\n    });\n\n    it('should handle error when updating compensation fund', () => {\n      service.update(1, updateData).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete a compensation fund', () => {\n      service.delete(1).subscribe((response) => {\n        expect(response).toBeNull();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n\n    it('should handle error when deleting compensation fund', () => {\n      service.delete(1).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n\n  describe('getByName', () => {\n    it('should return a compensation fund by name', () => {\n      const name = 'Comfama';\n\n      service.getByName(name).subscribe((compensationFund) => {\n        expect(compensationFund).toEqual(mockCompensationFund);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockCompensationFund);\n    });\n\n    it('should handle error when getting compensation fund by name', () => {\n      const name = 'NonExistent';\n\n      service.getByName(name).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,uBAAuB,QAAQ,6BAA6B;AAErEC,QAAQ,CAAC,yBAAyB,EAAE,MAAK;EACvC,IAAIC,OAAgC;EACpC,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,qBAAqB;EAEzDC,UAAU,CAAC,MAAK;IACdP,OAAO,CAACQ,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACX,uBAAuB,CAAC;MAClCY,SAAS,EAAE,CAACR,uBAAuB;KACpC,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACW,MAAM,CAACT,uBAAuB,CAAC;IACjDG,QAAQ,GAAGL,OAAO,CAACW,MAAM,CAACZ,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFa,SAAS,CAAC,MAAK;IACbP,QAAQ,CAACQ,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACX,OAAO,CAAC,CAACY,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEF,MAAMC,oBAAoB,GAAqB;IAC7CC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE;GACP;EAEDhB,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBW,EAAE,CAAC,sCAAsC,EAAE,MAAK;MAC9C,MAAMM,qBAAqB,GAAG,CAACH,oBAAoB,CAAC;MAEpDb,OAAO,CAACiB,MAAM,EAAE,CAACC,SAAS,CAAEC,iBAAiB,IAAI;QAC/CR,MAAM,CAACQ,iBAAiB,CAAC,CAACC,OAAO,CAACJ,qBAAqB,CAAC;MAC1D,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCS,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,qBAAqB,CAAC;IAClC,CAAC,CAAC;IAEFN,EAAE,CAAC,yDAAyD,EAAE,MAAK;MACjEV,OAAO,CAACiB,MAAM,EAAE,CAACC,SAAS,CAAC;QACzBS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,cAAc,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC5D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBW,EAAE,CAAC,yCAAyC,EAAE,MAAK;MACjDV,OAAO,CAAC8B,OAAO,CAAC,CAAC,CAAC,CAACZ,SAAS,CAAEa,gBAAgB,IAAI;QAChDpB,MAAM,CAACoB,gBAAgB,CAAC,CAACX,OAAO,CAACP,oBAAoB,CAAC;MACxD,CAAC,CAAC;MAEF,MAAMQ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAI,CAAC;MAC7CS,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACb,oBAAoB,CAAC;IACjC,CAAC,CAAC;IAEFH,EAAE,CAAC,0DAA0D,EAAE,MAAK;MAClEV,OAAO,CAAC8B,OAAO,CAAC,GAAG,CAAC,CAACZ,SAAS,CAAC;QAC7BS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,MAAM,CAAC;MAC/CmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB,MAAMiC,mBAAmB,GAAiC;MACxDjB,IAAI,EAAE;KACP;IAEDL,EAAE,CAAC,uCAAuC,EAAE,MAAK;MAC/CV,OAAO,CAACiC,MAAM,CAACD,mBAAmB,CAAC,CAACd,SAAS,CAAEa,gBAAgB,IAAI;QACjEpB,MAAM,CAACoB,gBAAgB,CAAC,CAACX,OAAO,CAACP,oBAAoB,CAAC;MACxD,CAAC,CAAC;MAEF,MAAMQ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCS,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCd,MAAM,CAACU,GAAG,CAACE,OAAO,CAACW,IAAI,CAAC,CAACd,OAAO,CAACY,mBAAmB,CAAC;MACrDX,GAAG,CAACK,KAAK,CAACb,oBAAoB,CAAC;IACjC,CAAC,CAAC;IAEFH,EAAE,CAAC,qDAAqD,EAAE,MAAK;MAC7DV,OAAO,CAACiC,MAAM,CAACD,mBAAmB,CAAC,CAACd,SAAS,CAAC;QAC5CS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB,MAAMoC,UAAU,GAA8B;MAC5CpB,IAAI,EAAE;KACP;IAEDL,EAAE,CAAC,mCAAmC,EAAE,MAAK;MAC3CV,OAAO,CAACoC,MAAM,CAAC,CAAC,EAAED,UAAU,CAAC,CAACjB,SAAS,CAAEa,gBAAgB,IAAI;QAC3DpB,MAAM,CAACoB,gBAAgB,CAAC,CAACX,OAAO,CAAC;UAC/B,GAAGP,oBAAoB;UACvB,GAAGsB;SACJ,CAAC;MACJ,CAAC,CAAC;MAEF,MAAMd,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAI,CAAC;MAC7CS,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCd,MAAM,CAACU,GAAG,CAACE,OAAO,CAACW,IAAI,CAAC,CAACd,OAAO,CAACe,UAAU,CAAC;MAC5Cd,GAAG,CAACK,KAAK,CAAC;QAAE,GAAGb,oBAAoB;QAAE,GAAGsB;MAAU,CAAE,CAAC;IACvD,CAAC,CAAC;IAEFzB,EAAE,CAAC,qDAAqD,EAAE,MAAK;MAC7DV,OAAO,CAACoC,MAAM,CAAC,CAAC,EAAED,UAAU,CAAC,CAACjB,SAAS,CAAC;QACtCS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAI,CAAC;MAC7CmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBW,EAAE,CAAC,mCAAmC,EAAE,MAAK;MAC3CV,OAAO,CAACqC,MAAM,CAAC,CAAC,CAAC,CAACnB,SAAS,CAAEoB,QAAQ,IAAI;QACvC3B,MAAM,CAAC2B,QAAQ,CAAC,CAACC,QAAQ,EAAE;MAC7B,CAAC,CAAC;MAEF,MAAMlB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAI,CAAC;MAC7CS,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;IAEFhB,EAAE,CAAC,qDAAqD,EAAE,MAAK;MAC7DV,OAAO,CAACqC,MAAM,CAAC,CAAC,CAAC,CAACnB,SAAS,CAAC;QAC1BS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAI,CAAC;MAC7CmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,WAAW,EAAE,MAAK;IACzBW,EAAE,CAAC,2CAA2C,EAAE,MAAK;MACnD,MAAMK,IAAI,GAAG,SAAS;MAEtBf,OAAO,CAACwC,SAAS,CAACzB,IAAI,CAAC,CAACG,SAAS,CAAEa,gBAAgB,IAAI;QACrDpB,MAAM,CAACoB,gBAAgB,CAAC,CAACX,OAAO,CAACP,oBAAoB,CAAC;MACxD,CAAC,CAAC;MAEF,MAAMQ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,SAASa,IAAI,EAAE,CAAC;MACxDJ,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACb,oBAAoB,CAAC;IACjC,CAAC,CAAC;IAEFH,EAAE,CAAC,4DAA4D,EAAE,MAAK;MACpE,MAAMK,IAAI,GAAG,aAAa;MAE1Bf,OAAO,CAACwC,SAAS,CAACzB,IAAI,CAAC,CAACG,SAAS,CAAC;QAChCS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,SAASa,IAAI,EAAE,CAAC;MACxDM,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}