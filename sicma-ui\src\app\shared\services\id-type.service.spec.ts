import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { environment } from '@env';
import { IDType } from '@shared/models/id-type.model';
import { IDTypeService } from './id-type.service';

describe('IDTypeService', () => {
  let service: IDTypeService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/id-types`;

  const mockIDType: IDType = {
    id: 1,
    name: 'Test ID Type',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [IDTypeService],
    });
    service = TestBed.inject(IDTypeService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all ID types', () => {
      const mockIDTypes = [mockIDType];

      service.getAll().subscribe((idTypes) => {
        expect(idTypes).toEqual(mockIDTypes);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockIDTypes);
    });
  });

  describe('getById', () => {
    it('should return an ID type by id', () => {
      const id = 1;

      service.getById(id).subscribe((idType) => {
        expect(idType).toEqual(mockIDType);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockIDType);
    });
  });

  describe('create', () => {
    it('should create a new ID type', () => {
      const newIDType: Omit<IDType, 'id'> = {
        name: 'New ID Type',
      };

      service.create(newIDType).subscribe((idType) => {
        expect(idType).toEqual(mockIDType);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newIDType);
      req.flush(mockIDType);
    });
  });

  describe('update', () => {
    it('should update an ID type', () => {
      const id = 1;
      const updateData: Partial<IDType> = {
        name: 'Updated ID Type',
      };

      service.update(id, updateData).subscribe((idType) => {
        expect(idType).toEqual(mockIDType);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockIDType);
    });
  });

  describe('delete', () => {
    it('should delete an ID type', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });

  describe('getByName', () => {
    it('should return an ID type by name', () => {
      const name = 'Test ID Type';

      service.getByName(name).subscribe((idType) => {
        expect(idType).toEqual(mockIDType);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockIDType);
    });
  });
});