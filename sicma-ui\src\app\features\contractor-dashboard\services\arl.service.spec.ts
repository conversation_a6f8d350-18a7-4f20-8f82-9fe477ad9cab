import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { ARL } from '@contractor-dashboard/models/arl.model';
import { environment } from '@env';
import { ArlService } from './arl.service';

describe('ArlService', () => {
  let service: ArlService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/arls`;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ArlService],
    });
    service = TestBed.inject(ArlService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  const mockArl: ARL = {
    id: 1,
    name: 'Sura',
  };

  describe('getAll', () => {
    it('should return all ARLs', () => {
      const mockArls = [mockArl];

      service.getAll().subscribe((arls) => {
        expect(arls).toEqual(mockArls);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockArls);
    });

    it('should handle error when getting all ARLs', () => {
      service.getAll().subscribe({
        error: (error) => {
          expect(error.status).toBe(500);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Server error'), { status: 500 });
    });
  });

  describe('getById', () => {
    it('should return an ARL by id', () => {
      service.getById(1).subscribe((arl) => {
        expect(arl).toEqual(mockArl);
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('GET');
      req.flush(mockArl);
    });

    it('should handle error when getting ARL by id', () => {
      service.getById(999).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/999`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('create', () => {
    const newArl = {
      name: 'New ARL',
    };

    it('should create a new ARL', () => {
      service.create(newArl).subscribe((arl) => {
        expect(arl).toEqual(mockArl);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newArl);
      req.flush(mockArl);
    });

    it('should handle error when creating ARL', () => {
      service.create(newArl).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('update', () => {
    const updateData = {
      name: 'Updated Name',
    };

    it('should update an ARL', () => {
      service.update(1, updateData).subscribe((arl) => {
        expect(arl).toEqual({ ...mockArl, ...updateData });
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush({ ...mockArl, ...updateData });
    });

    it('should handle error when updating ARL', () => {
      service.update(1, updateData).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('delete', () => {
    it('should delete an ARL', () => {
      service.delete(1).subscribe((response) => {
        expect(response).toBeNull();
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });

    it('should handle error when deleting ARL', () => {
      service.delete(1).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('getByName', () => {
    it('should return an ARL by name', () => {
      const name = 'Sura';

      service.getByName(name).subscribe((arl) => {
        expect(arl).toEqual(mockArl);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockArl);
    });

    it('should handle error when getting ARL by name', () => {
      const name = 'NonExistent';

      service.getByName(name).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });
});