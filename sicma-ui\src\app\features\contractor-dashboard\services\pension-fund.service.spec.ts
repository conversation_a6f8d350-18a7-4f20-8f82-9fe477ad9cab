import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { PensionFund } from '@contractor-dashboard/models/pension-fund.model';
import { environment } from '@env';
import { PensionFundService } from './pension-fund.service';

describe('PensionFundService', () => {
  let service: PensionFundService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/pension-funds`;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [PensionFundService],
    });
    service = TestBed.inject(PensionFundService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  const mockPensionFund: PensionFund = {
    id: 1,
    name: 'Porvenir',
  };

  describe('getAll', () => {
    it('should return all pension funds', () => {
      const mockPensionFunds = [mockPensionFund];

      service.getAll().subscribe((pensionFunds) => {
        expect(pensionFunds).toEqual(mockPensionFunds);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockPensionFunds);
    });

    it('should handle error when getting all pension funds', () => {
      service.getAll().subscribe({
        error: (error) => {
          expect(error.status).toBe(500);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Server error'), { status: 500 });
    });
  });

  describe('getById', () => {
    it('should return a pension fund by id', () => {
      service.getById(1).subscribe((pensionFund) => {
        expect(pensionFund).toEqual(mockPensionFund);
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('GET');
      req.flush(mockPensionFund);
    });

    it('should handle error when getting pension fund by id', () => {
      service.getById(999).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/999`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('create', () => {
    const newPensionFund: Omit<PensionFund, 'id'> = {
      name: 'New Fund',
    };

    it('should create a new pension fund', () => {
      service.create(newPensionFund).subscribe((pensionFund) => {
        expect(pensionFund).toEqual(mockPensionFund);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newPensionFund);
      req.flush(mockPensionFund);
    });

    it('should handle error when creating pension fund', () => {
      service.create(newPensionFund).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('update', () => {
    const updateData: Partial<PensionFund> = {
      name: 'Updated Name',
    };

    it('should update a pension fund', () => {
      service.update(1, updateData).subscribe((pensionFund) => {
        expect(pensionFund).toEqual({ ...mockPensionFund, ...updateData });
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush({ ...mockPensionFund, ...updateData });
    });

    it('should handle error when updating pension fund', () => {
      service.update(1, updateData).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('delete', () => {
    it('should delete a pension fund', () => {
      service.delete(1).subscribe((response) => {
        expect(response).toBeNull();
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });

    it('should handle error when deleting pension fund', () => {
      service.delete(1).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('getByName', () => {
    it('should return a pension fund by name', () => {
      const name = 'Porvenir';

      service.getByName(name).subscribe((pensionFund) => {
        expect(pensionFund).toEqual(mockPensionFund);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockPensionFund);
    });

    it('should handle error when getting pension fund by name', () => {
      const name = 'NonExistent';

      service.getByName(name).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });
});