import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { Entity } from '@contract-management/models/entity.model';
import { environment } from '@env';
import { EntityService } from './entity.service';

describe('EntityService', () => {
  let service: EntityService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/entities`;

  const mockEntity: Entity = {
    id: 1,
    name: 'Test Entity',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [EntityService],
    });
    service = TestBed.inject(EntityService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all entities', () => {
      const mockEntities = [mockEntity];

      service.getAll().subscribe((entities) => {
        expect(entities).toEqual(mockEntities);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockEntities);
    });
  });

  describe('getById', () => {
    it('should return an entity by id', () => {
      const id = 1;

      service.getById(id).subscribe((entity) => {
        expect(entity).toEqual(mockEntity);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockEntity);
    });
  });

  describe('create', () => {
    it('should create a new entity', () => {
      const newEntity: Omit<Entity, 'id'> = {
        name: 'New Entity',
      };

      service.create(newEntity).subscribe((entity) => {
        expect(entity).toEqual(mockEntity);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newEntity);
      req.flush(mockEntity);
    });
  });

  describe('update', () => {
    it('should update an entity', () => {
      const id = 1;
      const updateData: Partial<Entity> = {
        name: 'Updated Entity',
      };

      service.update(id, updateData).subscribe((entity) => {
        expect(entity).toEqual(mockEntity);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockEntity);
    });
  });

  describe('delete', () => {
    it('should delete an entity', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });

  describe('getByName', () => {
    it('should return an entity by name', () => {
      const name = 'Test Entity';

      service.getByName(name).subscribe((entity) => {
        expect(entity).toEqual(mockEntity);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockEntity);
    });
  });
});