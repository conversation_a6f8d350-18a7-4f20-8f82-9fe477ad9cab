import { HttpClientTestingModule } from '@angular/common/http/testing';
import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import {
  MatDialog,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ContractorDialogComponent } from '@contractor-management/components/contractor-dialog/contractor-dialog.component';
import { Contractor } from '@contractor-management/models/contractor.model';
import { ContractorService } from '@contractor-management/services/contractor.service';
import { AlertService } from '@shared/services/alert.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { of, throwError } from 'rxjs';
import { ContractorsListPageComponent } from './contractors-list-page.component';

describe('ContractorsListPageComponent', () => {
  let component: ContractorsListPageComponent;
  let fixture: ComponentFixture<ContractorsListPageComponent>;
  let contractorService: jasmine.SpyObj<ContractorService>;
  let dialog: jasmine.SpyObj<MatDialog>;
  let alertService: jasmine.SpyObj<AlertService>;
  let spinnerService: jasmine.SpyObj<NgxSpinnerService>;

  const mockContractor: Contractor = {
    id: 1,
    fullName: 'Test Contractor',
    idNumber: 123456789,
    personalEmail: '<EMAIL>',
    idType: { id: 1, name: 'CC' },
  };

  beforeEach(() => {
    const contractorServiceSpy = jasmine.createSpyObj('ContractorService', [
      'getAll',
    ]);
    const dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);
    const alertServiceSpy = jasmine.createSpyObj('AlertService', ['error']);
    const spinnerServiceSpy = jasmine.createSpyObj('NgxSpinnerService', [
      'show',
      'hide',
    ]);

    TestBed.configureTestingModule({
      imports: [
        ContractorsListPageComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        MatDialogModule,
        MatPaginatorModule,
        MatSortModule,
      ],
      providers: [
        { provide: ContractorService, useValue: contractorServiceSpy },
        { provide: MatDialog, useValue: dialogSpy },
        { provide: AlertService, useValue: alertServiceSpy },
        { provide: NgxSpinnerService, useValue: spinnerServiceSpy },
      ],
    });

    fixture = TestBed.createComponent(ContractorsListPageComponent);
    component = fixture.componentInstance;
    contractorService = TestBed.inject(
      ContractorService,
    ) as jasmine.SpyObj<ContractorService>;
    dialog = TestBed.inject(MatDialog) as jasmine.SpyObj<MatDialog>;
    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;
    spinnerService = TestBed.inject(
      NgxSpinnerService,
    ) as jasmine.SpyObj<NgxSpinnerService>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with empty data', () => {
    expect(component.dataSource.data).toEqual([]);
  });

  it('should load contractors on init', fakeAsync(() => {
    const mockContractors = [mockContractor];
    contractorService.getAll.and.returnValue(of(mockContractors));

    component.ngOnInit();
    tick();

    expect(spinnerService.show).toHaveBeenCalled();
    expect(contractorService.getAll).toHaveBeenCalled();
    expect(component.dataSource.data).toEqual(mockContractors);
    expect(spinnerService.hide).toHaveBeenCalled();
  }));

  it('should handle error when loading contractors', fakeAsync(() => {
    contractorService.getAll.and.returnValue(
      throwError(() => ({ error: 'Error' })),
    );

    component.ngOnInit();
    tick();

    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar contratistas',
    );
    expect(spinnerService.hide).toHaveBeenCalled();
  }));

  it('should set up paginator and sort after view init', () => {
    contractorService.getAll.and.returnValue(of([]));
    fixture.detectChanges();
    component.ngAfterViewInit();
    expect(component.dataSource.paginator).toBeTruthy();
    expect(component.dataSource.sort).toBeTruthy();
  });

  it('should open dialog to create new contractor', () => {
    const dialogRef = {
      afterClosed: () => of(mockContractor),
    } as MatDialogRef<ContractorDialogComponent>;

    dialog.open.and.returnValue(dialogRef);

    component.openContractorDialog();

    expect(dialog.open).toHaveBeenCalledWith(ContractorDialogComponent, {
      width: '95%',
      maxWidth: '800px',
      height: 'auto',
      maxHeight: '90vh',
      data: undefined,
    });
  });

  it('should open dialog to edit existing contractor', () => {
    const dialogRef = {
      afterClosed: () => of(mockContractor),
    } as MatDialogRef<ContractorDialogComponent>;

    dialog.open.and.returnValue(dialogRef);

    component.openContractorDialog(mockContractor);

    expect(dialog.open).toHaveBeenCalledWith(ContractorDialogComponent, {
      width: '95%',
      maxWidth: '800px',
      height: 'auto',
      maxHeight: '90vh',
      data: mockContractor,
    });
  });

  it('should update data source when creating new contractor', () => {
    const dialogRef = {
      afterClosed: () => of(mockContractor),
    } as MatDialogRef<ContractorDialogComponent>;

    dialog.open.and.returnValue(dialogRef);
    component.dataSource.data = [];

    component.openContractorDialog();

    expect(component.dataSource.data).toEqual([mockContractor]);
  });

  it('should update data source when editing contractor', () => {
    const updatedContractor = { ...mockContractor, fullName: 'Updated Name' };
    const dialogRef = {
      afterClosed: () => of(updatedContractor),
    } as MatDialogRef<ContractorDialogComponent>;

    dialog.open.and.returnValue(dialogRef);
    component.dataSource.data = [mockContractor];

    component.openContractorDialog(mockContractor);

    expect(component.dataSource.data).toEqual([updatedContractor]);
  });

  it('should not update data source when dialog is closed without result', () => {
    const dialogRef = {
      afterClosed: () => of(undefined),
    } as MatDialogRef<ContractorDialogComponent>;

    dialog.open.and.returnValue(dialogRef);
    const initialData = [mockContractor];
    component.dataSource.data = initialData;

    component.openContractorDialog(mockContractor);

    expect(component.dataSource.data).toEqual(initialData);
  });

  it('should apply filter correctly', () => {
    const event = new Event('input');
    Object.defineProperty(event, 'target', { value: { value: 'test' } });

    component.applyFilter(event);

    expect(component.dataSource.filter).toBe('TEST');
  });

  it('should reset to first page when filtering', () => {
    const event = new Event('input');
    Object.defineProperty(event, 'target', { value: { value: 'test' } });

    contractorService.getAll.and.returnValue(of([]));
    fixture.detectChanges();
    component.ngAfterViewInit();

    const paginatorSpy = spyOn(component.dataSource.paginator!, 'firstPage');

    component.applyFilter(event);

    expect(paginatorSpy).toHaveBeenCalled();
  });
});