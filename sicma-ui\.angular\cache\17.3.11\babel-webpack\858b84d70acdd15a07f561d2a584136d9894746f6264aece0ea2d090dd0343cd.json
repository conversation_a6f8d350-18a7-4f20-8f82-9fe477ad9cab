{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/Github/SICMA/sicma-ui/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { NoopAnimationsModule } from '@angular/platform-browser/animations';\nimport { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { ReductionDialogComponent } from './reduction-dialog.component';\ndescribe('ReductionDialogComponent', () => {\n  let component;\n  let fixture;\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    yield TestBed.configureTestingModule({\n      imports: [ReductionDialogComponent, HttpClientTestingModule, NoopAnimationsModule],\n      providers: [{\n        provide: MatDialogRef,\n        useValue: {\n          close: jasmine.createSpy('close')\n        }\n      }, {\n        provide: MAT_DIALOG_DATA,\n        useValue: {}\n      }]\n    }).compileComponents();\n    fixture = TestBed.createComponent(ReductionDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  }));\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "HttpClientTestingModule", "NoopAnimationsModule", "MatDialogRef", "MAT_DIALOG_DATA", "ReductionDialogComponent", "describe", "component", "fixture", "beforeEach", "_asyncToGenerator", "configureTestingModule", "imports", "providers", "provide", "useValue", "close", "jasmine", "createSpy", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\components\\reductions-list\\reduction-dialog\\reduction-dialog.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { NoopAnimationsModule } from '@angular/platform-browser/animations';\nimport { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';\n\nimport { ReductionDialogComponent } from './reduction-dialog.component';\n\ndescribe('ReductionDialogComponent', () => {\n  let component: ReductionDialogComponent;\n  let fixture: ComponentFixture<ReductionDialogComponent>;\n\n  beforeEach(async () => {\n    await TestBed.configureTestingModule({\n      imports: [\n        ReductionDialogComponent,\n        HttpClientTestingModule,\n        NoopAnimationsModule,\n      ],\n      providers: [\n        {\n          provide: MatDialogRef,\n          useValue: {\n            close: jasmine.createSpy('close'),\n          },\n        },\n        {\n          provide: MAT_DIALOG_DATA,\n          useValue: {},\n        },\n      ],\n    }).compileComponents();\n\n    fixture = TestBed.createComponent(ReductionDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});\n"], "mappings": ";AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AACjE,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,oBAAoB,QAAQ,sCAAsC;AAC3E,SAASC,YAAY,EAAEC,eAAe,QAAQ,0BAA0B;AAExE,SAASC,wBAAwB,QAAQ,8BAA8B;AAEvEC,QAAQ,CAAC,0BAA0B,EAAE,MAAK;EACxC,IAAIC,SAAmC;EACvC,IAAIC,OAAmD;EAEvDC,UAAU,eAAAC,iBAAA,CAAC,aAAW;IACpB,MAAMV,OAAO,CAACW,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CACPP,wBAAwB,EACxBJ,uBAAuB,EACvBC,oBAAoB,CACrB;MACDW,SAAS,EAAE,CACT;QACEC,OAAO,EAAEX,YAAY;QACrBY,QAAQ,EAAE;UACRC,KAAK,EAAEC,OAAO,CAACC,SAAS,CAAC,OAAO;;OAEnC,EACD;QACEJ,OAAO,EAAEV,eAAe;QACxBW,QAAQ,EAAE;OACX;KAEJ,CAAC,CAACI,iBAAiB,EAAE;IAEtBX,OAAO,GAAGR,OAAO,CAACoB,eAAe,CAACf,wBAAwB,CAAC;IAC3DE,SAAS,GAAGC,OAAO,CAACa,iBAAiB;IACrCb,OAAO,CAACc,aAAa,EAAE;EACzB,CAAC,EAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACjB,SAAS,CAAC,CAACkB,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}