import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import { Suspension } from '@contract-management/models/suspension.model';
import { environment } from '@env';
import { SuspensionService } from './suspension.service';
import { AuthService } from '@core/auth/services/auth.service';
import { ContractAuditHistoryService } from './contract-audit-history.service';
import { ContractAuditStatusService } from './contract-audit-status.service';
import { User } from '@core/auth/models/user.model';
import { ContractAuditStatus } from '@contract-management/models/contract-audit-status.model';
import { ContractAuditHistory } from '@contract-management/models/contract-audit-history.model';
import { of as observableOf } from 'rxjs';

describe('SuspensionService', () => {
  let service: SuspensionService;
  let httpMock: HttpTestingController;
  let authServiceSpy: jasmine.SpyObj<AuthService>;
  let contractAuditHistoryServiceSpy: jasmine.SpyObj<ContractAuditHistoryService>;
  let contractAuditStatusServiceSpy: jasmine.SpyObj<ContractAuditStatusService>;
  const apiUrl = `${environment.apiUrl}/suspensions`;

  const mockSuspension: Suspension = {
    id: 1,
    startDate: new Date('2024-02-20'),
    endDate: new Date('2024-03-20'),
    reason: 'Test suspension reason',
    contractId: 1,
    suspensionDays: 29,
  };

  const mockUser: User = { id: 1, username: 'testuser', profiles: [] };

  const mockAuditStatus: ContractAuditStatus = {
    id: 1,
    name: 'Edición de suspensión',
    description: 'Edición de suspensión',
  };

  const mockAuditHistory: ContractAuditHistory = {
    id: 1,
    contractId: 1,
    auditStatusId: 1,
    auditDate: new Date(),
    comment: 'Test comment',
    auditorId: 1,
  };

  beforeEach(() => {
    authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);
    contractAuditHistoryServiceSpy = jasmine.createSpyObj(
      'ContractAuditHistoryService',
      ['create'],
    );
    contractAuditStatusServiceSpy = jasmine.createSpyObj(
      'ContractAuditStatusService',
      ['getByName'],
    );

    authServiceSpy.getCurrentUser.and.returnValue(mockUser);
    contractAuditHistoryServiceSpy.create.and.returnValue(
      observableOf(mockAuditHistory),
    );

    contractAuditStatusServiceSpy.getByName.and.returnValue(
      observableOf(mockAuditStatus),
    );

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        SuspensionService,
        { provide: AuthService, useValue: authServiceSpy },
        {
          provide: ContractAuditHistoryService,
          useValue: contractAuditHistoryServiceSpy,
        },
        {
          provide: ContractAuditStatusService,
          useValue: contractAuditStatusServiceSpy,
        },
      ],
    });
    service = TestBed.inject(SuspensionService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all suspensions', () => {
      const mockSuspensions = [mockSuspension];

      service.getAll().subscribe((suspensions) => {
        expect(suspensions).toEqual(mockSuspensions);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockSuspensions);
    });

    it('should handle error when getting all suspensions', () => {
      service.getAll().subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(500);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.flush('Internal Server Error', {
        status: 500,
        statusText: 'Internal Server Error',
      });
    });
  });

  describe('getById', () => {
    it('should return a suspension by id', () => {
      const id = 1;

      service.getById(id).subscribe((suspension) => {
        expect(suspension).toEqual(mockSuspension);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockSuspension);
    });

    it('should handle error when getting suspension by id', () => {
      const id = 999;

      service.getById(id).subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.flush('Not Found', { status: 404, statusText: 'Not Found' });
    });
  });

  describe('create', () => {
    it('should create a new suspension with audit when user is authenticated', fakeAsync(() => {
      const newSuspension: Omit<Suspension, 'id'> = {
        startDate: new Date('2024-02-20'),
        endDate: new Date('2024-03-20'),
        reason: 'New suspension reason',
        contractId: 1,
        suspensionDays: 29,
      };

      authServiceSpy.getCurrentUser.and.returnValue(mockUser);

      let completed = false;
      service.create(newSuspension).subscribe((suspension) => {
        expect(suspension).toEqual(mockSuspension);
        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();
        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith(
          'Creación de suspensión',
        );
        completed = true;
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newSuspension);
      req.flush(mockSuspension);

      tick(100);

      expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();
      expect(completed).toBeTrue();
    }));

    it('should create a new suspension without audit when user is not authenticated', () => {
      const newSuspension: Omit<Suspension, 'id'> = {
        startDate: new Date('2024-02-20'),
        endDate: new Date('2024-03-20'),
        reason: 'New suspension reason',
        contractId: 1,
        suspensionDays: 29,
      };

      authServiceSpy.getCurrentUser.and.returnValue(null);

      service.create(newSuspension).subscribe((suspension) => {
        expect(suspension).toEqual(mockSuspension);
        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();
        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newSuspension);
      req.flush(mockSuspension);
    });

    it('should handle error when creating a new suspension', () => {
      const newSuspension: Omit<Suspension, 'id'> = {
        startDate: new Date('2024-02-20'),
        endDate: new Date('2024-03-20'),
        reason: 'New suspension reason',
        contractId: 1,
        suspensionDays: 29,
      };

      service.create(newSuspension).subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.flush('Bad Request', { status: 400, statusText: 'Bad Request' });
    });
  });

  describe('update', () => {
    it('should update a suspension with audit when startDate has changed and user is authenticated', fakeAsync(() => {
      const id = 1;
      const updateData: Partial<Suspension> = {
        startDate: new Date('2024-03-01'),
        endDate: new Date('2024-03-20'),
        reason: 'Updated suspension reason',
        contractId: 1,
        suspensionDays: 20,
      };

      authServiceSpy.getCurrentUser.and.returnValue(mockUser);

      let completed = false;
      service.update(id, updateData).subscribe((suspension) => {
        expect(suspension).toEqual(mockSuspension);
        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();
        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith(
          'Edición de suspensión',
        );
        completed = true;
      });

      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(getReq.request.method).toBe('GET');

      getReq.flush({
        ...mockSuspension,
        startDate: new Date('2024-02-20'),
        endDate: new Date('2024-03-20'),
      });

      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(putReq.request.method).toBe('PUT');
      expect(putReq.request.body).toEqual(updateData);
      putReq.flush(mockSuspension);

      tick(100);

      expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();
      expect(completed).toBeTrue();
    }));

    it('should update a suspension with audit when endDate has changed and user is authenticated', fakeAsync(() => {
      const id = 1;
      const updateData: Partial<Suspension> = {
        startDate: new Date('2024-02-20'),
        endDate: new Date('2024-04-20'),
        reason: 'Updated suspension reason',
        contractId: 1,
        suspensionDays: 60,
      };

      authServiceSpy.getCurrentUser.and.returnValue(mockUser);

      let completed = false;
      service.update(id, updateData).subscribe((suspension) => {
        expect(suspension).toEqual(mockSuspension);
        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();
        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith(
          'Edición de suspensión',
        );
        completed = true;
      });

      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(getReq.request.method).toBe('GET');

      getReq.flush({
        ...mockSuspension,
        startDate: new Date('2024-02-20'),
        endDate: new Date('2024-03-20'),
      });

      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(putReq.request.method).toBe('PUT');
      expect(putReq.request.body).toEqual(updateData);
      putReq.flush(mockSuspension);

      tick(100);

      expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();
      expect(completed).toBeTrue();
    }));

    it('should update a suspension with audit when both dates have changed and user is authenticated', fakeAsync(() => {
      const id = 1;
      const updateData: Partial<Suspension> = {
        startDate: new Date('2024-03-01'),
        endDate: new Date('2024-04-20'),
        reason: 'Updated suspension reason',
        contractId: 1,
        suspensionDays: 50,
      };

      authServiceSpy.getCurrentUser.and.returnValue(mockUser);

      let completed = false;
      service.update(id, updateData).subscribe((suspension) => {
        expect(suspension).toEqual(mockSuspension);
        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();
        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith(
          'Edición de suspensión',
        );
        completed = true;
      });

      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(getReq.request.method).toBe('GET');

      getReq.flush({
        ...mockSuspension,
        startDate: new Date('2024-02-20'),
        endDate: new Date('2024-03-20'),
      });

      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(putReq.request.method).toBe('PUT');
      expect(putReq.request.body).toEqual(updateData);
      putReq.flush(mockSuspension);

      tick(100);

      expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();
      expect(completed).toBeTrue();
    }));

    it('should update a suspension without audit when dates have not changed and user is authenticated', fakeAsync(() => {
      const id = 1;

      const originalStartDate = new Date('2024-02-20');
      const originalEndDate = new Date('2024-03-20');

      const updateData: Partial<Suspension> = {
        startDate: originalStartDate,
        endDate: originalEndDate,
        reason: 'Updated suspension reason only',
        contractId: 1,
        suspensionDays: 29,
      };

      authServiceSpy.getCurrentUser.and.returnValue(mockUser);

      service.update(id, updateData).subscribe((suspension) => {
        expect(suspension).toEqual(mockSuspension);
        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();

        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();
      });

      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(getReq.request.method).toBe('GET');

      getReq.flush({
        ...mockSuspension,
        startDate: originalStartDate,
        endDate: originalEndDate,
      });

      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(putReq.request.method).toBe('PUT');
      expect(putReq.request.body).toEqual(updateData);
      putReq.flush(mockSuspension);

      tick();
    }));

    it('should update a suspension without audit when user is not authenticated', () => {
      const id = 1;
      const updateData: Partial<Suspension> = {
        startDate: new Date('2024-03-01'),
        endDate: new Date('2024-04-20'),
        reason: 'Updated suspension reason',
        contractId: 1,
        suspensionDays: 50,
      };

      authServiceSpy.getCurrentUser.and.returnValue(null);

      service.update(id, updateData).subscribe((suspension) => {
        expect(suspension).toEqual(mockSuspension);
        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();
        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();
      });

      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(getReq.request.method).toBe('GET');
      getReq.flush(mockSuspension);

      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(putReq.request.method).toBe('PUT');
      expect(putReq.request.body).toEqual(updateData);
      putReq.flush(mockSuspension);
    });

    it('should handle error when getting suspension for update', () => {
      const id = 999;
      const updateData: Partial<Suspension> = {
        reason: 'Updated suspension reason',
      };

      service.update(id, updateData).subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.flush('Not Found', { status: 404, statusText: 'Not Found' });
    });

    it('should handle error when updating a suspension', () => {
      const id = 1;
      const updateData: Partial<Suspension> = {
        reason: 'Updated suspension reason',
      };

      service.update(id, updateData).subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(getReq.request.method).toBe('GET');
      getReq.flush(mockSuspension);

      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);
      putReq.flush('Bad Request', { status: 400, statusText: 'Bad Request' });
    });
  });

  describe('delete', () => {
    it('should delete a suspension', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });

    it('should handle error when deleting a suspension', () => {
      const id = 999;

      service.delete(id).subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.flush('Not Found', { status: 404, statusText: 'Not Found' });
    });
  });

  describe('getAllByContractId', () => {
    it('should return all suspensions by contract id', () => {
      const contractId = 1;
      const mockSuspensions = [mockSuspension];

      service.getAllByContractId(contractId).subscribe((suspensions) => {
        expect(suspensions).toEqual(mockSuspensions);
      });

      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockSuspensions);
    });

    it('should handle error when getting suspensions by contract id', () => {
      const contractId = 999;

      service.getAllByContractId(contractId).subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}`);
      req.flush('Not Found', { status: 404, statusText: 'Not Found' });
    });
  });
});