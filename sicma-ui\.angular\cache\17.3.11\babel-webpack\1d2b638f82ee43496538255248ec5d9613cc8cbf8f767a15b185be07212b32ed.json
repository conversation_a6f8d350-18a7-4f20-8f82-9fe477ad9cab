{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/Github/SICMA/sicma-ui/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { MatDialogRef } from '@angular/material/dialog';\nimport { ReductionDialogComponent } from './reduction-dialog.component';\ndescribe('ReductionDialogComponent', () => {\n  let component;\n  let fixture;\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    yield TestBed.configureTestingModule({\n      imports: [ReductionDialogComponent, HttpClientTestingModule],\n      providers: [{\n        provide: MatDialogRef,\n        useValue: {\n          close: jasmine.createSpy('close')\n        }\n      }]\n    }).compileComponents();\n    fixture = TestBed.createComponent(ReductionDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  }));\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "HttpClientTestingModule", "MatDialogRef", "ReductionDialogComponent", "describe", "component", "fixture", "beforeEach", "_asyncToGenerator", "configureTestingModule", "imports", "providers", "provide", "useValue", "close", "jasmine", "createSpy", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\components\\reductions-list\\reduction-dialog\\reduction-dialog.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { MatDialogRef } from '@angular/material/dialog';\n\nimport { ReductionDialogComponent } from './reduction-dialog.component';\n\ndescribe('ReductionDialogComponent', () => {\n  let component: ReductionDialogComponent;\n  let fixture: ComponentFixture<ReductionDialogComponent>;\n\n  beforeEach(async () => {\n    await TestBed.configureTestingModule({\n      imports: [ReductionDialogComponent, HttpClientTestingModule],\n      providers: [\n        {\n          provide: MatDialogRef,\n          useValue: {\n            close: jasmine.createSpy('close'),\n          },\n        },\n      ],\n    }).compileComponents();\n\n    fixture = TestBed.createComponent(ReductionDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});"], "mappings": ";AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AACjE,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,YAAY,QAAQ,0BAA0B;AAEvD,SAASC,wBAAwB,QAAQ,8BAA8B;AAEvEC,QAAQ,CAAC,0BAA0B,EAAE,MAAK;EACxC,IAAIC,SAAmC;EACvC,IAAIC,OAAmD;EAEvDC,UAAU,eAAAC,iBAAA,CAAC,aAAW;IACpB,MAAMR,OAAO,CAACS,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CAACP,wBAAwB,EAAEF,uBAAuB,CAAC;MAC5DU,SAAS,EAAE,CACT;QACEC,OAAO,EAAEV,YAAY;QACrBW,QAAQ,EAAE;UACRC,KAAK,EAAEC,OAAO,CAACC,SAAS,CAAC,OAAO;;OAEnC;KAEJ,CAAC,CAACC,iBAAiB,EAAE;IAEtBX,OAAO,GAAGN,OAAO,CAACkB,eAAe,CAACf,wBAAwB,CAAC;IAC3DE,SAAS,GAAGC,OAAO,CAACa,iBAAiB;IACrCb,OAAO,CAACc,aAAa,EAAE;EACzB,CAAC,EAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACjB,SAAS,CAAC,CAACkB,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}