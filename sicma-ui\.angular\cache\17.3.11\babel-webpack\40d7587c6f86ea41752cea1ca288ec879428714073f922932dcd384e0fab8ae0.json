{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { ProfessionService } from './profession.service';\ndescribe('ProfessionService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/professions`;\n  const mockProfession = {\n    id: 1,\n    name: 'Test Profession'\n  };\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [ProfessionService]\n    });\n    service = TestBed.inject(ProfessionService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all professions', () => {\n      const mockProfessions = [mockProfession];\n      service.getAll().subscribe(professions => {\n        expect(professions).toEqual(mockProfessions);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockProfessions);\n    });\n  });\n  describe('getById', () => {\n    it('should return a profession by id', () => {\n      const id = 1;\n      service.getById(id).subscribe(profession => {\n        expect(profession).toEqual(mockProfession);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockProfession);\n    });\n  });\n  describe('create', () => {\n    it('should create a new profession', () => {\n      const newProfession = {\n        name: 'New Profession'\n      };\n      service.create(newProfession).subscribe(profession => {\n        expect(profession).toEqual(mockProfession);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newProfession);\n      req.flush(mockProfession);\n    });\n  });\n  describe('update', () => {\n    it('should update a profession', () => {\n      const id = 1;\n      const updateData = {\n        name: 'Updated Profession'\n      };\n      service.update(id, updateData).subscribe(profession => {\n        expect(profession).toEqual(mockProfession);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockProfession);\n    });\n  });\n  describe('delete', () => {\n    it('should delete a profession', () => {\n      const id = 1;\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n  describe('getByName', () => {\n    it('should return a profession by name', () => {\n      const name = 'Test Profession';\n      service.getByName(name).subscribe(profession => {\n        expect(profession).toEqual(mockProfession);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockProfession);\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "ProfessionService", "describe", "service", "httpMock", "apiUrl", "mockProfession", "id", "name", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockProfessions", "getAll", "subscribe", "professions", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "getById", "profession", "newProfession", "create", "body", "updateData", "update", "delete", "nothing", "getByName"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-management\\services\\profession.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { Profession } from '@contractor-management/models/profession.model';\nimport { environment } from '@env';\nimport { ProfessionService } from './profession.service';\n\ndescribe('ProfessionService', () => {\n  let service: ProfessionService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/professions`;\n\n  const mockProfession: Profession = {\n    id: 1,\n    name: 'Test Profession',\n  };\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [ProfessionService],\n    });\n    service = TestBed.inject(ProfessionService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all professions', () => {\n      const mockProfessions = [mockProfession];\n\n      service.getAll().subscribe((professions) => {\n        expect(professions).toEqual(mockProfessions);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockProfessions);\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a profession by id', () => {\n      const id = 1;\n\n      service.getById(id).subscribe((profession) => {\n        expect(profession).toEqual(mockProfession);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockProfession);\n    });\n  });\n\n  describe('create', () => {\n    it('should create a new profession', () => {\n      const newProfession: Omit<Profession, 'id'> = {\n        name: 'New Profession',\n      };\n\n      service.create(newProfession).subscribe((profession) => {\n        expect(profession).toEqual(mockProfession);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newProfession);\n      req.flush(mockProfession);\n    });\n  });\n\n  describe('update', () => {\n    it('should update a profession', () => {\n      const id = 1;\n      const updateData: Partial<Profession> = {\n        name: 'Updated Profession',\n      };\n\n      service.update(id, updateData).subscribe((profession) => {\n        expect(profession).toEqual(mockProfession);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockProfession);\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete a profession', () => {\n      const id = 1;\n\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n\n  describe('getByName', () => {\n    it('should return a profession by name', () => {\n      const name = 'Test Profession';\n\n      service.getByName(name).subscribe((profession) => {\n        expect(profession).toEqual(mockProfession);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockProfession);\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,iBAAiB,QAAQ,sBAAsB;AAExDC,QAAQ,CAAC,mBAAmB,EAAE,MAAK;EACjC,IAAIC,OAA0B;EAC9B,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,cAAc;EAElD,MAAMC,cAAc,GAAe;IACjCC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE;GACP;EAEDC,UAAU,CAAC,MAAK;IACdV,OAAO,CAACW,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACd,uBAAuB,CAAC;MAClCe,SAAS,EAAE,CAACX,iBAAiB;KAC9B,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACc,MAAM,CAACZ,iBAAiB,CAAC;IAC3CG,QAAQ,GAAGL,OAAO,CAACc,MAAM,CAACf,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFgB,SAAS,CAAC,MAAK;IACbV,QAAQ,CAACW,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACd,OAAO,CAAC,CAACe,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFhB,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,+BAA+B,EAAE,MAAK;MACvC,MAAMG,eAAe,GAAG,CAACb,cAAc,CAAC;MAExCH,OAAO,CAACiB,MAAM,EAAE,CAACC,SAAS,CAAEC,WAAW,IAAI;QACzCL,MAAM,CAACK,WAAW,CAAC,CAACC,OAAO,CAACJ,eAAe,CAAC;MAC9C,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,eAAe,CAAC;IAC5B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjB,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBc,EAAE,CAAC,kCAAkC,EAAE,MAAK;MAC1C,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAAC2B,OAAO,CAACvB,EAAE,CAAC,CAACc,SAAS,CAAEU,UAAU,IAAI;QAC3Cd,MAAM,CAACc,UAAU,CAAC,CAACR,OAAO,CAACjB,cAAc,CAAC;MAC5C,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvB,cAAc,CAAC;IAC3B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,gCAAgC,EAAE,MAAK;MACxC,MAAMgB,aAAa,GAA2B;QAC5CxB,IAAI,EAAE;OACP;MAEDL,OAAO,CAAC8B,MAAM,CAACD,aAAa,CAAC,CAACX,SAAS,CAAEU,UAAU,IAAI;QACrDd,MAAM,CAACc,UAAU,CAAC,CAACR,OAAO,CAACjB,cAAc,CAAC;MAC5C,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACS,aAAa,CAAC;MAC/CR,GAAG,CAACK,KAAK,CAACvB,cAAc,CAAC;IAC3B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,4BAA4B,EAAE,MAAK;MACpC,MAAMT,EAAE,GAAG,CAAC;MACZ,MAAM4B,UAAU,GAAwB;QACtC3B,IAAI,EAAE;OACP;MAEDL,OAAO,CAACiC,MAAM,CAAC7B,EAAE,EAAE4B,UAAU,CAAC,CAACd,SAAS,CAAEU,UAAU,IAAI;QACtDd,MAAM,CAACc,UAAU,CAAC,CAACR,OAAO,CAACjB,cAAc,CAAC;MAC5C,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACY,UAAU,CAAC;MAC5CX,GAAG,CAACK,KAAK,CAACvB,cAAc,CAAC;IAC3B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,4BAA4B,EAAE,MAAK;MACpC,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAACkC,MAAM,CAAC9B,EAAE,CAAC,CAACc,SAAS,CAAC,MAAK;QAChCJ,MAAM,EAAE,CAACqB,OAAO,EAAE;MACpB,CAAC,CAAC;MAEF,MAAMd,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF3B,QAAQ,CAAC,WAAW,EAAE,MAAK;IACzBc,EAAE,CAAC,oCAAoC,EAAE,MAAK;MAC5C,MAAMR,IAAI,GAAG,iBAAiB;MAE9BL,OAAO,CAACoC,SAAS,CAAC/B,IAAI,CAAC,CAACa,SAAS,CAAEU,UAAU,IAAI;QAC/Cd,MAAM,CAACc,UAAU,CAAC,CAACR,OAAO,CAACjB,cAAc,CAAC;MAC5C,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,SAASG,IAAI,EAAE,CAAC;MACxDS,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvB,cAAc,CAAC;IAC3B,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}