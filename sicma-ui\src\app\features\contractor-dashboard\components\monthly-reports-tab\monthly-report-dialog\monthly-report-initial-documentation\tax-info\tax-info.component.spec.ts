import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { InitialReportDocumentation } from '@contractor-dashboard/models/initial-report-documentation.model';
import { TaxRegimeService } from '@contractor-dashboard/services/tax-regime.service';
import { AlertService } from '@shared/services/alert.service';
import { of, throwError } from 'rxjs';
import { TaxInfoComponent } from './tax-info.component';

describe('TaxInfoComponent', () => {
  let component: TaxInfoComponent;
  let fixture: ComponentFixture<TaxInfoComponent>;
  let alertService: jasmine.SpyObj<AlertService>;
  let taxRegimeService: jasmine.SpyObj<TaxRegimeService>;

  beforeEach(async () => {
    const taxRegimeServiceSpy = jasmine.createSpyObj('TaxRegimeService', [
      'getAll',
    ]);
    const alertServiceSpy = jasmine.createSpyObj('AlertService', ['error']);

    await TestBed.configureTestingModule({
      imports: [TaxInfoComponent, NoopAnimationsModule],
      providers: [
        { provide: TaxRegimeService, useValue: taxRegimeServiceSpy },
        { provide: AlertService, useValue: alertServiceSpy },
      ],
    }).compileComponents();

    taxRegimeService = TestBed.inject(
      TaxRegimeService,
    ) as jasmine.SpyObj<TaxRegimeService>;
    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;

    taxRegimeService.getAll.and.returnValue(of([]));
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TaxInfoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Initialization', () => {
    it('should initialize form with empty values', () => {
      expect(component.form.get('regimeType')?.value).toBe('');
      expect(component.form.get('regimeSupportFile')?.value).toBeNull();
      expect(component.taxFormFileName).toBeNull();
    });

    it('should handle error when loading tax regimes', () => {
      taxRegimeService.getAll.and.returnValue(throwError(() => new Error()));
      component.loadTaxRegimes();
      expect(alertService.error).toHaveBeenCalledWith(
        'Error al cargar los regímenes tributarios',
      );
    });
  });

  describe('Form Validation', () => {
    it('should validate required fields', () => {
      expect(component.form.get('regimeType')?.hasError('required')).toBeTrue();
    });

    it('should be valid when all required fields are filled', () => {
      const form = component.form;
      form.patchValue({
        regimeType: 1,
      });
      expect(form.valid).toBeTruthy();
    });
  });

  describe('File Handling', () => {
    it('should accept PDF file and set filename', () => {
      const file = new File([''], 'test.pdf', { type: 'application/pdf' });
      const event = { target: { files: [file] } } as unknown as Event;

      component.onFileSelected(event, 'regimeSupportFile');
      expect(component.taxFormFileName).toBe('test.pdf');
      expect(alertService.error).not.toHaveBeenCalled();
    });

    it('should reject non-PDF file', () => {
      const file = new File([''], 'test.txt', { type: 'text/plain' });
      const event = { target: { files: [file] } } as unknown as Event;

      component.onFileSelected(event, 'regimeSupportFile');
      expect(component.taxFormFileName).toBeNull();
      expect(alertService.error).toHaveBeenCalledWith(
        'Solo se permiten archivos PDF',
      );
    });

    it('should reject file larger than 1MB', () => {
      const largeFile = new File(
        [''.padStart(1024 * 1024 + 1, 'x')],
        'large.pdf',
        { type: 'application/pdf' },
      );
      const event = { target: { files: [largeFile] } } as unknown as Event;

      component.onFileSelected(event, 'regimeSupportFile');
      expect(component.taxFormFileName).toBeNull();
      expect(alertService.error).toHaveBeenCalledWith(
        'El archivo no debe superar 1MB',
      );
    });
  });

  describe('Initial Data Loading', () => {
    it('should load initial data correctly', () => {
      const initialData: InitialReportDocumentation = {
        id: 1,
        contractorContractId: 1,
        bankId: 1,
        bankAccountTypeId: 1,
        accountNumber: '*********',
        bankCertificateFileUrl: 'test.pdf',
        taxRegimeId: 1,
        taxFormFileUrl: 'test.pdf',
        epsId: 1,
        arlId: 1,
        pensionFundId: 1,
        hasDependents: false,
        hasHousingInterest: false,
        housingInterestAnnualPayment: 0,
        hasPrepaidMedicine: false,
        prepaidMedicineAnnualPayment: 0,
        hasAfcAccount: false,
        hasVoluntarySavings: false,
        afcAccountAnnualPayment: 0,
        voluntarySavingsAnnualPayment: 0,
      };

      component.initialData = initialData;
      component.ngOnChanges({
        initialData: {
          currentValue: initialData,
          previousValue: undefined,
          firstChange: true,
          isFirstChange: () => true,
        },
      });

      expect(component.form.get('regimeType')?.value).toBe(1);
    });

    it('should handle undefined initial data', () => {
      component.ngOnChanges({
        initialData: {
          currentValue: undefined,
          previousValue: undefined,
          firstChange: true,
          isFirstChange: () => true,
        },
      });

      expect(component.form.get('regimeType')?.value).toBe('');
      expect(component.taxFormFileName).toBeNull();
    });

    it('should handle file validation when file url exists', () => {
      component.initialData = {
        id: 1,
        contractorContractId: 1,
        taxRegimeId: 1,
        taxFormFileUrl: 'http://example.com/file.pdf',
        epsId: 1,
        arlId: 1,
        pensionFundId: 1,
        bankId: 1,
        bankAccountTypeId: 1,
        accountNumber: '',
        hasDependents: false,
        hasHousingInterest: false,
        hasPrepaidMedicine: false,
        hasAfcAccount: false,
        hasVoluntarySavings: false,
      };

      component.form.patchValue({
        regimeType: 1,
      });

      expect(component.isValid).toBeTrue();
    });

    it('should handle file validation when no file url exists', () => {
      component.initialData = {
        id: 1,
        contractorContractId: 1,
        taxRegimeId: 1,
        taxFormFileUrl: undefined,
        epsId: 1,
        arlId: 1,
        pensionFundId: 1,
        bankId: 1,
        bankAccountTypeId: 1,
        accountNumber: '',
        hasDependents: false,
        hasHousingInterest: false,
        hasPrepaidMedicine: false,
        hasAfcAccount: false,
        hasVoluntarySavings: false,
      };

      component.form.patchValue({
        regimeType: 1,
      });

      expect(component.isValid).toBeFalse();

      const mockFile = new File([''], 'test.pdf', { type: 'application/pdf' });
      component.form.patchValue({
        regimeSupportFile: mockFile,
      });

      expect(component.isValid).toBeTrue();
    });
  });

  describe('Supervisor Mode', () => {
    it('should show readonly inputs in supervisor mode', () => {
      component.isSupervisor = true;
      fixture.detectChanges();
      expect(component.isSupervisor).toBeTrue();
    });
  });

  describe('File Download', () => {
    it('should open file in new tab when downloading', () => {
      component.initialData = {
        id: 1,
        contractorContractId: 1,
        taxRegimeId: 1,
        taxFormFileUrl: 'http://example.com/file.pdf',
        epsId: 1,
        arlId: 1,
        pensionFundId: 1,
        bankId: 1,
        bankAccountTypeId: 1,
        accountNumber: '',
        hasDependents: false,
        hasHousingInterest: false,
        hasPrepaidMedicine: false,
        hasAfcAccount: false,
        hasVoluntarySavings: false,
      };

      const windowSpy = spyOn(window, 'open');
      component.downloadFile();

      expect(windowSpy).toHaveBeenCalledWith(
        'http://example.com/file.pdf',
        '_blank',
      );
    });

    it('should not attempt to open when no file url exists', () => {
      component.initialData = {
        id: 1,
        contractorContractId: 1,
        taxRegimeId: 1,
        taxFormFileUrl: undefined,
        epsId: 1,
        arlId: 1,
        pensionFundId: 1,
        bankId: 1,
        bankAccountTypeId: 1,
        accountNumber: '',
        hasDependents: false,
        hasHousingInterest: false,
        hasPrepaidMedicine: false,
        hasAfcAccount: false,
        hasVoluntarySavings: false,
      };

      const windowSpy = spyOn(window, 'open');
      component.downloadFile();

      expect(windowSpy).not.toHaveBeenCalled();
    });
  });
});
