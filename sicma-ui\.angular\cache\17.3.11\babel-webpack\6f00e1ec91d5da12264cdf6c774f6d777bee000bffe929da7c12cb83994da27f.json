{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { CausesSelectionService } from './causes-selection.service';\ndescribe('CausesSelectionService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/causes_selection`;\n  const mockCausesSelection = {\n    id: 1,\n    name: 'Test Causes Selection'\n  };\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [CausesSelectionService]\n    });\n    service = TestBed.inject(CausesSelectionService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all causes selections', () => {\n      const mockCausesSelections = [mockCausesSelection];\n      service.getAll().subscribe(causesSelections => {\n        expect(causesSelections).toEqual(mockCausesSelections);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockCausesSelections);\n    });\n  });\n  describe('getById', () => {\n    it('should return a causes selection by id', () => {\n      const id = 1;\n      service.getById(id).subscribe(causesSelection => {\n        expect(causesSelection).toEqual(mockCausesSelection);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockCausesSelection);\n    });\n  });\n  describe('create', () => {\n    it('should create a new causes selection', () => {\n      const newCausesSelection = {\n        name: 'New Causes Selection'\n      };\n      service.create(newCausesSelection).subscribe(causesSelection => {\n        expect(causesSelection).toEqual(mockCausesSelection);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newCausesSelection);\n      req.flush(mockCausesSelection);\n    });\n  });\n  describe('update', () => {\n    it('should update a causes selection', () => {\n      const id = 1;\n      const updateData = {\n        name: 'Updated Causes Selection'\n      };\n      service.update(id, updateData).subscribe(causesSelection => {\n        expect(causesSelection).toEqual(mockCausesSelection);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockCausesSelection);\n    });\n  });\n  describe('delete', () => {\n    it('should delete a causes selection', () => {\n      const id = 1;\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n  describe('getByName', () => {\n    it('should return a causes selection by name', () => {\n      const name = 'Test Causes Selection';\n      service.getByName(name).subscribe(causesSelection => {\n        expect(causesSelection).toEqual(mockCausesSelection);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockCausesSelection);\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "CausesSelectionService", "describe", "service", "httpMock", "apiUrl", "mockCausesSelection", "id", "name", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockCausesSelections", "getAll", "subscribe", "causesSelections", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "getById", "causesSelection", "newCausesSelection", "create", "body", "updateData", "update", "delete", "nothing", "getByName"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\services\\causes-selection.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { CausesSelection } from '@contract-management/models/causes-seletion.model';\nimport { environment } from '@env';\nimport { CausesSelectionService } from './causes-selection.service';\n\ndescribe('CausesSelectionService', () => {\n  let service: CausesSelectionService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/causes_selection`;\n\n  const mockCausesSelection: CausesSelection = {\n    id: 1,\n    name: 'Test Causes Selection',\n  };\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [CausesSelectionService],\n    });\n    service = TestBed.inject(CausesSelectionService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all causes selections', () => {\n      const mockCausesSelections = [mockCausesSelection];\n\n      service.getAll().subscribe((causesSelections) => {\n        expect(causesSelections).toEqual(mockCausesSelections);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockCausesSelections);\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a causes selection by id', () => {\n      const id = 1;\n\n      service.getById(id).subscribe((causesSelection) => {\n        expect(causesSelection).toEqual(mockCausesSelection);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockCausesSelection);\n    });\n  });\n\n  describe('create', () => {\n    it('should create a new causes selection', () => {\n      const newCausesSelection: Omit<CausesSelection, 'id'> = {\n        name: 'New Causes Selection',\n      };\n\n      service.create(newCausesSelection).subscribe((causesSelection) => {\n        expect(causesSelection).toEqual(mockCausesSelection);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newCausesSelection);\n      req.flush(mockCausesSelection);\n    });\n  });\n\n  describe('update', () => {\n    it('should update a causes selection', () => {\n      const id = 1;\n      const updateData: Partial<CausesSelection> = {\n        name: 'Updated Causes Selection',\n      };\n\n      service.update(id, updateData).subscribe((causesSelection) => {\n        expect(causesSelection).toEqual(mockCausesSelection);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockCausesSelection);\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete a causes selection', () => {\n      const id = 1;\n\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n\n  describe('getByName', () => {\n    it('should return a causes selection by name', () => {\n      const name = 'Test Causes Selection';\n\n      service.getByName(name).subscribe((causesSelection) => {\n        expect(causesSelection).toEqual(mockCausesSelection);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockCausesSelection);\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,sBAAsB,QAAQ,4BAA4B;AAEnEC,QAAQ,CAAC,wBAAwB,EAAE,MAAK;EACtC,IAAIC,OAA+B;EACnC,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,mBAAmB;EAEvD,MAAMC,mBAAmB,GAAoB;IAC3CC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE;GACP;EAEDC,UAAU,CAAC,MAAK;IACdV,OAAO,CAACW,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACd,uBAAuB,CAAC;MAClCe,SAAS,EAAE,CAACX,sBAAsB;KACnC,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACc,MAAM,CAACZ,sBAAsB,CAAC;IAChDG,QAAQ,GAAGL,OAAO,CAACc,MAAM,CAACf,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFgB,SAAS,CAAC,MAAK;IACbV,QAAQ,CAACW,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACd,OAAO,CAAC,CAACe,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFhB,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,qCAAqC,EAAE,MAAK;MAC7C,MAAMG,oBAAoB,GAAG,CAACb,mBAAmB,CAAC;MAElDH,OAAO,CAACiB,MAAM,EAAE,CAACC,SAAS,CAAEC,gBAAgB,IAAI;QAC9CL,MAAM,CAACK,gBAAgB,CAAC,CAACC,OAAO,CAACJ,oBAAoB,CAAC;MACxD,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,oBAAoB,CAAC;IACjC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjB,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBc,EAAE,CAAC,wCAAwC,EAAE,MAAK;MAChD,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAAC2B,OAAO,CAACvB,EAAE,CAAC,CAACc,SAAS,CAAEU,eAAe,IAAI;QAChDd,MAAM,CAACc,eAAe,CAAC,CAACR,OAAO,CAACjB,mBAAmB,CAAC;MACtD,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvB,mBAAmB,CAAC;IAChC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,sCAAsC,EAAE,MAAK;MAC9C,MAAMgB,kBAAkB,GAAgC;QACtDxB,IAAI,EAAE;OACP;MAEDL,OAAO,CAAC8B,MAAM,CAACD,kBAAkB,CAAC,CAACX,SAAS,CAAEU,eAAe,IAAI;QAC/Dd,MAAM,CAACc,eAAe,CAAC,CAACR,OAAO,CAACjB,mBAAmB,CAAC;MACtD,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACS,kBAAkB,CAAC;MACpDR,GAAG,CAACK,KAAK,CAACvB,mBAAmB,CAAC;IAChC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,kCAAkC,EAAE,MAAK;MAC1C,MAAMT,EAAE,GAAG,CAAC;MACZ,MAAM4B,UAAU,GAA6B;QAC3C3B,IAAI,EAAE;OACP;MAEDL,OAAO,CAACiC,MAAM,CAAC7B,EAAE,EAAE4B,UAAU,CAAC,CAACd,SAAS,CAAEU,eAAe,IAAI;QAC3Dd,MAAM,CAACc,eAAe,CAAC,CAACR,OAAO,CAACjB,mBAAmB,CAAC;MACtD,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACY,UAAU,CAAC;MAC5CX,GAAG,CAACK,KAAK,CAACvB,mBAAmB,CAAC;IAChC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,kCAAkC,EAAE,MAAK;MAC1C,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAACkC,MAAM,CAAC9B,EAAE,CAAC,CAACc,SAAS,CAAC,MAAK;QAChCJ,MAAM,EAAE,CAACqB,OAAO,EAAE;MACpB,CAAC,CAAC;MAEF,MAAMd,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF3B,QAAQ,CAAC,WAAW,EAAE,MAAK;IACzBc,EAAE,CAAC,0CAA0C,EAAE,MAAK;MAClD,MAAMR,IAAI,GAAG,uBAAuB;MAEpCL,OAAO,CAACoC,SAAS,CAAC/B,IAAI,CAAC,CAACa,SAAS,CAAEU,eAAe,IAAI;QACpDd,MAAM,CAACc,eAAe,CAAC,CAACR,OAAO,CAACjB,mBAAmB,CAAC;MACtD,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,SAASG,IAAI,EAAE,CAAC;MACxDS,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvB,mBAAmB,CAAC;IAChC,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}