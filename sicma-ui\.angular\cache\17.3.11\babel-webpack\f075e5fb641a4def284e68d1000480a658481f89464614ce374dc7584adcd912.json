{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { ArlAffiliationClassService } from './arl-affiliation-class.service';\ndescribe('ArlAffiliationClassService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/arl-affiliation-classes`;\n  const mockArlAffiliationClass = {\n    id: 1,\n    name: 'Class I'\n  };\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [ArlAffiliationClassService]\n    });\n    service = TestBed.inject(ArlAffiliationClassService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all ARL affiliation classes', () => {\n      const mockClasses = [mockArlAffiliationClass];\n      service.getAll().subscribe(classes => {\n        expect(classes).toEqual(mockClasses);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockClasses);\n    });\n    it('should handle error when getting all classes', () => {\n      service.getAll().subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n  describe('getById', () => {\n    it('should return a single ARL affiliation class by id', () => {\n      const id = 1;\n      service.getById(id).subscribe(affiliationClass => {\n        expect(affiliationClass).toEqual(mockArlAffiliationClass);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockArlAffiliationClass);\n    });\n    it('should handle error when getting class by id', () => {\n      const id = 999;\n      service.getById(id).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n  describe('create', () => {\n    it('should create a new ARL affiliation class', () => {\n      const newClass = {\n        name: 'New Class'\n      };\n      service.create(newClass).subscribe(affiliationClass => {\n        expect(affiliationClass).toEqual(mockArlAffiliationClass);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newClass);\n      req.flush(mockArlAffiliationClass);\n    });\n    it('should handle error when creating class', () => {\n      const newClass = {\n        name: 'New Class'\n      };\n      service.create(newClass).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n  describe('update', () => {\n    it('should update an existing ARL affiliation class', () => {\n      const id = 1;\n      const updateData = {\n        name: 'Updated Class'\n      };\n      service.update(id, updateData).subscribe(affiliationClass => {\n        expect(affiliationClass).toEqual(mockArlAffiliationClass);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockArlAffiliationClass);\n    });\n    it('should handle error when updating class', () => {\n      const id = 1;\n      const updateData = {\n        name: 'Updated Class'\n      };\n      service.update(id, updateData).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n  describe('delete', () => {\n    it('should delete an ARL affiliation class', () => {\n      const id = 1;\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n    it('should handle error when deleting class', () => {\n      const id = 1;\n      service.delete(id).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "ArlAffiliationClassService", "describe", "service", "httpMock", "apiUrl", "mockArlAffiliationClass", "id", "name", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockClasses", "getAll", "subscribe", "classes", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "error", "status", "ErrorEvent", "getById", "affiliationClass", "newClass", "create", "body", "updateData", "update", "delete", "nothing"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\services\\arl-affiliation-class.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { ARLAffiliationClass } from '@contractor-dashboard/models/arl-affiliation-class.model';\nimport { environment } from '@env';\nimport { ArlAffiliationClassService } from './arl-affiliation-class.service';\n\ndescribe('ArlAffiliationClassService', () => {\n  let service: ArlAffiliationClassService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/arl-affiliation-classes`;\n\n  const mockArlAffiliationClass: ARLAffiliationClass = {\n    id: 1,\n    name: 'Class I',\n  };\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [ArlAffiliationClassService],\n    });\n    service = TestBed.inject(ArlAffiliationClassService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all ARL affiliation classes', () => {\n      const mockClasses = [mockArlAffiliationClass];\n\n      service.getAll().subscribe((classes) => {\n        expect(classes).toEqual(mockClasses);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockClasses);\n    });\n\n    it('should handle error when getting all classes', () => {\n      service.getAll().subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a single ARL affiliation class by id', () => {\n      const id = 1;\n\n      service.getById(id).subscribe((affiliationClass) => {\n        expect(affiliationClass).toEqual(mockArlAffiliationClass);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockArlAffiliationClass);\n    });\n\n    it('should handle error when getting class by id', () => {\n      const id = 999;\n\n      service.getById(id).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n\n  describe('create', () => {\n    it('should create a new ARL affiliation class', () => {\n      const newClass: Omit<ARLAffiliationClass, 'id'> = {\n        name: 'New Class',\n      };\n\n      service.create(newClass).subscribe((affiliationClass) => {\n        expect(affiliationClass).toEqual(mockArlAffiliationClass);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newClass);\n      req.flush(mockArlAffiliationClass);\n    });\n\n    it('should handle error when creating class', () => {\n      const newClass: Omit<ARLAffiliationClass, 'id'> = {\n        name: 'New Class',\n      };\n\n      service.create(newClass).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n\n  describe('update', () => {\n    it('should update an existing ARL affiliation class', () => {\n      const id = 1;\n      const updateData: Partial<ARLAffiliationClass> = {\n        name: 'Updated Class',\n      };\n\n      service.update(id, updateData).subscribe((affiliationClass) => {\n        expect(affiliationClass).toEqual(mockArlAffiliationClass);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockArlAffiliationClass);\n    });\n\n    it('should handle error when updating class', () => {\n      const id = 1;\n      const updateData: Partial<ARLAffiliationClass> = {\n        name: 'Updated Class',\n      };\n\n      service.update(id, updateData).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete an ARL affiliation class', () => {\n      const id = 1;\n\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n\n    it('should handle error when deleting class', () => {\n      const id = 1;\n\n      service.delete(id).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,0BAA0B,QAAQ,iCAAiC;AAE5EC,QAAQ,CAAC,4BAA4B,EAAE,MAAK;EAC1C,IAAIC,OAAmC;EACvC,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,0BAA0B;EAE9D,MAAMC,uBAAuB,GAAwB;IACnDC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE;GACP;EAEDC,UAAU,CAAC,MAAK;IACdV,OAAO,CAACW,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACd,uBAAuB,CAAC;MAClCe,SAAS,EAAE,CAACX,0BAA0B;KACvC,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACc,MAAM,CAACZ,0BAA0B,CAAC;IACpDG,QAAQ,GAAGL,OAAO,CAACc,MAAM,CAACf,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFgB,SAAS,CAAC,MAAK;IACbV,QAAQ,CAACW,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACd,OAAO,CAAC,CAACe,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFhB,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,2CAA2C,EAAE,MAAK;MACnD,MAAMG,WAAW,GAAG,CAACb,uBAAuB,CAAC;MAE7CH,OAAO,CAACiB,MAAM,EAAE,CAACC,SAAS,CAAEC,OAAO,IAAI;QACrCL,MAAM,CAACK,OAAO,CAAC,CAACC,OAAO,CAACJ,WAAW,CAAC;MACtC,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,WAAW,CAAC;IACxB,CAAC,CAAC;IAEFH,EAAE,CAAC,8CAA8C,EAAE,MAAK;MACtDb,OAAO,CAACiB,MAAM,EAAE,CAACC,SAAS,CAAC;QACzBS,KAAK,EAAGA,KAAK,IAAI;UACfb,MAAM,CAACa,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBc,EAAE,CAAC,oDAAoD,EAAE,MAAK;MAC5D,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAAC8B,OAAO,CAAC1B,EAAE,CAAC,CAACc,SAAS,CAAEa,gBAAgB,IAAI;QACjDjB,MAAM,CAACiB,gBAAgB,CAAC,CAACX,OAAO,CAACjB,uBAAuB,CAAC;MAC3D,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvB,uBAAuB,CAAC;IACpC,CAAC,CAAC;IAEFU,EAAE,CAAC,8CAA8C,EAAE,MAAK;MACtD,MAAMT,EAAE,GAAG,GAAG;MAEdJ,OAAO,CAAC8B,OAAO,CAAC1B,EAAE,CAAC,CAACc,SAAS,CAAC;QAC5BS,KAAK,EAAGA,KAAK,IAAI;UACfb,MAAM,CAACa,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDiB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,2CAA2C,EAAE,MAAK;MACnD,MAAMmB,QAAQ,GAAoC;QAChD3B,IAAI,EAAE;OACP;MAEDL,OAAO,CAACiC,MAAM,CAACD,QAAQ,CAAC,CAACd,SAAS,CAAEa,gBAAgB,IAAI;QACtDjB,MAAM,CAACiB,gBAAgB,CAAC,CAACX,OAAO,CAACjB,uBAAuB,CAAC;MAC3D,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACW,IAAI,CAAC,CAACd,OAAO,CAACY,QAAQ,CAAC;MAC1CX,GAAG,CAACK,KAAK,CAACvB,uBAAuB,CAAC;IACpC,CAAC,CAAC;IAEFU,EAAE,CAAC,yCAAyC,EAAE,MAAK;MACjD,MAAMmB,QAAQ,GAAoC;QAChD3B,IAAI,EAAE;OACP;MAEDL,OAAO,CAACiC,MAAM,CAACD,QAAQ,CAAC,CAACd,SAAS,CAAC;QACjCS,KAAK,EAAGA,KAAK,IAAI;UACfb,MAAM,CAACa,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,iDAAiD,EAAE,MAAK;MACzD,MAAMT,EAAE,GAAG,CAAC;MACZ,MAAM+B,UAAU,GAAiC;QAC/C9B,IAAI,EAAE;OACP;MAEDL,OAAO,CAACoC,MAAM,CAAChC,EAAE,EAAE+B,UAAU,CAAC,CAACjB,SAAS,CAAEa,gBAAgB,IAAI;QAC5DjB,MAAM,CAACiB,gBAAgB,CAAC,CAACX,OAAO,CAACjB,uBAAuB,CAAC;MAC3D,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACW,IAAI,CAAC,CAACd,OAAO,CAACe,UAAU,CAAC;MAC5Cd,GAAG,CAACK,KAAK,CAACvB,uBAAuB,CAAC;IACpC,CAAC,CAAC;IAEFU,EAAE,CAAC,yCAAyC,EAAE,MAAK;MACjD,MAAMT,EAAE,GAAG,CAAC;MACZ,MAAM+B,UAAU,GAAiC;QAC/C9B,IAAI,EAAE;OACP;MAEDL,OAAO,CAACoC,MAAM,CAAChC,EAAE,EAAE+B,UAAU,CAAC,CAACjB,SAAS,CAAC;QACvCS,KAAK,EAAGA,KAAK,IAAI;UACfb,MAAM,CAACa,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDiB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,wCAAwC,EAAE,MAAK;MAChD,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAACqC,MAAM,CAACjC,EAAE,CAAC,CAACc,SAAS,CAAC,MAAK;QAChCJ,MAAM,EAAE,CAACwB,OAAO,EAAE;MACpB,CAAC,CAAC;MAEF,MAAMjB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;IAEFb,EAAE,CAAC,yCAAyC,EAAE,MAAK;MACjD,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAACqC,MAAM,CAACjC,EAAE,CAAC,CAACc,SAAS,CAAC;QAC3BS,KAAK,EAAGA,KAAK,IAAI;UACfb,MAAM,CAACa,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDiB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}