import { Component, Inject } from '@angular/core';
import {
  Form<PERSON>uilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { Reduction } from '@contract-management/models/reduction.model';
import { CommonModule } from '@angular/common';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { ReductionService } from '@contract-management/services/reduction.service';
import { AlertService } from '@shared/services/alert.service';
import { NgxCurrencyDirective } from 'ngx-currency';
import { NgxSpinnerService } from 'ngx-spinner';
import { finalize } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-reduction-dialog',
  templateUrl: './reduction-dialog.component.html',
  styleUrl: './reduction-dialog.component.scss',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatDatepickerModule,
    NgxCurrencyDirective,
  ],
})
export class ReductionDialogComponent {
  reductionForm: FormGroup;

  constructor(
    private readonly dialogRef: MatDialogRef<ReductionDialogComponent>,
    private readonly formBuilder: FormBuilder,
    private readonly reductionService: ReductionService,
    private readonly spinner: NgxSpinnerService,
    private readonly alert: AlertService,
    @Inject(MAT_DIALOG_DATA)
    public data: {
      reduction?: Reduction;
      contractId: number;
    },
  ) {
    this.reductionForm = this.formBuilder.group({
      valueReduction: [null, [Validators.required, Validators.min(0)]],
    });

    if (this.data.reduction) {
      this.reductionForm.patchValue({
        valueReduction: (this.data.reduction as any).valueReduction,
      });
    }
  }

  onSubmit(): void {
    if (this.reductionForm.valid) {
      const reductionData = {
        ...this.reductionForm.value,
      };

      this.spinner.show();

      const CreateReduction: Reduction = {
        id: 0,
        contractId: this.data.contractId,
        dateRedution: (() => {
          const today = new Date();
          const year = today.getFullYear();
          const month = String(today.getMonth() + 1).padStart(2, '0');
          const day = String(today.getDate()).padStart(2, '0');
          return `${year}-${month}-${day}`;
        })(),
        valueRedution: this.reductionForm.value.valueReduction,
      };

      const operation = this.data.reduction
        ? this.reductionService.update(this.data.reduction.id!, {
            ...this.data.reduction,
            valueRedution: reductionData.valueReduction,
          })
        : this.reductionService.create(CreateReduction);

      operation.pipe(finalize(() => this.spinner.hide())).subscribe({
        next: (reduction) => {
          this.alert.success(
            `Reducción ${
              this.data.reduction ? 'editada' : 'agregada'
            } correctamente`,
          );
          this.dialogRef.close(reduction);
        },
        error: (error: HttpErrorResponse) => {
          this.alert.error(
            error.error?.detail ??
              `Error al ${
                this.data.reduction ? 'actualizar' : 'crear'
              } la reducción`,
          );
        },
      });
    }
  }
}
