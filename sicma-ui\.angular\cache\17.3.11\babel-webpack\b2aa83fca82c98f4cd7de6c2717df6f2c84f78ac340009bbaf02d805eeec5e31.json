{"ast": null, "code": "function cov_2rg34zq8x9() {\n  var path = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contract-management\\\\components\\\\reductions-list\\\\reduction-dialog\\\\reduction-dialog.component.ts\";\n  var hash = \"80c077c281ba12e0a10c9a893beb1f0be002147d\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contract-management\\\\components\\\\reductions-list\\\\reduction-dialog\\\\reduction-dialog.component.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 18,\n          column: 31\n        },\n        end: {\n          line: 79,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 20,\n          column: 8\n        },\n        end: {\n          line: 20,\n          column: 35\n        }\n      },\n      \"2\": {\n        start: {\n          line: 21,\n          column: 8\n        },\n        end: {\n          line: 21,\n          column: 39\n        }\n      },\n      \"3\": {\n        start: {\n          line: 22,\n          column: 8\n        },\n        end: {\n          line: 22,\n          column: 49\n        }\n      },\n      \"4\": {\n        start: {\n          line: 23,\n          column: 8\n        },\n        end: {\n          line: 23,\n          column: 31\n        }\n      },\n      \"5\": {\n        start: {\n          line: 24,\n          column: 8\n        },\n        end: {\n          line: 24,\n          column: 27\n        }\n      },\n      \"6\": {\n        start: {\n          line: 25,\n          column: 8\n        },\n        end: {\n          line: 25,\n          column: 25\n        }\n      },\n      \"7\": {\n        start: {\n          line: 26,\n          column: 8\n        },\n        end: {\n          line: 28,\n          column: 11\n        }\n      },\n      \"8\": {\n        start: {\n          line: 29,\n          column: 8\n        },\n        end: {\n          line: 33,\n          column: 9\n        }\n      },\n      \"9\": {\n        start: {\n          line: 30,\n          column: 12\n        },\n        end: {\n          line: 32,\n          column: 15\n        }\n      },\n      \"10\": {\n        start: {\n          line: 36,\n          column: 8\n        },\n        end: {\n          line: 69,\n          column: 9\n        }\n      },\n      \"11\": {\n        start: {\n          line: 37,\n          column: 34\n        },\n        end: {\n          line: 39,\n          column: 13\n        }\n      },\n      \"12\": {\n        start: {\n          line: 40,\n          column: 12\n        },\n        end: {\n          line: 40,\n          column: 32\n        }\n      },\n      \"13\": {\n        start: {\n          line: 41,\n          column: 36\n        },\n        end: {\n          line: 52,\n          column: 13\n        }\n      },\n      \"14\": {\n        start: {\n          line: 45,\n          column: 34\n        },\n        end: {\n          line: 45,\n          column: 44\n        }\n      },\n      \"15\": {\n        start: {\n          line: 46,\n          column: 33\n        },\n        end: {\n          line: 46,\n          column: 52\n        }\n      },\n      \"16\": {\n        start: {\n          line: 47,\n          column: 34\n        },\n        end: {\n          line: 47,\n          column: 79\n        }\n      },\n      \"17\": {\n        start: {\n          line: 48,\n          column: 32\n        },\n        end: {\n          line: 48,\n          column: 72\n        }\n      },\n      \"18\": {\n        start: {\n          line: 49,\n          column: 20\n        },\n        end: {\n          line: 49,\n          column: 53\n        }\n      },\n      \"19\": {\n        start: {\n          line: 53,\n          column: 30\n        },\n        end: {\n          line: 58,\n          column: 63\n        }\n      },\n      \"20\": {\n        start: {\n          line: 59,\n          column: 12\n        },\n        end: {\n          line: 68,\n          column: 15\n        }\n      },\n      \"21\": {\n        start: {\n          line: 59,\n          column: 42\n        },\n        end: {\n          line: 59,\n          column: 61\n        }\n      },\n      \"22\": {\n        start: {\n          line: 61,\n          column: 20\n        },\n        end: {\n          line: 61,\n          column: 114\n        }\n      },\n      \"23\": {\n        start: {\n          line: 62,\n          column: 20\n        },\n        end: {\n          line: 62,\n          column: 52\n        }\n      },\n      \"24\": {\n        start: {\n          line: 65,\n          column: 20\n        },\n        end: {\n          line: 66,\n          column: 97\n        }\n      },\n      \"25\": {\n        start: {\n          line: 71,\n          column: 13\n        },\n        end: {\n          line: 78,\n          column: 6\n        }\n      },\n      \"26\": {\n        start: {\n          line: 71,\n          column: 41\n        },\n        end: {\n          line: 78,\n          column: 5\n        }\n      },\n      \"27\": {\n        start: {\n          line: 80,\n          column: 0\n        },\n        end: {\n          line: 98,\n          column: 29\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 19,\n            column: 4\n          },\n          end: {\n            line: 19,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 19,\n            column: 80\n          },\n          end: {\n            line: 34,\n            column: 5\n          }\n        },\n        line: 19\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 35,\n            column: 4\n          },\n          end: {\n            line: 35,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 35,\n            column: 15\n          },\n          end: {\n            line: 70,\n            column: 5\n          }\n        },\n        line: 35\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 44,\n            column: 31\n          },\n          end: {\n            line: 44,\n            column: 32\n          }\n        },\n        loc: {\n          start: {\n            line: 44,\n            column: 37\n          },\n          end: {\n            line: 50,\n            column: 17\n          }\n        },\n        line: 44\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 59,\n            column: 36\n          },\n          end: {\n            line: 59,\n            column: 37\n          }\n        },\n        loc: {\n          start: {\n            line: 59,\n            column: 42\n          },\n          end: {\n            line: 59,\n            column: 61\n          }\n        },\n        line: 59\n      },\n      \"4\": {\n        name: \"(anonymous_4)\",\n        decl: {\n          start: {\n            line: 60,\n            column: 22\n          },\n          end: {\n            line: 60,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 60,\n            column: 37\n          },\n          end: {\n            line: 63,\n            column: 17\n          }\n        },\n        line: 60\n      },\n      \"5\": {\n        name: \"(anonymous_5)\",\n        decl: {\n          start: {\n            line: 64,\n            column: 23\n          },\n          end: {\n            line: 64,\n            column: 24\n          }\n        },\n        loc: {\n          start: {\n            line: 64,\n            column: 34\n          },\n          end: {\n            line: 67,\n            column: 17\n          }\n        },\n        line: 64\n      },\n      \"6\": {\n        name: \"(anonymous_6)\",\n        decl: {\n          start: {\n            line: 71,\n            column: 35\n          },\n          end: {\n            line: 71,\n            column: 36\n          }\n        },\n        loc: {\n          start: {\n            line: 71,\n            column: 41\n          },\n          end: {\n            line: 78,\n            column: 5\n          }\n        },\n        line: 71\n      }\n    },\n    branchMap: {\n      \"0\": {\n        loc: {\n          start: {\n            line: 29,\n            column: 8\n          },\n          end: {\n            line: 33,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 29,\n            column: 8\n          },\n          end: {\n            line: 33,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 29\n      },\n      \"1\": {\n        loc: {\n          start: {\n            line: 36,\n            column: 8\n          },\n          end: {\n            line: 69,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 36,\n            column: 8\n          },\n          end: {\n            line: 69,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 36\n      },\n      \"2\": {\n        loc: {\n          start: {\n            line: 53,\n            column: 30\n          },\n          end: {\n            line: 58,\n            column: 63\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 54,\n            column: 18\n          },\n          end: {\n            line: 57,\n            column: 18\n          }\n        }, {\n          start: {\n            line: 58,\n            column: 18\n          },\n          end: {\n            line: 58,\n            column: 63\n          }\n        }],\n        line: 53\n      },\n      \"3\": {\n        loc: {\n          start: {\n            line: 61,\n            column: 52\n          },\n          end: {\n            line: 61,\n            column: 96\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 61,\n            column: 74\n          },\n          end: {\n            line: 61,\n            column: 83\n          }\n        }, {\n          start: {\n            line: 61,\n            column: 86\n          },\n          end: {\n            line: 61,\n            column: 96\n          }\n        }],\n        line: 61\n      },\n      \"4\": {\n        loc: {\n          start: {\n            line: 65,\n            column: 37\n          },\n          end: {\n            line: 66,\n            column: 95\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 65,\n            column: 37\n          },\n          end: {\n            line: 65,\n            column: 56\n          }\n        }, {\n          start: {\n            line: 66,\n            column: 24\n          },\n          end: {\n            line: 66,\n            column: 95\n          }\n        }],\n        line: 65\n      },\n      \"5\": {\n        loc: {\n          start: {\n            line: 66,\n            column: 36\n          },\n          end: {\n            line: 66,\n            column: 80\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 66,\n            column: 58\n          },\n          end: {\n            line: 66,\n            column: 70\n          }\n        }, {\n          start: {\n            line: 66,\n            column: 73\n          },\n          end: {\n            line: 66,\n            column: 80\n          }\n        }],\n        line: 66\n      }\n    },\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0\n    },\n    b: {\n      \"0\": [0, 0],\n      \"1\": [0, 0],\n      \"2\": [0, 0],\n      \"3\": [0, 0],\n      \"4\": [0, 0],\n      \"5\": [0, 0]\n    },\n    inputSourceMap: {\n      version: 3,\n      file: \"reduction-dialog.component.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contract-management\\\\components\\\\reductions-list\\\\reduction-dialog\\\\reduction-dialog.component.ts\"],\n      names: [],\n      mappings: \";;;AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,eAAe,CAAC;AAClD,OAAO,EACL,WAAW,EAEX,mBAAmB,EACnB,UAAU,GACX,MAAM,gBAAgB,CAAC;AACxB,OAAO,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAC3D,OAAO,EACL,eAAe,EACf,eAAe,EACf,YAAY,GACb,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,kBAAkB,EAAE,MAAM,8BAA8B,CAAC;AAClE,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AAEzD,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAC/C,OAAO,EAAE,mBAAmB,EAAE,MAAM,8BAA8B,CAAC;AACnE,OAAO,EAAE,gBAAgB,EAAE,MAAM,iDAAiD,CAAC;AACnF,OAAO,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAC9D,OAAO,EAAE,oBAAoB,EAAE,MAAM,cAAc,CAAC;AACpD,OAAO,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AAChD,OAAO,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAoBnC,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;IAGnC,YACmB,SAAiD,EACjD,WAAwB,EACxB,gBAAkC,EAClC,OAA0B,EAC1B,KAAmB,EAE7B,IAGN;QATgB,cAAS,GAAT,SAAS,CAAwC;QACjD,gBAAW,GAAX,WAAW,CAAa;QACxB,qBAAgB,GAAhB,gBAAgB,CAAkB;QAClC,YAAO,GAAP,OAAO,CAAmB;QAC1B,UAAK,GAAL,KAAK,CAAc;QAE7B,SAAI,GAAJ,IAAI,CAGV;QAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;YAC1C,cAAc,EAAE,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;SACjE,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACxB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;gBAC5B,cAAc,EAAG,IAAI,CAAC,IAAI,CAAC,SAAiB,CAAC,cAAc;aAC5D,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,QAAQ;QACN,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;YAC7B,MAAM,aAAa,GAAG;gBACpB,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK;aAC5B,CAAC;YAEF,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAEpB,MAAM,eAAe,GAAc;gBACjC,EAAE,EAAE,CAAC;gBACL,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU;gBAChC,YAAY,EAAE,CAAC,GAAG,EAAE;oBAClB,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;oBACzB,MAAM,IAAI,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;oBACjC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;oBAC5D,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;oBACrD,OAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,EAAE,CAAC;gBACnC,CAAC,CAAC,EAAE;gBACJ,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,cAAc;aACvD,CAAC;YAEF,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS;gBACnC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAG,EAAE;oBACpD,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS;oBACtB,aAAa,EAAE,aAAa,CAAC,cAAc;iBAC5C,CAAC;gBACJ,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YAElD,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;gBAC5D,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE;oBAClB,IAAI,CAAC,KAAK,CAAC,OAAO,CAChB,aACE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UACpC,gBAAgB,CACjB,CAAC;oBACF,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;gBAClC,CAAC;gBACD,KAAK,EAAE,CAAC,KAAwB,EAAE,EAAE;oBAClC,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,KAAK,CAAC,KAAK,EAAE,MAAM;wBACjB,YACE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,OACvC,eAAe,CAClB,CAAC;gBACJ,CAAC;aACF,CAAC,CAAC;QACL,CAAC;IACH,CAAC;;;;;;;gDAhEE,MAAM,SAAC,eAAe;;;AATd,wBAAwB;IAjBpC,SAAS,CAAC;QACT,QAAQ,EAAE,sBAAsB;QAChC,8BAAgD;QAEhD,UAAU,EAAE,IAAI;QAChB,OAAO,EAAE;YACP,YAAY;YACZ,mBAAmB;YACnB,eAAe;YACf,eAAe;YACf,kBAAkB;YAClB,cAAc;YACd,aAAa;YACb,mBAAmB;YACnB,oBAAoB;SACrB;;KACF,CAAC;GACW,wBAAwB,CA0EpC\",\n      sourcesContent: [\"import { Component, Inject } from '@angular/core';\\nimport {\\n  FormBuilder,\\n  FormGroup,\\n  ReactiveFormsModule,\\n  Validators,\\n} from '@angular/forms';\\nimport { MatButtonModule } from '@angular/material/button';\\nimport {\\n  MAT_DIALOG_DATA,\\n  MatDialogModule,\\n  MatDialogRef,\\n} from '@angular/material/dialog';\\nimport { MatFormFieldModule } from '@angular/material/form-field';\\nimport { MatIconModule } from '@angular/material/icon';\\nimport { MatInputModule } from '@angular/material/input';\\nimport { Reduction } from '@contract-management/models/reduction.model';\\nimport { CommonModule } from '@angular/common';\\nimport { MatDatepickerModule } from '@angular/material/datepicker';\\nimport { ReductionService } from '@contract-management/services/reduction.service';\\nimport { AlertService } from '@shared/services/alert.service';\\nimport { NgxCurrencyDirective } from 'ngx-currency';\\nimport { NgxSpinnerService } from 'ngx-spinner';\\nimport { finalize } from 'rxjs/operators';\\nimport { HttpErrorResponse } from '@angular/common/http';\\n\\n@Component({\\n  selector: 'app-reduction-dialog',\\n  templateUrl: './reduction-dialog.component.html',\\n  styleUrl: './reduction-dialog.component.scss',\\n  standalone: true,\\n  imports: [\\n    CommonModule,\\n    ReactiveFormsModule,\\n    MatDialogModule,\\n    MatButtonModule,\\n    MatFormFieldModule,\\n    MatInputModule,\\n    MatIconModule,\\n    MatDatepickerModule,\\n    NgxCurrencyDirective,\\n  ],\\n})\\nexport class ReductionDialogComponent {\\n  reductionForm: FormGroup;\\n\\n  constructor(\\n    private readonly dialogRef: MatDialogRef<ReductionDialogComponent>,\\n    private readonly formBuilder: FormBuilder,\\n    private readonly reductionService: ReductionService,\\n    private readonly spinner: NgxSpinnerService,\\n    private readonly alert: AlertService,\\n    @Inject(MAT_DIALOG_DATA)\\n    public data: {\\n      reduction?: Reduction;\\n      contractId: number;\\n    },\\n  ) {\\n    this.reductionForm = this.formBuilder.group({\\n      valueReduction: [null, [Validators.required, Validators.min(0)]],\\n    });\\n\\n    if (this.data.reduction) {\\n      this.reductionForm.patchValue({\\n        valueReduction: (this.data.reduction as any).valueReduction,\\n      });\\n    }\\n  }\\n\\n  onSubmit(): void {\\n    if (this.reductionForm.valid) {\\n      const reductionData = {\\n        ...this.reductionForm.value,\\n      };\\n\\n      this.spinner.show();\\n\\n      const CreateReduction: Reduction = {\\n        id: 0,\\n        contractId: this.data.contractId,\\n        dateRedution: (() => {\\n          const today = new Date();\\n          const year = today.getFullYear();\\n          const month = String(today.getMonth() + 1).padStart(2, '0');\\n          const day = String(today.getDate()).padStart(2, '0');\\n          return `${year}-${month}-${day}`;\\n        })(),\\n        valueRedution: this.reductionForm.value.valueReduction,\\n      };\\n\\n      const operation = this.data.reduction\\n        ? this.reductionService.update(this.data.reduction.id!, {\\n            ...this.data.reduction,\\n            valueRedution: reductionData.valueReduction,\\n          })\\n        : this.reductionService.create(CreateReduction);\\n\\n      operation.pipe(finalize(() => this.spinner.hide())).subscribe({\\n        next: (reduction) => {\\n          this.alert.success(\\n            `Reducci\\xF3n ${\\n              this.data.reduction ? 'editada' : 'agregada'\\n            } correctamente`,\\n          );\\n          this.dialogRef.close(reduction);\\n        },\\n        error: (error: HttpErrorResponse) => {\\n          this.alert.error(\\n            error.error?.detail ??\\n              `Error al ${\\n                this.data.reduction ? 'actualizar' : 'crear'\\n              } la reducci\\xF3n`,\\n          );\\n        },\\n      });\\n    }\\n  }\\n}\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"80c077c281ba12e0a10c9a893beb1f0be002147d\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_2rg34zq8x9 = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_2rg34zq8x9();\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./reduction-dialog.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./reduction-dialog.component.scss?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { CommonModule } from '@angular/common';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { ReductionService } from '@contract-management/services/reduction.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { NgxCurrencyDirective } from 'ngx-currency';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { finalize } from 'rxjs/operators';\ncov_2rg34zq8x9().s[0]++;\nlet ReductionDialogComponent = class ReductionDialogComponent {\n  constructor(dialogRef, formBuilder, reductionService, spinner, alert, data) {\n    cov_2rg34zq8x9().f[0]++;\n    cov_2rg34zq8x9().s[1]++;\n    this.dialogRef = dialogRef;\n    cov_2rg34zq8x9().s[2]++;\n    this.formBuilder = formBuilder;\n    cov_2rg34zq8x9().s[3]++;\n    this.reductionService = reductionService;\n    cov_2rg34zq8x9().s[4]++;\n    this.spinner = spinner;\n    cov_2rg34zq8x9().s[5]++;\n    this.alert = alert;\n    cov_2rg34zq8x9().s[6]++;\n    this.data = data;\n    cov_2rg34zq8x9().s[7]++;\n    this.reductionForm = this.formBuilder.group({\n      valueReduction: [null, [Validators.required, Validators.min(0)]]\n    });\n    cov_2rg34zq8x9().s[8]++;\n    if (this.data.reduction) {\n      cov_2rg34zq8x9().b[0][0]++;\n      cov_2rg34zq8x9().s[9]++;\n      this.reductionForm.patchValue({\n        valueReduction: this.data.reduction.valueReduction\n      });\n    } else {\n      cov_2rg34zq8x9().b[0][1]++;\n    }\n  }\n  onSubmit() {\n    cov_2rg34zq8x9().f[1]++;\n    cov_2rg34zq8x9().s[10]++;\n    if (this.reductionForm.valid) {\n      cov_2rg34zq8x9().b[1][0]++;\n      const reductionData = (cov_2rg34zq8x9().s[11]++, {\n        ...this.reductionForm.value\n      });\n      cov_2rg34zq8x9().s[12]++;\n      this.spinner.show();\n      const CreateReduction = (cov_2rg34zq8x9().s[13]++, {\n        id: 0,\n        contractId: this.data.contractId,\n        dateRedution: (() => {\n          cov_2rg34zq8x9().f[2]++;\n          const today = (cov_2rg34zq8x9().s[14]++, new Date());\n          const year = (cov_2rg34zq8x9().s[15]++, today.getFullYear());\n          const month = (cov_2rg34zq8x9().s[16]++, String(today.getMonth() + 1).padStart(2, '0'));\n          const day = (cov_2rg34zq8x9().s[17]++, String(today.getDate()).padStart(2, '0'));\n          cov_2rg34zq8x9().s[18]++;\n          return `${year}-${month}-${day}`;\n        })(),\n        valueRedution: this.reductionForm.value.valueReduction\n      });\n      const operation = (cov_2rg34zq8x9().s[19]++, this.data.reduction ? (cov_2rg34zq8x9().b[2][0]++, this.reductionService.update(this.data.reduction.id, {\n        ...this.data.reduction,\n        valueRedution: reductionData.valueReduction\n      })) : (cov_2rg34zq8x9().b[2][1]++, this.reductionService.create(CreateReduction)));\n      cov_2rg34zq8x9().s[20]++;\n      operation.pipe(finalize(() => {\n        cov_2rg34zq8x9().f[3]++;\n        cov_2rg34zq8x9().s[21]++;\n        return this.spinner.hide();\n      })).subscribe({\n        next: reduction => {\n          cov_2rg34zq8x9().f[4]++;\n          cov_2rg34zq8x9().s[22]++;\n          this.alert.success(`Reducción ${this.data.reduction ? (cov_2rg34zq8x9().b[3][0]++, 'editada') : (cov_2rg34zq8x9().b[3][1]++, 'agregada')} correctamente`);\n          cov_2rg34zq8x9().s[23]++;\n          this.dialogRef.close(reduction);\n        },\n        error: error => {\n          cov_2rg34zq8x9().f[5]++;\n          cov_2rg34zq8x9().s[24]++;\n          this.alert.error((cov_2rg34zq8x9().b[4][0]++, error.error?.detail) ?? (cov_2rg34zq8x9().b[4][1]++, `Error al ${this.data.reduction ? (cov_2rg34zq8x9().b[5][0]++, 'actualizar') : (cov_2rg34zq8x9().b[5][1]++, 'crear')} la reducción`));\n        }\n      });\n    } else {\n      cov_2rg34zq8x9().b[1][1]++;\n    }\n  }\n  static {\n    cov_2rg34zq8x9().s[25]++;\n    this.ctorParameters = () => {\n      cov_2rg34zq8x9().f[6]++;\n      cov_2rg34zq8x9().s[26]++;\n      return [{\n        type: MatDialogRef\n      }, {\n        type: FormBuilder\n      }, {\n        type: ReductionService\n      }, {\n        type: NgxSpinnerService\n      }, {\n        type: AlertService\n      }, {\n        type: undefined,\n        decorators: [{\n          type: Inject,\n          args: [MAT_DIALOG_DATA]\n        }]\n      }];\n    };\n  }\n};\ncov_2rg34zq8x9().s[27]++;\nReductionDialogComponent = __decorate([Component({\n  selector: 'app-reduction-dialog',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule, MatDialogModule, MatButtonModule, MatFormFieldModule, MatInputModule, MatIconModule, MatDatepickerModule, NgxCurrencyDirective],\n  styles: [__NG_CLI_RESOURCE__1]\n})], ReductionDialogComponent);\nexport { ReductionDialogComponent };", "map": {"version": 3, "names": ["cov_2rg34zq8x9", "actualCoverage", "Component", "Inject", "FormBuilder", "ReactiveFormsModule", "Validators", "MatButtonModule", "MAT_DIALOG_DATA", "MatDialogModule", "MatDialogRef", "MatFormFieldModule", "MatIconModule", "MatInputModule", "CommonModule", "MatDatepickerModule", "ReductionService", "AlertService", "NgxCurrencyDirective", "NgxSpinnerService", "finalize", "s", "ReductionDialogComponent", "constructor", "dialogRef", "formBuilder", "reductionService", "spinner", "alert", "data", "f", "reductionForm", "group", "valueReduction", "required", "min", "reduction", "b", "patchValue", "onSubmit", "valid", "reductionData", "value", "show", "CreateReduction", "id", "contractId", "dateRedution", "today", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "valueRedution", "operation", "update", "create", "pipe", "hide", "subscribe", "next", "success", "close", "error", "detail", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\components\\reductions-list\\reduction-dialog\\reduction-dialog.component.ts"], "sourcesContent": ["import { Component, Inject } from '@angular/core';\nimport {\n  Form<PERSON>uilder,\n  FormGroup,\n  ReactiveFormsModule,\n  Validators,\n} from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport {\n  MAT_DIALOG_DATA,\n  MatDialogModule,\n  MatDialogRef,\n} from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { Reduction } from '@contract-management/models/reduction.model';\nimport { CommonModule } from '@angular/common';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { ReductionService } from '@contract-management/services/reduction.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { NgxCurrencyDirective } from 'ngx-currency';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { finalize } from 'rxjs/operators';\nimport { HttpErrorResponse } from '@angular/common/http';\n\n@Component({\n  selector: 'app-reduction-dialog',\n  templateUrl: './reduction-dialog.component.html',\n  styleUrl: './reduction-dialog.component.scss',\n  standalone: true,\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    MatDialogModule,\n    MatButtonModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatIconModule,\n    MatDatepickerModule,\n    NgxCurrencyDirective,\n  ],\n})\nexport class ReductionDialogComponent {\n  reductionForm: FormGroup;\n\n  constructor(\n    private readonly dialogRef: MatDialogRef<ReductionDialogComponent>,\n    private readonly formBuilder: FormBuilder,\n    private readonly reductionService: ReductionService,\n    private readonly spinner: NgxSpinnerService,\n    private readonly alert: AlertService,\n    @Inject(MAT_DIALOG_DATA)\n    public data: {\n      reduction?: Reduction;\n      contractId: number;\n    },\n  ) {\n    this.reductionForm = this.formBuilder.group({\n      valueReduction: [null, [Validators.required, Validators.min(0)]],\n    });\n\n    if (this.data.reduction) {\n      this.reductionForm.patchValue({\n        valueReduction: (this.data.reduction as any).valueReduction,\n      });\n    }\n  }\n\n  onSubmit(): void {\n    if (this.reductionForm.valid) {\n      const reductionData = {\n        ...this.reductionForm.value,\n      };\n\n      this.spinner.show();\n\n      const CreateReduction: Reduction = {\n        id: 0,\n        contractId: this.data.contractId,\n        dateRedution: (() => {\n          const today = new Date();\n          const year = today.getFullYear();\n          const month = String(today.getMonth() + 1).padStart(2, '0');\n          const day = String(today.getDate()).padStart(2, '0');\n          return `${year}-${month}-${day}`;\n        })(),\n        valueRedution: this.reductionForm.value.valueReduction,\n      };\n\n      const operation = this.data.reduction\n        ? this.reductionService.update(this.data.reduction.id!, {\n            ...this.data.reduction,\n            valueRedution: reductionData.valueReduction,\n          })\n        : this.reductionService.create(CreateReduction);\n\n      operation.pipe(finalize(() => this.spinner.hide())).subscribe({\n        next: (reduction) => {\n          this.alert.success(\n            `Reducción ${\n              this.data.reduction ? 'editada' : 'agregada'\n            } correctamente`,\n          );\n          this.dialogRef.close(reduction);\n        },\n        error: (error: HttpErrorResponse) => {\n          this.alert.error(\n            error.error?.detail ??\n              `Error al ${\n                this.data.reduction ? 'actualizar' : 'crear'\n              } la reducción`,\n          );\n        },\n      });\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAsBS;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;AAtBT,SAASE,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SACEC,WAAW,EAEXC,mBAAmB,EACnBC,UAAU,QACL,gBAAgB;AACvB,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SACEC,eAAe,EACfC,eAAe,EACfC,YAAY,QACP,0BAA0B;AACjC,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AAExD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,gBAAgB,QAAQ,iDAAiD;AAClF,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,oBAAoB,QAAQ,cAAc;AACnD,SAASC,iBAAiB,QAAQ,aAAa;AAC/C,SAASC,QAAQ,QAAQ,gBAAgB;AAACpB,cAAA,GAAAqB,CAAA;AAoBnC,IAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB;EAGnCC,YACmBC,SAAiD,EACjDC,WAAwB,EACxBC,gBAAkC,EAClCC,OAA0B,EAC1BC,KAAmB,EAE7BC,IAGN;IAAA7B,cAAA,GAAA8B,CAAA;IAAA9B,cAAA,GAAAqB,CAAA;IATgB,KAAAG,SAAS,GAATA,SAAS;IAAwCxB,cAAA,GAAAqB,CAAA;IACjD,KAAAI,WAAW,GAAXA,WAAW;IAAazB,cAAA,GAAAqB,CAAA;IACxB,KAAAK,gBAAgB,GAAhBA,gBAAgB;IAAkB1B,cAAA,GAAAqB,CAAA;IAClC,KAAAM,OAAO,GAAPA,OAAO;IAAmB3B,cAAA,GAAAqB,CAAA;IAC1B,KAAAO,KAAK,GAALA,KAAK;IAAc5B,cAAA,GAAAqB,CAAA;IAE7B,KAAAQ,IAAI,GAAJA,IAAI;IAGV7B,cAAA,GAAAqB,CAAA;IAED,IAAI,CAACU,aAAa,GAAG,IAAI,CAACN,WAAW,CAACO,KAAK,CAAC;MAC1CC,cAAc,EAAE,CAAC,IAAI,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAAC6B,GAAG,CAAC,CAAC,CAAC,CAAC;KAChE,CAAC;IAACnC,cAAA,GAAAqB,CAAA;IAEH,IAAI,IAAI,CAACQ,IAAI,CAACO,SAAS,EAAE;MAAApC,cAAA,GAAAqC,CAAA;MAAArC,cAAA,GAAAqB,CAAA;MACvB,IAAI,CAACU,aAAa,CAACO,UAAU,CAAC;QAC5BL,cAAc,EAAG,IAAI,CAACJ,IAAI,CAACO,SAAiB,CAACH;OAC9C,CAAC;IACJ,CAAC;MAAAjC,cAAA,GAAAqC,CAAA;IAAA;EACH;EAEAE,QAAQA,CAAA;IAAAvC,cAAA,GAAA8B,CAAA;IAAA9B,cAAA,GAAAqB,CAAA;IACN,IAAI,IAAI,CAACU,aAAa,CAACS,KAAK,EAAE;MAAAxC,cAAA,GAAAqC,CAAA;MAC5B,MAAMI,aAAa,IAAAzC,cAAA,GAAAqB,CAAA,QAAG;QACpB,GAAG,IAAI,CAACU,aAAa,CAACW;OACvB;MAAC1C,cAAA,GAAAqB,CAAA;MAEF,IAAI,CAACM,OAAO,CAACgB,IAAI,EAAE;MAEnB,MAAMC,eAAe,IAAA5C,cAAA,GAAAqB,CAAA,QAAc;QACjCwB,EAAE,EAAE,CAAC;QACLC,UAAU,EAAE,IAAI,CAACjB,IAAI,CAACiB,UAAU;QAChCC,YAAY,EAAE,CAAC,MAAK;UAAA/C,cAAA,GAAA8B,CAAA;UAClB,MAAMkB,KAAK,IAAAhD,cAAA,GAAAqB,CAAA,QAAG,IAAI4B,IAAI,EAAE;UACxB,MAAMC,IAAI,IAAAlD,cAAA,GAAAqB,CAAA,QAAG2B,KAAK,CAACG,WAAW,EAAE;UAChC,MAAMC,KAAK,IAAApD,cAAA,GAAAqB,CAAA,QAAGgC,MAAM,CAACL,KAAK,CAACM,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;UAC3D,MAAMC,GAAG,IAAAxD,cAAA,GAAAqB,CAAA,QAAGgC,MAAM,CAACL,KAAK,CAACS,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;UAACvD,cAAA,GAAAqB,CAAA;UACrD,OAAO,GAAG6B,IAAI,IAAIE,KAAK,IAAII,GAAG,EAAE;QAClC,CAAC,EAAC,CAAE;QACJE,aAAa,EAAE,IAAI,CAAC3B,aAAa,CAACW,KAAK,CAACT;OACzC;MAED,MAAM0B,SAAS,IAAA3D,cAAA,GAAAqB,CAAA,QAAG,IAAI,CAACQ,IAAI,CAACO,SAAS,IAAApC,cAAA,GAAAqC,CAAA,UACjC,IAAI,CAACX,gBAAgB,CAACkC,MAAM,CAAC,IAAI,CAAC/B,IAAI,CAACO,SAAS,CAACS,EAAG,EAAE;QACpD,GAAG,IAAI,CAAChB,IAAI,CAACO,SAAS;QACtBsB,aAAa,EAAEjB,aAAa,CAACR;OAC9B,CAAC,KAAAjC,cAAA,GAAAqC,CAAA,UACF,IAAI,CAACX,gBAAgB,CAACmC,MAAM,CAACjB,eAAe,CAAC;MAAC5C,cAAA,GAAAqB,CAAA;MAElDsC,SAAS,CAACG,IAAI,CAAC1C,QAAQ,CAAC,MAAM;QAAApB,cAAA,GAAA8B,CAAA;QAAA9B,cAAA,GAAAqB,CAAA;QAAA,WAAI,CAACM,OAAO,CAACoC,IAAI,EAAE;MAAF,CAAE,CAAC,CAAC,CAACC,SAAS,CAAC;QAC5DC,IAAI,EAAG7B,SAAS,IAAI;UAAApC,cAAA,GAAA8B,CAAA;UAAA9B,cAAA,GAAAqB,CAAA;UAClB,IAAI,CAACO,KAAK,CAACsC,OAAO,CAChB,aACE,IAAI,CAACrC,IAAI,CAACO,SAAS,IAAApC,cAAA,GAAAqC,CAAA,UAAG,SAAS,KAAArC,cAAA,GAAAqC,CAAA,UAAG,UACpC,iBAAgB,CACjB;UAACrC,cAAA,GAAAqB,CAAA;UACF,IAAI,CAACG,SAAS,CAAC2C,KAAK,CAAC/B,SAAS,CAAC;QACjC,CAAC;QACDgC,KAAK,EAAGA,KAAwB,IAAI;UAAApE,cAAA,GAAA8B,CAAA;UAAA9B,cAAA,GAAAqB,CAAA;UAClC,IAAI,CAACO,KAAK,CAACwC,KAAK,CACd,CAAApE,cAAA,GAAAqC,CAAA,UAAA+B,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAArE,cAAA,GAAAqC,CAAA,UACjB,YACE,IAAI,CAACR,IAAI,CAACO,SAAS,IAAApC,cAAA,GAAAqC,CAAA,UAAG,YAAY,KAAArC,cAAA,GAAAqC,CAAA,UAAG,OACvC,gBAAe,EAClB;QACH;OACD,CAAC;IACJ,CAAC;MAAArC,cAAA,GAAAqC,CAAA;IAAA;EACH;;;;;;;;;;;;;;;;;;;gBAhEGlC,MAAM;UAAAmE,IAAA,GAAC9D,eAAe;QAAA;MAAA,E;;;;;AATdc,wBAAwB,GAAAiD,UAAA,EAjBpCrE,SAAS,CAAC;EACTsE,QAAQ,EAAE,sBAAsB;EAChCC,QAAA,EAAAC,oBAAgD;EAEhDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP9D,YAAY,EACZT,mBAAmB,EACnBI,eAAe,EACfF,eAAe,EACfI,kBAAkB,EAClBE,cAAc,EACdD,aAAa,EACbG,mBAAmB,EACnBG,oBAAoB,CACrB;;CACF,CAAC,C,EACWI,wBAAwB,CA0EpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}