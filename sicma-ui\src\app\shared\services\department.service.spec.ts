import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { environment } from '@env';
import { Department } from '@shared/models/department.model';
import { DepartmentService } from './department.service';

describe('DepartmentService', () => {
  let service: DepartmentService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/departments`;

  const mockDepartment: Department = {
    id: 1,
    name: 'Test Department',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [DepartmentService],
    });
    service = TestBed.inject(DepartmentService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all departments', () => {
      const mockDepartments = [mockDepartment];

      service.getAll().subscribe((departments) => {
        expect(departments).toEqual(mockDepartments);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockDepartments);
    });
  });

  describe('getById', () => {
    it('should return a department by id', () => {
      const id = 1;

      service.getById(id).subscribe((department) => {
        expect(department).toEqual(mockDepartment);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockDepartment);
    });
  });

  describe('create', () => {
    it('should create a new department', () => {
      const newDepartment: Omit<Department, 'id'> = {
        name: 'New Department',
      };

      service.create(newDepartment).subscribe((department) => {
        expect(department).toEqual(mockDepartment);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newDepartment);
      req.flush(mockDepartment);
    });
  });

  describe('update', () => {
    it('should update a department', () => {
      const id = 1;
      const updateData: Partial<Department> = {
        name: 'Updated Department',
      };

      service.update(id, updateData).subscribe((department) => {
        expect(department).toEqual(mockDepartment);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockDepartment);
    });
  });

  describe('delete', () => {
    it('should delete a department', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });

  describe('getByName', () => {
    it('should return a department by name', () => {
      const name = 'Test Department';

      service.getByName(name).subscribe((department) => {
        expect(department).toEqual(mockDepartment);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockDepartment);
    });
  });
});