import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { TaxBenefit } from '@contractor-dashboard/models/tax-benefit.model';
import { environment } from '@env';
import { TaxBenefitService } from './tax-benefit.service';

describe('TaxBenefitService', () => {
  let service: TaxBenefitService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/tax-benefits`;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [TaxBenefitService],
    });
    service = TestBed.inject(TaxBenefitService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  const mockTaxBenefit: TaxBenefit = {
    id: 1,
    prepaidMedicine: true,
    dependents: false,
    homeInterest: true,
    afcAccount: false,
    voluntaryPension: true,
    monthlyReportId: 1,
  };

  describe('getAll', () => {
    it('should return all tax benefits', () => {
      const mockTaxBenefits = [mockTaxBenefit];

      service.getAll().subscribe((taxBenefits) => {
        expect(taxBenefits).toEqual(mockTaxBenefits);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockTaxBenefits);
    });

    it('should handle error when getting all tax benefits', () => {
      service.getAll().subscribe({
        error: (error) => {
          expect(error.status).toBe(500);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Server error'), { status: 500 });
    });
  });

  describe('getById', () => {
    it('should return a tax benefit by id', () => {
      service.getById(1).subscribe((taxBenefit) => {
        expect(taxBenefit).toEqual(mockTaxBenefit);
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('GET');
      req.flush(mockTaxBenefit);
    });

    it('should handle error when getting tax benefit by id', () => {
      service.getById(999).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/999`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('create', () => {
    const newTaxBenefit: Omit<TaxBenefit, 'id'> = {
      prepaidMedicine: true,
      dependents: false,
      homeInterest: true,
      afcAccount: false,
      voluntaryPension: true,
      monthlyReportId: 1,
    };

    it('should create a new tax benefit', () => {
      service.create(newTaxBenefit).subscribe((taxBenefit) => {
        expect(taxBenefit).toEqual(mockTaxBenefit);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newTaxBenefit);
      req.flush(mockTaxBenefit);
    });

    it('should handle error when creating tax benefit', () => {
      service.create(newTaxBenefit).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('update', () => {
    const updateData: Partial<TaxBenefit> = {
      prepaidMedicine: false,
      dependents: true,
    };

    it('should update a tax benefit', () => {
      service.update(1, updateData).subscribe((taxBenefit) => {
        expect(taxBenefit).toEqual({ ...mockTaxBenefit, ...updateData });
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush({ ...mockTaxBenefit, ...updateData });
    });

    it('should handle error when updating tax benefit', () => {
      service.update(1, updateData).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('delete', () => {
    it('should delete a tax benefit', () => {
      service.delete(1).subscribe((response) => {
        expect(response).toBeNull();
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });

    it('should handle error when deleting tax benefit', () => {
      service.delete(1).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('getByMonthlyReportId', () => {
    it('should return a tax benefit by monthly report id', () => {
      const monthlyReportId = 1;

      service.getByMonthlyReportId(monthlyReportId).subscribe((taxBenefit) => {
        expect(taxBenefit).toEqual(mockTaxBenefit);
      });

      const req = httpMock.expectOne(
        `${apiUrl}/monthly-report/${monthlyReportId}`,
      );
      expect(req.request.method).toBe('GET');
      req.flush(mockTaxBenefit);
    });

    it('should handle error when getting tax benefit by monthly report id', () => {
      const monthlyReportId = 999;

      service.getByMonthlyReportId(monthlyReportId).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(
        `${apiUrl}/monthly-report/${monthlyReportId}`,
      );
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });
});