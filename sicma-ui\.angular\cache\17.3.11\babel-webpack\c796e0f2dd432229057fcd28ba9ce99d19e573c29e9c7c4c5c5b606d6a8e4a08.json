{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { ContractValuesService } from './contract-values.service';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { ContractAuditHistoryService } from './contract-audit-history.service';\nimport { ContractAuditStatusService } from './contract-audit-status.service';\nimport { of } from 'rxjs';\ndescribe('ContractValuesService', () => {\n  let service;\n  let httpMock;\n  let authServiceSpy;\n  let contractAuditHistoryServiceSpy;\n  let contractAuditStatusServiceSpy;\n  const apiUrl = `${environment.apiUrl}/contract-values`;\n  const mockContractValues = {\n    id: 1,\n    contractId: 1,\n    numericValue: 1000000,\n    startDate: '2023-01-01',\n    endDate: '2023-12-31',\n    subscriptionDate: '2023-01-01',\n    cdp: 12345,\n    cdpEntityId: 1,\n    madsValue: 0,\n    isOtherEntity: false,\n    cdpEntity: {\n      id: 1,\n      name: 'Test CDP Entity'\n    }\n  };\n  const mockUser = {\n    id: 1,\n    username: 'testuser',\n    profiles: []\n  };\n  const mockAuditStatus = {\n    id: 1,\n    name: 'Adición de valores',\n    description: 'Contract values creation status'\n  };\n  beforeEach(() => {\n    authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);\n    contractAuditHistoryServiceSpy = jasmine.createSpyObj('ContractAuditHistoryService', ['create']);\n    contractAuditStatusServiceSpy = jasmine.createSpyObj('ContractAuditStatusService', ['getByName']);\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [ContractValuesService, {\n        provide: AuthService,\n        useValue: authServiceSpy\n      }, {\n        provide: ContractAuditHistoryService,\n        useValue: contractAuditHistoryServiceSpy\n      }, {\n        provide: ContractAuditStatusService,\n        useValue: contractAuditStatusServiceSpy\n      }]\n    });\n    service = TestBed.inject(ContractValuesService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all contract values', () => {\n      const mockValues = [mockContractValues];\n      service.getAll().subscribe(values => {\n        expect(values).toEqual(mockValues);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockValues);\n    });\n  });\n  describe('getById', () => {\n    it('should return a single contract values by id', () => {\n      service.getById(1).subscribe(values => {\n        expect(values).toEqual(mockContractValues);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractValues);\n    });\n  });\n  describe('getAllByContractId', () => {\n    it('should return contract values by contract id', () => {\n      const contractId = 1;\n      const mockValues = [mockContractValues];\n      service.getAllByContractId(contractId).subscribe(values => {\n        expect(values).toEqual(mockValues);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockValues);\n    });\n  });\n  describe('getStartDateByContractId', () => {\n    it('should return start date by contract id', () => {\n      const contractId = 1;\n      const startDate = new Date('2023-01-01');\n      service.getStartDateByContractId(contractId).subscribe(date => {\n        expect(date).toEqual(startDate);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}/start-date`);\n      expect(req.request.method).toBe('GET');\n      req.flush(startDate);\n    });\n    it('should handle null date', () => {\n      const contractId = 1;\n      service.getStartDateByContractId(contractId).subscribe(date => {\n        expect(date).toBeNull();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}/start-date`);\n      expect(req.request.method).toBe('GET');\n      req.flush(null);\n    });\n  });\n  describe('getLatestEndDateByContractId', () => {\n    it('should return latest end date by contract id', () => {\n      const contractId = 1;\n      const endDate = new Date('2023-12-31');\n      service.getLatestEndDateByContractId(contractId).subscribe(date => {\n        expect(date).toEqual(endDate);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}/latest-end-date`);\n      expect(req.request.method).toBe('GET');\n      req.flush(endDate);\n    });\n    it('should handle null date', () => {\n      const contractId = 1;\n      service.getLatestEndDateByContractId(contractId).subscribe(date => {\n        expect(date).toBeNull();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}/latest-end-date`);\n      expect(req.request.method).toBe('GET');\n      req.flush(null);\n    });\n  });\n  describe('create', () => {\n    it('should create new contract values with audit record when user is logged in', () => {\n      const newContractValues = {\n        contractId: 1,\n        numericValue: 1000000,\n        startDate: '2023-01-01',\n        endDate: '2023-12-31',\n        subscriptionDate: '2023-01-01',\n        cdp: 12345,\n        cdpEntityId: 1,\n        madsValue: 0,\n        isOtherEntity: false,\n        cdpEntity: {\n          id: 1,\n          name: 'Test CDP Entity'\n        }\n      };\n      const mockResponse = {\n        id: 1,\n        ...newContractValues\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n      contractAuditStatusServiceSpy.getByName.and.returnValue(of(mockAuditStatus));\n      const mockAuditHistory = {\n        id: 1,\n        contractId: 1,\n        auditStatusId: 1,\n        auditDate: new Date(),\n        comment: 'Test comment',\n        auditorId: 1\n      };\n      contractAuditHistoryServiceSpy.create.and.returnValue(of(mockAuditHistory));\n      service.create(newContractValues).subscribe(values => {\n        expect(values).toEqual(mockResponse);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith('Adición de valores');\n        expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newContractValues);\n      req.flush(mockResponse);\n    });\n    it('should create new contract values without audit record when user is not logged in', () => {\n      const newContractValues = {\n        contractId: 1,\n        numericValue: 1000000,\n        startDate: '2023-01-01',\n        endDate: '2023-12-31',\n        subscriptionDate: '2023-01-01',\n        cdp: 12345,\n        cdpEntityId: 1,\n        madsValue: 0,\n        isOtherEntity: false,\n        cdpEntity: {\n          id: 1,\n          name: 'Test CDP Entity'\n        }\n      };\n      const mockResponse = {\n        id: 1,\n        ...newContractValues\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n      service.create(newContractValues).subscribe(values => {\n        expect(values).toEqual(mockResponse);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n        expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newContractValues);\n      req.flush(mockResponse);\n    });\n  });\n  describe('update', () => {\n    it('should update existing contract values with audit record when there are changes and user is logged in', () => {\n      const id = 1;\n      const originalValues = {\n        id: 1,\n        contractId: 1,\n        numericValue: 1000000,\n        startDate: '2023-01-01',\n        endDate: '2023-12-31',\n        subscriptionDate: '2023-01-01',\n        cdp: 12345,\n        cdpEntityId: 1,\n        madsValue: 0,\n        isOtherEntity: false,\n        cdpEntity: {\n          id: 1,\n          name: 'Test CDP Entity'\n        }\n      };\n      const updateValues = {\n        numericValue: 1500000,\n        endDate: '2024-06-30'\n      };\n      const mockResponse = {\n        ...originalValues,\n        ...updateValues\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n      contractAuditStatusServiceSpy.getByName.and.returnValue(of(mockAuditStatus));\n      const mockAuditHistory = {\n        id: 1,\n        contractId: 1,\n        auditorId: mockUser.id,\n        auditStatusId: mockAuditStatus.id,\n        contractAuditStatus: mockAuditStatus,\n        auditDate: new Date().toISOString(),\n        comment: 'Test comment'\n      };\n      contractAuditHistoryServiceSpy.create.and.returnValue(of(mockAuditHistory));\n      let updateResult;\n      service.update(id, updateValues).subscribe(result => {\n        updateResult = result;\n      });\n      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n      getReq.flush(originalValues);\n      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateValues);\n      putReq.flush(mockResponse);\n      expect(updateResult).toEqual(mockResponse);\n      expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith('Edición de valores');\n      expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();\n    });\n    it('should update existing contract values without audit record when there are no changes', () => {\n      const id = 1;\n      const originalValues = {\n        id: 1,\n        contractId: 1,\n        numericValue: 1000000,\n        startDate: '2023-01-01',\n        endDate: '2023-12-31',\n        subscriptionDate: '2023-01-01',\n        cdp: 12345,\n        cdpEntityId: 1,\n        madsValue: 0,\n        isOtherEntity: false,\n        cdpEntity: {\n          id: 1,\n          name: 'Test CDP Entity'\n        }\n      };\n      const updateValues = {\n        numericValue: 1000000,\n        startDate: '2023-01-01',\n        endDate: '2023-12-31',\n        subscriptionDate: '2023-01-01'\n      };\n      const mockResponse = {\n        ...originalValues\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n      let updateResult;\n      service.update(id, updateValues).subscribe(result => {\n        updateResult = result;\n      });\n      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n      getReq.flush(originalValues);\n      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateValues);\n      putReq.flush(mockResponse);\n      expect(updateResult).toEqual(mockResponse);\n      expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n      expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();\n    });\n    it('should update existing contract values without audit record when user is not logged in', () => {\n      const id = 1;\n      const originalValues = {\n        id: 1,\n        contractId: 1,\n        numericValue: 1000000,\n        startDate: '2023-01-01',\n        endDate: '2023-12-31',\n        subscriptionDate: '2023-01-01',\n        cdp: 12345,\n        cdpEntityId: 1,\n        madsValue: 0,\n        isOtherEntity: false,\n        cdpEntity: {\n          id: 1,\n          name: 'Test CDP Entity'\n        }\n      };\n      const updateValues = {\n        numericValue: 1500000,\n        endDate: '2024-06-30'\n      };\n      const mockResponse = {\n        ...originalValues,\n        ...updateValues\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n      let updateResult;\n      service.update(id, updateValues).subscribe(result => {\n        updateResult = result;\n      });\n      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n      getReq.flush(originalValues);\n      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateValues);\n      putReq.flush(mockResponse);\n      expect(updateResult).toEqual(mockResponse);\n      expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n      expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();\n    });\n  });\n  describe('delete', () => {\n    it('should delete contract values', () => {\n      const id = 1;\n      service.delete(id).subscribe(response => {\n        expect(response).toBeNull();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "ContractValuesService", "AuthService", "ContractAuditHistoryService", "ContractAuditStatusService", "of", "describe", "service", "httpMock", "authServiceSpy", "contractAuditHistoryServiceSpy", "contractAuditStatusServiceSpy", "apiUrl", "mockContractValues", "id", "contractId", "numericValue", "startDate", "endDate", "subscriptionDate", "cdp", "cdpEntityId", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isOtherEntity", "cdpEntity", "name", "mockUser", "username", "profiles", "mockAuditStatus", "description", "beforeEach", "jasmine", "createSpyObj", "configureTestingModule", "imports", "providers", "provide", "useValue", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockValues", "getAll", "subscribe", "values", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "getById", "getAllByContractId", "Date", "getStartDateByContractId", "date", "toBeNull", "getLatestEndDateByContractId", "newContractValues", "mockResponse", "getCurrentUser", "and", "returnValue", "getByName", "mockAuditHistory", "auditStatusId", "auditDate", "comment", "auditorId", "create", "toHaveBeenCalled", "toHaveBeenCalledWith", "body", "not", "originalValues", "updateValues", "contractAuditStatus", "toISOString", "updateResult", "update", "result", "getReq", "putReq", "delete", "response"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\services\\contract-values.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { ContractValues } from '@contract-management/models/contract-values.model';\nimport { environment } from '@env';\nimport { ContractValuesService } from './contract-values.service';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { ContractAuditHistoryService } from './contract-audit-history.service';\nimport { ContractAuditStatusService } from './contract-audit-status.service';\nimport { of } from 'rxjs';\nimport { User } from '@core/auth/models/user.model';\nimport { ContractAuditStatus } from '../models/contract-audit-status.model';\nimport { ContractAuditHistory } from '../models/contract-audit-history.model';\n\ndescribe('ContractValuesService', () => {\n  let service: ContractValuesService;\n  let httpMock: HttpTestingController;\n  let authServiceSpy: jasmine.SpyObj<AuthService>;\n  let contractAuditHistoryServiceSpy: jasmine.SpyObj<ContractAuditHistoryService>;\n  let contractAuditStatusServiceSpy: jasmine.SpyObj<ContractAuditStatusService>;\n  const apiUrl = `${environment.apiUrl}/contract-values`;\n\n  const mockContractValues: ContractValues = {\n    id: 1,\n    contractId: 1,\n    numericValue: 1000000,\n    startDate: '2023-01-01',\n    endDate: '2023-12-31',\n    subscriptionDate: '2023-01-01',\n    cdp: 12345,\n    cdpEntityId: 1,\n    madsValue: 0,\n    isOtherEntity: false,\n    cdpEntity: { id: 1, name: 'Test CDP Entity' },\n  };\n\n  const mockUser: User = {\n    id: 1,\n    username: 'testuser',\n    profiles: [],\n  };\n\n  const mockAuditStatus: ContractAuditStatus = {\n    id: 1,\n    name: 'Adición de valores',\n    description: 'Contract values creation status',\n  };\n\n  beforeEach(() => {\n    authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);\n    contractAuditHistoryServiceSpy = jasmine.createSpyObj(\n      'ContractAuditHistoryService',\n      ['create'],\n    );\n    contractAuditStatusServiceSpy = jasmine.createSpyObj(\n      'ContractAuditStatusService',\n      ['getByName'],\n    );\n\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [\n        ContractValuesService,\n        { provide: AuthService, useValue: authServiceSpy },\n        {\n          provide: ContractAuditHistoryService,\n          useValue: contractAuditHistoryServiceSpy,\n        },\n        {\n          provide: ContractAuditStatusService,\n          useValue: contractAuditStatusServiceSpy,\n        },\n      ],\n    });\n    service = TestBed.inject(ContractValuesService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all contract values', () => {\n      const mockValues: ContractValues[] = [mockContractValues];\n\n      service.getAll().subscribe((values) => {\n        expect(values).toEqual(mockValues);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockValues);\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a single contract values by id', () => {\n      service.getById(1).subscribe((values) => {\n        expect(values).toEqual(mockContractValues);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractValues);\n    });\n  });\n\n  describe('getAllByContractId', () => {\n    it('should return contract values by contract id', () => {\n      const contractId = 1;\n      const mockValues: ContractValues[] = [mockContractValues];\n\n      service.getAllByContractId(contractId).subscribe((values) => {\n        expect(values).toEqual(mockValues);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockValues);\n    });\n  });\n\n  describe('getStartDateByContractId', () => {\n    it('should return start date by contract id', () => {\n      const contractId = 1;\n      const startDate = new Date('2023-01-01');\n\n      service.getStartDateByContractId(contractId).subscribe((date) => {\n        expect(date).toEqual(startDate);\n      });\n\n      const req = httpMock.expectOne(\n        `${apiUrl}/contract/${contractId}/start-date`,\n      );\n      expect(req.request.method).toBe('GET');\n      req.flush(startDate);\n    });\n\n    it('should handle null date', () => {\n      const contractId = 1;\n\n      service.getStartDateByContractId(contractId).subscribe((date) => {\n        expect(date).toBeNull();\n      });\n\n      const req = httpMock.expectOne(\n        `${apiUrl}/contract/${contractId}/start-date`,\n      );\n      expect(req.request.method).toBe('GET');\n      req.flush(null);\n    });\n  });\n\n  describe('getLatestEndDateByContractId', () => {\n    it('should return latest end date by contract id', () => {\n      const contractId = 1;\n      const endDate = new Date('2023-12-31');\n\n      service.getLatestEndDateByContractId(contractId).subscribe((date) => {\n        expect(date).toEqual(endDate);\n      });\n\n      const req = httpMock.expectOne(\n        `${apiUrl}/contract/${contractId}/latest-end-date`,\n      );\n      expect(req.request.method).toBe('GET');\n      req.flush(endDate);\n    });\n\n    it('should handle null date', () => {\n      const contractId = 1;\n\n      service.getLatestEndDateByContractId(contractId).subscribe((date) => {\n        expect(date).toBeNull();\n      });\n\n      const req = httpMock.expectOne(\n        `${apiUrl}/contract/${contractId}/latest-end-date`,\n      );\n      expect(req.request.method).toBe('GET');\n      req.flush(null);\n    });\n  });\n\n  describe('create', () => {\n    it('should create new contract values with audit record when user is logged in', () => {\n      const newContractValues: Omit<ContractValues, 'id'> = {\n        contractId: 1,\n        numericValue: 1000000,\n        startDate: '2023-01-01',\n        endDate: '2023-12-31',\n        subscriptionDate: '2023-01-01',\n        cdp: 12345,\n        cdpEntityId: 1,\n        madsValue: 0,\n        isOtherEntity: false,\n        cdpEntity: { id: 1, name: 'Test CDP Entity' },\n      };\n\n      const mockResponse: ContractValues = {\n        id: 1,\n        ...newContractValues,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n      contractAuditStatusServiceSpy.getByName.and.returnValue(\n        of(mockAuditStatus),\n      );\n\n      const mockAuditHistory: ContractAuditHistory = {\n        id: 1,\n        contractId: 1,\n        auditStatusId: 1,\n        auditDate: new Date(),\n        comment: 'Test comment',\n        auditorId: 1,\n      };\n\n      contractAuditHistoryServiceSpy.create.and.returnValue(\n        of(mockAuditHistory),\n      );\n\n      service.create(newContractValues).subscribe((values) => {\n        expect(values).toEqual(mockResponse);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith(\n          'Adición de valores',\n        );\n        expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newContractValues);\n      req.flush(mockResponse);\n    });\n\n    it('should create new contract values without audit record when user is not logged in', () => {\n      const newContractValues: Omit<ContractValues, 'id'> = {\n        contractId: 1,\n        numericValue: 1000000,\n        startDate: '2023-01-01',\n        endDate: '2023-12-31',\n        subscriptionDate: '2023-01-01',\n        cdp: 12345,\n        cdpEntityId: 1,\n        madsValue: 0,\n        isOtherEntity: false,\n        cdpEntity: { id: 1, name: 'Test CDP Entity' },\n      };\n\n      const mockResponse: ContractValues = {\n        id: 1,\n        ...newContractValues,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n\n      service.create(newContractValues).subscribe((values) => {\n        expect(values).toEqual(mockResponse);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n        expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newContractValues);\n      req.flush(mockResponse);\n    });\n  });\n\n  describe('update', () => {\n    it('should update existing contract values with audit record when there are changes and user is logged in', () => {\n      const id = 1;\n      const originalValues: ContractValues = {\n        id: 1,\n        contractId: 1,\n        numericValue: 1000000,\n        startDate: '2023-01-01',\n        endDate: '2023-12-31',\n        subscriptionDate: '2023-01-01',\n        cdp: 12345,\n        cdpEntityId: 1,\n        madsValue: 0,\n        isOtherEntity: false,\n        cdpEntity: { id: 1, name: 'Test CDP Entity' },\n      };\n\n      const updateValues: Partial<ContractValues> = {\n        numericValue: 1500000,\n        endDate: '2024-06-30',\n      };\n\n      const mockResponse: ContractValues = {\n        ...originalValues,\n        ...updateValues,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n      contractAuditStatusServiceSpy.getByName.and.returnValue(\n        of(mockAuditStatus),\n      );\n\n      const mockAuditHistory: ContractAuditHistory = {\n        id: 1,\n        contractId: 1,\n        auditorId: mockUser.id,\n        auditStatusId: mockAuditStatus.id,\n        contractAuditStatus: mockAuditStatus,\n        auditDate: new Date().toISOString(),\n        comment: 'Test comment',\n      };\n\n      contractAuditHistoryServiceSpy.create.and.returnValue(\n        of(mockAuditHistory),\n      );\n\n      let updateResult: ContractValues | undefined;\n      service.update(id, updateValues).subscribe((result) => {\n        updateResult = result;\n      });\n\n      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n      getReq.flush(originalValues);\n\n      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateValues);\n      putReq.flush(mockResponse);\n\n      expect(updateResult).toEqual(mockResponse);\n      expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith(\n        'Edición de valores',\n      );\n      expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();\n    });\n\n    it('should update existing contract values without audit record when there are no changes', () => {\n      const id = 1;\n      const originalValues: ContractValues = {\n        id: 1,\n        contractId: 1,\n        numericValue: 1000000,\n        startDate: '2023-01-01',\n        endDate: '2023-12-31',\n        subscriptionDate: '2023-01-01',\n        cdp: 12345,\n        cdpEntityId: 1,\n        madsValue: 0,\n        isOtherEntity: false,\n        cdpEntity: { id: 1, name: 'Test CDP Entity' },\n      };\n\n      const updateValues: Partial<ContractValues> = {\n        numericValue: 1000000,\n        startDate: '2023-01-01',\n        endDate: '2023-12-31',\n        subscriptionDate: '2023-01-01',\n      };\n\n      const mockResponse: ContractValues = {\n        ...originalValues,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n\n      let updateResult: ContractValues | undefined;\n      service.update(id, updateValues).subscribe((result) => {\n        updateResult = result;\n      });\n\n      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n      getReq.flush(originalValues);\n\n      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateValues);\n      putReq.flush(mockResponse);\n\n      expect(updateResult).toEqual(mockResponse);\n      expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n      expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();\n    });\n\n    it('should update existing contract values without audit record when user is not logged in', () => {\n      const id = 1;\n      const originalValues: ContractValues = {\n        id: 1,\n        contractId: 1,\n        numericValue: 1000000,\n        startDate: '2023-01-01',\n        endDate: '2023-12-31',\n        subscriptionDate: '2023-01-01',\n        cdp: 12345,\n        cdpEntityId: 1,\n        madsValue: 0,\n        isOtherEntity: false,\n        cdpEntity: { id: 1, name: 'Test CDP Entity' },\n      };\n\n      const updateValues: Partial<ContractValues> = {\n        numericValue: 1500000,\n        endDate: '2024-06-30',\n      };\n\n      const mockResponse: ContractValues = {\n        ...originalValues,\n        ...updateValues,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n\n      let updateResult: ContractValues | undefined;\n      service.update(id, updateValues).subscribe((result) => {\n        updateResult = result;\n      });\n\n      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n      getReq.flush(originalValues);\n\n      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateValues);\n      putReq.flush(mockResponse);\n\n      expect(updateResult).toEqual(mockResponse);\n      expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n      expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete contract values', () => {\n      const id = 1;\n\n      service.delete(id).subscribe((response) => {\n        expect(response).toBeNull();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,0BAA0B,QAAQ,iCAAiC;AAC5E,SAASC,EAAE,QAAQ,MAAM;AAKzBC,QAAQ,CAAC,uBAAuB,EAAE,MAAK;EACrC,IAAIC,OAA8B;EAClC,IAAIC,QAA+B;EACnC,IAAIC,cAA2C;EAC/C,IAAIC,8BAA2E;EAC/E,IAAIC,6BAAyE;EAC7E,MAAMC,MAAM,GAAG,GAAGZ,WAAW,CAACY,MAAM,kBAAkB;EAEtD,MAAMC,kBAAkB,GAAmB;IACzCC,EAAE,EAAE,CAAC;IACLC,UAAU,EAAE,CAAC;IACbC,YAAY,EAAE,OAAO;IACrBC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,YAAY;IACrBC,gBAAgB,EAAE,YAAY;IAC9BC,GAAG,EAAE,KAAK;IACVC,WAAW,EAAE,CAAC;IACdC,SAAS,EAAE,CAAC;IACZC,aAAa,EAAE,KAAK;IACpBC,SAAS,EAAE;MAAEV,EAAE,EAAE,CAAC;MAAEW,IAAI,EAAE;IAAiB;GAC5C;EAED,MAAMC,QAAQ,GAAS;IACrBZ,EAAE,EAAE,CAAC;IACLa,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE;GACX;EAED,MAAMC,eAAe,GAAwB;IAC3Cf,EAAE,EAAE,CAAC;IACLW,IAAI,EAAE,oBAAoB;IAC1BK,WAAW,EAAE;GACd;EAEDC,UAAU,CAAC,MAAK;IACdtB,cAAc,GAAGuB,OAAO,CAACC,YAAY,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,CAAC;IACxEvB,8BAA8B,GAAGsB,OAAO,CAACC,YAAY,CACnD,6BAA6B,EAC7B,CAAC,QAAQ,CAAC,CACX;IACDtB,6BAA6B,GAAGqB,OAAO,CAACC,YAAY,CAClD,4BAA4B,EAC5B,CAAC,WAAW,CAAC,CACd;IAEDlC,OAAO,CAACmC,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACtC,uBAAuB,CAAC;MAClCuC,SAAS,EAAE,CACTnC,qBAAqB,EACrB;QAAEoC,OAAO,EAAEnC,WAAW;QAAEoC,QAAQ,EAAE7B;MAAc,CAAE,EAClD;QACE4B,OAAO,EAAElC,2BAA2B;QACpCmC,QAAQ,EAAE5B;OACX,EACD;QACE2B,OAAO,EAAEjC,0BAA0B;QACnCkC,QAAQ,EAAE3B;OACX;KAEJ,CAAC;IACFJ,OAAO,GAAGR,OAAO,CAACwC,MAAM,CAACtC,qBAAqB,CAAC;IAC/CO,QAAQ,GAAGT,OAAO,CAACwC,MAAM,CAACzC,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEF0C,SAAS,CAAC,MAAK;IACbhC,QAAQ,CAACiC,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACpC,OAAO,CAAC,CAACqC,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFtC,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBoC,EAAE,CAAC,mCAAmC,EAAE,MAAK;MAC3C,MAAMG,UAAU,GAAqB,CAAChC,kBAAkB,CAAC;MAEzDN,OAAO,CAACuC,MAAM,EAAE,CAACC,SAAS,CAAEC,MAAM,IAAI;QACpCL,MAAM,CAACK,MAAM,CAAC,CAACC,OAAO,CAACJ,UAAU,CAAC;MACpC,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAG1C,QAAQ,CAAC2C,SAAS,CAACvC,MAAM,CAAC;MACtC+B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,UAAU,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFvC,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBoC,EAAE,CAAC,8CAA8C,EAAE,MAAK;MACtDnC,OAAO,CAACiD,OAAO,CAAC,CAAC,CAAC,CAACT,SAAS,CAAEC,MAAM,IAAI;QACtCL,MAAM,CAACK,MAAM,CAAC,CAACC,OAAO,CAACpC,kBAAkB,CAAC;MAC5C,CAAC,CAAC;MAEF,MAAMqC,GAAG,GAAG1C,QAAQ,CAAC2C,SAAS,CAAC,GAAGvC,MAAM,IAAI,CAAC;MAC7C+B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAAC1C,kBAAkB,CAAC;IAC/B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFP,QAAQ,CAAC,oBAAoB,EAAE,MAAK;IAClCoC,EAAE,CAAC,8CAA8C,EAAE,MAAK;MACtD,MAAM3B,UAAU,GAAG,CAAC;MACpB,MAAM8B,UAAU,GAAqB,CAAChC,kBAAkB,CAAC;MAEzDN,OAAO,CAACkD,kBAAkB,CAAC1C,UAAU,CAAC,CAACgC,SAAS,CAAEC,MAAM,IAAI;QAC1DL,MAAM,CAACK,MAAM,CAAC,CAACC,OAAO,CAACJ,UAAU,CAAC;MACpC,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAG1C,QAAQ,CAAC2C,SAAS,CAAC,GAAGvC,MAAM,aAAaG,UAAU,EAAE,CAAC;MAClE4B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,UAAU,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFvC,QAAQ,CAAC,0BAA0B,EAAE,MAAK;IACxCoC,EAAE,CAAC,yCAAyC,EAAE,MAAK;MACjD,MAAM3B,UAAU,GAAG,CAAC;MACpB,MAAME,SAAS,GAAG,IAAIyC,IAAI,CAAC,YAAY,CAAC;MAExCnD,OAAO,CAACoD,wBAAwB,CAAC5C,UAAU,CAAC,CAACgC,SAAS,CAAEa,IAAI,IAAI;QAC9DjB,MAAM,CAACiB,IAAI,CAAC,CAACX,OAAO,CAAChC,SAAS,CAAC;MACjC,CAAC,CAAC;MAEF,MAAMiC,GAAG,GAAG1C,QAAQ,CAAC2C,SAAS,CAC5B,GAAGvC,MAAM,aAAaG,UAAU,aAAa,CAC9C;MACD4B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACtC,SAAS,CAAC;IACtB,CAAC,CAAC;IAEFyB,EAAE,CAAC,yBAAyB,EAAE,MAAK;MACjC,MAAM3B,UAAU,GAAG,CAAC;MAEpBR,OAAO,CAACoD,wBAAwB,CAAC5C,UAAU,CAAC,CAACgC,SAAS,CAAEa,IAAI,IAAI;QAC9DjB,MAAM,CAACiB,IAAI,CAAC,CAACC,QAAQ,EAAE;MACzB,CAAC,CAAC;MAEF,MAAMX,GAAG,GAAG1C,QAAQ,CAAC2C,SAAS,CAC5B,GAAGvC,MAAM,aAAaG,UAAU,aAAa,CAC9C;MACD4B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjD,QAAQ,CAAC,8BAA8B,EAAE,MAAK;IAC5CoC,EAAE,CAAC,8CAA8C,EAAE,MAAK;MACtD,MAAM3B,UAAU,GAAG,CAAC;MACpB,MAAMG,OAAO,GAAG,IAAIwC,IAAI,CAAC,YAAY,CAAC;MAEtCnD,OAAO,CAACuD,4BAA4B,CAAC/C,UAAU,CAAC,CAACgC,SAAS,CAAEa,IAAI,IAAI;QAClEjB,MAAM,CAACiB,IAAI,CAAC,CAACX,OAAO,CAAC/B,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,MAAMgC,GAAG,GAAG1C,QAAQ,CAAC2C,SAAS,CAC5B,GAAGvC,MAAM,aAAaG,UAAU,kBAAkB,CACnD;MACD4B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACrC,OAAO,CAAC;IACpB,CAAC,CAAC;IAEFwB,EAAE,CAAC,yBAAyB,EAAE,MAAK;MACjC,MAAM3B,UAAU,GAAG,CAAC;MAEpBR,OAAO,CAACuD,4BAA4B,CAAC/C,UAAU,CAAC,CAACgC,SAAS,CAAEa,IAAI,IAAI;QAClEjB,MAAM,CAACiB,IAAI,CAAC,CAACC,QAAQ,EAAE;MACzB,CAAC,CAAC;MAEF,MAAMX,GAAG,GAAG1C,QAAQ,CAAC2C,SAAS,CAC5B,GAAGvC,MAAM,aAAaG,UAAU,kBAAkB,CACnD;MACD4B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjD,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBoC,EAAE,CAAC,4EAA4E,EAAE,MAAK;MACpF,MAAMqB,iBAAiB,GAA+B;QACpDhD,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE,OAAO;QACrBC,SAAS,EAAE,YAAY;QACvBC,OAAO,EAAE,YAAY;QACrBC,gBAAgB,EAAE,YAAY;QAC9BC,GAAG,EAAE,KAAK;QACVC,WAAW,EAAE,CAAC;QACdC,SAAS,EAAE,CAAC;QACZC,aAAa,EAAE,KAAK;QACpBC,SAAS,EAAE;UAAEV,EAAE,EAAE,CAAC;UAAEW,IAAI,EAAE;QAAiB;OAC5C;MAED,MAAMuC,YAAY,GAAmB;QACnClD,EAAE,EAAE,CAAC;QACL,GAAGiD;OACJ;MAEDtD,cAAc,CAACwD,cAAc,CAACC,GAAG,CAACC,WAAW,CAACzC,QAAQ,CAAC;MACvDf,6BAA6B,CAACyD,SAAS,CAACF,GAAG,CAACC,WAAW,CACrD9D,EAAE,CAACwB,eAAe,CAAC,CACpB;MAED,MAAMwC,gBAAgB,GAAyB;QAC7CvD,EAAE,EAAE,CAAC;QACLC,UAAU,EAAE,CAAC;QACbuD,aAAa,EAAE,CAAC;QAChBC,SAAS,EAAE,IAAIb,IAAI,EAAE;QACrBc,OAAO,EAAE,cAAc;QACvBC,SAAS,EAAE;OACZ;MAED/D,8BAA8B,CAACgE,MAAM,CAACR,GAAG,CAACC,WAAW,CACnD9D,EAAE,CAACgE,gBAAgB,CAAC,CACrB;MAED9D,OAAO,CAACmE,MAAM,CAACX,iBAAiB,CAAC,CAAChB,SAAS,CAAEC,MAAM,IAAI;QACrDL,MAAM,CAACK,MAAM,CAAC,CAACC,OAAO,CAACe,YAAY,CAAC;QACpCrB,MAAM,CAAClC,cAAc,CAACwD,cAAc,CAAC,CAACU,gBAAgB,EAAE;QACxDhC,MAAM,CAAChC,6BAA6B,CAACyD,SAAS,CAAC,CAACQ,oBAAoB,CAClE,oBAAoB,CACrB;QACDjC,MAAM,CAACjC,8BAA8B,CAACgE,MAAM,CAAC,CAACC,gBAAgB,EAAE;MAClE,CAAC,CAAC;MAEF,MAAMzB,GAAG,GAAG1C,QAAQ,CAAC2C,SAAS,CAACvC,MAAM,CAAC;MACtC+B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACyB,IAAI,CAAC,CAAC5B,OAAO,CAACc,iBAAiB,CAAC;MACnDb,GAAG,CAACK,KAAK,CAACS,YAAY,CAAC;IACzB,CAAC,CAAC;IAEFtB,EAAE,CAAC,mFAAmF,EAAE,MAAK;MAC3F,MAAMqB,iBAAiB,GAA+B;QACpDhD,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE,OAAO;QACrBC,SAAS,EAAE,YAAY;QACvBC,OAAO,EAAE,YAAY;QACrBC,gBAAgB,EAAE,YAAY;QAC9BC,GAAG,EAAE,KAAK;QACVC,WAAW,EAAE,CAAC;QACdC,SAAS,EAAE,CAAC;QACZC,aAAa,EAAE,KAAK;QACpBC,SAAS,EAAE;UAAEV,EAAE,EAAE,CAAC;UAAEW,IAAI,EAAE;QAAiB;OAC5C;MAED,MAAMuC,YAAY,GAAmB;QACnClD,EAAE,EAAE,CAAC;QACL,GAAGiD;OACJ;MAEDtD,cAAc,CAACwD,cAAc,CAACC,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;MAEnD5D,OAAO,CAACmE,MAAM,CAACX,iBAAiB,CAAC,CAAChB,SAAS,CAAEC,MAAM,IAAI;QACrDL,MAAM,CAACK,MAAM,CAAC,CAACC,OAAO,CAACe,YAAY,CAAC;QACpCrB,MAAM,CAAClC,cAAc,CAACwD,cAAc,CAAC,CAACU,gBAAgB,EAAE;QACxDhC,MAAM,CAAChC,6BAA6B,CAACyD,SAAS,CAAC,CAACU,GAAG,CAACH,gBAAgB,EAAE;QACtEhC,MAAM,CAACjC,8BAA8B,CAACgE,MAAM,CAAC,CAACI,GAAG,CAACH,gBAAgB,EAAE;MACtE,CAAC,CAAC;MAEF,MAAMzB,GAAG,GAAG1C,QAAQ,CAAC2C,SAAS,CAACvC,MAAM,CAAC;MACtC+B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACyB,IAAI,CAAC,CAAC5B,OAAO,CAACc,iBAAiB,CAAC;MACnDb,GAAG,CAACK,KAAK,CAACS,YAAY,CAAC;IACzB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF1D,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBoC,EAAE,CAAC,uGAAuG,EAAE,MAAK;MAC/G,MAAM5B,EAAE,GAAG,CAAC;MACZ,MAAMiE,cAAc,GAAmB;QACrCjE,EAAE,EAAE,CAAC;QACLC,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE,OAAO;QACrBC,SAAS,EAAE,YAAY;QACvBC,OAAO,EAAE,YAAY;QACrBC,gBAAgB,EAAE,YAAY;QAC9BC,GAAG,EAAE,KAAK;QACVC,WAAW,EAAE,CAAC;QACdC,SAAS,EAAE,CAAC;QACZC,aAAa,EAAE,KAAK;QACpBC,SAAS,EAAE;UAAEV,EAAE,EAAE,CAAC;UAAEW,IAAI,EAAE;QAAiB;OAC5C;MAED,MAAMuD,YAAY,GAA4B;QAC5ChE,YAAY,EAAE,OAAO;QACrBE,OAAO,EAAE;OACV;MAED,MAAM8C,YAAY,GAAmB;QACnC,GAAGe,cAAc;QACjB,GAAGC;OACJ;MAEDvE,cAAc,CAACwD,cAAc,CAACC,GAAG,CAACC,WAAW,CAACzC,QAAQ,CAAC;MACvDf,6BAA6B,CAACyD,SAAS,CAACF,GAAG,CAACC,WAAW,CACrD9D,EAAE,CAACwB,eAAe,CAAC,CACpB;MAED,MAAMwC,gBAAgB,GAAyB;QAC7CvD,EAAE,EAAE,CAAC;QACLC,UAAU,EAAE,CAAC;QACb0D,SAAS,EAAE/C,QAAQ,CAACZ,EAAE;QACtBwD,aAAa,EAAEzC,eAAe,CAACf,EAAE;QACjCmE,mBAAmB,EAAEpD,eAAe;QACpC0C,SAAS,EAAE,IAAIb,IAAI,EAAE,CAACwB,WAAW,EAAE;QACnCV,OAAO,EAAE;OACV;MAED9D,8BAA8B,CAACgE,MAAM,CAACR,GAAG,CAACC,WAAW,CACnD9D,EAAE,CAACgE,gBAAgB,CAAC,CACrB;MAED,IAAIc,YAAwC;MAC5C5E,OAAO,CAAC6E,MAAM,CAACtE,EAAE,EAAEkE,YAAY,CAAC,CAACjC,SAAS,CAAEsC,MAAM,IAAI;QACpDF,YAAY,GAAGE,MAAM;MACvB,CAAC,CAAC;MAEF,MAAMC,MAAM,GAAG9E,QAAQ,CAAC2C,SAAS,CAAC,GAAGvC,MAAM,IAAIE,EAAE,EAAE,CAAC;MACpD6B,MAAM,CAAC2C,MAAM,CAAClC,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACzCgC,MAAM,CAAC/B,KAAK,CAACwB,cAAc,CAAC;MAE5B,MAAMQ,MAAM,GAAG/E,QAAQ,CAAC2C,SAAS,CAAC,GAAGvC,MAAM,IAAIE,EAAE,EAAE,CAAC;MACpD6B,MAAM,CAAC4C,MAAM,CAACnC,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACzCX,MAAM,CAAC4C,MAAM,CAACnC,OAAO,CAACyB,IAAI,CAAC,CAAC5B,OAAO,CAAC+B,YAAY,CAAC;MACjDO,MAAM,CAAChC,KAAK,CAACS,YAAY,CAAC;MAE1BrB,MAAM,CAACwC,YAAY,CAAC,CAAClC,OAAO,CAACe,YAAY,CAAC;MAC1CrB,MAAM,CAAChC,6BAA6B,CAACyD,SAAS,CAAC,CAACQ,oBAAoB,CAClE,oBAAoB,CACrB;MACDjC,MAAM,CAACjC,8BAA8B,CAACgE,MAAM,CAAC,CAACC,gBAAgB,EAAE;IAClE,CAAC,CAAC;IAEFjC,EAAE,CAAC,uFAAuF,EAAE,MAAK;MAC/F,MAAM5B,EAAE,GAAG,CAAC;MACZ,MAAMiE,cAAc,GAAmB;QACrCjE,EAAE,EAAE,CAAC;QACLC,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE,OAAO;QACrBC,SAAS,EAAE,YAAY;QACvBC,OAAO,EAAE,YAAY;QACrBC,gBAAgB,EAAE,YAAY;QAC9BC,GAAG,EAAE,KAAK;QACVC,WAAW,EAAE,CAAC;QACdC,SAAS,EAAE,CAAC;QACZC,aAAa,EAAE,KAAK;QACpBC,SAAS,EAAE;UAAEV,EAAE,EAAE,CAAC;UAAEW,IAAI,EAAE;QAAiB;OAC5C;MAED,MAAMuD,YAAY,GAA4B;QAC5ChE,YAAY,EAAE,OAAO;QACrBC,SAAS,EAAE,YAAY;QACvBC,OAAO,EAAE,YAAY;QACrBC,gBAAgB,EAAE;OACnB;MAED,MAAM6C,YAAY,GAAmB;QACnC,GAAGe;OACJ;MAEDtE,cAAc,CAACwD,cAAc,CAACC,GAAG,CAACC,WAAW,CAACzC,QAAQ,CAAC;MAEvD,IAAIyD,YAAwC;MAC5C5E,OAAO,CAAC6E,MAAM,CAACtE,EAAE,EAAEkE,YAAY,CAAC,CAACjC,SAAS,CAAEsC,MAAM,IAAI;QACpDF,YAAY,GAAGE,MAAM;MACvB,CAAC,CAAC;MAEF,MAAMC,MAAM,GAAG9E,QAAQ,CAAC2C,SAAS,CAAC,GAAGvC,MAAM,IAAIE,EAAE,EAAE,CAAC;MACpD6B,MAAM,CAAC2C,MAAM,CAAClC,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACzCgC,MAAM,CAAC/B,KAAK,CAACwB,cAAc,CAAC;MAE5B,MAAMQ,MAAM,GAAG/E,QAAQ,CAAC2C,SAAS,CAAC,GAAGvC,MAAM,IAAIE,EAAE,EAAE,CAAC;MACpD6B,MAAM,CAAC4C,MAAM,CAACnC,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACzCX,MAAM,CAAC4C,MAAM,CAACnC,OAAO,CAACyB,IAAI,CAAC,CAAC5B,OAAO,CAAC+B,YAAY,CAAC;MACjDO,MAAM,CAAChC,KAAK,CAACS,YAAY,CAAC;MAE1BrB,MAAM,CAACwC,YAAY,CAAC,CAAClC,OAAO,CAACe,YAAY,CAAC;MAC1CrB,MAAM,CAAChC,6BAA6B,CAACyD,SAAS,CAAC,CAACU,GAAG,CAACH,gBAAgB,EAAE;MACtEhC,MAAM,CAACjC,8BAA8B,CAACgE,MAAM,CAAC,CAACI,GAAG,CAACH,gBAAgB,EAAE;IACtE,CAAC,CAAC;IAEFjC,EAAE,CAAC,wFAAwF,EAAE,MAAK;MAChG,MAAM5B,EAAE,GAAG,CAAC;MACZ,MAAMiE,cAAc,GAAmB;QACrCjE,EAAE,EAAE,CAAC;QACLC,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE,OAAO;QACrBC,SAAS,EAAE,YAAY;QACvBC,OAAO,EAAE,YAAY;QACrBC,gBAAgB,EAAE,YAAY;QAC9BC,GAAG,EAAE,KAAK;QACVC,WAAW,EAAE,CAAC;QACdC,SAAS,EAAE,CAAC;QACZC,aAAa,EAAE,KAAK;QACpBC,SAAS,EAAE;UAAEV,EAAE,EAAE,CAAC;UAAEW,IAAI,EAAE;QAAiB;OAC5C;MAED,MAAMuD,YAAY,GAA4B;QAC5ChE,YAAY,EAAE,OAAO;QACrBE,OAAO,EAAE;OACV;MAED,MAAM8C,YAAY,GAAmB;QACnC,GAAGe,cAAc;QACjB,GAAGC;OACJ;MAEDvE,cAAc,CAACwD,cAAc,CAACC,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;MAEnD,IAAIgB,YAAwC;MAC5C5E,OAAO,CAAC6E,MAAM,CAACtE,EAAE,EAAEkE,YAAY,CAAC,CAACjC,SAAS,CAAEsC,MAAM,IAAI;QACpDF,YAAY,GAAGE,MAAM;MACvB,CAAC,CAAC;MAEF,MAAMC,MAAM,GAAG9E,QAAQ,CAAC2C,SAAS,CAAC,GAAGvC,MAAM,IAAIE,EAAE,EAAE,CAAC;MACpD6B,MAAM,CAAC2C,MAAM,CAAClC,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACzCgC,MAAM,CAAC/B,KAAK,CAACwB,cAAc,CAAC;MAE5B,MAAMQ,MAAM,GAAG/E,QAAQ,CAAC2C,SAAS,CAAC,GAAGvC,MAAM,IAAIE,EAAE,EAAE,CAAC;MACpD6B,MAAM,CAAC4C,MAAM,CAACnC,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACzCX,MAAM,CAAC4C,MAAM,CAACnC,OAAO,CAACyB,IAAI,CAAC,CAAC5B,OAAO,CAAC+B,YAAY,CAAC;MACjDO,MAAM,CAAChC,KAAK,CAACS,YAAY,CAAC;MAE1BrB,MAAM,CAACwC,YAAY,CAAC,CAAClC,OAAO,CAACe,YAAY,CAAC;MAC1CrB,MAAM,CAAChC,6BAA6B,CAACyD,SAAS,CAAC,CAACU,GAAG,CAACH,gBAAgB,EAAE;MACtEhC,MAAM,CAACjC,8BAA8B,CAACgE,MAAM,CAAC,CAACI,GAAG,CAACH,gBAAgB,EAAE;IACtE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFrE,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBoC,EAAE,CAAC,+BAA+B,EAAE,MAAK;MACvC,MAAM5B,EAAE,GAAG,CAAC;MAEZP,OAAO,CAACiF,MAAM,CAAC1E,EAAE,CAAC,CAACiC,SAAS,CAAE0C,QAAQ,IAAI;QACxC9C,MAAM,CAAC8C,QAAQ,CAAC,CAAC5B,QAAQ,EAAE;MAC7B,CAAC,CAAC;MAEF,MAAMX,GAAG,GAAG1C,QAAQ,CAAC2C,SAAS,CAAC,GAAGvC,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjD6B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}