{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { BankService } from './bank.service';\ndescribe('BankService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/banks`;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [BankService]\n    });\n    service = TestBed.inject(BankService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  const mockBank = {\n    id: 1,\n    name: 'Bancolombia'\n  };\n  describe('getAll', () => {\n    it('should return all banks', () => {\n      const mockBanks = [mockBank];\n      service.getAll().subscribe(banks => {\n        expect(banks).toEqual(mockBanks);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockBanks);\n    });\n    it('should handle error when getting all banks', () => {\n      service.getAll().subscribe({\n        error: error => {\n          expect(error.status).toBe(500);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Server error'), {\n        status: 500\n      });\n    });\n  });\n  describe('getById', () => {\n    it('should return a bank by id', () => {\n      service.getById(1).subscribe(bank => {\n        expect(bank).toEqual(mockBank);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockBank);\n    });\n    it('should handle error when getting bank by id', () => {\n      service.getById(999).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/999`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n  describe('create', () => {\n    const newBank = {\n      name: 'New Bank'\n    };\n    it('should create a new bank', () => {\n      service.create(newBank).subscribe(bank => {\n        expect(bank).toEqual(mockBank);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newBank);\n      req.flush(mockBank);\n    });\n    it('should handle error when creating bank', () => {\n      service.create(newBank).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n  describe('update', () => {\n    const updateData = {\n      name: 'Updated Name'\n    };\n    it('should update a bank', () => {\n      service.update(1, updateData).subscribe(bank => {\n        expect(bank).toEqual({\n          ...mockBank,\n          ...updateData\n        });\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush({\n        ...mockBank,\n        ...updateData\n      });\n    });\n    it('should handle error when updating bank', () => {\n      service.update(1, updateData).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n  describe('delete', () => {\n    it('should delete a bank', () => {\n      service.delete(1).subscribe(response => {\n        expect(response).toBeNull();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n    it('should handle error when deleting bank', () => {\n      service.delete(1).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n  describe('getByName', () => {\n    it('should return a bank by name', () => {\n      const name = 'Bancolombia';\n      service.getByName(name).subscribe(bank => {\n        expect(bank).toEqual(mockBank);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockBank);\n    });\n    it('should handle error when getting bank by name', () => {\n      const name = 'NonExistent';\n      service.getByName(name).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "BankService", "describe", "service", "httpMock", "apiUrl", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockBank", "id", "name", "mockBanks", "getAll", "subscribe", "banks", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "error", "status", "ErrorEvent", "getById", "bank", "newBank", "create", "body", "updateData", "update", "delete", "response", "toBeNull", "getByName"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\services\\bank.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { Bank } from '@contractor-dashboard/models/bank.model';\nimport { environment } from '@env';\nimport { BankService } from './bank.service';\n\ndescribe('BankService', () => {\n  let service: BankService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/banks`;\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [BankService],\n    });\n    service = TestBed.inject(BankService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  const mockBank: Bank = {\n    id: 1,\n    name: 'Bancolombia',\n  };\n\n  describe('getAll', () => {\n    it('should return all banks', () => {\n      const mockBanks = [mockBank];\n\n      service.getAll().subscribe((banks) => {\n        expect(banks).toEqual(mockBanks);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockBanks);\n    });\n\n    it('should handle error when getting all banks', () => {\n      service.getAll().subscribe({\n        error: (error) => {\n          expect(error.status).toBe(500);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Server error'), { status: 500 });\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a bank by id', () => {\n      service.getById(1).subscribe((bank) => {\n        expect(bank).toEqual(mockBank);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockBank);\n    });\n\n    it('should handle error when getting bank by id', () => {\n      service.getById(999).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/999`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n\n  describe('create', () => {\n    const newBank: Omit<Bank, 'id'> = {\n      name: 'New Bank',\n    };\n\n    it('should create a new bank', () => {\n      service.create(newBank).subscribe((bank) => {\n        expect(bank).toEqual(mockBank);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newBank);\n      req.flush(mockBank);\n    });\n\n    it('should handle error when creating bank', () => {\n      service.create(newBank).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n\n  describe('update', () => {\n    const updateData: Partial<Bank> = {\n      name: 'Updated Name',\n    };\n\n    it('should update a bank', () => {\n      service.update(1, updateData).subscribe((bank) => {\n        expect(bank).toEqual({ ...mockBank, ...updateData });\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush({ ...mockBank, ...updateData });\n    });\n\n    it('should handle error when updating bank', () => {\n      service.update(1, updateData).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete a bank', () => {\n      service.delete(1).subscribe((response) => {\n        expect(response).toBeNull();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n\n    it('should handle error when deleting bank', () => {\n      service.delete(1).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n\n  describe('getByName', () => {\n    it('should return a bank by name', () => {\n      const name = 'Bancolombia';\n\n      service.getByName(name).subscribe((bank) => {\n        expect(bank).toEqual(mockBank);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockBank);\n    });\n\n    it('should handle error when getting bank by name', () => {\n      const name = 'NonExistent';\n\n      service.getByName(name).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,WAAW,QAAQ,gBAAgB;AAE5CC,QAAQ,CAAC,aAAa,EAAE,MAAK;EAC3B,IAAIC,OAAoB;EACxB,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,QAAQ;EAE5CC,UAAU,CAAC,MAAK;IACdP,OAAO,CAACQ,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACX,uBAAuB,CAAC;MAClCY,SAAS,EAAE,CAACR,WAAW;KACxB,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACW,MAAM,CAACT,WAAW,CAAC;IACrCG,QAAQ,GAAGL,OAAO,CAACW,MAAM,CAACZ,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFa,SAAS,CAAC,MAAK;IACbP,QAAQ,CAACQ,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACX,OAAO,CAAC,CAACY,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEF,MAAMC,QAAQ,GAAS;IACrBC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE;GACP;EAEDhB,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBW,EAAE,CAAC,yBAAyB,EAAE,MAAK;MACjC,MAAMM,SAAS,GAAG,CAACH,QAAQ,CAAC;MAE5Bb,OAAO,CAACiB,MAAM,EAAE,CAACC,SAAS,CAAEC,KAAK,IAAI;QACnCR,MAAM,CAACQ,KAAK,CAAC,CAACC,OAAO,CAACJ,SAAS,CAAC;MAClC,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCS,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,SAAS,CAAC;IACtB,CAAC,CAAC;IAEFN,EAAE,CAAC,4CAA4C,EAAE,MAAK;MACpDV,OAAO,CAACiB,MAAM,EAAE,CAACC,SAAS,CAAC;QACzBS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,cAAc,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC5D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBW,EAAE,CAAC,4BAA4B,EAAE,MAAK;MACpCV,OAAO,CAAC8B,OAAO,CAAC,CAAC,CAAC,CAACZ,SAAS,CAAEa,IAAI,IAAI;QACpCpB,MAAM,CAACoB,IAAI,CAAC,CAACX,OAAO,CAACP,QAAQ,CAAC;MAChC,CAAC,CAAC;MAEF,MAAMQ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAI,CAAC;MAC7CS,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACb,QAAQ,CAAC;IACrB,CAAC,CAAC;IAEFH,EAAE,CAAC,6CAA6C,EAAE,MAAK;MACrDV,OAAO,CAAC8B,OAAO,CAAC,GAAG,CAAC,CAACZ,SAAS,CAAC;QAC7BS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,MAAM,CAAC;MAC/CmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB,MAAMiC,OAAO,GAAqB;MAChCjB,IAAI,EAAE;KACP;IAEDL,EAAE,CAAC,0BAA0B,EAAE,MAAK;MAClCV,OAAO,CAACiC,MAAM,CAACD,OAAO,CAAC,CAACd,SAAS,CAAEa,IAAI,IAAI;QACzCpB,MAAM,CAACoB,IAAI,CAAC,CAACX,OAAO,CAACP,QAAQ,CAAC;MAChC,CAAC,CAAC;MAEF,MAAMQ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCS,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCd,MAAM,CAACU,GAAG,CAACE,OAAO,CAACW,IAAI,CAAC,CAACd,OAAO,CAACY,OAAO,CAAC;MACzCX,GAAG,CAACK,KAAK,CAACb,QAAQ,CAAC;IACrB,CAAC,CAAC;IAEFH,EAAE,CAAC,wCAAwC,EAAE,MAAK;MAChDV,OAAO,CAACiC,MAAM,CAACD,OAAO,CAAC,CAACd,SAAS,CAAC;QAChCS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB,MAAMoC,UAAU,GAAkB;MAChCpB,IAAI,EAAE;KACP;IAEDL,EAAE,CAAC,sBAAsB,EAAE,MAAK;MAC9BV,OAAO,CAACoC,MAAM,CAAC,CAAC,EAAED,UAAU,CAAC,CAACjB,SAAS,CAAEa,IAAI,IAAI;QAC/CpB,MAAM,CAACoB,IAAI,CAAC,CAACX,OAAO,CAAC;UAAE,GAAGP,QAAQ;UAAE,GAAGsB;QAAU,CAAE,CAAC;MACtD,CAAC,CAAC;MAEF,MAAMd,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAI,CAAC;MAC7CS,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCd,MAAM,CAACU,GAAG,CAACE,OAAO,CAACW,IAAI,CAAC,CAACd,OAAO,CAACe,UAAU,CAAC;MAC5Cd,GAAG,CAACK,KAAK,CAAC;QAAE,GAAGb,QAAQ;QAAE,GAAGsB;MAAU,CAAE,CAAC;IAC3C,CAAC,CAAC;IAEFzB,EAAE,CAAC,wCAAwC,EAAE,MAAK;MAChDV,OAAO,CAACoC,MAAM,CAAC,CAAC,EAAED,UAAU,CAAC,CAACjB,SAAS,CAAC;QACtCS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAI,CAAC;MAC7CmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBW,EAAE,CAAC,sBAAsB,EAAE,MAAK;MAC9BV,OAAO,CAACqC,MAAM,CAAC,CAAC,CAAC,CAACnB,SAAS,CAAEoB,QAAQ,IAAI;QACvC3B,MAAM,CAAC2B,QAAQ,CAAC,CAACC,QAAQ,EAAE;MAC7B,CAAC,CAAC;MAEF,MAAMlB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAI,CAAC;MAC7CS,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;IAEFhB,EAAE,CAAC,wCAAwC,EAAE,MAAK;MAChDV,OAAO,CAACqC,MAAM,CAAC,CAAC,CAAC,CAACnB,SAAS,CAAC;QAC1BS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAI,CAAC;MAC7CmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,WAAW,EAAE,MAAK;IACzBW,EAAE,CAAC,8BAA8B,EAAE,MAAK;MACtC,MAAMK,IAAI,GAAG,aAAa;MAE1Bf,OAAO,CAACwC,SAAS,CAACzB,IAAI,CAAC,CAACG,SAAS,CAAEa,IAAI,IAAI;QACzCpB,MAAM,CAACoB,IAAI,CAAC,CAACX,OAAO,CAACP,QAAQ,CAAC;MAChC,CAAC,CAAC;MAEF,MAAMQ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,SAASa,IAAI,EAAE,CAAC;MACxDJ,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACb,QAAQ,CAAC;IACrB,CAAC,CAAC;IAEFH,EAAE,CAAC,+CAA+C,EAAE,MAAK;MACvD,MAAMK,IAAI,GAAG,aAAa;MAE1Bf,OAAO,CAACwC,SAAS,CAACzB,IAAI,CAAC,CAACG,SAAS,CAAC;QAChCS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,SAASa,IAAI,EAAE,CAAC;MACxDM,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}