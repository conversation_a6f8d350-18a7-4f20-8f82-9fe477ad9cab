{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/Github/SICMA/sicma-ui/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { ReductionsListComponent } from './reductions-list.component';\ndescribe('ReductionsListComponent', () => {\n  let component;\n  let fixture;\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    yield TestBed.configureTestingModule({\n      imports: [ReductionsListComponent]\n    }).compileComponents();\n    fixture = TestBed.createComponent(ReductionsListComponent);\n    component = fixture.componentInstance;\n    // Set required input\n    component.contract = {\n      id: 1,\n      contractNumber: 123,\n      monthlyPayment: 1000000,\n      object: 'Test Contract',\n      rup: true,\n      secopCode: 123456,\n      addition: false,\n      cession: false,\n      settled: false,\n      contractTypeId: 1,\n      statusId: 1,\n      causesSelectionId: 1,\n      managementSupportId: 1,\n      contractClassId: 1,\n      reduction: []\n    };\n    fixture.detectChanges();\n  }));\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "ReductionsListComponent", "describe", "component", "fixture", "beforeEach", "_asyncToGenerator", "configureTestingModule", "imports", "compileComponents", "createComponent", "componentInstance", "contract", "id", "contractNumber", "monthlyPayment", "object", "rup", "secopCode", "addition", "cession", "settled", "contractTypeId", "statusId", "causesSelectionId", "managementSupportId", "contractClassId", "reduction", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\components\\reductions-list\\reductions-list.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\n\nimport { ReductionsListComponent } from './reductions-list.component';\n\ndescribe('ReductionsListComponent', () => {\n  let component: ReductionsListComponent;\n  let fixture: ComponentFixture<ReductionsListComponent>;\n\n  beforeEach(async () => {\n    await TestBed.configureTestingModule({\n      imports: [ReductionsListComponent],\n    }).compileComponents();\n\n    fixture = TestBed.createComponent(ReductionsListComponent);\n    component = fixture.componentInstance;\n\n    // Set required input\n    component.contract = {\n      id: 1,\n      contractNumber: 123,\n      monthlyPayment: 1000000,\n      object: 'Test Contract',\n      rup: true,\n      secopCode: 123456,\n      addition: false,\n      cession: false,\n      settled: false,\n      contractTypeId: 1,\n      statusId: 1,\n      causesSelectionId: 1,\n      managementSupportId: 1,\n      contractClassId: 1,\n      reduction: [],\n    };\n\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});\n"], "mappings": ";AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AAEjE,SAASC,uBAAuB,QAAQ,6BAA6B;AAErEC,QAAQ,CAAC,yBAAyB,EAAE,MAAK;EACvC,IAAIC,SAAkC;EACtC,IAAIC,OAAkD;EAEtDC,UAAU,eAAAC,iBAAA,CAAC,aAAW;IACpB,MAAMN,OAAO,CAACO,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CAACP,uBAAuB;KAClC,CAAC,CAACQ,iBAAiB,EAAE;IAEtBL,OAAO,GAAGJ,OAAO,CAACU,eAAe,CAACT,uBAAuB,CAAC;IAC1DE,SAAS,GAAGC,OAAO,CAACO,iBAAiB;IAErC;IACAR,SAAS,CAACS,QAAQ,GAAG;MACnBC,EAAE,EAAE,CAAC;MACLC,cAAc,EAAE,GAAG;MACnBC,cAAc,EAAE,OAAO;MACvBC,MAAM,EAAE,eAAe;MACvBC,GAAG,EAAE,IAAI;MACTC,SAAS,EAAE,MAAM;MACjBC,QAAQ,EAAE,KAAK;MACfC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,KAAK;MACdC,cAAc,EAAE,CAAC;MACjBC,QAAQ,EAAE,CAAC;MACXC,iBAAiB,EAAE,CAAC;MACpBC,mBAAmB,EAAE,CAAC;MACtBC,eAAe,EAAE,CAAC;MAClBC,SAAS,EAAE;KACZ;IAEDvB,OAAO,CAACwB,aAAa,EAAE;EACzB,CAAC,EAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAAC3B,SAAS,CAAC,CAAC4B,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}