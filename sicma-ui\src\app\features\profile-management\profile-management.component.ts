import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { NgxSpinnerService } from 'ngx-spinner';
import { forkJoin } from 'rxjs';
import { finalize } from 'rxjs/operators';

import { AlertService } from '@shared/services/alert.service';
import { UserDialogComponent } from './components/user-dialog/user-dialog.component';
import { Profile, UserProfileAssignment } from './models/profile.model';
import { ProfileService } from './services/profile.service';

@Component({
  selector: 'app-profile-management',
  templateUrl: './profile-management.component.html',
  styleUrl: './profile-management.component.scss',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatChipsModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatSelectModule,
    MatInputModule,
    MatPaginatorModule,
    MatSortModule,
    FormsModule,
  ],
})
export class ProfileManagementComponent implements OnInit, AfterViewInit {
  displayedColumns: string[] = ['username', 'profiles', 'actions'];
  dataSource = new MatTableDataSource<UserProfileAssignment>();
  allProfiles: Profile[] = [];

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private profileService: ProfileService,
    private alert: AlertService,
    private spinner: NgxSpinnerService,
    private dialog: MatDialog,
  ) {}

  ngOnInit(): void {
    this.loadData();
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    this.dataSource.paginator?.firstPage();
  }

  private loadData(): void {
    this.spinner.show();
    forkJoin({
      profiles: this.profileService.getAllProfiles(),
      users: this.profileService.getUsersWithProfiles(),
    })
      .pipe(finalize(() => this.spinner.hide()))
      .subscribe({
        next: ({ profiles, users }) => {
          this.allProfiles = profiles;
          this.dataSource.data = users;
        },
        error: (error) => {
          this.alert.error(error.error?.detail ?? 'Error al cargar los datos');
        },
      });
  }

  assignProfile(userId: number, profileId: number): void {
    this.spinner.show();
    this.profileService
      .assignProfile({ user_id: userId, profile_id: profileId })
      .pipe(finalize(() => this.spinner.hide()))
      .subscribe({
        next: () => {
          this.alert.success('Perfil asignado correctamente');
          this.loadData();
        },
        error: (error) => {
          this.alert.error(error.error?.detail ?? 'Error al asignar el perfil');
        },
      });
  }

  removeProfile(userId: number, profileId: number): void {
    const user = this.dataSource.data.find((u) => u.userId === userId);
    if (!user) return;

    if (user.profiles.length <= 1) {
      this.alert.warning('El usuario debe tener al menos un perfil');
      return;
    }

    this.spinner.show();
    this.profileService
      .removeProfile({ user_id: userId, profile_id: profileId })
      .pipe(finalize(() => this.spinner.hide()))
      .subscribe({
        next: () => {
          this.alert.success('Perfil removido correctamente');
          this.loadData();
        },
        error: (error) => {
          this.alert.error(error.error?.detail ?? 'Error al remover el perfil');
        },
      });
  }

  getAvailableProfiles(user: UserProfileAssignment): Profile[] {
    return this.allProfiles.filter(
      (profile) => !user.profiles.some((p) => p.id === profile.id),
    );
  }

  openUserDialog(): void {
    this.dialog
      .open(UserDialogComponent, {
        width: '95%',
        maxWidth: '600px',
        height: 'auto',
        maxHeight: '90vh',
      })
      .afterClosed()
      .subscribe((result?: UserProfileAssignment) => {
        if (!result) return;
        this.loadData();
      });
  }
}
