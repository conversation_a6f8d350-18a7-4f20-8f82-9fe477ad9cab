{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/Github/SICMA/sicma-ui/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { FormControl, FormGroup } from '@angular/forms';\nimport { provideNativeDateAdapter } from '@angular/material/core';\nimport { MatDialog, MatDialogModule } from '@angular/material/dialog';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { ActivatedRoute } from '@angular/router';\nimport { RouterTestingModule } from '@angular/router/testing';\nimport { ContractService } from '@contract-management/services/contract.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { of, throwError } from 'rxjs';\nimport { ContractDetailPageComponent } from './contract-detail-page.component';\nimport { NO_ERRORS_SCHEMA } from '@angular/core';\ndescribe('ContractDetailPageComponent', () => {\n  let component;\n  let fixture;\n  let contractServiceSpy;\n  let alertServiceSpy;\n  let spinnerServiceSpy;\n  let dialogSpy;\n  const mockContract = {\n    id: 1,\n    contractNumber: 123,\n    object: 'Test Contract',\n    rup: false,\n    sigepLink: '',\n    secopLink: '',\n    addition: false,\n    cession: false,\n    settled: false,\n    monthlyPayment: 1000,\n    secopCode: 123,\n    selectionModalityId: 1,\n    trackingTypeId: 1,\n    contractTypeId: 1,\n    statusId: 1,\n    dependencyId: 1,\n    groupId: 1,\n    municipalityId: 1,\n    departmentId: 1,\n    causesSelectionId: 1,\n    managementSupportId: 1,\n    contractClassId: 1,\n    contractYearId: 2024,\n    earlyTermination: false\n  };\n  const mockContractorContract = {\n    id: 2,\n    contractNumber: 123,\n    contractorId: 3,\n    fullName: 'Test Contractor',\n    contractorIdNumber: 12345,\n    object: 'Test Contract',\n    rup: true,\n    addition: false,\n    cession: false,\n    settled: false,\n    hasCcp: false,\n    selectionModalityName: 'Test Modality',\n    trackingTypeName: 'Test Tracking',\n    contractTypeName: 'Test Type',\n    statusName: 'Active',\n    dependencyName: 'Test Dependency',\n    groupName: 'Test Group',\n    contractorEmail: '<EMAIL>',\n    monthlyPayment: 1000,\n    supervisorFullName: 'Test Supervisor',\n    supervisorIdNumber: '98765',\n    supervisorPosition: 'Test Position'\n  };\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    contractServiceSpy = jasmine.createSpyObj('ContractService', ['getById', 'getDetailsById']);\n    alertServiceSpy = jasmine.createSpyObj('AlertService', ['error', 'warning']);\n    spinnerServiceSpy = jasmine.createSpyObj('NgxSpinnerService', ['show', 'hide']);\n    dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);\n    yield TestBed.configureTestingModule({\n      imports: [ContractDetailPageComponent, HttpClientTestingModule, BrowserAnimationsModule, RouterTestingModule, MatDialogModule],\n      providers: [provideNativeDateAdapter(), {\n        provide: ActivatedRoute,\n        useValue: {\n          params: of({\n            id: '1'\n          })\n        }\n      }, {\n        provide: ContractService,\n        useValue: contractServiceSpy\n      }, {\n        provide: AlertService,\n        useValue: alertServiceSpy\n      }, {\n        provide: NgxSpinnerService,\n        useValue: spinnerServiceSpy\n      }, {\n        provide: MatDialog,\n        useValue: dialogSpy\n      }],\n      schemas: [NO_ERRORS_SCHEMA]\n    }).compileComponents();\n    fixture = TestBed.createComponent(ContractDetailPageComponent);\n    component = fixture.componentInstance;\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(ContractDetailPageComponent);\n    component = fixture.componentInstance;\n    contractServiceSpy.getById.and.returnValue(of(mockContract));\n    contractServiceSpy.getDetailsById.and.returnValue(of(mockContractorContract));\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should load contract on init', () => {\n    expect(spinnerServiceSpy.show).toHaveBeenCalled();\n    expect(contractServiceSpy.getById).toHaveBeenCalledWith(1);\n    expect(component.contract).toEqual(mockContract);\n    expect(spinnerServiceSpy.hide).toHaveBeenCalled();\n  });\n  it('should show error if contract fails to load', () => {\n    contractServiceSpy.getById.and.returnValue(throwError(() => new Error('Failed to load')));\n    component.ngOnInit();\n    expect(alertServiceSpy.error).toHaveBeenCalledWith('Error al cargar el contrato');\n    expect(spinnerServiceSpy.hide).toHaveBeenCalled();\n  });\n  describe('openEarlyTerminationDialog', () => {\n    it('should show warning if no contracts or no contractors associated', () => {\n      component.contract = null;\n      component.openEarlyTerminationDialog();\n      expect(alertServiceSpy.warning).not.toHaveBeenCalled();\n      expect(dialogSpy.open).not.toHaveBeenCalled();\n      component.contract = mockContract;\n      component.AssociatedContractorsListComponent = undefined;\n      component.openEarlyTerminationDialog();\n      expect(alertServiceSpy.warning).not.toHaveBeenCalled();\n      expect(dialogSpy.open).not.toHaveBeenCalled();\n      component.contract = mockContract;\n      component.AssociatedContractorsListComponent = {\n        getLatestContractorContract: () => null\n      };\n      component.openEarlyTerminationDialog();\n      expect(alertServiceSpy.warning).toHaveBeenCalledWith('No hay contratistas asociados a este contrato');\n    });\n  });\n  describe('dialog interactions', () => {\n    it('should handle dialog result correctly', () => {\n      component.contract = mockContract;\n      component.AssociatedContractorsListComponent = {\n        getLatestContractorContract: () => mockContractorContract,\n        updateContractorContracts: jasmine.createSpy('updateContractorContracts')\n      };\n      const earlyTerminationControl = new FormControl(true);\n      component.contractDetailForm = {\n        contractForm: new FormGroup({\n          earlyTermination: earlyTerminationControl\n        })\n      };\n      const dialogResult = {\n        endDate: '2023-12-31'\n      };\n      const handleDialogResult = result => {\n        if (result) {\n          if (component.contractDetailForm) {\n            component.contractDetailForm.contractForm.get('earlyTermination')?.disable();\n          }\n          if (component.AssociatedContractorsListComponent && mockContractorContract.id) {\n            component.AssociatedContractorsListComponent.updateContractorContracts(result.endDate, mockContractorContract.id);\n          }\n        } else {\n          if (component.contractDetailForm) {\n            component.contractDetailForm.contractForm.get('earlyTermination')?.setValue(false);\n          }\n        }\n      };\n      handleDialogResult(dialogResult);\n      expect(earlyTerminationControl.disabled).toBeTrue();\n      expect(component.AssociatedContractorsListComponent.updateContractorContracts).toHaveBeenCalledWith('2023-12-31', mockContractorContract.id);\n      earlyTerminationControl.enable();\n      earlyTerminationControl.setValue(true);\n      handleDialogResult(null);\n      expect(earlyTerminationControl.value).toBeFalse();\n      expect(earlyTerminationControl.enabled).toBeTrue();\n    });\n  });\n  describe('onContractorContractsChanged', () => {\n    it('should update contract cession property', () => {\n      component.contract = {\n        ...mockContract,\n        cession: false\n      };\n      component.onContractorContractsChanged();\n      expect(component.contract.cession).toBeTrue();\n    });\n    it('should not throw error if contract is null', () => {\n      component.contract = null;\n      expect(() => component.onContractorContractsChanged()).not.toThrow();\n    });\n    it('should update contract form with cession value', () => {\n      component.contract = mockContract;\n      component.contractDetailForm = {\n        contractForm: new FormGroup({\n          cession: new FormControl(false)\n        })\n      };\n      component.onContractorContractsChanged();\n      expect(component.contractDetailForm.contractForm.get('cession')?.value).toBeTrue();\n    });\n    it('should not throw error if contractDetailForm is null', () => {\n      component.contract = mockContract;\n      component.contractDetailForm = undefined;\n      expect(() => component.onContractorContractsChanged()).not.toThrow();\n    });\n  });\n  describe('uncheckEarlyTermination', () => {\n    it('should uncheck and enable early termination', () => {\n      const earlyTerminationControl = new FormControl(true);\n      component.contractDetailForm = {\n        contractForm: new FormGroup({\n          earlyTermination: earlyTerminationControl\n        })\n      };\n      component.uncheckEarlyTermination();\n      expect(earlyTerminationControl.value).toBeFalse();\n      expect(earlyTerminationControl.enabled).toBeTrue();\n    });\n    it('should not throw error if contractDetailForm is null', () => {\n      component.contractDetailForm = undefined;\n      expect(() => component.uncheckEarlyTermination()).not.toThrow();\n    });\n    it('should not throw error if earlyTermination control is missing', () => {\n      component.contractDetailForm = {\n        contractForm: new FormGroup({})\n      };\n      expect(() => component.uncheckEarlyTermination()).not.toThrow();\n    });\n  });\n  describe('ViewChild components', () => {\n    it('should set ViewChild components after view initialization', () => {\n      const mockAssociatedContractorsListComponent = {};\n      const mockContractDetailForm = {};\n      const mockContractorDetailForm = {};\n      component.AssociatedContractorsListComponent = mockAssociatedContractorsListComponent;\n      component.contractDetailForm = mockContractDetailForm;\n      component.contractorDetailForm = mockContractorDetailForm;\n      expect(component.AssociatedContractorsListComponent).toBe(mockAssociatedContractorsListComponent);\n      expect(component.contractDetailForm).toBe(mockContractDetailForm);\n      expect(component.contractorDetailForm).toBe(mockContractorDetailForm);\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "TestBed", "FormControl", "FormGroup", "provideNativeDateAdapter", "MatDialog", "MatDialogModule", "BrowserAnimationsModule", "ActivatedRoute", "RouterTestingModule", "ContractService", "AlertService", "NgxSpinnerService", "of", "throwError", "ContractDetailPageComponent", "NO_ERRORS_SCHEMA", "describe", "component", "fixture", "contractServiceSpy", "alertServiceSpy", "spinnerServiceSpy", "dialogSpy", "mockContract", "id", "contractNumber", "object", "rup", "sigepLink", "secopLink", "addition", "cession", "settled", "monthlyPayment", "secopCode", "selectionModalityId", "trackingTypeId", "contractTypeId", "statusId", "dependencyId", "groupId", "municipalityId", "departmentId", "causesSelectionId", "managementSupportId", "contractClassId", "contractYearId", "earlyTermination", "mockContractorContract", "contractorId", "fullName", "contractorIdNumber", "hasCcp", "selectionModalityName", "trackingTypeName", "contractTypeName", "statusName", "dependencyName", "groupName", "contractorEmail", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supervisorIdNumber", "supervisorPosition", "beforeEach", "_asyncToGenerator", "jasmine", "createSpyObj", "configureTestingModule", "imports", "providers", "provide", "useValue", "params", "schemas", "compileComponents", "createComponent", "componentInstance", "getById", "and", "returnValue", "getDetailsById", "detectChanges", "it", "expect", "toBeTruthy", "show", "toHaveBeenCalled", "toHaveBeenCalledWith", "contract", "toEqual", "hide", "Error", "ngOnInit", "error", "openEarlyTerminationDialog", "warning", "not", "open", "AssociatedContractorsListComponent", "undefined", "getLatestContractorContract", "updateContractorContracts", "createSpy", "earlyTerminationControl", "contractDetailForm", "contractForm", "dialogResult", "endDate", "handleDialogResult", "result", "get", "disable", "setValue", "disabled", "toBeTrue", "enable", "value", "toBeFalse", "enabled", "onContractorContractsChanged", "toThrow", "uncheckEarlyTermination", "mockAssociatedContractorsListComponent", "mockContractDetailForm", "mockContractorDetailForm", "contractorDetailForm", "toBe"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\pages\\contract-detail-page\\contract-detail-page.component.spec.ts"], "sourcesContent": ["import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { ComponentFixture, TestBed } from '@angular/core/testing';\nimport { FormControl, FormGroup } from '@angular/forms';\nimport { provideNativeDateAdapter } from '@angular/material/core';\nimport { MatDialog, MatDialogModule } from '@angular/material/dialog';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { ActivatedRoute } from '@angular/router';\nimport { RouterTestingModule } from '@angular/router/testing';\nimport { AssociatedContractorsListComponent } from '@contract-management/components/associated-contractors-list/associated-contractors-list.component';\nimport { ContractDetailFormComponent } from '@contract-management/components/contract-detail-form/contract-detail-form.component';\nimport { Contract } from '@contract-management/models/contract.model';\nimport { ContractDetails } from '@contract-management/models/contract-details.model';\nimport { ContractService } from '@contract-management/services/contract.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { of, throwError } from 'rxjs';\nimport { ContractDetailPageComponent } from './contract-detail-page.component';\nimport { ContractorDetailFormComponent } from '@contract-management/components/contractor-detail-form/contractor-detail-form.component';\nimport { NO_ERRORS_SCHEMA } from '@angular/core';\n\ninterface DialogResult {\n  endDate: string;\n}\n\ndescribe('ContractDetailPageComponent', () => {\n  let component: ContractDetailPageComponent;\n  let fixture: ComponentFixture<ContractDetailPageComponent>;\n  let contractServiceSpy: jasmine.SpyObj<ContractService>;\n  let alertServiceSpy: jasmine.SpyObj<AlertService>;\n  let spinnerServiceSpy: jasmine.SpyObj<NgxSpinnerService>;\n  let dialogSpy: jasmine.SpyObj<MatDialog>;\n\n  const mockContract: Contract = {\n    id: 1,\n    contractNumber: 123,\n    object: 'Test Contract',\n    rup: false,\n    sigepLink: '',\n    secopLink: '',\n    addition: false,\n    cession: false,\n    settled: false,\n    monthlyPayment: 1000,\n    secopCode: 123,\n    selectionModalityId: 1,\n    trackingTypeId: 1,\n    contractTypeId: 1,\n    statusId: 1,\n    dependencyId: 1,\n    groupId: 1,\n    municipalityId: 1,\n    departmentId: 1,\n    causesSelectionId: 1,\n    managementSupportId: 1,\n    contractClassId: 1,\n    contractYearId: 2024,\n    earlyTermination: false,\n  };\n\n  const mockContractorContract: ContractDetails = {\n    id: 2,\n    contractNumber: 123,\n    contractorId: 3,\n    fullName: 'Test Contractor',\n    contractorIdNumber: 12345,\n    object: 'Test Contract',\n    rup: true,\n    addition: false,\n    cession: false,\n    settled: false,\n    hasCcp: false,\n    selectionModalityName: 'Test Modality',\n    trackingTypeName: 'Test Tracking',\n    contractTypeName: 'Test Type',\n    statusName: 'Active',\n    dependencyName: 'Test Dependency',\n    groupName: 'Test Group',\n    contractorEmail: '<EMAIL>',\n    monthlyPayment: 1000,\n    supervisorFullName: 'Test Supervisor',\n    supervisorIdNumber: '98765',\n    supervisorPosition: 'Test Position',\n  };\n\n  beforeEach(async () => {\n    contractServiceSpy = jasmine.createSpyObj('ContractService', [\n      'getById',\n      'getDetailsById',\n    ]);\n    alertServiceSpy = jasmine.createSpyObj('AlertService', [\n      'error',\n      'warning',\n    ]);\n    spinnerServiceSpy = jasmine.createSpyObj('NgxSpinnerService', [\n      'show',\n      'hide',\n    ]);\n    dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);\n\n    await TestBed.configureTestingModule({\n      imports: [\n        ContractDetailPageComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n        RouterTestingModule,\n        MatDialogModule,\n      ],\n      providers: [\n        provideNativeDateAdapter(),\n        {\n          provide: ActivatedRoute,\n          useValue: { params: of({ id: '1' }) },\n        },\n        { provide: ContractService, useValue: contractServiceSpy },\n        { provide: AlertService, useValue: alertServiceSpy },\n        { provide: NgxSpinnerService, useValue: spinnerServiceSpy },\n        { provide: MatDialog, useValue: dialogSpy },\n      ],\n      schemas: [NO_ERRORS_SCHEMA],\n    }).compileComponents();\n\n    fixture = TestBed.createComponent(ContractDetailPageComponent);\n    component = fixture.componentInstance;\n  });\n\n  beforeEach(() => {\n    fixture = TestBed.createComponent(ContractDetailPageComponent);\n    component = fixture.componentInstance;\n    contractServiceSpy.getById.and.returnValue(of(mockContract));\n    contractServiceSpy.getDetailsById.and.returnValue(\n      of(mockContractorContract),\n    );\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should load contract on init', () => {\n    expect(spinnerServiceSpy.show).toHaveBeenCalled();\n    expect(contractServiceSpy.getById).toHaveBeenCalledWith(1);\n    expect(component.contract).toEqual(mockContract);\n    expect(spinnerServiceSpy.hide).toHaveBeenCalled();\n  });\n\n  it('should show error if contract fails to load', () => {\n    contractServiceSpy.getById.and.returnValue(\n      throwError(() => new Error('Failed to load')),\n    );\n    component.ngOnInit();\n    expect(alertServiceSpy.error).toHaveBeenCalledWith(\n      'Error al cargar el contrato',\n    );\n    expect(spinnerServiceSpy.hide).toHaveBeenCalled();\n  });\n\n  describe('openEarlyTerminationDialog', () => {\n    it('should show warning if no contracts or no contractors associated', () => {\n\n      component.contract = null;\n      component.openEarlyTerminationDialog();\n      expect(alertServiceSpy.warning).not.toHaveBeenCalled();\n      expect(dialogSpy.open).not.toHaveBeenCalled();\n\n      component.contract = mockContract;\n      component.AssociatedContractorsListComponent =\n        undefined as unknown as AssociatedContractorsListComponent;\n      component.openEarlyTerminationDialog();\n      expect(alertServiceSpy.warning).not.toHaveBeenCalled();\n      expect(dialogSpy.open).not.toHaveBeenCalled();\n\n      component.contract = mockContract;\n      component.AssociatedContractorsListComponent = {\n        getLatestContractorContract: () => null,\n      } as unknown as AssociatedContractorsListComponent;\n      component.openEarlyTerminationDialog();\n      expect(alertServiceSpy.warning).toHaveBeenCalledWith(\n        'No hay contratistas asociados a este contrato',\n      );\n    });\n  });\n\n  describe('dialog interactions', () => {\n    it('should handle dialog result correctly', () => {\n\n      component.contract = mockContract;\n\n      component.AssociatedContractorsListComponent = {\n        getLatestContractorContract: () => mockContractorContract,\n        updateContractorContracts: jasmine.createSpy(\n          'updateContractorContracts',\n        ),\n      } as unknown as AssociatedContractorsListComponent;\n\n      const earlyTerminationControl = new FormControl(true);\n      component.contractDetailForm = {\n        contractForm: new FormGroup({\n          earlyTermination: earlyTerminationControl,\n        }),\n      } as ContractDetailFormComponent;\n\n      const dialogResult: DialogResult = { endDate: '2023-12-31' };\n\n      const handleDialogResult = (result: DialogResult | null) => {\n        if (result) {\n          if (component.contractDetailForm) {\n            component.contractDetailForm.contractForm\n              .get('earlyTermination')\n              ?.disable();\n          }\n          if (\n            component.AssociatedContractorsListComponent &&\n            mockContractorContract.id\n          ) {\n            component.AssociatedContractorsListComponent.updateContractorContracts(\n              result.endDate,\n              mockContractorContract.id,\n            );\n          }\n        } else {\n          if (component.contractDetailForm) {\n            component.contractDetailForm.contractForm\n              .get('earlyTermination')\n              ?.setValue(false);\n          }\n        }\n      };\n\n      handleDialogResult(dialogResult);\n      expect(earlyTerminationControl.disabled).toBeTrue();\n      expect(\n        component.AssociatedContractorsListComponent.updateContractorContracts,\n      ).toHaveBeenCalledWith('2023-12-31', mockContractorContract.id);\n\n      earlyTerminationControl.enable();\n      earlyTerminationControl.setValue(true);\n\n      handleDialogResult(null);\n      expect(earlyTerminationControl.value).toBeFalse();\n      expect(earlyTerminationControl.enabled).toBeTrue();\n    });\n  });\n\n  describe('onContractorContractsChanged', () => {\n    it('should update contract cession property', () => {\n      component.contract = { ...mockContract, cession: false };\n      component.onContractorContractsChanged();\n      expect(component.contract.cession).toBeTrue();\n    });\n\n    it('should not throw error if contract is null', () => {\n      component.contract = null;\n      expect(() => component.onContractorContractsChanged()).not.toThrow();\n    });\n\n    it('should update contract form with cession value', () => {\n      component.contract = mockContract;\n      component.contractDetailForm = {\n        contractForm: new FormGroup({\n          cession: new FormControl(false),\n        }),\n      } as ContractDetailFormComponent;\n\n      component.onContractorContractsChanged();\n\n      expect(\n        component.contractDetailForm.contractForm.get('cession')?.value,\n      ).toBeTrue();\n    });\n\n    it('should not throw error if contractDetailForm is null', () => {\n      component.contract = mockContract;\n      component.contractDetailForm =\n        undefined as unknown as ContractDetailFormComponent;\n      expect(() => component.onContractorContractsChanged()).not.toThrow();\n    });\n  });\n\n  describe('uncheckEarlyTermination', () => {\n    it('should uncheck and enable early termination', () => {\n      const earlyTerminationControl = new FormControl(true);\n      component.contractDetailForm = {\n        contractForm: new FormGroup({\n          earlyTermination: earlyTerminationControl,\n        }),\n      } as ContractDetailFormComponent;\n\n      component.uncheckEarlyTermination();\n\n      expect(earlyTerminationControl.value).toBeFalse();\n      expect(earlyTerminationControl.enabled).toBeTrue();\n    });\n\n    it('should not throw error if contractDetailForm is null', () => {\n      component.contractDetailForm =\n        undefined as unknown as ContractDetailFormComponent;\n      expect(() => component.uncheckEarlyTermination()).not.toThrow();\n    });\n\n    it('should not throw error if earlyTermination control is missing', () => {\n      component.contractDetailForm = {\n        contractForm: new FormGroup({}),\n      } as ContractDetailFormComponent;\n      expect(() => component.uncheckEarlyTermination()).not.toThrow();\n    });\n  });\n\n  describe('ViewChild components', () => {\n    it('should set ViewChild components after view initialization', () => {\n\n      const mockAssociatedContractorsListComponent =\n        {} as AssociatedContractorsListComponent;\n      const mockContractDetailForm = {} as ContractDetailFormComponent;\n      const mockContractorDetailForm = {} as ContractorDetailFormComponent;\n\n      component.AssociatedContractorsListComponent =\n        mockAssociatedContractorsListComponent;\n      component.contractDetailForm = mockContractDetailForm;\n      component.contractorDetailForm = mockContractorDetailForm;\n\n      expect(component.AssociatedContractorsListComponent).toBe(\n        mockAssociatedContractorsListComponent,\n      );\n      expect(component.contractDetailForm).toBe(mockContractDetailForm);\n      expect(component.contractorDetailForm).toBe(mockContractorDetailForm);\n    });\n  });\n});"], "mappings": ";AAAA,SAASA,uBAAuB,QAAQ,8BAA8B;AACtE,SAA2BC,OAAO,QAAQ,uBAAuB;AACjE,SAASC,WAAW,EAAEC,SAAS,QAAQ,gBAAgB;AACvD,SAASC,wBAAwB,QAAQ,wBAAwB;AACjE,SAASC,SAAS,EAAEC,eAAe,QAAQ,0BAA0B;AACrE,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,mBAAmB,QAAQ,yBAAyB;AAK7D,SAASC,eAAe,QAAQ,gDAAgD;AAChF,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,iBAAiB,QAAQ,aAAa;AAC/C,SAASC,EAAE,EAAEC,UAAU,QAAQ,MAAM;AACrC,SAASC,2BAA2B,QAAQ,kCAAkC;AAE9E,SAASC,gBAAgB,QAAQ,eAAe;AAMhDC,QAAQ,CAAC,6BAA6B,EAAE,MAAK;EAC3C,IAAIC,SAAsC;EAC1C,IAAIC,OAAsD;EAC1D,IAAIC,kBAAmD;EACvD,IAAIC,eAA6C;EACjD,IAAIC,iBAAoD;EACxD,IAAIC,SAAoC;EAExC,MAAMC,YAAY,GAAa;IAC7BC,EAAE,EAAE,CAAC;IACLC,cAAc,EAAE,GAAG;IACnBC,MAAM,EAAE,eAAe;IACvBC,GAAG,EAAE,KAAK;IACVC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,KAAK;IACdC,cAAc,EAAE,IAAI;IACpBC,SAAS,EAAE,GAAG;IACdC,mBAAmB,EAAE,CAAC;IACtBC,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE,CAAC;IACjBC,QAAQ,EAAE,CAAC;IACXC,YAAY,EAAE,CAAC;IACfC,OAAO,EAAE,CAAC;IACVC,cAAc,EAAE,CAAC;IACjBC,YAAY,EAAE,CAAC;IACfC,iBAAiB,EAAE,CAAC;IACpBC,mBAAmB,EAAE,CAAC;IACtBC,eAAe,EAAE,CAAC;IAClBC,cAAc,EAAE,IAAI;IACpBC,gBAAgB,EAAE;GACnB;EAED,MAAMC,sBAAsB,GAAoB;IAC9CxB,EAAE,EAAE,CAAC;IACLC,cAAc,EAAE,GAAG;IACnBwB,YAAY,EAAE,CAAC;IACfC,QAAQ,EAAE,iBAAiB;IAC3BC,kBAAkB,EAAE,KAAK;IACzBzB,MAAM,EAAE,eAAe;IACvBC,GAAG,EAAE,IAAI;IACTG,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,KAAK;IACdoB,MAAM,EAAE,KAAK;IACbC,qBAAqB,EAAE,eAAe;IACtCC,gBAAgB,EAAE,eAAe;IACjCC,gBAAgB,EAAE,WAAW;IAC7BC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,iBAAiB;IACjCC,SAAS,EAAE,YAAY;IACvBC,eAAe,EAAE,kBAAkB;IACnC1B,cAAc,EAAE,IAAI;IACpB2B,kBAAkB,EAAE,iBAAiB;IACrCC,kBAAkB,EAAE,OAAO;IAC3BC,kBAAkB,EAAE;GACrB;EAEDC,UAAU,eAAAC,iBAAA,CAAC,aAAW;IACpB7C,kBAAkB,GAAG8C,OAAO,CAACC,YAAY,CAAC,iBAAiB,EAAE,CAC3D,SAAS,EACT,gBAAgB,CACjB,CAAC;IACF9C,eAAe,GAAG6C,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CACrD,OAAO,EACP,SAAS,CACV,CAAC;IACF7C,iBAAiB,GAAG4C,OAAO,CAACC,YAAY,CAAC,mBAAmB,EAAE,CAC5D,MAAM,EACN,MAAM,CACP,CAAC;IACF5C,SAAS,GAAG2C,OAAO,CAACC,YAAY,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,CAAC;IAEvD,MAAMlE,OAAO,CAACmE,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CACPtD,2BAA2B,EAC3Bf,uBAAuB,EACvBO,uBAAuB,EACvBE,mBAAmB,EACnBH,eAAe,CAChB;MACDgE,SAAS,EAAE,CACTlE,wBAAwB,EAAE,EAC1B;QACEmE,OAAO,EAAE/D,cAAc;QACvBgE,QAAQ,EAAE;UAAEC,MAAM,EAAE5D,EAAE,CAAC;YAAEY,EAAE,EAAE;UAAG,CAAE;QAAC;OACpC,EACD;QAAE8C,OAAO,EAAE7D,eAAe;QAAE8D,QAAQ,EAAEpD;MAAkB,CAAE,EAC1D;QAAEmD,OAAO,EAAE5D,YAAY;QAAE6D,QAAQ,EAAEnD;MAAe,CAAE,EACpD;QAAEkD,OAAO,EAAE3D,iBAAiB;QAAE4D,QAAQ,EAAElD;MAAiB,CAAE,EAC3D;QAAEiD,OAAO,EAAElE,SAAS;QAAEmE,QAAQ,EAAEjD;MAAS,CAAE,CAC5C;MACDmD,OAAO,EAAE,CAAC1D,gBAAgB;KAC3B,CAAC,CAAC2D,iBAAiB,EAAE;IAEtBxD,OAAO,GAAGlB,OAAO,CAAC2E,eAAe,CAAC7D,2BAA2B,CAAC;IAC9DG,SAAS,GAAGC,OAAO,CAAC0D,iBAAiB;EACvC,CAAC,EAAC;EAEFb,UAAU,CAAC,MAAK;IACd7C,OAAO,GAAGlB,OAAO,CAAC2E,eAAe,CAAC7D,2BAA2B,CAAC;IAC9DG,SAAS,GAAGC,OAAO,CAAC0D,iBAAiB;IACrCzD,kBAAkB,CAAC0D,OAAO,CAACC,GAAG,CAACC,WAAW,CAACnE,EAAE,CAACW,YAAY,CAAC,CAAC;IAC5DJ,kBAAkB,CAAC6D,cAAc,CAACF,GAAG,CAACC,WAAW,CAC/CnE,EAAE,CAACoC,sBAAsB,CAAC,CAC3B;IACD9B,OAAO,CAAC+D,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAAClE,SAAS,CAAC,CAACmE,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,8BAA8B,EAAE,MAAK;IACtCC,MAAM,CAAC9D,iBAAiB,CAACgE,IAAI,CAAC,CAACC,gBAAgB,EAAE;IACjDH,MAAM,CAAChE,kBAAkB,CAAC0D,OAAO,CAAC,CAACU,oBAAoB,CAAC,CAAC,CAAC;IAC1DJ,MAAM,CAAClE,SAAS,CAACuE,QAAQ,CAAC,CAACC,OAAO,CAAClE,YAAY,CAAC;IAChD4D,MAAM,CAAC9D,iBAAiB,CAACqE,IAAI,CAAC,CAACJ,gBAAgB,EAAE;EACnD,CAAC,CAAC;EAEFJ,EAAE,CAAC,6CAA6C,EAAE,MAAK;IACrD/D,kBAAkB,CAAC0D,OAAO,CAACC,GAAG,CAACC,WAAW,CACxClE,UAAU,CAAC,MAAM,IAAI8E,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAC9C;IACD1E,SAAS,CAAC2E,QAAQ,EAAE;IACpBT,MAAM,CAAC/D,eAAe,CAACyE,KAAK,CAAC,CAACN,oBAAoB,CAChD,6BAA6B,CAC9B;IACDJ,MAAM,CAAC9D,iBAAiB,CAACqE,IAAI,CAAC,CAACJ,gBAAgB,EAAE;EACnD,CAAC,CAAC;EAEFtE,QAAQ,CAAC,4BAA4B,EAAE,MAAK;IAC1CkE,EAAE,CAAC,kEAAkE,EAAE,MAAK;MAE1EjE,SAAS,CAACuE,QAAQ,GAAG,IAAI;MACzBvE,SAAS,CAAC6E,0BAA0B,EAAE;MACtCX,MAAM,CAAC/D,eAAe,CAAC2E,OAAO,CAAC,CAACC,GAAG,CAACV,gBAAgB,EAAE;MACtDH,MAAM,CAAC7D,SAAS,CAAC2E,IAAI,CAAC,CAACD,GAAG,CAACV,gBAAgB,EAAE;MAE7CrE,SAAS,CAACuE,QAAQ,GAAGjE,YAAY;MACjCN,SAAS,CAACiF,kCAAkC,GAC1CC,SAA0D;MAC5DlF,SAAS,CAAC6E,0BAA0B,EAAE;MACtCX,MAAM,CAAC/D,eAAe,CAAC2E,OAAO,CAAC,CAACC,GAAG,CAACV,gBAAgB,EAAE;MACtDH,MAAM,CAAC7D,SAAS,CAAC2E,IAAI,CAAC,CAACD,GAAG,CAACV,gBAAgB,EAAE;MAE7CrE,SAAS,CAACuE,QAAQ,GAAGjE,YAAY;MACjCN,SAAS,CAACiF,kCAAkC,GAAG;QAC7CE,2BAA2B,EAAEA,CAAA,KAAM;OACa;MAClDnF,SAAS,CAAC6E,0BAA0B,EAAE;MACtCX,MAAM,CAAC/D,eAAe,CAAC2E,OAAO,CAAC,CAACR,oBAAoB,CAClD,+CAA+C,CAChD;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFvE,QAAQ,CAAC,qBAAqB,EAAE,MAAK;IACnCkE,EAAE,CAAC,uCAAuC,EAAE,MAAK;MAE/CjE,SAAS,CAACuE,QAAQ,GAAGjE,YAAY;MAEjCN,SAAS,CAACiF,kCAAkC,GAAG;QAC7CE,2BAA2B,EAAEA,CAAA,KAAMpD,sBAAsB;QACzDqD,yBAAyB,EAAEpC,OAAO,CAACqC,SAAS,CAC1C,2BAA2B;OAEmB;MAElD,MAAMC,uBAAuB,GAAG,IAAItG,WAAW,CAAC,IAAI,CAAC;MACrDgB,SAAS,CAACuF,kBAAkB,GAAG;QAC7BC,YAAY,EAAE,IAAIvG,SAAS,CAAC;UAC1B6C,gBAAgB,EAAEwD;SACnB;OAC6B;MAEhC,MAAMG,YAAY,GAAiB;QAAEC,OAAO,EAAE;MAAY,CAAE;MAE5D,MAAMC,kBAAkB,GAAIC,MAA2B,IAAI;QACzD,IAAIA,MAAM,EAAE;UACV,IAAI5F,SAAS,CAACuF,kBAAkB,EAAE;YAChCvF,SAAS,CAACuF,kBAAkB,CAACC,YAAY,CACtCK,GAAG,CAAC,kBAAkB,CAAC,EACtBC,OAAO,EAAE;UACf;UACA,IACE9F,SAAS,CAACiF,kCAAkC,IAC5ClD,sBAAsB,CAACxB,EAAE,EACzB;YACAP,SAAS,CAACiF,kCAAkC,CAACG,yBAAyB,CACpEQ,MAAM,CAACF,OAAO,EACd3D,sBAAsB,CAACxB,EAAE,CAC1B;UACH;QACF,CAAC,MAAM;UACL,IAAIP,SAAS,CAACuF,kBAAkB,EAAE;YAChCvF,SAAS,CAACuF,kBAAkB,CAACC,YAAY,CACtCK,GAAG,CAAC,kBAAkB,CAAC,EACtBE,QAAQ,CAAC,KAAK,CAAC;UACrB;QACF;MACF,CAAC;MAEDJ,kBAAkB,CAACF,YAAY,CAAC;MAChCvB,MAAM,CAACoB,uBAAuB,CAACU,QAAQ,CAAC,CAACC,QAAQ,EAAE;MACnD/B,MAAM,CACJlE,SAAS,CAACiF,kCAAkC,CAACG,yBAAyB,CACvE,CAACd,oBAAoB,CAAC,YAAY,EAAEvC,sBAAsB,CAACxB,EAAE,CAAC;MAE/D+E,uBAAuB,CAACY,MAAM,EAAE;MAChCZ,uBAAuB,CAACS,QAAQ,CAAC,IAAI,CAAC;MAEtCJ,kBAAkB,CAAC,IAAI,CAAC;MACxBzB,MAAM,CAACoB,uBAAuB,CAACa,KAAK,CAAC,CAACC,SAAS,EAAE;MACjDlC,MAAM,CAACoB,uBAAuB,CAACe,OAAO,CAAC,CAACJ,QAAQ,EAAE;IACpD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFlG,QAAQ,CAAC,8BAA8B,EAAE,MAAK;IAC5CkE,EAAE,CAAC,yCAAyC,EAAE,MAAK;MACjDjE,SAAS,CAACuE,QAAQ,GAAG;QAAE,GAAGjE,YAAY;QAAEQ,OAAO,EAAE;MAAK,CAAE;MACxDd,SAAS,CAACsG,4BAA4B,EAAE;MACxCpC,MAAM,CAAClE,SAAS,CAACuE,QAAQ,CAACzD,OAAO,CAAC,CAACmF,QAAQ,EAAE;IAC/C,CAAC,CAAC;IAEFhC,EAAE,CAAC,4CAA4C,EAAE,MAAK;MACpDjE,SAAS,CAACuE,QAAQ,GAAG,IAAI;MACzBL,MAAM,CAAC,MAAMlE,SAAS,CAACsG,4BAA4B,EAAE,CAAC,CAACvB,GAAG,CAACwB,OAAO,EAAE;IACtE,CAAC,CAAC;IAEFtC,EAAE,CAAC,gDAAgD,EAAE,MAAK;MACxDjE,SAAS,CAACuE,QAAQ,GAAGjE,YAAY;MACjCN,SAAS,CAACuF,kBAAkB,GAAG;QAC7BC,YAAY,EAAE,IAAIvG,SAAS,CAAC;UAC1B6B,OAAO,EAAE,IAAI9B,WAAW,CAAC,KAAK;SAC/B;OAC6B;MAEhCgB,SAAS,CAACsG,4BAA4B,EAAE;MAExCpC,MAAM,CACJlE,SAAS,CAACuF,kBAAkB,CAACC,YAAY,CAACK,GAAG,CAAC,SAAS,CAAC,EAAEM,KAAK,CAChE,CAACF,QAAQ,EAAE;IACd,CAAC,CAAC;IAEFhC,EAAE,CAAC,sDAAsD,EAAE,MAAK;MAC9DjE,SAAS,CAACuE,QAAQ,GAAGjE,YAAY;MACjCN,SAAS,CAACuF,kBAAkB,GAC1BL,SAAmD;MACrDhB,MAAM,CAAC,MAAMlE,SAAS,CAACsG,4BAA4B,EAAE,CAAC,CAACvB,GAAG,CAACwB,OAAO,EAAE;IACtE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFxG,QAAQ,CAAC,yBAAyB,EAAE,MAAK;IACvCkE,EAAE,CAAC,6CAA6C,EAAE,MAAK;MACrD,MAAMqB,uBAAuB,GAAG,IAAItG,WAAW,CAAC,IAAI,CAAC;MACrDgB,SAAS,CAACuF,kBAAkB,GAAG;QAC7BC,YAAY,EAAE,IAAIvG,SAAS,CAAC;UAC1B6C,gBAAgB,EAAEwD;SACnB;OAC6B;MAEhCtF,SAAS,CAACwG,uBAAuB,EAAE;MAEnCtC,MAAM,CAACoB,uBAAuB,CAACa,KAAK,CAAC,CAACC,SAAS,EAAE;MACjDlC,MAAM,CAACoB,uBAAuB,CAACe,OAAO,CAAC,CAACJ,QAAQ,EAAE;IACpD,CAAC,CAAC;IAEFhC,EAAE,CAAC,sDAAsD,EAAE,MAAK;MAC9DjE,SAAS,CAACuF,kBAAkB,GAC1BL,SAAmD;MACrDhB,MAAM,CAAC,MAAMlE,SAAS,CAACwG,uBAAuB,EAAE,CAAC,CAACzB,GAAG,CAACwB,OAAO,EAAE;IACjE,CAAC,CAAC;IAEFtC,EAAE,CAAC,+DAA+D,EAAE,MAAK;MACvEjE,SAAS,CAACuF,kBAAkB,GAAG;QAC7BC,YAAY,EAAE,IAAIvG,SAAS,CAAC,EAAE;OACA;MAChCiF,MAAM,CAAC,MAAMlE,SAAS,CAACwG,uBAAuB,EAAE,CAAC,CAACzB,GAAG,CAACwB,OAAO,EAAE;IACjE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFxG,QAAQ,CAAC,sBAAsB,EAAE,MAAK;IACpCkE,EAAE,CAAC,2DAA2D,EAAE,MAAK;MAEnE,MAAMwC,sCAAsC,GAC1C,EAAwC;MAC1C,MAAMC,sBAAsB,GAAG,EAAiC;MAChE,MAAMC,wBAAwB,GAAG,EAAmC;MAEpE3G,SAAS,CAACiF,kCAAkC,GAC1CwB,sCAAsC;MACxCzG,SAAS,CAACuF,kBAAkB,GAAGmB,sBAAsB;MACrD1G,SAAS,CAAC4G,oBAAoB,GAAGD,wBAAwB;MAEzDzC,MAAM,CAAClE,SAAS,CAACiF,kCAAkC,CAAC,CAAC4B,IAAI,CACvDJ,sCAAsC,CACvC;MACDvC,MAAM,CAAClE,SAAS,CAACuF,kBAAkB,CAAC,CAACsB,IAAI,CAACH,sBAAsB,CAAC;MACjExC,MAAM,CAAClE,SAAS,CAAC4G,oBAAoB,CAAC,CAACC,IAAI,CAACF,wBAAwB,CAAC;IACvE,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}