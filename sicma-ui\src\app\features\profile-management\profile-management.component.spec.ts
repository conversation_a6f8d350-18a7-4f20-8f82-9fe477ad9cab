import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { AlertService } from '@shared/services/alert.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { of, throwError } from 'rxjs';
import { UserProfileAssignment } from './models/profile.model';
import { ProfileManagementComponent } from './profile-management.component';
import { ProfileService } from './services/profile.service';

describe('ProfileManagementComponent', () => {
  let component: ProfileManagementComponent;
  let fixture: ComponentFixture<ProfileManagementComponent>;
  let profileService: jasmine.SpyObj<ProfileService>;
  let alertService: jasmine.SpyObj<AlertService>;
  let spinnerService: jasmine.SpyObj<NgxSpinnerService>;
  let dialog: jasmine.SpyObj<MatDialog>;

  const mockProfiles = [
    { id: 1, name: 'Admin' },
    { id: 2, name: 'User' },
  ];

  const mockUsers: UserProfileAssignment[] = [
    {
      userId: 1,
      username: 'test',
      profiles: [{ id: 1, name: 'Admin' }],
    },
  ];

  beforeEach(() => {
    const profileServiceSpy = jasmine.createSpyObj('ProfileService', [
      'getAllProfiles',
      'getUsersWithProfiles',
      'assignProfile',
      'removeProfile',
    ]);
    const alertServiceSpy = jasmine.createSpyObj('AlertService', [
      'success',
      'error',
      'warning',
    ]);
    const spinnerServiceSpy = jasmine.createSpyObj('NgxSpinnerService', [
      'show',
      'hide',
    ]);
    const dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);

    profileServiceSpy.getAllProfiles.and.returnValue(of(mockProfiles));
    profileServiceSpy.getUsersWithProfiles.and.returnValue(of(mockUsers));
    dialogSpy.open.and.returnValue({
      afterClosed: () => of(undefined),
    } as MatDialogRef<unknown>);

    TestBed.configureTestingModule({
      imports: [
        ProfileManagementComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
      ],
      providers: [
        { provide: ProfileService, useValue: profileServiceSpy },
        { provide: AlertService, useValue: alertServiceSpy },
        { provide: NgxSpinnerService, useValue: spinnerServiceSpy },
        { provide: MatDialog, useValue: dialogSpy },
      ],
    });

    fixture = TestBed.createComponent(ProfileManagementComponent);
    component = fixture.componentInstance;
    profileService = TestBed.inject(
      ProfileService,
    ) as jasmine.SpyObj<ProfileService>;
    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;
    spinnerService = TestBed.inject(
      NgxSpinnerService,
    ) as jasmine.SpyObj<NgxSpinnerService>;
    dialog = TestBed.inject(MatDialog) as jasmine.SpyObj<MatDialog>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load data on init', () => {
    component.ngOnInit();
    expect(spinnerService.show).toHaveBeenCalled();
    expect(profileService.getAllProfiles).toHaveBeenCalled();
    expect(profileService.getUsersWithProfiles).toHaveBeenCalled();
    expect(component.allProfiles).toEqual(mockProfiles);
    expect(component.dataSource.data).toEqual(mockUsers);
  });

  it('should handle error when loading data', () => {
    profileService.getAllProfiles.and.returnValue(
      throwError(() => ({ error: 'Error' })),
    );
    component.ngOnInit();
    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar los datos',
    );
  });

  it('should assign profile successfully', () => {
    profileService.assignProfile.and.returnValue(of(undefined));
    component.assignProfile(1, 2);
    expect(spinnerService.show).toHaveBeenCalled();
    expect(profileService.assignProfile).toHaveBeenCalledWith({
      user_id: 1,
      profile_id: 2,
    });
    expect(alertService.success).toHaveBeenCalledWith(
      'Perfil asignado correctamente',
    );
  });

  it('should handle error when assigning profile', () => {
    profileService.assignProfile.and.returnValue(
      throwError(() => ({ error: 'Error' })),
    );
    component.assignProfile(1, 2);
    expect(alertService.error).toHaveBeenCalledWith(
      'Error al asignar el perfil',
    );
  });

  it('should prevent removing last profile', () => {
    component.dataSource.data = mockUsers;
    component.removeProfile(1, 1);
    expect(alertService.warning).toHaveBeenCalledWith(
      'El usuario debe tener al menos un perfil',
    );
    expect(profileService.removeProfile).not.toHaveBeenCalled();
  });

  it('should remove profile successfully', () => {
    const userWithMultipleProfiles = {
      userId: 1,
      username: 'test',
      profiles: [
        { id: 1, name: 'Admin' },
        { id: 2, name: 'User' },
      ],
    };
    component.dataSource.data = [userWithMultipleProfiles];
    profileService.removeProfile.and.returnValue(of(undefined));

    component.removeProfile(1, 1);
    expect(spinnerService.show).toHaveBeenCalled();
    expect(profileService.removeProfile).toHaveBeenCalledWith({
      user_id: 1,
      profile_id: 1,
    });
    expect(alertService.success).toHaveBeenCalledWith(
      'Perfil removido correctamente',
    );
  });

  it('should handle error when removing profile', () => {
    const userWithMultipleProfiles = {
      userId: 1,
      username: 'test',
      profiles: [
        { id: 1, name: 'Admin' },
        { id: 2, name: 'User' },
      ],
    };
    component.dataSource.data = [userWithMultipleProfiles];
    profileService.removeProfile.and.returnValue(
      throwError(() => ({ error: 'Error' })),
    );

    component.removeProfile(1, 1);
    expect(alertService.error).toHaveBeenCalledWith(
      'Error al remover el perfil',
    );
  });

  it('should get available profiles', () => {
    component.allProfiles = mockProfiles;
    const user = mockUsers[0];
    const availableProfiles = component.getAvailableProfiles(user);
    expect(availableProfiles).toEqual([{ id: 2, name: 'User' }]);
  });

  it('should open user dialog and reload data on success', () => {
    dialog.open.and.returnValue({
      afterClosed: () => of(mockUsers[0]),
    } as MatDialogRef<unknown>);
    component.openUserDialog();
    expect(dialog.open).toHaveBeenCalled();
    expect(profileService.getAllProfiles).toHaveBeenCalled();
    expect(profileService.getUsersWithProfiles).toHaveBeenCalled();
  });

  it('should apply filter', () => {
    const event = { target: { value: 'test' } } as unknown as Event;
    component.dataSource.paginator = component.paginator;
    component.applyFilter(event);
    expect(component.dataSource.filter).toBe('test');
  });
});
