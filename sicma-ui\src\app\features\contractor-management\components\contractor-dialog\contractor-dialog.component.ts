import { As<PERSON><PERSON>ip<PERSON> } from '@angular/common';
import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { NgxSpinnerService } from 'ngx-spinner';

import {
  MatAutocomplete,
  MatAutocompleteModule,
  MatAutocompleteSelectedEvent,
  MatAutocompleteTrigger,
} from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { Contractor } from '@contractor-management/models/contractor.model';
import { EducationLevel } from '@contractor-management/models/education-level.model';
import { Eps } from '@contractor-management/models/eps.model';
import { Gender } from '@contractor-management/models/gender.model';
import { LegalNature } from '@contractor-management/models/legal-nature.model';
import { Profession } from '@contractor-management/models/profession.model';
import { ContractorService } from '@contractor-management/services/contractor.service';
import { EducationLevelService } from '@contractor-management/services/education-level.service';
import { EpsService } from '@contractor-management/services/eps.service';
import { GenderService } from '@contractor-management/services/gender.service';
import { LegalNatureService } from '@contractor-management/services/legal-nature.service';
import { ProfessionService } from '@contractor-management/services/profession.service';
import { Department } from '@shared/models/department.model';
import { IDType } from '@shared/models/id-type.model';
import { Municipality } from '@shared/models/municipality.model';
import { AlertService } from '@shared/services/alert.service';
import { DepartmentService } from '@shared/services/department.service';
import { IDTypeService } from '@shared/services/id-type.service';
import { MunicipalityService } from '@shared/services/municipality.service';
import { Observable, finalize, forkJoin, map, startWith } from 'rxjs';

@Component({
  selector: 'app-contractor-dialog',
  templateUrl: './contractor-dialog.component.html',
  styleUrl: './contractor-dialog.component.scss',
  standalone: true,
  imports: [
    MatIconModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatDialogModule,
    MatDatepickerModule,
    MatAutocompleteModule,
    AsyncPipe,
  ],
})
export class ContractorDialogComponent implements OnInit {
  @ViewChild('professionAuto') professionAutocomplete?: MatAutocomplete;
  @ViewChild('professionInput', { read: MatAutocompleteTrigger })
  professionAutocompleteTrigger?: MatAutocompleteTrigger;
  @ViewChild('departmentAuto') departmentAutocomplete?: MatAutocomplete;
  @ViewChild('departmentInput', { read: MatAutocompleteTrigger })
  departmentAutocompleteTrigger?: MatAutocompleteTrigger;
  @ViewChild('municipalityAuto') municipalityAutocomplete?: MatAutocomplete;
  @ViewChild('municipalityInput', { read: MatAutocompleteTrigger })
  municipalityAutocompleteTrigger?: MatAutocompleteTrigger;

  contractorForm: FormGroup = this.formBuilder.group({
    fullName: ['', Validators.required],
    idTypeId: [null, Validators.required],
    idNumber: ['', [Validators.required, Validators.pattern(/^\d+$/)]],
    personalEmail: ['', [Validators.required, Validators.email]],
    corporateEmail: ['', Validators.email],
    phone: ['', Validators.pattern(/^\d+$/)],
    epsId: [null],
    birthDate: [null],
    genderId: [null],
    educationLevelId: [null],
    professionId: [null],
    lastObtainedDegree: [null],
    departmentId: [null],
    municipalityId: [null],
    legalNatureId: [null, Validators.required],
  });
  idTypes: IDType[] = [];
  epss: Eps[] = [];
  genders: Gender[] = [];
  educationLevels: EducationLevel[] = [];
  professions: Profession[] = [];
  departments: Department[] = [];
  municipalities: Municipality[] = [];
  legalNatures: LegalNature[] = [];
  today: Date = new Date();

  isLastObtainedDegreeEnabled = false;
  isProfessionEditable = true;

  private readonly EDUCATION_LEVELS_WITH_DEGREE = [
    'BACHILLER',
    'TÉCNICO',
    'TECNÓLOGO',
  ];

  private readonly ADVANCED_EDUCATION_LEVELS = [
    'ESPECIALIZACIÓN',
    'MAESTRIA',
    'DOCTORADO',
    'POST DOCTORADO',
  ];

  professionSearchCtrl = new FormControl('');
  departmentSearchCtrl = new FormControl('');
  municipalitySearchCtrl = new FormControl('');

  filteredProfessions: Observable<Profession[]>;
  filteredDepartments: Observable<Department[]>;
  filteredMunicipalities: Observable<Municipality[]>;

  constructor(
    private readonly dialogRef: MatDialogRef<ContractorDialogComponent>,
    private readonly spinner: NgxSpinnerService,
    @Inject(MAT_DIALOG_DATA) public inputContractor: Contractor | undefined,
    private readonly contractorService: ContractorService,
    private readonly idTypeService: IDTypeService,
    private readonly epsService: EpsService,
    private readonly genderService: GenderService,
    private readonly educationLevelService: EducationLevelService,
    private readonly professionService: ProfessionService,
    private readonly departmentService: DepartmentService,
    private readonly municipalityService: MunicipalityService,
    private readonly legalNatureService: LegalNatureService,
    private readonly alert: AlertService,
    private readonly formBuilder: FormBuilder,
  ) {
    this.filteredProfessions = this.professionSearchCtrl.valueChanges.pipe(
      startWith(''),
      map((value) => this._filterProfessions(value || '')),
    );

    this.filteredDepartments = this.departmentSearchCtrl.valueChanges.pipe(
      startWith(''),
      map((value) => this._filterDepartments(value || '')),
    );

    this.filteredMunicipalities = this.municipalitySearchCtrl.valueChanges.pipe(
      startWith(''),
      map((value) => this._filterMunicipalities(value || '')),
    );
  }

  ngOnInit(): void {
    this.spinner.show();
    forkJoin({
      idTypes: this.idTypeService.getAll(),
      epss: this.epsService.getAll(),
      genders: this.genderService.getAll(),
      educationLevels: this.educationLevelService.getAll(),
      professions: this.professionService.getAll(),
      departments: this.departmentService.getAll(),
      legalNatures: this.legalNatureService.getAll(),
    })
      .pipe(finalize(() => this.spinner.hide()))
      .subscribe({
        next: ({
          idTypes,
          epss,
          genders,
          educationLevels,
          professions,
          departments,
          legalNatures,
        }) => {
          this.idTypes = idTypes;
          this.epss = epss;
          this.genders = genders;
          this.educationLevels = educationLevels;
          this.professions = professions;
          this.departments = departments;
          this.legalNatures = legalNatures;

          if (this.inputContractor) {
            this.contractorForm.patchValue({
              ...this.inputContractor,
              idTypeId: this.inputContractor.idType?.id,
              epsId: this.inputContractor.eps?.id,
              genderId: this.inputContractor.gender?.id,
              educationLevelId: this.inputContractor.educationLevel?.id,
              professionId: this.inputContractor.profession?.id,
              departmentId: this.inputContractor.department?.id,
              municipalityId: this.inputContractor.municipality?.id,
              legalNatureId: this.inputContractor.legalNature?.id,
              birthDate: this.inputContractor.birthDate
                ? new Date(this.inputContractor.birthDate + 'T00:00:00')
                : null,
            });

            if (this.inputContractor.department) {
              this.loadMunicipalities(this.inputContractor.department.id);

              const profession = this.professions.find(
                (p) => p.id === this.inputContractor?.profession?.id,
              );
              const department = this.departments.find(
                (d) => d.id === this.inputContractor?.department?.id,
              );

              if (profession) {
                this.professionSearchCtrl.setValue(profession.name);
              }

              if (department) {
                this.departmentSearchCtrl.setValue(department.name);
              }

              if (this.inputContractor.municipality) {
                const municipality = this.municipalities.find(
                  (m) => m.id === this.inputContractor?.municipality?.id,
                );
                if (municipality) {
                  this.municipalitySearchCtrl.setValue(municipality.name);
                }
              }
            }
          }
        },
        error: (error) => {
          this.dialogRef.close();
          this.alert.error(error.error?.detail ?? 'Error al cargar los datos del formulario');
        },
      });

    this.setupFormListeners();
  }

  setupFormListeners(): void {
    this.contractorForm
      .get('departmentId')
      ?.valueChanges.subscribe((departmentId) => {
        if (departmentId) {
          this.loadMunicipalities(departmentId);
          this.municipalitySearchCtrl.enable();
        } else {
          this.municipalities = [];
          this.contractorForm.get('municipalityId')?.setValue(null);
          this.municipalitySearchCtrl.setValue('');
          this.municipalitySearchCtrl.disable();
        }
      });

    this.contractorForm
      .get('educationLevelId')
      ?.valueChanges.subscribe((value: number | null) => {
        const educationLevel = this.educationLevels.find(
          (el) => el.id === value,
        );
        this.onEducationLevelChange(educationLevel || null);
      });
  }

  onEducationLevelChange(educationLevel: EducationLevel | null): void {
    if (educationLevel) {
      const educationLevelName = educationLevel.name.toUpperCase();
      const isBachillerTecnicoTecnologo =
        this.EDUCATION_LEVELS_WITH_DEGREE.includes(educationLevelName);
      const isAdvancedEducationLevel =
        this.ADVANCED_EDUCATION_LEVELS.includes(educationLevelName);
      const lastObtainedDegreeControl =
        this.contractorForm.get('lastObtainedDegree');
      const professionControl = this.contractorForm.get('professionId');

      if (isBachillerTecnicoTecnologo) {
        this.isProfessionEditable = false;
        professionControl?.disable();
        this.professionSearchCtrl.disable();

        if (educationLevelName === 'BACHILLER') {
          const bachillerProfession = this.professions.find(
            (p) => p.name.toUpperCase() === 'BACHILLER',
          );
          professionControl?.setValue(bachillerProfession?.id ?? null);
          this.professionSearchCtrl.setValue(bachillerProfession?.name ?? '');
          lastObtainedDegreeControl?.disable();
          lastObtainedDegreeControl?.clearValidators();
          lastObtainedDegreeControl?.setValue(null);
        } else {
          lastObtainedDegreeControl?.enable();
          lastObtainedDegreeControl?.setValidators([Validators.required]);
        }
      } else if (isAdvancedEducationLevel) {
        this.isProfessionEditable = true;
        professionControl?.enable();
        professionControl?.setValidators([Validators.required]);
        this.professionSearchCtrl.enable();

        lastObtainedDegreeControl?.enable();
        lastObtainedDegreeControl?.setValidators([Validators.required]);
      } else {
        this.isProfessionEditable = true;
        professionControl?.enable();
        this.professionSearchCtrl.enable();

        lastObtainedDegreeControl?.disable();
        lastObtainedDegreeControl?.clearValidators();
        lastObtainedDegreeControl?.setValue(null);

        if (educationLevelName === 'PROFESIONAL') {
          professionControl?.setValidators([Validators.required]);
        } else {
          professionControl?.clearValidators();
        }
      }

      lastObtainedDegreeControl?.updateValueAndValidity();
      professionControl?.updateValueAndValidity();
    } else {
      this.isProfessionEditable = true;
      const lastObtainedDegreeControl =
        this.contractorForm.get('lastObtainedDegree');
      const professionControl = this.contractorForm.get('professionId');

      lastObtainedDegreeControl?.disable();
      lastObtainedDegreeControl?.clearValidators();
      lastObtainedDegreeControl?.setValue(null);
      lastObtainedDegreeControl?.updateValueAndValidity();

      professionControl?.enable();
      professionControl?.clearValidators();
      professionControl?.setValue(null);
      professionControl?.updateValueAndValidity();

      this.professionSearchCtrl.enable();
      this.professionSearchCtrl.setValue('');
    }
  }

  loadMunicipalities(departmentId: number): void {
    this.municipalityService.getAllByDepartmentId(departmentId).subscribe({
      next: (municipalities) => {
        this.municipalities = municipalities;
        this.filteredMunicipalities =
          this.municipalitySearchCtrl.valueChanges.pipe(
            startWith(''),
            map((value) => this._filterMunicipalities(value ?? '')),
          );
      },
      error: (error) => {
        this.alert.error(error.error?.detail ?? 'Error al cargar municipios');
      },
    });
  }

  onSubmit(): void {
    if (this.contractorForm.valid) {
      this.spinner.show();
      const contractorData = {
        ...this.contractorForm.value,
        birthDate: this.contractorForm.value.birthDate
          ? new Date(this.contractorForm.value.birthDate)
              .toISOString()
              .split('T')[0]
          : null,
        phone: this.contractorForm.value.phone || undefined,
      };

      const operation = this.inputContractor
        ? this.contractorService.update(this.inputContractor.id, contractorData)
        : this.contractorService.create(contractorData);

      operation.pipe(finalize(() => this.spinner.hide())).subscribe({
        next: (contractor) => {
          this.dialogRef.close(contractor);
          this.alert.success(
            `Contratista ${
              this.inputContractor ? 'editado' : 'creado'
            } exitosamente`,
          );
        },
        error: (error) => {
          let errorMessage = 'Error al procesar la solicitud';
          if (error?.error?.detail) {
            if (
              error.error.detail.includes(
                'Numero de cedula ya se encuentra registrado',
              )
            ) {
              errorMessage = 'Número de cédula ya se encuentra registrado';
            } else if (
              error.error.detail.includes(
                'El correo institucional ya se encuentra registrado',
              )
            ) {
              errorMessage =
                'El correo institucional ya se encuentra registrado';
            } else {
              errorMessage = `Error al ${this.inputContractor ? 'editar' : 'crear'} contratista`;
            }
          }

          this.alert.error(errorMessage);
        },
      });
    }
  }

  handleProfessionSelection(event: MatAutocompleteSelectedEvent): void {
    const selectedProfessionName = event.option.viewValue;
    const selectedProfession = this.professions.find(
      (prof) => prof.name === selectedProfessionName,
    );

    if (selectedProfession) {
      this.contractorForm.get('professionId')?.setValue(selectedProfession.id);
      setTimeout(() => {
        this.professionAutocompleteTrigger?.closePanel();
      }, 0);
    }
  }

  handleDepartmentSelection(event: MatAutocompleteSelectedEvent): void {
    const selectedDepartmentName = event.option.viewValue;
    const selectedDepartment = this.departments.find(
      (dept) => dept.name === selectedDepartmentName,
    );

    if (selectedDepartment) {
      this.contractorForm.get('departmentId')?.setValue(selectedDepartment.id);
      this.contractorForm.get('municipalityId')?.setValue(null);
      this.municipalitySearchCtrl.setValue('');
      setTimeout(() => {
        this.departmentAutocompleteTrigger?.closePanel();
      }, 0);
    }
  }

  handleMunicipalitySelection(event: MatAutocompleteSelectedEvent): void {
    const selectedMunicipalityName = event.option.viewValue;
    const selectedMunicipality = this.municipalities.find(
      (mun) => mun.name === selectedMunicipalityName,
    );

    if (selectedMunicipality) {
      this.contractorForm
        .get('municipalityId')
        ?.setValue(selectedMunicipality.id);
      setTimeout(() => {
        this.municipalityAutocompleteTrigger?.closePanel();
      }, 0);
    }
  }

  private _filterProfessions(value: string): Profession[] {
    const filterValue = value.toLowerCase();
    return this.professions.filter((profession) =>
      profession.name.toLowerCase().includes(filterValue),
    );
  }

  private _filterDepartments(value: string): Department[] {
    const filterValue = value.toLowerCase();
    return this.departments.filter((department) =>
      department.name.toLowerCase().includes(filterValue),
    );
  }

  private _filterMunicipalities(value: string): Municipality[] {
    const filterValue = value.toLowerCase();
    return this.municipalities.filter((municipality) =>
      municipality.name.toLowerCase().includes(filterValue),
    );
  }

  displayProfession(professionName: string): string {
    return professionName || '';
  }

  displayDepartment(departmentName: string): string {
    return departmentName || '';
  }

  displayMunicipality(municipalityName: string): string {
    return municipalityName || '';
  }

  showAllProfessions(): void {
    if (!this.professionAutocompleteTrigger?.panelOpen) {
      this.professionSearchCtrl.setValue('');
      setTimeout(() => {
        this.professionAutocompleteTrigger?.openPanel();
      }, 0);
    }
  }

  showAllDepartments(): void {
    if (!this.departmentAutocompleteTrigger?.panelOpen) {
      this.departmentSearchCtrl.setValue('');
      setTimeout(() => {
        this.departmentAutocompleteTrigger?.openPanel();
      }, 0);
    }
  }

  showAllMunicipalities(): void {
    if (
      this.contractorForm.get('departmentId')?.value &&
      !this.municipalityAutocompleteTrigger?.panelOpen
    ) {
      this.municipalitySearchCtrl.setValue('');
      setTimeout(() => {
        this.municipalityAutocompleteTrigger?.openPanel();
      }, 0);
    }
  }
}
