import { Component, OnInit } from '@angular/core';
import {
  Form<PERSON><PERSON>er,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { NgxSpinnerService } from 'ngx-spinner';
import { finalize } from 'rxjs';

import { HttpErrorResponse } from '@angular/common/http';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { UserService } from '@core/auth/services/user.service';
import { AlertService } from '@shared/services/alert.service';
import { Profile } from '../../models/profile.model';
import { ProfileService } from '../../services/profile.service';

@Component({
  selector: 'app-user-dialog',
  templateUrl: './user-dialog.component.html',
  styleUrl: './user-dialog.component.scss',
  standalone: true,
  imports: [
    MatIconModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatDialogModule,
  ],
})
export class UserDialogComponent implements OnInit {
  userForm: FormGroup = this.formBuilder.group({
    username: ['', Validators.required],
    profileIds: [[], [Validators.required, Validators.minLength(1)]],
  });

  availableProfiles: Profile[] = [];

  constructor(
    private dialogRef: MatDialogRef<UserDialogComponent>,
    private spinner: NgxSpinnerService,
    private profileService: ProfileService,
    private userService: UserService,
    private alert: AlertService,
    private formBuilder: FormBuilder,
  ) {}

  ngOnInit(): void {
    this.spinner.show();
    this.profileService
      .getAllProfiles()
      .pipe(finalize(() => this.spinner.hide()))
      .subscribe({
        next: (profiles) => (this.availableProfiles = profiles),
        error: (error) => {
          this.alert.error(error.error?.detail ?? 'Error al cargar los perfiles');
          this.dialogRef.close();
        },
      });
  }

  onSubmit(): void {
    if (!this.userForm.valid) {
      return;
    }

    const { username, profileIds } = this.userForm.value;
    this.spinner.show();
    this.userService
      .getByUsername(username)
      .pipe(finalize(() => this.spinner.hide()))
      .subscribe({
        next: () => this.alert.warning('El usuario ya existe en el sistema'),
        error: (error: HttpErrorResponse) => {
          if (error.status === 404) {
            this.spinner.show();
            this.userService
              .create({
                username,
                profile_ids: profileIds,
              })
              .pipe(finalize(() => this.spinner.hide()))
              .subscribe({
                next: (user) => {
                  this.alert.success('Usuario creado correctamente');
                  this.dialogRef.close(user);
                },
                error: (error) => {
                  this.alert.error(error.error?.detail ?? 'Error al crear el usuario');
                },
              });
          } else {
            this.alert.error(error.error?.detail ?? 'Error al verificar el usuario');
          }
        },
      });
  }
}
