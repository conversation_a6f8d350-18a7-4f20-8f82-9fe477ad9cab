import { HttpErrorResponse } from '@angular/common/http';
import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { User } from '@core/auth/models/user.model';
import { UserProfile } from '@core/auth/models/user_profile.model';
import { UserService } from '@core/auth/services/user.service';
import { AlertService } from '@shared/services/alert.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { of, throwError } from 'rxjs';
import { UserProfileAssignment } from '../../models/profile.model';
import { ProfileService } from '../../services/profile.service';
import { UserDialogComponent } from './user-dialog.component';

describe('UserDialogComponent', () => {
  let component: UserDialogComponent;
  let fixture: ComponentFixture<UserDialogComponent>;
  let dialogRef: jasmine.SpyObj<MatDialogRef<UserDialogComponent>>;
  let spinner: jasmine.SpyObj<NgxSpinnerService>;
  let profileService: jasmine.SpyObj<ProfileService>;
  let userService: jasmine.SpyObj<UserService>;
  let alertService: jasmine.SpyObj<AlertService>;

  const mockProfiles = [
    { id: 1, name: 'Profile 1' },
    { id: 2, name: 'Profile 2' },
  ];

  const mockUserProfiles: UserProfile[] = [
    { profile_id: 1, profile_name: 'Profile 1' },
    { profile_id: 2, profile_name: 'Profile 2' },
  ];

  beforeEach(async () => {
    dialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
    spinner = jasmine.createSpyObj('NgxSpinnerService', ['show', 'hide']);
    profileService = jasmine.createSpyObj('ProfileService', ['getAllProfiles']);
    userService = jasmine.createSpyObj('UserService', [
      'getByUsername',
      'create',
    ]);
    alertService = jasmine.createSpyObj('AlertService', [
      'success',
      'error',
      'warning',
    ]);

    profileService.getAllProfiles.and.returnValue(of(mockProfiles));

    await TestBed.configureTestingModule({
      imports: [
        ReactiveFormsModule,
        BrowserAnimationsModule,
        UserDialogComponent,
      ],
      providers: [
        FormBuilder,
        { provide: MatDialogRef, useValue: dialogRef },
        { provide: NgxSpinnerService, useValue: spinner },
        { provide: ProfileService, useValue: profileService },
        { provide: UserService, useValue: userService },
        { provide: AlertService, useValue: alertService },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(UserDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load profiles on init', fakeAsync(() => {
    tick();
    expect(spinner.show).toHaveBeenCalled();
    expect(profileService.getAllProfiles).toHaveBeenCalled();
    expect(component.availableProfiles).toEqual(mockProfiles);
    expect(spinner.hide).toHaveBeenCalled();
  }));

  it('should handle error when loading profiles', fakeAsync(() => {
    profileService.getAllProfiles.and.returnValue(
      throwError(() => ({ error: 'Error' })),
    );
    component.ngOnInit();
    tick();

    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar los perfiles',
    );
    expect(dialogRef.close).toHaveBeenCalled();
    expect(spinner.hide).toHaveBeenCalled();
  }));

  it('should validate username format', () => {
    const usernameControl = component.userForm.get('username');
    expect(usernameControl?.valid).toBeFalse();

    usernameControl?.setValue('validUsername');
    expect(usernameControl?.valid).toBeTrue();

    usernameControl?.setValue('');
    expect(usernameControl?.valid).toBeFalse();
  });

  it('should validate profile selection', () => {
    const profileIdsControl = component.userForm.get('profileIds');
    expect(profileIdsControl?.valid).toBeFalse();

    profileIdsControl?.setValue([1]);
    expect(profileIdsControl?.valid).toBeTrue();

    profileIdsControl?.setValue([]);
    expect(profileIdsControl?.valid).toBeFalse();
  });

  it('should not submit form when invalid', () => {
    component.userForm.patchValue({
      username: '',
      profileIds: [],
    });
    expect(component.userForm.valid).toBeFalse();

    component.onSubmit();

    expect(userService.getByUsername).not.toHaveBeenCalled();
    expect(userService.create).not.toHaveBeenCalled();
  });

  it('should show warning when username exists', fakeAsync(() => {
    const mockUser: User = {
      id: 1,
      username: 'existingUser',
      profiles: mockUserProfiles,
    };
    userService.getByUsername.and.returnValue(of(mockUser));

    component.userForm.patchValue({
      username: 'existingUser',
      profileIds: [1],
    });

    component.onSubmit();
    tick();

    expect(spinner.show).toHaveBeenCalled();
    expect(userService.getByUsername).toHaveBeenCalledWith('existingUser');
    expect(alertService.warning).toHaveBeenCalledWith(
      'El usuario ya existe en el sistema',
    );
    expect(spinner.hide).toHaveBeenCalled();
  }));

  it('should create user when username does not exist', fakeAsync(() => {
    const mockError = new HttpErrorResponse({ status: 404 });
    userService.getByUsername.and.returnValue(throwError(() => mockError));

    const mockCreatedUser: UserProfileAssignment = {
      userId: 1,
      username: 'newUser',
      profiles: mockProfiles,
    };
    userService.create.and.returnValue(of(mockCreatedUser));

    component.userForm.patchValue({
      username: 'newUser',
      profileIds: [1],
    });

    component.onSubmit();
    tick();

    expect(userService.create).toHaveBeenCalledWith({
      username: 'newUser',
      profile_ids: [1],
    });
    expect(alertService.success).toHaveBeenCalledWith(
      'Usuario creado correctamente',
    );
    expect(dialogRef.close).toHaveBeenCalledWith(mockCreatedUser);
  }));

  it('should handle error when creating user', fakeAsync(() => {
    const mockError = new HttpErrorResponse({ status: 404 });
    userService.getByUsername.and.returnValue(throwError(() => mockError));
    userService.create.and.returnValue(throwError(() => ({ error: 'Error' })));

    component.userForm.patchValue({
      username: 'newUser',
      profileIds: [1],
    });

    component.onSubmit();
    tick();

    expect(alertService.error).toHaveBeenCalledWith(
      'Error al crear el usuario',
    );
    expect(spinner.hide).toHaveBeenCalled();
  }));

  it('should handle error when verifying username', fakeAsync(() => {
    const mockError = new HttpErrorResponse({ status: 500 });
    userService.getByUsername.and.returnValue(throwError(() => mockError));

    component.userForm.patchValue({
      username: 'newUser',
      profileIds: [1],
    });

    component.onSubmit();
    tick();

    expect(alertService.error).toHaveBeenCalledWith(
      'Error al verificar el usuario',
    );
    expect(spinner.hide).toHaveBeenCalled();
  }));
});