{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { Router } from '@angular/router';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { profileGuard } from './profile.guard';\ndescribe('profileGuard', () => {\n  let router;\n  let authService;\n  let mockRoute;\n  let mockState;\n  beforeEach(() => {\n    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);\n    const authServiceSpy = jasmine.createSpyObj('AuthService', ['getUserProfiles']);\n    mockRoute = {\n      data: {\n        profiles: ['admin', 'supervisor']\n      },\n      params: {},\n      queryParams: {},\n      fragment: null,\n      outlet: 'primary',\n      component: null,\n      routeConfig: null,\n      root: null,\n      parent: null,\n      firstChild: null,\n      children: [],\n      pathFromRoot: [],\n      paramMap: {\n        has: jasmine.createSpy('has'),\n        get: jasmine.createSpy('get'),\n        getAll: jasmine.createSpy('getAll'),\n        keys: jasmine.createSpy('keys')\n      },\n      queryParamMap: {\n        has: jasmine.createSpy('has'),\n        get: jasmine.createSpy('get'),\n        getAll: jasmine.createSpy('getAll'),\n        keys: jasmine.createSpy('keys')\n      },\n      url: [],\n      title: null\n    };\n    mockState = {\n      url: '/test'\n    };\n    TestBed.configureTestingModule({\n      providers: [{\n        provide: Router,\n        useValue: routerSpy\n      }, {\n        provide: AuthService,\n        useValue: authServiceSpy\n      }]\n    });\n    router = TestBed.inject(Router);\n    authService = TestBed.inject(AuthService);\n  });\n  it('should be created', () => {\n    expect(profileGuard).toBeTruthy();\n  });\n  it('should allow access when user has required profile', () => {\n    authService.getUserProfiles.and.returnValue([{\n      profile_name: 'admin',\n      profile_id: 1\n    }]);\n    const result = TestBed.runInInjectionContext(() => profileGuard(mockRoute, mockState));\n    expect(result).toBe(true);\n    expect(router.navigate).not.toHaveBeenCalled();\n  });\n  it('should allow access when user has any of the required profiles', () => {\n    authService.getUserProfiles.and.returnValue([{\n      profile_name: 'supervisor',\n      profile_id: 2\n    }]);\n    const result = TestBed.runInInjectionContext(() => profileGuard(mockRoute, mockState));\n    expect(result).toBe(true);\n    expect(router.navigate).not.toHaveBeenCalled();\n  });\n  it('should deny access when user has no required profiles', () => {\n    authService.getUserProfiles.and.returnValue([{\n      profile_name: 'user',\n      profile_id: 3\n    }]);\n    const result = TestBed.runInInjectionContext(() => profileGuard(mockRoute, mockState));\n    expect(result).toBe(false);\n    expect(router.navigate).toHaveBeenCalledWith(['/unauthorized']);\n  });\n  it('should deny access when user has no profiles', () => {\n    authService.getUserProfiles.and.returnValue([]);\n    const result = TestBed.runInInjectionContext(() => profileGuard(mockRoute, mockState));\n    expect(result).toBe(false);\n    expect(router.navigate).toHaveBeenCalledWith(['/unauthorized']);\n  });\n  it('should handle route without profiles data', () => {\n    const routeWithoutProfiles = {\n      ...mockRoute,\n      data: {\n        profiles: []\n      }\n    };\n    authService.getUserProfiles.and.returnValue([{\n      profile_name: 'admin',\n      profile_id: 1\n    }]);\n    const result = TestBed.runInInjectionContext(() => profileGuard(routeWithoutProfiles, mockState));\n    expect(result).toBe(false);\n    expect(router.navigate).toHaveBeenCalledWith(['/unauthorized']);\n  });\n});", "map": {"version": 3, "names": ["TestBed", "Router", "AuthService", "profile<PERSON><PERSON>", "describe", "router", "authService", "mockRoute", "mockState", "beforeEach", "routerSpy", "jasmine", "createSpyObj", "authServiceSpy", "data", "profiles", "params", "queryParams", "fragment", "outlet", "component", "routeConfig", "root", "parent", "<PERSON><PERSON><PERSON><PERSON>", "children", "pathFromRoot", "paramMap", "has", "createSpy", "get", "getAll", "keys", "queryParamMap", "url", "title", "configureTestingModule", "providers", "provide", "useValue", "inject", "it", "expect", "toBeTruthy", "getUserProfiles", "and", "returnValue", "profile_name", "profile_id", "result", "runInInjectionContext", "toBe", "navigate", "not", "toHaveBeenCalled", "toHaveBeenCalledWith", "routeWithoutProfiles"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\core\\auth\\guards\\profile.guard.spec.ts"], "sourcesContent": ["import { TestBed } from '@angular/core/testing';\nimport {\n  ActivatedRouteSnapshot,\n  Router,\n  RouterStateSnapshot,\n} from '@angular/router';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { profileGuard } from './profile.guard';\n\ndescribe('profileGuard', () => {\n  let router: jasmine.SpyObj<Router>;\n  let authService: jasmine.SpyObj<AuthService>;\n  let mockRoute: ActivatedRouteSnapshot;\n  let mockState: RouterStateSnapshot;\n\n  beforeEach(() => {\n    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);\n    const authServiceSpy = jasmine.createSpyObj('AuthService', [\n      'getUserProfiles',\n    ]);\n\n    mockRoute = {\n      data: {\n        profiles: ['admin', 'supervisor'],\n      },\n      params: {},\n      queryParams: {},\n      fragment: null,\n      outlet: 'primary',\n      component: null,\n      routeConfig: null,\n      root: null,\n      parent: null,\n      firstChild: null,\n      children: [],\n      pathFromRoot: [],\n      paramMap: {\n        has: jasmine.createSpy('has'),\n        get: jasmine.createSpy('get'),\n        getAll: jasmine.createSpy('getAll'),\n        keys: jasmine.createSpy('keys'),\n      },\n      queryParamMap: {\n        has: jasmine.createSpy('has'),\n        get: jasmine.createSpy('get'),\n        getAll: jasmine.createSpy('getAll'),\n        keys: jasmine.createSpy('keys'),\n      },\n      url: [],\n      title: null,\n    } as unknown as ActivatedRouteSnapshot;\n\n    mockState = { url: '/test' } as RouterStateSnapshot;\n\n    TestBed.configureTestingModule({\n      providers: [\n        { provide: Router, useValue: routerSpy },\n        { provide: AuthService, useValue: authServiceSpy },\n      ],\n    });\n\n    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;\n    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;\n  });\n\n  it('should be created', () => {\n    expect(profileGuard).toBeTruthy();\n  });\n\n  it('should allow access when user has required profile', () => {\n    authService.getUserProfiles.and.returnValue([\n      { profile_name: 'admin', profile_id: 1 },\n    ]);\n\n    const result = TestBed.runInInjectionContext(() =>\n      profileGuard(mockRoute, mockState),\n    );\n\n    expect(result).toBe(true);\n    expect(router.navigate).not.toHaveBeenCalled();\n  });\n\n  it('should allow access when user has any of the required profiles', () => {\n    authService.getUserProfiles.and.returnValue([\n      { profile_name: 'supervisor', profile_id: 2 },\n    ]);\n\n    const result = TestBed.runInInjectionContext(() =>\n      profileGuard(mockRoute, mockState),\n    );\n\n    expect(result).toBe(true);\n    expect(router.navigate).not.toHaveBeenCalled();\n  });\n\n  it('should deny access when user has no required profiles', () => {\n    authService.getUserProfiles.and.returnValue([\n      { profile_name: 'user', profile_id: 3 },\n    ]);\n\n    const result = TestBed.runInInjectionContext(() =>\n      profileGuard(mockRoute, mockState),\n    );\n\n    expect(result).toBe(false);\n    expect(router.navigate).toHaveBeenCalledWith(['/unauthorized']);\n  });\n\n  it('should deny access when user has no profiles', () => {\n    authService.getUserProfiles.and.returnValue([]);\n\n    const result = TestBed.runInInjectionContext(() =>\n      profileGuard(mockRoute, mockState),\n    );\n\n    expect(result).toBe(false);\n    expect(router.navigate).toHaveBeenCalledWith(['/unauthorized']);\n  });\n\n  it('should handle route without profiles data', () => {\n    const routeWithoutProfiles = {\n      ...mockRoute,\n      data: { profiles: [] },\n    } as unknown as ActivatedRouteSnapshot;\n    authService.getUserProfiles.and.returnValue([\n      { profile_name: 'admin', profile_id: 1 },\n    ]);\n\n    const result = TestBed.runInInjectionContext(() =>\n      profileGuard(routeWithoutProfiles, mockState),\n    );\n\n    expect(result).toBe(false);\n    expect(router.navigate).toHaveBeenCalledWith(['/unauthorized']);\n  });\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,uBAAuB;AAC/C,SAEEC,MAAM,QAED,iBAAiB;AACxB,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,SAASC,YAAY,QAAQ,iBAAiB;AAE9CC,QAAQ,CAAC,cAAc,EAAE,MAAK;EAC5B,IAAIC,MAA8B;EAClC,IAAIC,WAAwC;EAC5C,IAAIC,SAAiC;EACrC,IAAIC,SAA8B;EAElCC,UAAU,CAAC,MAAK;IACd,MAAMC,SAAS,GAAGC,OAAO,CAACC,YAAY,CAAC,QAAQ,EAAE,CAAC,UAAU,CAAC,CAAC;IAC9D,MAAMC,cAAc,GAAGF,OAAO,CAACC,YAAY,CAAC,aAAa,EAAE,CACzD,iBAAiB,CAClB,CAAC;IAEFL,SAAS,GAAG;MACVO,IAAI,EAAE;QACJC,QAAQ,EAAE,CAAC,OAAO,EAAE,YAAY;OACjC;MACDC,MAAM,EAAE,EAAE;MACVC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,SAAS;MACjBC,SAAS,EAAE,IAAI;MACfC,WAAW,EAAE,IAAI;MACjBC,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,IAAI;MACZC,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE,EAAE;MAChBC,QAAQ,EAAE;QACRC,GAAG,EAAEjB,OAAO,CAACkB,SAAS,CAAC,KAAK,CAAC;QAC7BC,GAAG,EAAEnB,OAAO,CAACkB,SAAS,CAAC,KAAK,CAAC;QAC7BE,MAAM,EAAEpB,OAAO,CAACkB,SAAS,CAAC,QAAQ,CAAC;QACnCG,IAAI,EAAErB,OAAO,CAACkB,SAAS,CAAC,MAAM;OAC/B;MACDI,aAAa,EAAE;QACbL,GAAG,EAAEjB,OAAO,CAACkB,SAAS,CAAC,KAAK,CAAC;QAC7BC,GAAG,EAAEnB,OAAO,CAACkB,SAAS,CAAC,KAAK,CAAC;QAC7BE,MAAM,EAAEpB,OAAO,CAACkB,SAAS,CAAC,QAAQ,CAAC;QACnCG,IAAI,EAAErB,OAAO,CAACkB,SAAS,CAAC,MAAM;OAC/B;MACDK,GAAG,EAAE,EAAE;MACPC,KAAK,EAAE;KAC6B;IAEtC3B,SAAS,GAAG;MAAE0B,GAAG,EAAE;IAAO,CAAyB;IAEnDlC,OAAO,CAACoC,sBAAsB,CAAC;MAC7BC,SAAS,EAAE,CACT;QAAEC,OAAO,EAAErC,MAAM;QAAEsC,QAAQ,EAAE7B;MAAS,CAAE,EACxC;QAAE4B,OAAO,EAAEpC,WAAW;QAAEqC,QAAQ,EAAE1B;MAAc,CAAE;KAErD,CAAC;IAEFR,MAAM,GAAGL,OAAO,CAACwC,MAAM,CAACvC,MAAM,CAA2B;IACzDK,WAAW,GAAGN,OAAO,CAACwC,MAAM,CAACtC,WAAW,CAAgC;EAC1E,CAAC,CAAC;EAEFuC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACvC,YAAY,CAAC,CAACwC,UAAU,EAAE;EACnC,CAAC,CAAC;EAEFF,EAAE,CAAC,oDAAoD,EAAE,MAAK;IAC5DnC,WAAW,CAACsC,eAAe,CAACC,GAAG,CAACC,WAAW,CAAC,CAC1C;MAAEC,YAAY,EAAE,OAAO;MAAEC,UAAU,EAAE;IAAC,CAAE,CACzC,CAAC;IAEF,MAAMC,MAAM,GAAGjD,OAAO,CAACkD,qBAAqB,CAAC,MAC3C/C,YAAY,CAACI,SAAS,EAAEC,SAAS,CAAC,CACnC;IAEDkC,MAAM,CAACO,MAAM,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC;IACzBT,MAAM,CAACrC,MAAM,CAAC+C,QAAQ,CAAC,CAACC,GAAG,CAACC,gBAAgB,EAAE;EAChD,CAAC,CAAC;EAEFb,EAAE,CAAC,gEAAgE,EAAE,MAAK;IACxEnC,WAAW,CAACsC,eAAe,CAACC,GAAG,CAACC,WAAW,CAAC,CAC1C;MAAEC,YAAY,EAAE,YAAY;MAAEC,UAAU,EAAE;IAAC,CAAE,CAC9C,CAAC;IAEF,MAAMC,MAAM,GAAGjD,OAAO,CAACkD,qBAAqB,CAAC,MAC3C/C,YAAY,CAACI,SAAS,EAAEC,SAAS,CAAC,CACnC;IAEDkC,MAAM,CAACO,MAAM,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC;IACzBT,MAAM,CAACrC,MAAM,CAAC+C,QAAQ,CAAC,CAACC,GAAG,CAACC,gBAAgB,EAAE;EAChD,CAAC,CAAC;EAEFb,EAAE,CAAC,uDAAuD,EAAE,MAAK;IAC/DnC,WAAW,CAACsC,eAAe,CAACC,GAAG,CAACC,WAAW,CAAC,CAC1C;MAAEC,YAAY,EAAE,MAAM;MAAEC,UAAU,EAAE;IAAC,CAAE,CACxC,CAAC;IAEF,MAAMC,MAAM,GAAGjD,OAAO,CAACkD,qBAAqB,CAAC,MAC3C/C,YAAY,CAACI,SAAS,EAAEC,SAAS,CAAC,CACnC;IAEDkC,MAAM,CAACO,MAAM,CAAC,CAACE,IAAI,CAAC,KAAK,CAAC;IAC1BT,MAAM,CAACrC,MAAM,CAAC+C,QAAQ,CAAC,CAACG,oBAAoB,CAAC,CAAC,eAAe,CAAC,CAAC;EACjE,CAAC,CAAC;EAEFd,EAAE,CAAC,8CAA8C,EAAE,MAAK;IACtDnC,WAAW,CAACsC,eAAe,CAACC,GAAG,CAACC,WAAW,CAAC,EAAE,CAAC;IAE/C,MAAMG,MAAM,GAAGjD,OAAO,CAACkD,qBAAqB,CAAC,MAC3C/C,YAAY,CAACI,SAAS,EAAEC,SAAS,CAAC,CACnC;IAEDkC,MAAM,CAACO,MAAM,CAAC,CAACE,IAAI,CAAC,KAAK,CAAC;IAC1BT,MAAM,CAACrC,MAAM,CAAC+C,QAAQ,CAAC,CAACG,oBAAoB,CAAC,CAAC,eAAe,CAAC,CAAC;EACjE,CAAC,CAAC;EAEFd,EAAE,CAAC,2CAA2C,EAAE,MAAK;IACnD,MAAMe,oBAAoB,GAAG;MAC3B,GAAGjD,SAAS;MACZO,IAAI,EAAE;QAAEC,QAAQ,EAAE;MAAE;KACgB;IACtCT,WAAW,CAACsC,eAAe,CAACC,GAAG,CAACC,WAAW,CAAC,CAC1C;MAAEC,YAAY,EAAE,OAAO;MAAEC,UAAU,EAAE;IAAC,CAAE,CACzC,CAAC;IAEF,MAAMC,MAAM,GAAGjD,OAAO,CAACkD,qBAAqB,CAAC,MAC3C/C,YAAY,CAACqD,oBAAoB,EAAEhD,SAAS,CAAC,CAC9C;IAEDkC,MAAM,CAACO,MAAM,CAAC,CAACE,IAAI,CAAC,KAAK,CAAC;IAC1BT,MAAM,CAACrC,MAAM,CAAC+C,QAAQ,CAAC,CAACG,oBAAoB,CAAC,CAAC,eAAe,CAAC,CAAC;EACjE,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}