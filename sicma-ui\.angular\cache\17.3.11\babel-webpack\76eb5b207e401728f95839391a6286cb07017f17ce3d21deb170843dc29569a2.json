{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { SocialSecurityContributionService } from './social-security-contribution.service';\ndescribe('SocialSecurityContributionService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/social-security-contributions`;\n  const mockSocialSecurityContribution = {\n    id: 1,\n    monthlyReportId: 1,\n    healthContribution: 1000000,\n    pensionContribution: 1000000,\n    arlContribution: 1000000,\n    compensationFundContribution: 1000000,\n    ibc: 1000000,\n    paymentFormNumber: 12345,\n    epsId: 1,\n    arlId: 1,\n    pensionFundId: 1,\n    arlAffiliationClassId: 1,\n    compensationFundId: 1,\n    certificateFileUrl: 'path/to/file',\n    certificateFileKey: 'file-key'\n  };\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [SocialSecurityContributionService]\n    });\n    service = TestBed.inject(SocialSecurityContributionService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all social security contributions', () => {\n      const mockContributions = [mockSocialSecurityContribution];\n      service.getAll().subscribe(contributions => {\n        expect(contributions).toEqual(mockContributions);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContributions);\n    });\n    it('should handle error when getting all social security contributions', () => {\n      service.getAll().subscribe({\n        error: error => {\n          expect(error.status).toBe(500);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Server error'), {\n        status: 500\n      });\n    });\n  });\n  describe('getById', () => {\n    it('should return a social security contribution by id', () => {\n      const id = 1;\n      service.getById(id).subscribe(contribution => {\n        expect(contribution).toEqual(mockSocialSecurityContribution);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockSocialSecurityContribution);\n    });\n    it('should handle error when getting social security contribution by id', () => {\n      const id = 999;\n      service.getById(id).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n  describe('getByMonthlyReportId', () => {\n    it('should return a social security contribution by monthly report id', () => {\n      const monthlyReportId = 1;\n      service.getByMonthlyReportId(monthlyReportId).subscribe(contribution => {\n        expect(contribution).toEqual(mockSocialSecurityContribution);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/monthly-report/${monthlyReportId}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockSocialSecurityContribution);\n    });\n    it('should handle error when getting social security contribution by monthly report id', () => {\n      const monthlyReportId = 999;\n      service.getByMonthlyReportId(monthlyReportId).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/monthly-report/${monthlyReportId}`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n  describe('create', () => {\n    it('should create a new social security contribution', () => {\n      const newContribution = {\n        monthlyReportId: 1,\n        healthContribution: 1000000,\n        pensionContribution: 1000000,\n        arlContribution: 1000000,\n        compensationFundContribution: 1000000,\n        ibc: 1000000,\n        paymentFormNumber: 12345,\n        epsId: 1,\n        arlId: 1,\n        pensionFundId: 1,\n        arlAffiliationClassId: 1,\n        compensationFundId: 1,\n        certificateFileUrl: 'path/to/file',\n        certificateFileKey: 'file-key'\n      };\n      service.create(newContribution).subscribe(contribution => {\n        expect(contribution).toEqual(mockSocialSecurityContribution);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newContribution);\n      req.flush(mockSocialSecurityContribution);\n    });\n    it('should handle error when creating social security contribution', () => {\n      const newContribution = {\n        monthlyReportId: 1,\n        healthContribution: 1000000,\n        pensionContribution: 1000000,\n        arlContribution: 1000000,\n        compensationFundContribution: 1000000,\n        ibc: 1000000,\n        paymentFormNumber: 12345,\n        epsId: 1,\n        arlId: 1,\n        pensionFundId: 1,\n        arlAffiliationClassId: 1,\n        compensationFundId: 1,\n        certificateFileUrl: 'path/to/file',\n        certificateFileKey: 'file-key'\n      };\n      service.create(newContribution).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n  describe('update', () => {\n    it('should update a social security contribution', () => {\n      const id = 1;\n      const updateData = {\n        healthContribution: 2000000,\n        pensionContribution: 2000000\n      };\n      service.update(id, updateData).subscribe(contribution => {\n        expect(contribution).toEqual(mockSocialSecurityContribution);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockSocialSecurityContribution);\n    });\n    it('should handle error when updating social security contribution', () => {\n      const id = 1;\n      const updateData = {\n        healthContribution: 2000000,\n        pensionContribution: 2000000\n      };\n      service.update(id, updateData).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n  describe('delete', () => {\n    it('should delete a social security contribution', () => {\n      const id = 1;\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n    it('should handle error when deleting social security contribution', () => {\n      const id = 1;\n      service.delete(id).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n  describe('createWithFile', () => {\n    it('should create a social security contribution with file', () => {\n      const formData = new FormData();\n      formData.append('file', new File([''], 'test.pdf'));\n      formData.append('data', JSON.stringify({\n        monthlyReportId: 1,\n        healthContribution: 1000000,\n        pensionContribution: 1000000,\n        arlContribution: 1000000,\n        compensationFundContribution: 1000000,\n        ibc: 1000000,\n        paymentFormNumber: 12345,\n        arlAffiliationClassId: 1,\n        compensationFundId: 1\n      }));\n      service.createWithFile(formData).subscribe(contribution => {\n        expect(contribution).toEqual(mockSocialSecurityContribution);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/with-file`);\n      expect(req.request.method).toBe('POST');\n      req.flush(mockSocialSecurityContribution);\n    });\n    it('should handle error when creating social security contribution with file', () => {\n      const formData = new FormData();\n      formData.append('file', new File([''], 'test.pdf'));\n      formData.append('data', JSON.stringify({\n        monthlyReportId: 1,\n        healthContribution: 1000000,\n        pensionContribution: 1000000,\n        arlContribution: 1000000,\n        compensationFundContribution: 1000000,\n        ibc: 1000000,\n        paymentFormNumber: 12345,\n        arlAffiliationClassId: 1,\n        compensationFundId: 1\n      }));\n      service.createWithFile(formData).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/with-file`);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n  describe('updateWithFile', () => {\n    it('should update a social security contribution with file', () => {\n      const id = 1;\n      const formData = new FormData();\n      formData.append('file', new File([''], 'test.pdf'));\n      formData.append('data', JSON.stringify({\n        monthlyReportId: 1,\n        healthContribution: 1000000,\n        pensionContribution: 1000000,\n        arlContribution: 1000000,\n        compensationFundContribution: 1000000,\n        ibc: 1000000,\n        paymentFormNumber: 12345,\n        arlAffiliationClassId: 1,\n        compensationFundId: 1\n      }));\n      service.updateWithFile(id, formData).subscribe(contribution => {\n        expect(contribution).toEqual(mockSocialSecurityContribution);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}/with-file`);\n      expect(req.request.method).toBe('PUT');\n      req.flush(mockSocialSecurityContribution);\n    });\n    it('should handle error when updating social security contribution with file', () => {\n      const id = 1;\n      const formData = new FormData();\n      formData.append('file', new File([''], 'test.pdf'));\n      formData.append('data', JSON.stringify({\n        monthlyReportId: 1,\n        healthContribution: 1000000,\n        pensionContribution: 1000000,\n        arlContribution: 1000000,\n        compensationFundContribution: 1000000,\n        ibc: 1000000,\n        paymentFormNumber: 12345,\n        arlAffiliationClassId: 1,\n        compensationFundId: 1\n      }));\n      service.updateWithFile(id, formData).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}/with-file`);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "SocialSecurityContributionService", "describe", "service", "httpMock", "apiUrl", "mockSocialSecurityContribution", "id", "monthlyReportId", "healthContribution", "pensionContribution", "arlContribution", "compensationFundContribution", "ibc", "paymentFormNumber", "epsId", "arlId", "pensionFundId", "arlAffiliationClassId", "compensationFundId", "certificateFileUrl", "certificateFileKey", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockContributions", "getAll", "subscribe", "contributions", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "error", "status", "ErrorEvent", "getById", "contribution", "getByMonthlyReportId", "newContribution", "create", "body", "updateData", "update", "delete", "nothing", "formData", "FormData", "append", "File", "JSON", "stringify", "createWithFile", "updateWithFile"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\services\\social-security-contribution.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { SocialSecurityContribution } from '@contractor-dashboard/models/social-security-contribution.model';\nimport { environment } from '@env';\nimport { SocialSecurityContributionService } from './social-security-contribution.service';\n\ndescribe('SocialSecurityContributionService', () => {\n  let service: SocialSecurityContributionService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/social-security-contributions`;\n\n  const mockSocialSecurityContribution: SocialSecurityContribution = {\n    id: 1,\n    monthlyReportId: 1,\n    healthContribution: 1000000,\n    pensionContribution: 1000000,\n    arlContribution: 1000000,\n    compensationFundContribution: 1000000,\n    ibc: 1000000,\n    paymentFormNumber: 12345,\n    epsId: 1,\n    arlId: 1,\n    pensionFundId: 1,\n    arlAffiliationClassId: 1,\n    compensationFundId: 1,\n    certificateFileUrl: 'path/to/file',\n    certificateFileKey: 'file-key',\n  };\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [SocialSecurityContributionService],\n    });\n    service = TestBed.inject(SocialSecurityContributionService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all social security contributions', () => {\n      const mockContributions = [mockSocialSecurityContribution];\n\n      service.getAll().subscribe((contributions) => {\n        expect(contributions).toEqual(mockContributions);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContributions);\n    });\n\n    it('should handle error when getting all social security contributions', () => {\n      service.getAll().subscribe({\n        error: (error) => {\n          expect(error.status).toBe(500);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Server error'), { status: 500 });\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a social security contribution by id', () => {\n      const id = 1;\n\n      service.getById(id).subscribe((contribution) => {\n        expect(contribution).toEqual(mockSocialSecurityContribution);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockSocialSecurityContribution);\n    });\n\n    it('should handle error when getting social security contribution by id', () => {\n      const id = 999;\n\n      service.getById(id).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n\n  describe('getByMonthlyReportId', () => {\n    it('should return a social security contribution by monthly report id', () => {\n      const monthlyReportId = 1;\n\n      service\n        .getByMonthlyReportId(monthlyReportId)\n        .subscribe((contribution) => {\n          expect(contribution).toEqual(mockSocialSecurityContribution);\n        });\n\n      const req = httpMock.expectOne(\n        `${apiUrl}/monthly-report/${monthlyReportId}`,\n      );\n      expect(req.request.method).toBe('GET');\n      req.flush(mockSocialSecurityContribution);\n    });\n\n    it('should handle error when getting social security contribution by monthly report id', () => {\n      const monthlyReportId = 999;\n\n      service.getByMonthlyReportId(monthlyReportId).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(\n        `${apiUrl}/monthly-report/${monthlyReportId}`,\n      );\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n\n  describe('create', () => {\n    it('should create a new social security contribution', () => {\n      const newContribution: Omit<SocialSecurityContribution, 'id'> = {\n        monthlyReportId: 1,\n        healthContribution: 1000000,\n        pensionContribution: 1000000,\n        arlContribution: 1000000,\n        compensationFundContribution: 1000000,\n        ibc: 1000000,\n        paymentFormNumber: 12345,\n        epsId: 1,\n        arlId: 1,\n        pensionFundId: 1,\n        arlAffiliationClassId: 1,\n        compensationFundId: 1,\n        certificateFileUrl: 'path/to/file',\n        certificateFileKey: 'file-key',\n      };\n\n      service.create(newContribution).subscribe((contribution) => {\n        expect(contribution).toEqual(mockSocialSecurityContribution);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newContribution);\n      req.flush(mockSocialSecurityContribution);\n    });\n\n    it('should handle error when creating social security contribution', () => {\n      const newContribution: Omit<SocialSecurityContribution, 'id'> = {\n        monthlyReportId: 1,\n        healthContribution: 1000000,\n        pensionContribution: 1000000,\n        arlContribution: 1000000,\n        compensationFundContribution: 1000000,\n        ibc: 1000000,\n        paymentFormNumber: 12345,\n        epsId: 1,\n        arlId: 1,\n        pensionFundId: 1,\n        arlAffiliationClassId: 1,\n        compensationFundId: 1,\n        certificateFileUrl: 'path/to/file',\n        certificateFileKey: 'file-key',\n      };\n\n      service.create(newContribution).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n\n  describe('update', () => {\n    it('should update a social security contribution', () => {\n      const id = 1;\n      const updateData: Partial<SocialSecurityContribution> = {\n        healthContribution: 2000000,\n        pensionContribution: 2000000,\n      };\n\n      service.update(id, updateData).subscribe((contribution) => {\n        expect(contribution).toEqual(mockSocialSecurityContribution);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockSocialSecurityContribution);\n    });\n\n    it('should handle error when updating social security contribution', () => {\n      const id = 1;\n      const updateData: Partial<SocialSecurityContribution> = {\n        healthContribution: 2000000,\n        pensionContribution: 2000000,\n      };\n\n      service.update(id, updateData).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete a social security contribution', () => {\n      const id = 1;\n\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n\n    it('should handle error when deleting social security contribution', () => {\n      const id = 1;\n\n      service.delete(id).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n\n  describe('createWithFile', () => {\n    it('should create a social security contribution with file', () => {\n      const formData = new FormData();\n      formData.append('file', new File([''], 'test.pdf'));\n      formData.append(\n        'data',\n        JSON.stringify({\n          monthlyReportId: 1,\n          healthContribution: 1000000,\n          pensionContribution: 1000000,\n          arlContribution: 1000000,\n          compensationFundContribution: 1000000,\n          ibc: 1000000,\n          paymentFormNumber: 12345,\n          arlAffiliationClassId: 1,\n          compensationFundId: 1,\n        }),\n      );\n\n      service.createWithFile(formData).subscribe((contribution) => {\n        expect(contribution).toEqual(mockSocialSecurityContribution);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/with-file`);\n      expect(req.request.method).toBe('POST');\n      req.flush(mockSocialSecurityContribution);\n    });\n\n    it('should handle error when creating social security contribution with file', () => {\n      const formData = new FormData();\n      formData.append('file', new File([''], 'test.pdf'));\n      formData.append(\n        'data',\n        JSON.stringify({\n          monthlyReportId: 1,\n          healthContribution: 1000000,\n          pensionContribution: 1000000,\n          arlContribution: 1000000,\n          compensationFundContribution: 1000000,\n          ibc: 1000000,\n          paymentFormNumber: 12345,\n          arlAffiliationClassId: 1,\n          compensationFundId: 1,\n        }),\n      );\n\n      service.createWithFile(formData).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/with-file`);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n\n  describe('updateWithFile', () => {\n    it('should update a social security contribution with file', () => {\n      const id = 1;\n      const formData = new FormData();\n      formData.append('file', new File([''], 'test.pdf'));\n      formData.append(\n        'data',\n        JSON.stringify({\n          monthlyReportId: 1,\n          healthContribution: 1000000,\n          pensionContribution: 1000000,\n          arlContribution: 1000000,\n          compensationFundContribution: 1000000,\n          ibc: 1000000,\n          paymentFormNumber: 12345,\n          arlAffiliationClassId: 1,\n          compensationFundId: 1,\n        }),\n      );\n\n      service.updateWithFile(id, formData).subscribe((contribution) => {\n        expect(contribution).toEqual(mockSocialSecurityContribution);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}/with-file`);\n      expect(req.request.method).toBe('PUT');\n      req.flush(mockSocialSecurityContribution);\n    });\n\n    it('should handle error when updating social security contribution with file', () => {\n      const id = 1;\n      const formData = new FormData();\n      formData.append('file', new File([''], 'test.pdf'));\n      formData.append(\n        'data',\n        JSON.stringify({\n          monthlyReportId: 1,\n          healthContribution: 1000000,\n          pensionContribution: 1000000,\n          arlContribution: 1000000,\n          compensationFundContribution: 1000000,\n          ibc: 1000000,\n          paymentFormNumber: 12345,\n          arlAffiliationClassId: 1,\n          compensationFundId: 1,\n        }),\n      );\n\n      service.updateWithFile(id, formData).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}/with-file`);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,iCAAiC,QAAQ,wCAAwC;AAE1FC,QAAQ,CAAC,mCAAmC,EAAE,MAAK;EACjD,IAAIC,OAA0C;EAC9C,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,gCAAgC;EAEpE,MAAMC,8BAA8B,GAA+B;IACjEC,EAAE,EAAE,CAAC;IACLC,eAAe,EAAE,CAAC;IAClBC,kBAAkB,EAAE,OAAO;IAC3BC,mBAAmB,EAAE,OAAO;IAC5BC,eAAe,EAAE,OAAO;IACxBC,4BAA4B,EAAE,OAAO;IACrCC,GAAG,EAAE,OAAO;IACZC,iBAAiB,EAAE,KAAK;IACxBC,KAAK,EAAE,CAAC;IACRC,KAAK,EAAE,CAAC;IACRC,aAAa,EAAE,CAAC;IAChBC,qBAAqB,EAAE,CAAC;IACxBC,kBAAkB,EAAE,CAAC;IACrBC,kBAAkB,EAAE,cAAc;IAClCC,kBAAkB,EAAE;GACrB;EAEDC,UAAU,CAAC,MAAK;IACdvB,OAAO,CAACwB,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAAC3B,uBAAuB,CAAC;MAClC4B,SAAS,EAAE,CAACxB,iCAAiC;KAC9C,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAAC2B,MAAM,CAACzB,iCAAiC,CAAC;IAC3DG,QAAQ,GAAGL,OAAO,CAAC2B,MAAM,CAAC5B,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEF6B,SAAS,CAAC,MAAK;IACbvB,QAAQ,CAACwB,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAAC3B,OAAO,CAAC,CAAC4B,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEF7B,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB2B,EAAE,CAAC,iDAAiD,EAAE,MAAK;MACzD,MAAMG,iBAAiB,GAAG,CAAC1B,8BAA8B,CAAC;MAE1DH,OAAO,CAAC8B,MAAM,EAAE,CAACC,SAAS,CAAEC,aAAa,IAAI;QAC3CL,MAAM,CAACK,aAAa,CAAC,CAACC,OAAO,CAACJ,iBAAiB,CAAC;MAClD,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGjC,QAAQ,CAACkC,SAAS,CAACjC,MAAM,CAAC;MACtCyB,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,iBAAiB,CAAC;IAC9B,CAAC,CAAC;IAEFH,EAAE,CAAC,oEAAoE,EAAE,MAAK;MAC5E1B,OAAO,CAAC8B,MAAM,EAAE,CAACC,SAAS,CAAC;QACzBS,KAAK,EAAGA,KAAK,IAAI;UACfb,MAAM,CAACa,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGjC,QAAQ,CAACkC,SAAS,CAACjC,MAAM,CAAC;MACtCgC,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,cAAc,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC5D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF1C,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvB2B,EAAE,CAAC,oDAAoD,EAAE,MAAK;MAC5D,MAAMtB,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAAC2C,OAAO,CAACvC,EAAE,CAAC,CAAC2B,SAAS,CAAEa,YAAY,IAAI;QAC7CjB,MAAM,CAACiB,YAAY,CAAC,CAACX,OAAO,CAAC9B,8BAA8B,CAAC;MAC9D,CAAC,CAAC;MAEF,MAAM+B,GAAG,GAAGjC,QAAQ,CAACkC,SAAS,CAAC,GAAGjC,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDuB,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACpC,8BAA8B,CAAC;IAC3C,CAAC,CAAC;IAEFuB,EAAE,CAAC,qEAAqE,EAAE,MAAK;MAC7E,MAAMtB,EAAE,GAAG,GAAG;MAEdJ,OAAO,CAAC2C,OAAO,CAACvC,EAAE,CAAC,CAAC2B,SAAS,CAAC;QAC5BS,KAAK,EAAGA,KAAK,IAAI;UACfb,MAAM,CAACa,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGjC,QAAQ,CAACkC,SAAS,CAAC,GAAGjC,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjD8B,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF1C,QAAQ,CAAC,sBAAsB,EAAE,MAAK;IACpC2B,EAAE,CAAC,mEAAmE,EAAE,MAAK;MAC3E,MAAMrB,eAAe,GAAG,CAAC;MAEzBL,OAAO,CACJ6C,oBAAoB,CAACxC,eAAe,CAAC,CACrC0B,SAAS,CAAEa,YAAY,IAAI;QAC1BjB,MAAM,CAACiB,YAAY,CAAC,CAACX,OAAO,CAAC9B,8BAA8B,CAAC;MAC9D,CAAC,CAAC;MAEJ,MAAM+B,GAAG,GAAGjC,QAAQ,CAACkC,SAAS,CAC5B,GAAGjC,MAAM,mBAAmBG,eAAe,EAAE,CAC9C;MACDsB,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACpC,8BAA8B,CAAC;IAC3C,CAAC,CAAC;IAEFuB,EAAE,CAAC,oFAAoF,EAAE,MAAK;MAC5F,MAAMrB,eAAe,GAAG,GAAG;MAE3BL,OAAO,CAAC6C,oBAAoB,CAACxC,eAAe,CAAC,CAAC0B,SAAS,CAAC;QACtDS,KAAK,EAAGA,KAAK,IAAI;UACfb,MAAM,CAACa,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGjC,QAAQ,CAACkC,SAAS,CAC5B,GAAGjC,MAAM,mBAAmBG,eAAe,EAAE,CAC9C;MACD6B,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF1C,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB2B,EAAE,CAAC,kDAAkD,EAAE,MAAK;MAC1D,MAAMoB,eAAe,GAA2C;QAC9DzC,eAAe,EAAE,CAAC;QAClBC,kBAAkB,EAAE,OAAO;QAC3BC,mBAAmB,EAAE,OAAO;QAC5BC,eAAe,EAAE,OAAO;QACxBC,4BAA4B,EAAE,OAAO;QACrCC,GAAG,EAAE,OAAO;QACZC,iBAAiB,EAAE,KAAK;QACxBC,KAAK,EAAE,CAAC;QACRC,KAAK,EAAE,CAAC;QACRC,aAAa,EAAE,CAAC;QAChBC,qBAAqB,EAAE,CAAC;QACxBC,kBAAkB,EAAE,CAAC;QACrBC,kBAAkB,EAAE,cAAc;QAClCC,kBAAkB,EAAE;OACrB;MAEDlB,OAAO,CAAC+C,MAAM,CAACD,eAAe,CAAC,CAACf,SAAS,CAAEa,YAAY,IAAI;QACzDjB,MAAM,CAACiB,YAAY,CAAC,CAACX,OAAO,CAAC9B,8BAA8B,CAAC;MAC9D,CAAC,CAAC;MAEF,MAAM+B,GAAG,GAAGjC,QAAQ,CAACkC,SAAS,CAACjC,MAAM,CAAC;MACtCyB,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACY,IAAI,CAAC,CAACf,OAAO,CAACa,eAAe,CAAC;MACjDZ,GAAG,CAACK,KAAK,CAACpC,8BAA8B,CAAC;IAC3C,CAAC,CAAC;IAEFuB,EAAE,CAAC,gEAAgE,EAAE,MAAK;MACxE,MAAMoB,eAAe,GAA2C;QAC9DzC,eAAe,EAAE,CAAC;QAClBC,kBAAkB,EAAE,OAAO;QAC3BC,mBAAmB,EAAE,OAAO;QAC5BC,eAAe,EAAE,OAAO;QACxBC,4BAA4B,EAAE,OAAO;QACrCC,GAAG,EAAE,OAAO;QACZC,iBAAiB,EAAE,KAAK;QACxBC,KAAK,EAAE,CAAC;QACRC,KAAK,EAAE,CAAC;QACRC,aAAa,EAAE,CAAC;QAChBC,qBAAqB,EAAE,CAAC;QACxBC,kBAAkB,EAAE,CAAC;QACrBC,kBAAkB,EAAE,cAAc;QAClCC,kBAAkB,EAAE;OACrB;MAEDlB,OAAO,CAAC+C,MAAM,CAACD,eAAe,CAAC,CAACf,SAAS,CAAC;QACxCS,KAAK,EAAGA,KAAK,IAAI;UACfb,MAAM,CAACa,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGjC,QAAQ,CAACkC,SAAS,CAACjC,MAAM,CAAC;MACtCgC,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF1C,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB2B,EAAE,CAAC,8CAA8C,EAAE,MAAK;MACtD,MAAMtB,EAAE,GAAG,CAAC;MACZ,MAAM6C,UAAU,GAAwC;QACtD3C,kBAAkB,EAAE,OAAO;QAC3BC,mBAAmB,EAAE;OACtB;MAEDP,OAAO,CAACkD,MAAM,CAAC9C,EAAE,EAAE6C,UAAU,CAAC,CAAClB,SAAS,CAAEa,YAAY,IAAI;QACxDjB,MAAM,CAACiB,YAAY,CAAC,CAACX,OAAO,CAAC9B,8BAA8B,CAAC;MAC9D,CAAC,CAAC;MAEF,MAAM+B,GAAG,GAAGjC,QAAQ,CAACkC,SAAS,CAAC,GAAGjC,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDuB,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACY,IAAI,CAAC,CAACf,OAAO,CAACgB,UAAU,CAAC;MAC5Cf,GAAG,CAACK,KAAK,CAACpC,8BAA8B,CAAC;IAC3C,CAAC,CAAC;IAEFuB,EAAE,CAAC,gEAAgE,EAAE,MAAK;MACxE,MAAMtB,EAAE,GAAG,CAAC;MACZ,MAAM6C,UAAU,GAAwC;QACtD3C,kBAAkB,EAAE,OAAO;QAC3BC,mBAAmB,EAAE;OACtB;MAEDP,OAAO,CAACkD,MAAM,CAAC9C,EAAE,EAAE6C,UAAU,CAAC,CAAClB,SAAS,CAAC;QACvCS,KAAK,EAAGA,KAAK,IAAI;UACfb,MAAM,CAACa,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGjC,QAAQ,CAACkC,SAAS,CAAC,GAAGjC,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjD8B,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF1C,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB2B,EAAE,CAAC,8CAA8C,EAAE,MAAK;MACtD,MAAMtB,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAACmD,MAAM,CAAC/C,EAAE,CAAC,CAAC2B,SAAS,CAAC,MAAK;QAChCJ,MAAM,EAAE,CAACyB,OAAO,EAAE;MACpB,CAAC,CAAC;MAEF,MAAMlB,GAAG,GAAGjC,QAAQ,CAACkC,SAAS,CAAC,GAAGjC,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDuB,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;IAEFb,EAAE,CAAC,gEAAgE,EAAE,MAAK;MACxE,MAAMtB,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAACmD,MAAM,CAAC/C,EAAE,CAAC,CAAC2B,SAAS,CAAC;QAC3BS,KAAK,EAAGA,KAAK,IAAI;UACfb,MAAM,CAACa,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGjC,QAAQ,CAACkC,SAAS,CAAC,GAAGjC,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjD8B,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF1C,QAAQ,CAAC,gBAAgB,EAAE,MAAK;IAC9B2B,EAAE,CAAC,wDAAwD,EAAE,MAAK;MAChE,MAAM2B,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE,IAAIC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;MACnDH,QAAQ,CAACE,MAAM,CACb,MAAM,EACNE,IAAI,CAACC,SAAS,CAAC;QACbrD,eAAe,EAAE,CAAC;QAClBC,kBAAkB,EAAE,OAAO;QAC3BC,mBAAmB,EAAE,OAAO;QAC5BC,eAAe,EAAE,OAAO;QACxBC,4BAA4B,EAAE,OAAO;QACrCC,GAAG,EAAE,OAAO;QACZC,iBAAiB,EAAE,KAAK;QACxBI,qBAAqB,EAAE,CAAC;QACxBC,kBAAkB,EAAE;OACrB,CAAC,CACH;MAEDhB,OAAO,CAAC2D,cAAc,CAACN,QAAQ,CAAC,CAACtB,SAAS,CAAEa,YAAY,IAAI;QAC1DjB,MAAM,CAACiB,YAAY,CAAC,CAACX,OAAO,CAAC9B,8BAA8B,CAAC;MAC9D,CAAC,CAAC;MAEF,MAAM+B,GAAG,GAAGjC,QAAQ,CAACkC,SAAS,CAAC,GAAGjC,MAAM,YAAY,CAAC;MACrDyB,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCJ,GAAG,CAACK,KAAK,CAACpC,8BAA8B,CAAC;IAC3C,CAAC,CAAC;IAEFuB,EAAE,CAAC,0EAA0E,EAAE,MAAK;MAClF,MAAM2B,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE,IAAIC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;MACnDH,QAAQ,CAACE,MAAM,CACb,MAAM,EACNE,IAAI,CAACC,SAAS,CAAC;QACbrD,eAAe,EAAE,CAAC;QAClBC,kBAAkB,EAAE,OAAO;QAC3BC,mBAAmB,EAAE,OAAO;QAC5BC,eAAe,EAAE,OAAO;QACxBC,4BAA4B,EAAE,OAAO;QACrCC,GAAG,EAAE,OAAO;QACZC,iBAAiB,EAAE,KAAK;QACxBI,qBAAqB,EAAE,CAAC;QACxBC,kBAAkB,EAAE;OACrB,CAAC,CACH;MAEDhB,OAAO,CAAC2D,cAAc,CAACN,QAAQ,CAAC,CAACtB,SAAS,CAAC;QACzCS,KAAK,EAAGA,KAAK,IAAI;UACfb,MAAM,CAACa,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGjC,QAAQ,CAACkC,SAAS,CAAC,GAAGjC,MAAM,YAAY,CAAC;MACrDgC,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF1C,QAAQ,CAAC,gBAAgB,EAAE,MAAK;IAC9B2B,EAAE,CAAC,wDAAwD,EAAE,MAAK;MAChE,MAAMtB,EAAE,GAAG,CAAC;MACZ,MAAMiD,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE,IAAIC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;MACnDH,QAAQ,CAACE,MAAM,CACb,MAAM,EACNE,IAAI,CAACC,SAAS,CAAC;QACbrD,eAAe,EAAE,CAAC;QAClBC,kBAAkB,EAAE,OAAO;QAC3BC,mBAAmB,EAAE,OAAO;QAC5BC,eAAe,EAAE,OAAO;QACxBC,4BAA4B,EAAE,OAAO;QACrCC,GAAG,EAAE,OAAO;QACZC,iBAAiB,EAAE,KAAK;QACxBI,qBAAqB,EAAE,CAAC;QACxBC,kBAAkB,EAAE;OACrB,CAAC,CACH;MAEDhB,OAAO,CAAC4D,cAAc,CAACxD,EAAE,EAAEiD,QAAQ,CAAC,CAACtB,SAAS,CAAEa,YAAY,IAAI;QAC9DjB,MAAM,CAACiB,YAAY,CAAC,CAACX,OAAO,CAAC9B,8BAA8B,CAAC;MAC9D,CAAC,CAAC;MAEF,MAAM+B,GAAG,GAAGjC,QAAQ,CAACkC,SAAS,CAAC,GAAGjC,MAAM,IAAIE,EAAE,YAAY,CAAC;MAC3DuB,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACpC,8BAA8B,CAAC;IAC3C,CAAC,CAAC;IAEFuB,EAAE,CAAC,0EAA0E,EAAE,MAAK;MAClF,MAAMtB,EAAE,GAAG,CAAC;MACZ,MAAMiD,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE,IAAIC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;MACnDH,QAAQ,CAACE,MAAM,CACb,MAAM,EACNE,IAAI,CAACC,SAAS,CAAC;QACbrD,eAAe,EAAE,CAAC;QAClBC,kBAAkB,EAAE,OAAO;QAC3BC,mBAAmB,EAAE,OAAO;QAC5BC,eAAe,EAAE,OAAO;QACxBC,4BAA4B,EAAE,OAAO;QACrCC,GAAG,EAAE,OAAO;QACZC,iBAAiB,EAAE,KAAK;QACxBI,qBAAqB,EAAE,CAAC;QACxBC,kBAAkB,EAAE;OACrB,CAAC,CACH;MAEDhB,OAAO,CAAC4D,cAAc,CAACxD,EAAE,EAAEiD,QAAQ,CAAC,CAACtB,SAAS,CAAC;QAC7CS,KAAK,EAAGA,KAAK,IAAI;UACfb,MAAM,CAACa,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGjC,QAAQ,CAACkC,SAAS,CAAC,GAAGjC,MAAM,IAAIE,EAAE,YAAY,CAAC;MAC3D8B,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}