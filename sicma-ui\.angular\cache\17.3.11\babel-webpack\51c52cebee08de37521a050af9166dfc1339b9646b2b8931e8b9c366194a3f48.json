{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { environment } from '@env';\nimport { of } from 'rxjs';\nimport { ContractAuditHistoryService } from './contract-audit-history.service';\nimport { ContractAuditStatusService } from './contract-audit-status.service';\nimport { ObligationService } from './obligation.service';\ndescribe('ObligationService', () => {\n  let service;\n  let httpMock;\n  let authServiceSpy;\n  let contractAuditHistoryServiceSpy;\n  let contractAuditStatusServiceSpy;\n  const apiUrl = `${environment.apiUrl}/obligations`;\n  const mockObligation = {\n    id: 1,\n    name: 'Test Obligation',\n    contractId: 1,\n    number: 1\n  };\n  const mockUser = {\n    id: 1,\n    username: 'testuser',\n    profiles: []\n  };\n  const mockCreateStatus = {\n    id: 1,\n    name: 'Creación de obligación',\n    description: 'Audit status for obligation creation'\n  };\n  const mockEditStatus = {\n    id: 2,\n    name: 'Edición de obligación',\n    description: 'Audit status for obligation editing'\n  };\n  const mockAuditHistory = {\n    id: 1,\n    contractId: 1,\n    auditStatusId: 1,\n    auditDate: new Date(),\n    comment: 'Test comment',\n    auditorId: 1\n  };\n  beforeEach(() => {\n    authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);\n    contractAuditHistoryServiceSpy = jasmine.createSpyObj('ContractAuditHistoryService', ['create']);\n    contractAuditStatusServiceSpy = jasmine.createSpyObj('ContractAuditStatusService', ['getByName']);\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [ObligationService, {\n        provide: AuthService,\n        useValue: authServiceSpy\n      }, {\n        provide: ContractAuditHistoryService,\n        useValue: contractAuditHistoryServiceSpy\n      }, {\n        provide: ContractAuditStatusService,\n        useValue: contractAuditStatusServiceSpy\n      }]\n    });\n    service = TestBed.inject(ObligationService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all obligations', () => {\n      const mockObligations = [mockObligation];\n      service.getAll().subscribe(obligations => {\n        expect(obligations).toEqual(mockObligations);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockObligations);\n    });\n    it('should handle error when getting all obligations', () => {\n      service.getAll().subscribe({\n        next: () => fail('should have failed'),\n        error: error => {\n          expect(error.status).toBe(500);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.flush('Internal Server Error', {\n        status: 500,\n        statusText: 'Internal Server Error'\n      });\n    });\n  });\n  describe('getById', () => {\n    it('should return an obligation by id', () => {\n      const id = 1;\n      service.getById(id).subscribe(obligation => {\n        expect(obligation).toEqual(mockObligation);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockObligation);\n    });\n    it('should handle error when getting obligation by id', () => {\n      const id = 999;\n      service.getById(id).subscribe({\n        next: () => fail('should have failed'),\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.flush('Not Found', {\n        status: 404,\n        statusText: 'Not Found'\n      });\n    });\n  });\n  describe('create', () => {\n    it('should create a new obligation with audit when user is authenticated', () => {\n      const newObligation = {\n        name: 'Test Obligation',\n        contractId: 1,\n        number: 1\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n      contractAuditStatusServiceSpy.getByName.and.returnValue(of(mockCreateStatus));\n      contractAuditHistoryServiceSpy.create.and.returnValue(of(mockAuditHistory));\n      service.create(newObligation).subscribe(obligation => {\n        expect(obligation).toEqual(mockObligation);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith('Creación de obligación');\n        expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newObligation);\n      req.flush(mockObligation);\n    });\n    it('should create a new obligation without audit when user is not authenticated', () => {\n      const newObligation = {\n        name: 'Test Obligation',\n        contractId: 1,\n        number: 1\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n      service.create(newObligation).subscribe(obligation => {\n        expect(obligation).toEqual(mockObligation);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n        expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newObligation);\n      req.flush(mockObligation);\n    });\n    it('should handle error when creating a new obligation', () => {\n      const newObligation = {\n        name: 'Test Obligation',\n        contractId: 1\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n      service.create(newObligation).subscribe({\n        next: () => fail('should have failed'),\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.flush('Bad Request', {\n        status: 400,\n        statusText: 'Bad Request'\n      });\n    });\n  });\n  describe('update', () => {\n    it('should update an obligation with audit when name has changed and user is authenticated', () => {\n      const id = 1;\n      const originalObligation = {\n        ...mockObligation,\n        name: 'Original Name',\n        number: 1\n      };\n      const updateData = {\n        name: 'Updated Name',\n        contractId: 1,\n        number: 1\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n      contractAuditStatusServiceSpy.getByName.and.returnValue(of(mockEditStatus));\n      contractAuditHistoryServiceSpy.create.and.returnValue(of(mockAuditHistory));\n      service.update(id, updateData).subscribe(obligation => {\n        expect(obligation).toEqual(mockObligation);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith('Edición de obligación');\n        expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();\n      });\n      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n      getReq.flush(originalObligation);\n      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateData);\n      putReq.flush(mockObligation);\n    });\n    it('should update an obligation without audit when name has not changed and user is authenticated', () => {\n      const id = 1;\n      const originalObligation = {\n        ...mockObligation,\n        number: 1\n      };\n      const updateData = {\n        name: 'Test Obligation',\n        contractId: 1,\n        number: 1\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n      service.update(id, updateData).subscribe(obligation => {\n        expect(obligation).toEqual(mockObligation);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n        expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();\n      });\n      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n      getReq.flush(originalObligation);\n      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateData);\n      putReq.flush(mockObligation);\n    });\n    it('should update an obligation without audit when user is not authenticated', () => {\n      const id = 1;\n      const originalObligation = {\n        ...mockObligation,\n        name: 'Original Name',\n        number: 1\n      };\n      const updateData = {\n        name: 'Updated Name',\n        contractId: 1,\n        number: 1\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n      service.update(id, updateData).subscribe(obligation => {\n        expect(obligation).toEqual(mockObligation);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n        expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();\n      });\n      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n      getReq.flush(originalObligation);\n      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateData);\n      putReq.flush(mockObligation);\n    });\n    it('should handle error when updating an obligation', () => {\n      const id = 999;\n      const updateData = {\n        name: 'Updated Name',\n        contractId: 1\n      };\n      service.update(id, updateData).subscribe({\n        next: () => fail('should have failed'),\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.flush('Not Found', {\n        status: 404,\n        statusText: 'Not Found'\n      });\n    });\n  });\n  describe('delete', () => {\n    it('should delete an obligation', () => {\n      const id = 1;\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n    it('should handle error when deleting an obligation', () => {\n      const id = 999;\n      service.delete(id).subscribe({\n        next: () => fail('should have failed'),\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.flush('Not Found', {\n        status: 404,\n        statusText: 'Not Found'\n      });\n    });\n  });\n  describe('getAllByContractId', () => {\n    it('should return all obligations by contract id', () => {\n      const contractId = 1;\n      const mockObligations = [mockObligation];\n      service.getAllByContractId(contractId).subscribe(obligations => {\n        expect(obligations).toEqual(mockObligations);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockObligations);\n    });\n    it('should handle error when getting obligations by contract id', () => {\n      const contractId = 999;\n      service.getAllByContractId(contractId).subscribe({\n        next: () => fail('should have failed'),\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}`);\n      req.flush('Not Found', {\n        status: 404,\n        statusText: 'Not Found'\n      });\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "AuthService", "environment", "of", "ContractAuditHistoryService", "ContractAuditStatusService", "ObligationService", "describe", "service", "httpMock", "authServiceSpy", "contractAuditHistoryServiceSpy", "contractAuditStatusServiceSpy", "apiUrl", "mockObligation", "id", "name", "contractId", "number", "mockUser", "username", "profiles", "mockCreateStatus", "description", "mockEditStatus", "mockAuditHistory", "auditStatusId", "auditDate", "Date", "comment", "auditorId", "beforeEach", "jasmine", "createSpyObj", "configureTestingModule", "imports", "providers", "provide", "useValue", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockObligations", "getAll", "subscribe", "obligations", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "next", "fail", "error", "status", "statusText", "getById", "obligation", "newObligation", "getCurrentUser", "and", "returnValue", "getByName", "create", "toHaveBeenCalled", "toHaveBeenCalledWith", "body", "not", "originalObligation", "updateData", "update", "getReq", "putReq", "delete", "nothing", "getAllByContractId"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\services\\obligation.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { ContractAuditHistory } from '@contract-management/models/contract-audit-history.model';\nimport { ContractAuditStatus } from '@contract-management/models/contract-audit-status.model';\nimport { Obligation } from '@contract-management/models/obligation.model';\nimport { User } from '@core/auth/models/user.model';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { environment } from '@env';\nimport { of } from 'rxjs';\nimport { ContractAuditHistoryService } from './contract-audit-history.service';\nimport { ContractAuditStatusService } from './contract-audit-status.service';\nimport { ObligationService } from './obligation.service';\n\ndescribe('ObligationService', () => {\n  let service: ObligationService;\n  let httpMock: HttpTestingController;\n  let authServiceSpy: jasmine.SpyObj<AuthService>;\n  let contractAuditHistoryServiceSpy: jasmine.SpyObj<ContractAuditHistoryService>;\n  let contractAuditStatusServiceSpy: jasmine.SpyObj<ContractAuditStatusService>;\n  const apiUrl = `${environment.apiUrl}/obligations`;\n\n  const mockObligation: Obligation = {\n    id: 1,\n    name: 'Test Obligation',\n    contractId: 1,\n    number: 1,\n  };\n\n  const mockUser: User = { id: 1, username: 'testuser', profiles: [] };\n  const mockCreateStatus: ContractAuditStatus = {\n    id: 1,\n    name: 'Creación de obligación',\n    description: 'Audit status for obligation creation',\n  };\n\n  const mockEditStatus: ContractAuditStatus = {\n    id: 2,\n    name: 'Edición de obligación',\n    description: 'Audit status for obligation editing',\n  };\n\n  const mockAuditHistory: ContractAuditHistory = {\n    id: 1,\n    contractId: 1,\n    auditStatusId: 1,\n    auditDate: new Date(),\n    comment: 'Test comment',\n    auditorId: 1,\n  };\n\n  beforeEach(() => {\n    authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);\n    contractAuditHistoryServiceSpy = jasmine.createSpyObj(\n      'ContractAuditHistoryService',\n      ['create'],\n    );\n    contractAuditStatusServiceSpy = jasmine.createSpyObj(\n      'ContractAuditStatusService',\n      ['getByName'],\n    );\n\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [\n        ObligationService,\n        { provide: AuthService, useValue: authServiceSpy },\n        {\n          provide: ContractAuditHistoryService,\n          useValue: contractAuditHistoryServiceSpy,\n        },\n        {\n          provide: ContractAuditStatusService,\n          useValue: contractAuditStatusServiceSpy,\n        },\n      ],\n    });\n    service = TestBed.inject(ObligationService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all obligations', () => {\n      const mockObligations = [mockObligation];\n\n      service.getAll().subscribe((obligations) => {\n        expect(obligations).toEqual(mockObligations);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockObligations);\n    });\n\n    it('should handle error when getting all obligations', () => {\n      service.getAll().subscribe({\n        next: () => fail('should have failed'),\n        error: (error) => {\n          expect(error.status).toBe(500);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.flush('Internal Server Error', {\n        status: 500,\n        statusText: 'Internal Server Error',\n      });\n    });\n  });\n\n  describe('getById', () => {\n    it('should return an obligation by id', () => {\n      const id = 1;\n\n      service.getById(id).subscribe((obligation) => {\n        expect(obligation).toEqual(mockObligation);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockObligation);\n    });\n\n    it('should handle error when getting obligation by id', () => {\n      const id = 999;\n\n      service.getById(id).subscribe({\n        next: () => fail('should have failed'),\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.flush('Not Found', { status: 404, statusText: 'Not Found' });\n    });\n  });\n\n  describe('create', () => {\n    it('should create a new obligation with audit when user is authenticated', () => {\n      const newObligation: Omit<Obligation, 'id'> = {\n        name: 'Test Obligation',\n        contractId: 1,\n        number: 1,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n      contractAuditStatusServiceSpy.getByName.and.returnValue(\n        of(mockCreateStatus),\n      );\n      contractAuditHistoryServiceSpy.create.and.returnValue(\n        of(mockAuditHistory),\n      );\n\n      service.create(newObligation).subscribe((obligation) => {\n        expect(obligation).toEqual(mockObligation);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith(\n          'Creación de obligación',\n        );\n        expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newObligation);\n      req.flush(mockObligation);\n    });\n\n    it('should create a new obligation without audit when user is not authenticated', () => {\n      const newObligation: Omit<Obligation, 'id'> = {\n        name: 'Test Obligation',\n        contractId: 1,\n        number: 1,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n\n      service.create(newObligation).subscribe((obligation) => {\n        expect(obligation).toEqual(mockObligation);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n        expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newObligation);\n      req.flush(mockObligation);\n    });\n\n    it('should handle error when creating a new obligation', () => {\n      const newObligation: Omit<Obligation, 'id'> = {\n        name: 'Test Obligation',\n        contractId: 1,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n\n      service.create(newObligation).subscribe({\n        next: () => fail('should have failed'),\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.flush('Bad Request', { status: 400, statusText: 'Bad Request' });\n    });\n  });\n\n  describe('update', () => {\n    it('should update an obligation with audit when name has changed and user is authenticated', () => {\n      const id = 1;\n      const originalObligation: Obligation = {\n        ...mockObligation,\n        name: 'Original Name',\n        number: 1,\n      };\n      const updateData: Partial<Obligation> = {\n        name: 'Updated Name',\n        contractId: 1,\n        number: 1,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n      contractAuditStatusServiceSpy.getByName.and.returnValue(\n        of(mockEditStatus),\n      );\n      contractAuditHistoryServiceSpy.create.and.returnValue(\n        of(mockAuditHistory),\n      );\n\n      service.update(id, updateData).subscribe((obligation) => {\n        expect(obligation).toEqual(mockObligation);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith(\n          'Edición de obligación',\n        );\n        expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();\n      });\n\n      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n      getReq.flush(originalObligation);\n\n      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateData);\n      putReq.flush(mockObligation);\n    });\n\n    it('should update an obligation without audit when name has not changed and user is authenticated', () => {\n      const id = 1;\n      const originalObligation: Obligation = {\n        ...mockObligation,\n        number: 1,\n      };\n      const updateData: Partial<Obligation> = {\n        name: 'Test Obligation',\n        contractId: 1,\n        number: 1,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n\n      service.update(id, updateData).subscribe((obligation) => {\n        expect(obligation).toEqual(mockObligation);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n        expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();\n      });\n\n      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n      getReq.flush(originalObligation);\n\n      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateData);\n      putReq.flush(mockObligation);\n    });\n\n    it('should update an obligation without audit when user is not authenticated', () => {\n      const id = 1;\n      const originalObligation: Obligation = {\n        ...mockObligation,\n        name: 'Original Name',\n        number: 1,\n      };\n      const updateData: Partial<Obligation> = {\n        name: 'Updated Name',\n        contractId: 1,\n        number: 1,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n\n      service.update(id, updateData).subscribe((obligation) => {\n        expect(obligation).toEqual(mockObligation);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n        expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();\n      });\n\n      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n      getReq.flush(originalObligation);\n\n      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateData);\n      putReq.flush(mockObligation);\n    });\n\n    it('should handle error when updating an obligation', () => {\n      const id = 999;\n      const updateData: Partial<Obligation> = {\n        name: 'Updated Name',\n        contractId: 1,\n      };\n\n      service.update(id, updateData).subscribe({\n        next: () => fail('should have failed'),\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.flush('Not Found', { status: 404, statusText: 'Not Found' });\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete an obligation', () => {\n      const id = 1;\n\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n\n    it('should handle error when deleting an obligation', () => {\n      const id = 999;\n\n      service.delete(id).subscribe({\n        next: () => fail('should have failed'),\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.flush('Not Found', { status: 404, statusText: 'Not Found' });\n    });\n  });\n\n  describe('getAllByContractId', () => {\n    it('should return all obligations by contract id', () => {\n      const contractId = 1;\n      const mockObligations = [mockObligation];\n\n      service.getAllByContractId(contractId).subscribe((obligations) => {\n        expect(obligations).toEqual(mockObligations);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockObligations);\n    });\n\n    it('should handle error when getting obligations by contract id', () => {\n      const contractId = 999;\n\n      service.getAllByContractId(contractId).subscribe({\n        next: () => fail('should have failed'),\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}`);\n      req.flush('Not Found', { status: 404, statusText: 'Not Found' });\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAK/C,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,EAAE,QAAQ,MAAM;AACzB,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,0BAA0B,QAAQ,iCAAiC;AAC5E,SAASC,iBAAiB,QAAQ,sBAAsB;AAExDC,QAAQ,CAAC,mBAAmB,EAAE,MAAK;EACjC,IAAIC,OAA0B;EAC9B,IAAIC,QAA+B;EACnC,IAAIC,cAA2C;EAC/C,IAAIC,8BAA2E;EAC/E,IAAIC,6BAAyE;EAC7E,MAAMC,MAAM,GAAG,GAAGX,WAAW,CAACW,MAAM,cAAc;EAElD,MAAMC,cAAc,GAAe;IACjCC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,iBAAiB;IACvBC,UAAU,EAAE,CAAC;IACbC,MAAM,EAAE;GACT;EAED,MAAMC,QAAQ,GAAS;IAAEJ,EAAE,EAAE,CAAC;IAAEK,QAAQ,EAAE,UAAU;IAAEC,QAAQ,EAAE;EAAE,CAAE;EACpE,MAAMC,gBAAgB,GAAwB;IAC5CP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,wBAAwB;IAC9BO,WAAW,EAAE;GACd;EAED,MAAMC,cAAc,GAAwB;IAC1CT,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,uBAAuB;IAC7BO,WAAW,EAAE;GACd;EAED,MAAME,gBAAgB,GAAyB;IAC7CV,EAAE,EAAE,CAAC;IACLE,UAAU,EAAE,CAAC;IACbS,aAAa,EAAE,CAAC;IAChBC,SAAS,EAAE,IAAIC,IAAI,EAAE;IACrBC,OAAO,EAAE,cAAc;IACvBC,SAAS,EAAE;GACZ;EAEDC,UAAU,CAAC,MAAK;IACdrB,cAAc,GAAGsB,OAAO,CAACC,YAAY,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,CAAC;IACxEtB,8BAA8B,GAAGqB,OAAO,CAACC,YAAY,CACnD,6BAA6B,EAC7B,CAAC,QAAQ,CAAC,CACX;IACDrB,6BAA6B,GAAGoB,OAAO,CAACC,YAAY,CAClD,4BAA4B,EAC5B,CAAC,WAAW,CAAC,CACd;IAEDjC,OAAO,CAACkC,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACrC,uBAAuB,CAAC;MAClCsC,SAAS,EAAE,CACT9B,iBAAiB,EACjB;QAAE+B,OAAO,EAAEpC,WAAW;QAAEqC,QAAQ,EAAE5B;MAAc,CAAE,EAClD;QACE2B,OAAO,EAAEjC,2BAA2B;QACpCkC,QAAQ,EAAE3B;OACX,EACD;QACE0B,OAAO,EAAEhC,0BAA0B;QACnCiC,QAAQ,EAAE1B;OACX;KAEJ,CAAC;IACFJ,OAAO,GAAGR,OAAO,CAACuC,MAAM,CAACjC,iBAAiB,CAAC;IAC3CG,QAAQ,GAAGT,OAAO,CAACuC,MAAM,CAACxC,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFyC,SAAS,CAAC,MAAK;IACb/B,QAAQ,CAACgC,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACnC,OAAO,CAAC,CAACoC,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFrC,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBmC,EAAE,CAAC,+BAA+B,EAAE,MAAK;MACvC,MAAMG,eAAe,GAAG,CAAC/B,cAAc,CAAC;MAExCN,OAAO,CAACsC,MAAM,EAAE,CAACC,SAAS,CAAEC,WAAW,IAAI;QACzCL,MAAM,CAACK,WAAW,CAAC,CAACC,OAAO,CAACJ,eAAe,CAAC;MAC9C,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGzC,QAAQ,CAAC0C,SAAS,CAACtC,MAAM,CAAC;MACtC8B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,eAAe,CAAC;IAC5B,CAAC,CAAC;IAEFH,EAAE,CAAC,kDAAkD,EAAE,MAAK;MAC1DlC,OAAO,CAACsC,MAAM,EAAE,CAACC,SAAS,CAAC;QACzBS,IAAI,EAAEA,CAAA,KAAMC,IAAI,CAAC,oBAAoB,CAAC;QACtCC,KAAK,EAAGA,KAAK,IAAI;UACff,MAAM,CAACe,KAAK,CAACC,MAAM,CAAC,CAACL,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGzC,QAAQ,CAAC0C,SAAS,CAACtC,MAAM,CAAC;MACtCqC,GAAG,CAACK,KAAK,CAAC,uBAAuB,EAAE;QACjCI,MAAM,EAAE,GAAG;QACXC,UAAU,EAAE;OACb,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFrD,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBmC,EAAE,CAAC,mCAAmC,EAAE,MAAK;MAC3C,MAAM3B,EAAE,GAAG,CAAC;MAEZP,OAAO,CAACqD,OAAO,CAAC9C,EAAE,CAAC,CAACgC,SAAS,CAAEe,UAAU,IAAI;QAC3CnB,MAAM,CAACmB,UAAU,CAAC,CAACb,OAAO,CAACnC,cAAc,CAAC;MAC5C,CAAC,CAAC;MAEF,MAAMoC,GAAG,GAAGzC,QAAQ,CAAC0C,SAAS,CAAC,GAAGtC,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjD4B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACzC,cAAc,CAAC;IAC3B,CAAC,CAAC;IAEF4B,EAAE,CAAC,mDAAmD,EAAE,MAAK;MAC3D,MAAM3B,EAAE,GAAG,GAAG;MAEdP,OAAO,CAACqD,OAAO,CAAC9C,EAAE,CAAC,CAACgC,SAAS,CAAC;QAC5BS,IAAI,EAAEA,CAAA,KAAMC,IAAI,CAAC,oBAAoB,CAAC;QACtCC,KAAK,EAAGA,KAAK,IAAI;UACff,MAAM,CAACe,KAAK,CAACC,MAAM,CAAC,CAACL,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGzC,QAAQ,CAAC0C,SAAS,CAAC,GAAGtC,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDmC,GAAG,CAACK,KAAK,CAAC,WAAW,EAAE;QAAEI,MAAM,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAW,CAAE,CAAC;IAClE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFrD,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBmC,EAAE,CAAC,sEAAsE,EAAE,MAAK;MAC9E,MAAMqB,aAAa,GAA2B;QAC5C/C,IAAI,EAAE,iBAAiB;QACvBC,UAAU,EAAE,CAAC;QACbC,MAAM,EAAE;OACT;MAEDR,cAAc,CAACsD,cAAc,CAACC,GAAG,CAACC,WAAW,CAAC/C,QAAQ,CAAC;MACvDP,6BAA6B,CAACuD,SAAS,CAACF,GAAG,CAACC,WAAW,CACrD/D,EAAE,CAACmB,gBAAgB,CAAC,CACrB;MACDX,8BAA8B,CAACyD,MAAM,CAACH,GAAG,CAACC,WAAW,CACnD/D,EAAE,CAACsB,gBAAgB,CAAC,CACrB;MAEDjB,OAAO,CAAC4D,MAAM,CAACL,aAAa,CAAC,CAAChB,SAAS,CAAEe,UAAU,IAAI;QACrDnB,MAAM,CAACmB,UAAU,CAAC,CAACb,OAAO,CAACnC,cAAc,CAAC;QAC1C6B,MAAM,CAACjC,cAAc,CAACsD,cAAc,CAAC,CAACK,gBAAgB,EAAE;QACxD1B,MAAM,CAAC/B,6BAA6B,CAACuD,SAAS,CAAC,CAACG,oBAAoB,CAClE,wBAAwB,CACzB;QACD3B,MAAM,CAAChC,8BAA8B,CAACyD,MAAM,CAAC,CAACC,gBAAgB,EAAE;MAClE,CAAC,CAAC;MAEF,MAAMnB,GAAG,GAAGzC,QAAQ,CAAC0C,SAAS,CAACtC,MAAM,CAAC;MACtC8B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACmB,IAAI,CAAC,CAACtB,OAAO,CAACc,aAAa,CAAC;MAC/Cb,GAAG,CAACK,KAAK,CAACzC,cAAc,CAAC;IAC3B,CAAC,CAAC;IAEF4B,EAAE,CAAC,6EAA6E,EAAE,MAAK;MACrF,MAAMqB,aAAa,GAA2B;QAC5C/C,IAAI,EAAE,iBAAiB;QACvBC,UAAU,EAAE,CAAC;QACbC,MAAM,EAAE;OACT;MAEDR,cAAc,CAACsD,cAAc,CAACC,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;MAEnD1D,OAAO,CAAC4D,MAAM,CAACL,aAAa,CAAC,CAAChB,SAAS,CAAEe,UAAU,IAAI;QACrDnB,MAAM,CAACmB,UAAU,CAAC,CAACb,OAAO,CAACnC,cAAc,CAAC;QAC1C6B,MAAM,CAACjC,cAAc,CAACsD,cAAc,CAAC,CAACK,gBAAgB,EAAE;QACxD1B,MAAM,CAAC/B,6BAA6B,CAACuD,SAAS,CAAC,CAACK,GAAG,CAACH,gBAAgB,EAAE;QACtE1B,MAAM,CAAChC,8BAA8B,CAACyD,MAAM,CAAC,CAACI,GAAG,CAACH,gBAAgB,EAAE;MACtE,CAAC,CAAC;MAEF,MAAMnB,GAAG,GAAGzC,QAAQ,CAAC0C,SAAS,CAACtC,MAAM,CAAC;MACtC8B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACmB,IAAI,CAAC,CAACtB,OAAO,CAACc,aAAa,CAAC;MAC/Cb,GAAG,CAACK,KAAK,CAACzC,cAAc,CAAC;IAC3B,CAAC,CAAC;IAEF4B,EAAE,CAAC,oDAAoD,EAAE,MAAK;MAC5D,MAAMqB,aAAa,GAA2B;QAC5C/C,IAAI,EAAE,iBAAiB;QACvBC,UAAU,EAAE;OACb;MAEDP,cAAc,CAACsD,cAAc,CAACC,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;MAEnD1D,OAAO,CAAC4D,MAAM,CAACL,aAAa,CAAC,CAAChB,SAAS,CAAC;QACtCS,IAAI,EAAEA,CAAA,KAAMC,IAAI,CAAC,oBAAoB,CAAC;QACtCC,KAAK,EAAGA,KAAK,IAAI;UACff,MAAM,CAACe,KAAK,CAACC,MAAM,CAAC,CAACL,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGzC,QAAQ,CAAC0C,SAAS,CAACtC,MAAM,CAAC;MACtCqC,GAAG,CAACK,KAAK,CAAC,aAAa,EAAE;QAAEI,MAAM,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAa,CAAE,CAAC;IACtE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFrD,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBmC,EAAE,CAAC,wFAAwF,EAAE,MAAK;MAChG,MAAM3B,EAAE,GAAG,CAAC;MACZ,MAAM0D,kBAAkB,GAAe;QACrC,GAAG3D,cAAc;QACjBE,IAAI,EAAE,eAAe;QACrBE,MAAM,EAAE;OACT;MACD,MAAMwD,UAAU,GAAwB;QACtC1D,IAAI,EAAE,cAAc;QACpBC,UAAU,EAAE,CAAC;QACbC,MAAM,EAAE;OACT;MAEDR,cAAc,CAACsD,cAAc,CAACC,GAAG,CAACC,WAAW,CAAC/C,QAAQ,CAAC;MACvDP,6BAA6B,CAACuD,SAAS,CAACF,GAAG,CAACC,WAAW,CACrD/D,EAAE,CAACqB,cAAc,CAAC,CACnB;MACDb,8BAA8B,CAACyD,MAAM,CAACH,GAAG,CAACC,WAAW,CACnD/D,EAAE,CAACsB,gBAAgB,CAAC,CACrB;MAEDjB,OAAO,CAACmE,MAAM,CAAC5D,EAAE,EAAE2D,UAAU,CAAC,CAAC3B,SAAS,CAAEe,UAAU,IAAI;QACtDnB,MAAM,CAACmB,UAAU,CAAC,CAACb,OAAO,CAACnC,cAAc,CAAC;QAC1C6B,MAAM,CAACjC,cAAc,CAACsD,cAAc,CAAC,CAACK,gBAAgB,EAAE;QACxD1B,MAAM,CAAC/B,6BAA6B,CAACuD,SAAS,CAAC,CAACG,oBAAoB,CAClE,uBAAuB,CACxB;QACD3B,MAAM,CAAChC,8BAA8B,CAACyD,MAAM,CAAC,CAACC,gBAAgB,EAAE;MAClE,CAAC,CAAC;MAEF,MAAMO,MAAM,GAAGnE,QAAQ,CAAC0C,SAAS,CAAC,GAAGtC,MAAM,IAAIE,EAAE,EAAE,CAAC;MACpD4B,MAAM,CAACiC,MAAM,CAACxB,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACzCsB,MAAM,CAACrB,KAAK,CAACkB,kBAAkB,CAAC;MAEhC,MAAMI,MAAM,GAAGpE,QAAQ,CAAC0C,SAAS,CAAC,GAAGtC,MAAM,IAAIE,EAAE,EAAE,CAAC;MACpD4B,MAAM,CAACkC,MAAM,CAACzB,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACzCX,MAAM,CAACkC,MAAM,CAACzB,OAAO,CAACmB,IAAI,CAAC,CAACtB,OAAO,CAACyB,UAAU,CAAC;MAC/CG,MAAM,CAACtB,KAAK,CAACzC,cAAc,CAAC;IAC9B,CAAC,CAAC;IAEF4B,EAAE,CAAC,+FAA+F,EAAE,MAAK;MACvG,MAAM3B,EAAE,GAAG,CAAC;MACZ,MAAM0D,kBAAkB,GAAe;QACrC,GAAG3D,cAAc;QACjBI,MAAM,EAAE;OACT;MACD,MAAMwD,UAAU,GAAwB;QACtC1D,IAAI,EAAE,iBAAiB;QACvBC,UAAU,EAAE,CAAC;QACbC,MAAM,EAAE;OACT;MAEDR,cAAc,CAACsD,cAAc,CAACC,GAAG,CAACC,WAAW,CAAC/C,QAAQ,CAAC;MAEvDX,OAAO,CAACmE,MAAM,CAAC5D,EAAE,EAAE2D,UAAU,CAAC,CAAC3B,SAAS,CAAEe,UAAU,IAAI;QACtDnB,MAAM,CAACmB,UAAU,CAAC,CAACb,OAAO,CAACnC,cAAc,CAAC;QAC1C6B,MAAM,CAACjC,cAAc,CAACsD,cAAc,CAAC,CAACK,gBAAgB,EAAE;QACxD1B,MAAM,CAAC/B,6BAA6B,CAACuD,SAAS,CAAC,CAACK,GAAG,CAACH,gBAAgB,EAAE;QACtE1B,MAAM,CAAChC,8BAA8B,CAACyD,MAAM,CAAC,CAACI,GAAG,CAACH,gBAAgB,EAAE;MACtE,CAAC,CAAC;MAEF,MAAMO,MAAM,GAAGnE,QAAQ,CAAC0C,SAAS,CAAC,GAAGtC,MAAM,IAAIE,EAAE,EAAE,CAAC;MACpD4B,MAAM,CAACiC,MAAM,CAACxB,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACzCsB,MAAM,CAACrB,KAAK,CAACkB,kBAAkB,CAAC;MAEhC,MAAMI,MAAM,GAAGpE,QAAQ,CAAC0C,SAAS,CAAC,GAAGtC,MAAM,IAAIE,EAAE,EAAE,CAAC;MACpD4B,MAAM,CAACkC,MAAM,CAACzB,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACzCX,MAAM,CAACkC,MAAM,CAACzB,OAAO,CAACmB,IAAI,CAAC,CAACtB,OAAO,CAACyB,UAAU,CAAC;MAC/CG,MAAM,CAACtB,KAAK,CAACzC,cAAc,CAAC;IAC9B,CAAC,CAAC;IAEF4B,EAAE,CAAC,0EAA0E,EAAE,MAAK;MAClF,MAAM3B,EAAE,GAAG,CAAC;MACZ,MAAM0D,kBAAkB,GAAe;QACrC,GAAG3D,cAAc;QACjBE,IAAI,EAAE,eAAe;QACrBE,MAAM,EAAE;OACT;MACD,MAAMwD,UAAU,GAAwB;QACtC1D,IAAI,EAAE,cAAc;QACpBC,UAAU,EAAE,CAAC;QACbC,MAAM,EAAE;OACT;MAEDR,cAAc,CAACsD,cAAc,CAACC,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;MAEnD1D,OAAO,CAACmE,MAAM,CAAC5D,EAAE,EAAE2D,UAAU,CAAC,CAAC3B,SAAS,CAAEe,UAAU,IAAI;QACtDnB,MAAM,CAACmB,UAAU,CAAC,CAACb,OAAO,CAACnC,cAAc,CAAC;QAC1C6B,MAAM,CAACjC,cAAc,CAACsD,cAAc,CAAC,CAACK,gBAAgB,EAAE;QACxD1B,MAAM,CAAC/B,6BAA6B,CAACuD,SAAS,CAAC,CAACK,GAAG,CAACH,gBAAgB,EAAE;QACtE1B,MAAM,CAAChC,8BAA8B,CAACyD,MAAM,CAAC,CAACI,GAAG,CAACH,gBAAgB,EAAE;MACtE,CAAC,CAAC;MAEF,MAAMO,MAAM,GAAGnE,QAAQ,CAAC0C,SAAS,CAAC,GAAGtC,MAAM,IAAIE,EAAE,EAAE,CAAC;MACpD4B,MAAM,CAACiC,MAAM,CAACxB,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACzCsB,MAAM,CAACrB,KAAK,CAACkB,kBAAkB,CAAC;MAEhC,MAAMI,MAAM,GAAGpE,QAAQ,CAAC0C,SAAS,CAAC,GAAGtC,MAAM,IAAIE,EAAE,EAAE,CAAC;MACpD4B,MAAM,CAACkC,MAAM,CAACzB,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACzCX,MAAM,CAACkC,MAAM,CAACzB,OAAO,CAACmB,IAAI,CAAC,CAACtB,OAAO,CAACyB,UAAU,CAAC;MAC/CG,MAAM,CAACtB,KAAK,CAACzC,cAAc,CAAC;IAC9B,CAAC,CAAC;IAEF4B,EAAE,CAAC,iDAAiD,EAAE,MAAK;MACzD,MAAM3B,EAAE,GAAG,GAAG;MACd,MAAM2D,UAAU,GAAwB;QACtC1D,IAAI,EAAE,cAAc;QACpBC,UAAU,EAAE;OACb;MAEDT,OAAO,CAACmE,MAAM,CAAC5D,EAAE,EAAE2D,UAAU,CAAC,CAAC3B,SAAS,CAAC;QACvCS,IAAI,EAAEA,CAAA,KAAMC,IAAI,CAAC,oBAAoB,CAAC;QACtCC,KAAK,EAAGA,KAAK,IAAI;UACff,MAAM,CAACe,KAAK,CAACC,MAAM,CAAC,CAACL,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGzC,QAAQ,CAAC0C,SAAS,CAAC,GAAGtC,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDmC,GAAG,CAACK,KAAK,CAAC,WAAW,EAAE;QAAEI,MAAM,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAW,CAAE,CAAC;IAClE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFrD,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBmC,EAAE,CAAC,6BAA6B,EAAE,MAAK;MACrC,MAAM3B,EAAE,GAAG,CAAC;MAEZP,OAAO,CAACsE,MAAM,CAAC/D,EAAE,CAAC,CAACgC,SAAS,CAAC,MAAK;QAChCJ,MAAM,EAAE,CAACoC,OAAO,EAAE;MACpB,CAAC,CAAC;MAEF,MAAM7B,GAAG,GAAGzC,QAAQ,CAAC0C,SAAS,CAAC,GAAGtC,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjD4B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;IAEFb,EAAE,CAAC,iDAAiD,EAAE,MAAK;MACzD,MAAM3B,EAAE,GAAG,GAAG;MAEdP,OAAO,CAACsE,MAAM,CAAC/D,EAAE,CAAC,CAACgC,SAAS,CAAC;QAC3BS,IAAI,EAAEA,CAAA,KAAMC,IAAI,CAAC,oBAAoB,CAAC;QACtCC,KAAK,EAAGA,KAAK,IAAI;UACff,MAAM,CAACe,KAAK,CAACC,MAAM,CAAC,CAACL,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGzC,QAAQ,CAAC0C,SAAS,CAAC,GAAGtC,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDmC,GAAG,CAACK,KAAK,CAAC,WAAW,EAAE;QAAEI,MAAM,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAW,CAAE,CAAC;IAClE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFrD,QAAQ,CAAC,oBAAoB,EAAE,MAAK;IAClCmC,EAAE,CAAC,8CAA8C,EAAE,MAAK;MACtD,MAAMzB,UAAU,GAAG,CAAC;MACpB,MAAM4B,eAAe,GAAG,CAAC/B,cAAc,CAAC;MAExCN,OAAO,CAACwE,kBAAkB,CAAC/D,UAAU,CAAC,CAAC8B,SAAS,CAAEC,WAAW,IAAI;QAC/DL,MAAM,CAACK,WAAW,CAAC,CAACC,OAAO,CAACJ,eAAe,CAAC;MAC9C,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGzC,QAAQ,CAAC0C,SAAS,CAAC,GAAGtC,MAAM,aAAaI,UAAU,EAAE,CAAC;MAClE0B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,eAAe,CAAC;IAC5B,CAAC,CAAC;IAEFH,EAAE,CAAC,6DAA6D,EAAE,MAAK;MACrE,MAAMzB,UAAU,GAAG,GAAG;MAEtBT,OAAO,CAACwE,kBAAkB,CAAC/D,UAAU,CAAC,CAAC8B,SAAS,CAAC;QAC/CS,IAAI,EAAEA,CAAA,KAAMC,IAAI,CAAC,oBAAoB,CAAC;QACtCC,KAAK,EAAGA,KAAK,IAAI;UACff,MAAM,CAACe,KAAK,CAACC,MAAM,CAAC,CAACL,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGzC,QAAQ,CAAC0C,SAAS,CAAC,GAAGtC,MAAM,aAAaI,UAAU,EAAE,CAAC;MAClEiC,GAAG,CAACK,KAAK,CAAC,WAAW,EAAE;QAAEI,MAAM,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAW,CAAE,CAAC;IAClE,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}