{"ast": null, "code": "import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { MatDialog } from '@angular/material/dialog';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { SupervisorService } from '@supervisor-management/services/supervisor.service';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { of, throwError } from 'rxjs';\nimport { MonthlyReportsReviewListComponent } from './monthly-reports-review-list.component';\ndescribe('MonthlyReportsReviewListComponent', () => {\n  let component;\n  let fixture;\n  let monthlyReportService;\n  let supervisorService;\n  let authService;\n  let alertService;\n  let spinnerService;\n  let dialog;\n  const mockUser = {\n    id: 1,\n    username: '<EMAIL>',\n    profiles: []\n  };\n  const mockSupervisor = {\n    id: 1,\n    fullName: 'Test Supervisor',\n    email: '<EMAIL>',\n    signatureFileKey: undefined,\n    signatureFileUrl: undefined,\n    idNumber: 123456789,\n    position: 'Test Position',\n    idType: {\n      id: 1,\n      name: 'CC'\n    }\n  };\n  const mockReport = {\n    report_id: 1,\n    report_number: 1,\n    start_date: '2024-01-01',\n    end_date: '2024-01-31',\n    contract_number: 123,\n    contract_year: 2024,\n    contractor_full_name: 'Test Contractor',\n    contract_dependency: 'Test Dependency',\n    contract_group: 'Test Group',\n    total_value: 1000,\n    review_status_id: 1,\n    review_status_name: 'Pendiente de revisión'\n  };\n  beforeEach(() => {\n    const monthlyReportServiceSpy = jasmine.createSpyObj('MonthlyReportService', ['getFilteredReportsBySupervisorEmail', 'getById']);\n    const supervisorServiceSpy = jasmine.createSpyObj('SupervisorService', ['getByEmail']);\n    const authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);\n    const alertServiceSpy = jasmine.createSpyObj('AlertService', ['error']);\n    const spinnerServiceSpy = jasmine.createSpyObj('NgxSpinnerService', ['show', 'hide']);\n    const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);\n    dialogRefSpy.afterClosed.and.returnValue(of(true));\n    const dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);\n    dialogSpy.open.and.returnValue(dialogRefSpy);\n    TestBed.configureTestingModule({\n      imports: [MonthlyReportsReviewListComponent, HttpClientTestingModule, BrowserAnimationsModule],\n      providers: [{\n        provide: MonthlyReportService,\n        useValue: monthlyReportServiceSpy\n      }, {\n        provide: SupervisorService,\n        useValue: supervisorServiceSpy\n      }, {\n        provide: AuthService,\n        useValue: authServiceSpy\n      }, {\n        provide: AlertService,\n        useValue: alertServiceSpy\n      }, {\n        provide: NgxSpinnerService,\n        useValue: spinnerServiceSpy\n      }, {\n        provide: MatDialog,\n        useValue: dialogSpy\n      }]\n    });\n    fixture = TestBed.createComponent(MonthlyReportsReviewListComponent);\n    component = fixture.componentInstance;\n    monthlyReportService = TestBed.inject(MonthlyReportService);\n    supervisorService = TestBed.inject(SupervisorService);\n    authService = TestBed.inject(AuthService);\n    alertService = TestBed.inject(AlertService);\n    spinnerService = TestBed.inject(NgxSpinnerService);\n    dialog = TestBed.inject(MatDialog);\n    const supervisorWithSignature = {\n      ...mockSupervisor,\n      signatureFileKey: 'key123',\n      signatureFileUrl: 'http://example.com/signature.png'\n    };\n    authService.getCurrentUser.and.returnValue(mockUser);\n    supervisorService.getByEmail.and.returnValue(of(supervisorWithSignature));\n    monthlyReportService.getFilteredReportsBySupervisorEmail.and.returnValue(of([mockReport]));\n    const mockMonthlyReport = {\n      id: 1,\n      reportNumber: 1,\n      startDate: new Date('2024-01-01'),\n      endDate: new Date('2024-01-31'),\n      creationDate: new Date('2024-01-01'),\n      contractorContractId: 1,\n      currentReviewStatus: {\n        id: 1,\n        name: 'Pendiente de revisión'\n      }\n    };\n    monthlyReportService.getById.and.returnValue(of(mockMonthlyReport));\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should load monthly reports on init', () => {\n    component.ngOnInit();\n    expect(spinnerService.show).toHaveBeenCalled();\n    expect(supervisorService.getByEmail).toHaveBeenCalledWith(mockUser.username);\n    expect(monthlyReportService.getFilteredReportsBySupervisorEmail).toHaveBeenCalledWith(mockUser.username);\n    expect(component.dataSource.data).toEqual([mockReport]);\n    expect(component.supervisorFullName).toBe(mockSupervisor.fullName);\n  });\n  it('should handle error when user is not found', () => {\n    authService.getCurrentUser.and.returnValue(null);\n    component.ngOnInit();\n    expect(alertService.error).toHaveBeenCalledWith('No se encontró información del usuario actual');\n  });\n  it('should handle error when loading supervisor information', () => {\n    supervisorService.getByEmail.and.returnValue(throwError(() => new Error()));\n    component.ngOnInit();\n    expect(alertService.error).toHaveBeenCalledWith('Error al cargar información del supervisor');\n  });\n  it('should handle error when loading monthly reports', () => {\n    monthlyReportService.getFilteredReportsBySupervisorEmail.and.returnValue(throwError(() => new Error()));\n    component.ngOnInit();\n    expect(alertService.error).toHaveBeenCalledWith('Error al cargar los informes');\n  });\n  it('should apply filter', () => {\n    component.ngOnInit();\n    const event = {\n      target: {\n        value: 'test'\n      }\n    };\n    component.dataSource.paginator = component.paginator;\n    component.applyFilter(event);\n    expect(component.dataSource.filter).toBe('test');\n  });\n  it('should handle error when opening report details without report', () => {\n    component.openReportDetails(null);\n    expect(alertService.error).toHaveBeenCalledWith('No se encontró información del informe');\n  });\n  it('should handle error when opening report details without user', () => {\n    authService.getCurrentUser.and.returnValue(null);\n    component.openReportDetails(mockReport);\n    expect(alertService.error).toHaveBeenCalledWith('No se encontró información del usuario actual');\n  });\n  it('should open digital signature dialog when supervisor has no signature', () => {\n    supervisorService.getByEmail.and.returnValue(of(mockSupervisor));\n    const dialogRef = {\n      afterClosed: () => of(false)\n    };\n    dialog.open.and.returnValue(dialogRef);\n    component.openReportDetails(mockReport);\n    expect(dialog.open).toHaveBeenCalledWith(jasmine.any(Function), {\n      width: '90vw',\n      height: '90vh',\n      maxWidth: '100vw',\n      maxHeight: '100vh',\n      data: {\n        report: jasmine.any(Object)\n      }\n    });\n  });\n  it('should open report dialog when supervisor has signature', () => {\n    const dialogRef = {\n      afterClosed: () => of(false)\n    };\n    dialog.open.and.returnValue(dialogRef);\n    component.openReportDetails(mockReport);\n    expect(dialog.open).toHaveBeenCalledWith(jasmine.any(Function), {\n      width: '90vw',\n      height: '90vh',\n      maxWidth: '100vw',\n      maxHeight: '100vh',\n      data: {\n        report: jasmine.any(Object)\n      }\n    });\n  });\n  it('should reload reports when dialog is closed with result', () => {\n    const dialogRef = {\n      afterClosed: () => of(true)\n    };\n    dialog.open.and.returnValue(dialogRef);\n    spyOn(component, 'loadMonthlyReports');\n    component.openReportDetails(mockReport);\n    expect(component.loadMonthlyReports).toHaveBeenCalled();\n  });\n  it('should handle error when verifying supervisor information', () => {\n    supervisorService.getByEmail.and.returnValue(throwError(() => new Error()));\n    component.openReportDetails(mockReport);\n    expect(alertService.error).toHaveBeenCalledWith('Error al cargar información del supervisor');\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "TestBed", "MatDialog", "BrowserAnimationsModule", "MonthlyReportService", "AuthService", "AlertService", "SupervisorService", "NgxSpinnerService", "of", "throwError", "MonthlyReportsReviewListComponent", "describe", "component", "fixture", "monthlyReportService", "supervisorService", "authService", "alertService", "spinnerService", "dialog", "mockUser", "id", "username", "profiles", "mockSupervisor", "fullName", "email", "signatureFile<PERSON>ey", "undefined", "signatureFileUrl", "idNumber", "position", "idType", "name", "mockReport", "report_id", "report_number", "start_date", "end_date", "contract_number", "contract_year", "contractor_full_name", "contract_dependency", "contract_group", "total_value", "review_status_id", "review_status_name", "beforeEach", "monthlyReportServiceSpy", "jasmine", "createSpyObj", "supervisorServiceSpy", "authServiceSpy", "alertServiceSpy", "spinnerServiceSpy", "dialogRefSpy", "afterClosed", "and", "returnValue", "dialogSpy", "open", "configureTestingModule", "imports", "providers", "provide", "useValue", "createComponent", "componentInstance", "inject", "supervisorWithSignature", "getCurrentUser", "getByEmail", "getFilteredReportsBySupervisorEmail", "mockMonthlyReport", "reportNumber", "startDate", "Date", "endDate", "creationDate", "contractorContractId", "currentReviewStatus", "getById", "it", "expect", "toBeTruthy", "ngOnInit", "show", "toHaveBeenCalled", "toHaveBeenCalledWith", "dataSource", "data", "toEqual", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toBe", "error", "Error", "event", "target", "value", "paginator", "applyFilter", "filter", "openReportDetails", "dialogRef", "any", "Function", "width", "height", "max<PERSON><PERSON><PERSON>", "maxHeight", "report", "Object", "spyOn", "loadMonthlyReports"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\monthly-reports-review-list\\monthly-reports-review-list.component.spec.ts"], "sourcesContent": ["import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { ComponentFixture, TestBed } from '@angular/core/testing';\nimport { MatDialog, MatDialogRef } from '@angular/material/dialog';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\n\nimport { MonthlyReportSupervisorExportModel } from '@contractor-dashboard/models/monthly_report_supervisor_export.model';\nimport { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { SupervisorService } from '@supervisor-management/services/supervisor.service';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { of, throwError } from 'rxjs';\n\nimport { MonthlyReportsReviewListComponent } from './monthly-reports-review-list.component';\n\ndescribe('MonthlyReportsReviewListComponent', () => {\n  let component: MonthlyReportsReviewListComponent;\n  let fixture: ComponentFixture<MonthlyReportsReviewListComponent>;\n  let monthlyReportService: jasmine.SpyObj<MonthlyReportService>;\n  let supervisorService: jasmine.SpyObj<SupervisorService>;\n  let authService: jasmine.SpyObj<AuthService>;\n  let alertService: jasmine.SpyObj<AlertService>;\n  let spinnerService: jasmine.SpyObj<NgxSpinnerService>;\n  let dialog: jasmine.SpyObj<MatDialog>;\n\n  const mockUser = {\n    id: 1,\n    username: '<EMAIL>',\n    profiles: [],\n  };\n\n  const mockSupervisor = {\n    id: 1,\n    fullName: 'Test Supervisor',\n    email: '<EMAIL>',\n    signatureFileKey: undefined,\n    signatureFileUrl: undefined,\n    idNumber: 123456789,\n    position: 'Test Position',\n    idType: { id: 1, name: 'CC' },\n  };\n\n  const mockReport: MonthlyReportSupervisorExportModel = {\n    report_id: 1,\n    report_number: 1,\n    start_date: '2024-01-01',\n    end_date: '2024-01-31',\n    contract_number: 123,\n    contract_year: 2024,\n    contractor_full_name: 'Test Contractor',\n    contract_dependency: 'Test Dependency',\n    contract_group: 'Test Group',\n    total_value: 1000,\n    review_status_id: 1,\n    review_status_name: 'Pendiente de revisión',\n  };\n\n  beforeEach(() => {\n    const monthlyReportServiceSpy = jasmine.createSpyObj(\n      'MonthlyReportService',\n      ['getFilteredReportsBySupervisorEmail', 'getById'],\n    );\n    const supervisorServiceSpy = jasmine.createSpyObj('SupervisorService', [\n      'getByEmail',\n    ]);\n    const authServiceSpy = jasmine.createSpyObj('AuthService', [\n      'getCurrentUser',\n    ]);\n    const alertServiceSpy = jasmine.createSpyObj('AlertService', ['error']);\n    const spinnerServiceSpy = jasmine.createSpyObj('NgxSpinnerService', [\n      'show',\n      'hide',\n    ]);\n    const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);\n    dialogRefSpy.afterClosed.and.returnValue(of(true));\n    const dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);\n    dialogSpy.open.and.returnValue(dialogRefSpy);\n\n    TestBed.configureTestingModule({\n      imports: [\n        MonthlyReportsReviewListComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n      ],\n      providers: [\n        { provide: MonthlyReportService, useValue: monthlyReportServiceSpy },\n        { provide: SupervisorService, useValue: supervisorServiceSpy },\n        { provide: AuthService, useValue: authServiceSpy },\n        { provide: AlertService, useValue: alertServiceSpy },\n        { provide: NgxSpinnerService, useValue: spinnerServiceSpy },\n        { provide: MatDialog, useValue: dialogSpy },\n      ],\n    });\n\n    fixture = TestBed.createComponent(MonthlyReportsReviewListComponent);\n    component = fixture.componentInstance;\n    monthlyReportService = TestBed.inject(\n      MonthlyReportService,\n    ) as jasmine.SpyObj<MonthlyReportService>;\n    supervisorService = TestBed.inject(\n      SupervisorService,\n    ) as jasmine.SpyObj<SupervisorService>;\n    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;\n    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;\n    spinnerService = TestBed.inject(\n      NgxSpinnerService,\n    ) as jasmine.SpyObj<NgxSpinnerService>;\n    dialog = TestBed.inject(MatDialog) as jasmine.SpyObj<MatDialog>;\n\n    const supervisorWithSignature = {\n      ...mockSupervisor,\n      signatureFileKey: 'key123',\n      signatureFileUrl: 'http://example.com/signature.png',\n    };\n\n    authService.getCurrentUser.and.returnValue(mockUser);\n    supervisorService.getByEmail.and.returnValue(of(supervisorWithSignature));\n    monthlyReportService.getFilteredReportsBySupervisorEmail.and.returnValue(\n      of([mockReport]),\n    );\n    const mockMonthlyReport = {\n      id: 1,\n      reportNumber: 1,\n      startDate: new Date('2024-01-01'),\n      endDate: new Date('2024-01-31'),\n      creationDate: new Date('2024-01-01'),\n      contractorContractId: 1,\n      currentReviewStatus: { id: 1, name: 'Pendiente de revisión' },\n    };\n    monthlyReportService.getById.and.returnValue(of(mockMonthlyReport));\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should load monthly reports on init', () => {\n    component.ngOnInit();\n    expect(spinnerService.show).toHaveBeenCalled();\n    expect(supervisorService.getByEmail).toHaveBeenCalledWith(\n      mockUser.username,\n    );\n    expect(\n      monthlyReportService.getFilteredReportsBySupervisorEmail,\n    ).toHaveBeenCalledWith(mockUser.username);\n    expect(component.dataSource.data).toEqual([mockReport]);\n    expect(component.supervisorFullName).toBe(mockSupervisor.fullName);\n  });\n\n  it('should handle error when user is not found', () => {\n    authService.getCurrentUser.and.returnValue(null);\n    component.ngOnInit();\n    expect(alertService.error).toHaveBeenCalledWith(\n      'No se encontró información del usuario actual',\n    );\n  });\n\n  it('should handle error when loading supervisor information', () => {\n    supervisorService.getByEmail.and.returnValue(throwError(() => new Error()));\n    component.ngOnInit();\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al cargar información del supervisor',\n    );\n  });\n\n  it('should handle error when loading monthly reports', () => {\n    monthlyReportService.getFilteredReportsBySupervisorEmail.and.returnValue(\n      throwError(() => new Error()),\n    );\n    component.ngOnInit();\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al cargar los informes',\n    );\n  });\n\n  it('should apply filter', () => {\n    component.ngOnInit();\n    const event = { target: { value: 'test' } } as unknown as Event;\n    component.dataSource.paginator = component.paginator;\n    component.applyFilter(event);\n    expect(component.dataSource.filter).toBe('test');\n  });\n\n  it('should handle error when opening report details without report', () => {\n    component.openReportDetails(\n      null as unknown as MonthlyReportSupervisorExportModel,\n    );\n    expect(alertService.error).toHaveBeenCalledWith(\n      'No se encontró información del informe',\n    );\n  });\n\n  it('should handle error when opening report details without user', () => {\n    authService.getCurrentUser.and.returnValue(null);\n    component.openReportDetails(mockReport);\n    expect(alertService.error).toHaveBeenCalledWith(\n      'No se encontró información del usuario actual',\n    );\n  });\n\n  it('should open digital signature dialog when supervisor has no signature', () => {\n    supervisorService.getByEmail.and.returnValue(of(mockSupervisor));\n\n    const dialogRef = {\n      afterClosed: () => of(false),\n    } as MatDialogRef<unknown>;\n    dialog.open.and.returnValue(dialogRef);\n\n    component.openReportDetails(mockReport);\n\n    expect(dialog.open).toHaveBeenCalledWith(jasmine.any(Function), {\n      width: '90vw',\n      height: '90vh',\n      maxWidth: '100vw',\n      maxHeight: '100vh',\n      data: { report: jasmine.any(Object) },\n    });\n  });\n\n  it('should open report dialog when supervisor has signature', () => {\n    const dialogRef = {\n      afterClosed: () => of(false),\n    } as MatDialogRef<unknown>;\n    dialog.open.and.returnValue(dialogRef);\n\n    component.openReportDetails(mockReport);\n\n    expect(dialog.open).toHaveBeenCalledWith(jasmine.any(Function), {\n      width: '90vw',\n      height: '90vh',\n      maxWidth: '100vw',\n      maxHeight: '100vh',\n      data: { report: jasmine.any(Object) },\n    });\n  });\n\n  it('should reload reports when dialog is closed with result', () => {\n    const dialogRef = {\n      afterClosed: () => of(true),\n    } as MatDialogRef<unknown>;\n    dialog.open.and.returnValue(dialogRef);\n\n    spyOn(component, 'loadMonthlyReports');\n    component.openReportDetails(mockReport);\n    expect(component.loadMonthlyReports).toHaveBeenCalled();\n  });\n\n  it('should handle error when verifying supervisor information', () => {\n    supervisorService.getByEmail.and.returnValue(throwError(() => new Error()));\n    component.openReportDetails(mockReport);\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al cargar información del supervisor',\n    );\n  });\n});\n"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,8BAA8B;AACtE,SAA2BC,OAAO,QAAQ,uBAAuB;AACjE,SAASC,SAAS,QAAsB,0BAA0B;AAClE,SAASC,uBAAuB,QAAQ,sCAAsC;AAG9E,SAASC,oBAAoB,QAAQ,uDAAuD;AAC5F,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,iBAAiB,QAAQ,oDAAoD;AACtF,SAASC,iBAAiB,QAAQ,aAAa;AAC/C,SAASC,EAAE,EAAEC,UAAU,QAAQ,MAAM;AAErC,SAASC,iCAAiC,QAAQ,yCAAyC;AAE3FC,QAAQ,CAAC,mCAAmC,EAAE,MAAK;EACjD,IAAIC,SAA4C;EAChD,IAAIC,OAA4D;EAChE,IAAIC,oBAA0D;EAC9D,IAAIC,iBAAoD;EACxD,IAAIC,WAAwC;EAC5C,IAAIC,YAA0C;EAC9C,IAAIC,cAAiD;EACrD,IAAIC,MAAiC;EAErC,MAAMC,QAAQ,GAAG;IACfC,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,kBAAkB;IAC5BC,QAAQ,EAAE;GACX;EAED,MAAMC,cAAc,GAAG;IACrBH,EAAE,EAAE,CAAC;IACLI,QAAQ,EAAE,iBAAiB;IAC3BC,KAAK,EAAE,kBAAkB;IACzBC,gBAAgB,EAAEC,SAAS;IAC3BC,gBAAgB,EAAED,SAAS;IAC3BE,QAAQ,EAAE,SAAS;IACnBC,QAAQ,EAAE,eAAe;IACzBC,MAAM,EAAE;MAAEX,EAAE,EAAE,CAAC;MAAEY,IAAI,EAAE;IAAI;GAC5B;EAED,MAAMC,UAAU,GAAuC;IACrDC,SAAS,EAAE,CAAC;IACZC,aAAa,EAAE,CAAC;IAChBC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,YAAY;IACtBC,eAAe,EAAE,GAAG;IACpBC,aAAa,EAAE,IAAI;IACnBC,oBAAoB,EAAE,iBAAiB;IACvCC,mBAAmB,EAAE,iBAAiB;IACtCC,cAAc,EAAE,YAAY;IAC5BC,WAAW,EAAE,IAAI;IACjBC,gBAAgB,EAAE,CAAC;IACnBC,kBAAkB,EAAE;GACrB;EAEDC,UAAU,CAAC,MAAK;IACd,MAAMC,uBAAuB,GAAGC,OAAO,CAACC,YAAY,CAClD,sBAAsB,EACtB,CAAC,qCAAqC,EAAE,SAAS,CAAC,CACnD;IACD,MAAMC,oBAAoB,GAAGF,OAAO,CAACC,YAAY,CAAC,mBAAmB,EAAE,CACrE,YAAY,CACb,CAAC;IACF,MAAME,cAAc,GAAGH,OAAO,CAACC,YAAY,CAAC,aAAa,EAAE,CACzD,gBAAgB,CACjB,CAAC;IACF,MAAMG,eAAe,GAAGJ,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC;IACvE,MAAMI,iBAAiB,GAAGL,OAAO,CAACC,YAAY,CAAC,mBAAmB,EAAE,CAClE,MAAM,EACN,MAAM,CACP,CAAC;IACF,MAAMK,YAAY,GAAGN,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,aAAa,CAAC,CAAC;IAC1EK,YAAY,CAACC,WAAW,CAACC,GAAG,CAACC,WAAW,CAAClD,EAAE,CAAC,IAAI,CAAC,CAAC;IAClD,MAAMmD,SAAS,GAAGV,OAAO,CAACC,YAAY,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,CAAC;IAC7DS,SAAS,CAACC,IAAI,CAACH,GAAG,CAACC,WAAW,CAACH,YAAY,CAAC;IAE5CvD,OAAO,CAAC6D,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPpD,iCAAiC,EACjCX,uBAAuB,EACvBG,uBAAuB,CACxB;MACD6D,SAAS,EAAE,CACT;QAAEC,OAAO,EAAE7D,oBAAoB;QAAE8D,QAAQ,EAAEjB;MAAuB,CAAE,EACpE;QAAEgB,OAAO,EAAE1D,iBAAiB;QAAE2D,QAAQ,EAAEd;MAAoB,CAAE,EAC9D;QAAEa,OAAO,EAAE5D,WAAW;QAAE6D,QAAQ,EAAEb;MAAc,CAAE,EAClD;QAAEY,OAAO,EAAE3D,YAAY;QAAE4D,QAAQ,EAAEZ;MAAe,CAAE,EACpD;QAAEW,OAAO,EAAEzD,iBAAiB;QAAE0D,QAAQ,EAAEX;MAAiB,CAAE,EAC3D;QAAEU,OAAO,EAAE/D,SAAS;QAAEgE,QAAQ,EAAEN;MAAS,CAAE;KAE9C,CAAC;IAEF9C,OAAO,GAAGb,OAAO,CAACkE,eAAe,CAACxD,iCAAiC,CAAC;IACpEE,SAAS,GAAGC,OAAO,CAACsD,iBAAiB;IACrCrD,oBAAoB,GAAGd,OAAO,CAACoE,MAAM,CACnCjE,oBAAoB,CACmB;IACzCY,iBAAiB,GAAGf,OAAO,CAACoE,MAAM,CAChC9D,iBAAiB,CACmB;IACtCU,WAAW,GAAGhB,OAAO,CAACoE,MAAM,CAAChE,WAAW,CAAgC;IACxEa,YAAY,GAAGjB,OAAO,CAACoE,MAAM,CAAC/D,YAAY,CAAiC;IAC3Ea,cAAc,GAAGlB,OAAO,CAACoE,MAAM,CAC7B7D,iBAAiB,CACmB;IACtCY,MAAM,GAAGnB,OAAO,CAACoE,MAAM,CAACnE,SAAS,CAA8B;IAE/D,MAAMoE,uBAAuB,GAAG;MAC9B,GAAG7C,cAAc;MACjBG,gBAAgB,EAAE,QAAQ;MAC1BE,gBAAgB,EAAE;KACnB;IAEDb,WAAW,CAACsD,cAAc,CAACb,GAAG,CAACC,WAAW,CAACtC,QAAQ,CAAC;IACpDL,iBAAiB,CAACwD,UAAU,CAACd,GAAG,CAACC,WAAW,CAAClD,EAAE,CAAC6D,uBAAuB,CAAC,CAAC;IACzEvD,oBAAoB,CAAC0D,mCAAmC,CAACf,GAAG,CAACC,WAAW,CACtElD,EAAE,CAAC,CAAC0B,UAAU,CAAC,CAAC,CACjB;IACD,MAAMuC,iBAAiB,GAAG;MACxBpD,EAAE,EAAE,CAAC;MACLqD,YAAY,EAAE,CAAC;MACfC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACjCC,OAAO,EAAE,IAAID,IAAI,CAAC,YAAY,CAAC;MAC/BE,YAAY,EAAE,IAAIF,IAAI,CAAC,YAAY,CAAC;MACpCG,oBAAoB,EAAE,CAAC;MACvBC,mBAAmB,EAAE;QAAE3D,EAAE,EAAE,CAAC;QAAEY,IAAI,EAAE;MAAuB;KAC5D;IACDnB,oBAAoB,CAACmE,OAAO,CAACxB,GAAG,CAACC,WAAW,CAAClD,EAAE,CAACiE,iBAAiB,CAAC,CAAC;EACrE,CAAC,CAAC;EAEFS,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACvE,SAAS,CAAC,CAACwE,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,qCAAqC,EAAE,MAAK;IAC7CtE,SAAS,CAACyE,QAAQ,EAAE;IACpBF,MAAM,CAACjE,cAAc,CAACoE,IAAI,CAAC,CAACC,gBAAgB,EAAE;IAC9CJ,MAAM,CAACpE,iBAAiB,CAACwD,UAAU,CAAC,CAACiB,oBAAoB,CACvDpE,QAAQ,CAACE,QAAQ,CAClB;IACD6D,MAAM,CACJrE,oBAAoB,CAAC0D,mCAAmC,CACzD,CAACgB,oBAAoB,CAACpE,QAAQ,CAACE,QAAQ,CAAC;IACzC6D,MAAM,CAACvE,SAAS,CAAC6E,UAAU,CAACC,IAAI,CAAC,CAACC,OAAO,CAAC,CAACzD,UAAU,CAAC,CAAC;IACvDiD,MAAM,CAACvE,SAAS,CAACgF,kBAAkB,CAAC,CAACC,IAAI,CAACrE,cAAc,CAACC,QAAQ,CAAC;EACpE,CAAC,CAAC;EAEFyD,EAAE,CAAC,4CAA4C,EAAE,MAAK;IACpDlE,WAAW,CAACsD,cAAc,CAACb,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;IAChD9C,SAAS,CAACyE,QAAQ,EAAE;IACpBF,MAAM,CAAClE,YAAY,CAAC6E,KAAK,CAAC,CAACN,oBAAoB,CAC7C,+CAA+C,CAChD;EACH,CAAC,CAAC;EAEFN,EAAE,CAAC,yDAAyD,EAAE,MAAK;IACjEnE,iBAAiB,CAACwD,UAAU,CAACd,GAAG,CAACC,WAAW,CAACjD,UAAU,CAAC,MAAM,IAAIsF,KAAK,EAAE,CAAC,CAAC;IAC3EnF,SAAS,CAACyE,QAAQ,EAAE;IACpBF,MAAM,CAAClE,YAAY,CAAC6E,KAAK,CAAC,CAACN,oBAAoB,CAC7C,4CAA4C,CAC7C;EACH,CAAC,CAAC;EAEFN,EAAE,CAAC,kDAAkD,EAAE,MAAK;IAC1DpE,oBAAoB,CAAC0D,mCAAmC,CAACf,GAAG,CAACC,WAAW,CACtEjD,UAAU,CAAC,MAAM,IAAIsF,KAAK,EAAE,CAAC,CAC9B;IACDnF,SAAS,CAACyE,QAAQ,EAAE;IACpBF,MAAM,CAAClE,YAAY,CAAC6E,KAAK,CAAC,CAACN,oBAAoB,CAC7C,8BAA8B,CAC/B;EACH,CAAC,CAAC;EAEFN,EAAE,CAAC,qBAAqB,EAAE,MAAK;IAC7BtE,SAAS,CAACyE,QAAQ,EAAE;IACpB,MAAMW,KAAK,GAAG;MAAEC,MAAM,EAAE;QAAEC,KAAK,EAAE;MAAM;IAAE,CAAsB;IAC/DtF,SAAS,CAAC6E,UAAU,CAACU,SAAS,GAAGvF,SAAS,CAACuF,SAAS;IACpDvF,SAAS,CAACwF,WAAW,CAACJ,KAAK,CAAC;IAC5Bb,MAAM,CAACvE,SAAS,CAAC6E,UAAU,CAACY,MAAM,CAAC,CAACR,IAAI,CAAC,MAAM,CAAC;EAClD,CAAC,CAAC;EAEFX,EAAE,CAAC,gEAAgE,EAAE,MAAK;IACxEtE,SAAS,CAAC0F,iBAAiB,CACzB,IAAqD,CACtD;IACDnB,MAAM,CAAClE,YAAY,CAAC6E,KAAK,CAAC,CAACN,oBAAoB,CAC7C,wCAAwC,CACzC;EACH,CAAC,CAAC;EAEFN,EAAE,CAAC,8DAA8D,EAAE,MAAK;IACtElE,WAAW,CAACsD,cAAc,CAACb,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;IAChD9C,SAAS,CAAC0F,iBAAiB,CAACpE,UAAU,CAAC;IACvCiD,MAAM,CAAClE,YAAY,CAAC6E,KAAK,CAAC,CAACN,oBAAoB,CAC7C,+CAA+C,CAChD;EACH,CAAC,CAAC;EAEFN,EAAE,CAAC,uEAAuE,EAAE,MAAK;IAC/EnE,iBAAiB,CAACwD,UAAU,CAACd,GAAG,CAACC,WAAW,CAAClD,EAAE,CAACgB,cAAc,CAAC,CAAC;IAEhE,MAAM+E,SAAS,GAAG;MAChB/C,WAAW,EAAEA,CAAA,KAAMhD,EAAE,CAAC,KAAK;KACH;IAC1BW,MAAM,CAACyC,IAAI,CAACH,GAAG,CAACC,WAAW,CAAC6C,SAAS,CAAC;IAEtC3F,SAAS,CAAC0F,iBAAiB,CAACpE,UAAU,CAAC;IAEvCiD,MAAM,CAAChE,MAAM,CAACyC,IAAI,CAAC,CAAC4B,oBAAoB,CAACvC,OAAO,CAACuD,GAAG,CAACC,QAAQ,CAAC,EAAE;MAC9DC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,OAAO;MACjBC,SAAS,EAAE,OAAO;MAClBnB,IAAI,EAAE;QAAEoB,MAAM,EAAE7D,OAAO,CAACuD,GAAG,CAACO,MAAM;MAAC;KACpC,CAAC;EACJ,CAAC,CAAC;EAEF7B,EAAE,CAAC,yDAAyD,EAAE,MAAK;IACjE,MAAMqB,SAAS,GAAG;MAChB/C,WAAW,EAAEA,CAAA,KAAMhD,EAAE,CAAC,KAAK;KACH;IAC1BW,MAAM,CAACyC,IAAI,CAACH,GAAG,CAACC,WAAW,CAAC6C,SAAS,CAAC;IAEtC3F,SAAS,CAAC0F,iBAAiB,CAACpE,UAAU,CAAC;IAEvCiD,MAAM,CAAChE,MAAM,CAACyC,IAAI,CAAC,CAAC4B,oBAAoB,CAACvC,OAAO,CAACuD,GAAG,CAACC,QAAQ,CAAC,EAAE;MAC9DC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,OAAO;MACjBC,SAAS,EAAE,OAAO;MAClBnB,IAAI,EAAE;QAAEoB,MAAM,EAAE7D,OAAO,CAACuD,GAAG,CAACO,MAAM;MAAC;KACpC,CAAC;EACJ,CAAC,CAAC;EAEF7B,EAAE,CAAC,yDAAyD,EAAE,MAAK;IACjE,MAAMqB,SAAS,GAAG;MAChB/C,WAAW,EAAEA,CAAA,KAAMhD,EAAE,CAAC,IAAI;KACF;IAC1BW,MAAM,CAACyC,IAAI,CAACH,GAAG,CAACC,WAAW,CAAC6C,SAAS,CAAC;IAEtCS,KAAK,CAACpG,SAAS,EAAE,oBAAoB,CAAC;IACtCA,SAAS,CAAC0F,iBAAiB,CAACpE,UAAU,CAAC;IACvCiD,MAAM,CAACvE,SAAS,CAACqG,kBAAkB,CAAC,CAAC1B,gBAAgB,EAAE;EACzD,CAAC,CAAC;EAEFL,EAAE,CAAC,2DAA2D,EAAE,MAAK;IACnEnE,iBAAiB,CAACwD,UAAU,CAACd,GAAG,CAACC,WAAW,CAACjD,UAAU,CAAC,MAAM,IAAIsF,KAAK,EAAE,CAAC,CAAC;IAC3EnF,SAAS,CAAC0F,iBAAiB,CAACpE,UAAU,CAAC;IACvCiD,MAAM,CAAClE,YAAY,CAAC6E,KAAK,CAAC,CAACN,oBAAoB,CAC7C,4CAA4C,CAC7C;EACH,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}