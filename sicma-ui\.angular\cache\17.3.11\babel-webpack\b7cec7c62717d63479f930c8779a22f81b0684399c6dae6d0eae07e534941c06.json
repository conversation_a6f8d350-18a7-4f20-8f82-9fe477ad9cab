{"ast": null, "code": "function cov_tehfx1xm0() {\n  var path = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-dashboard\\\\components\\\\monthly-reports-tab\\\\monthly-report-dialog\\\\monthly-report-initial-documentation\\\\tax-info\\\\tax-info.component.ts\";\n  var hash = \"5184e650ffb461b153f55f5f1a6d1daaef11b631\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-dashboard\\\\components\\\\monthly-reports-tab\\\\monthly-report-dialog\\\\monthly-report-initial-documentation\\\\tax-info\\\\tax-info.component.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 16,\n          column: 23\n        },\n        end: {\n          line: 100,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 18,\n          column: 8\n        },\n        end: {\n          line: 18,\n          column: 21\n        }\n      },\n      \"2\": {\n        start: {\n          line: 19,\n          column: 8\n        },\n        end: {\n          line: 19,\n          column: 49\n        }\n      },\n      \"3\": {\n        start: {\n          line: 20,\n          column: 8\n        },\n        end: {\n          line: 20,\n          column: 27\n        }\n      },\n      \"4\": {\n        start: {\n          line: 21,\n          column: 8\n        },\n        end: {\n          line: 21,\n          column: 34\n        }\n      },\n      \"5\": {\n        start: {\n          line: 22,\n          column: 8\n        },\n        end: {\n          line: 22,\n          column: 45\n        }\n      },\n      \"6\": {\n        start: {\n          line: 23,\n          column: 8\n        },\n        end: {\n          line: 26,\n          column: 11\n        }\n      },\n      \"7\": {\n        start: {\n          line: 27,\n          column: 8\n        },\n        end: {\n          line: 27,\n          column: 32\n        }\n      },\n      \"8\": {\n        start: {\n          line: 28,\n          column: 8\n        },\n        end: {\n          line: 28,\n          column: 36\n        }\n      },\n      \"9\": {\n        start: {\n          line: 29,\n          column: 8\n        },\n        end: {\n          line: 29,\n          column: 29\n        }\n      },\n      \"10\": {\n        start: {\n          line: 32,\n          column: 8\n        },\n        end: {\n          line: 32,\n          column: 30\n        }\n      },\n      \"11\": {\n        start: {\n          line: 33,\n          column: 8\n        },\n        end: {\n          line: 35,\n          column: 11\n        }\n      },\n      \"12\": {\n        start: {\n          line: 34,\n          column: 12\n        },\n        end: {\n          line: 34,\n          column: 35\n        }\n      },\n      \"13\": {\n        start: {\n          line: 38,\n          column: 8\n        },\n        end: {\n          line: 43,\n          column: 9\n        }\n      },\n      \"14\": {\n        start: {\n          line: 39,\n          column: 25\n        },\n        end: {\n          line: 39,\n          column: 60\n        }\n      },\n      \"15\": {\n        start: {\n          line: 40,\n          column: 12\n        },\n        end: {\n          line: 42,\n          column: 15\n        }\n      },\n      \"16\": {\n        start: {\n          line: 46,\n          column: 8\n        },\n        end: {\n          line: 51,\n          column: 11\n        }\n      },\n      \"17\": {\n        start: {\n          line: 47,\n          column: 32\n        },\n        end: {\n          line: 47,\n          column: 57\n        }\n      },\n      \"18\": {\n        start: {\n          line: 49,\n          column: 16\n        },\n        end: {\n          line: 49,\n          column: 101\n        }\n      },\n      \"19\": {\n        start: {\n          line: 54,\n          column: 8\n        },\n        end: {\n          line: 54,\n          column: 47\n        }\n      },\n      \"20\": {\n        start: {\n          line: 57,\n          column: 21\n        },\n        end: {\n          line: 57,\n          column: 44\n        }\n      },\n      \"21\": {\n        start: {\n          line: 58,\n          column: 8\n        },\n        end: {\n          line: 74,\n          column: 9\n        }\n      },\n      \"22\": {\n        start: {\n          line: 59,\n          column: 12\n        },\n        end: {\n          line: 72,\n          column: 13\n        }\n      },\n      \"23\": {\n        start: {\n          line: 60,\n          column: 16\n        },\n        end: {\n          line: 68,\n          column: 17\n        }\n      },\n      \"24\": {\n        start: {\n          line: 62,\n          column: 20\n        },\n        end: {\n          line: 62,\n          column: 66\n        }\n      },\n      \"25\": {\n        start: {\n          line: 63,\n          column: 20\n        },\n        end: {\n          line: 63,\n          column: 44\n        }\n      },\n      \"26\": {\n        start: {\n          line: 64,\n          column: 20\n        },\n        end: {\n          line: 64,\n          column: 53\n        }\n      },\n      \"27\": {\n        start: {\n          line: 67,\n          column: 20\n        },\n        end: {\n          line: 67,\n          column: 71\n        }\n      },\n      \"28\": {\n        start: {\n          line: 71,\n          column: 16\n        },\n        end: {\n          line: 71,\n          column: 66\n        }\n      },\n      \"29\": {\n        start: {\n          line: 73,\n          column: 12\n        },\n        end: {\n          line: 73,\n          column: 65\n        }\n      },\n      \"30\": {\n        start: {\n          line: 77,\n          column: 8\n        },\n        end: {\n          line: 79,\n          column: 9\n        }\n      },\n      \"31\": {\n        start: {\n          line: 78,\n          column: 12\n        },\n        end: {\n          line: 78,\n          column: 67\n        }\n      },\n      \"32\": {\n        start: {\n          line: 82,\n          column: 33\n        },\n        end: {\n          line: 82,\n          column: 76\n        }\n      },\n      \"33\": {\n        start: {\n          line: 83,\n          column: 27\n        },\n        end: {\n          line: 83,\n          column: 77\n        }\n      },\n      \"34\": {\n        start: {\n          line: 84,\n          column: 32\n        },\n        end: {\n          line: 84,\n          column: 73\n        }\n      },\n      \"35\": {\n        start: {\n          line: 85,\n          column: 8\n        },\n        end: {\n          line: 85,\n          column: 67\n        }\n      },\n      \"36\": {\n        start: {\n          line: 88,\n          column: 8\n        },\n        end: {\n          line: 88,\n          column: 78\n        }\n      },\n      \"37\": {\n        start: {\n          line: 88,\n          column: 48\n        },\n        end: {\n          line: 88,\n          column: 64\n        }\n      },\n      \"38\": {\n        start: {\n          line: 90,\n          column: 13\n        },\n        end: {\n          line: 94,\n          column: 6\n        }\n      },\n      \"39\": {\n        start: {\n          line: 90,\n          column: 41\n        },\n        end: {\n          line: 94,\n          column: 5\n        }\n      },\n      \"40\": {\n        start: {\n          line: 95,\n          column: 13\n        },\n        end: {\n          line: 99,\n          column: 6\n        }\n      },\n      \"41\": {\n        start: {\n          line: 101,\n          column: 0\n        },\n        end: {\n          line: 121,\n          column: 21\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 17,\n            column: 4\n          },\n          end: {\n            line: 17,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 17,\n            column: 45\n          },\n          end: {\n            line: 30,\n            column: 5\n          }\n        },\n        line: 17\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 31,\n            column: 4\n          },\n          end: {\n            line: 31,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 31,\n            column: 15\n          },\n          end: {\n            line: 36,\n            column: 5\n          }\n        },\n        line: 31\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 33,\n            column: 41\n          },\n          end: {\n            line: 33,\n            column: 42\n          }\n        },\n        loc: {\n          start: {\n            line: 33,\n            column: 47\n          },\n          end: {\n            line: 35,\n            column: 9\n          }\n        },\n        line: 33\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 37,\n            column: 4\n          },\n          end: {\n            line: 37,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 37,\n            column: 25\n          },\n          end: {\n            line: 44,\n            column: 5\n          }\n        },\n        line: 37\n      },\n      \"4\": {\n        name: \"(anonymous_4)\",\n        decl: {\n          start: {\n            line: 45,\n            column: 4\n          },\n          end: {\n            line: 45,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 45,\n            column: 21\n          },\n          end: {\n            line: 52,\n            column: 5\n          }\n        },\n        line: 45\n      },\n      \"5\": {\n        name: \"(anonymous_5)\",\n        decl: {\n          start: {\n            line: 47,\n            column: 18\n          },\n          end: {\n            line: 47,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 47,\n            column: 32\n          },\n          end: {\n            line: 47,\n            column: 57\n          }\n        },\n        line: 47\n      },\n      \"6\": {\n        name: \"(anonymous_6)\",\n        decl: {\n          start: {\n            line: 48,\n            column: 19\n          },\n          end: {\n            line: 48,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 48,\n            column: 30\n          },\n          end: {\n            line: 50,\n            column: 13\n          }\n        },\n        line: 48\n      },\n      \"7\": {\n        name: \"(anonymous_7)\",\n        decl: {\n          start: {\n            line: 53,\n            column: 4\n          },\n          end: {\n            line: 53,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 53,\n            column: 20\n          },\n          end: {\n            line: 55,\n            column: 5\n          }\n        },\n        line: 53\n      },\n      \"8\": {\n        name: \"(anonymous_8)\",\n        decl: {\n          start: {\n            line: 56,\n            column: 4\n          },\n          end: {\n            line: 56,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 56,\n            column: 39\n          },\n          end: {\n            line: 75,\n            column: 5\n          }\n        },\n        line: 56\n      },\n      \"9\": {\n        name: \"(anonymous_9)\",\n        decl: {\n          start: {\n            line: 76,\n            column: 4\n          },\n          end: {\n            line: 76,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 76,\n            column: 19\n          },\n          end: {\n            line: 80,\n            column: 5\n          }\n        },\n        line: 76\n      },\n      \"10\": {\n        name: \"(anonymous_10)\",\n        decl: {\n          start: {\n            line: 81,\n            column: 4\n          },\n          end: {\n            line: 81,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 81,\n            column: 18\n          },\n          end: {\n            line: 86,\n            column: 5\n          }\n        },\n        line: 81\n      },\n      \"11\": {\n        name: \"(anonymous_11)\",\n        decl: {\n          start: {\n            line: 87,\n            column: 4\n          },\n          end: {\n            line: 87,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 87,\n            column: 25\n          },\n          end: {\n            line: 89,\n            column: 5\n          }\n        },\n        line: 87\n      },\n      \"12\": {\n        name: \"(anonymous_12)\",\n        decl: {\n          start: {\n            line: 88,\n            column: 36\n          },\n          end: {\n            line: 88,\n            column: 37\n          }\n        },\n        loc: {\n          start: {\n            line: 88,\n            column: 48\n          },\n          end: {\n            line: 88,\n            column: 64\n          }\n        },\n        line: 88\n      },\n      \"13\": {\n        name: \"(anonymous_13)\",\n        decl: {\n          start: {\n            line: 90,\n            column: 35\n          },\n          end: {\n            line: 90,\n            column: 36\n          }\n        },\n        loc: {\n          start: {\n            line: 90,\n            column: 41\n          },\n          end: {\n            line: 94,\n            column: 5\n          }\n        },\n        line: 90\n      }\n    },\n    branchMap: {\n      \"0\": {\n        loc: {\n          start: {\n            line: 38,\n            column: 8\n          },\n          end: {\n            line: 43,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 38,\n            column: 8\n          },\n          end: {\n            line: 43,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 38\n      },\n      \"1\": {\n        loc: {\n          start: {\n            line: 49,\n            column: 33\n          },\n          end: {\n            line: 49,\n            column: 99\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 49,\n            column: 33\n          },\n          end: {\n            line: 49,\n            column: 52\n          }\n        }, {\n          start: {\n            line: 49,\n            column: 56\n          },\n          end: {\n            line: 49,\n            column: 99\n          }\n        }],\n        line: 49\n      },\n      \"2\": {\n        loc: {\n          start: {\n            line: 58,\n            column: 8\n          },\n          end: {\n            line: 74,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 58,\n            column: 8\n          },\n          end: {\n            line: 74,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 58\n      },\n      \"3\": {\n        loc: {\n          start: {\n            line: 59,\n            column: 12\n          },\n          end: {\n            line: 72,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 59,\n            column: 12\n          },\n          end: {\n            line: 72,\n            column: 13\n          }\n        }, {\n          start: {\n            line: 70,\n            column: 17\n          },\n          end: {\n            line: 72,\n            column: 13\n          }\n        }],\n        line: 59\n      },\n      \"4\": {\n        loc: {\n          start: {\n            line: 60,\n            column: 16\n          },\n          end: {\n            line: 68,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 60,\n            column: 16\n          },\n          end: {\n            line: 68,\n            column: 17\n          }\n        }, {\n          start: {\n            line: 66,\n            column: 21\n          },\n          end: {\n            line: 68,\n            column: 17\n          }\n        }],\n        line: 60\n      },\n      \"5\": {\n        loc: {\n          start: {\n            line: 77,\n            column: 8\n          },\n          end: {\n            line: 79,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 77,\n            column: 8\n          },\n          end: {\n            line: 79,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 77\n      },\n      \"6\": {\n        loc: {\n          start: {\n            line: 85,\n            column: 15\n          },\n          end: {\n            line: 85,\n            column: 66\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 85,\n            column: 15\n          },\n          end: {\n            line: 85,\n            column: 31\n          }\n        }, {\n          start: {\n            line: 85,\n            column: 36\n          },\n          end: {\n            line: 85,\n            column: 46\n          }\n        }, {\n          start: {\n            line: 85,\n            column: 50\n          },\n          end: {\n            line: 85,\n            column: 65\n          }\n        }],\n        line: 85\n      },\n      \"7\": {\n        loc: {\n          start: {\n            line: 88,\n            column: 15\n          },\n          end: {\n            line: 88,\n            column: 77\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 88,\n            column: 15\n          },\n          end: {\n            line: 88,\n            column: 71\n          }\n        }, {\n          start: {\n            line: 88,\n            column: 75\n          },\n          end: {\n            line: 88,\n            column: 77\n          }\n        }],\n        line: 88\n      }\n    },\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0,\n      \"36\": 0,\n      \"37\": 0,\n      \"38\": 0,\n      \"39\": 0,\n      \"40\": 0,\n      \"41\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0\n    },\n    b: {\n      \"0\": [0, 0],\n      \"1\": [0, 0],\n      \"2\": [0, 0],\n      \"3\": [0, 0],\n      \"4\": [0, 0],\n      \"5\": [0, 0],\n      \"6\": [0, 0, 0],\n      \"7\": [0, 0]\n    },\n    inputSourceMap: {\n      version: 3,\n      file: \"tax-info.component.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-dashboard\\\\components\\\\monthly-reports-tab\\\\monthly-report-dialog\\\\monthly-report-initial-documentation\\\\tax-info\\\\tax-info.component.ts\"],\n      names: [],\n      mappings: \";;;AAAA,OAAO,EACL,SAAS,EACT,YAAY,EACZ,KAAK,EAGL,MAAM,GAEP,MAAM,eAAe,CAAC;AACvB,OAAO,EACL,WAAW,EAEX,mBAAmB,EACnB,UAAU,GACX,MAAM,gBAAgB,CAAC;AACxB,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AACpE,OAAO,EAAE,SAAS,EAAE,MAAM,wBAAwB,CAAC;AACnD,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,8BAA8B,CAAC;AAChF,OAAO,EAAE,OAAO,EAAE,MAAM,wBAAwB,CAAC;AACjD,OAAO,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AACnD,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AACrD,OAAO,EAAE,UAAU,EAAE,MAAM,2BAA2B,CAAC;AAGvD,OAAO,EAAE,gBAAgB,EAAE,MAAM,mDAAmD,CAAC;AACrF,OAAO,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAC9D,OAAO,EACL,iBAAiB,EACjB,gBAAgB,GACjB,MAAM,oCAAoC,CAAC;AAqBrC,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAc3B,YACU,EAAe,EACf,gBAAkC,EAClC,KAAmB;QAFnB,OAAE,GAAF,EAAE,CAAa;QACf,qBAAgB,GAAhB,gBAAgB,CAAkB;QAClC,UAAK,GAAL,KAAK,CAAc;QAfpB,iBAAY,GAAG,KAAK;QACnB,eAAU,GAAG,IAAI,YAAY,EAAQ;QAE/C,SAAI,GAAc,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;YAC9B,UAAU,EAAE,CAAC,EAAE,EAAE,UAAU,CAAC,QAAQ,CAAC;YACrC,iBAAiB,EAAE,CAAC,IAAI,EAAE,CAAC,iBAAiB,EAAE,EAAE,gBAAgB,EAAE,CAAC,CAAC;SACrE,CAAC,CAAC;QAEH,gBAAW,GAAgB,IAAI,CAAC;QAChC,oBAAe,GAAkB,IAAI,CAAC;QACtC,eAAU,GAAgB,EAAE,CAAC;IAM1B,CAAC;IAEJ,QAAQ;QACN,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,EAAE;YACpC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,WAAW,CAAC,OAAsB;QAChC,IAAI,OAAO,CAAC,aAAa,CAAC,EAAE,YAAY,EAAE,CAAC;YACzC,MAAM,IAAI,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC;YACjD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;gBACnB,UAAU,EAAE,IAAI,CAAC,WAAW;aAC7B,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,cAAc;QACZ,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,SAAS,CAAC;YACvC,IAAI,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC;YAC9C,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,2CAA2C,CAAC,CAAC;YACvF,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAEO,SAAS,CAAC,IAAU;QAC1B,OAAO,IAAI,CAAC,IAAI,KAAK,iBAAiB,CAAC;IACzC,CAAC;IAED,cAAc,CAAC,KAAY,EAAE,WAAmB;QAC9C,MAAM,IAAI,GAAI,KAAK,CAAC,MAA2B,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;QAC3D,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;gBACzB,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC;oBAC7B,MAAM;oBACN,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;oBACxB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC;gBACnC,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;gBACrD,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;YACpD,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,sBAAsB,EAAE,CAAC;QACvD,CAAC;IACH,CAAC;IAED,YAAY;QACV,IAAI,IAAI,CAAC,WAAW,EAAE,cAAc,EAAE,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED,IAAI,OAAO;QACT,MAAM,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,KAAK,CAAC,CAAC;QACrE,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE,KAAK,CAAC,CAAC;QACtE,MAAM,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;QAElE,OAAO,gBAAgB,IAAI,CAAC,UAAU,IAAI,eAAe,CAAC,CAAC;IAC7D,CAAC;IAED,gBAAgB,CAAC,EAAU;QACzB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC;IACxE,CAAC;;;;;;;8BAnFA,KAAK;+BACL,KAAK;6BACL,MAAM;;;AAHI,gBAAgB;IAnB5B,SAAS,CAAC;QACT,QAAQ,EAAE,cAAc;QACxB,8BAAwC;QAExC,UAAU,EAAE,IAAI;QAChB,OAAO,EAAE;YACP,mBAAmB;YACnB,OAAO;YACP,YAAY;YACZ,QAAQ;YACR,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,SAAS;YACT,aAAa;YACb,UAAU;SACX;;KACF,CAAC;GACW,gBAAgB,CAqF5B\",\n      sourcesContent: [\"import {\\n  Component,\\n  EventEmitter,\\n  Input,\\n  OnChanges,\\n  OnInit,\\n  Output,\\n  SimpleChanges,\\n} from '@angular/core';\\nimport {\\n  FormBuilder,\\n  FormGroup,\\n  ReactiveFormsModule,\\n  Validators,\\n} from '@angular/forms';\\nimport { MatButton, MatIconButton } from '@angular/material/button';\\nimport { MatOption } from '@angular/material/core';\\nimport { MatError, MatFormField, MatLabel } from '@angular/material/form-field';\\nimport { MatIcon } from '@angular/material/icon';\\nimport { MatInput } from '@angular/material/input';\\nimport { MatSelect } from '@angular/material/select';\\nimport { MatTooltip } from '@angular/material/tooltip';\\nimport { InitialReportDocumentation } from '@contractor-dashboard/models/initial-report-documentation.model';\\nimport { TaxRegime } from '@contractor-dashboard/models/tax-regime.model';\\nimport { TaxRegimeService } from '@contractor-dashboard/services/tax-regime.service';\\nimport { AlertService } from '@shared/services/alert.service';\\nimport {\\n  fileSizeValidator,\\n  pdfFileValidator,\\n} from '@shared/validators/file.validators';\\n\\n@Component({\\n  selector: 'app-tax-info',\\n  templateUrl: './tax-info.component.html',\\n  styleUrl: './tax-info.component.scss',\\n  standalone: true,\\n  imports: [\\n    ReactiveFormsModule,\\n    MatIcon,\\n    MatFormField,\\n    MatLabel,\\n    MatSelect,\\n    MatOption,\\n    MatInput,\\n    MatError,\\n    MatButton,\\n    MatIconButton,\\n    MatTooltip,\\n  ],\\n})\\nexport class TaxInfoComponent implements OnInit, OnChanges {\\n  @Input() initialData?: InitialReportDocumentation;\\n  @Input() isSupervisor = false;\\n  @Output() formChange = new EventEmitter<void>();\\n\\n  form: FormGroup = this.fb.group({\\n    regimeType: ['', Validators.required],\\n    regimeSupportFile: [null, [fileSizeValidator(), pdfFileValidator()]],\\n  });\\n\\n  taxFormFile: File | null = null;\\n  taxFormFileName: string | null = null;\\n  taxRegimes: TaxRegime[] = [];\\n\\n  constructor(\\n    private fb: FormBuilder,\\n    private taxRegimeService: TaxRegimeService,\\n    private alert: AlertService,\\n  ) {}\\n\\n  ngOnInit(): void {\\n    this.loadTaxRegimes();\\n    this.form.valueChanges.subscribe(() => {\\n      this.formChange.emit();\\n    });\\n  }\\n\\n  ngOnChanges(changes: SimpleChanges): void {\\n    if (changes['initialData']?.currentValue) {\\n      const data = changes['initialData'].currentValue;\\n      this.form.patchValue({\\n        regimeType: data.taxRegimeId,\\n      });\\n    }\\n  }\\n\\n  loadTaxRegimes(): void {\\n    this.taxRegimeService.getAll().subscribe({\\n      next: (regimes) => (this.taxRegimes = regimes),\\n      error: (error) => {\\n        this.alert.error(error.error?.detail ?? 'Error al cargar los reg\\xEDmenes tributarios');\\n      },\\n    });\\n  }\\n\\n  private isPdfFile(file: File): boolean {\\n    return file.type === 'application/pdf';\\n  }\\n\\n  onFileSelected(event: Event, controlName: string): void {\\n    const file = (event.target as HTMLInputElement).files?.[0];\\n    if (file) {\\n      if (this.isPdfFile(file)) {\\n        if (file.size <= 1024 * 1024) {\\n          // 1MB\\n          this.form.patchValue({ [controlName]: file });\\n          this.taxFormFile = file;\\n          this.taxFormFileName = file.name;\\n        } else {\\n          this.alert.error('El archivo no debe superar 1MB');\\n        }\\n      } else {\\n        this.alert.error('Solo se permiten archivos PDF');\\n      }\\n      this.form.get(controlName)?.updateValueAndValidity();\\n    }\\n  }\\n\\n  downloadFile(): void {\\n    if (this.initialData?.taxFormFileUrl) {\\n      window.open(this.initialData.taxFormFileUrl, '_blank');\\n    }\\n  }\\n\\n  get isValid(): boolean {\\n    const basicFieldsValid = Boolean(this.form.get('regimeType')?.valid);\\n    const hasNewFile = Boolean(this.form.get('regimeSupportFile')?.value);\\n    const hasExistingFile = Boolean(this.initialData?.taxFormFileUrl);\\n\\n    return basicFieldsValid && (hasNewFile || hasExistingFile);\\n  }\\n\\n  getTaxRegimeName(id: number): string {\\n    return this.taxRegimes.find((regime) => regime.id === id)?.name || '';\\n  }\\n}\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"5184e650ffb461b153f55f5f1a6d1daaef11b631\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_tehfx1xm0 = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_tehfx1xm0();\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./tax-info.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./tax-info.component.scss?ngResource\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatButton, MatIconButton } from '@angular/material/button';\nimport { MatOption } from '@angular/material/core';\nimport { MatError, MatFormField, MatLabel } from '@angular/material/form-field';\nimport { MatIcon } from '@angular/material/icon';\nimport { MatInput } from '@angular/material/input';\nimport { MatSelect } from '@angular/material/select';\nimport { MatTooltip } from '@angular/material/tooltip';\nimport { TaxRegimeService } from '@contractor-dashboard/services/tax-regime.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { fileSizeValidator, pdfFileValidator } from '@shared/validators/file.validators';\ncov_tehfx1xm0().s[0]++;\nlet TaxInfoComponent = class TaxInfoComponent {\n  constructor(fb, taxRegimeService, alert) {\n    cov_tehfx1xm0().f[0]++;\n    cov_tehfx1xm0().s[1]++;\n    this.fb = fb;\n    cov_tehfx1xm0().s[2]++;\n    this.taxRegimeService = taxRegimeService;\n    cov_tehfx1xm0().s[3]++;\n    this.alert = alert;\n    cov_tehfx1xm0().s[4]++;\n    this.isSupervisor = false;\n    cov_tehfx1xm0().s[5]++;\n    this.formChange = new EventEmitter();\n    cov_tehfx1xm0().s[6]++;\n    this.form = this.fb.group({\n      regimeType: ['', Validators.required],\n      regimeSupportFile: [null, [fileSizeValidator(), pdfFileValidator()]]\n    });\n    cov_tehfx1xm0().s[7]++;\n    this.taxFormFile = null;\n    cov_tehfx1xm0().s[8]++;\n    this.taxFormFileName = null;\n    cov_tehfx1xm0().s[9]++;\n    this.taxRegimes = [];\n  }\n  ngOnInit() {\n    cov_tehfx1xm0().f[1]++;\n    cov_tehfx1xm0().s[10]++;\n    this.loadTaxRegimes();\n    cov_tehfx1xm0().s[11]++;\n    this.form.valueChanges.subscribe(() => {\n      cov_tehfx1xm0().f[2]++;\n      cov_tehfx1xm0().s[12]++;\n      this.formChange.emit();\n    });\n  }\n  ngOnChanges(changes) {\n    cov_tehfx1xm0().f[3]++;\n    cov_tehfx1xm0().s[13]++;\n    if (changes['initialData']?.currentValue) {\n      cov_tehfx1xm0().b[0][0]++;\n      const data = (cov_tehfx1xm0().s[14]++, changes['initialData'].currentValue);\n      cov_tehfx1xm0().s[15]++;\n      this.form.patchValue({\n        regimeType: data.taxRegimeId\n      });\n    } else {\n      cov_tehfx1xm0().b[0][1]++;\n    }\n  }\n  loadTaxRegimes() {\n    cov_tehfx1xm0().f[4]++;\n    cov_tehfx1xm0().s[16]++;\n    this.taxRegimeService.getAll().subscribe({\n      next: regimes => {\n        cov_tehfx1xm0().f[5]++;\n        cov_tehfx1xm0().s[17]++;\n        return this.taxRegimes = regimes;\n      },\n      error: error => {\n        cov_tehfx1xm0().f[6]++;\n        cov_tehfx1xm0().s[18]++;\n        this.alert.error((cov_tehfx1xm0().b[1][0]++, error.error?.detail) ?? (cov_tehfx1xm0().b[1][1]++, 'Error al cargar los regímenes tributarios'));\n      }\n    });\n  }\n  isPdfFile(file) {\n    cov_tehfx1xm0().f[7]++;\n    cov_tehfx1xm0().s[19]++;\n    return file.type === 'application/pdf';\n  }\n  onFileSelected(event, controlName) {\n    cov_tehfx1xm0().f[8]++;\n    const file = (cov_tehfx1xm0().s[20]++, event.target.files?.[0]);\n    cov_tehfx1xm0().s[21]++;\n    if (file) {\n      cov_tehfx1xm0().b[2][0]++;\n      cov_tehfx1xm0().s[22]++;\n      if (this.isPdfFile(file)) {\n        cov_tehfx1xm0().b[3][0]++;\n        cov_tehfx1xm0().s[23]++;\n        if (file.size <= 1024 * 1024) {\n          cov_tehfx1xm0().b[4][0]++;\n          cov_tehfx1xm0().s[24]++;\n          // 1MB\n          this.form.patchValue({\n            [controlName]: file\n          });\n          cov_tehfx1xm0().s[25]++;\n          this.taxFormFile = file;\n          cov_tehfx1xm0().s[26]++;\n          this.taxFormFileName = file.name;\n        } else {\n          cov_tehfx1xm0().b[4][1]++;\n          cov_tehfx1xm0().s[27]++;\n          this.alert.error('El archivo no debe superar 1MB');\n        }\n      } else {\n        cov_tehfx1xm0().b[3][1]++;\n        cov_tehfx1xm0().s[28]++;\n        this.alert.error('Solo se permiten archivos PDF');\n      }\n      cov_tehfx1xm0().s[29]++;\n      this.form.get(controlName)?.updateValueAndValidity();\n    } else {\n      cov_tehfx1xm0().b[2][1]++;\n    }\n  }\n  downloadFile() {\n    cov_tehfx1xm0().f[9]++;\n    cov_tehfx1xm0().s[30]++;\n    if (this.initialData?.taxFormFileUrl) {\n      cov_tehfx1xm0().b[5][0]++;\n      cov_tehfx1xm0().s[31]++;\n      window.open(this.initialData.taxFormFileUrl, '_blank');\n    } else {\n      cov_tehfx1xm0().b[5][1]++;\n    }\n  }\n  get isValid() {\n    cov_tehfx1xm0().f[10]++;\n    const basicFieldsValid = (cov_tehfx1xm0().s[32]++, Boolean(this.form.get('regimeType')?.valid));\n    const hasNewFile = (cov_tehfx1xm0().s[33]++, Boolean(this.form.get('regimeSupportFile')?.value));\n    const hasExistingFile = (cov_tehfx1xm0().s[34]++, Boolean(this.initialData?.taxFormFileUrl));\n    cov_tehfx1xm0().s[35]++;\n    return (cov_tehfx1xm0().b[6][0]++, basicFieldsValid) && ((cov_tehfx1xm0().b[6][1]++, hasNewFile) || (cov_tehfx1xm0().b[6][2]++, hasExistingFile));\n  }\n  getTaxRegimeName(id) {\n    cov_tehfx1xm0().f[11]++;\n    cov_tehfx1xm0().s[36]++;\n    return (cov_tehfx1xm0().b[7][0]++, this.taxRegimes.find(regime => {\n      cov_tehfx1xm0().f[12]++;\n      cov_tehfx1xm0().s[37]++;\n      return regime.id === id;\n    })?.name) || (cov_tehfx1xm0().b[7][1]++, '');\n  }\n  static {\n    cov_tehfx1xm0().s[38]++;\n    this.ctorParameters = () => {\n      cov_tehfx1xm0().f[13]++;\n      cov_tehfx1xm0().s[39]++;\n      return [{\n        type: FormBuilder\n      }, {\n        type: TaxRegimeService\n      }, {\n        type: AlertService\n      }];\n    };\n  }\n  static {\n    cov_tehfx1xm0().s[40]++;\n    this.propDecorators = {\n      initialData: [{\n        type: Input\n      }],\n      isSupervisor: [{\n        type: Input\n      }],\n      formChange: [{\n        type: Output\n      }]\n    };\n  }\n};\ncov_tehfx1xm0().s[41]++;\nTaxInfoComponent = __decorate([Component({\n  selector: 'app-tax-info',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: true,\n  imports: [ReactiveFormsModule, MatIcon, MatFormField, MatLabel, MatSelect, MatOption, MatInput, MatError, MatButton, MatIconButton, MatTooltip],\n  styles: [__NG_CLI_RESOURCE__1]\n})], TaxInfoComponent);\nexport { TaxInfoComponent };", "map": {"version": 3, "names": ["cov_tehfx1xm0", "actualCoverage", "Component", "EventEmitter", "Input", "Output", "FormBuilder", "ReactiveFormsModule", "Validators", "MatButton", "MatIconButton", "MatOption", "<PERSON><PERSON><PERSON><PERSON>", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatIcon", "MatInput", "MatSelect", "MatTooltip", "TaxRegimeService", "AlertService", "fileSizeValidator", "pdfFileValidator", "s", "TaxInfoComponent", "constructor", "fb", "taxRegimeService", "alert", "f", "isSupervisor", "formChange", "form", "group", "regimeType", "required", "regimeSupportFile", "taxFormFile", "taxFormFileName", "taxRegimes", "ngOnInit", "loadTaxRegimes", "valueChanges", "subscribe", "emit", "ngOnChanges", "changes", "currentValue", "b", "data", "patchValue", "taxRegimeId", "getAll", "next", "regimes", "error", "detail", "isPdfFile", "file", "type", "onFileSelected", "event", "controlName", "target", "files", "size", "name", "get", "updateValueAndValidity", "downloadFile", "initialData", "taxFormFileUrl", "window", "open", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Boolean", "valid", "hasNewFile", "value", "hasExistingFile", "getTaxRegimeName", "id", "find", "regime", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\components\\monthly-reports-tab\\monthly-report-dialog\\monthly-report-initial-documentation\\tax-info\\tax-info.component.ts"], "sourcesContent": ["import {\n  Component,\n  EventEmitter,\n  Input,\n  OnChanges,\n  OnInit,\n  Output,\n  SimpleChanges,\n} from '@angular/core';\nimport {\n  FormBuilder,\n  FormGroup,\n  ReactiveFormsModule,\n  Validators,\n} from '@angular/forms';\nimport { MatButton, MatIconButton } from '@angular/material/button';\nimport { MatOption } from '@angular/material/core';\nimport { MatError, MatFormField, MatLabel } from '@angular/material/form-field';\nimport { MatIcon } from '@angular/material/icon';\nimport { MatInput } from '@angular/material/input';\nimport { MatSelect } from '@angular/material/select';\nimport { MatTooltip } from '@angular/material/tooltip';\nimport { InitialReportDocumentation } from '@contractor-dashboard/models/initial-report-documentation.model';\nimport { TaxRegime } from '@contractor-dashboard/models/tax-regime.model';\nimport { TaxRegimeService } from '@contractor-dashboard/services/tax-regime.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport {\n  fileSizeValidator,\n  pdfFileValidator,\n} from '@shared/validators/file.validators';\n\n@Component({\n  selector: 'app-tax-info',\n  templateUrl: './tax-info.component.html',\n  styleUrl: './tax-info.component.scss',\n  standalone: true,\n  imports: [\n    ReactiveFormsModule,\n    MatIcon,\n    MatFormField,\n    MatLabel,\n    MatSelect,\n    MatOption,\n    MatInput,\n    MatError,\n    MatButton,\n    MatIconButton,\n    MatTooltip,\n  ],\n})\nexport class TaxInfoComponent implements OnInit, OnChanges {\n  @Input() initialData?: InitialReportDocumentation;\n  @Input() isSupervisor = false;\n  @Output() formChange = new EventEmitter<void>();\n\n  form: FormGroup = this.fb.group({\n    regimeType: ['', Validators.required],\n    regimeSupportFile: [null, [fileSizeValidator(), pdfFileValidator()]],\n  });\n\n  taxFormFile: File | null = null;\n  taxFormFileName: string | null = null;\n  taxRegimes: TaxRegime[] = [];\n\n  constructor(\n    private fb: FormBuilder,\n    private taxRegimeService: TaxRegimeService,\n    private alert: AlertService,\n  ) {}\n\n  ngOnInit(): void {\n    this.loadTaxRegimes();\n    this.form.valueChanges.subscribe(() => {\n      this.formChange.emit();\n    });\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    if (changes['initialData']?.currentValue) {\n      const data = changes['initialData'].currentValue;\n      this.form.patchValue({\n        regimeType: data.taxRegimeId,\n      });\n    }\n  }\n\n  loadTaxRegimes(): void {\n    this.taxRegimeService.getAll().subscribe({\n      next: (regimes) => (this.taxRegimes = regimes),\n      error: (error) => {\n        this.alert.error(error.error?.detail ?? 'Error al cargar los regímenes tributarios');\n      },\n    });\n  }\n\n  private isPdfFile(file: File): boolean {\n    return file.type === 'application/pdf';\n  }\n\n  onFileSelected(event: Event, controlName: string): void {\n    const file = (event.target as HTMLInputElement).files?.[0];\n    if (file) {\n      if (this.isPdfFile(file)) {\n        if (file.size <= 1024 * 1024) {\n          // 1MB\n          this.form.patchValue({ [controlName]: file });\n          this.taxFormFile = file;\n          this.taxFormFileName = file.name;\n        } else {\n          this.alert.error('El archivo no debe superar 1MB');\n        }\n      } else {\n        this.alert.error('Solo se permiten archivos PDF');\n      }\n      this.form.get(controlName)?.updateValueAndValidity();\n    }\n  }\n\n  downloadFile(): void {\n    if (this.initialData?.taxFormFileUrl) {\n      window.open(this.initialData.taxFormFileUrl, '_blank');\n    }\n  }\n\n  get isValid(): boolean {\n    const basicFieldsValid = Boolean(this.form.get('regimeType')?.valid);\n    const hasNewFile = Boolean(this.form.get('regimeSupportFile')?.value);\n    const hasExistingFile = Boolean(this.initialData?.taxFormFileUrl);\n\n    return basicFieldsValid && (hasNewFile || hasExistingFile);\n  }\n\n  getTaxRegimeName(id: number): string {\n    return this.taxRegimes.find((regime) => regime.id === id)?.name || '';\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAkDa;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;AAlDb,SACEE,SAAS,EACTC,YAAY,EACZC,KAAK,EAGLC,MAAM,QAED,eAAe;AACtB,SACEC,WAAW,EAEXC,mBAAmB,EACnBC,UAAU,QACL,gBAAgB;AACvB,SAASC,SAAS,EAAEC,aAAa,QAAQ,0BAA0B;AACnE,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,QAAQ,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,8BAA8B;AAC/E,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,UAAU,QAAQ,2BAA2B;AAGtD,SAASC,gBAAgB,QAAQ,mDAAmD;AACpF,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SACEC,iBAAiB,EACjBC,gBAAgB,QACX,oCAAoC;AAACtB,aAAA,GAAAuB,CAAA;AAqBrC,IAAMC,gBAAgB,GAAtB,MAAMA,gBAAgB;EAc3BC,YACUC,EAAe,EACfC,gBAAkC,EAClCC,KAAmB;IAAA5B,aAAA,GAAA6B,CAAA;IAAA7B,aAAA,GAAAuB,CAAA;IAFnB,KAAAG,EAAE,GAAFA,EAAE;IAAa1B,aAAA,GAAAuB,CAAA;IACf,KAAAI,gBAAgB,GAAhBA,gBAAgB;IAAkB3B,aAAA,GAAAuB,CAAA;IAClC,KAAAK,KAAK,GAALA,KAAK;IAAc5B,aAAA,GAAAuB,CAAA;IAfpB,KAAAO,YAAY,GAAG,KAAK;IAAA9B,aAAA,GAAAuB,CAAA;IACnB,KAAAQ,UAAU,GAAG,IAAI5B,YAAY,EAAQ;IAAAH,aAAA,GAAAuB,CAAA;IAE/C,KAAAS,IAAI,GAAc,IAAI,CAACN,EAAE,CAACO,KAAK,CAAC;MAC9BC,UAAU,EAAE,CAAC,EAAE,EAAE1B,UAAU,CAAC2B,QAAQ,CAAC;MACrCC,iBAAiB,EAAE,CAAC,IAAI,EAAE,CAACf,iBAAiB,EAAE,EAAEC,gBAAgB,EAAE,CAAC;KACpE,CAAC;IAACtB,aAAA,GAAAuB,CAAA;IAEH,KAAAc,WAAW,GAAgB,IAAI;IAACrC,aAAA,GAAAuB,CAAA;IAChC,KAAAe,eAAe,GAAkB,IAAI;IAACtC,aAAA,GAAAuB,CAAA;IACtC,KAAAgB,UAAU,GAAgB,EAAE;EAMzB;EAEHC,QAAQA,CAAA;IAAAxC,aAAA,GAAA6B,CAAA;IAAA7B,aAAA,GAAAuB,CAAA;IACN,IAAI,CAACkB,cAAc,EAAE;IAACzC,aAAA,GAAAuB,CAAA;IACtB,IAAI,CAACS,IAAI,CAACU,YAAY,CAACC,SAAS,CAAC,MAAK;MAAA3C,aAAA,GAAA6B,CAAA;MAAA7B,aAAA,GAAAuB,CAAA;MACpC,IAAI,CAACQ,UAAU,CAACa,IAAI,EAAE;IACxB,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAACC,OAAsB;IAAA9C,aAAA,GAAA6B,CAAA;IAAA7B,aAAA,GAAAuB,CAAA;IAChC,IAAIuB,OAAO,CAAC,aAAa,CAAC,EAAEC,YAAY,EAAE;MAAA/C,aAAA,GAAAgD,CAAA;MACxC,MAAMC,IAAI,IAAAjD,aAAA,GAAAuB,CAAA,QAAGuB,OAAO,CAAC,aAAa,CAAC,CAACC,YAAY;MAAC/C,aAAA,GAAAuB,CAAA;MACjD,IAAI,CAACS,IAAI,CAACkB,UAAU,CAAC;QACnBhB,UAAU,EAAEe,IAAI,CAACE;OAClB,CAAC;IACJ,CAAC;MAAAnD,aAAA,GAAAgD,CAAA;IAAA;EACH;EAEAP,cAAcA,CAAA;IAAAzC,aAAA,GAAA6B,CAAA;IAAA7B,aAAA,GAAAuB,CAAA;IACZ,IAAI,CAACI,gBAAgB,CAACyB,MAAM,EAAE,CAACT,SAAS,CAAC;MACvCU,IAAI,EAAGC,OAAO,IAAM;QAAAtD,aAAA,GAAA6B,CAAA;QAAA7B,aAAA,GAAAuB,CAAA;QAAA,WAAI,CAACgB,UAAU,GAAGe,OAAO;MAAP,CAAQ;MAC9CC,KAAK,EAAGA,KAAK,IAAI;QAAAvD,aAAA,GAAA6B,CAAA;QAAA7B,aAAA,GAAAuB,CAAA;QACf,IAAI,CAACK,KAAK,CAAC2B,KAAK,CAAC,CAAAvD,aAAA,GAAAgD,CAAA,UAAAO,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAAxD,aAAA,GAAAgD,CAAA,UAAI,2CAA2C,EAAC;MACtF;KACD,CAAC;EACJ;EAEQS,SAASA,CAACC,IAAU;IAAA1D,aAAA,GAAA6B,CAAA;IAAA7B,aAAA,GAAAuB,CAAA;IAC1B,OAAOmC,IAAI,CAACC,IAAI,KAAK,iBAAiB;EACxC;EAEAC,cAAcA,CAACC,KAAY,EAAEC,WAAmB;IAAA9D,aAAA,GAAA6B,CAAA;IAC9C,MAAM6B,IAAI,IAAA1D,aAAA,GAAAuB,CAAA,QAAIsC,KAAK,CAACE,MAA2B,CAACC,KAAK,GAAG,CAAC,CAAC;IAAChE,aAAA,GAAAuB,CAAA;IAC3D,IAAImC,IAAI,EAAE;MAAA1D,aAAA,GAAAgD,CAAA;MAAAhD,aAAA,GAAAuB,CAAA;MACR,IAAI,IAAI,CAACkC,SAAS,CAACC,IAAI,CAAC,EAAE;QAAA1D,aAAA,GAAAgD,CAAA;QAAAhD,aAAA,GAAAuB,CAAA;QACxB,IAAImC,IAAI,CAACO,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;UAAAjE,aAAA,GAAAgD,CAAA;UAAAhD,aAAA,GAAAuB,CAAA;UAC5B;UACA,IAAI,CAACS,IAAI,CAACkB,UAAU,CAAC;YAAE,CAACY,WAAW,GAAGJ;UAAI,CAAE,CAAC;UAAC1D,aAAA,GAAAuB,CAAA;UAC9C,IAAI,CAACc,WAAW,GAAGqB,IAAI;UAAC1D,aAAA,GAAAuB,CAAA;UACxB,IAAI,CAACe,eAAe,GAAGoB,IAAI,CAACQ,IAAI;QAClC,CAAC,MAAM;UAAAlE,aAAA,GAAAgD,CAAA;UAAAhD,aAAA,GAAAuB,CAAA;UACL,IAAI,CAACK,KAAK,CAAC2B,KAAK,CAAC,gCAAgC,CAAC;QACpD;MACF,CAAC,MAAM;QAAAvD,aAAA,GAAAgD,CAAA;QAAAhD,aAAA,GAAAuB,CAAA;QACL,IAAI,CAACK,KAAK,CAAC2B,KAAK,CAAC,+BAA+B,CAAC;MACnD;MAACvD,aAAA,GAAAuB,CAAA;MACD,IAAI,CAACS,IAAI,CAACmC,GAAG,CAACL,WAAW,CAAC,EAAEM,sBAAsB,EAAE;IACtD,CAAC;MAAApE,aAAA,GAAAgD,CAAA;IAAA;EACH;EAEAqB,YAAYA,CAAA;IAAArE,aAAA,GAAA6B,CAAA;IAAA7B,aAAA,GAAAuB,CAAA;IACV,IAAI,IAAI,CAAC+C,WAAW,EAAEC,cAAc,EAAE;MAAAvE,aAAA,GAAAgD,CAAA;MAAAhD,aAAA,GAAAuB,CAAA;MACpCiD,MAAM,CAACC,IAAI,CAAC,IAAI,CAACH,WAAW,CAACC,cAAc,EAAE,QAAQ,CAAC;IACxD,CAAC;MAAAvE,aAAA,GAAAgD,CAAA;IAAA;EACH;EAEA,IAAI0B,OAAOA,CAAA;IAAA1E,aAAA,GAAA6B,CAAA;IACT,MAAM8C,gBAAgB,IAAA3E,aAAA,GAAAuB,CAAA,QAAGqD,OAAO,CAAC,IAAI,CAAC5C,IAAI,CAACmC,GAAG,CAAC,YAAY,CAAC,EAAEU,KAAK,CAAC;IACpE,MAAMC,UAAU,IAAA9E,aAAA,GAAAuB,CAAA,QAAGqD,OAAO,CAAC,IAAI,CAAC5C,IAAI,CAACmC,GAAG,CAAC,mBAAmB,CAAC,EAAEY,KAAK,CAAC;IACrE,MAAMC,eAAe,IAAAhF,aAAA,GAAAuB,CAAA,QAAGqD,OAAO,CAAC,IAAI,CAACN,WAAW,EAAEC,cAAc,CAAC;IAACvE,aAAA,GAAAuB,CAAA;IAElE,OAAO,CAAAvB,aAAA,GAAAgD,CAAA,UAAA2B,gBAAgB,MAAK,CAAA3E,aAAA,GAAAgD,CAAA,UAAA8B,UAAU,MAAA9E,aAAA,GAAAgD,CAAA,UAAIgC,eAAe,EAAC;EAC5D;EAEAC,gBAAgBA,CAACC,EAAU;IAAAlF,aAAA,GAAA6B,CAAA;IAAA7B,aAAA,GAAAuB,CAAA;IACzB,OAAO,CAAAvB,aAAA,GAAAgD,CAAA,cAAI,CAACT,UAAU,CAAC4C,IAAI,CAAEC,MAAM,IAAK;MAAApF,aAAA,GAAA6B,CAAA;MAAA7B,aAAA,GAAAuB,CAAA;MAAA,OAAA6D,MAAM,CAACF,EAAE,KAAKA,EAAE;IAAF,CAAE,CAAC,EAAEhB,IAAI,MAAAlE,aAAA,GAAAgD,CAAA,UAAI,EAAE;EACvE;;;;;;;;;;;;;;;;;;;cAnFC5C;MAAK;;cACLA;MAAK;;cACLC;MAAM;;;;;AAHImB,gBAAgB,GAAA6D,UAAA,EAnB5BnF,SAAS,CAAC;EACToF,QAAQ,EAAE,cAAc;EACxBC,QAAA,EAAAC,oBAAwC;EAExCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPnF,mBAAmB,EACnBQ,OAAO,EACPF,YAAY,EACZC,QAAQ,EACRG,SAAS,EACTN,SAAS,EACTK,QAAQ,EACRJ,QAAQ,EACRH,SAAS,EACTC,aAAa,EACbQ,UAAU,CACX;;CACF,CAAC,C,EACWM,gBAAgB,CAqF5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}