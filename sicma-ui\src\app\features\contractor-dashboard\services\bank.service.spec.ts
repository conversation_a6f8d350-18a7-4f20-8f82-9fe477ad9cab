import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { Bank } from '@contractor-dashboard/models/bank.model';
import { environment } from '@env';
import { BankService } from './bank.service';

describe('BankService', () => {
  let service: BankService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/banks`;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [BankService],
    });
    service = TestBed.inject(BankService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  const mockBank: Bank = {
    id: 1,
    name: 'Bancolombia',
  };

  describe('getAll', () => {
    it('should return all banks', () => {
      const mockBanks = [mockBank];

      service.getAll().subscribe((banks) => {
        expect(banks).toEqual(mockBanks);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockBanks);
    });

    it('should handle error when getting all banks', () => {
      service.getAll().subscribe({
        error: (error) => {
          expect(error.status).toBe(500);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Server error'), { status: 500 });
    });
  });

  describe('getById', () => {
    it('should return a bank by id', () => {
      service.getById(1).subscribe((bank) => {
        expect(bank).toEqual(mockBank);
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('GET');
      req.flush(mockBank);
    });

    it('should handle error when getting bank by id', () => {
      service.getById(999).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/999`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('create', () => {
    const newBank: Omit<Bank, 'id'> = {
      name: 'New Bank',
    };

    it('should create a new bank', () => {
      service.create(newBank).subscribe((bank) => {
        expect(bank).toEqual(mockBank);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newBank);
      req.flush(mockBank);
    });

    it('should handle error when creating bank', () => {
      service.create(newBank).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('update', () => {
    const updateData: Partial<Bank> = {
      name: 'Updated Name',
    };

    it('should update a bank', () => {
      service.update(1, updateData).subscribe((bank) => {
        expect(bank).toEqual({ ...mockBank, ...updateData });
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush({ ...mockBank, ...updateData });
    });

    it('should handle error when updating bank', () => {
      service.update(1, updateData).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('delete', () => {
    it('should delete a bank', () => {
      service.delete(1).subscribe((response) => {
        expect(response).toBeNull();
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });

    it('should handle error when deleting bank', () => {
      service.delete(1).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('getByName', () => {
    it('should return a bank by name', () => {
      const name = 'Bancolombia';

      service.getByName(name).subscribe((bank) => {
        expect(bank).toEqual(mockBank);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockBank);
    });

    it('should handle error when getting bank by name', () => {
      const name = 'NonExistent';

      service.getByName(name).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });
});