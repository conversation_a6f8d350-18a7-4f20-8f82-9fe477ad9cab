{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/Github/SICMA/sicma-ui/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { Component } from '@angular/core';\nimport { TestBed, fakeAsync, tick } from '@angular/core/testing';\nimport { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { ContractService } from '@contract-management/services/contract.service';\nimport { ArlAffiliationClassService } from '@contractor-dashboard/services/arl-affiliation-class.service';\nimport { CompensationFundService } from '@contractor-dashboard/services/compensation-fund.service';\nimport { InitialReportDocumentationService } from '@contractor-dashboard/services/initial-report-documentation.service';\nimport { PeriodService } from '@contractor-dashboard/services/period.service';\nimport { SocialSecurityContributionService } from '@contractor-dashboard/services/social-security-contribution.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { of, throwError } from 'rxjs';\nimport { MonthlyReportSocialSecurityInformationComponent } from './monthly-report-social-security-information.component';\nlet MockMonthlyReportSocialSecurityInformationComponent = class MockMonthlyReportSocialSecurityInformationComponent extends MonthlyReportSocialSecurityInformationComponent {};\nMockMonthlyReportSocialSecurityInformationComponent = __decorate([Component({\n  selector: 'app-monthly-report-social-security-information',\n  template: `\n    <form [formGroup]=\"socialSecurityForm\">\n      <input formControlName=\"arlContribution\" />\n      <input formControlName=\"compensationFundContribution\" />\n    </form>\n  `\n})], MockMonthlyReportSocialSecurityInformationComponent);\ndescribe('MonthlyReportSocialSecurityInformationComponent', () => {\n  let component;\n  let fixture;\n  let contractService;\n  let initialReportDocumentationService;\n  let alertService;\n  let arlAffiliationClassService;\n  let compensationFundService;\n  let socialSecurityContributionService;\n  const mockContract = {\n    id: 1,\n    contractNumber: 1,\n    monthlyPayment: 1000000,\n    object: 'Test contract',\n    rup: true,\n    secopCode: 123,\n    addition: false,\n    cession: false,\n    settled: false,\n    causesSelectionId: 1,\n    managementSupportId: 1,\n    contractClassId: 1\n  };\n  const mockInitialReportDocumentation = {\n    id: 1,\n    contractorContractId: 1,\n    epsId: 1,\n    arlId: 1,\n    pensionFundId: 1,\n    bankId: 1,\n    bankAccountTypeId: 1,\n    taxRegimeId: 1,\n    accountNumber: '123',\n    hasDependents: false,\n    hasHousingInterest: false,\n    hasPrepaidMedicine: false,\n    hasAfcAccount: false,\n    hasVoluntarySavings: false\n  };\n  const mockArlAffiliationClasses = [{\n    id: 1,\n    name: 'Class 1',\n    percentage: 0.522\n  }, {\n    id: 2,\n    name: 'Class 2',\n    percentage: 1.044\n  }];\n  const mockCompensationFunds = [{\n    id: 1,\n    name: 'Fund 1'\n  }, {\n    id: 2,\n    name: 'Fund 2'\n  }];\n  const mockSocialSecurityContribution = {\n    id: 1,\n    monthlyReportId: 1,\n    paymentFormNumber: 12345,\n    certificateFileUrl: 'http://example.com/file.pdf',\n    arlAffiliationClassId: 1,\n    arlContribution: 5000,\n    compensationFundId: 1,\n    compensationFundContribution: 4000,\n    healthContribution: 50000,\n    pensionContribution: 64000,\n    ibc: 400000\n  };\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    const contractServiceSpy = jasmine.createSpyObj('ContractService', ['getById']);\n    const initialReportDocServiceSpy = jasmine.createSpyObj('InitialReportDocumentationService', ['getByContractorContractId']);\n    const alertServiceSpy = jasmine.createSpyObj('AlertService', ['error']);\n    const arlServiceSpy = jasmine.createSpyObj('ArlAffiliationClassService', ['getAll']);\n    const compensationFundServiceSpy = jasmine.createSpyObj('CompensationFundService', ['getAll']);\n    const socialSecurityServiceSpy = jasmine.createSpyObj('SocialSecurityContributionService', ['getByMonthlyReportId', 'updateWithFile']);\n    const periodServiceSpy = jasmine.createSpyObj('PeriodService', ['getCurrentPeriod']);\n    yield TestBed.configureTestingModule({\n      declarations: [MockMonthlyReportSocialSecurityInformationComponent],\n      imports: [HttpClientTestingModule, BrowserAnimationsModule, ReactiveFormsModule],\n      providers: [FormBuilder, {\n        provide: ContractService,\n        useValue: contractServiceSpy\n      }, {\n        provide: InitialReportDocumentationService,\n        useValue: initialReportDocServiceSpy\n      }, {\n        provide: AlertService,\n        useValue: alertServiceSpy\n      }, {\n        provide: ArlAffiliationClassService,\n        useValue: arlServiceSpy\n      }, {\n        provide: CompensationFundService,\n        useValue: compensationFundServiceSpy\n      }, {\n        provide: SocialSecurityContributionService,\n        useValue: socialSecurityServiceSpy\n      }, {\n        provide: PeriodService,\n        useValue: periodServiceSpy\n      }]\n    }).compileComponents();\n    contractService = TestBed.inject(ContractService);\n    initialReportDocumentationService = TestBed.inject(InitialReportDocumentationService);\n    alertService = TestBed.inject(AlertService);\n    arlAffiliationClassService = TestBed.inject(ArlAffiliationClassService);\n    compensationFundService = TestBed.inject(CompensationFundService);\n    socialSecurityContributionService = TestBed.inject(SocialSecurityContributionService);\n    fixture = TestBed.createComponent(MockMonthlyReportSocialSecurityInformationComponent);\n    component = fixture.componentInstance;\n    component.contractorContractId = 1;\n    component.report = {\n      id: 1,\n      contractorContractId: 1,\n      reportNumber: 1,\n      startDate: new Date(),\n      endDate: new Date(),\n      creationDate: new Date()\n    };\n    contractService.getById.and.returnValue(of(mockContract));\n    initialReportDocumentationService.getByContractorContractId.and.returnValue(of(mockInitialReportDocumentation));\n    arlAffiliationClassService.getAll.and.returnValue(of(mockArlAffiliationClasses));\n    compensationFundService.getAll.and.returnValue(of(mockCompensationFunds));\n    socialSecurityContributionService.getByMonthlyReportId.and.returnValue(of(mockSocialSecurityContribution));\n  }));\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should load initial data on init', fakeAsync(() => {\n    fixture.detectChanges();\n    tick();\n    expect(contractService.getById).toHaveBeenCalledWith(1);\n    expect(initialReportDocumentationService.getByContractorContractId).toHaveBeenCalledWith(1);\n    expect(arlAffiliationClassService.getAll).toHaveBeenCalled();\n    expect(compensationFundService.getAll).toHaveBeenCalled();\n    expect(socialSecurityContributionService.getByMonthlyReportId).toHaveBeenCalledWith(1);\n    expect(component.contract).toEqual(mockContract);\n    expect(component.initialReportDocumentation).toEqual(mockInitialReportDocumentation);\n    expect(component.arlAffiliationClasses).toEqual(mockArlAffiliationClasses);\n    expect(component.compensationFunds).toEqual(mockCompensationFunds);\n  }));\n  it('should handle errors when loading data', fakeAsync(() => {\n    contractService.getById.and.returnValue(throwError(() => new Error()));\n    initialReportDocumentationService.getByContractorContractId.and.returnValue(throwError(() => new Error()));\n    arlAffiliationClassService.getAll.and.returnValue(throwError(() => new Error()));\n    compensationFundService.getAll.and.returnValue(throwError(() => new Error()));\n    fixture.detectChanges();\n    tick();\n    expect(alertService.error).toHaveBeenCalledWith('Error al cargar la información del contrato');\n    expect(alertService.error).toHaveBeenCalledWith('Error al cargar la documentación inicial');\n    expect(alertService.error).toHaveBeenCalledWith('Error al cargar las clases de afiliación ARL');\n    expect(alertService.error).toHaveBeenCalledWith('Error al cargar las cajas de compensación');\n  }));\n  it('should calculate contributions correctly', fakeAsync(() => {\n    component.previousPaymentValue = 1000000;\n    component.calculateContributions();\n    expect(component.ibc).toBe(400000);\n    expect(component.healthContribution).toBe(50000);\n    expect(component.pensionContribution).toBe(64000);\n  }));\n  it('should handle file selection', () => {\n    const mockFile = new File([''], 'test.pdf', {\n      type: 'application/pdf'\n    });\n    const event = {\n      target: {\n        files: [mockFile]\n      }\n    };\n    component.onFileSelected(event);\n    expect(component.selectedFile).toBe(mockFile);\n    expect(component.fileName).toBe('test.pdf');\n  });\n  it('should update compensation fund validation when checkbox changes', () => {\n    component.isSupervisor = false;\n    fixture.detectChanges();\n    const hasCompensationFundControl = component.socialSecurityForm.get('hasCompensationFund');\n    const compensationFundControl = component.socialSecurityForm.get('compensationFund');\n    const compensationFundContributionControl = component.socialSecurityForm.get('compensationFundContribution');\n    hasCompensationFundControl?.setValue(true);\n    expect(compensationFundControl?.hasValidator(Validators.required)).toBeTrue();\n    expect(compensationFundContributionControl?.hasValidator(Validators.required)).toBeTrue();\n    hasCompensationFundControl?.setValue(false);\n    expect(compensationFundControl?.hasValidator(Validators.required)).toBeFalse();\n    expect(compensationFundContributionControl?.hasValidator(Validators.required)).toBeFalse();\n  });\n  it('should emit form validity changes', () => {\n    spyOn(component.formValidityChange, 'emit');\n    fixture.detectChanges();\n    component.socialSecurityForm.patchValue({\n      paymentFormNumber: '12345',\n      arlAffiliationClass: 1,\n      arlContribution: 5000\n    });\n    expect(component.formValidityChange.emit).toHaveBeenCalled();\n  });\n  it('should download certificate', () => {\n    const mockUrl = 'http://example.com/file.pdf';\n    spyOn(window, 'open');\n    component.socialSecurityContribution = {\n      ...mockSocialSecurityContribution,\n      certificateFileUrl: mockUrl\n    };\n    component.downloadCertificate();\n    expect(window.open).toHaveBeenCalledWith(mockUrl, '_blank');\n  });\n  it('should initialize form differently for supervisor', () => {\n    component.isSupervisor = true;\n    fixture = TestBed.createComponent(MockMonthlyReportSocialSecurityInformationComponent);\n    component = fixture.componentInstance;\n    component.isSupervisor = true;\n    component.contractorContractId = 1;\n    component.report = {\n      id: 1,\n      contractorContractId: 1,\n      reportNumber: 1,\n      startDate: new Date(),\n      endDate: new Date(),\n      creationDate: new Date()\n    };\n    fixture.detectChanges();\n    expect(component.socialSecurityForm.get('hasCompensationFund')?.disabled).toBeTrue();\n  });\n  it('should apply validators differently based on existing certificate file', () => {\n    component.isSupervisor = false;\n    component.socialSecurityContribution = {\n      ...mockSocialSecurityContribution,\n      certificateFileUrl: undefined\n    };\n    component.ngOnChanges({\n      socialSecurityContribution: {\n        currentValue: component.socialSecurityContribution,\n        firstChange: true,\n        previousValue: null,\n        isFirstChange: () => true\n      }\n    });\n    const certificateFileControl = component.socialSecurityForm.get('certificateFile');\n    expect(certificateFileControl?.hasValidator(Validators.required)).toBeTrue();\n  });\n  it('should not require certificate file when supervisor', () => {\n    component.isSupervisor = true;\n    component.ngOnChanges({\n      socialSecurityContribution: {\n        currentValue: component.socialSecurityContribution,\n        firstChange: true,\n        previousValue: null,\n        isFirstChange: () => true\n      }\n    });\n    const certificateFileControl = component.socialSecurityForm.get('certificateFile');\n    expect(certificateFileControl?.hasValidator(Validators.required)).toBeFalse();\n  });\n  it('should not require certificate file when file already exists', () => {\n    component.isSupervisor = false;\n    component.socialSecurityContribution = {\n      ...mockSocialSecurityContribution,\n      certificateFileUrl: 'http://example.com/file.pdf'\n    };\n    component.ngOnChanges({\n      socialSecurityContribution: {\n        currentValue: component.socialSecurityContribution,\n        firstChange: true,\n        previousValue: null,\n        isFirstChange: () => true\n      }\n    });\n    const certificateFileControl = component.socialSecurityForm.get('certificateFile');\n    expect(certificateFileControl?.hasValidator(Validators.required)).toBeFalse();\n  });\n  it('should handle 404 error gracefully when loading initial report documentation', fakeAsync(() => {\n    const error404 = {\n      status: 404\n    };\n    initialReportDocumentationService.getByContractorContractId.and.returnValue(throwError(() => error404));\n    component.loadInitialReportDocumentation();\n    tick();\n    expect(alertService.error).not.toHaveBeenCalledWith('Error al cargar la documentación inicial');\n  }));\n  it('should handle non-404 errors when loading initial report documentation', fakeAsync(() => {\n    const error500 = {\n      status: 500\n    };\n    initialReportDocumentationService.getByContractorContractId.and.returnValue(throwError(() => error500));\n    component.loadInitialReportDocumentation();\n    tick();\n    expect(alertService.error).toHaveBeenCalledWith('Error al cargar la documentación inicial');\n  }));\n  it('should emit true for form validity when isSupervisor is true, regardless of form validity', () => {\n    component.isSupervisor = true;\n    spyOn(component.formValidityChange, 'emit');\n    fixture.detectChanges();\n    component.socialSecurityForm.get('arlContribution')?.setValue(null);\n    expect(component.formValidityChange.emit).toHaveBeenCalledWith(true);\n  });\n  it('should correctly prepare social security data with compensation fund', () => {\n    component.isSupervisor = false;\n    component.selectedFile = new File([''], 'test.pdf', {\n      type: 'application/pdf'\n    });\n    component.socialSecurityForm.patchValue({\n      paymentFormNumber: 123456,\n      arlAffiliationClass: 1,\n      arlContribution: 10000,\n      hasCompensationFund: true,\n      compensationFund: 1,\n      compensationFundContribution: 5000\n    });\n    const result = component.getSocialSecurityData();\n    expect(result.paymentFormNumber).toBe(123456);\n    expect(result.arlAffiliationClassId).toBe(1);\n    expect(result.arlContribution).toBe(10000);\n    expect(result.compensationFundId).toBe(1);\n    expect(result.compensationFundContribution).toBe(5000);\n  });\n  it('should correctly prepare social security data without compensation fund', () => {\n    component.isSupervisor = false;\n    component.selectedFile = new File([''], 'test.pdf', {\n      type: 'application/pdf'\n    });\n    component.socialSecurityForm.patchValue({\n      paymentFormNumber: 123456,\n      arlAffiliationClass: 1,\n      arlContribution: 10000,\n      hasCompensationFund: false\n    });\n    const result = component.getSocialSecurityData();\n    expect(result.paymentFormNumber).toBe(123456);\n    expect(result.arlAffiliationClassId).toBe(1);\n    expect(result.arlContribution).toBe(10000);\n    expect(result.compensationFundId).toBeNull();\n    expect(result.compensationFundContribution).toBeNull();\n  });\n  it('should emit social security data when saving', () => {\n    component.isSupervisor = false;\n    component.socialSecurityContribution = mockSocialSecurityContribution;\n    component.socialSecurityForm.patchValue({\n      paymentFormNumber: 123456,\n      arlAffiliationClass: 1,\n      arlContribution: 10000,\n      hasCompensationFund: true,\n      compensationFund: 1,\n      compensationFundContribution: 5000\n    });\n    spyOn(component.saveSocialSecurity, 'emit');\n    component.onSave();\n    expect(component.saveSocialSecurity.emit).toHaveBeenCalled();\n  });\n  it('should handle error when loading ARL affiliation classes', fakeAsync(() => {\n    arlAffiliationClassService.getAll.and.returnValue(throwError(() => new Error()));\n    component.loadARLAffiliationClasses();\n    tick();\n    expect(alertService.error).toHaveBeenCalledWith('Error al cargar las clases de afiliación ARL');\n  }));\n  it('should not update compensation fund validation when isSupervisor is true', () => {\n    component.isSupervisor = true;\n    fixture.detectChanges();\n    const compensationFundControl = component.socialSecurityForm.get('compensationFund');\n    const compensationFundContributionControl = component.socialSecurityForm.get('compensationFundContribution');\n    spyOn(compensationFundControl, 'setValidators');\n    spyOn(compensationFundContributionControl, 'setValidators');\n    component.socialSecurityForm.get('hasCompensationFund')?.setValue(true);\n    expect(compensationFundControl.setValidators).not.toHaveBeenCalled();\n    expect(compensationFundContributionControl.setValidators).not.toHaveBeenCalled();\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "Component", "TestBed", "fakeAsync", "tick", "FormBuilder", "ReactiveFormsModule", "Validators", "BrowserAnimationsModule", "ContractService", "ArlAffiliationClassService", "CompensationFundService", "InitialReportDocumentationService", "PeriodService", "SocialSecurityContributionService", "AlertService", "of", "throwError", "MonthlyReportSocialSecurityInformationComponent", "MockMonthlyReportSocialSecurityInformationComponent", "__decorate", "selector", "template", "describe", "component", "fixture", "contractService", "initialReportDocumentationService", "alertService", "arlAffiliationClassService", "compensationFundService", "socialSecurityContributionService", "mockContract", "id", "contractNumber", "monthlyPayment", "object", "rup", "secopCode", "addition", "cession", "settled", "causesSelectionId", "managementSupportId", "contractClassId", "mockInitialReportDocumentation", "contractorContractId", "epsId", "arlId", "pensionFundId", "bankId", "bankAccountTypeId", "taxRegimeId", "accountNumber", "hasDependents", "hasHousingInterest", "hasPrepaidMedicine", "hasAfcAccount", "hasVoluntarySavings", "mockArlAffiliationClasses", "name", "percentage", "mockCompensationFunds", "mockSocialSecurityContribution", "monthlyReportId", "paymentFormNumber", "certificateFileUrl", "arlAffiliationClassId", "arlContribution", "compensationFundId", "compensationFundContribution", "healthContribution", "pensionContribution", "ibc", "beforeEach", "_asyncToGenerator", "contractServiceSpy", "jasmine", "createSpyObj", "initialReportDocServiceSpy", "alertServiceSpy", "arlServiceSpy", "compensationFundServiceSpy", "socialSecurityServiceSpy", "periodServiceSpy", "configureTestingModule", "declarations", "imports", "providers", "provide", "useValue", "compileComponents", "inject", "createComponent", "componentInstance", "report", "reportNumber", "startDate", "Date", "endDate", "creationDate", "getById", "and", "returnValue", "getByContractorContractId", "getAll", "getByMonthlyReportId", "it", "expect", "toBeTruthy", "detectChanges", "toHaveBeenCalledWith", "toHaveBeenCalled", "contract", "toEqual", "initialReportDocumentation", "arlAffiliationClasses", "compensationFunds", "Error", "error", "previousPaymentValue", "calculateContributions", "toBe", "mockFile", "File", "type", "event", "target", "files", "onFileSelected", "selectedFile", "fileName", "isSupervisor", "hasCompensationFundControl", "socialSecurityForm", "get", "compensationFundControl", "compensationFundContributionControl", "setValue", "hasValidator", "required", "toBeTrue", "toBeFalse", "spyOn", "formValidityChange", "patchValue", "arlAffiliationClass", "emit", "mockUrl", "window", "socialSecurityContribution", "downloadCertificate", "open", "disabled", "undefined", "ngOnChanges", "currentValue", "firstChange", "previousValue", "isFirstChange", "certificateFileControl", "error404", "status", "loadInitialReportDocumentation", "not", "error500", "hasCompensationFund", "compensationFund", "result", "getSocialSecurityData", "toBeNull", "saveSocialSecurity", "onSave", "loadARLAffiliationClasses", "setValidators"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\components\\monthly-reports-tab\\monthly-report-dialog\\monthly-report-social-security-information\\monthly-report-social-security-information.component.spec.ts"], "sourcesContent": ["import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { Component } from '@angular/core';\nimport {\n  ComponentFixture,\n  TestBed,\n  fakeAsync,\n  tick,\n} from '@angular/core/testing';\nimport { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { Contract } from '@contract-management/models/contract.model';\nimport { ContractService } from '@contract-management/services/contract.service';\nimport { InitialReportDocumentation } from '@contractor-dashboard/models/initial-report-documentation.model';\nimport { SocialSecurityContribution } from '@contractor-dashboard/models/social-security-contribution.model';\nimport { ArlAffiliationClassService } from '@contractor-dashboard/services/arl-affiliation-class.service';\nimport { CompensationFundService } from '@contractor-dashboard/services/compensation-fund.service';\nimport { InitialReportDocumentationService } from '@contractor-dashboard/services/initial-report-documentation.service';\nimport { PeriodService } from '@contractor-dashboard/services/period.service';\nimport { SocialSecurityContributionService } from '@contractor-dashboard/services/social-security-contribution.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { of, throwError } from 'rxjs';\nimport { MonthlyReportSocialSecurityInformationComponent } from './monthly-report-social-security-information.component';\n\n@Component({\n  selector: 'app-monthly-report-social-security-information',\n  template: `\n    <form [formGroup]=\"socialSecurityForm\">\n      <input formControlName=\"arlContribution\" />\n      <input formControlName=\"compensationFundContribution\" />\n    </form>\n  `,\n})\nclass MockMonthlyReportSocialSecurityInformationComponent extends MonthlyReportSocialSecurityInformationComponent {}\n\ndescribe('MonthlyReportSocialSecurityInformationComponent', () => {\n  let component: MockMonthlyReportSocialSecurityInformationComponent;\n  let fixture: ComponentFixture<MockMonthlyReportSocialSecurityInformationComponent>;\n  let contractService: jasmine.SpyObj<ContractService>;\n  let initialReportDocumentationService: jasmine.SpyObj<InitialReportDocumentationService>;\n  let alertService: jasmine.SpyObj<AlertService>;\n  let arlAffiliationClassService: jasmine.SpyObj<ArlAffiliationClassService>;\n  let compensationFundService: jasmine.SpyObj<CompensationFundService>;\n  let socialSecurityContributionService: jasmine.SpyObj<SocialSecurityContributionService>;\n\n  const mockContract: Contract = {\n    id: 1,\n    contractNumber: 1,\n    monthlyPayment: 1000000,\n    object: 'Test contract',\n    rup: true,\n    secopCode: 123,\n    addition: false,\n    cession: false,\n    settled: false,\n    causesSelectionId: 1,\n    managementSupportId: 1,\n    contractClassId: 1,\n  };\n\n  const mockInitialReportDocumentation: InitialReportDocumentation = {\n    id: 1,\n    contractorContractId: 1,\n    epsId: 1,\n    arlId: 1,\n    pensionFundId: 1,\n    bankId: 1,\n    bankAccountTypeId: 1,\n    taxRegimeId: 1,\n    accountNumber: '123',\n    hasDependents: false,\n    hasHousingInterest: false,\n    hasPrepaidMedicine: false,\n    hasAfcAccount: false,\n    hasVoluntarySavings: false,\n  };\n\n  const mockArlAffiliationClasses = [\n    { id: 1, name: 'Class 1', percentage: 0.522 },\n    { id: 2, name: 'Class 2', percentage: 1.044 },\n  ];\n\n  const mockCompensationFunds = [\n    { id: 1, name: 'Fund 1' },\n    { id: 2, name: 'Fund 2' },\n  ];\n\n  const mockSocialSecurityContribution: SocialSecurityContribution = {\n    id: 1,\n    monthlyReportId: 1,\n    paymentFormNumber: 12345,\n    certificateFileUrl: 'http://example.com/file.pdf',\n    arlAffiliationClassId: 1,\n    arlContribution: 5000,\n    compensationFundId: 1,\n    compensationFundContribution: 4000,\n    healthContribution: 50000,\n    pensionContribution: 64000,\n    ibc: 400000,\n  };\n\n  beforeEach(async () => {\n    const contractServiceSpy = jasmine.createSpyObj('ContractService', [\n      'getById',\n    ]);\n    const initialReportDocServiceSpy = jasmine.createSpyObj(\n      'InitialReportDocumentationService',\n      ['getByContractorContractId'],\n    );\n    const alertServiceSpy = jasmine.createSpyObj('AlertService', ['error']);\n    const arlServiceSpy = jasmine.createSpyObj('ArlAffiliationClassService', [\n      'getAll',\n    ]);\n    const compensationFundServiceSpy = jasmine.createSpyObj(\n      'CompensationFundService',\n      ['getAll'],\n    );\n    const socialSecurityServiceSpy = jasmine.createSpyObj(\n      'SocialSecurityContributionService',\n      ['getByMonthlyReportId', 'updateWithFile'],\n    );\n    const periodServiceSpy = jasmine.createSpyObj('PeriodService', [\n      'getCurrentPeriod',\n    ]);\n\n    await TestBed.configureTestingModule({\n      declarations: [MockMonthlyReportSocialSecurityInformationComponent],\n      imports: [\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n        ReactiveFormsModule,\n      ],\n      providers: [\n        FormBuilder,\n        { provide: ContractService, useValue: contractServiceSpy },\n        {\n          provide: InitialReportDocumentationService,\n          useValue: initialReportDocServiceSpy,\n        },\n        { provide: AlertService, useValue: alertServiceSpy },\n        { provide: ArlAffiliationClassService, useValue: arlServiceSpy },\n        {\n          provide: CompensationFundService,\n          useValue: compensationFundServiceSpy,\n        },\n        {\n          provide: SocialSecurityContributionService,\n          useValue: socialSecurityServiceSpy,\n        },\n        { provide: PeriodService, useValue: periodServiceSpy },\n      ],\n    }).compileComponents();\n\n    contractService = TestBed.inject(\n      ContractService,\n    ) as jasmine.SpyObj<ContractService>;\n    initialReportDocumentationService = TestBed.inject(\n      InitialReportDocumentationService,\n    ) as jasmine.SpyObj<InitialReportDocumentationService>;\n    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;\n    arlAffiliationClassService = TestBed.inject(\n      ArlAffiliationClassService,\n    ) as jasmine.SpyObj<ArlAffiliationClassService>;\n    compensationFundService = TestBed.inject(\n      CompensationFundService,\n    ) as jasmine.SpyObj<CompensationFundService>;\n    socialSecurityContributionService = TestBed.inject(\n      SocialSecurityContributionService,\n    ) as jasmine.SpyObj<SocialSecurityContributionService>;\n\n    fixture = TestBed.createComponent(\n      MockMonthlyReportSocialSecurityInformationComponent,\n    );\n    component = fixture.componentInstance;\n    component.contractorContractId = 1;\n    component.report = {\n      id: 1,\n      contractorContractId: 1,\n      reportNumber: 1,\n      startDate: new Date(),\n      endDate: new Date(),\n      creationDate: new Date(),\n    };\n\n    contractService.getById.and.returnValue(of(mockContract));\n    initialReportDocumentationService.getByContractorContractId.and.returnValue(\n      of(mockInitialReportDocumentation),\n    );\n    arlAffiliationClassService.getAll.and.returnValue(\n      of(mockArlAffiliationClasses),\n    );\n    compensationFundService.getAll.and.returnValue(of(mockCompensationFunds));\n    socialSecurityContributionService.getByMonthlyReportId.and.returnValue(\n      of(mockSocialSecurityContribution),\n    );\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should load initial data on init', fakeAsync(() => {\n    fixture.detectChanges();\n    tick();\n\n    expect(contractService.getById).toHaveBeenCalledWith(1);\n    expect(\n      initialReportDocumentationService.getByContractorContractId,\n    ).toHaveBeenCalledWith(1);\n    expect(arlAffiliationClassService.getAll).toHaveBeenCalled();\n    expect(compensationFundService.getAll).toHaveBeenCalled();\n    expect(\n      socialSecurityContributionService.getByMonthlyReportId,\n    ).toHaveBeenCalledWith(1);\n\n    expect(component.contract).toEqual(mockContract);\n    expect(component.initialReportDocumentation).toEqual(\n      mockInitialReportDocumentation,\n    );\n    expect(component.arlAffiliationClasses).toEqual(mockArlAffiliationClasses);\n    expect(component.compensationFunds).toEqual(mockCompensationFunds);\n  }));\n\n  it('should handle errors when loading data', fakeAsync(() => {\n    contractService.getById.and.returnValue(throwError(() => new Error()));\n    initialReportDocumentationService.getByContractorContractId.and.returnValue(\n      throwError(() => new Error()),\n    );\n    arlAffiliationClassService.getAll.and.returnValue(\n      throwError(() => new Error()),\n    );\n    compensationFundService.getAll.and.returnValue(\n      throwError(() => new Error()),\n    );\n\n    fixture.detectChanges();\n    tick();\n\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al cargar la información del contrato',\n    );\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al cargar la documentación inicial',\n    );\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al cargar las clases de afiliación ARL',\n    );\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al cargar las cajas de compensación',\n    );\n  }));\n\n  it('should calculate contributions correctly', fakeAsync(() => {\n    component.previousPaymentValue = 1000000;\n    component.calculateContributions();\n\n    expect(component.ibc).toBe(400000);\n    expect(component.healthContribution).toBe(50000);\n    expect(component.pensionContribution).toBe(64000);\n  }));\n\n  it('should handle file selection', () => {\n    const mockFile = new File([''], 'test.pdf', { type: 'application/pdf' });\n    const event = { target: { files: [mockFile] } } as unknown as Event;\n\n    component.onFileSelected(event);\n\n    expect(component.selectedFile).toBe(mockFile);\n    expect(component.fileName).toBe('test.pdf');\n  });\n\n  it('should update compensation fund validation when checkbox changes', () => {\n    component.isSupervisor = false;\n    fixture.detectChanges();\n\n    const hasCompensationFundControl = component.socialSecurityForm.get(\n      'hasCompensationFund',\n    );\n    const compensationFundControl =\n      component.socialSecurityForm.get('compensationFund');\n    const compensationFundContributionControl =\n      component.socialSecurityForm.get('compensationFundContribution');\n\n    hasCompensationFundControl?.setValue(true);\n    expect(\n      compensationFundControl?.hasValidator(Validators.required),\n    ).toBeTrue();\n    expect(\n      compensationFundContributionControl?.hasValidator(Validators.required),\n    ).toBeTrue();\n\n    hasCompensationFundControl?.setValue(false);\n    expect(\n      compensationFundControl?.hasValidator(Validators.required),\n    ).toBeFalse();\n    expect(\n      compensationFundContributionControl?.hasValidator(Validators.required),\n    ).toBeFalse();\n  });\n\n  it('should emit form validity changes', () => {\n    spyOn(component.formValidityChange, 'emit');\n    fixture.detectChanges();\n\n    component.socialSecurityForm.patchValue({\n      paymentFormNumber: '12345',\n      arlAffiliationClass: 1,\n      arlContribution: 5000,\n    });\n\n    expect(component.formValidityChange.emit).toHaveBeenCalled();\n  });\n\n  it('should download certificate', () => {\n    const mockUrl = 'http://example.com/file.pdf';\n    spyOn(window, 'open');\n\n    component.socialSecurityContribution = {\n      ...mockSocialSecurityContribution,\n      certificateFileUrl: mockUrl,\n    };\n\n    component.downloadCertificate();\n\n    expect(window.open).toHaveBeenCalledWith(mockUrl, '_blank');\n  });\n\n  it('should initialize form differently for supervisor', () => {\n    component.isSupervisor = true;\n\n    fixture = TestBed.createComponent(\n      MockMonthlyReportSocialSecurityInformationComponent,\n    );\n    component = fixture.componentInstance;\n    component.isSupervisor = true;\n    component.contractorContractId = 1;\n    component.report = {\n      id: 1,\n      contractorContractId: 1,\n      reportNumber: 1,\n      startDate: new Date(),\n      endDate: new Date(),\n      creationDate: new Date(),\n    };\n\n    fixture.detectChanges();\n\n    expect(\n      component.socialSecurityForm.get('hasCompensationFund')?.disabled,\n    ).toBeTrue();\n  });\n\n  it('should apply validators differently based on existing certificate file', () => {\n    component.isSupervisor = false;\n    component.socialSecurityContribution = {\n      ...mockSocialSecurityContribution,\n      certificateFileUrl: undefined,\n    };\n    component.ngOnChanges({\n      socialSecurityContribution: {\n        currentValue: component.socialSecurityContribution,\n        firstChange: true,\n        previousValue: null,\n        isFirstChange: () => true,\n      },\n    });\n\n    const certificateFileControl =\n      component.socialSecurityForm.get('certificateFile');\n    expect(\n      certificateFileControl?.hasValidator(Validators.required),\n    ).toBeTrue();\n  });\n\n  it('should not require certificate file when supervisor', () => {\n    component.isSupervisor = true;\n    component.ngOnChanges({\n      socialSecurityContribution: {\n        currentValue: component.socialSecurityContribution,\n        firstChange: true,\n        previousValue: null,\n        isFirstChange: () => true,\n      },\n    });\n\n    const certificateFileControl =\n      component.socialSecurityForm.get('certificateFile');\n    expect(\n      certificateFileControl?.hasValidator(Validators.required),\n    ).toBeFalse();\n  });\n\n  it('should not require certificate file when file already exists', () => {\n    component.isSupervisor = false;\n    component.socialSecurityContribution = {\n      ...mockSocialSecurityContribution,\n      certificateFileUrl: 'http://example.com/file.pdf',\n    };\n    component.ngOnChanges({\n      socialSecurityContribution: {\n        currentValue: component.socialSecurityContribution,\n        firstChange: true,\n        previousValue: null,\n        isFirstChange: () => true,\n      },\n    });\n\n    const certificateFileControl =\n      component.socialSecurityForm.get('certificateFile');\n    expect(\n      certificateFileControl?.hasValidator(Validators.required),\n    ).toBeFalse();\n  });\n\n  it('should handle 404 error gracefully when loading initial report documentation', fakeAsync(() => {\n    const error404 = { status: 404 };\n    initialReportDocumentationService.getByContractorContractId.and.returnValue(\n      throwError(() => error404),\n    );\n\n    component.loadInitialReportDocumentation();\n    tick();\n\n    expect(alertService.error).not.toHaveBeenCalledWith(\n      'Error al cargar la documentación inicial',\n    );\n  }));\n\n  it('should handle non-404 errors when loading initial report documentation', fakeAsync(() => {\n    const error500 = { status: 500 };\n    initialReportDocumentationService.getByContractorContractId.and.returnValue(\n      throwError(() => error500),\n    );\n\n    component.loadInitialReportDocumentation();\n    tick();\n\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al cargar la documentación inicial',\n    );\n  }));\n\n  it('should emit true for form validity when isSupervisor is true, regardless of form validity', () => {\n    component.isSupervisor = true;\n    spyOn(component.formValidityChange, 'emit');\n    fixture.detectChanges();\n\n    component.socialSecurityForm.get('arlContribution')?.setValue(null);\n\n    expect(component.formValidityChange.emit).toHaveBeenCalledWith(true);\n  });\n\n  it('should correctly prepare social security data with compensation fund', () => {\n    component.isSupervisor = false;\n    component.selectedFile = new File([''], 'test.pdf', {\n      type: 'application/pdf',\n    });\n    component.socialSecurityForm.patchValue({\n      paymentFormNumber: 123456,\n      arlAffiliationClass: 1,\n      arlContribution: 10000,\n      hasCompensationFund: true,\n      compensationFund: 1,\n      compensationFundContribution: 5000,\n    });\n\n    const result = component.getSocialSecurityData();\n\n    expect(result.paymentFormNumber).toBe(123456);\n    expect(result.arlAffiliationClassId).toBe(1);\n    expect(result.arlContribution).toBe(10000);\n    expect(result.compensationFundId).toBe(1);\n    expect(result.compensationFundContribution).toBe(5000);\n  });\n\n  it('should correctly prepare social security data without compensation fund', () => {\n    component.isSupervisor = false;\n    component.selectedFile = new File([''], 'test.pdf', {\n      type: 'application/pdf',\n    });\n    component.socialSecurityForm.patchValue({\n      paymentFormNumber: 123456,\n      arlAffiliationClass: 1,\n      arlContribution: 10000,\n      hasCompensationFund: false,\n    });\n\n    const result = component.getSocialSecurityData();\n\n    expect(result.paymentFormNumber).toBe(123456);\n    expect(result.arlAffiliationClassId).toBe(1);\n    expect(result.arlContribution).toBe(10000);\n    expect(result.compensationFundId).toBeNull();\n    expect(result.compensationFundContribution).toBeNull();\n  });\n\n  it('should emit social security data when saving', () => {\n    component.isSupervisor = false;\n    component.socialSecurityContribution = mockSocialSecurityContribution;\n    component.socialSecurityForm.patchValue({\n      paymentFormNumber: 123456,\n      arlAffiliationClass: 1,\n      arlContribution: 10000,\n      hasCompensationFund: true,\n      compensationFund: 1,\n      compensationFundContribution: 5000,\n    });\n    spyOn(component.saveSocialSecurity, 'emit');\n\n    component.onSave();\n\n    expect(component.saveSocialSecurity.emit).toHaveBeenCalled();\n  });\n\n  it('should handle error when loading ARL affiliation classes', fakeAsync(() => {\n    arlAffiliationClassService.getAll.and.returnValue(\n      throwError(() => new Error()),\n    );\n\n    component.loadARLAffiliationClasses();\n    tick();\n\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al cargar las clases de afiliación ARL',\n    );\n  }));\n\n  it('should not update compensation fund validation when isSupervisor is true', () => {\n    component.isSupervisor = true;\n    fixture.detectChanges();\n\n    const compensationFundControl =\n      component.socialSecurityForm.get('compensationFund');\n    const compensationFundContributionControl =\n      component.socialSecurityForm.get('compensationFundContribution');\n\n    spyOn(compensationFundControl!, 'setValidators');\n    spyOn(compensationFundContributionControl!, 'setValidators');\n\n    component.socialSecurityForm.get('hasCompensationFund')?.setValue(true);\n\n    expect(compensationFundControl!.setValidators).not.toHaveBeenCalled();\n    expect(\n      compensationFundContributionControl!.setValidators,\n    ).not.toHaveBeenCalled();\n  });\n});\n"], "mappings": ";;AAAA,SAASA,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,SAAS,QAAQ,eAAe;AACzC,SAEEC,OAAO,EACPC,SAAS,EACTC,IAAI,QACC,uBAAuB;AAC9B,SAASC,WAAW,EAAEC,mBAAmB,EAAEC,UAAU,QAAQ,gBAAgB;AAC7E,SAASC,uBAAuB,QAAQ,sCAAsC;AAE9E,SAASC,eAAe,QAAQ,gDAAgD;AAGhF,SAASC,0BAA0B,QAAQ,8DAA8D;AACzG,SAASC,uBAAuB,QAAQ,0DAA0D;AAClG,SAASC,iCAAiC,QAAQ,qEAAqE;AACvH,SAASC,aAAa,QAAQ,+CAA+C;AAC7E,SAASC,iCAAiC,QAAQ,qEAAqE;AACvH,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,EAAE,EAAEC,UAAU,QAAQ,MAAM;AACrC,SAASC,+CAA+C,QAAQ,wDAAwD;AAWxH,IAAMC,mDAAmD,GAAzD,MAAMA,mDAAoD,SAAQD,+CAA+C,GAAG;AAA9GC,mDAAmD,GAAAC,UAAA,EATxDnB,SAAS,CAAC;EACToB,QAAQ,EAAE,gDAAgD;EAC1DC,QAAQ,EAAE;;;;;;CAMX,CAAC,C,EACIH,mDAAmD,CAA2D;AAEpHI,QAAQ,CAAC,iDAAiD,EAAE,MAAK;EAC/D,IAAIC,SAA8D;EAClE,IAAIC,OAA8E;EAClF,IAAIC,eAAgD;EACpD,IAAIC,iCAAoF;EACxF,IAAIC,YAA0C;EAC9C,IAAIC,0BAAsE;EAC1E,IAAIC,uBAAgE;EACpE,IAAIC,iCAAoF;EAExF,MAAMC,YAAY,GAAa;IAC7BC,EAAE,EAAE,CAAC;IACLC,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE,OAAO;IACvBC,MAAM,EAAE,eAAe;IACvBC,GAAG,EAAE,IAAI;IACTC,SAAS,EAAE,GAAG;IACdC,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,KAAK;IACdC,iBAAiB,EAAE,CAAC;IACpBC,mBAAmB,EAAE,CAAC;IACtBC,eAAe,EAAE;GAClB;EAED,MAAMC,8BAA8B,GAA+B;IACjEZ,EAAE,EAAE,CAAC;IACLa,oBAAoB,EAAE,CAAC;IACvBC,KAAK,EAAE,CAAC;IACRC,KAAK,EAAE,CAAC;IACRC,aAAa,EAAE,CAAC;IAChBC,MAAM,EAAE,CAAC;IACTC,iBAAiB,EAAE,CAAC;IACpBC,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,KAAK;IACpBC,aAAa,EAAE,KAAK;IACpBC,kBAAkB,EAAE,KAAK;IACzBC,kBAAkB,EAAE,KAAK;IACzBC,aAAa,EAAE,KAAK;IACpBC,mBAAmB,EAAE;GACtB;EAED,MAAMC,yBAAyB,GAAG,CAChC;IAAE1B,EAAE,EAAE,CAAC;IAAE2B,IAAI,EAAE,SAAS;IAAEC,UAAU,EAAE;EAAK,CAAE,EAC7C;IAAE5B,EAAE,EAAE,CAAC;IAAE2B,IAAI,EAAE,SAAS;IAAEC,UAAU,EAAE;EAAK,CAAE,CAC9C;EAED,MAAMC,qBAAqB,GAAG,CAC5B;IAAE7B,EAAE,EAAE,CAAC;IAAE2B,IAAI,EAAE;EAAQ,CAAE,EACzB;IAAE3B,EAAE,EAAE,CAAC;IAAE2B,IAAI,EAAE;EAAQ,CAAE,CAC1B;EAED,MAAMG,8BAA8B,GAA+B;IACjE9B,EAAE,EAAE,CAAC;IACL+B,eAAe,EAAE,CAAC;IAClBC,iBAAiB,EAAE,KAAK;IACxBC,kBAAkB,EAAE,6BAA6B;IACjDC,qBAAqB,EAAE,CAAC;IACxBC,eAAe,EAAE,IAAI;IACrBC,kBAAkB,EAAE,CAAC;IACrBC,4BAA4B,EAAE,IAAI;IAClCC,kBAAkB,EAAE,KAAK;IACzBC,mBAAmB,EAAE,KAAK;IAC1BC,GAAG,EAAE;GACN;EAEDC,UAAU,eAAAC,iBAAA,CAAC,aAAW;IACpB,MAAMC,kBAAkB,GAAGC,OAAO,CAACC,YAAY,CAAC,iBAAiB,EAAE,CACjE,SAAS,CACV,CAAC;IACF,MAAMC,0BAA0B,GAAGF,OAAO,CAACC,YAAY,CACrD,mCAAmC,EACnC,CAAC,2BAA2B,CAAC,CAC9B;IACD,MAAME,eAAe,GAAGH,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC;IACvE,MAAMG,aAAa,GAAGJ,OAAO,CAACC,YAAY,CAAC,4BAA4B,EAAE,CACvE,QAAQ,CACT,CAAC;IACF,MAAMI,0BAA0B,GAAGL,OAAO,CAACC,YAAY,CACrD,yBAAyB,EACzB,CAAC,QAAQ,CAAC,CACX;IACD,MAAMK,wBAAwB,GAAGN,OAAO,CAACC,YAAY,CACnD,mCAAmC,EACnC,CAAC,sBAAsB,EAAE,gBAAgB,CAAC,CAC3C;IACD,MAAMM,gBAAgB,GAAGP,OAAO,CAACC,YAAY,CAAC,eAAe,EAAE,CAC7D,kBAAkB,CACnB,CAAC;IAEF,MAAM5E,OAAO,CAACmF,sBAAsB,CAAC;MACnCC,YAAY,EAAE,CAACnE,mDAAmD,CAAC;MACnEoE,OAAO,EAAE,CACPvF,uBAAuB,EACvBQ,uBAAuB,EACvBF,mBAAmB,CACpB;MACDkF,SAAS,EAAE,CACTnF,WAAW,EACX;QAAEoF,OAAO,EAAEhF,eAAe;QAAEiF,QAAQ,EAAEd;MAAkB,CAAE,EAC1D;QACEa,OAAO,EAAE7E,iCAAiC;QAC1C8E,QAAQ,EAAEX;OACX,EACD;QAAEU,OAAO,EAAE1E,YAAY;QAAE2E,QAAQ,EAAEV;MAAe,CAAE,EACpD;QAAES,OAAO,EAAE/E,0BAA0B;QAAEgF,QAAQ,EAAET;MAAa,CAAE,EAChE;QACEQ,OAAO,EAAE9E,uBAAuB;QAChC+E,QAAQ,EAAER;OACX,EACD;QACEO,OAAO,EAAE3E,iCAAiC;QAC1C4E,QAAQ,EAAEP;OACX,EACD;QAAEM,OAAO,EAAE5E,aAAa;QAAE6E,QAAQ,EAAEN;MAAgB,CAAE;KAEzD,CAAC,CAACO,iBAAiB,EAAE;IAEtBjE,eAAe,GAAGxB,OAAO,CAAC0F,MAAM,CAC9BnF,eAAe,CACmB;IACpCkB,iCAAiC,GAAGzB,OAAO,CAAC0F,MAAM,CAChDhF,iCAAiC,CACmB;IACtDgB,YAAY,GAAG1B,OAAO,CAAC0F,MAAM,CAAC7E,YAAY,CAAiC;IAC3Ec,0BAA0B,GAAG3B,OAAO,CAAC0F,MAAM,CACzClF,0BAA0B,CACmB;IAC/CoB,uBAAuB,GAAG5B,OAAO,CAAC0F,MAAM,CACtCjF,uBAAuB,CACmB;IAC5CoB,iCAAiC,GAAG7B,OAAO,CAAC0F,MAAM,CAChD9E,iCAAiC,CACmB;IAEtDW,OAAO,GAAGvB,OAAO,CAAC2F,eAAe,CAC/B1E,mDAAmD,CACpD;IACDK,SAAS,GAAGC,OAAO,CAACqE,iBAAiB;IACrCtE,SAAS,CAACsB,oBAAoB,GAAG,CAAC;IAClCtB,SAAS,CAACuE,MAAM,GAAG;MACjB9D,EAAE,EAAE,CAAC;MACLa,oBAAoB,EAAE,CAAC;MACvBkD,YAAY,EAAE,CAAC;MACfC,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBC,OAAO,EAAE,IAAID,IAAI,EAAE;MACnBE,YAAY,EAAE,IAAIF,IAAI;KACvB;IAEDxE,eAAe,CAAC2E,OAAO,CAACC,GAAG,CAACC,WAAW,CAACvF,EAAE,CAACgB,YAAY,CAAC,CAAC;IACzDL,iCAAiC,CAAC6E,yBAAyB,CAACF,GAAG,CAACC,WAAW,CACzEvF,EAAE,CAAC6B,8BAA8B,CAAC,CACnC;IACDhB,0BAA0B,CAAC4E,MAAM,CAACH,GAAG,CAACC,WAAW,CAC/CvF,EAAE,CAAC2C,yBAAyB,CAAC,CAC9B;IACD7B,uBAAuB,CAAC2E,MAAM,CAACH,GAAG,CAACC,WAAW,CAACvF,EAAE,CAAC8C,qBAAqB,CAAC,CAAC;IACzE/B,iCAAiC,CAAC2E,oBAAoB,CAACJ,GAAG,CAACC,WAAW,CACpEvF,EAAE,CAAC+C,8BAA8B,CAAC,CACnC;EACH,CAAC,EAAC;EAEF4C,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACpF,SAAS,CAAC,CAACqF,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,kCAAkC,EAAExG,SAAS,CAAC,MAAK;IACpDsB,OAAO,CAACqF,aAAa,EAAE;IACvB1G,IAAI,EAAE;IAENwG,MAAM,CAAClF,eAAe,CAAC2E,OAAO,CAAC,CAACU,oBAAoB,CAAC,CAAC,CAAC;IACvDH,MAAM,CACJjF,iCAAiC,CAAC6E,yBAAyB,CAC5D,CAACO,oBAAoB,CAAC,CAAC,CAAC;IACzBH,MAAM,CAAC/E,0BAA0B,CAAC4E,MAAM,CAAC,CAACO,gBAAgB,EAAE;IAC5DJ,MAAM,CAAC9E,uBAAuB,CAAC2E,MAAM,CAAC,CAACO,gBAAgB,EAAE;IACzDJ,MAAM,CACJ7E,iCAAiC,CAAC2E,oBAAoB,CACvD,CAACK,oBAAoB,CAAC,CAAC,CAAC;IAEzBH,MAAM,CAACpF,SAAS,CAACyF,QAAQ,CAAC,CAACC,OAAO,CAAClF,YAAY,CAAC;IAChD4E,MAAM,CAACpF,SAAS,CAAC2F,0BAA0B,CAAC,CAACD,OAAO,CAClDrE,8BAA8B,CAC/B;IACD+D,MAAM,CAACpF,SAAS,CAAC4F,qBAAqB,CAAC,CAACF,OAAO,CAACvD,yBAAyB,CAAC;IAC1EiD,MAAM,CAACpF,SAAS,CAAC6F,iBAAiB,CAAC,CAACH,OAAO,CAACpD,qBAAqB,CAAC;EACpE,CAAC,CAAC,CAAC;EAEH6C,EAAE,CAAC,wCAAwC,EAAExG,SAAS,CAAC,MAAK;IAC1DuB,eAAe,CAAC2E,OAAO,CAACC,GAAG,CAACC,WAAW,CAACtF,UAAU,CAAC,MAAM,IAAIqG,KAAK,EAAE,CAAC,CAAC;IACtE3F,iCAAiC,CAAC6E,yBAAyB,CAACF,GAAG,CAACC,WAAW,CACzEtF,UAAU,CAAC,MAAM,IAAIqG,KAAK,EAAE,CAAC,CAC9B;IACDzF,0BAA0B,CAAC4E,MAAM,CAACH,GAAG,CAACC,WAAW,CAC/CtF,UAAU,CAAC,MAAM,IAAIqG,KAAK,EAAE,CAAC,CAC9B;IACDxF,uBAAuB,CAAC2E,MAAM,CAACH,GAAG,CAACC,WAAW,CAC5CtF,UAAU,CAAC,MAAM,IAAIqG,KAAK,EAAE,CAAC,CAC9B;IAED7F,OAAO,CAACqF,aAAa,EAAE;IACvB1G,IAAI,EAAE;IAENwG,MAAM,CAAChF,YAAY,CAAC2F,KAAK,CAAC,CAACR,oBAAoB,CAC7C,6CAA6C,CAC9C;IACDH,MAAM,CAAChF,YAAY,CAAC2F,KAAK,CAAC,CAACR,oBAAoB,CAC7C,0CAA0C,CAC3C;IACDH,MAAM,CAAChF,YAAY,CAAC2F,KAAK,CAAC,CAACR,oBAAoB,CAC7C,8CAA8C,CAC/C;IACDH,MAAM,CAAChF,YAAY,CAAC2F,KAAK,CAAC,CAACR,oBAAoB,CAC7C,2CAA2C,CAC5C;EACH,CAAC,CAAC,CAAC;EAEHJ,EAAE,CAAC,0CAA0C,EAAExG,SAAS,CAAC,MAAK;IAC5DqB,SAAS,CAACgG,oBAAoB,GAAG,OAAO;IACxChG,SAAS,CAACiG,sBAAsB,EAAE;IAElCb,MAAM,CAACpF,SAAS,CAACiD,GAAG,CAAC,CAACiD,IAAI,CAAC,MAAM,CAAC;IAClCd,MAAM,CAACpF,SAAS,CAAC+C,kBAAkB,CAAC,CAACmD,IAAI,CAAC,KAAK,CAAC;IAChDd,MAAM,CAACpF,SAAS,CAACgD,mBAAmB,CAAC,CAACkD,IAAI,CAAC,KAAK,CAAC;EACnD,CAAC,CAAC,CAAC;EAEHf,EAAE,CAAC,8BAA8B,EAAE,MAAK;IACtC,MAAMgB,QAAQ,GAAG,IAAIC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE;MAAEC,IAAI,EAAE;IAAiB,CAAE,CAAC;IACxE,MAAMC,KAAK,GAAG;MAAEC,MAAM,EAAE;QAAEC,KAAK,EAAE,CAACL,QAAQ;MAAC;IAAE,CAAsB;IAEnEnG,SAAS,CAACyG,cAAc,CAACH,KAAK,CAAC;IAE/BlB,MAAM,CAACpF,SAAS,CAAC0G,YAAY,CAAC,CAACR,IAAI,CAACC,QAAQ,CAAC;IAC7Cf,MAAM,CAACpF,SAAS,CAAC2G,QAAQ,CAAC,CAACT,IAAI,CAAC,UAAU,CAAC;EAC7C,CAAC,CAAC;EAEFf,EAAE,CAAC,kEAAkE,EAAE,MAAK;IAC1EnF,SAAS,CAAC4G,YAAY,GAAG,KAAK;IAC9B3G,OAAO,CAACqF,aAAa,EAAE;IAEvB,MAAMuB,0BAA0B,GAAG7G,SAAS,CAAC8G,kBAAkB,CAACC,GAAG,CACjE,qBAAqB,CACtB;IACD,MAAMC,uBAAuB,GAC3BhH,SAAS,CAAC8G,kBAAkB,CAACC,GAAG,CAAC,kBAAkB,CAAC;IACtD,MAAME,mCAAmC,GACvCjH,SAAS,CAAC8G,kBAAkB,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAElEF,0BAA0B,EAAEK,QAAQ,CAAC,IAAI,CAAC;IAC1C9B,MAAM,CACJ4B,uBAAuB,EAAEG,YAAY,CAACpI,UAAU,CAACqI,QAAQ,CAAC,CAC3D,CAACC,QAAQ,EAAE;IACZjC,MAAM,CACJ6B,mCAAmC,EAAEE,YAAY,CAACpI,UAAU,CAACqI,QAAQ,CAAC,CACvE,CAACC,QAAQ,EAAE;IAEZR,0BAA0B,EAAEK,QAAQ,CAAC,KAAK,CAAC;IAC3C9B,MAAM,CACJ4B,uBAAuB,EAAEG,YAAY,CAACpI,UAAU,CAACqI,QAAQ,CAAC,CAC3D,CAACE,SAAS,EAAE;IACblC,MAAM,CACJ6B,mCAAmC,EAAEE,YAAY,CAACpI,UAAU,CAACqI,QAAQ,CAAC,CACvE,CAACE,SAAS,EAAE;EACf,CAAC,CAAC;EAEFnC,EAAE,CAAC,mCAAmC,EAAE,MAAK;IAC3CoC,KAAK,CAACvH,SAAS,CAACwH,kBAAkB,EAAE,MAAM,CAAC;IAC3CvH,OAAO,CAACqF,aAAa,EAAE;IAEvBtF,SAAS,CAAC8G,kBAAkB,CAACW,UAAU,CAAC;MACtChF,iBAAiB,EAAE,OAAO;MAC1BiF,mBAAmB,EAAE,CAAC;MACtB9E,eAAe,EAAE;KAClB,CAAC;IAEFwC,MAAM,CAACpF,SAAS,CAACwH,kBAAkB,CAACG,IAAI,CAAC,CAACnC,gBAAgB,EAAE;EAC9D,CAAC,CAAC;EAEFL,EAAE,CAAC,6BAA6B,EAAE,MAAK;IACrC,MAAMyC,OAAO,GAAG,6BAA6B;IAC7CL,KAAK,CAACM,MAAM,EAAE,MAAM,CAAC;IAErB7H,SAAS,CAAC8H,0BAA0B,GAAG;MACrC,GAAGvF,8BAA8B;MACjCG,kBAAkB,EAAEkF;KACrB;IAED5H,SAAS,CAAC+H,mBAAmB,EAAE;IAE/B3C,MAAM,CAACyC,MAAM,CAACG,IAAI,CAAC,CAACzC,oBAAoB,CAACqC,OAAO,EAAE,QAAQ,CAAC;EAC7D,CAAC,CAAC;EAEFzC,EAAE,CAAC,mDAAmD,EAAE,MAAK;IAC3DnF,SAAS,CAAC4G,YAAY,GAAG,IAAI;IAE7B3G,OAAO,GAAGvB,OAAO,CAAC2F,eAAe,CAC/B1E,mDAAmD,CACpD;IACDK,SAAS,GAAGC,OAAO,CAACqE,iBAAiB;IACrCtE,SAAS,CAAC4G,YAAY,GAAG,IAAI;IAC7B5G,SAAS,CAACsB,oBAAoB,GAAG,CAAC;IAClCtB,SAAS,CAACuE,MAAM,GAAG;MACjB9D,EAAE,EAAE,CAAC;MACLa,oBAAoB,EAAE,CAAC;MACvBkD,YAAY,EAAE,CAAC;MACfC,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBC,OAAO,EAAE,IAAID,IAAI,EAAE;MACnBE,YAAY,EAAE,IAAIF,IAAI;KACvB;IAEDzE,OAAO,CAACqF,aAAa,EAAE;IAEvBF,MAAM,CACJpF,SAAS,CAAC8G,kBAAkB,CAACC,GAAG,CAAC,qBAAqB,CAAC,EAAEkB,QAAQ,CAClE,CAACZ,QAAQ,EAAE;EACd,CAAC,CAAC;EAEFlC,EAAE,CAAC,wEAAwE,EAAE,MAAK;IAChFnF,SAAS,CAAC4G,YAAY,GAAG,KAAK;IAC9B5G,SAAS,CAAC8H,0BAA0B,GAAG;MACrC,GAAGvF,8BAA8B;MACjCG,kBAAkB,EAAEwF;KACrB;IACDlI,SAAS,CAACmI,WAAW,CAAC;MACpBL,0BAA0B,EAAE;QAC1BM,YAAY,EAAEpI,SAAS,CAAC8H,0BAA0B;QAClDO,WAAW,EAAE,IAAI;QACjBC,aAAa,EAAE,IAAI;QACnBC,aAAa,EAAEA,CAAA,KAAM;;KAExB,CAAC;IAEF,MAAMC,sBAAsB,GAC1BxI,SAAS,CAAC8G,kBAAkB,CAACC,GAAG,CAAC,iBAAiB,CAAC;IACrD3B,MAAM,CACJoD,sBAAsB,EAAErB,YAAY,CAACpI,UAAU,CAACqI,QAAQ,CAAC,CAC1D,CAACC,QAAQ,EAAE;EACd,CAAC,CAAC;EAEFlC,EAAE,CAAC,qDAAqD,EAAE,MAAK;IAC7DnF,SAAS,CAAC4G,YAAY,GAAG,IAAI;IAC7B5G,SAAS,CAACmI,WAAW,CAAC;MACpBL,0BAA0B,EAAE;QAC1BM,YAAY,EAAEpI,SAAS,CAAC8H,0BAA0B;QAClDO,WAAW,EAAE,IAAI;QACjBC,aAAa,EAAE,IAAI;QACnBC,aAAa,EAAEA,CAAA,KAAM;;KAExB,CAAC;IAEF,MAAMC,sBAAsB,GAC1BxI,SAAS,CAAC8G,kBAAkB,CAACC,GAAG,CAAC,iBAAiB,CAAC;IACrD3B,MAAM,CACJoD,sBAAsB,EAAErB,YAAY,CAACpI,UAAU,CAACqI,QAAQ,CAAC,CAC1D,CAACE,SAAS,EAAE;EACf,CAAC,CAAC;EAEFnC,EAAE,CAAC,8DAA8D,EAAE,MAAK;IACtEnF,SAAS,CAAC4G,YAAY,GAAG,KAAK;IAC9B5G,SAAS,CAAC8H,0BAA0B,GAAG;MACrC,GAAGvF,8BAA8B;MACjCG,kBAAkB,EAAE;KACrB;IACD1C,SAAS,CAACmI,WAAW,CAAC;MACpBL,0BAA0B,EAAE;QAC1BM,YAAY,EAAEpI,SAAS,CAAC8H,0BAA0B;QAClDO,WAAW,EAAE,IAAI;QACjBC,aAAa,EAAE,IAAI;QACnBC,aAAa,EAAEA,CAAA,KAAM;;KAExB,CAAC;IAEF,MAAMC,sBAAsB,GAC1BxI,SAAS,CAAC8G,kBAAkB,CAACC,GAAG,CAAC,iBAAiB,CAAC;IACrD3B,MAAM,CACJoD,sBAAsB,EAAErB,YAAY,CAACpI,UAAU,CAACqI,QAAQ,CAAC,CAC1D,CAACE,SAAS,EAAE;EACf,CAAC,CAAC;EAEFnC,EAAE,CAAC,8EAA8E,EAAExG,SAAS,CAAC,MAAK;IAChG,MAAM8J,QAAQ,GAAG;MAAEC,MAAM,EAAE;IAAG,CAAE;IAChCvI,iCAAiC,CAAC6E,yBAAyB,CAACF,GAAG,CAACC,WAAW,CACzEtF,UAAU,CAAC,MAAMgJ,QAAQ,CAAC,CAC3B;IAEDzI,SAAS,CAAC2I,8BAA8B,EAAE;IAC1C/J,IAAI,EAAE;IAENwG,MAAM,CAAChF,YAAY,CAAC2F,KAAK,CAAC,CAAC6C,GAAG,CAACrD,oBAAoB,CACjD,0CAA0C,CAC3C;EACH,CAAC,CAAC,CAAC;EAEHJ,EAAE,CAAC,wEAAwE,EAAExG,SAAS,CAAC,MAAK;IAC1F,MAAMkK,QAAQ,GAAG;MAAEH,MAAM,EAAE;IAAG,CAAE;IAChCvI,iCAAiC,CAAC6E,yBAAyB,CAACF,GAAG,CAACC,WAAW,CACzEtF,UAAU,CAAC,MAAMoJ,QAAQ,CAAC,CAC3B;IAED7I,SAAS,CAAC2I,8BAA8B,EAAE;IAC1C/J,IAAI,EAAE;IAENwG,MAAM,CAAChF,YAAY,CAAC2F,KAAK,CAAC,CAACR,oBAAoB,CAC7C,0CAA0C,CAC3C;EACH,CAAC,CAAC,CAAC;EAEHJ,EAAE,CAAC,2FAA2F,EAAE,MAAK;IACnGnF,SAAS,CAAC4G,YAAY,GAAG,IAAI;IAC7BW,KAAK,CAACvH,SAAS,CAACwH,kBAAkB,EAAE,MAAM,CAAC;IAC3CvH,OAAO,CAACqF,aAAa,EAAE;IAEvBtF,SAAS,CAAC8G,kBAAkB,CAACC,GAAG,CAAC,iBAAiB,CAAC,EAAEG,QAAQ,CAAC,IAAI,CAAC;IAEnE9B,MAAM,CAACpF,SAAS,CAACwH,kBAAkB,CAACG,IAAI,CAAC,CAACpC,oBAAoB,CAAC,IAAI,CAAC;EACtE,CAAC,CAAC;EAEFJ,EAAE,CAAC,sEAAsE,EAAE,MAAK;IAC9EnF,SAAS,CAAC4G,YAAY,GAAG,KAAK;IAC9B5G,SAAS,CAAC0G,YAAY,GAAG,IAAIN,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE;MAClDC,IAAI,EAAE;KACP,CAAC;IACFrG,SAAS,CAAC8G,kBAAkB,CAACW,UAAU,CAAC;MACtChF,iBAAiB,EAAE,MAAM;MACzBiF,mBAAmB,EAAE,CAAC;MACtB9E,eAAe,EAAE,KAAK;MACtBkG,mBAAmB,EAAE,IAAI;MACzBC,gBAAgB,EAAE,CAAC;MACnBjG,4BAA4B,EAAE;KAC/B,CAAC;IAEF,MAAMkG,MAAM,GAAGhJ,SAAS,CAACiJ,qBAAqB,EAAE;IAEhD7D,MAAM,CAAC4D,MAAM,CAACvG,iBAAiB,CAAC,CAACyD,IAAI,CAAC,MAAM,CAAC;IAC7Cd,MAAM,CAAC4D,MAAM,CAACrG,qBAAqB,CAAC,CAACuD,IAAI,CAAC,CAAC,CAAC;IAC5Cd,MAAM,CAAC4D,MAAM,CAACpG,eAAe,CAAC,CAACsD,IAAI,CAAC,KAAK,CAAC;IAC1Cd,MAAM,CAAC4D,MAAM,CAACnG,kBAAkB,CAAC,CAACqD,IAAI,CAAC,CAAC,CAAC;IACzCd,MAAM,CAAC4D,MAAM,CAAClG,4BAA4B,CAAC,CAACoD,IAAI,CAAC,IAAI,CAAC;EACxD,CAAC,CAAC;EAEFf,EAAE,CAAC,yEAAyE,EAAE,MAAK;IACjFnF,SAAS,CAAC4G,YAAY,GAAG,KAAK;IAC9B5G,SAAS,CAAC0G,YAAY,GAAG,IAAIN,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE;MAClDC,IAAI,EAAE;KACP,CAAC;IACFrG,SAAS,CAAC8G,kBAAkB,CAACW,UAAU,CAAC;MACtChF,iBAAiB,EAAE,MAAM;MACzBiF,mBAAmB,EAAE,CAAC;MACtB9E,eAAe,EAAE,KAAK;MACtBkG,mBAAmB,EAAE;KACtB,CAAC;IAEF,MAAME,MAAM,GAAGhJ,SAAS,CAACiJ,qBAAqB,EAAE;IAEhD7D,MAAM,CAAC4D,MAAM,CAACvG,iBAAiB,CAAC,CAACyD,IAAI,CAAC,MAAM,CAAC;IAC7Cd,MAAM,CAAC4D,MAAM,CAACrG,qBAAqB,CAAC,CAACuD,IAAI,CAAC,CAAC,CAAC;IAC5Cd,MAAM,CAAC4D,MAAM,CAACpG,eAAe,CAAC,CAACsD,IAAI,CAAC,KAAK,CAAC;IAC1Cd,MAAM,CAAC4D,MAAM,CAACnG,kBAAkB,CAAC,CAACqG,QAAQ,EAAE;IAC5C9D,MAAM,CAAC4D,MAAM,CAAClG,4BAA4B,CAAC,CAACoG,QAAQ,EAAE;EACxD,CAAC,CAAC;EAEF/D,EAAE,CAAC,8CAA8C,EAAE,MAAK;IACtDnF,SAAS,CAAC4G,YAAY,GAAG,KAAK;IAC9B5G,SAAS,CAAC8H,0BAA0B,GAAGvF,8BAA8B;IACrEvC,SAAS,CAAC8G,kBAAkB,CAACW,UAAU,CAAC;MACtChF,iBAAiB,EAAE,MAAM;MACzBiF,mBAAmB,EAAE,CAAC;MACtB9E,eAAe,EAAE,KAAK;MACtBkG,mBAAmB,EAAE,IAAI;MACzBC,gBAAgB,EAAE,CAAC;MACnBjG,4BAA4B,EAAE;KAC/B,CAAC;IACFyE,KAAK,CAACvH,SAAS,CAACmJ,kBAAkB,EAAE,MAAM,CAAC;IAE3CnJ,SAAS,CAACoJ,MAAM,EAAE;IAElBhE,MAAM,CAACpF,SAAS,CAACmJ,kBAAkB,CAACxB,IAAI,CAAC,CAACnC,gBAAgB,EAAE;EAC9D,CAAC,CAAC;EAEFL,EAAE,CAAC,0DAA0D,EAAExG,SAAS,CAAC,MAAK;IAC5E0B,0BAA0B,CAAC4E,MAAM,CAACH,GAAG,CAACC,WAAW,CAC/CtF,UAAU,CAAC,MAAM,IAAIqG,KAAK,EAAE,CAAC,CAC9B;IAED9F,SAAS,CAACqJ,yBAAyB,EAAE;IACrCzK,IAAI,EAAE;IAENwG,MAAM,CAAChF,YAAY,CAAC2F,KAAK,CAAC,CAACR,oBAAoB,CAC7C,8CAA8C,CAC/C;EACH,CAAC,CAAC,CAAC;EAEHJ,EAAE,CAAC,0EAA0E,EAAE,MAAK;IAClFnF,SAAS,CAAC4G,YAAY,GAAG,IAAI;IAC7B3G,OAAO,CAACqF,aAAa,EAAE;IAEvB,MAAM0B,uBAAuB,GAC3BhH,SAAS,CAAC8G,kBAAkB,CAACC,GAAG,CAAC,kBAAkB,CAAC;IACtD,MAAME,mCAAmC,GACvCjH,SAAS,CAAC8G,kBAAkB,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAElEQ,KAAK,CAACP,uBAAwB,EAAE,eAAe,CAAC;IAChDO,KAAK,CAACN,mCAAoC,EAAE,eAAe,CAAC;IAE5DjH,SAAS,CAAC8G,kBAAkB,CAACC,GAAG,CAAC,qBAAqB,CAAC,EAAEG,QAAQ,CAAC,IAAI,CAAC;IAEvE9B,MAAM,CAAC4B,uBAAwB,CAACsC,aAAa,CAAC,CAACV,GAAG,CAACpD,gBAAgB,EAAE;IACrEJ,MAAM,CACJ6B,mCAAoC,CAACqC,aAAa,CACnD,CAACV,GAAG,CAACpD,gBAAgB,EAAE;EAC1B,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}