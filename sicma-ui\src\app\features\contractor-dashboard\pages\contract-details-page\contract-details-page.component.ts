import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { MatProgressBar } from '@angular/material/progress-bar';
import { MatTab, MatTabGroup } from '@angular/material/tabs';
import { ContractDetails } from '@contract-management/models/contract-details.model';
import { ContractService } from '@contract-management/services/contract.service';
import { ContractorContractService } from '@contract-management/services/contractor-contract.service';
import { MonthlyReport } from '@contractor-dashboard/models/monthly-report.model';
import { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';
import { AlertService } from '@shared/services/alert.service';
import { finalize } from 'rxjs';
import { ContractDetailsTabComponent } from '../../components/contract-details-tab/contract-details-tab.component';
import { ContractSummaryComponent } from '../../components/contract-summary/contract-summary.component';
import { MonthlyReportsTabComponent } from '../../components/monthly-reports-tab/monthly-reports-tab.component';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-contract-details-page',
  templateUrl: './contract-details-page.component.html',
  styleUrl: './contract-details-page.component.scss',
  standalone: true,
  imports: [
    MatProgressBar,
    ContractSummaryComponent,
    MatTabGroup,
    MatTab,
    ContractDetailsTabComponent,
    MonthlyReportsTabComponent,
  ],
})
export class ContractDetailsPageComponent implements OnInit {
  contract: ContractDetails | null = null;
  contractorContractId: number | undefined;
  monthlyReports: MonthlyReport[] = [];
  isLoading = true;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly contractService: ContractService,
    private readonly contractorContractService: ContractorContractService,
    private readonly monthlyReportService: MonthlyReportService,
    private readonly alert: AlertService,
  ) {}

  ngOnInit(): void {
    const contractId = this.route.snapshot.paramMap.get('id');
    if (contractId) {
      this.loadContractDetails(+contractId);
    } else {
      this.alert.error('ID del contrato no proporcionado');
    }
  }

  loadContractDetails(contractId: number): void {
    this.isLoading = true;
    this.contractService
      .getDetailsById(contractId)
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe({
        next: (contract) => {
          if (contract.id) {
            this.contract = contract;
            this.loadContractorContract(contract.id);
          }
        },
        error: (error: HttpErrorResponse) => {
          this.alert.error(
            error.error?.detail ?? 'Error al cargar los detalles del contrato',
          );
        },
      });
  }

  loadContractorContract(contractId: number): void {
    this.isLoading = true;
    this.contractorContractService
      .getAllByContractId(contractId)
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe({
        next: (contractorContracts) => {
          if (contractorContracts.length > 0) {
            this.contractorContractId = contractorContracts[0].id;
            this.loadMonthlyReportsAndPayments();
          } else {
            this.alert.error(
              'No se encontró un contrato de contratista para este contrato',
            );
          }
        },
        error: (error: HttpErrorResponse) => {
          this.alert.error(
            error.error?.detail ??
              'Error al cargar el contrato del contratista',
          );
        },
      });
  }

  loadMonthlyReportsAndPayments(): void {
    if (!this.contractorContractId) {
      this.alert.error('ID de contrato de contratista no disponible');
      this.isLoading = false;
      return;
    }

    this.monthlyReportService
      .getByContractorContractId(this.contractorContractId)
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe({
        next: (monthlyReports: MonthlyReport[]): void => {
          this.monthlyReports = monthlyReports;
        },
        error: (error: HttpErrorResponse) => {
          this.alert.error(
            error.error?.detail ?? 'Error al cargar informes mensuales',
          );
        },
      });
  }
}
