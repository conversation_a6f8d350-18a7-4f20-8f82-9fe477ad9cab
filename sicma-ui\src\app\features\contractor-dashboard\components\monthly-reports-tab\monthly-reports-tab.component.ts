import {
  AfterViewInit,
  Component,
  Input,
  OnChanges,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NgxSpinnerService } from 'ngx-spinner';
import { finalize, map, switchMap } from 'rxjs';

import { CurrencyPipe, DatePipe } from '@angular/common';
import { ContractorContractService } from '@contract-management/services/contractor-contract.service';
import { ConsultPeriod } from '@contractor-dashboard/models/ConsultPeriod.model';
import { MonthlyReport } from '@contractor-dashboard/models/monthly-report.model';
import { Period } from '@contractor-dashboard/models/Period.model';
import { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';
import { PeriodService } from '@contractor-dashboard/services/period.service';
import { ReportReviewHistoryService } from '@contractor-dashboard/services/report-review-history.service';
import { ReportReviewStatusService } from '@contractor-dashboard/services/report-review-status.service';
import { AuthService } from '@core/auth/services/auth.service';
import { AlertService } from '@shared/services/alert.service';
import { MonthlyReportDialogComponent } from './monthly-report-dialog/monthly-report-dialog.component';
import { MonthlyReportReviewHistoryDialogComponent } from './monthly-report-review-history-dialog/monthly-report-review-history-dialog.component';
import { SelectPeriodDialogComponent } from './select-period-dialog/select-period-dialog.component';
import { ReportReviewStatus } from '@contractor-dashboard/models/report-review-status.model';
import { ContractorContract } from '@contract-management/models/contractor-contract.model';

@Component({
  selector: 'app-monthly-reports-tab',
  templateUrl: './monthly-reports-tab.component.html',
  styleUrl: './monthly-reports-tab.component.scss',
  standalone: true,
  imports: [
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatCardModule,
    DatePipe,
    CurrencyPipe,
  ],
})
export class MonthlyReportsTabComponent implements OnChanges, AfterViewInit {
  @Input() monthlyReports: MonthlyReport[] = [];
  @Input() contractorContractId: number | undefined;
  @Input() contractStartDate: Date | undefined;

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  Period!: Period;
  displayedColumns: string[] = [
    'reportNumber',
    'startDate',
    'endDate',
    'currentReviewStatus',
    'totalValue',
    'actions',
  ];
  dataSource = new MatTableDataSource<MonthlyReport>();

  constructor(
    private readonly dialog: MatDialog,
    private readonly alert: AlertService,
    private readonly monthlyReportService: MonthlyReportService,
    private readonly contractorContractService: ContractorContractService,
    private readonly reportReviewStatusService: ReportReviewStatusService,
    private readonly periodService: PeriodService,
    private readonly spinner: NgxSpinnerService,
    private readonly reportReviewHistoryService: ReportReviewHistoryService,
    private readonly authService: AuthService,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['monthlyReports']) {
      this.dataSource.data = this.monthlyReports;
      if (this.dataSource.paginator) {
        this.dataSource.paginator.firstPage();
      }
    }
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  openReportDetailsForm(report: MonthlyReport): void {
    const dialogRef = this.dialog.open(MonthlyReportDialogComponent, {
      width: '90vw',
      height: '90vh',
      maxWidth: '100vw',
      maxHeight: '100vh',
      data: { report },
    });

    dialogRef.afterClosed().subscribe((result: boolean) => {
      if (result && this.contractorContractId) {
        this.monthlyReportService
          .getByContractorContractId(this.contractorContractId)
          .subscribe({
            next: (monthlyReports) => {
              this.monthlyReports = monthlyReports;
              this.dataSource.data = monthlyReports;
            },
            error: (error) => {
              this.alert.error(error.error?.detail ?? 'Error al actualizar la tabla de informes');
            },
          });
      }
    });
  }

  createNewReport(): void {
    this.spinner.show();

    if (!this.contractorContractId || !this.contractStartDate) {
      this.alert.warning(
        'No se puede crear un informe sin un contrato asociado',
      );
      this.spinner.hide();
      return;
    }

    this.contractorContractService
      .getById(this.contractorContractId)
      .pipe(
        switchMap((contractorContract) => {
          if (!contractorContract.contract?.id) {
            throw new Error('Error: No se encontró el ID del contrato');
          }

          if (this.monthlyReports.length === 0) {
            this.spinner.hide();
            const dialogRef = this.dialog.open(SelectPeriodDialogComponent, {
              maxWidth: '600px',
              width: '100%',
              data: {
                contractId: contractorContract.contract.id,
                contractorContractId: this.contractorContractId,
              },
              autoFocus: 'dialog',
            });

            return dialogRef.afterClosed().pipe(
              switchMap((selectedPeriod: Period | undefined) => {
                if (selectedPeriod) {
                  this.spinner.show();
                  return this.monthlyReportService
                    .getByContractorContractId(this.contractorContractId!)
                    .pipe(
                      map((monthlyReports) => ({
                        monthlyReports,
                        selectedPeriod,
                      })),
                    );
                } else {
                  return [];
                }
              }),
            );
          } else {
            this.monthlyReports.sort((a, b) => a.id - b.id);
            const lastReport =
              this.monthlyReports[this.monthlyReports.length - 1];
            const newReportNumber = lastReport
              ? lastReport.reportNumber + 1
              : 1;

            const newPeriod: Omit<ConsultPeriod, 'id'> = {
              id_contrat: contractorContract.contract.id,
              num_payment: newReportNumber,
            };

            return this.periodService.create(newPeriod).pipe(
              switchMap((period) => {
                return this.reportReviewStatusService
                  .getByName('Borrador')
                  .pipe(
                    map((reportReviewStatus) => ({
                      period,
                      reportReviewStatus,
                      contractorContract,
                      newReportNumber,
                    })),
                  );
              }),
            );
          }
        }),
        finalize(() => this.spinner.hide()),
      )
      .subscribe({
        next: ({
          monthlyReports,
          period,
          reportReviewStatus,
          contractorContract,
          newReportNumber,
          selectedPeriod,
        }: {
          monthlyReports?: MonthlyReport[];
          period?: Period;
          reportReviewStatus?: ReportReviewStatus;
          contractorContract?: ContractorContract;
          newReportNumber?: number;
          selectedPeriod?: Period;
        }) => {
          if (monthlyReports) {
            this.monthlyReports = monthlyReports;
            this.dataSource.data = this.monthlyReports;
            if (selectedPeriod) {
              const reportToOpen = this.monthlyReports.find(
                (r) => r.reportNumber === selectedPeriod.num_payment,
              );
              if (reportToOpen) {
                if (reportToOpen.reportNumber === 1) {
                  this.alert.success('Informe mensual creado correctamente');
                } else {
                  this.alert.success(
                    'Informes mensuales creados correctamente',
                  );
                }
                this.openReportDetailsForm(reportToOpen);
              }
            }
          } else if (
            period &&
            reportReviewStatus &&
            contractorContract &&
            newReportNumber
          ) {
            if (reportReviewStatus.id) {
              this.Period = period;
              this.monthlyReportService
                .create({
                  contractorContractId: contractorContract.id,
                  reportNumber: newReportNumber,
                  startDate: period.start_date,
                  endDate: period.end_date,
                  totalValue: period.payment,
                  creationDate: new Date().toISOString().slice(0, 10),
                  contractYear: contractorContract.contract?.contractYear?.year,
                })
                .subscribe({
                  next: (createdReport: MonthlyReport) => {
                    const currentUser = this.authService.getCurrentUser();
                    if (!currentUser?.id) {
                      this.alert.error(
                        'Error: No se pudo obtener el ID del usuario actual',
                      );
                      return;
                    }

                    this.reportReviewHistoryService
                      .create({
                        monthlyReportId: createdReport.id,
                        reviewStatusId: reportReviewStatus.id,
                        reviewDate: new Date(),
                        comment: 'Informe creado',
                        reviewerId: currentUser.id,
                      })
                      .subscribe({
                        next: () => {
                          this.monthlyReportService
                            .getByContractorContractId(contractorContract.id)
                            .subscribe({
                              next: (monthlyReports) => {
                                this.monthlyReports = monthlyReports;
                                this.dataSource.data = this.monthlyReports;
                                this.alert.success(
                                  'Informe mensual creado correctamente',
                                );
                                this.openReportDetailsForm(createdReport);
                              },
                              error: (error) => {
                                this.alert.error(error.error?.detail ?? 'Error al actualizar la lista de informes');
                              },
                            });
                        },
                        error: (error) => {
                          this.alert.error(error.error?.detail ?? 'Error al crear el historial de revisiones');
                          this.monthlyReportService
                            .getByContractorContractId(contractorContract.id)
                            .subscribe({
                              next: (monthlyReports) => {
                                this.monthlyReports = monthlyReports;
                                this.dataSource.data = this.monthlyReports;
                                this.alert.success(
                                  'Informe mensual creado correctamente',
                                );
                                this.openReportDetailsForm(createdReport);
                              },
                              error: (error) => {
                                this.alert.error(error.error?.detail ?? 'Error al actualizar la lista de informes');
                              },
                            });
                        },
                      });
                  },
                  error: (error) => {
                    this.alert.error(error.error?.detail ?? 'Error al crear el nuevo informe.');
                  },
                });
            }
          }
        },
        error: (error) => {
          this.alert.error(error.error?.detail ?? 'Error al crear el nuevo informe.');
        },
      });
  }

  get canCreateNewReport(): boolean {
    if (this.monthlyReports.length === 0) return true;

    const sortedReports = [...this.monthlyReports].sort(
      (a, b) => b.reportNumber - a.reportNumber,
    );
    const lastReport = sortedReports[0];

    return lastReport.currentReviewStatus?.name === 'Aprobado';
  }

  downloadPdf(report: MonthlyReport): void {
    if (!report.id) return;

    this.spinner.show();
    this.monthlyReportService
      .downloadPdf(report.id)
      .pipe(finalize(() => this.spinner.hide()))
      .subscribe({
        next: (blob) => {
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `Informe_Mensual_${report.reportNumber}.pdf`;
          link.click();
          window.URL.revokeObjectURL(url);
          this.alert.success('PDF descargado exitosamente');
        },
        error: (error) => {
          this.alert.error(error.error?.detail ?? 'Error al descargar el PDF');
        },
      });
  }

  openReviewHistoryDialog(report: MonthlyReport): void {
    this.dialog.open(MonthlyReportReviewHistoryDialogComponent, {
      width: '1000px',
      data: { monthlyReportId: report.id },
    });
  }
}
