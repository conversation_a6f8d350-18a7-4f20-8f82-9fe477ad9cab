{"ast": null, "code": "import { FormBuilder, FormControl } from '@angular/forms';\nimport { createDateComparisonValidator } from './date-comparison.validator';\ndescribe('DateComparisonValidator', () => {\n  let fb;\n  let form;\n  let config;\n  beforeEach(() => {\n    fb = new FormBuilder();\n    form = fb.group({\n      startDate: [null],\n      subscriptionDate: [null]\n    });\n    config = {\n      minStartDate: null,\n      latestContractEndDate: null\n    };\n  });\n  it('should return null when value is null', () => {\n    const validator = createDateComparisonValidator(config);\n    const result = validator(form.get('startDate'));\n    expect(result).toBeNull();\n  });\n  describe('Subscription Date Validation', () => {\n    it('should return subscriptionDateNotBeforeStartDate error when subscription date is after start date', () => {\n      const validator = createDateComparisonValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-01-01'));\n      form.get('subscriptionDate')?.setValue(new Date('2024-02-01'));\n      const result = validator(form.get('subscriptionDate'));\n      expect(result).toEqual({\n        subscriptionDateNotBeforeStartDate: true\n      });\n    });\n    it('should return null when subscription date is before start date', () => {\n      const validator = createDateComparisonValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-02-01'));\n      form.get('subscriptionDate')?.setValue(new Date('2024-01-01'));\n      const result = validator(form.get('subscriptionDate'));\n      expect(result).toBeNull();\n    });\n  });\n  describe('Start Date Validation', () => {\n    it('should return startDateNotAfterSubscriptionDate error when start date is before subscription date', () => {\n      const validator = createDateComparisonValidator(config);\n      form.get('subscriptionDate')?.setValue(new Date('2024-06-01'));\n      form.get('startDate')?.setValue(new Date('2024-05-01'));\n      const result = validator(form.get('startDate'));\n      expect(result).toEqual({\n        startDateNotAfterSubscriptionDate: true\n      });\n    });\n    it('should return startDateTooEarly error when start date is before minStartDate', () => {\n      config.minStartDate = new Date('2024-06-01');\n      const validator = createDateComparisonValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-05-01'));\n      const result = validator(form.get('startDate'));\n      expect(result).toEqual({\n        startDateTooEarly: true\n      });\n    });\n    it('should return startDateAfterContractEnd error when start date is after latestContractEndDate', () => {\n      config.latestContractEndDate = new Date('2024-12-31');\n      const validator = createDateComparisonValidator(config);\n      form.get('startDate')?.setValue(new Date('2025-01-01'));\n      const result = validator(form.get('startDate'));\n      expect(result).toEqual({\n        startDateAfterContractEnd: true\n      });\n    });\n    it('should return null when start date is valid', () => {\n      config.minStartDate = new Date('2024-01-01');\n      config.latestContractEndDate = new Date('2024-12-31');\n      const validator = createDateComparisonValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-06-01'));\n      form.get('subscriptionDate')?.setValue(new Date('2024-05-01'));\n      const result = validator(form.get('startDate'));\n      expect(result).toBeNull();\n    });\n  });\n  describe('Edge Cases', () => {\n    it('should return null when form is null', () => {\n      const validator = createDateComparisonValidator(config);\n      const mockControl = new FormControl(null);\n      const result = validator(mockControl);\n      expect(result).toBeNull();\n    });\n  });\n});", "map": {"version": 3, "names": ["FormBuilder", "FormControl", "createDateComparisonValidator", "describe", "fb", "form", "config", "beforeEach", "group", "startDate", "subscriptionDate", "minStartDate", "latestContractEndDate", "it", "validator", "result", "get", "expect", "toBeNull", "setValue", "Date", "toEqual", "subscriptionDateNotBeforeStartDate", "startDateNotAfterSubscriptionDate", "startDateTooEarly", "startDateAfterContractEnd", "mockControl"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\components\\associated-contractors-list\\associate-contractor-dialog\\validators\\date-comparison.validator.spec.ts"], "sourcesContent": ["import { Form<PERSON><PERSON>er, FormControl, FormGroup } from '@angular/forms';\nimport {\n  createDateComparisonValidator,\n  DateComparisonValidatorConfig,\n} from './date-comparison.validator';\n\ndescribe('DateComparisonValidator', () => {\n  let fb: FormBuilder;\n  let form: FormGroup;\n  let config: DateComparisonValidatorConfig;\n\n  beforeEach(() => {\n    fb = new FormBuilder();\n    form = fb.group({\n      startDate: [null],\n      subscriptionDate: [null],\n    });\n\n    config = {\n      minStartDate: null,\n      latestContractEndDate: null,\n    };\n  });\n\n  it('should return null when value is null', () => {\n    const validator = createDateComparisonValidator(config);\n    const result = validator(form.get('startDate')!);\n    expect(result).toBeNull();\n  });\n\n  describe('Subscription Date Validation', () => {\n    it('should return subscriptionDateNotBeforeStartDate error when subscription date is after start date', () => {\n      const validator = createDateComparisonValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-01-01'));\n      form.get('subscriptionDate')?.setValue(new Date('2024-02-01'));\n      const result = validator(form.get('subscriptionDate')!);\n      expect(result).toEqual({ subscriptionDateNotBeforeStartDate: true });\n    });\n\n    it('should return null when subscription date is before start date', () => {\n      const validator = createDateComparisonValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-02-01'));\n      form.get('subscriptionDate')?.setValue(new Date('2024-01-01'));\n      const result = validator(form.get('subscriptionDate')!);\n      expect(result).toBeNull();\n    });\n  });\n\n  describe('Start Date Validation', () => {\n    it('should return startDateNotAfterSubscriptionDate error when start date is before subscription date', () => {\n      const validator = createDateComparisonValidator(config);\n      form.get('subscriptionDate')?.setValue(new Date('2024-06-01'));\n      form.get('startDate')?.setValue(new Date('2024-05-01'));\n      const result = validator(form.get('startDate')!);\n      expect(result).toEqual({ startDateNotAfterSubscriptionDate: true });\n    });\n\n    it('should return startDateTooEarly error when start date is before minStartDate', () => {\n      config.minStartDate = new Date('2024-06-01');\n      const validator = createDateComparisonValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-05-01'));\n      const result = validator(form.get('startDate')!);\n      expect(result).toEqual({ startDateTooEarly: true });\n    });\n\n    it('should return startDateAfterContractEnd error when start date is after latestContractEndDate', () => {\n      config.latestContractEndDate = new Date('2024-12-31');\n      const validator = createDateComparisonValidator(config);\n      form.get('startDate')?.setValue(new Date('2025-01-01'));\n      const result = validator(form.get('startDate')!);\n      expect(result).toEqual({ startDateAfterContractEnd: true });\n    });\n\n    it('should return null when start date is valid', () => {\n      config.minStartDate = new Date('2024-01-01');\n      config.latestContractEndDate = new Date('2024-12-31');\n      const validator = createDateComparisonValidator(config);\n      form.get('startDate')?.setValue(new Date('2024-06-01'));\n      form.get('subscriptionDate')?.setValue(new Date('2024-05-01'));\n      const result = validator(form.get('startDate')!);\n      expect(result).toBeNull();\n    });\n  });\n\n  describe('Edge Cases', () => {\n    it('should return null when form is null', () => {\n      const validator = createDateComparisonValidator(config);\n      const mockControl = new FormControl(null);\n      const result = validator(mockControl);\n      expect(result).toBeNull();\n    });\n  });\n});"], "mappings": "AAAA,SAASA,WAAW,EAAEC,WAAW,QAAmB,gBAAgB;AACpE,SACEC,6BAA6B,QAExB,6BAA6B;AAEpCC,QAAQ,CAAC,yBAAyB,EAAE,MAAK;EACvC,IAAIC,EAAe;EACnB,IAAIC,IAAe;EACnB,IAAIC,MAAqC;EAEzCC,UAAU,CAAC,MAAK;IACdH,EAAE,GAAG,IAAIJ,WAAW,EAAE;IACtBK,IAAI,GAAGD,EAAE,CAACI,KAAK,CAAC;MACdC,SAAS,EAAE,CAAC,IAAI,CAAC;MACjBC,gBAAgB,EAAE,CAAC,IAAI;KACxB,CAAC;IAEFJ,MAAM,GAAG;MACPK,YAAY,EAAE,IAAI;MAClBC,qBAAqB,EAAE;KACxB;EACH,CAAC,CAAC;EAEFC,EAAE,CAAC,uCAAuC,EAAE,MAAK;IAC/C,MAAMC,SAAS,GAAGZ,6BAA6B,CAACI,MAAM,CAAC;IACvD,MAAMS,MAAM,GAAGD,SAAS,CAACT,IAAI,CAACW,GAAG,CAAC,WAAW,CAAE,CAAC;IAChDC,MAAM,CAACF,MAAM,CAAC,CAACG,QAAQ,EAAE;EAC3B,CAAC,CAAC;EAEFf,QAAQ,CAAC,8BAA8B,EAAE,MAAK;IAC5CU,EAAE,CAAC,mGAAmG,EAAE,MAAK;MAC3G,MAAMC,SAAS,GAAGZ,6BAA6B,CAACI,MAAM,CAAC;MACvDD,IAAI,CAACW,GAAG,CAAC,WAAW,CAAC,EAAEG,QAAQ,CAAC,IAAIC,IAAI,CAAC,YAAY,CAAC,CAAC;MACvDf,IAAI,CAACW,GAAG,CAAC,kBAAkB,CAAC,EAAEG,QAAQ,CAAC,IAAIC,IAAI,CAAC,YAAY,CAAC,CAAC;MAC9D,MAAML,MAAM,GAAGD,SAAS,CAACT,IAAI,CAACW,GAAG,CAAC,kBAAkB,CAAE,CAAC;MACvDC,MAAM,CAACF,MAAM,CAAC,CAACM,OAAO,CAAC;QAAEC,kCAAkC,EAAE;MAAI,CAAE,CAAC;IACtE,CAAC,CAAC;IAEFT,EAAE,CAAC,gEAAgE,EAAE,MAAK;MACxE,MAAMC,SAAS,GAAGZ,6BAA6B,CAACI,MAAM,CAAC;MACvDD,IAAI,CAACW,GAAG,CAAC,WAAW,CAAC,EAAEG,QAAQ,CAAC,IAAIC,IAAI,CAAC,YAAY,CAAC,CAAC;MACvDf,IAAI,CAACW,GAAG,CAAC,kBAAkB,CAAC,EAAEG,QAAQ,CAAC,IAAIC,IAAI,CAAC,YAAY,CAAC,CAAC;MAC9D,MAAML,MAAM,GAAGD,SAAS,CAACT,IAAI,CAACW,GAAG,CAAC,kBAAkB,CAAE,CAAC;MACvDC,MAAM,CAACF,MAAM,CAAC,CAACG,QAAQ,EAAE;IAC3B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFf,QAAQ,CAAC,uBAAuB,EAAE,MAAK;IACrCU,EAAE,CAAC,mGAAmG,EAAE,MAAK;MAC3G,MAAMC,SAAS,GAAGZ,6BAA6B,CAACI,MAAM,CAAC;MACvDD,IAAI,CAACW,GAAG,CAAC,kBAAkB,CAAC,EAAEG,QAAQ,CAAC,IAAIC,IAAI,CAAC,YAAY,CAAC,CAAC;MAC9Df,IAAI,CAACW,GAAG,CAAC,WAAW,CAAC,EAAEG,QAAQ,CAAC,IAAIC,IAAI,CAAC,YAAY,CAAC,CAAC;MACvD,MAAML,MAAM,GAAGD,SAAS,CAACT,IAAI,CAACW,GAAG,CAAC,WAAW,CAAE,CAAC;MAChDC,MAAM,CAACF,MAAM,CAAC,CAACM,OAAO,CAAC;QAAEE,iCAAiC,EAAE;MAAI,CAAE,CAAC;IACrE,CAAC,CAAC;IAEFV,EAAE,CAAC,8EAA8E,EAAE,MAAK;MACtFP,MAAM,CAACK,YAAY,GAAG,IAAIS,IAAI,CAAC,YAAY,CAAC;MAC5C,MAAMN,SAAS,GAAGZ,6BAA6B,CAACI,MAAM,CAAC;MACvDD,IAAI,CAACW,GAAG,CAAC,WAAW,CAAC,EAAEG,QAAQ,CAAC,IAAIC,IAAI,CAAC,YAAY,CAAC,CAAC;MACvD,MAAML,MAAM,GAAGD,SAAS,CAACT,IAAI,CAACW,GAAG,CAAC,WAAW,CAAE,CAAC;MAChDC,MAAM,CAACF,MAAM,CAAC,CAACM,OAAO,CAAC;QAAEG,iBAAiB,EAAE;MAAI,CAAE,CAAC;IACrD,CAAC,CAAC;IAEFX,EAAE,CAAC,8FAA8F,EAAE,MAAK;MACtGP,MAAM,CAACM,qBAAqB,GAAG,IAAIQ,IAAI,CAAC,YAAY,CAAC;MACrD,MAAMN,SAAS,GAAGZ,6BAA6B,CAACI,MAAM,CAAC;MACvDD,IAAI,CAACW,GAAG,CAAC,WAAW,CAAC,EAAEG,QAAQ,CAAC,IAAIC,IAAI,CAAC,YAAY,CAAC,CAAC;MACvD,MAAML,MAAM,GAAGD,SAAS,CAACT,IAAI,CAACW,GAAG,CAAC,WAAW,CAAE,CAAC;MAChDC,MAAM,CAACF,MAAM,CAAC,CAACM,OAAO,CAAC;QAAEI,yBAAyB,EAAE;MAAI,CAAE,CAAC;IAC7D,CAAC,CAAC;IAEFZ,EAAE,CAAC,6CAA6C,EAAE,MAAK;MACrDP,MAAM,CAACK,YAAY,GAAG,IAAIS,IAAI,CAAC,YAAY,CAAC;MAC5Cd,MAAM,CAACM,qBAAqB,GAAG,IAAIQ,IAAI,CAAC,YAAY,CAAC;MACrD,MAAMN,SAAS,GAAGZ,6BAA6B,CAACI,MAAM,CAAC;MACvDD,IAAI,CAACW,GAAG,CAAC,WAAW,CAAC,EAAEG,QAAQ,CAAC,IAAIC,IAAI,CAAC,YAAY,CAAC,CAAC;MACvDf,IAAI,CAACW,GAAG,CAAC,kBAAkB,CAAC,EAAEG,QAAQ,CAAC,IAAIC,IAAI,CAAC,YAAY,CAAC,CAAC;MAC9D,MAAML,MAAM,GAAGD,SAAS,CAACT,IAAI,CAACW,GAAG,CAAC,WAAW,CAAE,CAAC;MAChDC,MAAM,CAACF,MAAM,CAAC,CAACG,QAAQ,EAAE;IAC3B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFf,QAAQ,CAAC,YAAY,EAAE,MAAK;IAC1BU,EAAE,CAAC,sCAAsC,EAAE,MAAK;MAC9C,MAAMC,SAAS,GAAGZ,6BAA6B,CAACI,MAAM,CAAC;MACvD,MAAMoB,WAAW,GAAG,IAAIzB,WAAW,CAAC,IAAI,CAAC;MACzC,MAAMc,MAAM,GAAGD,SAAS,CAACY,WAAW,CAAC;MACrCT,MAAM,CAACF,MAAM,CAAC,CAACG,QAAQ,EAAE;IAC3B,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}