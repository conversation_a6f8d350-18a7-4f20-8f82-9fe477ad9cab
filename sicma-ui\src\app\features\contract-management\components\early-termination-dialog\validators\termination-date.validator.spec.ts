import { FormControl } from '@angular/forms';
import { createTerminationDateValidator } from './termination-date.validator';

describe('TerminationDateValidator', () => {
  let control: FormControl;

  beforeEach(() => {
    control = new FormControl(null);
  });

  it('should return null when value is null', () => {
    const validator = createTerminationDateValidator(new Date());
    const result = validator(control);
    expect(result).toBeNull();
  });

  it('should return dateAfterContractEnd error when date is after contract end date', () => {
    const contractEndDate = new Date('2024-12-31');
    const validator = createTerminationDateValidator(contractEndDate);
    control.setValue(new Date('2025-01-02'));
    const result = validator(control);
    expect(result).toEqual({ dateAfterContractEnd: true });
  });

  it('should return null when date is within allowed range', () => {
    const contractEndDate = new Date('2024-12-31');
    const validator = createTerminationDateValidator(contractEndDate);
    control.setValue(new Date('2024-12-31'));
    const result = validator(control);
    expect(result).toBeNull();
  });

  it('should return null when date is before contract end date', () => {
    const contractEndDate = new Date('2024-12-31');
    const validator = createTerminationDateValidator(contractEndDate);
    control.setValue(new Date('2024-12-30'));
    const result = validator(control);
    expect(result).toBeNull();
  });

  it('should return null when latestContractEndDate is null', () => {
    const validator = createTerminationDateValidator(null);
    control.setValue(new Date('2024-12-31'));
    const result = validator(control);
    expect(result).toBeNull();
  });
});