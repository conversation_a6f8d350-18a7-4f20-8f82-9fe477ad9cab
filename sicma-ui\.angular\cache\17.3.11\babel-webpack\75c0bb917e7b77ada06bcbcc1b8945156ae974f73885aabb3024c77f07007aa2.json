{"ast": null, "code": "function cov_2lr8rlrnlf() {\n  var path = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\profile-management\\\\profile-management.component.ts\";\n  var hash = \"296783b7a74d8f310609a759eaf12b7e7bdbb729\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\profile-management\\\\profile-management.component.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 24,\n          column: 33\n        },\n        end: {\n          line: 128,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 26,\n          column: 8\n        },\n        end: {\n          line: 26,\n          column: 45\n        }\n      },\n      \"2\": {\n        start: {\n          line: 27,\n          column: 8\n        },\n        end: {\n          line: 27,\n          column: 27\n        }\n      },\n      \"3\": {\n        start: {\n          line: 28,\n          column: 8\n        },\n        end: {\n          line: 28,\n          column: 31\n        }\n      },\n      \"4\": {\n        start: {\n          line: 29,\n          column: 8\n        },\n        end: {\n          line: 29,\n          column: 29\n        }\n      },\n      \"5\": {\n        start: {\n          line: 30,\n          column: 8\n        },\n        end: {\n          line: 30,\n          column: 68\n        }\n      },\n      \"6\": {\n        start: {\n          line: 31,\n          column: 8\n        },\n        end: {\n          line: 31,\n          column: 51\n        }\n      },\n      \"7\": {\n        start: {\n          line: 32,\n          column: 8\n        },\n        end: {\n          line: 32,\n          column: 30\n        }\n      },\n      \"8\": {\n        start: {\n          line: 35,\n          column: 8\n        },\n        end: {\n          line: 35,\n          column: 24\n        }\n      },\n      \"9\": {\n        start: {\n          line: 38,\n          column: 8\n        },\n        end: {\n          line: 38,\n          column: 51\n        }\n      },\n      \"10\": {\n        start: {\n          line: 39,\n          column: 8\n        },\n        end: {\n          line: 39,\n          column: 41\n        }\n      },\n      \"11\": {\n        start: {\n          line: 42,\n          column: 28\n        },\n        end: {\n          line: 42,\n          column: 46\n        }\n      },\n      \"12\": {\n        start: {\n          line: 43,\n          column: 8\n        },\n        end: {\n          line: 43,\n          column: 66\n        }\n      },\n      \"13\": {\n        start: {\n          line: 44,\n          column: 8\n        },\n        end: {\n          line: 44,\n          column: 47\n        }\n      },\n      \"14\": {\n        start: {\n          line: 47,\n          column: 8\n        },\n        end: {\n          line: 47,\n          column: 28\n        }\n      },\n      \"15\": {\n        start: {\n          line: 48,\n          column: 8\n        },\n        end: {\n          line: 61,\n          column: 11\n        }\n      },\n      \"16\": {\n        start: {\n          line: 52,\n          column: 33\n        },\n        end: {\n          line: 52,\n          column: 52\n        }\n      },\n      \"17\": {\n        start: {\n          line: 55,\n          column: 16\n        },\n        end: {\n          line: 55,\n          column: 44\n        }\n      },\n      \"18\": {\n        start: {\n          line: 56,\n          column: 16\n        },\n        end: {\n          line: 56,\n          column: 45\n        }\n      },\n      \"19\": {\n        start: {\n          line: 59,\n          column: 16\n        },\n        end: {\n          line: 59,\n          column: 85\n        }\n      },\n      \"20\": {\n        start: {\n          line: 64,\n          column: 8\n        },\n        end: {\n          line: 64,\n          column: 28\n        }\n      },\n      \"21\": {\n        start: {\n          line: 65,\n          column: 8\n        },\n        end: {\n          line: 76,\n          column: 11\n        }\n      },\n      \"22\": {\n        start: {\n          line: 67,\n          column: 33\n        },\n        end: {\n          line: 67,\n          column: 52\n        }\n      },\n      \"23\": {\n        start: {\n          line: 70,\n          column: 16\n        },\n        end: {\n          line: 70,\n          column: 68\n        }\n      },\n      \"24\": {\n        start: {\n          line: 71,\n          column: 16\n        },\n        end: {\n          line: 71,\n          column: 32\n        }\n      },\n      \"25\": {\n        start: {\n          line: 74,\n          column: 16\n        },\n        end: {\n          line: 74,\n          column: 86\n        }\n      },\n      \"26\": {\n        start: {\n          line: 79,\n          column: 21\n        },\n        end: {\n          line: 79,\n          column: 74\n        }\n      },\n      \"27\": {\n        start: {\n          line: 79,\n          column: 54\n        },\n        end: {\n          line: 79,\n          column: 73\n        }\n      },\n      \"28\": {\n        start: {\n          line: 80,\n          column: 8\n        },\n        end: {\n          line: 81,\n          column: 19\n        }\n      },\n      \"29\": {\n        start: {\n          line: 81,\n          column: 12\n        },\n        end: {\n          line: 81,\n          column: 19\n        }\n      },\n      \"30\": {\n        start: {\n          line: 82,\n          column: 8\n        },\n        end: {\n          line: 85,\n          column: 9\n        }\n      },\n      \"31\": {\n        start: {\n          line: 83,\n          column: 12\n        },\n        end: {\n          line: 83,\n          column: 75\n        }\n      },\n      \"32\": {\n        start: {\n          line: 84,\n          column: 12\n        },\n        end: {\n          line: 84,\n          column: 19\n        }\n      },\n      \"33\": {\n        start: {\n          line: 86,\n          column: 8\n        },\n        end: {\n          line: 86,\n          column: 28\n        }\n      },\n      \"34\": {\n        start: {\n          line: 87,\n          column: 8\n        },\n        end: {\n          line: 98,\n          column: 11\n        }\n      },\n      \"35\": {\n        start: {\n          line: 89,\n          column: 33\n        },\n        end: {\n          line: 89,\n          column: 52\n        }\n      },\n      \"36\": {\n        start: {\n          line: 92,\n          column: 16\n        },\n        end: {\n          line: 92,\n          column: 68\n        }\n      },\n      \"37\": {\n        start: {\n          line: 93,\n          column: 16\n        },\n        end: {\n          line: 93,\n          column: 32\n        }\n      },\n      \"38\": {\n        start: {\n          line: 96,\n          column: 16\n        },\n        end: {\n          line: 96,\n          column: 86\n        }\n      },\n      \"39\": {\n        start: {\n          line: 101,\n          column: 8\n        },\n        end: {\n          line: 101,\n          column: 101\n        }\n      },\n      \"40\": {\n        start: {\n          line: 101,\n          column: 52\n        },\n        end: {\n          line: 101,\n          column: 99\n        }\n      },\n      \"41\": {\n        start: {\n          line: 101,\n          column: 79\n        },\n        end: {\n          line: 101,\n          column: 98\n        }\n      },\n      \"42\": {\n        start: {\n          line: 104,\n          column: 8\n        },\n        end: {\n          line: 116,\n          column: 11\n        }\n      },\n      \"43\": {\n        start: {\n          line: 113,\n          column: 12\n        },\n        end: {\n          line: 114,\n          column: 23\n        }\n      },\n      \"44\": {\n        start: {\n          line: 114,\n          column: 16\n        },\n        end: {\n          line: 114,\n          column: 23\n        }\n      },\n      \"45\": {\n        start: {\n          line: 115,\n          column: 12\n        },\n        end: {\n          line: 115,\n          column: 28\n        }\n      },\n      \"46\": {\n        start: {\n          line: 118,\n          column: 13\n        },\n        end: {\n          line: 123,\n          column: 6\n        }\n      },\n      \"47\": {\n        start: {\n          line: 118,\n          column: 41\n        },\n        end: {\n          line: 123,\n          column: 5\n        }\n      },\n      \"48\": {\n        start: {\n          line: 124,\n          column: 13\n        },\n        end: {\n          line: 127,\n          column: 6\n        }\n      },\n      \"49\": {\n        start: {\n          line: 129,\n          column: 0\n        },\n        end: {\n          line: 150,\n          column: 31\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 25,\n            column: 4\n          },\n          end: {\n            line: 25,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 25,\n            column: 56\n          },\n          end: {\n            line: 33,\n            column: 5\n          }\n        },\n        line: 25\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 34,\n            column: 4\n          },\n          end: {\n            line: 34,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 34,\n            column: 15\n          },\n          end: {\n            line: 36,\n            column: 5\n          }\n        },\n        line: 34\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 37,\n            column: 4\n          },\n          end: {\n            line: 37,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 37,\n            column: 22\n          },\n          end: {\n            line: 40,\n            column: 5\n          }\n        },\n        line: 37\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 41,\n            column: 4\n          },\n          end: {\n            line: 41,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 41,\n            column: 23\n          },\n          end: {\n            line: 45,\n            column: 5\n          }\n        },\n        line: 41\n      },\n      \"4\": {\n        name: \"(anonymous_4)\",\n        decl: {\n          start: {\n            line: 46,\n            column: 4\n          },\n          end: {\n            line: 46,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 46,\n            column: 15\n          },\n          end: {\n            line: 62,\n            column: 5\n          }\n        },\n        line: 46\n      },\n      \"5\": {\n        name: \"(anonymous_5)\",\n        decl: {\n          start: {\n            line: 52,\n            column: 27\n          },\n          end: {\n            line: 52,\n            column: 28\n          }\n        },\n        loc: {\n          start: {\n            line: 52,\n            column: 33\n          },\n          end: {\n            line: 52,\n            column: 52\n          }\n        },\n        line: 52\n      },\n      \"6\": {\n        name: \"(anonymous_6)\",\n        decl: {\n          start: {\n            line: 54,\n            column: 18\n          },\n          end: {\n            line: 54,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 54,\n            column: 43\n          },\n          end: {\n            line: 57,\n            column: 13\n          }\n        },\n        line: 54\n      },\n      \"7\": {\n        name: \"(anonymous_7)\",\n        decl: {\n          start: {\n            line: 58,\n            column: 19\n          },\n          end: {\n            line: 58,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 58,\n            column: 30\n          },\n          end: {\n            line: 60,\n            column: 13\n          }\n        },\n        line: 58\n      },\n      \"8\": {\n        name: \"(anonymous_8)\",\n        decl: {\n          start: {\n            line: 63,\n            column: 4\n          },\n          end: {\n            line: 63,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 63,\n            column: 37\n          },\n          end: {\n            line: 77,\n            column: 5\n          }\n        },\n        line: 63\n      },\n      \"9\": {\n        name: \"(anonymous_9)\",\n        decl: {\n          start: {\n            line: 67,\n            column: 27\n          },\n          end: {\n            line: 67,\n            column: 28\n          }\n        },\n        loc: {\n          start: {\n            line: 67,\n            column: 33\n          },\n          end: {\n            line: 67,\n            column: 52\n          }\n        },\n        line: 67\n      },\n      \"10\": {\n        name: \"(anonymous_10)\",\n        decl: {\n          start: {\n            line: 69,\n            column: 18\n          },\n          end: {\n            line: 69,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 69,\n            column: 24\n          },\n          end: {\n            line: 72,\n            column: 13\n          }\n        },\n        line: 69\n      },\n      \"11\": {\n        name: \"(anonymous_11)\",\n        decl: {\n          start: {\n            line: 73,\n            column: 19\n          },\n          end: {\n            line: 73,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 73,\n            column: 30\n          },\n          end: {\n            line: 75,\n            column: 13\n          }\n        },\n        line: 73\n      },\n      \"12\": {\n        name: \"(anonymous_12)\",\n        decl: {\n          start: {\n            line: 78,\n            column: 4\n          },\n          end: {\n            line: 78,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 78,\n            column: 37\n          },\n          end: {\n            line: 99,\n            column: 5\n          }\n        },\n        line: 78\n      },\n      \"13\": {\n        name: \"(anonymous_13)\",\n        decl: {\n          start: {\n            line: 79,\n            column: 47\n          },\n          end: {\n            line: 79,\n            column: 48\n          }\n        },\n        loc: {\n          start: {\n            line: 79,\n            column: 54\n          },\n          end: {\n            line: 79,\n            column: 73\n          }\n        },\n        line: 79\n      },\n      \"14\": {\n        name: \"(anonymous_14)\",\n        decl: {\n          start: {\n            line: 89,\n            column: 27\n          },\n          end: {\n            line: 89,\n            column: 28\n          }\n        },\n        loc: {\n          start: {\n            line: 89,\n            column: 33\n          },\n          end: {\n            line: 89,\n            column: 52\n          }\n        },\n        line: 89\n      },\n      \"15\": {\n        name: \"(anonymous_15)\",\n        decl: {\n          start: {\n            line: 91,\n            column: 18\n          },\n          end: {\n            line: 91,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 91,\n            column: 24\n          },\n          end: {\n            line: 94,\n            column: 13\n          }\n        },\n        line: 91\n      },\n      \"16\": {\n        name: \"(anonymous_16)\",\n        decl: {\n          start: {\n            line: 95,\n            column: 19\n          },\n          end: {\n            line: 95,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 95,\n            column: 30\n          },\n          end: {\n            line: 97,\n            column: 13\n          }\n        },\n        line: 95\n      },\n      \"17\": {\n        name: \"(anonymous_17)\",\n        decl: {\n          start: {\n            line: 100,\n            column: 4\n          },\n          end: {\n            line: 100,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 100,\n            column: 31\n          },\n          end: {\n            line: 102,\n            column: 5\n          }\n        },\n        line: 100\n      },\n      \"18\": {\n        name: \"(anonymous_18)\",\n        decl: {\n          start: {\n            line: 101,\n            column: 39\n          },\n          end: {\n            line: 101,\n            column: 40\n          }\n        },\n        loc: {\n          start: {\n            line: 101,\n            column: 52\n          },\n          end: {\n            line: 101,\n            column: 99\n          }\n        },\n        line: 101\n      },\n      \"19\": {\n        name: \"(anonymous_19)\",\n        decl: {\n          start: {\n            line: 101,\n            column: 72\n          },\n          end: {\n            line: 101,\n            column: 73\n          }\n        },\n        loc: {\n          start: {\n            line: 101,\n            column: 79\n          },\n          end: {\n            line: 101,\n            column: 98\n          }\n        },\n        line: 101\n      },\n      \"20\": {\n        name: \"(anonymous_20)\",\n        decl: {\n          start: {\n            line: 103,\n            column: 4\n          },\n          end: {\n            line: 103,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 103,\n            column: 21\n          },\n          end: {\n            line: 117,\n            column: 5\n          }\n        },\n        line: 103\n      },\n      \"21\": {\n        name: \"(anonymous_21)\",\n        decl: {\n          start: {\n            line: 112,\n            column: 23\n          },\n          end: {\n            line: 112,\n            column: 24\n          }\n        },\n        loc: {\n          start: {\n            line: 112,\n            column: 35\n          },\n          end: {\n            line: 116,\n            column: 9\n          }\n        },\n        line: 112\n      },\n      \"22\": {\n        name: \"(anonymous_22)\",\n        decl: {\n          start: {\n            line: 118,\n            column: 35\n          },\n          end: {\n            line: 118,\n            column: 36\n          }\n        },\n        loc: {\n          start: {\n            line: 118,\n            column: 41\n          },\n          end: {\n            line: 123,\n            column: 5\n          }\n        },\n        line: 118\n      }\n    },\n    branchMap: {\n      \"0\": {\n        loc: {\n          start: {\n            line: 59,\n            column: 33\n          },\n          end: {\n            line: 59,\n            column: 83\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 59,\n            column: 33\n          },\n          end: {\n            line: 59,\n            column: 52\n          }\n        }, {\n          start: {\n            line: 59,\n            column: 56\n          },\n          end: {\n            line: 59,\n            column: 83\n          }\n        }],\n        line: 59\n      },\n      \"1\": {\n        loc: {\n          start: {\n            line: 74,\n            column: 33\n          },\n          end: {\n            line: 74,\n            column: 84\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 74,\n            column: 33\n          },\n          end: {\n            line: 74,\n            column: 52\n          }\n        }, {\n          start: {\n            line: 74,\n            column: 56\n          },\n          end: {\n            line: 74,\n            column: 84\n          }\n        }],\n        line: 74\n      },\n      \"2\": {\n        loc: {\n          start: {\n            line: 80,\n            column: 8\n          },\n          end: {\n            line: 81,\n            column: 19\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 80,\n            column: 8\n          },\n          end: {\n            line: 81,\n            column: 19\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 80\n      },\n      \"3\": {\n        loc: {\n          start: {\n            line: 82,\n            column: 8\n          },\n          end: {\n            line: 85,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 82,\n            column: 8\n          },\n          end: {\n            line: 85,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 82\n      },\n      \"4\": {\n        loc: {\n          start: {\n            line: 96,\n            column: 33\n          },\n          end: {\n            line: 96,\n            column: 84\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 96,\n            column: 33\n          },\n          end: {\n            line: 96,\n            column: 52\n          }\n        }, {\n          start: {\n            line: 96,\n            column: 56\n          },\n          end: {\n            line: 96,\n            column: 84\n          }\n        }],\n        line: 96\n      },\n      \"5\": {\n        loc: {\n          start: {\n            line: 113,\n            column: 12\n          },\n          end: {\n            line: 114,\n            column: 23\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 113,\n            column: 12\n          },\n          end: {\n            line: 114,\n            column: 23\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 113\n      }\n    },\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0,\n      \"36\": 0,\n      \"37\": 0,\n      \"38\": 0,\n      \"39\": 0,\n      \"40\": 0,\n      \"41\": 0,\n      \"42\": 0,\n      \"43\": 0,\n      \"44\": 0,\n      \"45\": 0,\n      \"46\": 0,\n      \"47\": 0,\n      \"48\": 0,\n      \"49\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0\n    },\n    b: {\n      \"0\": [0, 0],\n      \"1\": [0, 0],\n      \"2\": [0, 0],\n      \"3\": [0, 0],\n      \"4\": [0, 0],\n      \"5\": [0, 0]\n    },\n    inputSourceMap: {\n      version: 3,\n      file: \"profile-management.component.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\profile-management\\\\profile-management.component.ts\"],\n      names: [],\n      mappings: \";;;AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAC/C,OAAO,EAAiB,SAAS,EAAU,SAAS,EAAE,MAAM,eAAe,CAAC;AAC5E,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAC7C,OAAO,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAC3D,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACzD,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AACrD,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACzD,OAAO,EAAE,YAAY,EAAE,kBAAkB,EAAE,MAAM,6BAA6B,CAAC;AAC/E,OAAO,EAAE,wBAAwB,EAAE,MAAM,oCAAoC,CAAC;AAC9E,OAAO,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAC3D,OAAO,EAAE,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AAChE,OAAO,EAAE,kBAAkB,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AAC7E,OAAO,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AAChD,OAAO,EAAE,QAAQ,EAAE,MAAM,MAAM,CAAC;AAChC,OAAO,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAE1C,OAAO,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAC9D,OAAO,EAAE,mBAAmB,EAAE,MAAM,gDAAgD,CAAC;AAErF,OAAO,EAAE,cAAc,EAAE,MAAM,4BAA4B,CAAC;AAsBrD,IAAM,0BAA0B,GAAhC,MAAM,0BAA0B;IAQrC,YACU,cAA8B,EAC9B,KAAmB,EACnB,OAA0B,EAC1B,MAAiB;QAHjB,mBAAc,GAAd,cAAc,CAAgB;QAC9B,UAAK,GAAL,KAAK,CAAc;QACnB,YAAO,GAAP,OAAO,CAAmB;QAC1B,WAAM,GAAN,MAAM,CAAW;QAX3B,qBAAgB,GAAa,CAAC,UAAU,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;QACjE,eAAU,GAAG,IAAI,kBAAkB,EAAyB,CAAC;QAC7D,gBAAW,GAAc,EAAE,CAAC;IAUzB,CAAC;IAEJ,QAAQ;QACN,IAAI,CAAC,QAAQ,EAAE,CAAC;IAClB,CAAC;IAED,eAAe;QACb,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC3C,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;IACnC,CAAC;IAED,WAAW,CAAC,KAAY;QACtB,MAAM,WAAW,GAAI,KAAK,CAAC,MAA2B,CAAC,KAAK,CAAC;QAC7D,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAC1D,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,SAAS,EAAE,CAAC;IACzC,CAAC;IAEO,QAAQ;QACd,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACpB,QAAQ,CAAC;YACP,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE;YAC9C,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,oBAAoB,EAAE;SAClD,CAAC;aACC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;aACzC,SAAS,CAAC;YACT,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,EAAE;gBAC5B,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;gBAC5B,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,KAAK,CAAC;YAC/B,CAAC;YACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,2BAA2B,CAAC,CAAC;YACvE,CAAC;SACF,CAAC,CAAC;IACP,CAAC;IAED,aAAa,CAAC,MAAc,EAAE,SAAiB;QAC7C,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACpB,IAAI,CAAC,cAAc;aAChB,aAAa,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC;aACzD,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;aACzC,SAAS,CAAC;YACT,IAAI,EAAE,GAAG,EAAE;gBACT,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC;gBACpD,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,CAAC;YACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,4BAA4B,CAAC,CAAC;YACxE,CAAC;SACF,CAAC,CAAC;IACP,CAAC;IAED,aAAa,CAAC,MAAc,EAAE,SAAiB;QAC7C,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;QACnE,IAAI,CAAC,IAAI;YAAE,OAAO;QAElB,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,0CAA0C,CAAC,CAAC;YAC/D,OAAO;QACT,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACpB,IAAI,CAAC,cAAc;aAChB,aAAa,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC;aACzD,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;aACzC,SAAS,CAAC;YACT,IAAI,EAAE,GAAG,EAAE;gBACT,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC;gBACpD,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,CAAC;YACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,4BAA4B,CAAC,CAAC;YACxE,CAAC;SACF,CAAC,CAAC;IACP,CAAC;IAED,oBAAoB,CAAC,IAA2B;QAC9C,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAC5B,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE,CAAC,CAC7D,CAAC;IACJ,CAAC;IAED,cAAc;QACZ,IAAI,CAAC,MAAM;aACR,IAAI,CAAC,mBAAmB,EAAE;YACzB,KAAK,EAAE,KAAK;YACZ,QAAQ,EAAE,OAAO;YACjB,MAAM,EAAE,MAAM;YACd,SAAS,EAAE,MAAM;SAClB,CAAC;aACD,WAAW,EAAE;aACb,SAAS,CAAC,CAAC,MAA8B,EAAE,EAAE;YAC5C,IAAI,CAAC,MAAM;gBAAE,OAAO;YACpB,IAAI,CAAC,QAAQ,EAAE,CAAC;QAClB,CAAC,CAAC,CAAC;IACP,CAAC;;;;;;;;4BAtGA,SAAS,SAAC,YAAY;uBACtB,SAAS,SAAC,OAAO;;;AANP,0BAA0B;IApBtC,SAAS,CAAC;QACT,QAAQ,EAAE,wBAAwB;QAClC,8BAAkD;QAElD,UAAU,EAAE,IAAI;QAChB,OAAO,EAAE;YACP,YAAY;YACZ,cAAc;YACd,cAAc;YACd,eAAe;YACf,aAAa;YACb,aAAa;YACb,wBAAwB;YACxB,eAAe;YACf,cAAc;YACd,kBAAkB;YAClB,aAAa;YACb,WAAW;SACZ;;KACF,CAAC;GACW,0BAA0B,CA4GtC\",\n      sourcesContent: [\"import { CommonModule } from '@angular/common';\\nimport { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';\\nimport { FormsModule } from '@angular/forms';\\nimport { MatButtonModule } from '@angular/material/button';\\nimport { MatCardModule } from '@angular/material/card';\\nimport { MatChipsModule } from '@angular/material/chips';\\nimport { MatDialog } from '@angular/material/dialog';\\nimport { MatIconModule } from '@angular/material/icon';\\nimport { MatInputModule } from '@angular/material/input';\\nimport { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';\\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\\nimport { MatSelectModule } from '@angular/material/select';\\nimport { MatSort, MatSortModule } from '@angular/material/sort';\\nimport { MatTableDataSource, MatTableModule } from '@angular/material/table';\\nimport { NgxSpinnerService } from 'ngx-spinner';\\nimport { forkJoin } from 'rxjs';\\nimport { finalize } from 'rxjs/operators';\\n\\nimport { AlertService } from '@shared/services/alert.service';\\nimport { UserDialogComponent } from './components/user-dialog/user-dialog.component';\\nimport { Profile, UserProfileAssignment } from './models/profile.model';\\nimport { ProfileService } from './services/profile.service';\\n\\n@Component({\\n  selector: 'app-profile-management',\\n  templateUrl: './profile-management.component.html',\\n  styleUrl: './profile-management.component.scss',\\n  standalone: true,\\n  imports: [\\n    CommonModule,\\n    MatTableModule,\\n    MatChipsModule,\\n    MatButtonModule,\\n    MatIconModule,\\n    MatCardModule,\\n    MatProgressSpinnerModule,\\n    MatSelectModule,\\n    MatInputModule,\\n    MatPaginatorModule,\\n    MatSortModule,\\n    FormsModule,\\n  ],\\n})\\nexport class ProfileManagementComponent implements OnInit, AfterViewInit {\\n  displayedColumns: string[] = ['username', 'profiles', 'actions'];\\n  dataSource = new MatTableDataSource<UserProfileAssignment>();\\n  allProfiles: Profile[] = [];\\n\\n  @ViewChild(MatPaginator) paginator!: MatPaginator;\\n  @ViewChild(MatSort) sort!: MatSort;\\n\\n  constructor(\\n    private profileService: ProfileService,\\n    private alert: AlertService,\\n    private spinner: NgxSpinnerService,\\n    private dialog: MatDialog,\\n  ) {}\\n\\n  ngOnInit(): void {\\n    this.loadData();\\n  }\\n\\n  ngAfterViewInit(): void {\\n    this.dataSource.paginator = this.paginator;\\n    this.dataSource.sort = this.sort;\\n  }\\n\\n  applyFilter(event: Event): void {\\n    const filterValue = (event.target as HTMLInputElement).value;\\n    this.dataSource.filter = filterValue.trim().toLowerCase();\\n    this.dataSource.paginator?.firstPage();\\n  }\\n\\n  private loadData(): void {\\n    this.spinner.show();\\n    forkJoin({\\n      profiles: this.profileService.getAllProfiles(),\\n      users: this.profileService.getUsersWithProfiles(),\\n    })\\n      .pipe(finalize(() => this.spinner.hide()))\\n      .subscribe({\\n        next: ({ profiles, users }) => {\\n          this.allProfiles = profiles;\\n          this.dataSource.data = users;\\n        },\\n        error: (error) => {\\n          this.alert.error(error.error?.detail ?? 'Error al cargar los datos');\\n        },\\n      });\\n  }\\n\\n  assignProfile(userId: number, profileId: number): void {\\n    this.spinner.show();\\n    this.profileService\\n      .assignProfile({ user_id: userId, profile_id: profileId })\\n      .pipe(finalize(() => this.spinner.hide()))\\n      .subscribe({\\n        next: () => {\\n          this.alert.success('Perfil asignado correctamente');\\n          this.loadData();\\n        },\\n        error: (error) => {\\n          this.alert.error(error.error?.detail ?? 'Error al asignar el perfil');\\n        },\\n      });\\n  }\\n\\n  removeProfile(userId: number, profileId: number): void {\\n    const user = this.dataSource.data.find((u) => u.userId === userId);\\n    if (!user) return;\\n\\n    if (user.profiles.length <= 1) {\\n      this.alert.warning('El usuario debe tener al menos un perfil');\\n      return;\\n    }\\n\\n    this.spinner.show();\\n    this.profileService\\n      .removeProfile({ user_id: userId, profile_id: profileId })\\n      .pipe(finalize(() => this.spinner.hide()))\\n      .subscribe({\\n        next: () => {\\n          this.alert.success('Perfil removido correctamente');\\n          this.loadData();\\n        },\\n        error: (error) => {\\n          this.alert.error(error.error?.detail ?? 'Error al remover el perfil');\\n        },\\n      });\\n  }\\n\\n  getAvailableProfiles(user: UserProfileAssignment): Profile[] {\\n    return this.allProfiles.filter(\\n      (profile) => !user.profiles.some((p) => p.id === profile.id),\\n    );\\n  }\\n\\n  openUserDialog(): void {\\n    this.dialog\\n      .open(UserDialogComponent, {\\n        width: '95%',\\n        maxWidth: '600px',\\n        height: 'auto',\\n        maxHeight: '90vh',\\n      })\\n      .afterClosed()\\n      .subscribe((result?: UserProfileAssignment) => {\\n        if (!result) return;\\n        this.loadData();\\n      });\\n  }\\n}\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"296783b7a74d8f310609a759eaf12b7e7bdbb729\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_2lr8rlrnlf = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_2lr8rlrnlf();\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./profile-management.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./profile-management.component.scss?ngResource\";\nimport { CommonModule } from '@angular/common';\nimport { Component, ViewChild } from '@angular/core';\nimport { FormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatDialog } from '@angular/material/dialog';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatSort, MatSortModule } from '@angular/material/sort';\nimport { MatTableDataSource, MatTableModule } from '@angular/material/table';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { forkJoin } from 'rxjs';\nimport { finalize } from 'rxjs/operators';\nimport { AlertService } from '@shared/services/alert.service';\nimport { UserDialogComponent } from './components/user-dialog/user-dialog.component';\nimport { ProfileService } from './services/profile.service';\ncov_2lr8rlrnlf().s[0]++;\nlet ProfileManagementComponent = class ProfileManagementComponent {\n  constructor(profileService, alert, spinner, dialog) {\n    cov_2lr8rlrnlf().f[0]++;\n    cov_2lr8rlrnlf().s[1]++;\n    this.profileService = profileService;\n    cov_2lr8rlrnlf().s[2]++;\n    this.alert = alert;\n    cov_2lr8rlrnlf().s[3]++;\n    this.spinner = spinner;\n    cov_2lr8rlrnlf().s[4]++;\n    this.dialog = dialog;\n    cov_2lr8rlrnlf().s[5]++;\n    this.displayedColumns = ['username', 'profiles', 'actions'];\n    cov_2lr8rlrnlf().s[6]++;\n    this.dataSource = new MatTableDataSource();\n    cov_2lr8rlrnlf().s[7]++;\n    this.allProfiles = [];\n  }\n  ngOnInit() {\n    cov_2lr8rlrnlf().f[1]++;\n    cov_2lr8rlrnlf().s[8]++;\n    this.loadData();\n  }\n  ngAfterViewInit() {\n    cov_2lr8rlrnlf().f[2]++;\n    cov_2lr8rlrnlf().s[9]++;\n    this.dataSource.paginator = this.paginator;\n    cov_2lr8rlrnlf().s[10]++;\n    this.dataSource.sort = this.sort;\n  }\n  applyFilter(event) {\n    cov_2lr8rlrnlf().f[3]++;\n    const filterValue = (cov_2lr8rlrnlf().s[11]++, event.target.value);\n    cov_2lr8rlrnlf().s[12]++;\n    this.dataSource.filter = filterValue.trim().toLowerCase();\n    cov_2lr8rlrnlf().s[13]++;\n    this.dataSource.paginator?.firstPage();\n  }\n  loadData() {\n    cov_2lr8rlrnlf().f[4]++;\n    cov_2lr8rlrnlf().s[14]++;\n    this.spinner.show();\n    cov_2lr8rlrnlf().s[15]++;\n    forkJoin({\n      profiles: this.profileService.getAllProfiles(),\n      users: this.profileService.getUsersWithProfiles()\n    }).pipe(finalize(() => {\n      cov_2lr8rlrnlf().f[5]++;\n      cov_2lr8rlrnlf().s[16]++;\n      return this.spinner.hide();\n    })).subscribe({\n      next: ({\n        profiles,\n        users\n      }) => {\n        cov_2lr8rlrnlf().f[6]++;\n        cov_2lr8rlrnlf().s[17]++;\n        this.allProfiles = profiles;\n        cov_2lr8rlrnlf().s[18]++;\n        this.dataSource.data = users;\n      },\n      error: error => {\n        cov_2lr8rlrnlf().f[7]++;\n        cov_2lr8rlrnlf().s[19]++;\n        this.alert.error((cov_2lr8rlrnlf().b[0][0]++, error.error?.detail) ?? (cov_2lr8rlrnlf().b[0][1]++, 'Error al cargar los datos'));\n      }\n    });\n  }\n  assignProfile(userId, profileId) {\n    cov_2lr8rlrnlf().f[8]++;\n    cov_2lr8rlrnlf().s[20]++;\n    this.spinner.show();\n    cov_2lr8rlrnlf().s[21]++;\n    this.profileService.assignProfile({\n      user_id: userId,\n      profile_id: profileId\n    }).pipe(finalize(() => {\n      cov_2lr8rlrnlf().f[9]++;\n      cov_2lr8rlrnlf().s[22]++;\n      return this.spinner.hide();\n    })).subscribe({\n      next: () => {\n        cov_2lr8rlrnlf().f[10]++;\n        cov_2lr8rlrnlf().s[23]++;\n        this.alert.success('Perfil asignado correctamente');\n        cov_2lr8rlrnlf().s[24]++;\n        this.loadData();\n      },\n      error: error => {\n        cov_2lr8rlrnlf().f[11]++;\n        cov_2lr8rlrnlf().s[25]++;\n        this.alert.error((cov_2lr8rlrnlf().b[1][0]++, error.error?.detail) ?? (cov_2lr8rlrnlf().b[1][1]++, 'Error al asignar el perfil'));\n      }\n    });\n  }\n  removeProfile(userId, profileId) {\n    cov_2lr8rlrnlf().f[12]++;\n    const user = (cov_2lr8rlrnlf().s[26]++, this.dataSource.data.find(u => {\n      cov_2lr8rlrnlf().f[13]++;\n      cov_2lr8rlrnlf().s[27]++;\n      return u.userId === userId;\n    }));\n    cov_2lr8rlrnlf().s[28]++;\n    if (!user) {\n      cov_2lr8rlrnlf().b[2][0]++;\n      cov_2lr8rlrnlf().s[29]++;\n      return;\n    } else {\n      cov_2lr8rlrnlf().b[2][1]++;\n    }\n    cov_2lr8rlrnlf().s[30]++;\n    if (user.profiles.length <= 1) {\n      cov_2lr8rlrnlf().b[3][0]++;\n      cov_2lr8rlrnlf().s[31]++;\n      this.alert.warning('El usuario debe tener al menos un perfil');\n      cov_2lr8rlrnlf().s[32]++;\n      return;\n    } else {\n      cov_2lr8rlrnlf().b[3][1]++;\n    }\n    cov_2lr8rlrnlf().s[33]++;\n    this.spinner.show();\n    cov_2lr8rlrnlf().s[34]++;\n    this.profileService.removeProfile({\n      user_id: userId,\n      profile_id: profileId\n    }).pipe(finalize(() => {\n      cov_2lr8rlrnlf().f[14]++;\n      cov_2lr8rlrnlf().s[35]++;\n      return this.spinner.hide();\n    })).subscribe({\n      next: () => {\n        cov_2lr8rlrnlf().f[15]++;\n        cov_2lr8rlrnlf().s[36]++;\n        this.alert.success('Perfil removido correctamente');\n        cov_2lr8rlrnlf().s[37]++;\n        this.loadData();\n      },\n      error: error => {\n        cov_2lr8rlrnlf().f[16]++;\n        cov_2lr8rlrnlf().s[38]++;\n        this.alert.error((cov_2lr8rlrnlf().b[4][0]++, error.error?.detail) ?? (cov_2lr8rlrnlf().b[4][1]++, 'Error al remover el perfil'));\n      }\n    });\n  }\n  getAvailableProfiles(user) {\n    cov_2lr8rlrnlf().f[17]++;\n    cov_2lr8rlrnlf().s[39]++;\n    return this.allProfiles.filter(profile => {\n      cov_2lr8rlrnlf().f[18]++;\n      cov_2lr8rlrnlf().s[40]++;\n      return !user.profiles.some(p => {\n        cov_2lr8rlrnlf().f[19]++;\n        cov_2lr8rlrnlf().s[41]++;\n        return p.id === profile.id;\n      });\n    });\n  }\n  openUserDialog() {\n    cov_2lr8rlrnlf().f[20]++;\n    cov_2lr8rlrnlf().s[42]++;\n    this.dialog.open(UserDialogComponent, {\n      width: '95%',\n      maxWidth: '600px',\n      height: 'auto',\n      maxHeight: '90vh'\n    }).afterClosed().subscribe(result => {\n      cov_2lr8rlrnlf().f[21]++;\n      cov_2lr8rlrnlf().s[43]++;\n      if (!result) {\n        cov_2lr8rlrnlf().b[5][0]++;\n        cov_2lr8rlrnlf().s[44]++;\n        return;\n      } else {\n        cov_2lr8rlrnlf().b[5][1]++;\n      }\n      cov_2lr8rlrnlf().s[45]++;\n      this.loadData();\n    });\n  }\n  static {\n    cov_2lr8rlrnlf().s[46]++;\n    this.ctorParameters = () => {\n      cov_2lr8rlrnlf().f[22]++;\n      cov_2lr8rlrnlf().s[47]++;\n      return [{\n        type: ProfileService\n      }, {\n        type: AlertService\n      }, {\n        type: NgxSpinnerService\n      }, {\n        type: MatDialog\n      }];\n    };\n  }\n  static {\n    cov_2lr8rlrnlf().s[48]++;\n    this.propDecorators = {\n      paginator: [{\n        type: ViewChild,\n        args: [MatPaginator]\n      }],\n      sort: [{\n        type: ViewChild,\n        args: [MatSort]\n      }]\n    };\n  }\n};\ncov_2lr8rlrnlf().s[49]++;\nProfileManagementComponent = __decorate([Component({\n  selector: 'app-profile-management',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: true,\n  imports: [CommonModule, MatTableModule, MatChipsModule, MatButtonModule, MatIconModule, MatCardModule, MatProgressSpinnerModule, MatSelectModule, MatInputModule, MatPaginatorModule, MatSortModule, FormsModule],\n  styles: [__NG_CLI_RESOURCE__1]\n})], ProfileManagementComponent);\nexport { ProfileManagementComponent };", "map": {"version": 3, "names": ["cov_2lr8rlrnlf", "actualCoverage", "CommonModule", "Component", "ViewChild", "FormsModule", "MatButtonModule", "MatCardModule", "MatChipsModule", "MatDialog", "MatIconModule", "MatInputModule", "MatPaginator", "MatPaginatorModule", "MatProgressSpinnerModule", "MatSelectModule", "MatSort", "MatSortModule", "MatTableDataSource", "MatTableModule", "NgxSpinnerService", "fork<PERSON><PERSON>n", "finalize", "AlertService", "UserDialogComponent", "ProfileService", "s", "ProfileManagementComponent", "constructor", "profileService", "alert", "spinner", "dialog", "f", "displayedColumns", "dataSource", "allProfiles", "ngOnInit", "loadData", "ngAfterViewInit", "paginator", "sort", "applyFilter", "event", "filterValue", "target", "value", "filter", "trim", "toLowerCase", "firstPage", "show", "profiles", "getAllProfiles", "users", "getUsersWithProfiles", "pipe", "hide", "subscribe", "next", "data", "error", "b", "detail", "assignProfile", "userId", "profileId", "user_id", "profile_id", "success", "removeProfile", "user", "find", "u", "length", "warning", "getAvailableProfiles", "profile", "some", "p", "id", "openUserDialog", "open", "width", "max<PERSON><PERSON><PERSON>", "height", "maxHeight", "afterClosed", "result", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\profile-management\\profile-management.component.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';\nimport { FormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatDialog } from '@angular/material/dialog';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatSort, MatSortModule } from '@angular/material/sort';\nimport { MatTableDataSource, MatTableModule } from '@angular/material/table';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { forkJoin } from 'rxjs';\nimport { finalize } from 'rxjs/operators';\n\nimport { AlertService } from '@shared/services/alert.service';\nimport { UserDialogComponent } from './components/user-dialog/user-dialog.component';\nimport { Profile, UserProfileAssignment } from './models/profile.model';\nimport { ProfileService } from './services/profile.service';\n\n@Component({\n  selector: 'app-profile-management',\n  templateUrl: './profile-management.component.html',\n  styleUrl: './profile-management.component.scss',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatTableModule,\n    MatChipsModule,\n    MatButtonModule,\n    MatIconModule,\n    MatCardModule,\n    MatProgressSpinnerModule,\n    MatSelectModule,\n    MatInputModule,\n    MatPaginatorModule,\n    MatSortModule,\n    FormsModule,\n  ],\n})\nexport class ProfileManagementComponent implements OnInit, AfterViewInit {\n  displayedColumns: string[] = ['username', 'profiles', 'actions'];\n  dataSource = new MatTableDataSource<UserProfileAssignment>();\n  allProfiles: Profile[] = [];\n\n  @ViewChild(MatPaginator) paginator!: MatPaginator;\n  @ViewChild(MatSort) sort!: MatSort;\n\n  constructor(\n    private profileService: ProfileService,\n    private alert: AlertService,\n    private spinner: NgxSpinnerService,\n    private dialog: MatDialog,\n  ) {}\n\n  ngOnInit(): void {\n    this.loadData();\n  }\n\n  ngAfterViewInit(): void {\n    this.dataSource.paginator = this.paginator;\n    this.dataSource.sort = this.sort;\n  }\n\n  applyFilter(event: Event): void {\n    const filterValue = (event.target as HTMLInputElement).value;\n    this.dataSource.filter = filterValue.trim().toLowerCase();\n    this.dataSource.paginator?.firstPage();\n  }\n\n  private loadData(): void {\n    this.spinner.show();\n    forkJoin({\n      profiles: this.profileService.getAllProfiles(),\n      users: this.profileService.getUsersWithProfiles(),\n    })\n      .pipe(finalize(() => this.spinner.hide()))\n      .subscribe({\n        next: ({ profiles, users }) => {\n          this.allProfiles = profiles;\n          this.dataSource.data = users;\n        },\n        error: (error) => {\n          this.alert.error(error.error?.detail ?? 'Error al cargar los datos');\n        },\n      });\n  }\n\n  assignProfile(userId: number, profileId: number): void {\n    this.spinner.show();\n    this.profileService\n      .assignProfile({ user_id: userId, profile_id: profileId })\n      .pipe(finalize(() => this.spinner.hide()))\n      .subscribe({\n        next: () => {\n          this.alert.success('Perfil asignado correctamente');\n          this.loadData();\n        },\n        error: (error) => {\n          this.alert.error(error.error?.detail ?? 'Error al asignar el perfil');\n        },\n      });\n  }\n\n  removeProfile(userId: number, profileId: number): void {\n    const user = this.dataSource.data.find((u) => u.userId === userId);\n    if (!user) return;\n\n    if (user.profiles.length <= 1) {\n      this.alert.warning('El usuario debe tener al menos un perfil');\n      return;\n    }\n\n    this.spinner.show();\n    this.profileService\n      .removeProfile({ user_id: userId, profile_id: profileId })\n      .pipe(finalize(() => this.spinner.hide()))\n      .subscribe({\n        next: () => {\n          this.alert.success('Perfil removido correctamente');\n          this.loadData();\n        },\n        error: (error) => {\n          this.alert.error(error.error?.detail ?? 'Error al remover el perfil');\n        },\n      });\n  }\n\n  getAvailableProfiles(user: UserProfileAssignment): Profile[] {\n    return this.allProfiles.filter(\n      (profile) => !user.profiles.some((p) => p.id === profile.id),\n    );\n  }\n\n  openUserDialog(): void {\n    this.dialog\n      .open(UserDialogComponent, {\n        width: '95%',\n        maxWidth: '600px',\n        height: 'auto',\n        maxHeight: '90vh',\n      })\n      .afterClosed()\n      .subscribe((result?: UserProfileAssignment) => {\n        if (!result) return;\n        this.loadData();\n      });\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAYS;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;AAZT,SAASE,YAAY,QAAQ,iBAAiB;AAC9C,SAAwBC,SAAS,EAAUC,SAAS,QAAQ,eAAe;AAC3E,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,6BAA6B;AAC9E,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,OAAO,EAAEC,aAAa,QAAQ,wBAAwB;AAC/D,SAASC,kBAAkB,EAAEC,cAAc,QAAQ,yBAAyB;AAC5E,SAASC,iBAAiB,QAAQ,aAAa;AAC/C,SAASC,QAAQ,QAAQ,MAAM;AAC/B,SAASC,QAAQ,QAAQ,gBAAgB;AAEzC,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,mBAAmB,QAAQ,gDAAgD;AAEpF,SAASC,cAAc,QAAQ,4BAA4B;AAACzB,cAAA,GAAA0B,CAAA;AAsBrD,IAAMC,0BAA0B,GAAhC,MAAMA,0BAA0B;EAQrCC,YACUC,cAA8B,EAC9BC,KAAmB,EACnBC,OAA0B,EAC1BC,MAAiB;IAAAhC,cAAA,GAAAiC,CAAA;IAAAjC,cAAA,GAAA0B,CAAA;IAHjB,KAAAG,cAAc,GAAdA,cAAc;IAAgB7B,cAAA,GAAA0B,CAAA;IAC9B,KAAAI,KAAK,GAALA,KAAK;IAAc9B,cAAA,GAAA0B,CAAA;IACnB,KAAAK,OAAO,GAAPA,OAAO;IAAmB/B,cAAA,GAAA0B,CAAA;IAC1B,KAAAM,MAAM,GAANA,MAAM;IAAWhC,cAAA,GAAA0B,CAAA;IAX3B,KAAAQ,gBAAgB,GAAa,CAAC,UAAU,EAAE,UAAU,EAAE,SAAS,CAAC;IAAClC,cAAA,GAAA0B,CAAA;IACjE,KAAAS,UAAU,GAAG,IAAIjB,kBAAkB,EAAyB;IAAClB,cAAA,GAAA0B,CAAA;IAC7D,KAAAU,WAAW,GAAc,EAAE;EAUxB;EAEHC,QAAQA,CAAA;IAAArC,cAAA,GAAAiC,CAAA;IAAAjC,cAAA,GAAA0B,CAAA;IACN,IAAI,CAACY,QAAQ,EAAE;EACjB;EAEAC,eAAeA,CAAA;IAAAvC,cAAA,GAAAiC,CAAA;IAAAjC,cAAA,GAAA0B,CAAA;IACb,IAAI,CAACS,UAAU,CAACK,SAAS,GAAG,IAAI,CAACA,SAAS;IAACxC,cAAA,GAAA0B,CAAA;IAC3C,IAAI,CAACS,UAAU,CAACM,IAAI,GAAG,IAAI,CAACA,IAAI;EAClC;EAEAC,WAAWA,CAACC,KAAY;IAAA3C,cAAA,GAAAiC,CAAA;IACtB,MAAMW,WAAW,IAAA5C,cAAA,GAAA0B,CAAA,QAAIiB,KAAK,CAACE,MAA2B,CAACC,KAAK;IAAC9C,cAAA,GAAA0B,CAAA;IAC7D,IAAI,CAACS,UAAU,CAACY,MAAM,GAAGH,WAAW,CAACI,IAAI,EAAE,CAACC,WAAW,EAAE;IAACjD,cAAA,GAAA0B,CAAA;IAC1D,IAAI,CAACS,UAAU,CAACK,SAAS,EAAEU,SAAS,EAAE;EACxC;EAEQZ,QAAQA,CAAA;IAAAtC,cAAA,GAAAiC,CAAA;IAAAjC,cAAA,GAAA0B,CAAA;IACd,IAAI,CAACK,OAAO,CAACoB,IAAI,EAAE;IAACnD,cAAA,GAAA0B,CAAA;IACpBL,QAAQ,CAAC;MACP+B,QAAQ,EAAE,IAAI,CAACvB,cAAc,CAACwB,cAAc,EAAE;MAC9CC,KAAK,EAAE,IAAI,CAACzB,cAAc,CAAC0B,oBAAoB;KAChD,CAAC,CACCC,IAAI,CAAClC,QAAQ,CAAC,MAAM;MAAAtB,cAAA,GAAAiC,CAAA;MAAAjC,cAAA,GAAA0B,CAAA;MAAA,WAAI,CAACK,OAAO,CAAC0B,IAAI,EAAE;IAAF,CAAE,CAAC,CAAC,CACzCC,SAAS,CAAC;MACTC,IAAI,EAAEA,CAAC;QAAEP,QAAQ;QAAEE;MAAK,CAAE,KAAI;QAAAtD,cAAA,GAAAiC,CAAA;QAAAjC,cAAA,GAAA0B,CAAA;QAC5B,IAAI,CAACU,WAAW,GAAGgB,QAAQ;QAACpD,cAAA,GAAA0B,CAAA;QAC5B,IAAI,CAACS,UAAU,CAACyB,IAAI,GAAGN,KAAK;MAC9B,CAAC;MACDO,KAAK,EAAGA,KAAK,IAAI;QAAA7D,cAAA,GAAAiC,CAAA;QAAAjC,cAAA,GAAA0B,CAAA;QACf,IAAI,CAACI,KAAK,CAAC+B,KAAK,CAAC,CAAA7D,cAAA,GAAA8D,CAAA,UAAAD,KAAK,CAACA,KAAK,EAAEE,MAAM,MAAA/D,cAAA,GAAA8D,CAAA,UAAI,2BAA2B,EAAC;MACtE;KACD,CAAC;EACN;EAEAE,aAAaA,CAACC,MAAc,EAAEC,SAAiB;IAAAlE,cAAA,GAAAiC,CAAA;IAAAjC,cAAA,GAAA0B,CAAA;IAC7C,IAAI,CAACK,OAAO,CAACoB,IAAI,EAAE;IAACnD,cAAA,GAAA0B,CAAA;IACpB,IAAI,CAACG,cAAc,CAChBmC,aAAa,CAAC;MAAEG,OAAO,EAAEF,MAAM;MAAEG,UAAU,EAAEF;IAAS,CAAE,CAAC,CACzDV,IAAI,CAAClC,QAAQ,CAAC,MAAM;MAAAtB,cAAA,GAAAiC,CAAA;MAAAjC,cAAA,GAAA0B,CAAA;MAAA,WAAI,CAACK,OAAO,CAAC0B,IAAI,EAAE;IAAF,CAAE,CAAC,CAAC,CACzCC,SAAS,CAAC;MACTC,IAAI,EAAEA,CAAA,KAAK;QAAA3D,cAAA,GAAAiC,CAAA;QAAAjC,cAAA,GAAA0B,CAAA;QACT,IAAI,CAACI,KAAK,CAACuC,OAAO,CAAC,+BAA+B,CAAC;QAACrE,cAAA,GAAA0B,CAAA;QACpD,IAAI,CAACY,QAAQ,EAAE;MACjB,CAAC;MACDuB,KAAK,EAAGA,KAAK,IAAI;QAAA7D,cAAA,GAAAiC,CAAA;QAAAjC,cAAA,GAAA0B,CAAA;QACf,IAAI,CAACI,KAAK,CAAC+B,KAAK,CAAC,CAAA7D,cAAA,GAAA8D,CAAA,UAAAD,KAAK,CAACA,KAAK,EAAEE,MAAM,MAAA/D,cAAA,GAAA8D,CAAA,UAAI,4BAA4B,EAAC;MACvE;KACD,CAAC;EACN;EAEAQ,aAAaA,CAACL,MAAc,EAAEC,SAAiB;IAAAlE,cAAA,GAAAiC,CAAA;IAC7C,MAAMsC,IAAI,IAAAvE,cAAA,GAAA0B,CAAA,QAAG,IAAI,CAACS,UAAU,CAACyB,IAAI,CAACY,IAAI,CAAEC,CAAC,IAAK;MAAAzE,cAAA,GAAAiC,CAAA;MAAAjC,cAAA,GAAA0B,CAAA;MAAA,OAAA+C,CAAC,CAACR,MAAM,KAAKA,MAAM;IAAN,CAAM,CAAC;IAACjE,cAAA,GAAA0B,CAAA;IACnE,IAAI,CAAC6C,IAAI,EAAE;MAAAvE,cAAA,GAAA8D,CAAA;MAAA9D,cAAA,GAAA0B,CAAA;MAAA;IAAA,CAAO;MAAA1B,cAAA,GAAA8D,CAAA;IAAA;IAAA9D,cAAA,GAAA0B,CAAA;IAElB,IAAI6C,IAAI,CAACnB,QAAQ,CAACsB,MAAM,IAAI,CAAC,EAAE;MAAA1E,cAAA,GAAA8D,CAAA;MAAA9D,cAAA,GAAA0B,CAAA;MAC7B,IAAI,CAACI,KAAK,CAAC6C,OAAO,CAAC,0CAA0C,CAAC;MAAC3E,cAAA,GAAA0B,CAAA;MAC/D;IACF,CAAC;MAAA1B,cAAA,GAAA8D,CAAA;IAAA;IAAA9D,cAAA,GAAA0B,CAAA;IAED,IAAI,CAACK,OAAO,CAACoB,IAAI,EAAE;IAACnD,cAAA,GAAA0B,CAAA;IACpB,IAAI,CAACG,cAAc,CAChByC,aAAa,CAAC;MAAEH,OAAO,EAAEF,MAAM;MAAEG,UAAU,EAAEF;IAAS,CAAE,CAAC,CACzDV,IAAI,CAAClC,QAAQ,CAAC,MAAM;MAAAtB,cAAA,GAAAiC,CAAA;MAAAjC,cAAA,GAAA0B,CAAA;MAAA,WAAI,CAACK,OAAO,CAAC0B,IAAI,EAAE;IAAF,CAAE,CAAC,CAAC,CACzCC,SAAS,CAAC;MACTC,IAAI,EAAEA,CAAA,KAAK;QAAA3D,cAAA,GAAAiC,CAAA;QAAAjC,cAAA,GAAA0B,CAAA;QACT,IAAI,CAACI,KAAK,CAACuC,OAAO,CAAC,+BAA+B,CAAC;QAACrE,cAAA,GAAA0B,CAAA;QACpD,IAAI,CAACY,QAAQ,EAAE;MACjB,CAAC;MACDuB,KAAK,EAAGA,KAAK,IAAI;QAAA7D,cAAA,GAAAiC,CAAA;QAAAjC,cAAA,GAAA0B,CAAA;QACf,IAAI,CAACI,KAAK,CAAC+B,KAAK,CAAC,CAAA7D,cAAA,GAAA8D,CAAA,UAAAD,KAAK,CAACA,KAAK,EAAEE,MAAM,MAAA/D,cAAA,GAAA8D,CAAA,UAAI,4BAA4B,EAAC;MACvE;KACD,CAAC;EACN;EAEAc,oBAAoBA,CAACL,IAA2B;IAAAvE,cAAA,GAAAiC,CAAA;IAAAjC,cAAA,GAAA0B,CAAA;IAC9C,OAAO,IAAI,CAACU,WAAW,CAACW,MAAM,CAC3B8B,OAAO,IAAK;MAAA7E,cAAA,GAAAiC,CAAA;MAAAjC,cAAA,GAAA0B,CAAA;MAAA,QAAC6C,IAAI,CAACnB,QAAQ,CAAC0B,IAAI,CAAEC,CAAC,IAAK;QAAA/E,cAAA,GAAAiC,CAAA;QAAAjC,cAAA,GAAA0B,CAAA;QAAA,OAAAqD,CAAC,CAACC,EAAE,KAAKH,OAAO,CAACG,EAAE;MAAF,CAAE,CAAC;IAAD,CAAC,CAC7D;EACH;EAEAC,cAAcA,CAAA;IAAAjF,cAAA,GAAAiC,CAAA;IAAAjC,cAAA,GAAA0B,CAAA;IACZ,IAAI,CAACM,MAAM,CACRkD,IAAI,CAAC1D,mBAAmB,EAAE;MACzB2D,KAAK,EAAE,KAAK;MACZC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE;KACZ,CAAC,CACDC,WAAW,EAAE,CACb7B,SAAS,CAAE8B,MAA8B,IAAI;MAAAxF,cAAA,GAAAiC,CAAA;MAAAjC,cAAA,GAAA0B,CAAA;MAC5C,IAAI,CAAC8D,MAAM,EAAE;QAAAxF,cAAA,GAAA8D,CAAA;QAAA9D,cAAA,GAAA0B,CAAA;QAAA;MAAA,CAAO;QAAA1B,cAAA,GAAA8D,CAAA;MAAA;MAAA9D,cAAA,GAAA0B,CAAA;MACpB,IAAI,CAACY,QAAQ,EAAE;IACjB,CAAC,CAAC;EACN;;;;;;;;;;;;;;;;;;;;;cAtGClC,SAAS;QAAAqF,IAAA,GAAC7E,YAAY;MAAA;;cACtBR,SAAS;QAAAqF,IAAA,GAACzE,OAAO;MAAA;;;;;AANPW,0BAA0B,GAAA+D,UAAA,EApBtCvF,SAAS,CAAC;EACTwF,QAAQ,EAAE,wBAAwB;EAClCC,QAAA,EAAAC,oBAAkD;EAElDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP7F,YAAY,EACZiB,cAAc,EACdX,cAAc,EACdF,eAAe,EACfI,aAAa,EACbH,aAAa,EACbO,wBAAwB,EACxBC,eAAe,EACfJ,cAAc,EACdE,kBAAkB,EAClBI,aAAa,EACbZ,WAAW,CACZ;;CACF,CAAC,C,EACWsB,0BAA0B,CA4GtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}