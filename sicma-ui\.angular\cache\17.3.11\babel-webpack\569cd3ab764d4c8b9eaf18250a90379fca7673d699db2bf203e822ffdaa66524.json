{"ast": null, "code": "import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { Validators } from '@angular/forms';\nimport { provideNativeDateAdapter } from '@angular/material/core';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { ContractValuesService } from '@contract-management/services/contract-values.service';\nimport { ContractService } from '@contract-management/services/contract.service';\nimport { ContractorContractService } from '@contract-management/services/contractor-contract.service';\nimport { InsuredRisksService } from '@contract-management/services/insured_risks.service';\nimport { TypeWarrantyService } from '@contract-management/services/type_warranty.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { of, throwError } from 'rxjs';\nimport { AssociateContractorDialogComponent } from './associate-contractor-dialog.component';\ndescribe('AssociateContractorDialogComponent', () => {\n  let component;\n  let fixture;\n  let dialogRef;\n  let contractorContractService;\n  let contractService;\n  let contractValuesService;\n  let typeWarrantyService;\n  let insuredRisksService;\n  let alertService;\n  const mockTypeWarranty = [{\n    id: 1,\n    name: 'Type 1'\n  }, {\n    id: 2,\n    name: 'Type 2'\n  }];\n  const mockInsuredRisks = [{\n    id: 1,\n    name: 'Risk 1'\n  }, {\n    id: 2,\n    name: 'Risk 2'\n  }];\n  const mockContractorContract = {\n    id: 1,\n    contractorId: 1,\n    contractId: 1,\n    subscriptionDate: new Date('2024-02-19').toISOString().slice(0, 10),\n    contractStartDate: new Date('2024-02-21').toISOString().slice(0, 10),\n    warranty: true,\n    dateExpeditionWarranty: new Date('2024-02-19').toISOString().slice(0, 10),\n    typeWarrantyId: 1,\n    insuredRisksId: 1\n  };\n  const mockContract = {\n    id: 1,\n    contractNumber: 123,\n    monthlyPayment: 1000000,\n    object: 'Test Contract',\n    rup: true,\n    secopCode: 123456,\n    addition: false,\n    cession: true,\n    settled: false,\n    contractTypeId: 1,\n    statusId: 1,\n    causesSelectionId: 1,\n    managementSupportId: 1,\n    contractClassId: 1\n  };\n  beforeEach(() => {\n    const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close']);\n    const contractorContractServiceSpy = jasmine.createSpyObj('ContractorContractService', ['getLatestTerminationDate', 'create']);\n    const contractServiceSpy = jasmine.createSpyObj('ContractService', ['update']);\n    const contractValuesServiceSpy = jasmine.createSpyObj('ContractValuesService', ['getLatestEndDateByContractId']);\n    const typeWarrantyServiceSpy = jasmine.createSpyObj('TypeWarrantyService', ['getAll']);\n    const insuredRisksServiceSpy = jasmine.createSpyObj('InsuredRisksService', ['getAll']);\n    const alertServiceSpy = jasmine.createSpyObj('AlertService', ['success', 'error', 'warning']);\n    TestBed.configureTestingModule({\n      imports: [AssociateContractorDialogComponent, HttpClientTestingModule, BrowserAnimationsModule],\n      providers: [{\n        provide: MatDialogRef,\n        useValue: dialogRefSpy\n      }, {\n        provide: MAT_DIALOG_DATA,\n        useValue: {\n          contractId: 1\n        }\n      }, {\n        provide: ContractorContractService,\n        useValue: contractorContractServiceSpy\n      }, {\n        provide: ContractService,\n        useValue: contractServiceSpy\n      }, {\n        provide: ContractValuesService,\n        useValue: contractValuesServiceSpy\n      }, {\n        provide: TypeWarrantyService,\n        useValue: typeWarrantyServiceSpy\n      }, {\n        provide: InsuredRisksService,\n        useValue: insuredRisksServiceSpy\n      }, {\n        provide: AlertService,\n        useValue: alertServiceSpy\n      }, provideNativeDateAdapter()]\n    });\n    fixture = TestBed.createComponent(AssociateContractorDialogComponent);\n    component = fixture.componentInstance;\n    dialogRef = TestBed.inject(MatDialogRef);\n    contractorContractService = TestBed.inject(ContractorContractService);\n    contractService = TestBed.inject(ContractService);\n    contractValuesService = TestBed.inject(ContractValuesService);\n    typeWarrantyService = TestBed.inject(TypeWarrantyService);\n    insuredRisksService = TestBed.inject(InsuredRisksService);\n    alertService = TestBed.inject(AlertService);\n    typeWarrantyService.getAll.and.returnValue(of(mockTypeWarranty));\n    insuredRisksService.getAll.and.returnValue(of(mockInsuredRisks));\n    contractorContractService.getLatestTerminationDate.and.returnValue(of('2024-02-19'));\n    contractValuesService.getLatestEndDateByContractId.and.returnValue(of(new Date('2024-12-31')));\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  describe('Initial Data Loading', () => {\n    it('should load type warranty and insured risks data', () => {\n      expect(component.typeWarranty).toEqual(mockTypeWarranty);\n      expect(component.insuredRisks).toEqual(mockInsuredRisks);\n    });\n    it('should handle error when loading initial data', () => {\n      typeWarrantyService.getAll.and.returnValue(throwError(() => new Error()));\n      component.ngOnInit();\n      expect(alertService.error).toHaveBeenCalledWith('Error al cargar los datos iniciales');\n    });\n    it('should set minStartDate based on latest termination date', () => {\n      const expectedDate = new Date('2024-02-21');\n      expect(component.minStartDate?.toISOString().slice(0, 10)).toBe(expectedDate.toISOString().slice(0, 10));\n    });\n    it('should set latestContractEndDate', () => {\n      const expectedDate = new Date('2024-12-31');\n      expect(component.latestContractEndDate?.toISOString()).toBe(expectedDate.toISOString());\n    });\n  });\n  describe('Form Validation', () => {\n    it('should validate required fields', () => {\n      expect(component.cessionForm.get('subscriptionDate')?.errors?.['required']).toBeTruthy();\n      expect(component.cessionForm.get('startDate')?.errors?.['required']).toBeTruthy();\n    });\n    it('should update warranty-related validations when warranty is toggled', () => {\n      component.cessionForm.patchValue({\n        warranty: true\n      });\n      expect(component.cessionForm.get('dateExpeditionWarranty')?.hasValidator(Validators.required)).toBeTrue();\n      expect(component.cessionForm.get('typeWarrantyId')?.hasValidator(Validators.required)).toBeTrue();\n      expect(component.cessionForm.get('insuredRisksId')?.hasValidator(Validators.required)).toBeTrue();\n      component.cessionForm.patchValue({\n        warranty: false\n      });\n      expect(component.cessionForm.get('dateExpeditionWarranty')?.hasValidator(Validators.required)).toBeFalse();\n      expect(component.cessionForm.get('typeWarrantyId')?.hasValidator(Validators.required)).toBeFalse();\n      expect(component.cessionForm.get('insuredRisksId')?.hasValidator(Validators.required)).toBeFalse();\n    });\n  });\n  describe('Form Submission', () => {\n    beforeEach(() => {\n      component.contractorDetailForm = {\n        isValid: () => true,\n        getValue: () => ({\n          id: 1,\n          name: 'Test Contractor'\n        })\n      };\n      spyOn(component, 'isFormValid').and.returnValue(true);\n    });\n    it('should not submit if form is invalid', () => {\n      component.isFormValid = jasmine.createSpy().and.returnValue(false);\n      component.onSubmit();\n      expect(contractorContractService.create).not.toHaveBeenCalled();\n    });\n    it('should submit form successfully', () => {\n      const today = new Date();\n      component.cessionForm.patchValue({\n        subscriptionDate: today,\n        startDate: today,\n        warranty: true,\n        dateExpeditionWarranty: today,\n        typeWarrantyId: 1,\n        insuredRisksId: 1\n      });\n      contractorContractService.create.and.returnValue(of(mockContractorContract));\n      contractService.update.and.returnValue(of(mockContract));\n      component.onSubmit();\n      expect(contractorContractService.create).toHaveBeenCalledWith({\n        contractorId: 1,\n        contractId: 1,\n        subscriptionDate: today.toISOString().slice(0, 10),\n        contractStartDate: today.toISOString().slice(0, 10),\n        warranty: true,\n        dateExpeditionWarranty: today.toISOString().slice(0, 10),\n        typeWarrantyId: 1,\n        insuredRisksId: 1\n      });\n      expect(contractService.update).toHaveBeenCalledWith(1, {\n        earlyTermination: false,\n        cession: true\n      });\n      expect(alertService.success).toHaveBeenCalledWith('Contratista asociado exitosamente y contrato actualizado');\n      expect(dialogRef.close).toHaveBeenCalledWith({\n        success: true,\n        contractorAdded: true\n      });\n    });\n    it('should handle error when creating contractor contract', () => {\n      const today = new Date();\n      component.cessionForm.patchValue({\n        subscriptionDate: today,\n        startDate: today,\n        warranty: true,\n        dateExpeditionWarranty: today,\n        typeWarrantyId: 1,\n        insuredRisksId: 1\n      });\n      contractorContractService.create.and.returnValue(throwError(() => ({\n        error: 'Error'\n      })));\n      component.onSubmit();\n      expect(alertService.error).toHaveBeenCalledWith('Error al asociar el contratista.');\n    });\n    it('should handle error when updating contract', () => {\n      const today = new Date();\n      component.cessionForm.patchValue({\n        subscriptionDate: today,\n        startDate: today,\n        warranty: true,\n        dateExpeditionWarranty: today,\n        typeWarrantyId: 1,\n        insuredRisksId: 1\n      });\n      contractorContractService.create.and.returnValue(of(mockContractorContract));\n      contractService.update.and.returnValue(throwError(() => new Error()));\n      component.onSubmit();\n      expect(alertService.warning).toHaveBeenCalledWith('Contratista asociado, pero hubo un error al actualizar el contrato');\n      expect(dialogRef.close).toHaveBeenCalledWith({\n        success: true,\n        contractorAdded: true\n      });\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "TestBed", "Validators", "provideNativeDateAdapter", "MAT_DIALOG_DATA", "MatDialogRef", "BrowserAnimationsModule", "ContractValuesService", "ContractService", "ContractorContractService", "InsuredRisksService", "TypeWarrantyService", "AlertService", "of", "throwError", "AssociateContractorDialogComponent", "describe", "component", "fixture", "dialogRef", "contractorContractService", "contractService", "contractValuesService", "typeWarrantyService", "insuredRisksService", "alertService", "mockTypeWarranty", "id", "name", "mockInsuredRisks", "mockContractorContract", "contractorId", "contractId", "subscriptionDate", "Date", "toISOString", "slice", "contractStartDate", "warranty", "dateExpeditionWarranty", "typeWarrantyId", "insuredRisksId", "mockContract", "contractNumber", "monthlyPayment", "object", "rup", "secopCode", "addition", "cession", "settled", "contractTypeId", "statusId", "causesSelectionId", "managementSupportId", "contractClassId", "beforeEach", "dialogRefSpy", "jasmine", "createSpyObj", "contractorContractServiceSpy", "contractServiceSpy", "contractValuesServiceSpy", "typeWarrantyServiceSpy", "insuredRisksServiceSpy", "alertServiceSpy", "configureTestingModule", "imports", "providers", "provide", "useValue", "createComponent", "componentInstance", "inject", "getAll", "and", "returnValue", "getLatestTerminationDate", "getLatestEndDateByContractId", "detectChanges", "it", "expect", "toBeTruthy", "typeWarranty", "toEqual", "insuredRisks", "Error", "ngOnInit", "error", "toHaveBeenCalledWith", "expectedDate", "minStartDate", "toBe", "latestContractEndDate", "cessionForm", "get", "errors", "patchValue", "hasValidator", "required", "toBeTrue", "toBeFalse", "contractorDetailForm", "<PERSON><PERSON><PERSON><PERSON>", "getValue", "spyOn", "isFormValid", "createSpy", "onSubmit", "create", "not", "toHaveBeenCalled", "today", "startDate", "update", "earlyTermination", "success", "close", "contractorAdded", "warning"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\components\\associated-contractors-list\\associate-contractor-dialog\\associate-contractor-dialog.component.spec.ts"], "sourcesContent": ["import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { ComponentFixture, TestBed } from '@angular/core/testing';\nimport { Validators } from '@angular/forms';\nimport { provideNativeDateAdapter } from '@angular/material/core';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { ContractorDetailFormComponent } from '@contract-management/components/contractor-detail-form/contractor-detail-form.component';\nimport { Contract } from '@contract-management/models/contract.model';\nimport { ContractorContract } from '@contract-management/models/contractor-contract.model';\nimport { InsuredRisks } from '@contract-management/models/insured_risks.model';\nimport { TypeWarranty } from '@contract-management/models/type_warranty.model';\nimport { ContractValuesService } from '@contract-management/services/contract-values.service';\nimport { ContractService } from '@contract-management/services/contract.service';\nimport { ContractorContractService } from '@contract-management/services/contractor-contract.service';\nimport { InsuredRisksService } from '@contract-management/services/insured_risks.service';\nimport { TypeWarrantyService } from '@contract-management/services/type_warranty.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { of, throwError } from 'rxjs';\nimport { AssociateContractorDialogComponent } from './associate-contractor-dialog.component';\n\ndescribe('AssociateContractorDialogComponent', () => {\n  let component: AssociateContractorDialogComponent;\n  let fixture: ComponentFixture<AssociateContractorDialogComponent>;\n  let dialogRef: jasmine.SpyObj<\n    MatDialogRef<AssociateContractorDialogComponent>\n  >;\n  let contractorContractService: jasmine.SpyObj<ContractorContractService>;\n  let contractService: jasmine.SpyObj<ContractService>;\n  let contractValuesService: jasmine.SpyObj<ContractValuesService>;\n  let typeWarrantyService: jasmine.SpyObj<TypeWarrantyService>;\n  let insuredRisksService: jasmine.SpyObj<InsuredRisksService>;\n  let alertService: jasmine.SpyObj<AlertService>;\n\n  const mockTypeWarranty: TypeWarranty[] = [\n    { id: 1, name: 'Type 1' },\n    { id: 2, name: 'Type 2' },\n  ];\n\n  const mockInsuredRisks: InsuredRisks[] = [\n    { id: 1, name: 'Risk 1' },\n    { id: 2, name: 'Risk 2' },\n  ];\n\n  const mockContractorContract: ContractorContract = {\n    id: 1,\n    contractorId: 1,\n    contractId: 1,\n    subscriptionDate: new Date('2024-02-19').toISOString().slice(0, 10),\n    contractStartDate: new Date('2024-02-21').toISOString().slice(0, 10),\n    warranty: true,\n    dateExpeditionWarranty: new Date('2024-02-19').toISOString().slice(0, 10),\n    typeWarrantyId: 1,\n    insuredRisksId: 1,\n  };\n\n  const mockContract: Contract = {\n    id: 1,\n    contractNumber: 123,\n    monthlyPayment: 1000000,\n    object: 'Test Contract',\n    rup: true,\n    secopCode: 123456,\n    addition: false,\n    cession: true,\n    settled: false,\n    contractTypeId: 1,\n    statusId: 1,\n    causesSelectionId: 1,\n    managementSupportId: 1,\n    contractClassId: 1,\n  };\n\n  beforeEach(() => {\n    const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close']);\n    const contractorContractServiceSpy = jasmine.createSpyObj(\n      'ContractorContractService',\n      ['getLatestTerminationDate', 'create'],\n    );\n    const contractServiceSpy = jasmine.createSpyObj('ContractService', [\n      'update',\n    ]);\n    const contractValuesServiceSpy = jasmine.createSpyObj(\n      'ContractValuesService',\n      ['getLatestEndDateByContractId'],\n    );\n    const typeWarrantyServiceSpy = jasmine.createSpyObj('TypeWarrantyService', [\n      'getAll',\n    ]);\n    const insuredRisksServiceSpy = jasmine.createSpyObj('InsuredRisksService', [\n      'getAll',\n    ]);\n    const alertServiceSpy = jasmine.createSpyObj('AlertService', [\n      'success',\n      'error',\n      'warning',\n    ]);\n\n    TestBed.configureTestingModule({\n      imports: [\n        AssociateContractorDialogComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n      ],\n      providers: [\n        { provide: MatDialogRef, useValue: dialogRefSpy },\n        { provide: MAT_DIALOG_DATA, useValue: { contractId: 1 } },\n        {\n          provide: ContractorContractService,\n          useValue: contractorContractServiceSpy,\n        },\n        { provide: ContractService, useValue: contractServiceSpy },\n        { provide: ContractValuesService, useValue: contractValuesServiceSpy },\n        { provide: TypeWarrantyService, useValue: typeWarrantyServiceSpy },\n        { provide: InsuredRisksService, useValue: insuredRisksServiceSpy },\n        { provide: AlertService, useValue: alertServiceSpy },\n        provideNativeDateAdapter(),\n      ],\n    });\n\n    fixture = TestBed.createComponent(AssociateContractorDialogComponent);\n    component = fixture.componentInstance;\n    dialogRef = TestBed.inject(MatDialogRef) as jasmine.SpyObj<\n      MatDialogRef<AssociateContractorDialogComponent>\n    >;\n    contractorContractService = TestBed.inject(\n      ContractorContractService,\n    ) as jasmine.SpyObj<ContractorContractService>;\n    contractService = TestBed.inject(\n      ContractService,\n    ) as jasmine.SpyObj<ContractService>;\n    contractValuesService = TestBed.inject(\n      ContractValuesService,\n    ) as jasmine.SpyObj<ContractValuesService>;\n    typeWarrantyService = TestBed.inject(\n      TypeWarrantyService,\n    ) as jasmine.SpyObj<TypeWarrantyService>;\n    insuredRisksService = TestBed.inject(\n      InsuredRisksService,\n    ) as jasmine.SpyObj<InsuredRisksService>;\n    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;\n\n    typeWarrantyService.getAll.and.returnValue(of(mockTypeWarranty));\n    insuredRisksService.getAll.and.returnValue(of(mockInsuredRisks));\n    contractorContractService.getLatestTerminationDate.and.returnValue(\n      of('2024-02-19'),\n    );\n    contractValuesService.getLatestEndDateByContractId.and.returnValue(\n      of(new Date('2024-12-31')),\n    );\n\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  describe('Initial Data Loading', () => {\n    it('should load type warranty and insured risks data', () => {\n      expect(component.typeWarranty).toEqual(mockTypeWarranty);\n      expect(component.insuredRisks).toEqual(mockInsuredRisks);\n    });\n\n    it('should handle error when loading initial data', () => {\n      typeWarrantyService.getAll.and.returnValue(throwError(() => new Error()));\n      component.ngOnInit();\n      expect(alertService.error).toHaveBeenCalledWith(\n        'Error al cargar los datos iniciales',\n      );\n    });\n\n    it('should set minStartDate based on latest termination date', () => {\n      const expectedDate = new Date('2024-02-21');\n      expect(component.minStartDate?.toISOString().slice(0, 10)).toBe(\n        expectedDate.toISOString().slice(0, 10),\n      );\n    });\n\n    it('should set latestContractEndDate', () => {\n      const expectedDate = new Date('2024-12-31');\n      expect(component.latestContractEndDate?.toISOString()).toBe(\n        expectedDate.toISOString(),\n      );\n    });\n  });\n\n  describe('Form Validation', () => {\n    it('should validate required fields', () => {\n      expect(\n        component.cessionForm.get('subscriptionDate')?.errors?.['required'],\n      ).toBeTruthy();\n      expect(\n        component.cessionForm.get('startDate')?.errors?.['required'],\n      ).toBeTruthy();\n    });\n\n    it('should update warranty-related validations when warranty is toggled', () => {\n      component.cessionForm.patchValue({ warranty: true });\n      expect(\n        component.cessionForm\n          .get('dateExpeditionWarranty')\n          ?.hasValidator(Validators.required),\n      ).toBeTrue();\n      expect(\n        component.cessionForm\n          .get('typeWarrantyId')\n          ?.hasValidator(Validators.required),\n      ).toBeTrue();\n      expect(\n        component.cessionForm\n          .get('insuredRisksId')\n          ?.hasValidator(Validators.required),\n      ).toBeTrue();\n\n      component.cessionForm.patchValue({ warranty: false });\n      expect(\n        component.cessionForm\n          .get('dateExpeditionWarranty')\n          ?.hasValidator(Validators.required),\n      ).toBeFalse();\n      expect(\n        component.cessionForm\n          .get('typeWarrantyId')\n          ?.hasValidator(Validators.required),\n      ).toBeFalse();\n      expect(\n        component.cessionForm\n          .get('insuredRisksId')\n          ?.hasValidator(Validators.required),\n      ).toBeFalse();\n    });\n  });\n\n  describe('Form Submission', () => {\n    beforeEach(() => {\n      component.contractorDetailForm = {\n        isValid: () => true,\n        getValue: () => ({ id: 1, name: 'Test Contractor' }),\n      } as unknown as ContractorDetailFormComponent;\n\n      spyOn(component, 'isFormValid').and.returnValue(true);\n    });\n\n    it('should not submit if form is invalid', () => {\n      component.isFormValid = jasmine.createSpy().and.returnValue(false);\n      component.onSubmit();\n      expect(contractorContractService.create).not.toHaveBeenCalled();\n    });\n\n    it('should submit form successfully', () => {\n      const today = new Date();\n      component.cessionForm.patchValue({\n        subscriptionDate: today,\n        startDate: today,\n        warranty: true,\n        dateExpeditionWarranty: today,\n        typeWarrantyId: 1,\n        insuredRisksId: 1,\n      });\n\n      contractorContractService.create.and.returnValue(\n        of(mockContractorContract),\n      );\n      contractService.update.and.returnValue(of(mockContract));\n\n      component.onSubmit();\n\n      expect(contractorContractService.create).toHaveBeenCalledWith({\n        contractorId: 1,\n        contractId: 1,\n        subscriptionDate: today.toISOString().slice(0, 10),\n        contractStartDate: today.toISOString().slice(0, 10),\n        warranty: true,\n        dateExpeditionWarranty: today.toISOString().slice(0, 10),\n        typeWarrantyId: 1,\n        insuredRisksId: 1,\n      });\n\n      expect(contractService.update).toHaveBeenCalledWith(1, {\n        earlyTermination: false,\n        cession: true,\n      });\n\n      expect(alertService.success).toHaveBeenCalledWith(\n        'Contratista asociado exitosamente y contrato actualizado',\n      );\n\n      expect(dialogRef.close).toHaveBeenCalledWith({\n        success: true,\n        contractorAdded: true,\n      });\n    });\n\n    it('should handle error when creating contractor contract', () => {\n      const today = new Date();\n      component.cessionForm.patchValue({\n        subscriptionDate: today,\n        startDate: today,\n        warranty: true,\n        dateExpeditionWarranty: today,\n        typeWarrantyId: 1,\n        insuredRisksId: 1,\n      });\n\n      contractorContractService.create.and.returnValue(\n        throwError(() => ({ error: 'Error' })),\n      );\n\n      component.onSubmit();\n\n      expect(alertService.error).toHaveBeenCalledWith(\n        'Error al asociar el contratista.',\n      );\n    });\n\n    it('should handle error when updating contract', () => {\n      const today = new Date();\n      component.cessionForm.patchValue({\n        subscriptionDate: today,\n        startDate: today,\n        warranty: true,\n        dateExpeditionWarranty: today,\n        typeWarrantyId: 1,\n        insuredRisksId: 1,\n      });\n\n      contractorContractService.create.and.returnValue(\n        of(mockContractorContract),\n      );\n      contractService.update.and.returnValue(throwError(() => new Error()));\n\n      component.onSubmit();\n\n      expect(alertService.warning).toHaveBeenCalledWith(\n        'Contratista asociado, pero hubo un error al actualizar el contrato',\n      );\n      expect(dialogRef.close).toHaveBeenCalledWith({\n        success: true,\n        contractorAdded: true,\n      });\n    });\n  });\n});\n"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,8BAA8B;AACtE,SAA2BC,OAAO,QAAQ,uBAAuB;AACjE,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,wBAAwB,QAAQ,wBAAwB;AACjE,SAASC,eAAe,EAAEC,YAAY,QAAQ,0BAA0B;AACxE,SAASC,uBAAuB,QAAQ,sCAAsC;AAM9E,SAASC,qBAAqB,QAAQ,uDAAuD;AAC7F,SAASC,eAAe,QAAQ,gDAAgD;AAChF,SAASC,yBAAyB,QAAQ,2DAA2D;AACrG,SAASC,mBAAmB,QAAQ,qDAAqD;AACzF,SAASC,mBAAmB,QAAQ,qDAAqD;AACzF,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,EAAE,EAAEC,UAAU,QAAQ,MAAM;AACrC,SAASC,kCAAkC,QAAQ,yCAAyC;AAE5FC,QAAQ,CAAC,oCAAoC,EAAE,MAAK;EAClD,IAAIC,SAA6C;EACjD,IAAIC,OAA6D;EACjE,IAAIC,SAEH;EACD,IAAIC,yBAAoE;EACxE,IAAIC,eAAgD;EACpD,IAAIC,qBAA4D;EAChE,IAAIC,mBAAwD;EAC5D,IAAIC,mBAAwD;EAC5D,IAAIC,YAA0C;EAE9C,MAAMC,gBAAgB,GAAmB,CACvC;IAAEC,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAQ,CAAE,EACzB;IAAED,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAQ,CAAE,CAC1B;EAED,MAAMC,gBAAgB,GAAmB,CACvC;IAAEF,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAQ,CAAE,EACzB;IAAED,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAQ,CAAE,CAC1B;EAED,MAAME,sBAAsB,GAAuB;IACjDH,EAAE,EAAE,CAAC;IACLI,YAAY,EAAE,CAAC;IACfC,UAAU,EAAE,CAAC;IACbC,gBAAgB,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IACnEC,iBAAiB,EAAE,IAAIH,IAAI,CAAC,YAAY,CAAC,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IACpEE,QAAQ,EAAE,IAAI;IACdC,sBAAsB,EAAE,IAAIL,IAAI,CAAC,YAAY,CAAC,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IACzEI,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE;GACjB;EAED,MAAMC,YAAY,GAAa;IAC7Bf,EAAE,EAAE,CAAC;IACLgB,cAAc,EAAE,GAAG;IACnBC,cAAc,EAAE,OAAO;IACvBC,MAAM,EAAE,eAAe;IACvBC,GAAG,EAAE,IAAI;IACTC,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE,KAAK;IACdC,cAAc,EAAE,CAAC;IACjBC,QAAQ,EAAE,CAAC;IACXC,iBAAiB,EAAE,CAAC;IACpBC,mBAAmB,EAAE,CAAC;IACtBC,eAAe,EAAE;GAClB;EAEDC,UAAU,CAAC,MAAK;IACd,MAAMC,YAAY,GAAGC,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC;IACpE,MAAMC,4BAA4B,GAAGF,OAAO,CAACC,YAAY,CACvD,2BAA2B,EAC3B,CAAC,0BAA0B,EAAE,QAAQ,CAAC,CACvC;IACD,MAAME,kBAAkB,GAAGH,OAAO,CAACC,YAAY,CAAC,iBAAiB,EAAE,CACjE,QAAQ,CACT,CAAC;IACF,MAAMG,wBAAwB,GAAGJ,OAAO,CAACC,YAAY,CACnD,uBAAuB,EACvB,CAAC,8BAA8B,CAAC,CACjC;IACD,MAAMI,sBAAsB,GAAGL,OAAO,CAACC,YAAY,CAAC,qBAAqB,EAAE,CACzE,QAAQ,CACT,CAAC;IACF,MAAMK,sBAAsB,GAAGN,OAAO,CAACC,YAAY,CAAC,qBAAqB,EAAE,CACzE,QAAQ,CACT,CAAC;IACF,MAAMM,eAAe,GAAGP,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAC3D,SAAS,EACT,OAAO,EACP,SAAS,CACV,CAAC;IAEF1D,OAAO,CAACiE,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPpD,kCAAkC,EAClCf,uBAAuB,EACvBM,uBAAuB,CACxB;MACD8D,SAAS,EAAE,CACT;QAAEC,OAAO,EAAEhE,YAAY;QAAEiE,QAAQ,EAAEb;MAAY,CAAE,EACjD;QAAEY,OAAO,EAAEjE,eAAe;QAAEkE,QAAQ,EAAE;UAAEtC,UAAU,EAAE;QAAC;MAAE,CAAE,EACzD;QACEqC,OAAO,EAAE5D,yBAAyB;QAClC6D,QAAQ,EAAEV;OACX,EACD;QAAES,OAAO,EAAE7D,eAAe;QAAE8D,QAAQ,EAAET;MAAkB,CAAE,EAC1D;QAAEQ,OAAO,EAAE9D,qBAAqB;QAAE+D,QAAQ,EAAER;MAAwB,CAAE,EACtE;QAAEO,OAAO,EAAE1D,mBAAmB;QAAE2D,QAAQ,EAAEP;MAAsB,CAAE,EAClE;QAAEM,OAAO,EAAE3D,mBAAmB;QAAE4D,QAAQ,EAAEN;MAAsB,CAAE,EAClE;QAAEK,OAAO,EAAEzD,YAAY;QAAE0D,QAAQ,EAAEL;MAAe,CAAE,EACpD9D,wBAAwB,EAAE;KAE7B,CAAC;IAEFe,OAAO,GAAGjB,OAAO,CAACsE,eAAe,CAACxD,kCAAkC,CAAC;IACrEE,SAAS,GAAGC,OAAO,CAACsD,iBAAiB;IACrCrD,SAAS,GAAGlB,OAAO,CAACwE,MAAM,CAACpE,YAAY,CAEtC;IACDe,yBAAyB,GAAGnB,OAAO,CAACwE,MAAM,CACxChE,yBAAyB,CACmB;IAC9CY,eAAe,GAAGpB,OAAO,CAACwE,MAAM,CAC9BjE,eAAe,CACmB;IACpCc,qBAAqB,GAAGrB,OAAO,CAACwE,MAAM,CACpClE,qBAAqB,CACmB;IAC1CgB,mBAAmB,GAAGtB,OAAO,CAACwE,MAAM,CAClC9D,mBAAmB,CACmB;IACxCa,mBAAmB,GAAGvB,OAAO,CAACwE,MAAM,CAClC/D,mBAAmB,CACmB;IACxCe,YAAY,GAAGxB,OAAO,CAACwE,MAAM,CAAC7D,YAAY,CAAiC;IAE3EW,mBAAmB,CAACmD,MAAM,CAACC,GAAG,CAACC,WAAW,CAAC/D,EAAE,CAACa,gBAAgB,CAAC,CAAC;IAChEF,mBAAmB,CAACkD,MAAM,CAACC,GAAG,CAACC,WAAW,CAAC/D,EAAE,CAACgB,gBAAgB,CAAC,CAAC;IAChET,yBAAyB,CAACyD,wBAAwB,CAACF,GAAG,CAACC,WAAW,CAChE/D,EAAE,CAAC,YAAY,CAAC,CACjB;IACDS,qBAAqB,CAACwD,4BAA4B,CAACH,GAAG,CAACC,WAAW,CAChE/D,EAAE,CAAC,IAAIqB,IAAI,CAAC,YAAY,CAAC,CAAC,CAC3B;IAEDhB,OAAO,CAAC6D,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAAChE,SAAS,CAAC,CAACiE,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFlE,QAAQ,CAAC,sBAAsB,EAAE,MAAK;IACpCgE,EAAE,CAAC,kDAAkD,EAAE,MAAK;MAC1DC,MAAM,CAAChE,SAAS,CAACkE,YAAY,CAAC,CAACC,OAAO,CAAC1D,gBAAgB,CAAC;MACxDuD,MAAM,CAAChE,SAAS,CAACoE,YAAY,CAAC,CAACD,OAAO,CAACvD,gBAAgB,CAAC;IAC1D,CAAC,CAAC;IAEFmD,EAAE,CAAC,+CAA+C,EAAE,MAAK;MACvDzD,mBAAmB,CAACmD,MAAM,CAACC,GAAG,CAACC,WAAW,CAAC9D,UAAU,CAAC,MAAM,IAAIwE,KAAK,EAAE,CAAC,CAAC;MACzErE,SAAS,CAACsE,QAAQ,EAAE;MACpBN,MAAM,CAACxD,YAAY,CAAC+D,KAAK,CAAC,CAACC,oBAAoB,CAC7C,qCAAqC,CACtC;IACH,CAAC,CAAC;IAEFT,EAAE,CAAC,0DAA0D,EAAE,MAAK;MAClE,MAAMU,YAAY,GAAG,IAAIxD,IAAI,CAAC,YAAY,CAAC;MAC3C+C,MAAM,CAAChE,SAAS,CAAC0E,YAAY,EAAExD,WAAW,EAAE,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAACwD,IAAI,CAC7DF,YAAY,CAACvD,WAAW,EAAE,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CACxC;IACH,CAAC,CAAC;IAEF4C,EAAE,CAAC,kCAAkC,EAAE,MAAK;MAC1C,MAAMU,YAAY,GAAG,IAAIxD,IAAI,CAAC,YAAY,CAAC;MAC3C+C,MAAM,CAAChE,SAAS,CAAC4E,qBAAqB,EAAE1D,WAAW,EAAE,CAAC,CAACyD,IAAI,CACzDF,YAAY,CAACvD,WAAW,EAAE,CAC3B;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFnB,QAAQ,CAAC,iBAAiB,EAAE,MAAK;IAC/BgE,EAAE,CAAC,iCAAiC,EAAE,MAAK;MACzCC,MAAM,CACJhE,SAAS,CAAC6E,WAAW,CAACC,GAAG,CAAC,kBAAkB,CAAC,EAAEC,MAAM,GAAG,UAAU,CAAC,CACpE,CAACd,UAAU,EAAE;MACdD,MAAM,CACJhE,SAAS,CAAC6E,WAAW,CAACC,GAAG,CAAC,WAAW,CAAC,EAAEC,MAAM,GAAG,UAAU,CAAC,CAC7D,CAACd,UAAU,EAAE;IAChB,CAAC,CAAC;IAEFF,EAAE,CAAC,qEAAqE,EAAE,MAAK;MAC7E/D,SAAS,CAAC6E,WAAW,CAACG,UAAU,CAAC;QAAE3D,QAAQ,EAAE;MAAI,CAAE,CAAC;MACpD2C,MAAM,CACJhE,SAAS,CAAC6E,WAAW,CAClBC,GAAG,CAAC,wBAAwB,CAAC,EAC5BG,YAAY,CAAChG,UAAU,CAACiG,QAAQ,CAAC,CACtC,CAACC,QAAQ,EAAE;MACZnB,MAAM,CACJhE,SAAS,CAAC6E,WAAW,CAClBC,GAAG,CAAC,gBAAgB,CAAC,EACpBG,YAAY,CAAChG,UAAU,CAACiG,QAAQ,CAAC,CACtC,CAACC,QAAQ,EAAE;MACZnB,MAAM,CACJhE,SAAS,CAAC6E,WAAW,CAClBC,GAAG,CAAC,gBAAgB,CAAC,EACpBG,YAAY,CAAChG,UAAU,CAACiG,QAAQ,CAAC,CACtC,CAACC,QAAQ,EAAE;MAEZnF,SAAS,CAAC6E,WAAW,CAACG,UAAU,CAAC;QAAE3D,QAAQ,EAAE;MAAK,CAAE,CAAC;MACrD2C,MAAM,CACJhE,SAAS,CAAC6E,WAAW,CAClBC,GAAG,CAAC,wBAAwB,CAAC,EAC5BG,YAAY,CAAChG,UAAU,CAACiG,QAAQ,CAAC,CACtC,CAACE,SAAS,EAAE;MACbpB,MAAM,CACJhE,SAAS,CAAC6E,WAAW,CAClBC,GAAG,CAAC,gBAAgB,CAAC,EACpBG,YAAY,CAAChG,UAAU,CAACiG,QAAQ,CAAC,CACtC,CAACE,SAAS,EAAE;MACbpB,MAAM,CACJhE,SAAS,CAAC6E,WAAW,CAClBC,GAAG,CAAC,gBAAgB,CAAC,EACpBG,YAAY,CAAChG,UAAU,CAACiG,QAAQ,CAAC,CACtC,CAACE,SAAS,EAAE;IACf,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFrF,QAAQ,CAAC,iBAAiB,EAAE,MAAK;IAC/BwC,UAAU,CAAC,MAAK;MACdvC,SAAS,CAACqF,oBAAoB,GAAG;QAC/BC,OAAO,EAAEA,CAAA,KAAM,IAAI;QACnBC,QAAQ,EAAEA,CAAA,MAAO;UAAE7E,EAAE,EAAE,CAAC;UAAEC,IAAI,EAAE;QAAiB,CAAE;OACR;MAE7C6E,KAAK,CAACxF,SAAS,EAAE,aAAa,CAAC,CAAC0D,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;IACvD,CAAC,CAAC;IAEFI,EAAE,CAAC,sCAAsC,EAAE,MAAK;MAC9C/D,SAAS,CAACyF,WAAW,GAAGhD,OAAO,CAACiD,SAAS,EAAE,CAAChC,GAAG,CAACC,WAAW,CAAC,KAAK,CAAC;MAClE3D,SAAS,CAAC2F,QAAQ,EAAE;MACpB3B,MAAM,CAAC7D,yBAAyB,CAACyF,MAAM,CAAC,CAACC,GAAG,CAACC,gBAAgB,EAAE;IACjE,CAAC,CAAC;IAEF/B,EAAE,CAAC,iCAAiC,EAAE,MAAK;MACzC,MAAMgC,KAAK,GAAG,IAAI9E,IAAI,EAAE;MACxBjB,SAAS,CAAC6E,WAAW,CAACG,UAAU,CAAC;QAC/BhE,gBAAgB,EAAE+E,KAAK;QACvBC,SAAS,EAAED,KAAK;QAChB1E,QAAQ,EAAE,IAAI;QACdC,sBAAsB,EAAEyE,KAAK;QAC7BxE,cAAc,EAAE,CAAC;QACjBC,cAAc,EAAE;OACjB,CAAC;MAEFrB,yBAAyB,CAACyF,MAAM,CAAClC,GAAG,CAACC,WAAW,CAC9C/D,EAAE,CAACiB,sBAAsB,CAAC,CAC3B;MACDT,eAAe,CAAC6F,MAAM,CAACvC,GAAG,CAACC,WAAW,CAAC/D,EAAE,CAAC6B,YAAY,CAAC,CAAC;MAExDzB,SAAS,CAAC2F,QAAQ,EAAE;MAEpB3B,MAAM,CAAC7D,yBAAyB,CAACyF,MAAM,CAAC,CAACpB,oBAAoB,CAAC;QAC5D1D,YAAY,EAAE,CAAC;QACfC,UAAU,EAAE,CAAC;QACbC,gBAAgB,EAAE+E,KAAK,CAAC7E,WAAW,EAAE,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;QAClDC,iBAAiB,EAAE2E,KAAK,CAAC7E,WAAW,EAAE,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;QACnDE,QAAQ,EAAE,IAAI;QACdC,sBAAsB,EAAEyE,KAAK,CAAC7E,WAAW,EAAE,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;QACxDI,cAAc,EAAE,CAAC;QACjBC,cAAc,EAAE;OACjB,CAAC;MAEFwC,MAAM,CAAC5D,eAAe,CAAC6F,MAAM,CAAC,CAACzB,oBAAoB,CAAC,CAAC,EAAE;QACrD0B,gBAAgB,EAAE,KAAK;QACvBlE,OAAO,EAAE;OACV,CAAC;MAEFgC,MAAM,CAACxD,YAAY,CAAC2F,OAAO,CAAC,CAAC3B,oBAAoB,CAC/C,0DAA0D,CAC3D;MAEDR,MAAM,CAAC9D,SAAS,CAACkG,KAAK,CAAC,CAAC5B,oBAAoB,CAAC;QAC3C2B,OAAO,EAAE,IAAI;QACbE,eAAe,EAAE;OAClB,CAAC;IACJ,CAAC,CAAC;IAEFtC,EAAE,CAAC,uDAAuD,EAAE,MAAK;MAC/D,MAAMgC,KAAK,GAAG,IAAI9E,IAAI,EAAE;MACxBjB,SAAS,CAAC6E,WAAW,CAACG,UAAU,CAAC;QAC/BhE,gBAAgB,EAAE+E,KAAK;QACvBC,SAAS,EAAED,KAAK;QAChB1E,QAAQ,EAAE,IAAI;QACdC,sBAAsB,EAAEyE,KAAK;QAC7BxE,cAAc,EAAE,CAAC;QACjBC,cAAc,EAAE;OACjB,CAAC;MAEFrB,yBAAyB,CAACyF,MAAM,CAAClC,GAAG,CAACC,WAAW,CAC9C9D,UAAU,CAAC,OAAO;QAAE0E,KAAK,EAAE;MAAO,CAAE,CAAC,CAAC,CACvC;MAEDvE,SAAS,CAAC2F,QAAQ,EAAE;MAEpB3B,MAAM,CAACxD,YAAY,CAAC+D,KAAK,CAAC,CAACC,oBAAoB,CAC7C,kCAAkC,CACnC;IACH,CAAC,CAAC;IAEFT,EAAE,CAAC,4CAA4C,EAAE,MAAK;MACpD,MAAMgC,KAAK,GAAG,IAAI9E,IAAI,EAAE;MACxBjB,SAAS,CAAC6E,WAAW,CAACG,UAAU,CAAC;QAC/BhE,gBAAgB,EAAE+E,KAAK;QACvBC,SAAS,EAAED,KAAK;QAChB1E,QAAQ,EAAE,IAAI;QACdC,sBAAsB,EAAEyE,KAAK;QAC7BxE,cAAc,EAAE,CAAC;QACjBC,cAAc,EAAE;OACjB,CAAC;MAEFrB,yBAAyB,CAACyF,MAAM,CAAClC,GAAG,CAACC,WAAW,CAC9C/D,EAAE,CAACiB,sBAAsB,CAAC,CAC3B;MACDT,eAAe,CAAC6F,MAAM,CAACvC,GAAG,CAACC,WAAW,CAAC9D,UAAU,CAAC,MAAM,IAAIwE,KAAK,EAAE,CAAC,CAAC;MAErErE,SAAS,CAAC2F,QAAQ,EAAE;MAEpB3B,MAAM,CAACxD,YAAY,CAAC8F,OAAO,CAAC,CAAC9B,oBAAoB,CAC/C,oEAAoE,CACrE;MACDR,MAAM,CAAC9D,SAAS,CAACkG,KAAK,CAAC,CAAC5B,oBAAoB,CAAC;QAC3C2B,OAAO,EAAE,IAAI;QACbE,eAAe,EAAE;OAClB,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}