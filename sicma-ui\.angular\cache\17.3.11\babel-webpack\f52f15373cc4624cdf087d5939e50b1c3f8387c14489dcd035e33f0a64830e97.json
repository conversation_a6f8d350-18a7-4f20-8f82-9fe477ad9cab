{"ast": null, "code": "function cov_25a3ximgx8() {\n  var path = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contract-management\\\\components\\\\ccps-list\\\\ccp-dialog\\\\ccp-dialog.component.ts\";\n  var hash = \"7987a9396d73cf773109d2c9c02e954599cb64af\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contract-management\\\\components\\\\ccps-list\\\\ccp-dialog\\\\ccp-dialog.component.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 17,\n          column: 25\n        },\n        end: {\n          line: 128,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 19,\n          column: 8\n        },\n        end: {\n          line: 19,\n          column: 35\n        }\n      },\n      \"2\": {\n        start: {\n          line: 20,\n          column: 8\n        },\n        end: {\n          line: 20,\n          column: 39\n        }\n      },\n      \"3\": {\n        start: {\n          line: 21,\n          column: 8\n        },\n        end: {\n          line: 21,\n          column: 37\n        }\n      },\n      \"4\": {\n        start: {\n          line: 22,\n          column: 8\n        },\n        end: {\n          line: 22,\n          column: 59\n        }\n      },\n      \"5\": {\n        start: {\n          line: 23,\n          column: 8\n        },\n        end: {\n          line: 23,\n          column: 27\n        }\n      },\n      \"6\": {\n        start: {\n          line: 24,\n          column: 8\n        },\n        end: {\n          line: 24,\n          column: 25\n        }\n      },\n      \"7\": {\n        start: {\n          line: 25,\n          column: 8\n        },\n        end: {\n          line: 30,\n          column: 11\n        }\n      },\n      \"8\": {\n        start: {\n          line: 31,\n          column: 8\n        },\n        end: {\n          line: 31,\n          column: 36\n        }\n      },\n      \"9\": {\n        start: {\n          line: 32,\n          column: 8\n        },\n        end: {\n          line: 32,\n          column: 38\n        }\n      },\n      \"10\": {\n        start: {\n          line: 33,\n          column: 8\n        },\n        end: {\n          line: 33,\n          column: 32\n        }\n      },\n      \"11\": {\n        start: {\n          line: 34,\n          column: 8\n        },\n        end: {\n          line: 61,\n          column: 9\n        }\n      },\n      \"12\": {\n        start: {\n          line: 35,\n          column: 12\n        },\n        end: {\n          line: 40,\n          column: 15\n        }\n      },\n      \"13\": {\n        start: {\n          line: 41,\n          column: 12\n        },\n        end: {\n          line: 41,\n          column: 49\n        }\n      },\n      \"14\": {\n        start: {\n          line: 42,\n          column: 12\n        },\n        end: {\n          line: 56,\n          column: 15\n        }\n      },\n      \"15\": {\n        start: {\n          line: 43,\n          column: 33\n        },\n        end: {\n          line: 43,\n          column: 51\n        }\n      },\n      \"16\": {\n        start: {\n          line: 44,\n          column: 38\n        },\n        end: {\n          line: 44,\n          column: 73\n        }\n      },\n      \"17\": {\n        start: {\n          line: 45,\n          column: 16\n        },\n        end: {\n          line: 47,\n          column: 17\n        }\n      },\n      \"18\": {\n        start: {\n          line: 46,\n          column: 20\n        },\n        end: {\n          line: 46,\n          column: 54\n        }\n      },\n      \"19\": {\n        start: {\n          line: 48,\n          column: 38\n        },\n        end: {\n          line: 48,\n          column: 76\n        }\n      },\n      \"20\": {\n        start: {\n          line: 49,\n          column: 39\n        },\n        end: {\n          line: 52,\n          column: 38\n        }\n      },\n      \"21\": {\n        start: {\n          line: 53,\n          column: 16\n        },\n        end: {\n          line: 55,\n          column: 49\n        }\n      },\n      \"22\": {\n        start: {\n          line: 59,\n          column: 12\n        },\n        end: {\n          line: 59,\n          column: 59\n        }\n      },\n      \"23\": {\n        start: {\n          line: 60,\n          column: 12\n        },\n        end: {\n          line: 60,\n          column: 61\n        }\n      },\n      \"24\": {\n        start: {\n          line: 62,\n          column: 8\n        },\n        end: {\n          line: 62,\n          column: 60\n        }\n      },\n      \"25\": {\n        start: {\n          line: 65,\n          column: 8\n        },\n        end: {\n          line: 65,\n          column: 34\n        }\n      },\n      \"26\": {\n        start: {\n          line: 66,\n          column: 8\n        },\n        end: {\n          line: 75,\n          column: 9\n        }\n      },\n      \"27\": {\n        start: {\n          line: 67,\n          column: 12\n        },\n        end: {\n          line: 74,\n          column: 15\n        }\n      },\n      \"28\": {\n        start: {\n          line: 68,\n          column: 16\n        },\n        end: {\n          line: 73,\n          column: 17\n        }\n      },\n      \"29\": {\n        start: {\n          line: 69,\n          column: 39\n        },\n        end: {\n          line: 69,\n          column: 88\n        }\n      },\n      \"30\": {\n        start: {\n          line: 70,\n          column: 20\n        },\n        end: {\n          line: 72,\n          column: 21\n        }\n      },\n      \"31\": {\n        start: {\n          line: 71,\n          column: 24\n        },\n        end: {\n          line: 71,\n          column: 89\n        }\n      },\n      \"32\": {\n        start: {\n          line: 78,\n          column: 8\n        },\n        end: {\n          line: 94,\n          column: 11\n        }\n      },\n      \"33\": {\n        start: {\n          line: 81,\n          column: 12\n        },\n        end: {\n          line: 81,\n          column: 80\n        }\n      },\n      \"34\": {\n        start: {\n          line: 81,\n          column: 54\n        },\n        end: {\n          line: 81,\n          column: 75\n        }\n      },\n      \"35\": {\n        start: {\n          line: 85,\n          column: 16\n        },\n        end: {\n          line: 85,\n          column: 53\n        }\n      },\n      \"36\": {\n        start: {\n          line: 86,\n          column: 40\n        },\n        end: {\n          line: 86,\n          column: 65\n        }\n      },\n      \"37\": {\n        start: {\n          line: 87,\n          column: 16\n        },\n        end: {\n          line: 89,\n          column: 70\n        }\n      },\n      \"38\": {\n        start: {\n          line: 92,\n          column: 16\n        },\n        end: {\n          line: 92,\n          column: 100\n        }\n      },\n      \"39\": {\n        start: {\n          line: 97,\n          column: 8\n        },\n        end: {\n          line: 115,\n          column: 9\n        }\n      },\n      \"40\": {\n        start: {\n          line: 98,\n          column: 30\n        },\n        end: {\n          line: 98,\n          column: 56\n        }\n      },\n      \"41\": {\n        start: {\n          line: 99,\n          column: 28\n        },\n        end: {\n          line: 102,\n          column: 13\n        }\n      },\n      \"42\": {\n        start: {\n          line: 103,\n          column: 30\n        },\n        end: {\n          line: 105,\n          column: 49\n        }\n      },\n      \"43\": {\n        start: {\n          line: 106,\n          column: 12\n        },\n        end: {\n          line: 114,\n          column: 15\n        }\n      },\n      \"44\": {\n        start: {\n          line: 108,\n          column: 20\n        },\n        end: {\n          line: 108,\n          column: 99\n        }\n      },\n      \"45\": {\n        start: {\n          line: 109,\n          column: 20\n        },\n        end: {\n          line: 109,\n          column: 46\n        }\n      },\n      \"46\": {\n        start: {\n          line: 112,\n          column: 20\n        },\n        end: {\n          line: 112,\n          column: 117\n        }\n      },\n      \"47\": {\n        start: {\n          line: 118,\n          column: 8\n        },\n        end: {\n          line: 118,\n          column: 31\n        }\n      },\n      \"48\": {\n        start: {\n          line: 120,\n          column: 13\n        },\n        end: {\n          line: 127,\n          column: 6\n        }\n      },\n      \"49\": {\n        start: {\n          line: 120,\n          column: 41\n        },\n        end: {\n          line: 127,\n          column: 5\n        }\n      },\n      \"50\": {\n        start: {\n          line: 129,\n          column: 0\n        },\n        end: {\n          line: 146,\n          column: 23\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 18,\n            column: 4\n          },\n          end: {\n            line: 18,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 18,\n            column: 88\n          },\n          end: {\n            line: 63,\n            column: 5\n          }\n        },\n        line: 18\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 42,\n            column: 63\n          },\n          end: {\n            line: 42,\n            column: 64\n          }\n        },\n        loc: {\n          start: {\n            line: 42,\n            column: 76\n          },\n          end: {\n            line: 56,\n            column: 13\n          }\n        },\n        line: 42\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 64,\n            column: 4\n          },\n          end: {\n            line: 64,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 64,\n            column: 15\n          },\n          end: {\n            line: 76,\n            column: 5\n          }\n        },\n        line: 64\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 67,\n            column: 62\n          },\n          end: {\n            line: 67,\n            column: 63\n          }\n        },\n        loc: {\n          start: {\n            line: 67,\n            column: 73\n          },\n          end: {\n            line: 74,\n            column: 13\n          }\n        },\n        line: 67\n      },\n      \"4\": {\n        name: \"(anonymous_4)\",\n        decl: {\n          start: {\n            line: 77,\n            column: 4\n          },\n          end: {\n            line: 77,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 77,\n            column: 25\n          },\n          end: {\n            line: 95,\n            column: 5\n          }\n        },\n        line: 77\n      },\n      \"5\": {\n        name: \"(anonymous_5)\",\n        decl: {\n          start: {\n            line: 80,\n            column: 22\n          },\n          end: {\n            line: 80,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 80,\n            column: 42\n          },\n          end: {\n            line: 82,\n            column: 9\n          }\n        },\n        line: 80\n      },\n      \"6\": {\n        name: \"(anonymous_6)\",\n        decl: {\n          start: {\n            line: 81,\n            column: 41\n          },\n          end: {\n            line: 81,\n            column: 42\n          }\n        },\n        loc: {\n          start: {\n            line: 81,\n            column: 54\n          },\n          end: {\n            line: 81,\n            column: 75\n          }\n        },\n        line: 81\n      },\n      \"7\": {\n        name: \"(anonymous_7)\",\n        decl: {\n          start: {\n            line: 84,\n            column: 18\n          },\n          end: {\n            line: 84,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 84,\n            column: 34\n          },\n          end: {\n            line: 90,\n            column: 13\n          }\n        },\n        line: 84\n      },\n      \"8\": {\n        name: \"(anonymous_8)\",\n        decl: {\n          start: {\n            line: 91,\n            column: 19\n          },\n          end: {\n            line: 91,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 91,\n            column: 30\n          },\n          end: {\n            line: 93,\n            column: 13\n          }\n        },\n        line: 91\n      },\n      \"9\": {\n        name: \"(anonymous_9)\",\n        decl: {\n          start: {\n            line: 96,\n            column: 4\n          },\n          end: {\n            line: 96,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 96,\n            column: 15\n          },\n          end: {\n            line: 116,\n            column: 5\n          }\n        },\n        line: 96\n      },\n      \"10\": {\n        name: \"(anonymous_10)\",\n        decl: {\n          start: {\n            line: 107,\n            column: 22\n          },\n          end: {\n            line: 107,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 107,\n            column: 31\n          },\n          end: {\n            line: 110,\n            column: 17\n          }\n        },\n        line: 107\n      },\n      \"11\": {\n        name: \"(anonymous_11)\",\n        decl: {\n          start: {\n            line: 111,\n            column: 23\n          },\n          end: {\n            line: 111,\n            column: 24\n          }\n        },\n        loc: {\n          start: {\n            line: 111,\n            column: 34\n          },\n          end: {\n            line: 113,\n            column: 17\n          }\n        },\n        line: 111\n      },\n      \"12\": {\n        name: \"(anonymous_12)\",\n        decl: {\n          start: {\n            line: 117,\n            column: 4\n          },\n          end: {\n            line: 117,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 117,\n            column: 15\n          },\n          end: {\n            line: 119,\n            column: 5\n          }\n        },\n        line: 117\n      },\n      \"13\": {\n        name: \"(anonymous_13)\",\n        decl: {\n          start: {\n            line: 120,\n            column: 35\n          },\n          end: {\n            line: 120,\n            column: 36\n          }\n        },\n        loc: {\n          start: {\n            line: 120,\n            column: 41\n          },\n          end: {\n            line: 127,\n            column: 5\n          }\n        },\n        line: 120\n      }\n    },\n    branchMap: {\n      \"0\": {\n        loc: {\n          start: {\n            line: 34,\n            column: 8\n          },\n          end: {\n            line: 61,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 34,\n            column: 8\n          },\n          end: {\n            line: 61,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 58,\n            column: 13\n          },\n          end: {\n            line: 61,\n            column: 9\n          }\n        }],\n        line: 34\n      },\n      \"1\": {\n        loc: {\n          start: {\n            line: 43,\n            column: 33\n          },\n          end: {\n            line: 43,\n            column: 51\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 43,\n            column: 33\n          },\n          end: {\n            line: 43,\n            column: 46\n          }\n        }, {\n          start: {\n            line: 43,\n            column: 50\n          },\n          end: {\n            line: 43,\n            column: 51\n          }\n        }],\n        line: 43\n      },\n      \"2\": {\n        loc: {\n          start: {\n            line: 44,\n            column: 38\n          },\n          end: {\n            line: 44,\n            column: 73\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 44,\n            column: 38\n          },\n          end: {\n            line: 44,\n            column: 68\n          }\n        }, {\n          start: {\n            line: 44,\n            column: 72\n          },\n          end: {\n            line: 44,\n            column: 73\n          }\n        }],\n        line: 44\n      },\n      \"3\": {\n        loc: {\n          start: {\n            line: 45,\n            column: 16\n          },\n          end: {\n            line: 47,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 45,\n            column: 16\n          },\n          end: {\n            line: 47,\n            column: 17\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 45\n      },\n      \"4\": {\n        loc: {\n          start: {\n            line: 48,\n            column: 39\n          },\n          end: {\n            line: 48,\n            column: 64\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 48,\n            column: 39\n          },\n          end: {\n            line: 48,\n            column: 59\n          }\n        }, {\n          start: {\n            line: 48,\n            column: 63\n          },\n          end: {\n            line: 48,\n            column: 64\n          }\n        }],\n        line: 48\n      },\n      \"5\": {\n        loc: {\n          start: {\n            line: 51,\n            column: 25\n          },\n          end: {\n            line: 51,\n            column: 50\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 51,\n            column: 25\n          },\n          end: {\n            line: 51,\n            column: 45\n          }\n        }, {\n          start: {\n            line: 51,\n            column: 49\n          },\n          end: {\n            line: 51,\n            column: 50\n          }\n        }],\n        line: 51\n      },\n      \"6\": {\n        loc: {\n          start: {\n            line: 53,\n            column: 23\n          },\n          end: {\n            line: 55,\n            column: 48\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 54,\n            column: 22\n          },\n          end: {\n            line: 54,\n            column: 26\n          }\n        }, {\n          start: {\n            line: 55,\n            column: 22\n          },\n          end: {\n            line: 55,\n            column: 48\n          }\n        }],\n        line: 53\n      },\n      \"7\": {\n        loc: {\n          start: {\n            line: 66,\n            column: 8\n          },\n          end: {\n            line: 75,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 66,\n            column: 8\n          },\n          end: {\n            line: 75,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 66\n      },\n      \"8\": {\n        loc: {\n          start: {\n            line: 68,\n            column: 16\n          },\n          end: {\n            line: 73,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 68,\n            column: 16\n          },\n          end: {\n            line: 73,\n            column: 17\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 68\n      },\n      \"9\": {\n        loc: {\n          start: {\n            line: 69,\n            column: 62\n          },\n          end: {\n            line: 69,\n            column: 87\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 69,\n            column: 62\n          },\n          end: {\n            line: 69,\n            column: 82\n          }\n        }, {\n          start: {\n            line: 69,\n            column: 86\n          },\n          end: {\n            line: 69,\n            column: 87\n          }\n        }],\n        line: 69\n      },\n      \"10\": {\n        loc: {\n          start: {\n            line: 70,\n            column: 20\n          },\n          end: {\n            line: 72,\n            column: 21\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 70,\n            column: 20\n          },\n          end: {\n            line: 72,\n            column: 21\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 70\n      },\n      \"11\": {\n        loc: {\n          start: {\n            line: 86,\n            column: 40\n          },\n          end: {\n            line: 86,\n            column: 65\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 86,\n            column: 40\n          },\n          end: {\n            line: 86,\n            column: 60\n          }\n        }, {\n          start: {\n            line: 86,\n            column: 64\n          },\n          end: {\n            line: 86,\n            column: 65\n          }\n        }],\n        line: 86\n      },\n      \"12\": {\n        loc: {\n          start: {\n            line: 92,\n            column: 33\n          },\n          end: {\n            line: 92,\n            column: 98\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 92,\n            column: 33\n          },\n          end: {\n            line: 92,\n            column: 52\n          }\n        }, {\n          start: {\n            line: 92,\n            column: 56\n          },\n          end: {\n            line: 92,\n            column: 98\n          }\n        }],\n        line: 92\n      },\n      \"13\": {\n        loc: {\n          start: {\n            line: 97,\n            column: 8\n          },\n          end: {\n            line: 115,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 97,\n            column: 8\n          },\n          end: {\n            line: 115,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 97\n      },\n      \"14\": {\n        loc: {\n          start: {\n            line: 103,\n            column: 30\n          },\n          end: {\n            line: 105,\n            column: 49\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 104,\n            column: 18\n          },\n          end: {\n            line: 104,\n            column: 67\n          }\n        }, {\n          start: {\n            line: 105,\n            column: 18\n          },\n          end: {\n            line: 105,\n            column: 49\n          }\n        }],\n        line: 103\n      },\n      \"15\": {\n        loc: {\n          start: {\n            line: 108,\n            column: 46\n          },\n          end: {\n            line: 108,\n            column: 82\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 108,\n            column: 62\n          },\n          end: {\n            line: 108,\n            column: 71\n          }\n        }, {\n          start: {\n            line: 108,\n            column: 74\n          },\n          end: {\n            line: 108,\n            column: 82\n          }\n        }],\n        line: 108\n      },\n      \"16\": {\n        loc: {\n          start: {\n            line: 112,\n            column: 37\n          },\n          end: {\n            line: 112,\n            column: 115\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 112,\n            column: 37\n          },\n          end: {\n            line: 112,\n            column: 56\n          }\n        }, {\n          start: {\n            line: 112,\n            column: 60\n          },\n          end: {\n            line: 112,\n            column: 115\n          }\n        }],\n        line: 112\n      },\n      \"17\": {\n        loc: {\n          start: {\n            line: 112,\n            column: 72\n          },\n          end: {\n            line: 112,\n            column: 106\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 112,\n            column: 88\n          },\n          end: {\n            line: 112,\n            column: 96\n          }\n        }, {\n          start: {\n            line: 112,\n            column: 99\n          },\n          end: {\n            line: 112,\n            column: 106\n          }\n        }],\n        line: 112\n      }\n    },\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0,\n      \"36\": 0,\n      \"37\": 0,\n      \"38\": 0,\n      \"39\": 0,\n      \"40\": 0,\n      \"41\": 0,\n      \"42\": 0,\n      \"43\": 0,\n      \"44\": 0,\n      \"45\": 0,\n      \"46\": 0,\n      \"47\": 0,\n      \"48\": 0,\n      \"49\": 0,\n      \"50\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0\n    },\n    b: {\n      \"0\": [0, 0],\n      \"1\": [0, 0],\n      \"2\": [0, 0],\n      \"3\": [0, 0],\n      \"4\": [0, 0],\n      \"5\": [0, 0],\n      \"6\": [0, 0],\n      \"7\": [0, 0],\n      \"8\": [0, 0],\n      \"9\": [0, 0],\n      \"10\": [0, 0],\n      \"11\": [0, 0],\n      \"12\": [0, 0],\n      \"13\": [0, 0],\n      \"14\": [0, 0],\n      \"15\": [0, 0],\n      \"16\": [0, 0],\n      \"17\": [0, 0]\n    },\n    inputSourceMap: {\n      version: 3,\n      file: \"ccp-dialog.component.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contract-management\\\\components\\\\ccps-list\\\\ccp-dialog\\\\ccp-dialog.component.ts\"],\n      names: [],\n      mappings: \";;;AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,EAAU,MAAM,eAAe,CAAC;AAC1D,OAAO,EACL,WAAW,EAEX,mBAAmB,EACnB,UAAU,GACX,MAAM,gBAAgB,CAAC;AACxB,OAAO,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAC3D,OAAO,EACL,eAAe,EACf,eAAe,EACf,YAAY,GACb,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,kBAAkB,EAAE,MAAM,8BAA8B,CAAC;AAClE,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AAEzD,OAAO,EAAE,UAAU,EAAE,MAAM,2CAA2C,CAAC;AACvE,OAAO,EAAE,qBAAqB,EAAE,MAAM,uDAAuD,CAAC;AAC9F,OAAO,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAC9D,OAAO,EAAE,oBAAoB,EAAE,MAAM,cAAc,CAAC;AACpD,OAAO,EAAE,GAAG,EAAE,MAAM,gBAAgB,CAAC;AACrC,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAkBxC,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IAY7B,YACmB,SAA2C,EAC3C,WAAwB,EACxB,UAAsB,EACtB,qBAA4C,EAC5C,KAAmB,EAE7B,IAA8D;QANpD,cAAS,GAAT,SAAS,CAAkC;QAC3C,gBAAW,GAAX,WAAW,CAAa;QACxB,eAAU,GAAV,UAAU,CAAY;QACtB,0BAAqB,GAArB,qBAAqB,CAAuB;QAC5C,UAAK,GAAL,KAAK,CAAc;QAE7B,SAAI,GAAJ,IAAI,CAA0D;QAlBvE,YAAO,GAAc,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;YAC1C,mBAAmB,EAAE,CAAC,EAAE,EAAE,UAAU,CAAC,QAAQ,CAAC;YAC9C,wBAAwB,EAAE,CAAC,EAAE,EAAE,UAAU,CAAC,QAAQ,CAAC;YACnD,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACvD,eAAe,EAAE,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;SAC7C,CAAC,CAAC;QAEH,uBAAkB,GAAG,CAAC,CAAC;QACvB,yBAAoB,GAAG,CAAC,CAAC;QACzB,mBAAc,GAAG,CAAC,CAAC;QAWjB,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YAClB,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;gBACtB,mBAAmB,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,mBAAmB;gBACtD,wBAAwB,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,wBAAwB;gBAChE,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK;gBAC1B,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,eAAe;aAC/C,CAAC,CAAC;YACH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,CAAC;YAErC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,aAAa,CAAC,CAAC,OAAO,EAAE,EAAE;gBAC7D,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,IAAI,CAAC,CAAC;gBACpC,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,eAAe,IAAI,CAAC,CAAC;gBAC1D,IAAI,QAAQ,GAAG,aAAa,EAAE,CAAC;oBAC7B,OAAO,EAAE,gBAAgB,EAAE,IAAI,EAAE,CAAC;gBACpC,CAAC;gBACD,MAAM,aAAa,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,CAAC,GAAG,QAAQ,CAAC;gBAC7D,MAAM,cAAc,GAClB,IAAI,CAAC,kBAAkB;oBACvB,CAAC,IAAI,CAAC,oBAAoB;wBACxB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,CAAC;wBAC3B,aAAa,CAAC,CAAC;gBACnB,OAAO,aAAa,IAAI,cAAc;oBACpC,CAAC,CAAC,IAAI;oBACN,CAAC,CAAC,EAAE,gBAAgB,EAAE,IAAI,EAAE,CAAC;YACjC,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,OAAO,EAAE,CAAC;YAC/C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC;QACD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;IACtD,CAAC;IAED,QAAQ;QACN,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YACnB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC1D,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;oBACnB,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;oBACrE,IAAI,KAAK,GAAG,UAAU,EAAE,CAAC;wBACvB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,SAAS,CAAC,EAAE,gBAAgB,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnE,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,kBAAkB;QACxB,IAAI,CAAC,qBAAqB;aACvB,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;aACxC,IAAI,CACH,GAAG,CAAC,CAAC,cAAc,EAAE,EAAE;YACrB,OAAO,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QACtE,CAAC,CAAC,CACH;aACA,SAAS,CAAC;YACT,IAAI,EAAE,CAAC,UAAU,EAAE,EAAE;gBACnB,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC;gBACrC,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,CAAC;gBAClD,IAAI,CAAC,cAAc;oBACjB,IAAI,CAAC,kBAAkB;wBACvB,CAAC,IAAI,CAAC,oBAAoB,GAAG,eAAe,CAAC,CAAC;YAClD,CAAC;YACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,0CAA0C,CAAC,CAAC;YACtF,CAAC;SACF,CAAC,CAAC;IACP,CAAC;IAED,QAAQ;QACN,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACvB,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YAC7C,MAAM,OAAO,GAAG;gBACd,GAAG,SAAS;gBACZ,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU;aACjC,CAAC;YAEF,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG;gBAC7B,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC;gBACnD,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAEpC,SAAS,CAAC,SAAS,CAAC;gBAClB,IAAI,EAAE,CAAC,GAAG,EAAE,EAAE;oBACZ,IAAI,CAAC,KAAK,CAAC,OAAO,CAChB,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,eAAe,CAC3D,CAAC;oBACF,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAC5B,CAAC;gBACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;oBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,YAAY,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,SAAS,CAC/E,CAAC;gBACJ,CAAC;aACF,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,QAAQ;QACN,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;IACzB,CAAC;;;;;;;gDAtGE,MAAM,SAAC,eAAe;;;AAlBd,kBAAkB;IAhB9B,SAAS,CAAC;QACT,QAAQ,EAAE,gBAAgB;QAC1B,UAAU,EAAE,IAAI;QAChB,OAAO,EAAE;YACP,mBAAmB;YACnB,eAAe;YACf,eAAe;YACf,kBAAkB;YAClB,cAAc;YACd,aAAa;YACb,oBAAoB;YACpB,YAAY;SACb;QACD,8BAA0C;;KAE3C,CAAC;GACW,kBAAkB,CAyH9B\",\n      sourcesContent: [\"import { Component, Inject, OnInit } from '@angular/core';\\nimport {\\n  FormBuilder,\\n  FormGroup,\\n  ReactiveFormsModule,\\n  Validators,\\n} from '@angular/forms';\\nimport { MatButtonModule } from '@angular/material/button';\\nimport {\\n  MAT_DIALOG_DATA,\\n  MatDialogModule,\\n  MatDialogRef,\\n} from '@angular/material/dialog';\\nimport { MatFormFieldModule } from '@angular/material/form-field';\\nimport { MatIconModule } from '@angular/material/icon';\\nimport { MatInputModule } from '@angular/material/input';\\nimport { CCP } from '@contract-management/models/ccp.model';\\nimport { CcpService } from '@contract-management/services/ccp.service';\\nimport { ContractValuesService } from '@contract-management/services/contract-values.service';\\nimport { AlertService } from '@shared/services/alert.service';\\nimport { NgxCurrencyDirective } from 'ngx-currency';\\nimport { map } from 'rxjs/operators';\\nimport { CurrencyPipe } from '@angular/common';\\n\\n@Component({\\n  selector: 'app-ccp-dialog',\\n  standalone: true,\\n  imports: [\\n    ReactiveFormsModule,\\n    MatDialogModule,\\n    MatButtonModule,\\n    MatFormFieldModule,\\n    MatInputModule,\\n    MatIconModule,\\n    NgxCurrencyDirective,\\n    CurrencyPipe,\\n  ],\\n  templateUrl: './ccp-dialog.component.html',\\n  styleUrl: './ccp-dialog.component.scss',\\n})\\nexport class CcpDialogComponent implements OnInit {\\n  ccpForm: FormGroup = this.formBuilder.group({\\n    expenseObjectUseCcp: ['', Validators.required],\\n    expenseObjectDescription: ['', Validators.required],\\n    value: [null, [Validators.required, Validators.min(0)]],\\n    additionalValue: [null, [Validators.min(0)]],\\n  });\\n\\n  totalContractValue = 0;\\n  currentTotalCcpValue = 0;\\n  remainingValue = 0;\\n\\n  constructor(\\n    private readonly dialogRef: MatDialogRef<CcpDialogComponent>,\\n    private readonly formBuilder: FormBuilder,\\n    private readonly ccpService: CcpService,\\n    private readonly contractValuesService: ContractValuesService,\\n    private readonly alert: AlertService,\\n    @Inject(MAT_DIALOG_DATA)\\n    public data: { ccp?: CCP; contractId: number; totalCcpValue: number },\\n  ) {\\n    if (this.data.ccp) {\\n      this.ccpForm.patchValue({\\n        expenseObjectUseCcp: this.data.ccp.expenseObjectUseCcp,\\n        expenseObjectDescription: this.data.ccp.expenseObjectDescription,\\n        value: this.data.ccp.value,\\n        additionalValue: this.data.ccp.additionalValue,\\n      });\\n      this.ccpForm.get('value')?.disable();\\n\\n      this.ccpForm.get('additionalValue')?.addValidators((control) => {\\n        const newValue = control.value || 0;\\n        const previousValue = this.data.ccp?.additionalValue || 0;\\n        if (newValue < previousValue) {\\n          return { lessThanPrevious: true };\\n        }\\n        const totalCcpValue = (this.data.ccp?.value || 0) + newValue;\\n        const availableValue =\\n          this.totalContractValue -\\n          (this.currentTotalCcpValue -\\n            (this.data.ccp?.value || 0) -\\n            previousValue);\\n        return totalCcpValue <= availableValue\\n          ? null\\n          : { maxValueExceeded: true };\\n      });\\n    } else {\\n      this.ccpForm.get('additionalValue')?.disable();\\n      this.ccpForm.get('additionalValue')?.setValue(0);\\n    }\\n    this.currentTotalCcpValue = this.data.totalCcpValue;\\n  }\\n\\n  ngOnInit(): void {\\n    this.loadContractValues();\\n\\n    if (!this.data.ccp) {\\n      this.ccpForm.get('value')?.valueChanges.subscribe((value) => {\\n        if (value !== null) {\\n          const maxAllowed = this.remainingValue + (this.data.ccp?.value || 0);\\n          if (value > maxAllowed) {\\n            this.ccpForm.get('value')?.setErrors({ maxValueExceeded: true });\\n          }\\n        }\\n      });\\n    }\\n  }\\n\\n  private loadContractValues(): void {\\n    this.contractValuesService\\n      .getAllByContractId(this.data.contractId)\\n      .pipe(\\n        map((contractValues) => {\\n          return contractValues.reduce((sum, cv) => sum + cv.numericValue, 0);\\n        }),\\n      )\\n      .subscribe({\\n        next: (totalValue) => {\\n          this.totalContractValue = totalValue;\\n          const currentCcpValue = this.data.ccp?.value || 0;\\n          this.remainingValue =\\n            this.totalContractValue -\\n            (this.currentTotalCcpValue - currentCcpValue);\\n        },\\n        error: (error) => {\\n          this.alert.error(error.error?.detail ?? 'Error al cargar los valores del contrato');\\n        },\\n      });\\n  }\\n\\n  onSubmit(): void {\\n    if (this.ccpForm.valid) {\\n      const formValue = this.ccpForm.getRawValue();\\n      const ccpData = {\\n        ...formValue,\\n        contractId: this.data.contractId,\\n      };\\n\\n      const operation = this.data.ccp\\n        ? this.ccpService.update(this.data.ccp.id, ccpData)\\n        : this.ccpService.create(ccpData);\\n\\n      operation.subscribe({\\n        next: (ccp) => {\\n          this.alert.success(\\n            `CCP ${this.data.ccp ? 'editado' : 'creado'} exitosamente`,\\n          );\\n          this.dialogRef.close(ccp);\\n        },\\n        error: (error) => {\\n          this.alert.error(\\n            error.error?.detail ?? `Error al ${this.data.ccp ? 'editar' : 'crear'} el CCP`\\n          );\\n        },\\n      });\\n    }\\n  }\\n\\n  onCancel(): void {\\n    this.dialogRef.close();\\n  }\\n}\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"7987a9396d73cf773109d2c9c02e954599cb64af\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_25a3ximgx8 = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_25a3ximgx8();\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./ccp-dialog.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./ccp-dialog.component.scss?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { CcpService } from '@contract-management/services/ccp.service';\nimport { ContractValuesService } from '@contract-management/services/contract-values.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { NgxCurrencyDirective } from 'ngx-currency';\nimport { map } from 'rxjs/operators';\nimport { CurrencyPipe } from '@angular/common';\ncov_25a3ximgx8().s[0]++;\nlet CcpDialogComponent = class CcpDialogComponent {\n  constructor(dialogRef, formBuilder, ccpService, contractValuesService, alert, data) {\n    cov_25a3ximgx8().f[0]++;\n    cov_25a3ximgx8().s[1]++;\n    this.dialogRef = dialogRef;\n    cov_25a3ximgx8().s[2]++;\n    this.formBuilder = formBuilder;\n    cov_25a3ximgx8().s[3]++;\n    this.ccpService = ccpService;\n    cov_25a3ximgx8().s[4]++;\n    this.contractValuesService = contractValuesService;\n    cov_25a3ximgx8().s[5]++;\n    this.alert = alert;\n    cov_25a3ximgx8().s[6]++;\n    this.data = data;\n    cov_25a3ximgx8().s[7]++;\n    this.ccpForm = this.formBuilder.group({\n      expenseObjectUseCcp: ['', Validators.required],\n      expenseObjectDescription: ['', Validators.required],\n      value: [null, [Validators.required, Validators.min(0)]],\n      additionalValue: [null, [Validators.min(0)]]\n    });\n    cov_25a3ximgx8().s[8]++;\n    this.totalContractValue = 0;\n    cov_25a3ximgx8().s[9]++;\n    this.currentTotalCcpValue = 0;\n    cov_25a3ximgx8().s[10]++;\n    this.remainingValue = 0;\n    cov_25a3ximgx8().s[11]++;\n    if (this.data.ccp) {\n      cov_25a3ximgx8().b[0][0]++;\n      cov_25a3ximgx8().s[12]++;\n      this.ccpForm.patchValue({\n        expenseObjectUseCcp: this.data.ccp.expenseObjectUseCcp,\n        expenseObjectDescription: this.data.ccp.expenseObjectDescription,\n        value: this.data.ccp.value,\n        additionalValue: this.data.ccp.additionalValue\n      });\n      cov_25a3ximgx8().s[13]++;\n      this.ccpForm.get('value')?.disable();\n      cov_25a3ximgx8().s[14]++;\n      this.ccpForm.get('additionalValue')?.addValidators(control => {\n        cov_25a3ximgx8().f[1]++;\n        const newValue = (cov_25a3ximgx8().s[15]++, (cov_25a3ximgx8().b[1][0]++, control.value) || (cov_25a3ximgx8().b[1][1]++, 0));\n        const previousValue = (cov_25a3ximgx8().s[16]++, (cov_25a3ximgx8().b[2][0]++, this.data.ccp?.additionalValue) || (cov_25a3ximgx8().b[2][1]++, 0));\n        cov_25a3ximgx8().s[17]++;\n        if (newValue < previousValue) {\n          cov_25a3ximgx8().b[3][0]++;\n          cov_25a3ximgx8().s[18]++;\n          return {\n            lessThanPrevious: true\n          };\n        } else {\n          cov_25a3ximgx8().b[3][1]++;\n        }\n        const totalCcpValue = (cov_25a3ximgx8().s[19]++, ((cov_25a3ximgx8().b[4][0]++, this.data.ccp?.value) || (cov_25a3ximgx8().b[4][1]++, 0)) + newValue);\n        const availableValue = (cov_25a3ximgx8().s[20]++, this.totalContractValue - (this.currentTotalCcpValue - ((cov_25a3ximgx8().b[5][0]++, this.data.ccp?.value) || (cov_25a3ximgx8().b[5][1]++, 0)) - previousValue));\n        cov_25a3ximgx8().s[21]++;\n        return totalCcpValue <= availableValue ? (cov_25a3ximgx8().b[6][0]++, null) : (cov_25a3ximgx8().b[6][1]++, {\n          maxValueExceeded: true\n        });\n      });\n    } else {\n      cov_25a3ximgx8().b[0][1]++;\n      cov_25a3ximgx8().s[22]++;\n      this.ccpForm.get('additionalValue')?.disable();\n      cov_25a3ximgx8().s[23]++;\n      this.ccpForm.get('additionalValue')?.setValue(0);\n    }\n    cov_25a3ximgx8().s[24]++;\n    this.currentTotalCcpValue = this.data.totalCcpValue;\n  }\n  ngOnInit() {\n    cov_25a3ximgx8().f[2]++;\n    cov_25a3ximgx8().s[25]++;\n    this.loadContractValues();\n    cov_25a3ximgx8().s[26]++;\n    if (!this.data.ccp) {\n      cov_25a3ximgx8().b[7][0]++;\n      cov_25a3ximgx8().s[27]++;\n      this.ccpForm.get('value')?.valueChanges.subscribe(value => {\n        cov_25a3ximgx8().f[3]++;\n        cov_25a3ximgx8().s[28]++;\n        if (value !== null) {\n          cov_25a3ximgx8().b[8][0]++;\n          const maxAllowed = (cov_25a3ximgx8().s[29]++, this.remainingValue + ((cov_25a3ximgx8().b[9][0]++, this.data.ccp?.value) || (cov_25a3ximgx8().b[9][1]++, 0)));\n          cov_25a3ximgx8().s[30]++;\n          if (value > maxAllowed) {\n            cov_25a3ximgx8().b[10][0]++;\n            cov_25a3ximgx8().s[31]++;\n            this.ccpForm.get('value')?.setErrors({\n              maxValueExceeded: true\n            });\n          } else {\n            cov_25a3ximgx8().b[10][1]++;\n          }\n        } else {\n          cov_25a3ximgx8().b[8][1]++;\n        }\n      });\n    } else {\n      cov_25a3ximgx8().b[7][1]++;\n    }\n  }\n  loadContractValues() {\n    cov_25a3ximgx8().f[4]++;\n    cov_25a3ximgx8().s[32]++;\n    this.contractValuesService.getAllByContractId(this.data.contractId).pipe(map(contractValues => {\n      cov_25a3ximgx8().f[5]++;\n      cov_25a3ximgx8().s[33]++;\n      return contractValues.reduce((sum, cv) => {\n        cov_25a3ximgx8().f[6]++;\n        cov_25a3ximgx8().s[34]++;\n        return sum + cv.numericValue;\n      }, 0);\n    })).subscribe({\n      next: totalValue => {\n        cov_25a3ximgx8().f[7]++;\n        cov_25a3ximgx8().s[35]++;\n        this.totalContractValue = totalValue;\n        const currentCcpValue = (cov_25a3ximgx8().s[36]++, (cov_25a3ximgx8().b[11][0]++, this.data.ccp?.value) || (cov_25a3ximgx8().b[11][1]++, 0));\n        cov_25a3ximgx8().s[37]++;\n        this.remainingValue = this.totalContractValue - (this.currentTotalCcpValue - currentCcpValue);\n      },\n      error: error => {\n        cov_25a3ximgx8().f[8]++;\n        cov_25a3ximgx8().s[38]++;\n        this.alert.error((cov_25a3ximgx8().b[12][0]++, error.error?.detail) ?? (cov_25a3ximgx8().b[12][1]++, 'Error al cargar los valores del contrato'));\n      }\n    });\n  }\n  onSubmit() {\n    cov_25a3ximgx8().f[9]++;\n    cov_25a3ximgx8().s[39]++;\n    if (this.ccpForm.valid) {\n      cov_25a3ximgx8().b[13][0]++;\n      const formValue = (cov_25a3ximgx8().s[40]++, this.ccpForm.getRawValue());\n      const ccpData = (cov_25a3ximgx8().s[41]++, {\n        ...formValue,\n        contractId: this.data.contractId\n      });\n      const operation = (cov_25a3ximgx8().s[42]++, this.data.ccp ? (cov_25a3ximgx8().b[14][0]++, this.ccpService.update(this.data.ccp.id, ccpData)) : (cov_25a3ximgx8().b[14][1]++, this.ccpService.create(ccpData)));\n      cov_25a3ximgx8().s[43]++;\n      operation.subscribe({\n        next: ccp => {\n          cov_25a3ximgx8().f[10]++;\n          cov_25a3ximgx8().s[44]++;\n          this.alert.success(`CCP ${this.data.ccp ? (cov_25a3ximgx8().b[15][0]++, 'editado') : (cov_25a3ximgx8().b[15][1]++, 'creado')} exitosamente`);\n          cov_25a3ximgx8().s[45]++;\n          this.dialogRef.close(ccp);\n        },\n        error: error => {\n          cov_25a3ximgx8().f[11]++;\n          cov_25a3ximgx8().s[46]++;\n          this.alert.error((cov_25a3ximgx8().b[16][0]++, error.error?.detail) ?? (cov_25a3ximgx8().b[16][1]++, `Error al ${this.data.ccp ? (cov_25a3ximgx8().b[17][0]++, 'editar') : (cov_25a3ximgx8().b[17][1]++, 'crear')} el CCP`));\n        }\n      });\n    } else {\n      cov_25a3ximgx8().b[13][1]++;\n    }\n  }\n  onCancel() {\n    cov_25a3ximgx8().f[12]++;\n    cov_25a3ximgx8().s[47]++;\n    this.dialogRef.close();\n  }\n  static {\n    cov_25a3ximgx8().s[48]++;\n    this.ctorParameters = () => {\n      cov_25a3ximgx8().f[13]++;\n      cov_25a3ximgx8().s[49]++;\n      return [{\n        type: MatDialogRef\n      }, {\n        type: FormBuilder\n      }, {\n        type: CcpService\n      }, {\n        type: ContractValuesService\n      }, {\n        type: AlertService\n      }, {\n        type: undefined,\n        decorators: [{\n          type: Inject,\n          args: [MAT_DIALOG_DATA]\n        }]\n      }];\n    };\n  }\n};\ncov_25a3ximgx8().s[50]++;\nCcpDialogComponent = __decorate([Component({\n  selector: 'app-ccp-dialog',\n  standalone: true,\n  imports: [ReactiveFormsModule, MatDialogModule, MatButtonModule, MatFormFieldModule, MatInputModule, MatIconModule, NgxCurrencyDirective, CurrencyPipe],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], CcpDialogComponent);\nexport { CcpDialogComponent };", "map": {"version": 3, "names": ["cov_25a3ximgx8", "actualCoverage", "Component", "Inject", "FormBuilder", "ReactiveFormsModule", "Validators", "MatButtonModule", "MAT_DIALOG_DATA", "MatDialogModule", "MatDialogRef", "MatFormFieldModule", "MatIconModule", "MatInputModule", "CcpService", "ContractValuesService", "AlertService", "NgxCurrencyDirective", "map", "C<PERSON><PERSON>cyPipe", "s", "CcpDialogComponent", "constructor", "dialogRef", "formBuilder", "ccpService", "contractValuesService", "alert", "data", "f", "ccpForm", "group", "expenseObjectUseCcp", "required", "expenseObjectDescription", "value", "min", "additionalValue", "totalContractValue", "currentTotalCcpValue", "remainingValue", "ccp", "b", "patchValue", "get", "disable", "addValidators", "control", "newValue", "previousValue", "lessThanPrevious", "totalCcpValue", "availableValue", "maxValueExceeded", "setValue", "ngOnInit", "loadContractValues", "valueChanges", "subscribe", "maxAllowed", "setErrors", "getAllByContractId", "contractId", "pipe", "contractValues", "reduce", "sum", "cv", "numericValue", "next", "totalValue", "currentCcpValue", "error", "detail", "onSubmit", "valid", "formValue", "getRawValue", "ccpData", "operation", "update", "id", "create", "success", "close", "onCancel", "args", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\components\\ccps-list\\ccp-dialog\\ccp-dialog.component.ts"], "sourcesContent": ["import { Component, Inject, OnInit } from '@angular/core';\nimport {\n  <PERSON><PERSON><PERSON>er,\n  FormGroup,\n  ReactiveFormsModule,\n  Validators,\n} from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport {\n  MAT_DIALOG_DATA,\n  MatDialogModule,\n  MatDialogRef,\n} from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { CCP } from '@contract-management/models/ccp.model';\nimport { CcpService } from '@contract-management/services/ccp.service';\nimport { ContractValuesService } from '@contract-management/services/contract-values.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { NgxCurrencyDirective } from 'ngx-currency';\nimport { map } from 'rxjs/operators';\nimport { CurrencyPipe } from '@angular/common';\n\n@Component({\n  selector: 'app-ccp-dialog',\n  standalone: true,\n  imports: [\n    ReactiveFormsModule,\n    MatDialogModule,\n    MatButtonModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatIconModule,\n    NgxCurrencyDirective,\n    CurrencyPipe,\n  ],\n  templateUrl: './ccp-dialog.component.html',\n  styleUrl: './ccp-dialog.component.scss',\n})\nexport class CcpDialogComponent implements OnInit {\n  ccpForm: FormGroup = this.formBuilder.group({\n    expenseObjectUseCcp: ['', Validators.required],\n    expenseObjectDescription: ['', Validators.required],\n    value: [null, [Validators.required, Validators.min(0)]],\n    additionalValue: [null, [Validators.min(0)]],\n  });\n\n  totalContractValue = 0;\n  currentTotalCcpValue = 0;\n  remainingValue = 0;\n\n  constructor(\n    private readonly dialogRef: MatDialogRef<CcpDialogComponent>,\n    private readonly formBuilder: FormBuilder,\n    private readonly ccpService: CcpService,\n    private readonly contractValuesService: ContractValuesService,\n    private readonly alert: AlertService,\n    @Inject(MAT_DIALOG_DATA)\n    public data: { ccp?: CCP; contractId: number; totalCcpValue: number },\n  ) {\n    if (this.data.ccp) {\n      this.ccpForm.patchValue({\n        expenseObjectUseCcp: this.data.ccp.expenseObjectUseCcp,\n        expenseObjectDescription: this.data.ccp.expenseObjectDescription,\n        value: this.data.ccp.value,\n        additionalValue: this.data.ccp.additionalValue,\n      });\n      this.ccpForm.get('value')?.disable();\n\n      this.ccpForm.get('additionalValue')?.addValidators((control) => {\n        const newValue = control.value || 0;\n        const previousValue = this.data.ccp?.additionalValue || 0;\n        if (newValue < previousValue) {\n          return { lessThanPrevious: true };\n        }\n        const totalCcpValue = (this.data.ccp?.value || 0) + newValue;\n        const availableValue =\n          this.totalContractValue -\n          (this.currentTotalCcpValue -\n            (this.data.ccp?.value || 0) -\n            previousValue);\n        return totalCcpValue <= availableValue\n          ? null\n          : { maxValueExceeded: true };\n      });\n    } else {\n      this.ccpForm.get('additionalValue')?.disable();\n      this.ccpForm.get('additionalValue')?.setValue(0);\n    }\n    this.currentTotalCcpValue = this.data.totalCcpValue;\n  }\n\n  ngOnInit(): void {\n    this.loadContractValues();\n\n    if (!this.data.ccp) {\n      this.ccpForm.get('value')?.valueChanges.subscribe((value) => {\n        if (value !== null) {\n          const maxAllowed = this.remainingValue + (this.data.ccp?.value || 0);\n          if (value > maxAllowed) {\n            this.ccpForm.get('value')?.setErrors({ maxValueExceeded: true });\n          }\n        }\n      });\n    }\n  }\n\n  private loadContractValues(): void {\n    this.contractValuesService\n      .getAllByContractId(this.data.contractId)\n      .pipe(\n        map((contractValues) => {\n          return contractValues.reduce((sum, cv) => sum + cv.numericValue, 0);\n        }),\n      )\n      .subscribe({\n        next: (totalValue) => {\n          this.totalContractValue = totalValue;\n          const currentCcpValue = this.data.ccp?.value || 0;\n          this.remainingValue =\n            this.totalContractValue -\n            (this.currentTotalCcpValue - currentCcpValue);\n        },\n        error: (error) => {\n          this.alert.error(error.error?.detail ?? 'Error al cargar los valores del contrato');\n        },\n      });\n  }\n\n  onSubmit(): void {\n    if (this.ccpForm.valid) {\n      const formValue = this.ccpForm.getRawValue();\n      const ccpData = {\n        ...formValue,\n        contractId: this.data.contractId,\n      };\n\n      const operation = this.data.ccp\n        ? this.ccpService.update(this.data.ccp.id, ccpData)\n        : this.ccpService.create(ccpData);\n\n      operation.subscribe({\n        next: (ccp) => {\n          this.alert.success(\n            `CCP ${this.data.ccp ? 'editado' : 'creado'} exitosamente`,\n          );\n          this.dialogRef.close(ccp);\n        },\n        error: (error) => {\n          this.alert.error(\n            error.error?.detail ?? `Error al ${this.data.ccp ? 'editar' : 'crear'} el CCP`\n          );\n        },\n      });\n    }\n  }\n\n  onCancel(): void {\n    this.dialogRef.close();\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAsBS;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;AAtBT,SAASE,SAAS,EAAEC,MAAM,QAAgB,eAAe;AACzD,SACEC,WAAW,EAEXC,mBAAmB,EACnBC,UAAU,QACL,gBAAgB;AACvB,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SACEC,eAAe,EACfC,eAAe,EACfC,YAAY,QACP,0BAA0B;AACjC,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AAExD,SAASC,UAAU,QAAQ,2CAA2C;AACtE,SAASC,qBAAqB,QAAQ,uDAAuD;AAC7F,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,oBAAoB,QAAQ,cAAc;AACnD,SAASC,GAAG,QAAQ,gBAAgB;AACpC,SAASC,YAAY,QAAQ,iBAAiB;AAACnB,cAAA,GAAAoB,CAAA;AAkBxC,IAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EAY7BC,YACmBC,SAA2C,EAC3CC,WAAwB,EACxBC,UAAsB,EACtBC,qBAA4C,EAC5CC,KAAmB,EAE7BC,IAA8D;IAAA5B,cAAA,GAAA6B,CAAA;IAAA7B,cAAA,GAAAoB,CAAA;IANpD,KAAAG,SAAS,GAATA,SAAS;IAAkCvB,cAAA,GAAAoB,CAAA;IAC3C,KAAAI,WAAW,GAAXA,WAAW;IAAaxB,cAAA,GAAAoB,CAAA;IACxB,KAAAK,UAAU,GAAVA,UAAU;IAAYzB,cAAA,GAAAoB,CAAA;IACtB,KAAAM,qBAAqB,GAArBA,qBAAqB;IAAuB1B,cAAA,GAAAoB,CAAA;IAC5C,KAAAO,KAAK,GAALA,KAAK;IAAc3B,cAAA,GAAAoB,CAAA;IAE7B,KAAAQ,IAAI,GAAJA,IAAI;IAA0D5B,cAAA,GAAAoB,CAAA;IAlBvE,KAAAU,OAAO,GAAc,IAAI,CAACN,WAAW,CAACO,KAAK,CAAC;MAC1CC,mBAAmB,EAAE,CAAC,EAAE,EAAE1B,UAAU,CAAC2B,QAAQ,CAAC;MAC9CC,wBAAwB,EAAE,CAAC,EAAE,EAAE5B,UAAU,CAAC2B,QAAQ,CAAC;MACnDE,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC7B,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC8B,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACvDC,eAAe,EAAE,CAAC,IAAI,EAAE,CAAC/B,UAAU,CAAC8B,GAAG,CAAC,CAAC,CAAC,CAAC;KAC5C,CAAC;IAACpC,cAAA,GAAAoB,CAAA;IAEH,KAAAkB,kBAAkB,GAAG,CAAC;IAACtC,cAAA,GAAAoB,CAAA;IACvB,KAAAmB,oBAAoB,GAAG,CAAC;IAACvC,cAAA,GAAAoB,CAAA;IACzB,KAAAoB,cAAc,GAAG,CAAC;IAACxC,cAAA,GAAAoB,CAAA;IAWjB,IAAI,IAAI,CAACQ,IAAI,CAACa,GAAG,EAAE;MAAAzC,cAAA,GAAA0C,CAAA;MAAA1C,cAAA,GAAAoB,CAAA;MACjB,IAAI,CAACU,OAAO,CAACa,UAAU,CAAC;QACtBX,mBAAmB,EAAE,IAAI,CAACJ,IAAI,CAACa,GAAG,CAACT,mBAAmB;QACtDE,wBAAwB,EAAE,IAAI,CAACN,IAAI,CAACa,GAAG,CAACP,wBAAwB;QAChEC,KAAK,EAAE,IAAI,CAACP,IAAI,CAACa,GAAG,CAACN,KAAK;QAC1BE,eAAe,EAAE,IAAI,CAACT,IAAI,CAACa,GAAG,CAACJ;OAChC,CAAC;MAACrC,cAAA,GAAAoB,CAAA;MACH,IAAI,CAACU,OAAO,CAACc,GAAG,CAAC,OAAO,CAAC,EAAEC,OAAO,EAAE;MAAC7C,cAAA,GAAAoB,CAAA;MAErC,IAAI,CAACU,OAAO,CAACc,GAAG,CAAC,iBAAiB,CAAC,EAAEE,aAAa,CAAEC,OAAO,IAAI;QAAA/C,cAAA,GAAA6B,CAAA;QAC7D,MAAMmB,QAAQ,IAAAhD,cAAA,GAAAoB,CAAA,QAAG,CAAApB,cAAA,GAAA0C,CAAA,UAAAK,OAAO,CAACZ,KAAK,MAAAnC,cAAA,GAAA0C,CAAA,UAAI,CAAC;QACnC,MAAMO,aAAa,IAAAjD,cAAA,GAAAoB,CAAA,QAAG,CAAApB,cAAA,GAAA0C,CAAA,cAAI,CAACd,IAAI,CAACa,GAAG,EAAEJ,eAAe,MAAArC,cAAA,GAAA0C,CAAA,UAAI,CAAC;QAAC1C,cAAA,GAAAoB,CAAA;QAC1D,IAAI4B,QAAQ,GAAGC,aAAa,EAAE;UAAAjD,cAAA,GAAA0C,CAAA;UAAA1C,cAAA,GAAAoB,CAAA;UAC5B,OAAO;YAAE8B,gBAAgB,EAAE;UAAI,CAAE;QACnC,CAAC;UAAAlD,cAAA,GAAA0C,CAAA;QAAA;QACD,MAAMS,aAAa,IAAAnD,cAAA,GAAAoB,CAAA,QAAG,CAAC,CAAApB,cAAA,GAAA0C,CAAA,cAAI,CAACd,IAAI,CAACa,GAAG,EAAEN,KAAK,MAAAnC,cAAA,GAAA0C,CAAA,UAAI,CAAC,KAAIM,QAAQ;QAC5D,MAAMI,cAAc,IAAApD,cAAA,GAAAoB,CAAA,QAClB,IAAI,CAACkB,kBAAkB,IACtB,IAAI,CAACC,oBAAoB,IACvB,CAAAvC,cAAA,GAAA0C,CAAA,cAAI,CAACd,IAAI,CAACa,GAAG,EAAEN,KAAK,MAAAnC,cAAA,GAAA0C,CAAA,UAAI,CAAC,EAAC,GAC3BO,aAAa,CAAC;QAACjD,cAAA,GAAAoB,CAAA;QACnB,OAAO+B,aAAa,IAAIC,cAAc,IAAApD,cAAA,GAAA0C,CAAA,UAClC,IAAI,KAAA1C,cAAA,GAAA0C,CAAA,UACJ;UAAEW,gBAAgB,EAAE;QAAI,CAAE;MAChC,CAAC,CAAC;IACJ,CAAC,MAAM;MAAArD,cAAA,GAAA0C,CAAA;MAAA1C,cAAA,GAAAoB,CAAA;MACL,IAAI,CAACU,OAAO,CAACc,GAAG,CAAC,iBAAiB,CAAC,EAAEC,OAAO,EAAE;MAAC7C,cAAA,GAAAoB,CAAA;MAC/C,IAAI,CAACU,OAAO,CAACc,GAAG,CAAC,iBAAiB,CAAC,EAAEU,QAAQ,CAAC,CAAC,CAAC;IAClD;IAACtD,cAAA,GAAAoB,CAAA;IACD,IAAI,CAACmB,oBAAoB,GAAG,IAAI,CAACX,IAAI,CAACuB,aAAa;EACrD;EAEAI,QAAQA,CAAA;IAAAvD,cAAA,GAAA6B,CAAA;IAAA7B,cAAA,GAAAoB,CAAA;IACN,IAAI,CAACoC,kBAAkB,EAAE;IAACxD,cAAA,GAAAoB,CAAA;IAE1B,IAAI,CAAC,IAAI,CAACQ,IAAI,CAACa,GAAG,EAAE;MAAAzC,cAAA,GAAA0C,CAAA;MAAA1C,cAAA,GAAAoB,CAAA;MAClB,IAAI,CAACU,OAAO,CAACc,GAAG,CAAC,OAAO,CAAC,EAAEa,YAAY,CAACC,SAAS,CAAEvB,KAAK,IAAI;QAAAnC,cAAA,GAAA6B,CAAA;QAAA7B,cAAA,GAAAoB,CAAA;QAC1D,IAAIe,KAAK,KAAK,IAAI,EAAE;UAAAnC,cAAA,GAAA0C,CAAA;UAClB,MAAMiB,UAAU,IAAA3D,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACoB,cAAc,IAAI,CAAAxC,cAAA,GAAA0C,CAAA,cAAI,CAACd,IAAI,CAACa,GAAG,EAAEN,KAAK,MAAAnC,cAAA,GAAA0C,CAAA,UAAI,CAAC,EAAC;UAAC1C,cAAA,GAAAoB,CAAA;UACrE,IAAIe,KAAK,GAAGwB,UAAU,EAAE;YAAA3D,cAAA,GAAA0C,CAAA;YAAA1C,cAAA,GAAAoB,CAAA;YACtB,IAAI,CAACU,OAAO,CAACc,GAAG,CAAC,OAAO,CAAC,EAAEgB,SAAS,CAAC;cAAEP,gBAAgB,EAAE;YAAI,CAAE,CAAC;UAClE,CAAC;YAAArD,cAAA,GAAA0C,CAAA;UAAA;QACH,CAAC;UAAA1C,cAAA,GAAA0C,CAAA;QAAA;MACH,CAAC,CAAC;IACJ,CAAC;MAAA1C,cAAA,GAAA0C,CAAA;IAAA;EACH;EAEQc,kBAAkBA,CAAA;IAAAxD,cAAA,GAAA6B,CAAA;IAAA7B,cAAA,GAAAoB,CAAA;IACxB,IAAI,CAACM,qBAAqB,CACvBmC,kBAAkB,CAAC,IAAI,CAACjC,IAAI,CAACkC,UAAU,CAAC,CACxCC,IAAI,CACH7C,GAAG,CAAE8C,cAAc,IAAI;MAAAhE,cAAA,GAAA6B,CAAA;MAAA7B,cAAA,GAAAoB,CAAA;MACrB,OAAO4C,cAAc,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,EAAE,KAAK;QAAAnE,cAAA,GAAA6B,CAAA;QAAA7B,cAAA,GAAAoB,CAAA;QAAA,OAAA8C,GAAG,GAAGC,EAAE,CAACC,YAAY;MAAZ,CAAY,EAAE,CAAC,CAAC;IACrE,CAAC,CAAC,CACH,CACAV,SAAS,CAAC;MACTW,IAAI,EAAGC,UAAU,IAAI;QAAAtE,cAAA,GAAA6B,CAAA;QAAA7B,cAAA,GAAAoB,CAAA;QACnB,IAAI,CAACkB,kBAAkB,GAAGgC,UAAU;QACpC,MAAMC,eAAe,IAAAvE,cAAA,GAAAoB,CAAA,QAAG,CAAApB,cAAA,GAAA0C,CAAA,eAAI,CAACd,IAAI,CAACa,GAAG,EAAEN,KAAK,MAAAnC,cAAA,GAAA0C,CAAA,WAAI,CAAC;QAAC1C,cAAA,GAAAoB,CAAA;QAClD,IAAI,CAACoB,cAAc,GACjB,IAAI,CAACF,kBAAkB,IACtB,IAAI,CAACC,oBAAoB,GAAGgC,eAAe,CAAC;MACjD,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QAAAxE,cAAA,GAAA6B,CAAA;QAAA7B,cAAA,GAAAoB,CAAA;QACf,IAAI,CAACO,KAAK,CAAC6C,KAAK,CAAC,CAAAxE,cAAA,GAAA0C,CAAA,WAAA8B,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAAzE,cAAA,GAAA0C,CAAA,WAAI,0CAA0C,EAAC;MACrF;KACD,CAAC;EACN;EAEAgC,QAAQA,CAAA;IAAA1E,cAAA,GAAA6B,CAAA;IAAA7B,cAAA,GAAAoB,CAAA;IACN,IAAI,IAAI,CAACU,OAAO,CAAC6C,KAAK,EAAE;MAAA3E,cAAA,GAAA0C,CAAA;MACtB,MAAMkC,SAAS,IAAA5E,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACU,OAAO,CAAC+C,WAAW,EAAE;MAC5C,MAAMC,OAAO,IAAA9E,cAAA,GAAAoB,CAAA,QAAG;QACd,GAAGwD,SAAS;QACZd,UAAU,EAAE,IAAI,CAAClC,IAAI,CAACkC;OACvB;MAED,MAAMiB,SAAS,IAAA/E,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACQ,IAAI,CAACa,GAAG,IAAAzC,cAAA,GAAA0C,CAAA,WAC3B,IAAI,CAACjB,UAAU,CAACuD,MAAM,CAAC,IAAI,CAACpD,IAAI,CAACa,GAAG,CAACwC,EAAE,EAAEH,OAAO,CAAC,KAAA9E,cAAA,GAAA0C,CAAA,WACjD,IAAI,CAACjB,UAAU,CAACyD,MAAM,CAACJ,OAAO,CAAC;MAAC9E,cAAA,GAAAoB,CAAA;MAEpC2D,SAAS,CAACrB,SAAS,CAAC;QAClBW,IAAI,EAAG5B,GAAG,IAAI;UAAAzC,cAAA,GAAA6B,CAAA;UAAA7B,cAAA,GAAAoB,CAAA;UACZ,IAAI,CAACO,KAAK,CAACwD,OAAO,CAChB,OAAO,IAAI,CAACvD,IAAI,CAACa,GAAG,IAAAzC,cAAA,GAAA0C,CAAA,WAAG,SAAS,KAAA1C,cAAA,GAAA0C,CAAA,WAAG,QAAQ,gBAAe,CAC3D;UAAC1C,cAAA,GAAAoB,CAAA;UACF,IAAI,CAACG,SAAS,CAAC6D,KAAK,CAAC3C,GAAG,CAAC;QAC3B,CAAC;QACD+B,KAAK,EAAGA,KAAK,IAAI;UAAAxE,cAAA,GAAA6B,CAAA;UAAA7B,cAAA,GAAAoB,CAAA;UACf,IAAI,CAACO,KAAK,CAAC6C,KAAK,CACd,CAAAxE,cAAA,GAAA0C,CAAA,WAAA8B,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAAzE,cAAA,GAAA0C,CAAA,WAAI,YAAY,IAAI,CAACd,IAAI,CAACa,GAAG,IAAAzC,cAAA,GAAA0C,CAAA,WAAG,QAAQ,KAAA1C,cAAA,GAAA0C,CAAA,WAAG,OAAO,UAAS,EAC/E;QACH;OACD,CAAC;IACJ,CAAC;MAAA1C,cAAA,GAAA0C,CAAA;IAAA;EACH;EAEA2C,QAAQA,CAAA;IAAArF,cAAA,GAAA6B,CAAA;IAAA7B,cAAA,GAAAoB,CAAA;IACN,IAAI,CAACG,SAAS,CAAC6D,KAAK,EAAE;EACxB;;;;;;;;;;;;;;;;;;;gBAtGGjF,MAAM;UAAAmF,IAAA,GAAC9E,eAAe;QAAA;MAAA,E;;;;;AAlBda,kBAAkB,GAAAkE,UAAA,EAhB9BrF,SAAS,CAAC;EACTsF,QAAQ,EAAE,gBAAgB;EAC1BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPrF,mBAAmB,EACnBI,eAAe,EACfF,eAAe,EACfI,kBAAkB,EAClBE,cAAc,EACdD,aAAa,EACbK,oBAAoB,EACpBE,YAAY,CACb;EACDwE,QAAA,EAAAC,oBAA0C;;CAE3C,CAAC,C,EACWvE,kBAAkB,CAyH9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}