{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { ContractYearService } from './contract-year.service';\ndescribe('ContractYearService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/contract-year`;\n  const mockContractYear = {\n    id: 1,\n    year: 2024\n  };\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [ContractYearService]\n    });\n    service = TestBed.inject(ContractYearService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all contract years', () => {\n      const mockContractYears = [mockContractYear];\n      service.getAll().subscribe(contractYears => {\n        expect(contractYears).toEqual(mockContractYears);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractYears);\n    });\n  });\n  describe('getById', () => {\n    it('should return a contract year by id', () => {\n      const id = 1;\n      service.getById(id).subscribe(contractYear => {\n        expect(contractYear).toEqual(mockContractYear);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractYear);\n    });\n  });\n  describe('create', () => {\n    it('should create a new contract year', () => {\n      const newContractYear = {\n        year: 2024\n      };\n      service.create(newContractYear).subscribe(contractYear => {\n        expect(contractYear).toEqual(mockContractYear);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newContractYear);\n      req.flush(mockContractYear);\n    });\n  });\n  describe('update', () => {\n    it('should update a contract year', () => {\n      const id = 1;\n      const updateData = {\n        year: 2025\n      };\n      service.update(id, updateData).subscribe(contractYear => {\n        expect(contractYear).toEqual(mockContractYear);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockContractYear);\n    });\n  });\n  describe('delete', () => {\n    it('should delete a contract year', () => {\n      const id = 1;\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n  describe('getByYear', () => {\n    it('should return a contract year by year', () => {\n      const year = 2024;\n      service.getByYear(year).subscribe(contractYear => {\n        expect(contractYear).toEqual(mockContractYear);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/year/${year}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractYear);\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "ContractYearService", "describe", "service", "httpMock", "apiUrl", "mockContractYear", "id", "year", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockContractYears", "getAll", "subscribe", "contractYears", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "getById", "contractYear", "newContractYear", "create", "body", "updateData", "update", "delete", "nothing", "getByYear"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\services\\contract-year.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { ContractYear } from '@contract-management/models/contract-year.model';\nimport { environment } from '@env';\nimport { ContractYearService } from './contract-year.service';\n\ndescribe('ContractYearService', () => {\n  let service: ContractYearService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/contract-year`;\n\n  const mockContractYear: ContractYear = {\n    id: 1,\n    year: 2024,\n  };\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [ContractYearService],\n    });\n    service = TestBed.inject(ContractYearService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all contract years', () => {\n      const mockContractYears = [mockContractYear];\n\n      service.getAll().subscribe((contractYears) => {\n        expect(contractYears).toEqual(mockContractYears);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractYears);\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a contract year by id', () => {\n      const id = 1;\n\n      service.getById(id).subscribe((contractYear) => {\n        expect(contractYear).toEqual(mockContractYear);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractYear);\n    });\n  });\n\n  describe('create', () => {\n    it('should create a new contract year', () => {\n      const newContractYear: Omit<ContractYear, 'id'> = {\n        year: 2024,\n      };\n\n      service.create(newContractYear).subscribe((contractYear) => {\n        expect(contractYear).toEqual(mockContractYear);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newContractYear);\n      req.flush(mockContractYear);\n    });\n  });\n\n  describe('update', () => {\n    it('should update a contract year', () => {\n      const id = 1;\n      const updateData: Partial<ContractYear> = {\n        year: 2025,\n      };\n\n      service.update(id, updateData).subscribe((contractYear) => {\n        expect(contractYear).toEqual(mockContractYear);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockContractYear);\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete a contract year', () => {\n      const id = 1;\n\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n\n  describe('getByYear', () => {\n    it('should return a contract year by year', () => {\n      const year = 2024;\n\n      service.getByYear(year).subscribe((contractYear) => {\n        expect(contractYear).toEqual(mockContractYear);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/year/${year}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractYear);\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,mBAAmB,QAAQ,yBAAyB;AAE7DC,QAAQ,CAAC,qBAAqB,EAAE,MAAK;EACnC,IAAIC,OAA4B;EAChC,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,gBAAgB;EAEpD,MAAMC,gBAAgB,GAAiB;IACrCC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE;GACP;EAEDC,UAAU,CAAC,MAAK;IACdV,OAAO,CAACW,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACd,uBAAuB,CAAC;MAClCe,SAAS,EAAE,CAACX,mBAAmB;KAChC,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACc,MAAM,CAACZ,mBAAmB,CAAC;IAC7CG,QAAQ,GAAGL,OAAO,CAACc,MAAM,CAACf,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFgB,SAAS,CAAC,MAAK;IACbV,QAAQ,CAACW,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACd,OAAO,CAAC,CAACe,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFhB,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,kCAAkC,EAAE,MAAK;MAC1C,MAAMG,iBAAiB,GAAG,CAACb,gBAAgB,CAAC;MAE5CH,OAAO,CAACiB,MAAM,EAAE,CAACC,SAAS,CAAEC,aAAa,IAAI;QAC3CL,MAAM,CAACK,aAAa,CAAC,CAACC,OAAO,CAACJ,iBAAiB,CAAC;MAClD,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,iBAAiB,CAAC;IAC9B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjB,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBc,EAAE,CAAC,qCAAqC,EAAE,MAAK;MAC7C,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAAC2B,OAAO,CAACvB,EAAE,CAAC,CAACc,SAAS,CAAEU,YAAY,IAAI;QAC7Cd,MAAM,CAACc,YAAY,CAAC,CAACR,OAAO,CAACjB,gBAAgB,CAAC;MAChD,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvB,gBAAgB,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,mCAAmC,EAAE,MAAK;MAC3C,MAAMgB,eAAe,GAA6B;QAChDxB,IAAI,EAAE;OACP;MAEDL,OAAO,CAAC8B,MAAM,CAACD,eAAe,CAAC,CAACX,SAAS,CAAEU,YAAY,IAAI;QACzDd,MAAM,CAACc,YAAY,CAAC,CAACR,OAAO,CAACjB,gBAAgB,CAAC;MAChD,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACS,eAAe,CAAC;MACjDR,GAAG,CAACK,KAAK,CAACvB,gBAAgB,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,+BAA+B,EAAE,MAAK;MACvC,MAAMT,EAAE,GAAG,CAAC;MACZ,MAAM4B,UAAU,GAA0B;QACxC3B,IAAI,EAAE;OACP;MAEDL,OAAO,CAACiC,MAAM,CAAC7B,EAAE,EAAE4B,UAAU,CAAC,CAACd,SAAS,CAAEU,YAAY,IAAI;QACxDd,MAAM,CAACc,YAAY,CAAC,CAACR,OAAO,CAACjB,gBAAgB,CAAC;MAChD,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACY,UAAU,CAAC;MAC5CX,GAAG,CAACK,KAAK,CAACvB,gBAAgB,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,+BAA+B,EAAE,MAAK;MACvC,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAACkC,MAAM,CAAC9B,EAAE,CAAC,CAACc,SAAS,CAAC,MAAK;QAChCJ,MAAM,EAAE,CAACqB,OAAO,EAAE;MACpB,CAAC,CAAC;MAEF,MAAMd,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF3B,QAAQ,CAAC,WAAW,EAAE,MAAK;IACzBc,EAAE,CAAC,uCAAuC,EAAE,MAAK;MAC/C,MAAMR,IAAI,GAAG,IAAI;MAEjBL,OAAO,CAACoC,SAAS,CAAC/B,IAAI,CAAC,CAACa,SAAS,CAAEU,YAAY,IAAI;QACjDd,MAAM,CAACc,YAAY,CAAC,CAACR,OAAO,CAACjB,gBAAgB,CAAC;MAChD,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,SAASG,IAAI,EAAE,CAAC;MACxDS,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvB,gBAAgB,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}