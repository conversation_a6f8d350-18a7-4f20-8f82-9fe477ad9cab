import { HttpClientTestingModule } from '@angular/common/http/testing';
import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { Router } from '@angular/router';
import { ContractDetails } from '@contract-management/models/contract-details.model';
import { ContractYear } from '@contract-management/models/contract-year.model';
import { ContractYearService } from '@contract-management/services/contract-year.service';
import { ContractService } from '@contract-management/services/contract.service';
import { AlertService } from '@shared/services/alert.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { of, throwError } from 'rxjs';
import { ContractsListPageComponent } from './contracts-list-page.component';

describe('ContractsListPageComponent', () => {
  let component: ContractsListPageComponent;
  let fixture: ComponentFixture<ContractsListPageComponent>;
  let contractService: jasmine.SpyObj<ContractService>;
  let contractYearService: jasmine.SpyObj<ContractYearService>;
  let dialog: jasmine.SpyObj<MatDialog>;
  let router: jasmine.SpyObj<Router>;
  let spinner: jasmine.SpyObj<NgxSpinnerService>;
  let alert: jasmine.SpyObj<AlertService>;

  const mockContractDetails: ContractDetails[] = [
    {
      id: 1,
      contractNumber: 1,
      contractYear: { id: 1, year: 2024 },
      contractorIdNumber: 123456,
      fullName: 'John Doe',
      subscriptionDate: new Date('2024-01-01'),
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-12-31'),
      durationDays: 365,
      initialValue: 100000.0,
      totalValue: 100000.0,
      monthlyPayment: 8333.33,
      addition: false,
      hasCcp: true,
      object: 'Test contract',
      rup: false,
      cession: false,
      settled: false,
      selectionModalityName: 'Test modality',
      trackingTypeName: 'Test tracking',
      contractTypeName: 'Test type',
      statusName: 'Active',
      dependencyName: 'Test dependency',
      groupName: 'Test group',
      contractorEmail: '<EMAIL>',
      supervisorFullName: 'Test Supervisor',
      supervisorIdNumber: '987654',
      supervisorPosition: 'Test Position',
      contractorId: 1,
    },
  ];

  const mockContractYears: ContractYear[] = [
    { id: 1, year: 2024 },
    { id: 2, year: 2023 },
  ];

  beforeEach(async () => {
    contractService = jasmine.createSpyObj('ContractService', [
      'getAllDetails',
      'exportContracts',
      'getContractList',
    ]);
    contractYearService = jasmine.createSpyObj('ContractYearService', [
      'getAll',
    ]);
    dialog = jasmine.createSpyObj('MatDialog', ['open']);
    router = jasmine.createSpyObj('Router', ['navigate']);
    spinner = jasmine.createSpyObj('NgxSpinnerService', ['show', 'hide']);
    alert = jasmine.createSpyObj('AlertService', [
      'success',
      'error',
      'warning',
    ]);

    contractService.getAllDetails.and.returnValue(of(mockContractDetails));
    contractYearService.getAll.and.returnValue(of(mockContractYears));
    dialog.open.and.returnValue({
      afterClosed: () => of('created'),
    } as MatDialogRef<unknown, 'created' | 'updated' | undefined>);

    await TestBed.configureTestingModule({
      imports: [
        ContractsListPageComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
      ],
      providers: [
        { provide: ContractService, useValue: contractService },
        { provide: ContractYearService, useValue: contractYearService },
        { provide: MatDialog, useValue: dialog },
        { provide: Router, useValue: router },
        { provide: NgxSpinnerService, useValue: spinner },
        { provide: AlertService, useValue: alert },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ContractsListPageComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load contract details and years on init', fakeAsync(() => {
    contractService.getContractList.and.returnValue(of(mockContractDetails));
    contractYearService.getAll.and.returnValue(of(mockContractYears));

    component.ngOnInit();
    tick();

    expect(contractService.getContractList).toHaveBeenCalled();
    expect(contractYearService.getAll).toHaveBeenCalled();
    expect(component.allContractsData).toEqual(mockContractDetails);
    expect(component.contractYears).toEqual(mockContractYears);
    expect(spinner.show).toHaveBeenCalledTimes(2);
    expect(spinner.hide).toHaveBeenCalledTimes(2);
  }));

  it('should handle error when loading contract details', fakeAsync(() => {
    contractService.getContractList.and.returnValue(
      throwError(() => ({ error: 'Error' })),
    );
    contractYearService.getAll.and.returnValue(of(mockContractYears));

    component.ngOnInit();
    tick();

    expect(alert.error).toHaveBeenCalledWith(
      'Error al cargar la lista de contratos',
    );
    expect(spinner.hide).toHaveBeenCalled();
  }));

  it('should handle error when loading contract years', fakeAsync(() => {
    contractService.getContractList.and.returnValue(of(mockContractDetails));
    contractYearService.getAll.and.returnValue(
      throwError(() => ({ error: 'Error' })),
    );

    component.ngOnInit();
    tick();

    expect(alert.error).toHaveBeenCalledWith(
      'Error al cargar los años de contrato',
    );
    expect(spinner.hide).toHaveBeenCalled();
  }));

  it('should open contract dialog and reload on success', fakeAsync(() => {
    contractService.getContractList.and.returnValue(of(mockContractDetails));
    contractYearService.getAll.and.returnValue(of(mockContractYears));

    component.openContractDialog();
    tick();

    expect(dialog.open).toHaveBeenCalled();
    expect(contractService.getContractList).toHaveBeenCalledTimes(1);
  }));

  it('should handle edit contract navigation', () => {
    component.handleEditContract(mockContractDetails[0]);

    expect(router.navigate).toHaveBeenCalledWith(['/contratos', 1]);
  });

  it('should show warning when exporting without year selected', () => {
    component.selectedYear = null;
    component.exportToCSV();

    expect(alert.warning).toHaveBeenCalledWith(
      'Por favor seleccione un año para exportar',
    );
    expect(contractService.exportContracts).not.toHaveBeenCalled();
  });

  it('should export contracts to CSV successfully', fakeAsync(() => {
    const mockBlob = new Blob(['test'], { type: 'text/csv' });
    contractService.exportContracts.and.returnValue(of(mockBlob));
    component.selectedYear = 2024;

    const mockUrl = 'blob:test';
    spyOn(window.URL, 'createObjectURL').and.returnValue(mockUrl);
    spyOn(window.URL, 'revokeObjectURL');
    const mockLink = document.createElement('a');
    spyOn(mockLink, 'click');
    spyOn(document, 'createElement').and.returnValue(mockLink);

    component.exportToCSV();
    tick();

    expect(contractService.exportContracts).toHaveBeenCalledWith(2024);
    expect(window.URL.createObjectURL).toHaveBeenCalledWith(mockBlob);
    expect(mockLink.click).toHaveBeenCalled();
    expect(window.URL.revokeObjectURL).toHaveBeenCalledWith(mockUrl);
    expect(alert.success).toHaveBeenCalledWith(
      'Exportación completada exitosamente',
    );
  }));

  it('should handle error when exporting contracts', fakeAsync(() => {
    contractService.exportContracts.and.returnValue(
      throwError(() => ({ error: 'Error' })),
    );
    component.selectedYear = 2024;

    component.exportToCSV();
    tick();

    expect(alert.error).toHaveBeenCalledWith('Error al exportar contratos.');
    expect(spinner.hide).toHaveBeenCalled();
  }));

  it('should filter contracts by selected year', () => {
    component.allContractsData = mockContractDetails;
    component.selectedYear = 2024;

    component.onYearChange();

    expect(component.dataSource.data).toEqual(mockContractDetails);
  });

  it('should show all contracts when no year is selected', () => {
    component.allContractsData = mockContractDetails;
    component.selectedYear = null;

    component.onYearChange();

    expect(component.dataSource.data).toEqual(mockContractDetails);
  });
});
