{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { FormBuilder, ReactiveFormsModule } from '@angular/forms';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { AlertService } from '@shared/services/alert.service';\nimport { TaxBenefitsComponent } from './tax-benefits.component';\ndescribe('TaxBenefitsComponent', () => {\n  let component;\n  let fixture;\n  let alertService;\n  const mockInitialData = {\n    id: 1,\n    contractorContractId: 1,\n    bankId: 1,\n    accountNumber: '123456',\n    bankAccountTypeId: 1,\n    taxRegimeId: 1,\n    epsId: 1,\n    arlId: 1,\n    pensionFundId: 1,\n    hasDependents: true,\n    hasHousingInterest: true,\n    housingInterestAnnualPayment: 1000000,\n    hasPrepaidMedicine: true,\n    prepaidMedicineAnnualPayment: 500000,\n    hasAfcAccount: true,\n    hasVoluntarySavings: true,\n    afcAccountAnnualPayment: 300000,\n    voluntarySavingsAnnualPayment: 200000\n  };\n  beforeEach(() => {\n    const alertServiceSpy = jasmine.createSpyObj('AlertService', ['error']);\n    TestBed.configureTestingModule({\n      imports: [TaxBenefitsComponent, ReactiveFormsModule, BrowserAnimationsModule],\n      providers: [FormBuilder, {\n        provide: AlertService,\n        useValue: alertServiceSpy\n      }]\n    });\n    fixture = TestBed.createComponent(TaxBenefitsComponent);\n    component = fixture.componentInstance;\n    alertService = TestBed.inject(AlertService);\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  describe('Form Initialization', () => {\n    it('should initialize form with default values', () => {\n      expect(component.form.get('hasDependents')?.value).toBeFalse();\n      expect(component.form.get('hasHousingInterest')?.value).toBeFalse();\n      expect(component.form.get('hasPrepaidMedicine')?.value).toBeFalse();\n      expect(component.form.get('hasAfcAccount')?.value).toBeFalse();\n      expect(component.form.get('hasVoluntarySavings')?.value).toBeFalse();\n    });\n    it('should initialize form with disabled payment controls', () => {\n      expect(component.form.get('housingInterestAnnualPayment')?.disabled).toBeTrue();\n      expect(component.form.get('prepaidMedicineAnnualPayment')?.disabled).toBeTrue();\n      expect(component.form.get('afcAccountAnnualPayment')?.disabled).toBeTrue();\n      expect(component.form.get('voluntarySavingsAnnualPayment')?.disabled).toBeTrue();\n    });\n    it('should disable controls in supervisor mode', () => {\n      component.isSupervisor = true;\n      component.ngOnChanges({\n        isSupervisor: {\n          currentValue: true,\n          previousValue: false,\n          firstChange: false,\n          isFirstChange: () => false\n        }\n      });\n      expect(component.form.get('hasDependents')?.disabled).toBeTrue();\n      expect(component.form.get('hasHousingInterest')?.disabled).toBeTrue();\n      expect(component.form.get('hasPrepaidMedicine')?.disabled).toBeTrue();\n      expect(component.form.get('hasAfcAccount')?.disabled).toBeTrue();\n      expect(component.form.get('hasVoluntarySavings')?.disabled).toBeTrue();\n    });\n  });\n  describe('Form Data Loading', () => {\n    it('should load initial data correctly', () => {\n      component.initialData = mockInitialData;\n      component.ngOnChanges({\n        initialData: {\n          currentValue: mockInitialData,\n          previousValue: undefined,\n          firstChange: true,\n          isFirstChange: () => true\n        }\n      });\n      expect(component.form.get('hasDependents')?.value).toBeTrue();\n      expect(component.form.get('hasHousingInterest')?.value).toBeTrue();\n      expect(component.form.get('housingInterestAnnualPayment')?.value).toBe(1000000);\n      expect(component.form.get('hasPrepaidMedicine')?.value).toBeTrue();\n      expect(component.form.get('prepaidMedicineAnnualPayment')?.value).toBe(500000);\n      expect(component.form.get('hasAfcAccount')?.value).toBeTrue();\n      expect(component.form.get('afcAccountAnnualPayment')?.value).toBe(300000);\n      expect(component.form.get('hasVoluntarySavings')?.value).toBeTrue();\n      expect(component.form.get('voluntarySavingsAnnualPayment')?.value).toBe(200000);\n    });\n  });\n  describe('Form Validation', () => {\n    it('should validate payment amounts when enabled', () => {\n      component.form.get('hasHousingInterest')?.setValue(true);\n      component.form.get('housingInterestAnnualPayment')?.setValue(-1);\n      expect(component.form.get('housingInterestAnnualPayment')?.errors?.['min']).toBeTruthy();\n      component.form.get('housingInterestAnnualPayment')?.setValue(1000);\n      expect(component.form.get('housingInterestAnnualPayment')?.valid).toBeTrue();\n    });\n    it('should clear and disable payment amounts when checkbox is unchecked', () => {\n      component.form.get('hasHousingInterest')?.setValue(true);\n      component.form.get('housingInterestAnnualPayment')?.setValue(1000);\n      component.form.get('hasHousingInterest')?.setValue(false);\n      expect(component.form.get('housingInterestAnnualPayment')?.disabled).toBeTrue();\n      expect(component.form.get('housingInterestAnnualPayment')?.value).toBeNull();\n    });\n  });\n  describe('File Handling', () => {\n    it('should validate PDF file type', () => {\n      const pdfFile = new File([''], 'test.pdf', {\n        type: 'application/pdf'\n      });\n      const event = {\n        target: {\n          files: [pdfFile]\n        }\n      };\n      component.onFileSelected(event, 'dependents');\n      expect(component.fileNames.dependents).toBe('test.pdf');\n      expect(alertService.error).not.toHaveBeenCalled();\n    });\n    it('should reject non-PDF files', () => {\n      const jpgFile = new File([''], 'test.jpg', {\n        type: 'image/jpeg'\n      });\n      const event = {\n        target: {\n          files: [jpgFile]\n        }\n      };\n      component.onFileSelected(event, 'dependents');\n      expect(component.fileNames.dependents).toBe('');\n      expect(alertService.error).toHaveBeenCalledWith('Solo se permiten archivos PDF');\n    });\n    it('should reject files larger than 1MB', () => {\n      const largeFile = new File(['x'.repeat(1024 * 1024 + 1)], 'large.pdf', {\n        type: 'application/pdf'\n      });\n      const event = {\n        target: {\n          files: [largeFile]\n        }\n      };\n      component.onFileSelected(event, 'dependents');\n      expect(component.fileNames.dependents).toBe('');\n      expect(alertService.error).toHaveBeenCalledWith('El archivo no debe superar 1MB');\n    });\n  });\n  describe('Form Changes', () => {\n    it('should emit form changes', () => {\n      const emitSpy = spyOn(component.formChange, 'emit');\n      component.form.get('hasDependents')?.setValue(true);\n      expect(emitSpy).toHaveBeenCalled();\n    });\n  });\n  describe('Form Validity', () => {\n    it('should validate form when all required fields are filled', () => {\n      component.form.get('hasDependents')?.setValue(true);\n      const pdfFile = new File([''], 'test.pdf', {\n        type: 'application/pdf'\n      });\n      component.form.get('dependentsFile')?.setValue(pdfFile);\n      expect(component.isValid).toBeTrue();\n    });\n    it('should invalidate form when required files are missing', () => {\n      component.form.get('hasDependents')?.setValue(true);\n      expect(component.isValid).toBeFalse();\n    });\n  });\n});", "map": {"version": 3, "names": ["TestBed", "FormBuilder", "ReactiveFormsModule", "BrowserAnimationsModule", "AlertService", "TaxBenefitsComponent", "describe", "component", "fixture", "alertService", "mockInitialData", "id", "contractorContractId", "bankId", "accountNumber", "bankAccountTypeId", "taxRegimeId", "epsId", "arlId", "pensionFundId", "hasDependents", "hasHousingInterest", "housingInterestAnnualPayment", "hasPrepaidMedicine", "prepaidMedicineAnnualPayment", "hasAfcAccount", "hasVoluntarySavings", "afcAccountAnnualPayment", "voluntarySavingsAnnualPayment", "beforeEach", "alertServiceSpy", "jasmine", "createSpyObj", "configureTestingModule", "imports", "providers", "provide", "useValue", "createComponent", "componentInstance", "inject", "detectChanges", "it", "expect", "toBeTruthy", "form", "get", "value", "toBeFalse", "disabled", "toBeTrue", "isSupervisor", "ngOnChanges", "currentValue", "previousValue", "firstChange", "isFirstChange", "initialData", "undefined", "toBe", "setValue", "errors", "valid", "toBeNull", "pdfFile", "File", "type", "event", "target", "files", "onFileSelected", "fileNames", "dependents", "error", "not", "toHaveBeenCalled", "jpgFile", "toHaveBeenCalledWith", "largeFile", "repeat", "emitSpy", "spyOn", "formChange", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\components\\monthly-reports-tab\\monthly-report-dialog\\monthly-report-initial-documentation\\tax-benefits\\tax-benefits.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\nimport { FormBuilder, ReactiveFormsModule } from '@angular/forms';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { InitialReportDocumentation } from '@contractor-dashboard/models/initial-report-documentation.model';\nimport { AlertService } from '@shared/services/alert.service';\nimport { TaxBenefitsComponent } from './tax-benefits.component';\n\ndescribe('TaxBenefitsComponent', () => {\n  let component: TaxBenefitsComponent;\n  let fixture: ComponentFixture<TaxBenefitsComponent>;\n  let alertService: jasmine.SpyObj<AlertService>;\n\n  const mockInitialData: InitialReportDocumentation = {\n    id: 1,\n    contractorContractId: 1,\n    bankId: 1,\n    accountNumber: '123456',\n    bankAccountTypeId: 1,\n    taxRegimeId: 1,\n    epsId: 1,\n    arlId: 1,\n    pensionFundId: 1,\n    hasDependents: true,\n    hasHousingInterest: true,\n    housingInterestAnnualPayment: 1000000,\n    hasPrepaidMedicine: true,\n    prepaidMedicineAnnualPayment: 500000,\n    hasAfcAccount: true,\n    hasVoluntarySavings: true,\n    afcAccountAnnualPayment: 300000,\n    voluntarySavingsAnnualPayment: 200000,\n  };\n\n  beforeEach(() => {\n    const alertServiceSpy = jasmine.createSpyObj('AlertService', ['error']);\n\n    TestBed.configureTestingModule({\n      imports: [\n        TaxBenefitsComponent,\n        ReactiveFormsModule,\n        BrowserAnimationsModule,\n      ],\n      providers: [\n        FormBuilder,\n        { provide: AlertService, useValue: alertServiceSpy },\n      ],\n    });\n\n    fixture = TestBed.createComponent(TaxBenefitsComponent);\n    component = fixture.componentInstance;\n    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  describe('Form Initialization', () => {\n    it('should initialize form with default values', () => {\n      expect(component.form.get('hasDependents')?.value).toBeFalse();\n      expect(component.form.get('hasHousingInterest')?.value).toBeFalse();\n      expect(component.form.get('hasPrepaidMedicine')?.value).toBeFalse();\n      expect(component.form.get('hasAfcAccount')?.value).toBeFalse();\n      expect(component.form.get('hasVoluntarySavings')?.value).toBeFalse();\n    });\n\n    it('should initialize form with disabled payment controls', () => {\n      expect(\n        component.form.get('housingInterestAnnualPayment')?.disabled,\n      ).toBeTrue();\n      expect(\n        component.form.get('prepaidMedicineAnnualPayment')?.disabled,\n      ).toBeTrue();\n      expect(\n        component.form.get('afcAccountAnnualPayment')?.disabled,\n      ).toBeTrue();\n      expect(\n        component.form.get('voluntarySavingsAnnualPayment')?.disabled,\n      ).toBeTrue();\n    });\n\n    it('should disable controls in supervisor mode', () => {\n      component.isSupervisor = true;\n      component.ngOnChanges({\n        isSupervisor: {\n          currentValue: true,\n          previousValue: false,\n          firstChange: false,\n          isFirstChange: () => false,\n        },\n      });\n\n      expect(component.form.get('hasDependents')?.disabled).toBeTrue();\n      expect(component.form.get('hasHousingInterest')?.disabled).toBeTrue();\n      expect(component.form.get('hasPrepaidMedicine')?.disabled).toBeTrue();\n      expect(component.form.get('hasAfcAccount')?.disabled).toBeTrue();\n      expect(component.form.get('hasVoluntarySavings')?.disabled).toBeTrue();\n    });\n  });\n\n  describe('Form Data Loading', () => {\n    it('should load initial data correctly', () => {\n      component.initialData = mockInitialData;\n      component.ngOnChanges({\n        initialData: {\n          currentValue: mockInitialData,\n          previousValue: undefined,\n          firstChange: true,\n          isFirstChange: () => true,\n        },\n      });\n\n      expect(component.form.get('hasDependents')?.value).toBeTrue();\n      expect(component.form.get('hasHousingInterest')?.value).toBeTrue();\n      expect(component.form.get('housingInterestAnnualPayment')?.value).toBe(\n        1000000,\n      );\n      expect(component.form.get('hasPrepaidMedicine')?.value).toBeTrue();\n      expect(component.form.get('prepaidMedicineAnnualPayment')?.value).toBe(\n        500000,\n      );\n      expect(component.form.get('hasAfcAccount')?.value).toBeTrue();\n      expect(component.form.get('afcAccountAnnualPayment')?.value).toBe(300000);\n      expect(component.form.get('hasVoluntarySavings')?.value).toBeTrue();\n      expect(component.form.get('voluntarySavingsAnnualPayment')?.value).toBe(\n        200000,\n      );\n    });\n  });\n\n  describe('Form Validation', () => {\n    it('should validate payment amounts when enabled', () => {\n      component.form.get('hasHousingInterest')?.setValue(true);\n      component.form.get('housingInterestAnnualPayment')?.setValue(-1);\n      expect(\n        component.form.get('housingInterestAnnualPayment')?.errors?.['min'],\n      ).toBeTruthy();\n\n      component.form.get('housingInterestAnnualPayment')?.setValue(1000);\n      expect(\n        component.form.get('housingInterestAnnualPayment')?.valid,\n      ).toBeTrue();\n    });\n\n    it('should clear and disable payment amounts when checkbox is unchecked', () => {\n      component.form.get('hasHousingInterest')?.setValue(true);\n      component.form.get('housingInterestAnnualPayment')?.setValue(1000);\n\n      component.form.get('hasHousingInterest')?.setValue(false);\n      expect(\n        component.form.get('housingInterestAnnualPayment')?.disabled,\n      ).toBeTrue();\n      expect(\n        component.form.get('housingInterestAnnualPayment')?.value,\n      ).toBeNull();\n    });\n  });\n\n  describe('File Handling', () => {\n    it('should validate PDF file type', () => {\n      const pdfFile = new File([''], 'test.pdf', { type: 'application/pdf' });\n      const event = { target: { files: [pdfFile] } } as unknown as Event;\n\n      component.onFileSelected(event, 'dependents');\n      expect(component.fileNames.dependents).toBe('test.pdf');\n      expect(alertService.error).not.toHaveBeenCalled();\n    });\n\n    it('should reject non-PDF files', () => {\n      const jpgFile = new File([''], 'test.jpg', { type: 'image/jpeg' });\n      const event = { target: { files: [jpgFile] } } as unknown as Event;\n\n      component.onFileSelected(event, 'dependents');\n      expect(component.fileNames.dependents).toBe('');\n      expect(alertService.error).toHaveBeenCalledWith(\n        'Solo se permiten archivos PDF',\n      );\n    });\n\n    it('should reject files larger than 1MB', () => {\n      const largeFile = new File(['x'.repeat(1024 * 1024 + 1)], 'large.pdf', {\n        type: 'application/pdf',\n      });\n      const event = { target: { files: [largeFile] } } as unknown as Event;\n\n      component.onFileSelected(event, 'dependents');\n      expect(component.fileNames.dependents).toBe('');\n      expect(alertService.error).toHaveBeenCalledWith(\n        'El archivo no debe superar 1MB',\n      );\n    });\n  });\n\n  describe('Form Changes', () => {\n    it('should emit form changes', () => {\n      const emitSpy = spyOn(component.formChange, 'emit');\n      component.form.get('hasDependents')?.setValue(true);\n      expect(emitSpy).toHaveBeenCalled();\n    });\n  });\n\n  describe('Form Validity', () => {\n    it('should validate form when all required fields are filled', () => {\n      component.form.get('hasDependents')?.setValue(true);\n      const pdfFile = new File([''], 'test.pdf', { type: 'application/pdf' });\n      component.form.get('dependentsFile')?.setValue(pdfFile);\n\n      expect(component.isValid).toBeTrue();\n    });\n\n    it('should invalidate form when required files are missing', () => {\n      component.form.get('hasDependents')?.setValue(true);\n      expect(component.isValid).toBeFalse();\n    });\n  });\n});"], "mappings": "AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AACjE,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,uBAAuB,QAAQ,sCAAsC;AAE9E,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,oBAAoB,QAAQ,0BAA0B;AAE/DC,QAAQ,CAAC,sBAAsB,EAAE,MAAK;EACpC,IAAIC,SAA+B;EACnC,IAAIC,OAA+C;EACnD,IAAIC,YAA0C;EAE9C,MAAMC,eAAe,GAA+B;IAClDC,EAAE,EAAE,CAAC;IACLC,oBAAoB,EAAE,CAAC;IACvBC,MAAM,EAAE,CAAC;IACTC,aAAa,EAAE,QAAQ;IACvBC,iBAAiB,EAAE,CAAC;IACpBC,WAAW,EAAE,CAAC;IACdC,KAAK,EAAE,CAAC;IACRC,KAAK,EAAE,CAAC;IACRC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,IAAI;IACnBC,kBAAkB,EAAE,IAAI;IACxBC,4BAA4B,EAAE,OAAO;IACrCC,kBAAkB,EAAE,IAAI;IACxBC,4BAA4B,EAAE,MAAM;IACpCC,aAAa,EAAE,IAAI;IACnBC,mBAAmB,EAAE,IAAI;IACzBC,uBAAuB,EAAE,MAAM;IAC/BC,6BAA6B,EAAE;GAChC;EAEDC,UAAU,CAAC,MAAK;IACd,MAAMC,eAAe,GAAGC,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC;IAEvEhC,OAAO,CAACiC,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACP7B,oBAAoB,EACpBH,mBAAmB,EACnBC,uBAAuB,CACxB;MACDgC,SAAS,EAAE,CACTlC,WAAW,EACX;QAAEmC,OAAO,EAAEhC,YAAY;QAAEiC,QAAQ,EAAEP;MAAe,CAAE;KAEvD,CAAC;IAEFtB,OAAO,GAAGR,OAAO,CAACsC,eAAe,CAACjC,oBAAoB,CAAC;IACvDE,SAAS,GAAGC,OAAO,CAAC+B,iBAAiB;IACrC9B,YAAY,GAAGT,OAAO,CAACwC,MAAM,CAACpC,YAAY,CAAiC;IAC3EI,OAAO,CAACiC,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACpC,SAAS,CAAC,CAACqC,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFtC,QAAQ,CAAC,qBAAqB,EAAE,MAAK;IACnCoC,EAAE,CAAC,4CAA4C,EAAE,MAAK;MACpDC,MAAM,CAACpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,eAAe,CAAC,EAAEC,KAAK,CAAC,CAACC,SAAS,EAAE;MAC9DL,MAAM,CAACpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,oBAAoB,CAAC,EAAEC,KAAK,CAAC,CAACC,SAAS,EAAE;MACnEL,MAAM,CAACpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,oBAAoB,CAAC,EAAEC,KAAK,CAAC,CAACC,SAAS,EAAE;MACnEL,MAAM,CAACpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,eAAe,CAAC,EAAEC,KAAK,CAAC,CAACC,SAAS,EAAE;MAC9DL,MAAM,CAACpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,qBAAqB,CAAC,EAAEC,KAAK,CAAC,CAACC,SAAS,EAAE;IACtE,CAAC,CAAC;IAEFN,EAAE,CAAC,uDAAuD,EAAE,MAAK;MAC/DC,MAAM,CACJpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,8BAA8B,CAAC,EAAEG,QAAQ,CAC7D,CAACC,QAAQ,EAAE;MACZP,MAAM,CACJpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,8BAA8B,CAAC,EAAEG,QAAQ,CAC7D,CAACC,QAAQ,EAAE;MACZP,MAAM,CACJpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,yBAAyB,CAAC,EAAEG,QAAQ,CACxD,CAACC,QAAQ,EAAE;MACZP,MAAM,CACJpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,+BAA+B,CAAC,EAAEG,QAAQ,CAC9D,CAACC,QAAQ,EAAE;IACd,CAAC,CAAC;IAEFR,EAAE,CAAC,4CAA4C,EAAE,MAAK;MACpDnC,SAAS,CAAC4C,YAAY,GAAG,IAAI;MAC7B5C,SAAS,CAAC6C,WAAW,CAAC;QACpBD,YAAY,EAAE;UACZE,YAAY,EAAE,IAAI;UAClBC,aAAa,EAAE,KAAK;UACpBC,WAAW,EAAE,KAAK;UAClBC,aAAa,EAAEA,CAAA,KAAM;;OAExB,CAAC;MAEFb,MAAM,CAACpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,eAAe,CAAC,EAAEG,QAAQ,CAAC,CAACC,QAAQ,EAAE;MAChEP,MAAM,CAACpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,oBAAoB,CAAC,EAAEG,QAAQ,CAAC,CAACC,QAAQ,EAAE;MACrEP,MAAM,CAACpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,oBAAoB,CAAC,EAAEG,QAAQ,CAAC,CAACC,QAAQ,EAAE;MACrEP,MAAM,CAACpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,eAAe,CAAC,EAAEG,QAAQ,CAAC,CAACC,QAAQ,EAAE;MAChEP,MAAM,CAACpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,qBAAqB,CAAC,EAAEG,QAAQ,CAAC,CAACC,QAAQ,EAAE;IACxE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF5C,QAAQ,CAAC,mBAAmB,EAAE,MAAK;IACjCoC,EAAE,CAAC,oCAAoC,EAAE,MAAK;MAC5CnC,SAAS,CAACkD,WAAW,GAAG/C,eAAe;MACvCH,SAAS,CAAC6C,WAAW,CAAC;QACpBK,WAAW,EAAE;UACXJ,YAAY,EAAE3C,eAAe;UAC7B4C,aAAa,EAAEI,SAAS;UACxBH,WAAW,EAAE,IAAI;UACjBC,aAAa,EAAEA,CAAA,KAAM;;OAExB,CAAC;MAEFb,MAAM,CAACpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,eAAe,CAAC,EAAEC,KAAK,CAAC,CAACG,QAAQ,EAAE;MAC7DP,MAAM,CAACpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,oBAAoB,CAAC,EAAEC,KAAK,CAAC,CAACG,QAAQ,EAAE;MAClEP,MAAM,CAACpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,8BAA8B,CAAC,EAAEC,KAAK,CAAC,CAACY,IAAI,CACpE,OAAO,CACR;MACDhB,MAAM,CAACpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,oBAAoB,CAAC,EAAEC,KAAK,CAAC,CAACG,QAAQ,EAAE;MAClEP,MAAM,CAACpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,8BAA8B,CAAC,EAAEC,KAAK,CAAC,CAACY,IAAI,CACpE,MAAM,CACP;MACDhB,MAAM,CAACpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,eAAe,CAAC,EAAEC,KAAK,CAAC,CAACG,QAAQ,EAAE;MAC7DP,MAAM,CAACpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,yBAAyB,CAAC,EAAEC,KAAK,CAAC,CAACY,IAAI,CAAC,MAAM,CAAC;MACzEhB,MAAM,CAACpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,qBAAqB,CAAC,EAAEC,KAAK,CAAC,CAACG,QAAQ,EAAE;MACnEP,MAAM,CAACpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,+BAA+B,CAAC,EAAEC,KAAK,CAAC,CAACY,IAAI,CACrE,MAAM,CACP;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFrD,QAAQ,CAAC,iBAAiB,EAAE,MAAK;IAC/BoC,EAAE,CAAC,8CAA8C,EAAE,MAAK;MACtDnC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,oBAAoB,CAAC,EAAEc,QAAQ,CAAC,IAAI,CAAC;MACxDrD,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,8BAA8B,CAAC,EAAEc,QAAQ,CAAC,CAAC,CAAC,CAAC;MAChEjB,MAAM,CACJpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,8BAA8B,CAAC,EAAEe,MAAM,GAAG,KAAK,CAAC,CACpE,CAACjB,UAAU,EAAE;MAEdrC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,8BAA8B,CAAC,EAAEc,QAAQ,CAAC,IAAI,CAAC;MAClEjB,MAAM,CACJpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,8BAA8B,CAAC,EAAEgB,KAAK,CAC1D,CAACZ,QAAQ,EAAE;IACd,CAAC,CAAC;IAEFR,EAAE,CAAC,qEAAqE,EAAE,MAAK;MAC7EnC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,oBAAoB,CAAC,EAAEc,QAAQ,CAAC,IAAI,CAAC;MACxDrD,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,8BAA8B,CAAC,EAAEc,QAAQ,CAAC,IAAI,CAAC;MAElErD,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,oBAAoB,CAAC,EAAEc,QAAQ,CAAC,KAAK,CAAC;MACzDjB,MAAM,CACJpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,8BAA8B,CAAC,EAAEG,QAAQ,CAC7D,CAACC,QAAQ,EAAE;MACZP,MAAM,CACJpC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,8BAA8B,CAAC,EAAEC,KAAK,CAC1D,CAACgB,QAAQ,EAAE;IACd,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFzD,QAAQ,CAAC,eAAe,EAAE,MAAK;IAC7BoC,EAAE,CAAC,+BAA+B,EAAE,MAAK;MACvC,MAAMsB,OAAO,GAAG,IAAIC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE;QAAEC,IAAI,EAAE;MAAiB,CAAE,CAAC;MACvE,MAAMC,KAAK,GAAG;QAAEC,MAAM,EAAE;UAAEC,KAAK,EAAE,CAACL,OAAO;QAAC;MAAE,CAAsB;MAElEzD,SAAS,CAAC+D,cAAc,CAACH,KAAK,EAAE,YAAY,CAAC;MAC7CxB,MAAM,CAACpC,SAAS,CAACgE,SAAS,CAACC,UAAU,CAAC,CAACb,IAAI,CAAC,UAAU,CAAC;MACvDhB,MAAM,CAAClC,YAAY,CAACgE,KAAK,CAAC,CAACC,GAAG,CAACC,gBAAgB,EAAE;IACnD,CAAC,CAAC;IAEFjC,EAAE,CAAC,6BAA6B,EAAE,MAAK;MACrC,MAAMkC,OAAO,GAAG,IAAIX,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE;QAAEC,IAAI,EAAE;MAAY,CAAE,CAAC;MAClE,MAAMC,KAAK,GAAG;QAAEC,MAAM,EAAE;UAAEC,KAAK,EAAE,CAACO,OAAO;QAAC;MAAE,CAAsB;MAElErE,SAAS,CAAC+D,cAAc,CAACH,KAAK,EAAE,YAAY,CAAC;MAC7CxB,MAAM,CAACpC,SAAS,CAACgE,SAAS,CAACC,UAAU,CAAC,CAACb,IAAI,CAAC,EAAE,CAAC;MAC/ChB,MAAM,CAAClC,YAAY,CAACgE,KAAK,CAAC,CAACI,oBAAoB,CAC7C,+BAA+B,CAChC;IACH,CAAC,CAAC;IAEFnC,EAAE,CAAC,qCAAqC,EAAE,MAAK;MAC7C,MAAMoC,SAAS,GAAG,IAAIb,IAAI,CAAC,CAAC,GAAG,CAACc,MAAM,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE;QACrEb,IAAI,EAAE;OACP,CAAC;MACF,MAAMC,KAAK,GAAG;QAAEC,MAAM,EAAE;UAAEC,KAAK,EAAE,CAACS,SAAS;QAAC;MAAE,CAAsB;MAEpEvE,SAAS,CAAC+D,cAAc,CAACH,KAAK,EAAE,YAAY,CAAC;MAC7CxB,MAAM,CAACpC,SAAS,CAACgE,SAAS,CAACC,UAAU,CAAC,CAACb,IAAI,CAAC,EAAE,CAAC;MAC/ChB,MAAM,CAAClC,YAAY,CAACgE,KAAK,CAAC,CAACI,oBAAoB,CAC7C,gCAAgC,CACjC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFvE,QAAQ,CAAC,cAAc,EAAE,MAAK;IAC5BoC,EAAE,CAAC,0BAA0B,EAAE,MAAK;MAClC,MAAMsC,OAAO,GAAGC,KAAK,CAAC1E,SAAS,CAAC2E,UAAU,EAAE,MAAM,CAAC;MACnD3E,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,eAAe,CAAC,EAAEc,QAAQ,CAAC,IAAI,CAAC;MACnDjB,MAAM,CAACqC,OAAO,CAAC,CAACL,gBAAgB,EAAE;IACpC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFrE,QAAQ,CAAC,eAAe,EAAE,MAAK;IAC7BoC,EAAE,CAAC,0DAA0D,EAAE,MAAK;MAClEnC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,eAAe,CAAC,EAAEc,QAAQ,CAAC,IAAI,CAAC;MACnD,MAAMI,OAAO,GAAG,IAAIC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE;QAAEC,IAAI,EAAE;MAAiB,CAAE,CAAC;MACvE3D,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,gBAAgB,CAAC,EAAEc,QAAQ,CAACI,OAAO,CAAC;MAEvDrB,MAAM,CAACpC,SAAS,CAAC4E,OAAO,CAAC,CAACjC,QAAQ,EAAE;IACtC,CAAC,CAAC;IAEFR,EAAE,CAAC,wDAAwD,EAAE,MAAK;MAChEnC,SAAS,CAACsC,IAAI,CAACC,GAAG,CAAC,eAAe,CAAC,EAAEc,QAAQ,CAAC,IAAI,CAAC;MACnDjB,MAAM,CAACpC,SAAS,CAAC4E,OAAO,CAAC,CAACnC,SAAS,EAAE;IACvC,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}