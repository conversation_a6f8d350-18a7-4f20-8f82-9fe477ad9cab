{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { ContractClassService } from './contract-class.service';\ndescribe('ContractClassService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/contract_class`;\n  const mockContractClass = {\n    id: 1,\n    name: 'Test Contract Class'\n  };\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [ContractClassService]\n    });\n    service = TestBed.inject(ContractClassService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all contract classes', () => {\n      const mockContractClasses = [mockContractClass];\n      service.getAll().subscribe(contractClasses => {\n        expect(contractClasses).toEqual(mockContractClasses);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractClasses);\n    });\n  });\n  describe('getById', () => {\n    it('should return a contract class by id', () => {\n      const id = 1;\n      service.getById(id).subscribe(contractClass => {\n        expect(contractClass).toEqual(mockContractClass);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractClass);\n    });\n  });\n  describe('create', () => {\n    it('should create a new contract class', () => {\n      const newContractClass = {\n        name: 'New Contract Class'\n      };\n      service.create(newContractClass).subscribe(contractClass => {\n        expect(contractClass).toEqual(mockContractClass);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newContractClass);\n      req.flush(mockContractClass);\n    });\n  });\n  describe('update', () => {\n    it('should update a contract class', () => {\n      const id = 1;\n      const updateData = {\n        name: 'Updated Contract Class'\n      };\n      service.update(id, updateData).subscribe(contractClass => {\n        expect(contractClass).toEqual(mockContractClass);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockContractClass);\n    });\n  });\n  describe('delete', () => {\n    it('should delete a contract class', () => {\n      const id = 1;\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n  describe('getByName', () => {\n    it('should return a contract class by name', () => {\n      const name = 'Test Contract Class';\n      service.getByName(name).subscribe(contractClass => {\n        expect(contractClass).toEqual(mockContractClass);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractClass);\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "ContractClassService", "describe", "service", "httpMock", "apiUrl", "mockContractClass", "id", "name", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockContractClasses", "getAll", "subscribe", "contractClasses", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "getById", "contractClass", "newContractClass", "create", "body", "updateData", "update", "delete", "nothing", "getByName"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\services\\contract-class.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { ContractClass } from '@contract-management/models/contract-class.model';\nimport { environment } from '@env';\nimport { ContractClassService } from './contract-class.service';\n\ndescribe('ContractClassService', () => {\n  let service: ContractClassService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/contract_class`;\n\n  const mockContractClass: ContractClass = {\n    id: 1,\n    name: 'Test Contract Class',\n  };\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [ContractClassService],\n    });\n    service = TestBed.inject(ContractClassService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all contract classes', () => {\n      const mockContractClasses = [mockContractClass];\n\n      service.getAll().subscribe((contractClasses) => {\n        expect(contractClasses).toEqual(mockContractClasses);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractClasses);\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a contract class by id', () => {\n      const id = 1;\n\n      service.getById(id).subscribe((contractClass) => {\n        expect(contractClass).toEqual(mockContractClass);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractClass);\n    });\n  });\n\n  describe('create', () => {\n    it('should create a new contract class', () => {\n      const newContractClass: Omit<ContractClass, 'id'> = {\n        name: 'New Contract Class',\n      };\n\n      service.create(newContractClass).subscribe((contractClass) => {\n        expect(contractClass).toEqual(mockContractClass);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newContractClass);\n      req.flush(mockContractClass);\n    });\n  });\n\n  describe('update', () => {\n    it('should update a contract class', () => {\n      const id = 1;\n      const updateData: Partial<ContractClass> = {\n        name: 'Updated Contract Class',\n      };\n\n      service.update(id, updateData).subscribe((contractClass) => {\n        expect(contractClass).toEqual(mockContractClass);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockContractClass);\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete a contract class', () => {\n      const id = 1;\n\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n\n  describe('getByName', () => {\n    it('should return a contract class by name', () => {\n      const name = 'Test Contract Class';\n\n      service.getByName(name).subscribe((contractClass) => {\n        expect(contractClass).toEqual(mockContractClass);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockContractClass);\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,oBAAoB,QAAQ,0BAA0B;AAE/DC,QAAQ,CAAC,sBAAsB,EAAE,MAAK;EACpC,IAAIC,OAA6B;EACjC,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,iBAAiB;EAErD,MAAMC,iBAAiB,GAAkB;IACvCC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE;GACP;EAEDC,UAAU,CAAC,MAAK;IACdV,OAAO,CAACW,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACd,uBAAuB,CAAC;MAClCe,SAAS,EAAE,CAACX,oBAAoB;KACjC,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACc,MAAM,CAACZ,oBAAoB,CAAC;IAC9CG,QAAQ,GAAGL,OAAO,CAACc,MAAM,CAACf,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFgB,SAAS,CAAC,MAAK;IACbV,QAAQ,CAACW,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACd,OAAO,CAAC,CAACe,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFhB,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,oCAAoC,EAAE,MAAK;MAC5C,MAAMG,mBAAmB,GAAG,CAACb,iBAAiB,CAAC;MAE/CH,OAAO,CAACiB,MAAM,EAAE,CAACC,SAAS,CAAEC,eAAe,IAAI;QAC7CL,MAAM,CAACK,eAAe,CAAC,CAACC,OAAO,CAACJ,mBAAmB,CAAC;MACtD,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,mBAAmB,CAAC;IAChC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjB,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBc,EAAE,CAAC,sCAAsC,EAAE,MAAK;MAC9C,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAAC2B,OAAO,CAACvB,EAAE,CAAC,CAACc,SAAS,CAAEU,aAAa,IAAI;QAC9Cd,MAAM,CAACc,aAAa,CAAC,CAACR,OAAO,CAACjB,iBAAiB,CAAC;MAClD,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvB,iBAAiB,CAAC;IAC9B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,oCAAoC,EAAE,MAAK;MAC5C,MAAMgB,gBAAgB,GAA8B;QAClDxB,IAAI,EAAE;OACP;MAEDL,OAAO,CAAC8B,MAAM,CAACD,gBAAgB,CAAC,CAACX,SAAS,CAAEU,aAAa,IAAI;QAC3Dd,MAAM,CAACc,aAAa,CAAC,CAACR,OAAO,CAACjB,iBAAiB,CAAC;MAClD,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACS,gBAAgB,CAAC;MAClDR,GAAG,CAACK,KAAK,CAACvB,iBAAiB,CAAC;IAC9B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,gCAAgC,EAAE,MAAK;MACxC,MAAMT,EAAE,GAAG,CAAC;MACZ,MAAM4B,UAAU,GAA2B;QACzC3B,IAAI,EAAE;OACP;MAEDL,OAAO,CAACiC,MAAM,CAAC7B,EAAE,EAAE4B,UAAU,CAAC,CAACd,SAAS,CAAEU,aAAa,IAAI;QACzDd,MAAM,CAACc,aAAa,CAAC,CAACR,OAAO,CAACjB,iBAAiB,CAAC;MAClD,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACY,UAAU,CAAC;MAC5CX,GAAG,CAACK,KAAK,CAACvB,iBAAiB,CAAC;IAC9B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,gCAAgC,EAAE,MAAK;MACxC,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAACkC,MAAM,CAAC9B,EAAE,CAAC,CAACc,SAAS,CAAC,MAAK;QAChCJ,MAAM,EAAE,CAACqB,OAAO,EAAE;MACpB,CAAC,CAAC;MAEF,MAAMd,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF3B,QAAQ,CAAC,WAAW,EAAE,MAAK;IACzBc,EAAE,CAAC,wCAAwC,EAAE,MAAK;MAChD,MAAMR,IAAI,GAAG,qBAAqB;MAElCL,OAAO,CAACoC,SAAS,CAAC/B,IAAI,CAAC,CAACa,SAAS,CAAEU,aAAa,IAAI;QAClDd,MAAM,CAACc,aAAa,CAAC,CAACR,OAAO,CAACjB,iBAAiB,CAAC;MAClD,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,SAASG,IAAI,EAAE,CAAC;MACxDS,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvB,iBAAiB,CAAC;IAC9B,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}