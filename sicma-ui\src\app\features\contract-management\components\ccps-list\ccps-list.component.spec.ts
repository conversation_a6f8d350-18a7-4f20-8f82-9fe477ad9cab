import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { CCP } from '@contract-management/models/ccp.model';
import { Contract } from '@contract-management/models/contract.model';
import { of } from 'rxjs';
import { CcpDialogComponent } from './ccp-dialog/ccp-dialog.component';
import { CcpsListComponent } from './ccps-list.component';

describe('CcpsListComponent', () => {
  let component: CcpsListComponent;
  let fixture: ComponentFixture<CcpsListComponent>;
  let dialog: jasmine.SpyObj<MatDialog>;

  const mockCcps: CCP[] = [
    {
      id: 1,
      expenseObjectUseCcp: 'Object 1',
      expenseObjectDescription: 'Description 1',
      value: 1000,
      contractId: 1,
    },
    {
      id: 2,
      expenseObjectUseCcp: 'Object 2',
      expenseObjectDescription: 'Description 2',
      value: 2000,
      contractId: 1,
    },
  ];

  const mockContract: Contract = {
    id: 1,
    contractNumber: 123,
    monthlyPayment: 1000000,
    object: 'Test Contract',
    rup: true,
    secopCode: 123456,
    addition: false,
    cession: false,
    settled: false,
    contractTypeId: 1,
    statusId: 1,
    causesSelectionId: 1,
    managementSupportId: 1,
    contractClassId: 1,
    ccps: mockCcps,
  };

  beforeEach(async () => {
    const dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);
    dialogSpy.open.and.returnValue({
      afterClosed: () => of(undefined),
    } as MatDialogRef<unknown>);

    await TestBed.configureTestingModule({
      imports: [
        CcpsListComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        MatTableModule,
        MatPaginatorModule,
        MatSortModule,
      ],
      providers: [{ provide: MatDialog, useValue: dialogSpy }],
    }).compileComponents();

    fixture = TestBed.createComponent(CcpsListComponent);
    component = fixture.componentInstance;
    dialog = TestBed.inject(MatDialog) as jasmine.SpyObj<MatDialog>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with contract ccps', () => {
    component.contract = mockContract;
    component.ngOnInit();
    expect(component.dataSource.data).toEqual(mockCcps);
  });

  it('should initialize with empty array when contract has no ccps', () => {
    component.contract = { ...mockContract, ccps: undefined };
    component.ngOnInit();
    expect(component.dataSource.data).toEqual([]);
  });

  it('should set up sort after view init', () => {
    component.contract = mockContract;
    component.ngOnInit();
    fixture.detectChanges();
    component.ngAfterViewInit();

    expect(component.dataSource.sort).toBeTruthy();
  });

  it('should open dialog for new ccp', () => {
    component.contract = mockContract;
    component.ngOnInit();

    const newCcp: CCP = {
      id: 3,
      expenseObjectUseCcp: 'New Object',
      expenseObjectDescription: 'New Description',
      value: 3000,
      contractId: 1,
    };

    const mockDialogRef = {
      afterClosed: () => of(newCcp),
    } as MatDialogRef<unknown>;

    dialog.open.and.returnValue(mockDialogRef);

    component.openCcpForm();

    expect(dialog.open).toHaveBeenCalledWith(CcpDialogComponent, {
      width: '580px',
      data: {
        ccp: undefined,
        contractId: mockContract.id,
        totalCcpValue: 3000,
      },
    });

    expect(component.dataSource.data.length).toBe(3);
    expect(component.dataSource.data).toContain(newCcp);
  });

  it('should open dialog for existing ccp', () => {
    component.contract = mockContract;
    component.ngOnInit();

    const updatedCcp: CCP = {
      ...mockCcps[0],
      expenseObjectDescription: 'Updated Description',
    };

    const mockDialogRef = {
      afterClosed: () => of(updatedCcp),
    } as MatDialogRef<unknown>;

    dialog.open.and.returnValue(mockDialogRef);

    component.openCcpForm(mockCcps[0]);

    expect(dialog.open).toHaveBeenCalledWith(CcpDialogComponent, {
      width: '580px',
      data: {
        ccp: mockCcps[0],
        contractId: mockContract.id,
        totalCcpValue: 3000,
      },
    });

    expect(component.dataSource.data[0]).toEqual(updatedCcp);
  });

  it('should not update data when dialog is closed without result', () => {
    component.contract = mockContract;
    component.ngOnInit();
    const initialData = [...component.dataSource.data];

    const mockDialogRef = {
      afterClosed: () => of(undefined),
    } as MatDialogRef<unknown>;

    dialog.open.and.returnValue(mockDialogRef);

    component.openCcpForm();

    expect(component.dataSource.data).toEqual(initialData);
  });
});