import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { ContractEarlyTermination } from '@contract-management/models/contract-early-termination.model';
import { ContractorContract } from '@contract-management/models/contractor-contract.model';
import { environment } from '@env';
import { ContractorContractService } from './contractor-contract.service';
import { AuthService } from '@core/auth/services/auth.service';
import { ContractAuditHistoryService } from './contract-audit-history.service';
import { ContractAuditStatusService } from './contract-audit-status.service';
import { ContractorService } from '@contractor-management/services/contractor.service';
import { User } from '@core/auth/models/user.model';
import { Contractor } from '@contractor-management/models/contractor.model';
import { ContractAuditStatus } from '@contract-management/models/contract-audit-status.model';
import { ContractAuditHistory } from '@contract-management/models/contract-audit-history.model';

describe('ContractorContractService', () => {
  let service: ContractorContractService;
  let httpMock: HttpTestingController;
  let authServiceSpy: jasmine.SpyObj<AuthService>;
  let contractAuditHistoryServiceSpy: jasmine.SpyObj<ContractAuditHistoryService>;
  let contractAuditStatusServiceSpy: jasmine.SpyObj<ContractAuditStatusService>;
  let contractorServiceSpy: jasmine.SpyObj<ContractorService>;

  const apiUrl = `${environment.apiUrl}/contractor-contracts`;

  const mockContractorContract: ContractorContract = {
    id: 1,
    subscriptionDate: '2024-02-20',
    contractStartDate: '2024-02-20',
    contractEndDate: '2024-12-31',
    warranty: false,
    typeWarrantyId: 1,
    insuredRisksId: 1,
    contractorId: 1,
    contractId: 1,
  };

  const mockUser: User = { id: 1, username: 'testuser', profiles: [] };

  const mockContractor: Partial<Contractor> = {
    id: 1,
    fullName: 'Test Contractor',
    idNumber: 12345678,
    personalEmail: '<EMAIL>',

  };

  const mockAuditStatus: ContractAuditStatus = {
    id: 1,
    name: 'Cesión de contrato',
    description: 'Audit status for contract transfer',
  };

  const mockTerminationStatus: ContractAuditStatus = {
    id: 2,
    name: 'Terminación anticipada',
    description: 'Audit status for early termination',
  };

  const mockAuditHistory: ContractAuditHistory = {
    id: 1,
    contractId: 1,
    auditStatusId: 1,
    auditDate: new Date(),
    comment: 'Test comment',
    auditorId: 1,
  };

  beforeEach(() => {
    authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);
    contractAuditHistoryServiceSpy = jasmine.createSpyObj(
      'ContractAuditHistoryService',
      ['create'],
    );
    contractAuditStatusServiceSpy = jasmine.createSpyObj(
      'ContractAuditStatusService',
      ['getByName'],
    );
    contractorServiceSpy = jasmine.createSpyObj('ContractorService', [
      'getById',
    ]);

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        ContractorContractService,
        { provide: AuthService, useValue: authServiceSpy },
        {
          provide: ContractAuditHistoryService,
          useValue: contractAuditHistoryServiceSpy,
        },
        {
          provide: ContractAuditStatusService,
          useValue: contractAuditStatusServiceSpy,
        },
        { provide: ContractorService, useValue: contractorServiceSpy },
      ],
    });
    service = TestBed.inject(ContractorContractService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all contractor contracts', () => {
      const mockContractorContracts = [mockContractorContract];

      service.getAll().subscribe((contractorContracts) => {
        expect(contractorContracts).toEqual(mockContractorContracts);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockContractorContracts);
    });

    it('should handle error when getting all contractor contracts', () => {
      service.getAll().subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(500);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.flush('Internal Server Error', {
        status: 500,
        statusText: 'Internal Server Error',
      });
    });
  });

  describe('getById', () => {
    it('should return a contractor contract by id', () => {
      const id = 1;

      service.getById(id).subscribe((contractorContract) => {
        expect(contractorContract).toEqual(mockContractorContract);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockContractorContract);
    });

    it('should handle error when getting contractor contract by id', () => {
      const id = 999;

      service.getById(id).subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.flush('Not Found', { status: 404, statusText: 'Not Found' });
    });
  });

  describe('getAllByContractorId', () => {
    it('should return all contractor contracts by contractor id', () => {
      const contractorId = 1;
      const mockContractorContracts = [mockContractorContract];

      service
        .getAllByContractorId(contractorId)
        .subscribe((contractorContracts) => {
          expect(contractorContracts).toEqual(mockContractorContracts);
        });

      const req = httpMock.expectOne(`${apiUrl}/contractor/${contractorId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockContractorContracts);
    });

    it('should handle error when getting contractor contracts by contractor id', () => {
      const contractorId = 999;

      service.getAllByContractorId(contractorId).subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/contractor/${contractorId}`);
      req.flush('Not Found', { status: 404, statusText: 'Not Found' });
    });
  });

  describe('getAllByContractId', () => {
    it('should return all contractor contracts by contract id', () => {
      const contractId = 1;
      const mockContractorContracts = [mockContractorContract];

      service
        .getAllByContractId(contractId)
        .subscribe((contractorContracts) => {
          expect(contractorContracts).toEqual(mockContractorContracts);
        });

      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockContractorContracts);
    });

    it('should handle error when getting contractor contracts by contract id', () => {
      const contractId = 999;

      service.getAllByContractId(contractId).subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}`);
      req.flush('Not Found', { status: 404, statusText: 'Not Found' });
    });
  });

  describe('getByContractorAndContractId', () => {
    it('should return a contractor contract by contractor and contract id', () => {
      const contractorId = 1;
      const contractId = 1;

      service
        .getByContractorAndContractId(contractorId, contractId)
        .subscribe((contractorContract) => {
          expect(contractorContract).toEqual(mockContractorContract);
        });

      const req = httpMock.expectOne(
        `${apiUrl}/contractor/${contractorId}/contract/${contractId}`,
      );
      expect(req.request.method).toBe('GET');
      req.flush(mockContractorContract);
    });

    it('should handle error when getting contractor contract by contractor and contract id', () => {
      const contractorId = 999;
      const contractId = 999;

      service.getByContractorAndContractId(contractorId, contractId).subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(
        `${apiUrl}/contractor/${contractorId}/contract/${contractId}`,
      );
      req.flush('Not Found', { status: 404, statusText: 'Not Found' });
    });
  });

  describe('getLatestTerminationDate', () => {
    it('should return the latest termination date for a contract', () => {
      const contractId = 1;
      const mockDate = '2024-12-31';

      service.getLatestTerminationDate(contractId).subscribe((date) => {
        expect(date).toEqual(mockDate);
      });

      const req = httpMock.expectOne(
        `${apiUrl}/contract/${contractId}/latest-termination-date`,
      );
      expect(req.request.method).toBe('GET');
      req.flush(mockDate);
    });

    it('should handle error when getting latest termination date', () => {
      const contractId = 999;

      service.getLatestTerminationDate(contractId).subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(
        `${apiUrl}/contract/${contractId}/latest-termination-date`,
      );
      req.flush('Not Found', { status: 404, statusText: 'Not Found' });
    });
  });

  describe('hasActiveContract', () => {
    it('should check if contractor has active contract - true case', () => {
      const contractorId = 1;

      service.hasActiveContract(contractorId).subscribe((hasActive) => {
        expect(hasActive).toBeTrue();
      });

      const req = httpMock.expectOne(
        `${apiUrl}/contractor/${contractorId}/has-active-contract`,
      );
      expect(req.request.method).toBe('GET');
      req.flush(true);
    });

    it('should check if contractor has active contract - false case', () => {
      const contractorId = 2;

      service.hasActiveContract(contractorId).subscribe((hasActive) => {
        expect(hasActive).toBeFalse();
      });

      const req = httpMock.expectOne(
        `${apiUrl}/contractor/${contractorId}/has-active-contract`,
      );
      expect(req.request.method).toBe('GET');
      req.flush(false);
    });

    it('should handle error when checking if contractor has active contract', () => {
      const contractorId = 999;

      service.hasActiveContract(contractorId).subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(
        `${apiUrl}/contractor/${contractorId}/has-active-contract`,
      );
      req.flush('Not Found', { status: 404, statusText: 'Not Found' });
    });
  });

  describe('create', () => {
    it('should create a new contractor contract with audit when user is authenticated', () => {
      const newContractorContract: Omit<ContractorContract, 'id'> = {
        subscriptionDate: '2024-02-20',
        contractStartDate: '2024-02-20',
        contractEndDate: '2024-12-31',
        warranty: false,
        typeWarrantyId: 1,
        insuredRisksId: 1,
        contractorId: 1,
        contractId: 1,
      };

      authServiceSpy.getCurrentUser.and.returnValue(mockUser);
      contractorServiceSpy.getById.and.returnValue(
        of(mockContractor as Contractor),
      );
      contractAuditStatusServiceSpy.getByName.and.returnValue(
        of(mockAuditStatus),
      );
      contractAuditHistoryServiceSpy.create.and.returnValue(
        of(mockAuditHistory),
      );

      service.create(newContractorContract).subscribe((contractorContract) => {
        expect(contractorContract).toEqual(mockContractorContract);
        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();
        expect(contractorServiceSpy.getById).toHaveBeenCalledWith(1);
        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith(
          'Cesión de contrato',
        );
        expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newContractorContract);
      req.flush(mockContractorContract);
    });

    it('should create a new contractor contract without audit when user is not authenticated', () => {
      const newContractorContract: Omit<ContractorContract, 'id'> = {
        subscriptionDate: '2024-02-20',
        contractStartDate: '2024-02-20',
        contractEndDate: '2024-12-31',
        warranty: false,
        typeWarrantyId: 1,
        insuredRisksId: 1,
        contractorId: 1,
        contractId: 1,
      };

      authServiceSpy.getCurrentUser.and.returnValue(null);

      service.create(newContractorContract).subscribe((contractorContract) => {
        expect(contractorContract).toEqual(mockContractorContract);
        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();
        expect(contractorServiceSpy.getById).not.toHaveBeenCalled();
        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();
        expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newContractorContract);
      req.flush(mockContractorContract);
    });

    it('should handle error when creating a new contractor contract', () => {
      const newContractorContract: Omit<ContractorContract, 'id'> = {
        subscriptionDate: '2024-02-20',
        contractStartDate: '2024-02-20',
        contractEndDate: '2024-12-31',
        warranty: false,
        typeWarrantyId: 1,
        insuredRisksId: 1,
        contractorId: 1,
        contractId: 1,
      };

      authServiceSpy.getCurrentUser.and.returnValue(null);

      service.create(newContractorContract).subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.flush('Bad Request', { status: 400, statusText: 'Bad Request' });
    });
  });

  describe('earlyTerminateContract', () => {
    it('should early terminate a contract with audit when user is authenticated', () => {
      const terminationData: ContractEarlyTermination = {
        contractorId: 1,
        contractId: 1,
        endDate: '2024-03-01',
        terminationReason: 'Test reason',
      };

      authServiceSpy.getCurrentUser.and.returnValue(mockUser);
      contractAuditStatusServiceSpy.getByName.and.returnValue(
        of(mockTerminationStatus),
      );
      contractAuditHistoryServiceSpy.create.and.returnValue(
        of(mockAuditHistory),
      );

      service
        .earlyTerminateContract(terminationData)
        .subscribe((contractorContract) => {
          expect(contractorContract).toEqual(mockContractorContract);
          expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();
          expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith(
            'Terminación anticipada',
          );
          expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();
        });

      const req = httpMock.expectOne(`${apiUrl}/early-termination`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(terminationData);
      req.flush(mockContractorContract);
    });

    it('should early terminate a contract without audit when user is not authenticated', () => {
      const terminationData: ContractEarlyTermination = {
        contractorId: 1,
        contractId: 1,
        endDate: '2024-03-01',
        terminationReason: 'Test reason',
      };

      authServiceSpy.getCurrentUser.and.returnValue(null);

      service
        .earlyTerminateContract(terminationData)
        .subscribe((contractorContract) => {
          expect(contractorContract).toEqual(mockContractorContract);
          expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();
          expect(
            contractAuditStatusServiceSpy.getByName,
          ).not.toHaveBeenCalled();
          expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();
        });

      const req = httpMock.expectOne(`${apiUrl}/early-termination`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(terminationData);
      req.flush(mockContractorContract);
    });

    it('should handle error when early terminating a contract', () => {
      const terminationData: ContractEarlyTermination = {
        contractorId: 1,
        contractId: 1,
        endDate: '2024-03-01',
        terminationReason: 'Test reason',
      };

      authServiceSpy.getCurrentUser.and.returnValue(null);

      service.earlyTerminateContract(terminationData).subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/early-termination`);
      req.flush('Bad Request', { status: 400, statusText: 'Bad Request' });
    });
  });

  describe('update', () => {
    it('should update a contractor contract', () => {
      const id = 1;
      const updateData: Partial<ContractorContract> = {
        contractEndDate: '2024-06-30',
      };

      service.update(id, updateData).subscribe((contractorContract) => {
        expect(contractorContract).toEqual(mockContractorContract);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockContractorContract);
    });

    it('should handle error when updating a contractor contract', () => {
      const id = 999;
      const updateData: Partial<ContractorContract> = {
        contractEndDate: '2024-06-30',
      };

      service.update(id, updateData).subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.flush('Not Found', { status: 404, statusText: 'Not Found' });
    });
  });

  describe('delete', () => {
    it('should delete a contractor contract', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });

    it('should handle error when deleting a contractor contract', () => {
      const id = 999;

      service.delete(id).subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.flush('Not Found', { status: 404, statusText: 'Not Found' });
    });
  });
});