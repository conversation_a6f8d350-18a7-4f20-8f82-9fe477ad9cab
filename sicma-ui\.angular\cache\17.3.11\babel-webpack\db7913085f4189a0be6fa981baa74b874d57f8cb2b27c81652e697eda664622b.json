{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { EducationLevelService } from './education-level.service';\ndescribe('EducationLevelService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/education-levels`;\n  const mockEducationLevel = {\n    id: 1,\n    name: 'Test Education Level'\n  };\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [EducationLevelService]\n    });\n    service = TestBed.inject(EducationLevelService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all education levels', () => {\n      const mockEducationLevels = [mockEducationLevel];\n      service.getAll().subscribe(educationLevels => {\n        expect(educationLevels).toEqual(mockEducationLevels);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockEducationLevels);\n    });\n  });\n  describe('getById', () => {\n    it('should return an education level by id', () => {\n      const id = 1;\n      service.getById(id).subscribe(educationLevel => {\n        expect(educationLevel).toEqual(mockEducationLevel);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockEducationLevel);\n    });\n  });\n  describe('create', () => {\n    it('should create a new education level', () => {\n      const newEducationLevel = {\n        name: 'New Education Level'\n      };\n      service.create(newEducationLevel).subscribe(educationLevel => {\n        expect(educationLevel).toEqual(mockEducationLevel);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newEducationLevel);\n      req.flush(mockEducationLevel);\n    });\n  });\n  describe('update', () => {\n    it('should update an education level', () => {\n      const id = 1;\n      const updateData = {\n        name: 'Updated Education Level'\n      };\n      service.update(id, updateData).subscribe(educationLevel => {\n        expect(educationLevel).toEqual(mockEducationLevel);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockEducationLevel);\n    });\n  });\n  describe('delete', () => {\n    it('should delete an education level', () => {\n      const id = 1;\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n  describe('getByName', () => {\n    it('should return an education level by name', () => {\n      const name = 'Test Education Level';\n      service.getByName(name).subscribe(educationLevel => {\n        expect(educationLevel).toEqual(mockEducationLevel);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockEducationLevel);\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "EducationLevelService", "describe", "service", "httpMock", "apiUrl", "mockEducationLevel", "id", "name", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockEducationLevels", "getAll", "subscribe", "educationLevels", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "getById", "educationLevel", "newEducationLevel", "create", "body", "updateData", "update", "delete", "nothing", "getByName"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-management\\services\\education-level.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { EducationLevel } from '@contractor-management/models/education-level.model';\nimport { environment } from '@env';\nimport { EducationLevelService } from './education-level.service';\n\ndescribe('EducationLevelService', () => {\n  let service: EducationLevelService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/education-levels`;\n\n  const mockEducationLevel: EducationLevel = {\n    id: 1,\n    name: 'Test Education Level',\n  };\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [EducationLevelService],\n    });\n    service = TestBed.inject(EducationLevelService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all education levels', () => {\n      const mockEducationLevels = [mockEducationLevel];\n\n      service.getAll().subscribe((educationLevels) => {\n        expect(educationLevels).toEqual(mockEducationLevels);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockEducationLevels);\n    });\n  });\n\n  describe('getById', () => {\n    it('should return an education level by id', () => {\n      const id = 1;\n\n      service.getById(id).subscribe((educationLevel) => {\n        expect(educationLevel).toEqual(mockEducationLevel);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockEducationLevel);\n    });\n  });\n\n  describe('create', () => {\n    it('should create a new education level', () => {\n      const newEducationLevel: Omit<EducationLevel, 'id'> = {\n        name: 'New Education Level',\n      };\n\n      service.create(newEducationLevel).subscribe((educationLevel) => {\n        expect(educationLevel).toEqual(mockEducationLevel);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newEducationLevel);\n      req.flush(mockEducationLevel);\n    });\n  });\n\n  describe('update', () => {\n    it('should update an education level', () => {\n      const id = 1;\n      const updateData: Partial<EducationLevel> = {\n        name: 'Updated Education Level',\n      };\n\n      service.update(id, updateData).subscribe((educationLevel) => {\n        expect(educationLevel).toEqual(mockEducationLevel);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockEducationLevel);\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete an education level', () => {\n      const id = 1;\n\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n\n  describe('getByName', () => {\n    it('should return an education level by name', () => {\n      const name = 'Test Education Level';\n\n      service.getByName(name).subscribe((educationLevel) => {\n        expect(educationLevel).toEqual(mockEducationLevel);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockEducationLevel);\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,qBAAqB,QAAQ,2BAA2B;AAEjEC,QAAQ,CAAC,uBAAuB,EAAE,MAAK;EACrC,IAAIC,OAA8B;EAClC,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,mBAAmB;EAEvD,MAAMC,kBAAkB,GAAmB;IACzCC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE;GACP;EAEDC,UAAU,CAAC,MAAK;IACdV,OAAO,CAACW,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACd,uBAAuB,CAAC;MAClCe,SAAS,EAAE,CAACX,qBAAqB;KAClC,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACc,MAAM,CAACZ,qBAAqB,CAAC;IAC/CG,QAAQ,GAAGL,OAAO,CAACc,MAAM,CAACf,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFgB,SAAS,CAAC,MAAK;IACbV,QAAQ,CAACW,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACd,OAAO,CAAC,CAACe,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFhB,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,oCAAoC,EAAE,MAAK;MAC5C,MAAMG,mBAAmB,GAAG,CAACb,kBAAkB,CAAC;MAEhDH,OAAO,CAACiB,MAAM,EAAE,CAACC,SAAS,CAAEC,eAAe,IAAI;QAC7CL,MAAM,CAACK,eAAe,CAAC,CAACC,OAAO,CAACJ,mBAAmB,CAAC;MACtD,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,mBAAmB,CAAC;IAChC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjB,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBc,EAAE,CAAC,wCAAwC,EAAE,MAAK;MAChD,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAAC2B,OAAO,CAACvB,EAAE,CAAC,CAACc,SAAS,CAAEU,cAAc,IAAI;QAC/Cd,MAAM,CAACc,cAAc,CAAC,CAACR,OAAO,CAACjB,kBAAkB,CAAC;MACpD,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvB,kBAAkB,CAAC;IAC/B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,qCAAqC,EAAE,MAAK;MAC7C,MAAMgB,iBAAiB,GAA+B;QACpDxB,IAAI,EAAE;OACP;MAEDL,OAAO,CAAC8B,MAAM,CAACD,iBAAiB,CAAC,CAACX,SAAS,CAAEU,cAAc,IAAI;QAC7Dd,MAAM,CAACc,cAAc,CAAC,CAACR,OAAO,CAACjB,kBAAkB,CAAC;MACpD,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACS,iBAAiB,CAAC;MACnDR,GAAG,CAACK,KAAK,CAACvB,kBAAkB,CAAC;IAC/B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,kCAAkC,EAAE,MAAK;MAC1C,MAAMT,EAAE,GAAG,CAAC;MACZ,MAAM4B,UAAU,GAA4B;QAC1C3B,IAAI,EAAE;OACP;MAEDL,OAAO,CAACiC,MAAM,CAAC7B,EAAE,EAAE4B,UAAU,CAAC,CAACd,SAAS,CAAEU,cAAc,IAAI;QAC1Dd,MAAM,CAACc,cAAc,CAAC,CAACR,OAAO,CAACjB,kBAAkB,CAAC;MACpD,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACY,UAAU,CAAC;MAC5CX,GAAG,CAACK,KAAK,CAACvB,kBAAkB,CAAC;IAC/B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,kCAAkC,EAAE,MAAK;MAC1C,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAACkC,MAAM,CAAC9B,EAAE,CAAC,CAACc,SAAS,CAAC,MAAK;QAChCJ,MAAM,EAAE,CAACqB,OAAO,EAAE;MACpB,CAAC,CAAC;MAEF,MAAMd,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF3B,QAAQ,CAAC,WAAW,EAAE,MAAK;IACzBc,EAAE,CAAC,0CAA0C,EAAE,MAAK;MAClD,MAAMR,IAAI,GAAG,sBAAsB;MAEnCL,OAAO,CAACoC,SAAS,CAAC/B,IAAI,CAAC,CAACa,SAAS,CAAEU,cAAc,IAAI;QACnDd,MAAM,CAACc,cAAc,CAAC,CAACR,OAAO,CAACjB,kBAAkB,CAAC;MACpD,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,SAASG,IAAI,EAAE,CAAC;MACxDS,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvB,kBAAkB,CAAC;IAC/B,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}