import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { Obligation } from '@contract-management/models/obligation.model';
import { MonthlyReport } from '@contractor-dashboard/models/monthly-report.model';
import { ReportObligation } from '@contractor-dashboard/models/report-obligation.model';
import { MonthlyReportObligationsComponent } from './monthly-report-obligations.component';

describe('MonthlyReportObligationsComponent', () => {
  let component: MonthlyReportObligationsComponent;
  let fixture: ComponentFixture<MonthlyReportObligationsComponent>;

  const mockObligations: Obligation[] = [
    { id: 1, name: 'Obligation 1', contractId: 1, number: 1 },
    { id: 2, name: 'Obligation 2', contractId: 1, number: 2 },
    { id: 3, name: 'Obligation 3', contractId: 1, number: 3 },
  ];

  const mockReportObligations: ReportObligation[] = [
    {
      id: 1,
      monthlyReportId: 1,
      obligationId: 1,
      description: 'Activity 1',
      evidence: 'Evidence 1',
      filePath: '',
    },
    {
      id: 2,
      monthlyReportId: 1,
      obligationId: 3,
      description: 'Activity 3',
      evidence: 'Evidence 3',
      filePath: '',
    },
    {
      id: 3,
      monthlyReportId: 1,
      obligationId: 2,
      description: 'Activity 2',
      evidence: 'Evidence 2',
      filePath: '',
    },
  ];

  const mockMonthlyReport: MonthlyReport = {
    id: 1,
    reportNumber: 1,
    startDate: new Date(),
    endDate: new Date(),
    creationDate: new Date(),
    contractorContractId: 1,
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        MonthlyReportObligationsComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        ReactiveFormsModule,
      ],
      providers: [FormBuilder],
    });

    fixture = TestBed.createComponent(MonthlyReportObligationsComponent);
    component = fixture.componentInstance;
    component.monthlyReport = mockMonthlyReport;
    component.obligations = [...mockObligations];
    component.reportObligations = [...mockReportObligations];
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with obligations', () => {
    expect(component.obligations).toEqual(mockObligations);
    expect(component.reportObligations.length).toBe(3);
  });

  it('should get obligation name correctly', () => {
    const name = component.getObligationName(1);
    expect(name).toBe('Obligation 1');
  });

  it('should get obligation number correctly', () => {
    const num = component.getObligationNumber(2);
    expect(num).toBe(2);
    const numUnknown = component.getObligationNumber(999);
    expect(numUnknown).toBe('N/A');
  });

  it('should handle unknown obligation name', () => {
    const name = component.getObligationName(999);
    expect(name).toBe('Unknown Obligation');
  });

  it('should start editing row', () => {
    component.startEditing(0);
    expect(component.editingRow).toBe(0);
    expect(component.editForm.get('activity')?.value).toBe('Activity 1');
    expect(component.editForm.get('evidence')?.value).toBe('Evidence 1');
  });

  it('should cancel editing', () => {
    component.startEditing(0);
    component.cancelEditing();
    expect(component.editingRow).toBeNull();
    expect(component.editForm.get('activity')?.value).toBeNull();
    expect(component.editForm.get('evidence')?.value).toBeNull();
  });

  it('should save editing', () => {
    const saveEditingSpy = spyOn(component.saveEditing, 'emit');
    component.startEditing(0);
    component.editForm.patchValue({
      activity: 'Updated Activity',
      evidence: 'Updated Evidence',
    });
    component.onSaveEditing(0);

    expect(saveEditingSpy).toHaveBeenCalledWith({
      ...mockReportObligations[0],
      description: 'Updated Activity',
      evidence: 'Updated Evidence',
    });
    expect(component.editingRow).toBeNull();
  });

  it('should emit next step', () => {
    const nextStepSpy = spyOn(component.nextStep, 'emit');
    component.onNextStep();
    expect(nextStepSpy).toHaveBeenCalled();
  });

  it('should emit previous step', () => {
    const previousStepSpy = spyOn(component.previousStep, 'emit');
    component.onPreviousStep();
    expect(previousStepSpy).toHaveBeenCalled();
  });

  it('should emit open obligations dialog', () => {
    const openDialogSpy = spyOn(component.openObligationsDialog, 'emit');
    component.onOpenObligationsDialog();
    expect(openDialogSpy).toHaveBeenCalled();
  });

  it('should handle changes in obligations', () => {
    const currentObligations = [...mockObligations];
    const newObligationToAdd: Obligation = {
      id: 4,
      name: 'New Obligation',
      contractId: 1,
      number: 4,
    };

    component.obligations = [...currentObligations, newObligationToAdd];

    const initialReportObligations = component.reportObligations.filter(
      (ro) => ro.obligationId !== 4,
    );
    component.reportObligations = [...initialReportObligations];

    component.ngOnChanges({
      obligations: {
        currentValue: component.obligations,
        previousValue: currentObligations,
        firstChange: false,
        isFirstChange: () => false,
      },
      reportObligations: {
        currentValue: component.reportObligations,
        previousValue: initialReportObligations,
        firstChange: false,
        isFirstChange: () => false,
      },
    });
    fixture.detectChanges();

    expect(
      component.reportObligations.some((ro) => ro.obligationId === 4),
    ).toBeTrue();
    expect(component.reportObligations.map((ro) => ro.obligationId)).toEqual([
      1, 2, 3, 4,
    ]);
  });

  it('should display correct columns based on supervisor status', () => {
    expect(component.displayedColumns).toContain('number');
    expect(component.displayedColumns).toContain('actions');
    component.isSupervisor = true;
    fixture.detectChanges();
    expect(component.displayedColumns).toContain('number');
    expect(component.displayedColumns).not.toContain('actions');
  });

  describe('form validation', () => {
    it('should validate required fields', () => {
      component.startEditing(0);
      component.editForm.patchValue({
        activity: 'Test Activity',
        evidence: 'Test Evidence',
      });
      expect(component.editForm.valid).toBeTrue();
    });
  });

  describe('data loading and sorting', () => {
    it('should handle loading states and sort correctly initially', () => {
      expect(component.reportObligations.length).toBe(3);
      expect(component.reportObligations.map((ro) => ro.obligationId)).toEqual([
        1, 2, 3,
      ]);
    });

    it('should sort obligations correctly after ngOnChanges if obligation numbers change order', () => {
      const newObligationsOrder: Obligation[] = [
        { id: 2, name: 'Obligation 2', contractId: 1, number: 20 },
        { id: 3, name: 'Obligation 3', contractId: 1, number: 30 },
        { id: 1, name: 'Obligation 1', contractId: 1, number: 10 },
      ];
      component.obligations = [...newObligationsOrder];
      component.reportObligations = [...mockReportObligations];

      component.ngOnChanges({
        obligations: {
          currentValue: component.obligations,
          previousValue: mockObligations,
          firstChange: false,
          isFirstChange: () => false,
        },
      });
      fixture.detectChanges();
      expect(component.reportObligations.map((ro) => ro.obligationId)).toEqual([
        1, 2, 3,
      ]);
    });
  });
});