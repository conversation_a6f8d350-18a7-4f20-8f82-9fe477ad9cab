{"ast": null, "code": "function cov_cwp8plf6() {\n  var path = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contract-management\\\\pages\\\\contracts-list-page\\\\contracts-list-page.component.ts\";\n  var hash = \"637b2881ad08c065b424e17d72c660a7a200d9d6\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contract-management\\\\pages\\\\contracts-list-page\\\\contracts-list-page.component.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 25,\n          column: 33\n        },\n        end: {\n          line: 193,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 27,\n          column: 8\n        },\n        end: {\n          line: 27,\n          column: 47\n        }\n      },\n      \"2\": {\n        start: {\n          line: 28,\n          column: 8\n        },\n        end: {\n          line: 28,\n          column: 55\n        }\n      },\n      \"3\": {\n        start: {\n          line: 29,\n          column: 8\n        },\n        end: {\n          line: 29,\n          column: 29\n        }\n      },\n      \"4\": {\n        start: {\n          line: 30,\n          column: 8\n        },\n        end: {\n          line: 30,\n          column: 29\n        }\n      },\n      \"5\": {\n        start: {\n          line: 31,\n          column: 8\n        },\n        end: {\n          line: 31,\n          column: 31\n        }\n      },\n      \"6\": {\n        start: {\n          line: 32,\n          column: 8\n        },\n        end: {\n          line: 32,\n          column: 27\n        }\n      },\n      \"7\": {\n        start: {\n          line: 33,\n          column: 8\n        },\n        end: {\n          line: 48,\n          column: 10\n        }\n      },\n      \"8\": {\n        start: {\n          line: 49,\n          column: 8\n        },\n        end: {\n          line: 49,\n          column: 51\n        }\n      },\n      \"9\": {\n        start: {\n          line: 50,\n          column: 8\n        },\n        end: {\n          line: 50,\n          column: 53\n        }\n      },\n      \"10\": {\n        start: {\n          line: 51,\n          column: 8\n        },\n        end: {\n          line: 51,\n          column: 32\n        }\n      },\n      \"11\": {\n        start: {\n          line: 52,\n          column: 8\n        },\n        end: {\n          line: 52,\n          column: 35\n        }\n      },\n      \"12\": {\n        start: {\n          line: 53,\n          column: 8\n        },\n        end: {\n          line: 53,\n          column: 39\n        }\n      },\n      \"13\": {\n        start: {\n          line: 54,\n          column: 8\n        },\n        end: {\n          line: 54,\n          column: 39\n        }\n      },\n      \"14\": {\n        start: {\n          line: 55,\n          column: 8\n        },\n        end: {\n          line: 55,\n          column: 37\n        }\n      },\n      \"15\": {\n        start: {\n          line: 58,\n          column: 8\n        },\n        end: {\n          line: 58,\n          column: 32\n        }\n      },\n      \"16\": {\n        start: {\n          line: 59,\n          column: 8\n        },\n        end: {\n          line: 59,\n          column: 33\n        }\n      },\n      \"17\": {\n        start: {\n          line: 62,\n          column: 8\n        },\n        end: {\n          line: 62,\n          column: 51\n        }\n      },\n      \"18\": {\n        start: {\n          line: 63,\n          column: 8\n        },\n        end: {\n          line: 63,\n          column: 41\n        }\n      },\n      \"19\": {\n        start: {\n          line: 66,\n          column: 8\n        },\n        end: {\n          line: 72,\n          column: 9\n        }\n      },\n      \"20\": {\n        start: {\n          line: 67,\n          column: 12\n        },\n        end: {\n          line: 67,\n          column: 119\n        }\n      },\n      \"21\": {\n        start: {\n          line: 67,\n          column: 78\n        },\n        end: {\n          line: 67,\n          column: 117\n        }\n      },\n      \"22\": {\n        start: {\n          line: 70,\n          column: 12\n        },\n        end: {\n          line: 71,\n          column: 57\n        }\n      },\n      \"23\": {\n        start: {\n          line: 70,\n          column: 78\n        },\n        end: {\n          line: 71,\n          column: 55\n        }\n      },\n      \"24\": {\n        start: {\n          line: 75,\n          column: 38\n        },\n        end: {\n          line: 80,\n          column: 71\n        }\n      },\n      \"25\": {\n        start: {\n          line: 81,\n          column: 38\n        },\n        end: {\n          line: 85,\n          column: 71\n        }\n      },\n      \"26\": {\n        start: {\n          line: 86,\n          column: 36\n        },\n        end: {\n          line: 91,\n          column: 69\n        }\n      },\n      \"27\": {\n        start: {\n          line: 92,\n          column: 8\n        },\n        end: {\n          line: 92,\n          column: 94\n        }\n      },\n      \"28\": {\n        start: {\n          line: 95,\n          column: 8\n        },\n        end: {\n          line: 102,\n          column: 11\n        }\n      },\n      \"29\": {\n        start: {\n          line: 99,\n          column: 12\n        },\n        end: {\n          line: 101,\n          column: 13\n        }\n      },\n      \"30\": {\n        start: {\n          line: 100,\n          column: 16\n        },\n        end: {\n          line: 100,\n          column: 40\n        }\n      },\n      \"31\": {\n        start: {\n          line: 105,\n          column: 8\n        },\n        end: {\n          line: 105,\n          column: 58\n        }\n      },\n      \"32\": {\n        start: {\n          line: 108,\n          column: 8\n        },\n        end: {\n          line: 111,\n          column: 9\n        }\n      },\n      \"33\": {\n        start: {\n          line: 109,\n          column: 12\n        },\n        end: {\n          line: 109,\n          column: 76\n        }\n      },\n      \"34\": {\n        start: {\n          line: 110,\n          column: 12\n        },\n        end: {\n          line: 110,\n          column: 19\n        }\n      },\n      \"35\": {\n        start: {\n          line: 112,\n          column: 8\n        },\n        end: {\n          line: 112,\n          column: 28\n        }\n      },\n      \"36\": {\n        start: {\n          line: 113,\n          column: 8\n        },\n        end: {\n          line: 129,\n          column: 11\n        }\n      },\n      \"37\": {\n        start: {\n          line: 115,\n          column: 33\n        },\n        end: {\n          line: 115,\n          column: 52\n        }\n      },\n      \"38\": {\n        start: {\n          line: 118,\n          column: 28\n        },\n        end: {\n          line: 118,\n          column: 60\n        }\n      },\n      \"39\": {\n        start: {\n          line: 119,\n          column: 29\n        },\n        end: {\n          line: 119,\n          column: 56\n        }\n      },\n      \"40\": {\n        start: {\n          line: 120,\n          column: 16\n        },\n        end: {\n          line: 120,\n          column: 32\n        }\n      },\n      \"41\": {\n        start: {\n          line: 121,\n          column: 16\n        },\n        end: {\n          line: 121,\n          column: 116\n        }\n      },\n      \"42\": {\n        start: {\n          line: 122,\n          column: 16\n        },\n        end: {\n          line: 122,\n          column: 29\n        }\n      },\n      \"43\": {\n        start: {\n          line: 123,\n          column: 16\n        },\n        end: {\n          line: 123,\n          column: 48\n        }\n      },\n      \"44\": {\n        start: {\n          line: 124,\n          column: 16\n        },\n        end: {\n          line: 124,\n          column: 74\n        }\n      },\n      \"45\": {\n        start: {\n          line: 127,\n          column: 16\n        },\n        end: {\n          line: 127,\n          column: 88\n        }\n      },\n      \"46\": {\n        start: {\n          line: 132,\n          column: 8\n        },\n        end: {\n          line: 132,\n          column: 28\n        }\n      },\n      \"47\": {\n        start: {\n          line: 133,\n          column: 8\n        },\n        end: {\n          line: 154,\n          column: 11\n        }\n      },\n      \"48\": {\n        start: {\n          line: 135,\n          column: 33\n        },\n        end: {\n          line: 135,\n          column: 52\n        }\n      },\n      \"49\": {\n        start: {\n          line: 138,\n          column: 16\n        },\n        end: {\n          line: 138,\n          column: 43\n        }\n      },\n      \"50\": {\n        start: {\n          line: 139,\n          column: 36\n        },\n        end: {\n          line: 139,\n          column: 60\n        }\n      },\n      \"51\": {\n        start: {\n          line: 140,\n          column: 35\n        },\n        end: {\n          line: 140,\n          column: 76\n        }\n      },\n      \"52\": {\n        start: {\n          line: 140,\n          column: 53\n        },\n        end: {\n          line: 140,\n          column: 75\n        }\n      },\n      \"53\": {\n        start: {\n          line: 141,\n          column: 16\n        },\n        end: {\n          line: 148,\n          column: 17\n        }\n      },\n      \"54\": {\n        start: {\n          line: 142,\n          column: 20\n        },\n        end: {\n          line: 142,\n          column: 52\n        }\n      },\n      \"55\": {\n        start: {\n          line: 144,\n          column: 21\n        },\n        end: {\n          line: 148,\n          column: 17\n        }\n      },\n      \"56\": {\n        start: {\n          line: 145,\n          column: 20\n        },\n        end: {\n          line: 147,\n          column: 50\n        }\n      },\n      \"57\": {\n        start: {\n          line: 146,\n          column: 36\n        },\n        end: {\n          line: 146,\n          column: 42\n        }\n      },\n      \"58\": {\n        start: {\n          line: 147,\n          column: 40\n        },\n        end: {\n          line: 147,\n          column: 45\n        }\n      },\n      \"59\": {\n        start: {\n          line: 149,\n          column: 16\n        },\n        end: {\n          line: 149,\n          column: 46\n        }\n      },\n      \"60\": {\n        start: {\n          line: 152,\n          column: 16\n        },\n        end: {\n          line: 152,\n          column: 96\n        }\n      },\n      \"61\": {\n        start: {\n          line: 157,\n          column: 8\n        },\n        end: {\n          line: 157,\n          column: 28\n        }\n      },\n      \"62\": {\n        start: {\n          line: 158,\n          column: 8\n        },\n        end: {\n          line: 169,\n          column: 11\n        }\n      },\n      \"63\": {\n        start: {\n          line: 160,\n          column: 33\n        },\n        end: {\n          line: 160,\n          column: 52\n        }\n      },\n      \"64\": {\n        start: {\n          line: 163,\n          column: 16\n        },\n        end: {\n          line: 163,\n          column: 50\n        }\n      },\n      \"65\": {\n        start: {\n          line: 164,\n          column: 16\n        },\n        end: {\n          line: 164,\n          column: 49\n        }\n      },\n      \"66\": {\n        start: {\n          line: 167,\n          column: 16\n        },\n        end: {\n          line: 167,\n          column: 97\n        }\n      },\n      \"67\": {\n        start: {\n          line: 172,\n          column: 8\n        },\n        end: {\n          line: 172,\n          column: 38\n        }\n      },\n      \"68\": {\n        start: {\n          line: 175,\n          column: 8\n        },\n        end: {\n          line: 175,\n          column: 33\n        }\n      },\n      \"69\": {\n        start: {\n          line: 176,\n          column: 8\n        },\n        end: {\n          line: 176,\n          column: 39\n        }\n      },\n      \"70\": {\n        start: {\n          line: 177,\n          column: 8\n        },\n        end: {\n          line: 177,\n          column: 39\n        }\n      },\n      \"71\": {\n        start: {\n          line: 178,\n          column: 8\n        },\n        end: {\n          line: 178,\n          column: 37\n        }\n      },\n      \"72\": {\n        start: {\n          line: 179,\n          column: 8\n        },\n        end: {\n          line: 179,\n          column: 38\n        }\n      },\n      \"73\": {\n        start: {\n          line: 181,\n          column: 13\n        },\n        end: {\n          line: 188,\n          column: 6\n        }\n      },\n      \"74\": {\n        start: {\n          line: 181,\n          column: 41\n        },\n        end: {\n          line: 188,\n          column: 5\n        }\n      },\n      \"75\": {\n        start: {\n          line: 189,\n          column: 13\n        },\n        end: {\n          line: 192,\n          column: 6\n        }\n      },\n      \"76\": {\n        start: {\n          line: 194,\n          column: 0\n        },\n        end: {\n          line: 217,\n          column: 31\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 26,\n            column: 4\n          },\n          end: {\n            line: 26,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 26,\n            column: 86\n          },\n          end: {\n            line: 56,\n            column: 5\n          }\n        },\n        line: 26\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 57,\n            column: 4\n          },\n          end: {\n            line: 57,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 57,\n            column: 15\n          },\n          end: {\n            line: 60,\n            column: 5\n          }\n        },\n        line: 57\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 61,\n            column: 4\n          },\n          end: {\n            line: 61,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 61,\n            column: 22\n          },\n          end: {\n            line: 64,\n            column: 5\n          }\n        },\n        line: 61\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 65,\n            column: 4\n          },\n          end: {\n            line: 65,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 65,\n            column: 29\n          },\n          end: {\n            line: 73,\n            column: 5\n          }\n        },\n        line: 65\n      },\n      \"4\": {\n        name: \"(anonymous_4)\",\n        decl: {\n          start: {\n            line: 67,\n            column: 64\n          },\n          end: {\n            line: 67,\n            column: 65\n          }\n        },\n        loc: {\n          start: {\n            line: 67,\n            column: 78\n          },\n          end: {\n            line: 67,\n            column: 117\n          }\n        },\n        line: 67\n      },\n      \"5\": {\n        name: \"(anonymous_5)\",\n        decl: {\n          start: {\n            line: 70,\n            column: 64\n          },\n          end: {\n            line: 70,\n            column: 65\n          }\n        },\n        loc: {\n          start: {\n            line: 70,\n            column: 78\n          },\n          end: {\n            line: 71,\n            column: 55\n          }\n        },\n        line: 70\n      },\n      \"6\": {\n        name: \"(anonymous_6)\",\n        decl: {\n          start: {\n            line: 74,\n            column: 4\n          },\n          end: {\n            line: 74,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 74,\n            column: 39\n          },\n          end: {\n            line: 93,\n            column: 5\n          }\n        },\n        line: 74\n      },\n      \"7\": {\n        name: \"(anonymous_7)\",\n        decl: {\n          start: {\n            line: 94,\n            column: 4\n          },\n          end: {\n            line: 94,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 94,\n            column: 25\n          },\n          end: {\n            line: 103,\n            column: 5\n          }\n        },\n        line: 94\n      },\n      \"8\": {\n        name: \"(anonymous_8)\",\n        decl: {\n          start: {\n            line: 98,\n            column: 23\n          },\n          end: {\n            line: 98,\n            column: 24\n          }\n        },\n        loc: {\n          start: {\n            line: 98,\n            column: 35\n          },\n          end: {\n            line: 102,\n            column: 9\n          }\n        },\n        line: 98\n      },\n      \"9\": {\n        name: \"(anonymous_9)\",\n        decl: {\n          start: {\n            line: 104,\n            column: 4\n          },\n          end: {\n            line: 104,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 104,\n            column: 33\n          },\n          end: {\n            line: 106,\n            column: 5\n          }\n        },\n        line: 104\n      },\n      \"10\": {\n        name: \"(anonymous_10)\",\n        decl: {\n          start: {\n            line: 107,\n            column: 4\n          },\n          end: {\n            line: 107,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 107,\n            column: 18\n          },\n          end: {\n            line: 130,\n            column: 5\n          }\n        },\n        line: 107\n      },\n      \"11\": {\n        name: \"(anonymous_11)\",\n        decl: {\n          start: {\n            line: 115,\n            column: 27\n          },\n          end: {\n            line: 115,\n            column: 28\n          }\n        },\n        loc: {\n          start: {\n            line: 115,\n            column: 33\n          },\n          end: {\n            line: 115,\n            column: 52\n          }\n        },\n        line: 115\n      },\n      \"12\": {\n        name: \"(anonymous_12)\",\n        decl: {\n          start: {\n            line: 117,\n            column: 18\n          },\n          end: {\n            line: 117,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 117,\n            column: 28\n          },\n          end: {\n            line: 125,\n            column: 13\n          }\n        },\n        line: 117\n      },\n      \"13\": {\n        name: \"(anonymous_13)\",\n        decl: {\n          start: {\n            line: 126,\n            column: 19\n          },\n          end: {\n            line: 126,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 126,\n            column: 30\n          },\n          end: {\n            line: 128,\n            column: 13\n          }\n        },\n        line: 126\n      },\n      \"14\": {\n        name: \"(anonymous_14)\",\n        decl: {\n          start: {\n            line: 131,\n            column: 4\n          },\n          end: {\n            line: 131,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 131,\n            column: 24\n          },\n          end: {\n            line: 155,\n            column: 5\n          }\n        },\n        line: 131\n      },\n      \"15\": {\n        name: \"(anonymous_15)\",\n        decl: {\n          start: {\n            line: 135,\n            column: 27\n          },\n          end: {\n            line: 135,\n            column: 28\n          }\n        },\n        loc: {\n          start: {\n            line: 135,\n            column: 33\n          },\n          end: {\n            line: 135,\n            column: 52\n          }\n        },\n        line: 135\n      },\n      \"16\": {\n        name: \"(anonymous_16)\",\n        decl: {\n          start: {\n            line: 137,\n            column: 18\n          },\n          end: {\n            line: 137,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 137,\n            column: 29\n          },\n          end: {\n            line: 150,\n            column: 13\n          }\n        },\n        line: 137\n      },\n      \"17\": {\n        name: \"(anonymous_17)\",\n        decl: {\n          start: {\n            line: 140,\n            column: 46\n          },\n          end: {\n            line: 140,\n            column: 47\n          }\n        },\n        loc: {\n          start: {\n            line: 140,\n            column: 53\n          },\n          end: {\n            line: 140,\n            column: 75\n          }\n        },\n        line: 140\n      },\n      \"18\": {\n        name: \"(anonymous_18)\",\n        decl: {\n          start: {\n            line: 146,\n            column: 29\n          },\n          end: {\n            line: 146,\n            column: 30\n          }\n        },\n        loc: {\n          start: {\n            line: 146,\n            column: 36\n          },\n          end: {\n            line: 146,\n            column: 42\n          }\n        },\n        line: 146\n      },\n      \"19\": {\n        name: \"(anonymous_19)\",\n        decl: {\n          start: {\n            line: 147,\n            column: 30\n          },\n          end: {\n            line: 147,\n            column: 31\n          }\n        },\n        loc: {\n          start: {\n            line: 147,\n            column: 40\n          },\n          end: {\n            line: 147,\n            column: 45\n          }\n        },\n        line: 147\n      },\n      \"20\": {\n        name: \"(anonymous_20)\",\n        decl: {\n          start: {\n            line: 151,\n            column: 19\n          },\n          end: {\n            line: 151,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 151,\n            column: 30\n          },\n          end: {\n            line: 153,\n            column: 13\n          }\n        },\n        line: 151\n      },\n      \"21\": {\n        name: \"(anonymous_21)\",\n        decl: {\n          start: {\n            line: 156,\n            column: 4\n          },\n          end: {\n            line: 156,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 156,\n            column: 23\n          },\n          end: {\n            line: 170,\n            column: 5\n          }\n        },\n        line: 156\n      },\n      \"22\": {\n        name: \"(anonymous_22)\",\n        decl: {\n          start: {\n            line: 160,\n            column: 27\n          },\n          end: {\n            line: 160,\n            column: 28\n          }\n        },\n        loc: {\n          start: {\n            line: 160,\n            column: 33\n          },\n          end: {\n            line: 160,\n            column: 52\n          }\n        },\n        line: 160\n      },\n      \"23\": {\n        name: \"(anonymous_23)\",\n        decl: {\n          start: {\n            line: 162,\n            column: 18\n          },\n          end: {\n            line: 162,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 162,\n            column: 33\n          },\n          end: {\n            line: 165,\n            column: 13\n          }\n        },\n        line: 162\n      },\n      \"24\": {\n        name: \"(anonymous_24)\",\n        decl: {\n          start: {\n            line: 166,\n            column: 19\n          },\n          end: {\n            line: 166,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 166,\n            column: 30\n          },\n          end: {\n            line: 168,\n            column: 13\n          }\n        },\n        line: 166\n      },\n      \"25\": {\n        name: \"(anonymous_25)\",\n        decl: {\n          start: {\n            line: 171,\n            column: 4\n          },\n          end: {\n            line: 171,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 171,\n            column: 19\n          },\n          end: {\n            line: 173,\n            column: 5\n          }\n        },\n        line: 171\n      },\n      \"26\": {\n        name: \"(anonymous_26)\",\n        decl: {\n          start: {\n            line: 174,\n            column: 4\n          },\n          end: {\n            line: 174,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 174,\n            column: 19\n          },\n          end: {\n            line: 180,\n            column: 5\n          }\n        },\n        line: 174\n      },\n      \"27\": {\n        name: \"(anonymous_27)\",\n        decl: {\n          start: {\n            line: 181,\n            column: 35\n          },\n          end: {\n            line: 181,\n            column: 36\n          }\n        },\n        loc: {\n          start: {\n            line: 181,\n            column: 41\n          },\n          end: {\n            line: 188,\n            column: 5\n          }\n        },\n        line: 181\n      }\n    },\n    branchMap: {\n      \"0\": {\n        loc: {\n          start: {\n            line: 66,\n            column: 8\n          },\n          end: {\n            line: 72,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 66,\n            column: 8\n          },\n          end: {\n            line: 72,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 69,\n            column: 13\n          },\n          end: {\n            line: 72,\n            column: 9\n          }\n        }],\n        line: 66\n      },\n      \"1\": {\n        loc: {\n          start: {\n            line: 70,\n            column: 78\n          },\n          end: {\n            line: 71,\n            column: 55\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 70,\n            column: 78\n          },\n          end: {\n            line: 70,\n            column: 127\n          }\n        }, {\n          start: {\n            line: 71,\n            column: 16\n          },\n          end: {\n            line: 71,\n            column: 55\n          }\n        }],\n        line: 70\n      },\n      \"2\": {\n        loc: {\n          start: {\n            line: 75,\n            column: 38\n          },\n          end: {\n            line: 80,\n            column: 71\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 75,\n            column: 38\n          },\n          end: {\n            line: 75,\n            column: 70\n          }\n        }, {\n          start: {\n            line: 76,\n            column: 13\n          },\n          end: {\n            line: 76,\n            column: 36\n          }\n        }, {\n          start: {\n            line: 77,\n            column: 16\n          },\n          end: {\n            line: 80,\n            column: 70\n          }\n        }],\n        line: 75\n      },\n      \"3\": {\n        loc: {\n          start: {\n            line: 81,\n            column: 38\n          },\n          end: {\n            line: 85,\n            column: 71\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 81,\n            column: 38\n          },\n          end: {\n            line: 81,\n            column: 70\n          }\n        }, {\n          start: {\n            line: 82,\n            column: 13\n          },\n          end: {\n            line: 82,\n            column: 30\n          }\n        }, {\n          start: {\n            line: 83,\n            column: 16\n          },\n          end: {\n            line: 85,\n            column: 70\n          }\n        }],\n        line: 81\n      },\n      \"4\": {\n        loc: {\n          start: {\n            line: 86,\n            column: 36\n          },\n          end: {\n            line: 91,\n            column: 69\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 86,\n            column: 36\n          },\n          end: {\n            line: 86,\n            column: 66\n          }\n        }, {\n          start: {\n            line: 87,\n            column: 13\n          },\n          end: {\n            line: 87,\n            column: 40\n          }\n        }, {\n          start: {\n            line: 88,\n            column: 16\n          },\n          end: {\n            line: 91,\n            column: 68\n          }\n        }],\n        line: 86\n      },\n      \"5\": {\n        loc: {\n          start: {\n            line: 92,\n            column: 23\n          },\n          end: {\n            line: 92,\n            column: 92\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 92,\n            column: 23\n          },\n          end: {\n            line: 92,\n            column: 44\n          }\n        }, {\n          start: {\n            line: 92,\n            column: 48\n          },\n          end: {\n            line: 92,\n            column: 69\n          }\n        }, {\n          start: {\n            line: 92,\n            column: 73\n          },\n          end: {\n            line: 92,\n            column: 92\n          }\n        }],\n        line: 92\n      },\n      \"6\": {\n        loc: {\n          start: {\n            line: 99,\n            column: 12\n          },\n          end: {\n            line: 101,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 99,\n            column: 12\n          },\n          end: {\n            line: 101,\n            column: 13\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 99\n      },\n      \"7\": {\n        loc: {\n          start: {\n            line: 99,\n            column: 16\n          },\n          end: {\n            line: 99,\n            column: 60\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 99,\n            column: 16\n          },\n          end: {\n            line: 99,\n            column: 36\n          }\n        }, {\n          start: {\n            line: 99,\n            column: 40\n          },\n          end: {\n            line: 99,\n            column: 60\n          }\n        }],\n        line: 99\n      },\n      \"8\": {\n        loc: {\n          start: {\n            line: 108,\n            column: 8\n          },\n          end: {\n            line: 111,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 108,\n            column: 8\n          },\n          end: {\n            line: 111,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 108\n      },\n      \"9\": {\n        loc: {\n          start: {\n            line: 127,\n            column: 33\n          },\n          end: {\n            line: 127,\n            column: 86\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 127,\n            column: 33\n          },\n          end: {\n            line: 127,\n            column: 52\n          }\n        }, {\n          start: {\n            line: 127,\n            column: 56\n          },\n          end: {\n            line: 127,\n            column: 86\n          }\n        }],\n        line: 127\n      },\n      \"10\": {\n        loc: {\n          start: {\n            line: 141,\n            column: 16\n          },\n          end: {\n            line: 148,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 141,\n            column: 16\n          },\n          end: {\n            line: 148,\n            column: 17\n          }\n        }, {\n          start: {\n            line: 144,\n            column: 21\n          },\n          end: {\n            line: 148,\n            column: 17\n          }\n        }],\n        line: 141\n      },\n      \"11\": {\n        loc: {\n          start: {\n            line: 144,\n            column: 21\n          },\n          end: {\n            line: 148,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 144,\n            column: 21\n          },\n          end: {\n            line: 148,\n            column: 17\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 144\n      },\n      \"12\": {\n        loc: {\n          start: {\n            line: 152,\n            column: 33\n          },\n          end: {\n            line: 152,\n            column: 94\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 152,\n            column: 33\n          },\n          end: {\n            line: 152,\n            column: 52\n          }\n        }, {\n          start: {\n            line: 152,\n            column: 56\n          },\n          end: {\n            line: 152,\n            column: 94\n          }\n        }],\n        line: 152\n      },\n      \"13\": {\n        loc: {\n          start: {\n            line: 167,\n            column: 33\n          },\n          end: {\n            line: 167,\n            column: 95\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 167,\n            column: 33\n          },\n          end: {\n            line: 167,\n            column: 52\n          }\n        }, {\n          start: {\n            line: 167,\n            column: 56\n          },\n          end: {\n            line: 167,\n            column: 95\n          }\n        }],\n        line: 167\n      }\n    },\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0,\n      \"36\": 0,\n      \"37\": 0,\n      \"38\": 0,\n      \"39\": 0,\n      \"40\": 0,\n      \"41\": 0,\n      \"42\": 0,\n      \"43\": 0,\n      \"44\": 0,\n      \"45\": 0,\n      \"46\": 0,\n      \"47\": 0,\n      \"48\": 0,\n      \"49\": 0,\n      \"50\": 0,\n      \"51\": 0,\n      \"52\": 0,\n      \"53\": 0,\n      \"54\": 0,\n      \"55\": 0,\n      \"56\": 0,\n      \"57\": 0,\n      \"58\": 0,\n      \"59\": 0,\n      \"60\": 0,\n      \"61\": 0,\n      \"62\": 0,\n      \"63\": 0,\n      \"64\": 0,\n      \"65\": 0,\n      \"66\": 0,\n      \"67\": 0,\n      \"68\": 0,\n      \"69\": 0,\n      \"70\": 0,\n      \"71\": 0,\n      \"72\": 0,\n      \"73\": 0,\n      \"74\": 0,\n      \"75\": 0,\n      \"76\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0\n    },\n    b: {\n      \"0\": [0, 0],\n      \"1\": [0, 0],\n      \"2\": [0, 0, 0],\n      \"3\": [0, 0, 0],\n      \"4\": [0, 0, 0],\n      \"5\": [0, 0, 0],\n      \"6\": [0, 0],\n      \"7\": [0, 0],\n      \"8\": [0, 0],\n      \"9\": [0, 0],\n      \"10\": [0, 0],\n      \"11\": [0, 0],\n      \"12\": [0, 0],\n      \"13\": [0, 0]\n    },\n    inputSourceMap: {\n      version: 3,\n      file: \"contracts-list-page.component.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contract-management\\\\pages\\\\contracts-list-page\\\\contracts-list-page.component.ts\"],\n      names: [],\n      mappings: \";;;AAAA,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,iBAAiB,CAAC;AACzD,OAAO,EAAiB,SAAS,EAAU,SAAS,EAAE,MAAM,eAAe,CAAC;AAC5E,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAC7C,OAAO,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAC3D,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AACrD,OAAO,EAAE,kBAAkB,EAAE,MAAM,8BAA8B,CAAC;AAClE,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACzD,OAAO,EAAE,YAAY,EAAE,kBAAkB,EAAE,MAAM,6BAA6B,CAAC;AAC/E,OAAO,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAC3D,OAAO,EAAE,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AAChE,OAAO,EAAE,kBAAkB,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AAC7E,OAAO,EAAE,gBAAgB,EAAE,MAAM,2BAA2B,CAAC;AAC7D,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AACvD,OAAO,EAAE,uBAAuB,EAAE,MAAM,2EAA2E,CAAC;AAGpH,OAAO,EAAE,mBAAmB,EAAE,MAAM,qDAAqD,CAAC;AAC1F,OAAO,EAAE,eAAe,EAAE,MAAM,gDAAgD,CAAC;AACjF,OAAO,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAC9D,OAAO,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AAChD,OAAO,EAAE,QAAQ,EAAE,MAAM,MAAM,CAAC;AAwBzB,IAAM,0BAA0B,GAAhC,MAAM,0BAA0B;IA6BrC,YACmB,eAAgC,EAChC,mBAAwC,EACxC,MAAiB,EACjB,MAAc,EACd,OAA0B,EAC1B,KAAmB;QALnB,oBAAe,GAAf,eAAe,CAAiB;QAChC,wBAAmB,GAAnB,mBAAmB,CAAqB;QACxC,WAAM,GAAN,MAAM,CAAW;QACjB,WAAM,GAAN,MAAM,CAAQ;QACd,YAAO,GAAP,OAAO,CAAmB;QAC1B,UAAK,GAAL,KAAK,CAAc;QAlCtC,qBAAgB,GAAa;YAC3B,gBAAgB;YAChB,cAAc;YACd,oBAAoB;YACpB,UAAU;YACV,kBAAkB;YAClB,WAAW;YACX,SAAS;YACT,cAAc;YACd,cAAc;YACd,YAAY;YACZ,gBAAgB;YAChB,UAAU;YACV,QAAQ;YACR,SAAS;SACV,CAAC;QACF,eAAU,GAAG,IAAI,kBAAkB,EAAgB,CAAC;QACpD,iBAAY,GAAkB,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QACvD,kBAAa,GAAmB,EAAE,CAAC;QACnC,qBAAgB,GAAmB,EAAE,CAAC;QAEtC,yBAAoB,GAAG,EAAE,CAAC;QAC1B,yBAAoB,GAAG,EAAE,CAAC;QAC1B,uBAAkB,GAAG,EAAE,CAAC;IAYrB,CAAC;IAEJ,QAAQ;QACN,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAED,eAAe;QACb,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC3C,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;IACnC,CAAC;IAED,sBAAsB;QACpB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,CAC/D,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CACxC,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CACjD,CAAC,QAAQ,EAAE,EAAE,CACX,QAAQ,CAAC,YAAY,EAAE,IAAI,KAAK,IAAI,CAAC,YAAY;gBACjD,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAC1C,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,wBAAwB,CAAC,QAAsB;QACrD,MAAM,qBAAqB,GACzB,IAAI,CAAC,oBAAoB,KAAK,EAAE;YAChC,CAAC,QAAQ,CAAC,cAAc;gBACtB,QAAQ,CAAC,cAAc;qBACpB,QAAQ,EAAE;qBACV,WAAW,EAAE;qBACb,QAAQ,CAAC,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAE1D,MAAM,qBAAqB,GACzB,IAAI,CAAC,oBAAoB,KAAK,EAAE;YAChC,CAAC,QAAQ,CAAC,QAAQ;gBAChB,QAAQ,CAAC,QAAQ;qBACd,WAAW,EAAE;qBACb,QAAQ,CAAC,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAE1D,MAAM,mBAAmB,GACvB,IAAI,CAAC,kBAAkB,KAAK,EAAE;YAC9B,CAAC,QAAQ,CAAC,kBAAkB;gBAC1B,QAAQ,CAAC,kBAAkB;qBACxB,QAAQ,EAAE;qBACV,WAAW,EAAE;qBACb,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAExD,OAAO,OAAO,CACZ,qBAAqB,IAAI,qBAAqB,IAAI,mBAAmB,CACtE,CAAC;IACJ,CAAC;IAED,kBAAkB;QAChB,IAAI,CAAC,MAAM;aACR,IAAI,CAAC,uBAAuB,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;aAClD,WAAW,EAAE;aACb,SAAS,CAAC,CAAC,MAAyC,EAAE,EAAE;YACvD,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;gBACjD,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC,CAAC;IACP,CAAC;IAED,kBAAkB,CAAC,QAAsB;QACvC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IACpD,CAAC;IAED,WAAW;QACT,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,2CAA2C,CAAC,CAAC;YAChE,OAAO;QACT,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACpB,IAAI,CAAC,eAAe;aACjB,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC;aAClC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;aACzC,SAAS,CAAC;YACT,IAAI,EAAE,CAAC,IAAI,EAAE,EAAE;gBACb,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAC7C,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;gBACzC,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;gBAChB,IAAI,CAAC,QAAQ,GAAG,kBAAkB,IAAI,CAAC,YAAY,IACjD,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CACvC,MAAM,CAAC;gBACP,IAAI,CAAC,KAAK,EAAE,CAAC;gBACb,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;gBAChC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,qCAAqC,CAAC,CAAC;YAC5D,CAAC;YACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,8BAA8B,CAAC,CAAC;YAC1E,CAAC;SACF,CAAC,CAAC;IACP,CAAC;IAEO,iBAAiB;QACvB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACpB,IAAI,CAAC,mBAAmB;aACrB,MAAM,EAAE;aACR,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;aACzC,SAAS,CAAC;YACT,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE;gBACd,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;gBAE3B,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;gBAC7C,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC;gBAE7D,IAAI,UAAU,EAAE,CAAC;oBACf,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;gBAClC,CAAC;qBAAM,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC5B,IAAI,CAAC,YAAY,GAAG,KAAK;yBACtB,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;yBAClB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9B,CAAC;gBAED,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAChC,CAAC;YACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,sCAAsC,CAAC,CAAC;YAClF,CAAC;SACF,CAAC,CAAC;IACP,CAAC;IAEO,gBAAgB;QACtB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACpB,IAAI,CAAC,eAAe;aACjB,eAAe,EAAE;aACjB,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;aACzC,SAAS,CAAC;YACT,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE;gBAClB,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;gBAClC,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,SAAS,CAAC;YACnC,CAAC;YACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,uCAAuC,CAAC,CAAC;YACnF,CAAC;SACF,CAAC,CAAC;IACP,CAAC;IAED,YAAY;QACV,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAED,YAAY;QACV,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;QAC/B,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;QAC/B,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;;;;;;;;;;4BAlKA,SAAS,SAAC,YAAY;uBACtB,SAAS,SAAC,OAAO;;;AA3BP,0BAA0B;IAtBtC,SAAS,CAAC;QACT,QAAQ,EAAE,yBAAyB;QACnC,8BAAmD;QAEnD,UAAU,EAAE,IAAI;QAChB,OAAO,EAAE;YACP,eAAe;YACf,aAAa;YACb,aAAa;YACb,kBAAkB;YAClB,cAAc;YACd,cAAc;YACd,aAAa;YACb,kBAAkB;YAClB,gBAAgB;YAChB,eAAe;YACf,WAAW;YACX,YAAY;YACZ,QAAQ;YACR,YAAY;SACb;;KACF,CAAC;GACW,0BAA0B,CA6LtC\",\n      sourcesContent: [\"import { CurrencyPipe, DatePipe } from '@angular/common';\\nimport { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';\\nimport { FormsModule } from '@angular/forms';\\nimport { MatButtonModule } from '@angular/material/button';\\nimport { MatCardModule } from '@angular/material/card';\\nimport { MatDialog } from '@angular/material/dialog';\\nimport { MatFormFieldModule } from '@angular/material/form-field';\\nimport { MatIconModule } from '@angular/material/icon';\\nimport { MatInputModule } from '@angular/material/input';\\nimport { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';\\nimport { MatSelectModule } from '@angular/material/select';\\nimport { MatSort, MatSortModule } from '@angular/material/sort';\\nimport { MatTableDataSource, MatTableModule } from '@angular/material/table';\\nimport { MatTooltipModule } from '@angular/material/tooltip';\\nimport { Router, RouterModule } from '@angular/router';\\nimport { ContractDialogComponent } from '@contract-management/components/contract-dialog/contract-dialog.component';\\nimport { ContractList } from '@contract-management/models/contract-list.model';\\nimport { ContractYear } from '@contract-management/models/contract-year.model';\\nimport { ContractYearService } from '@contract-management/services/contract-year.service';\\nimport { ContractService } from '@contract-management/services/contract.service';\\nimport { AlertService } from '@shared/services/alert.service';\\nimport { NgxSpinnerService } from 'ngx-spinner';\\nimport { finalize } from 'rxjs';\\n\\n@Component({\\n  selector: 'app-contracts-list-page',\\n  templateUrl: './contracts-list-page.component.html',\\n  styleUrl: './contracts-list-page.component.scss',\\n  standalone: true,\\n  imports: [\\n    MatButtonModule,\\n    MatCardModule,\\n    MatIconModule,\\n    MatFormFieldModule,\\n    MatInputModule,\\n    MatTableModule,\\n    MatSortModule,\\n    MatPaginatorModule,\\n    MatTooltipModule,\\n    MatSelectModule,\\n    FormsModule,\\n    RouterModule,\\n    DatePipe,\\n    CurrencyPipe,\\n  ],\\n})\\nexport class ContractsListPageComponent implements OnInit, AfterViewInit {\\n  displayedColumns: string[] = [\\n    'contractNumber',\\n    'contractYear',\\n    'contractorIdNumber',\\n    'fullName',\\n    'subscriptionDate',\\n    'startDate',\\n    'endDate',\\n    'durationDays',\\n    'initialValue',\\n    'totalValue',\\n    'monthlyPayment',\\n    'addition',\\n    'hasCcp',\\n    'actions',\\n  ];\\n  dataSource = new MatTableDataSource<ContractList>();\\n  selectedYear: number | null = new Date().getFullYear();\\n  contractYears: ContractYear[] = [];\\n  allContractsData: ContractList[] = [];\\n\\n  contractNumberFilter = '';\\n  contractorNameFilter = '';\\n  contractorIdFilter = '';\\n\\n  @ViewChild(MatPaginator) paginator!: MatPaginator;\\n  @ViewChild(MatSort) sort!: MatSort;\\n\\n  constructor(\\n    private readonly contractService: ContractService,\\n    private readonly contractYearService: ContractYearService,\\n    private readonly dialog: MatDialog,\\n    private readonly router: Router,\\n    private readonly spinner: NgxSpinnerService,\\n    private readonly alert: AlertService,\\n  ) {}\\n\\n  ngOnInit(): void {\\n    this.loadContractList();\\n    this.loadContractYears();\\n  }\\n\\n  ngAfterViewInit() {\\n    this.dataSource.paginator = this.paginator;\\n    this.dataSource.sort = this.sort;\\n  }\\n\\n  applyIndividualFilters() {\\n    if (!this.selectedYear) {\\n      this.dataSource.data = this.allContractsData.filter((contract) =>\\n        this.matchesIndividualFilters(contract),\\n      );\\n    } else {\\n      this.dataSource.data = this.allContractsData.filter(\\n        (contract) =>\\n          contract.contractYear?.year === this.selectedYear &&\\n          this.matchesIndividualFilters(contract),\\n      );\\n    }\\n  }\\n\\n  private matchesIndividualFilters(contract: ContractList): boolean {\\n    const matchesContractNumber =\\n      this.contractNumberFilter === '' ||\\n      (contract.contractNumber &&\\n        contract.contractNumber\\n          .toString()\\n          .toUpperCase()\\n          .includes(this.contractNumberFilter.toUpperCase()));\\n\\n    const matchesContractorName =\\n      this.contractorNameFilter === '' ||\\n      (contract.fullName &&\\n        contract.fullName\\n          .toUpperCase()\\n          .includes(this.contractorNameFilter.toUpperCase()));\\n\\n    const matchesContractorId =\\n      this.contractorIdFilter === '' ||\\n      (contract.contractorIdNumber &&\\n        contract.contractorIdNumber\\n          .toString()\\n          .toUpperCase()\\n          .includes(this.contractorIdFilter.toUpperCase()));\\n\\n    return Boolean(\\n      matchesContractNumber && matchesContractorName && matchesContractorId,\\n    );\\n  }\\n\\n  openContractDialog(): void {\\n    this.dialog\\n      .open(ContractDialogComponent, { width: '1000px' })\\n      .afterClosed()\\n      .subscribe((result: 'created' | 'updated' | undefined) => {\\n        if (result === 'created' || result === 'updated') {\\n          this.loadContractList();\\n        }\\n      });\\n  }\\n\\n  handleEditContract(contract: ContractList): void {\\n    this.router.navigate(['/contratos', contract.id]);\\n  }\\n\\n  exportToCSV(): void {\\n    if (!this.selectedYear) {\\n      this.alert.warning('Por favor seleccione un a\\xF1o para exportar');\\n      return;\\n    }\\n\\n    this.spinner.show();\\n    this.contractService\\n      .exportContracts(this.selectedYear)\\n      .pipe(finalize(() => this.spinner.hide()))\\n      .subscribe({\\n        next: (blob) => {\\n          const url = window.URL.createObjectURL(blob);\\n          const link = document.createElement('a');\\n          link.href = url;\\n          link.download = `Contratos_MADS_${this.selectedYear}_${\\n            new Date().toISOString().split('T')[0]\\n          }.csv`;\\n          link.click();\\n          window.URL.revokeObjectURL(url);\\n          this.alert.success('Exportaci\\xF3n completada exitosamente');\\n        },\\n        error: (error) => {\\n          this.alert.error(error.error?.detail ?? 'Error al exportar contratos.');\\n        },\\n      });\\n  }\\n\\n  private loadContractYears(): void {\\n    this.spinner.show();\\n    this.contractYearService\\n      .getAll()\\n      .pipe(finalize(() => this.spinner.hide()))\\n      .subscribe({\\n        next: (years) => {\\n          this.contractYears = years;\\n\\n          const currentYear = new Date().getFullYear();\\n          const yearExists = years.some((y) => y.year === currentYear);\\n\\n          if (yearExists) {\\n            this.selectedYear = currentYear;\\n          } else if (years.length > 0) {\\n            this.selectedYear = years\\n              .map((y) => y.year)\\n              .sort((a, b) => b - a)[0];\\n          }\\n\\n          this.applyIndividualFilters();\\n        },\\n        error: (error) => {\\n          this.alert.error(error.error?.detail ?? 'Error al cargar los a\\xF1os de contrato');\\n        },\\n      });\\n  }\\n\\n  private loadContractList(): void {\\n    this.spinner.show();\\n    this.contractService\\n      .getContractList()\\n      .pipe(finalize(() => this.spinner.hide()))\\n      .subscribe({\\n        next: (contracts) => {\\n          this.allContractsData = contracts;\\n          this.dataSource.data = contracts;\\n        },\\n        error: (error) => {\\n          this.alert.error(error.error?.detail ?? 'Error al cargar la lista de contratos');\\n        },\\n      });\\n  }\\n\\n  onYearChange(): void {\\n    this.applyIndividualFilters();\\n  }\\n\\n  clearFilters(): void {\\n    this.selectedYear = null;\\n    this.contractNumberFilter = '';\\n    this.contractorNameFilter = '';\\n    this.contractorIdFilter = '';\\n    this.applyIndividualFilters();\\n  }\\n}\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"637b2881ad08c065b424e17d72c660a7a200d9d6\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_cwp8plf6 = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_cwp8plf6();\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./contracts-list-page.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./contracts-list-page.component.scss?ngResource\";\nimport { CurrencyPipe, DatePipe } from '@angular/common';\nimport { Component, ViewChild } from '@angular/core';\nimport { FormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatDialog } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatSort, MatSortModule } from '@angular/material/sort';\nimport { MatTableDataSource, MatTableModule } from '@angular/material/table';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { Router, RouterModule } from '@angular/router';\nimport { ContractDialogComponent } from '@contract-management/components/contract-dialog/contract-dialog.component';\nimport { ContractYearService } from '@contract-management/services/contract-year.service';\nimport { ContractService } from '@contract-management/services/contract.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { finalize } from 'rxjs';\ncov_cwp8plf6().s[0]++;\nlet ContractsListPageComponent = class ContractsListPageComponent {\n  constructor(contractService, contractYearService, dialog, router, spinner, alert) {\n    cov_cwp8plf6().f[0]++;\n    cov_cwp8plf6().s[1]++;\n    this.contractService = contractService;\n    cov_cwp8plf6().s[2]++;\n    this.contractYearService = contractYearService;\n    cov_cwp8plf6().s[3]++;\n    this.dialog = dialog;\n    cov_cwp8plf6().s[4]++;\n    this.router = router;\n    cov_cwp8plf6().s[5]++;\n    this.spinner = spinner;\n    cov_cwp8plf6().s[6]++;\n    this.alert = alert;\n    cov_cwp8plf6().s[7]++;\n    this.displayedColumns = ['contractNumber', 'contractYear', 'contractorIdNumber', 'fullName', 'subscriptionDate', 'startDate', 'endDate', 'durationDays', 'initialValue', 'totalValue', 'monthlyPayment', 'addition', 'hasCcp', 'actions'];\n    cov_cwp8plf6().s[8]++;\n    this.dataSource = new MatTableDataSource();\n    cov_cwp8plf6().s[9]++;\n    this.selectedYear = new Date().getFullYear();\n    cov_cwp8plf6().s[10]++;\n    this.contractYears = [];\n    cov_cwp8plf6().s[11]++;\n    this.allContractsData = [];\n    cov_cwp8plf6().s[12]++;\n    this.contractNumberFilter = '';\n    cov_cwp8plf6().s[13]++;\n    this.contractorNameFilter = '';\n    cov_cwp8plf6().s[14]++;\n    this.contractorIdFilter = '';\n  }\n  ngOnInit() {\n    cov_cwp8plf6().f[1]++;\n    cov_cwp8plf6().s[15]++;\n    this.loadContractList();\n    cov_cwp8plf6().s[16]++;\n    this.loadContractYears();\n  }\n  ngAfterViewInit() {\n    cov_cwp8plf6().f[2]++;\n    cov_cwp8plf6().s[17]++;\n    this.dataSource.paginator = this.paginator;\n    cov_cwp8plf6().s[18]++;\n    this.dataSource.sort = this.sort;\n  }\n  applyIndividualFilters() {\n    cov_cwp8plf6().f[3]++;\n    cov_cwp8plf6().s[19]++;\n    if (!this.selectedYear) {\n      cov_cwp8plf6().b[0][0]++;\n      cov_cwp8plf6().s[20]++;\n      this.dataSource.data = this.allContractsData.filter(contract => {\n        cov_cwp8plf6().f[4]++;\n        cov_cwp8plf6().s[21]++;\n        return this.matchesIndividualFilters(contract);\n      });\n    } else {\n      cov_cwp8plf6().b[0][1]++;\n      cov_cwp8plf6().s[22]++;\n      this.dataSource.data = this.allContractsData.filter(contract => {\n        cov_cwp8plf6().f[5]++;\n        cov_cwp8plf6().s[23]++;\n        return (cov_cwp8plf6().b[1][0]++, contract.contractYear?.year === this.selectedYear) && (cov_cwp8plf6().b[1][1]++, this.matchesIndividualFilters(contract));\n      });\n    }\n  }\n  matchesIndividualFilters(contract) {\n    cov_cwp8plf6().f[6]++;\n    const matchesContractNumber = (cov_cwp8plf6().s[24]++, (cov_cwp8plf6().b[2][0]++, this.contractNumberFilter === '') || (cov_cwp8plf6().b[2][1]++, contract.contractNumber) && (cov_cwp8plf6().b[2][2]++, contract.contractNumber.toString().toUpperCase().includes(this.contractNumberFilter.toUpperCase())));\n    const matchesContractorName = (cov_cwp8plf6().s[25]++, (cov_cwp8plf6().b[3][0]++, this.contractorNameFilter === '') || (cov_cwp8plf6().b[3][1]++, contract.fullName) && (cov_cwp8plf6().b[3][2]++, contract.fullName.toUpperCase().includes(this.contractorNameFilter.toUpperCase())));\n    const matchesContractorId = (cov_cwp8plf6().s[26]++, (cov_cwp8plf6().b[4][0]++, this.contractorIdFilter === '') || (cov_cwp8plf6().b[4][1]++, contract.contractorIdNumber) && (cov_cwp8plf6().b[4][2]++, contract.contractorIdNumber.toString().toUpperCase().includes(this.contractorIdFilter.toUpperCase())));\n    cov_cwp8plf6().s[27]++;\n    return Boolean((cov_cwp8plf6().b[5][0]++, matchesContractNumber) && (cov_cwp8plf6().b[5][1]++, matchesContractorName) && (cov_cwp8plf6().b[5][2]++, matchesContractorId));\n  }\n  openContractDialog() {\n    cov_cwp8plf6().f[7]++;\n    cov_cwp8plf6().s[28]++;\n    this.dialog.open(ContractDialogComponent, {\n      width: '1000px'\n    }).afterClosed().subscribe(result => {\n      cov_cwp8plf6().f[8]++;\n      cov_cwp8plf6().s[29]++;\n      if ((cov_cwp8plf6().b[7][0]++, result === 'created') || (cov_cwp8plf6().b[7][1]++, result === 'updated')) {\n        cov_cwp8plf6().b[6][0]++;\n        cov_cwp8plf6().s[30]++;\n        this.loadContractList();\n      } else {\n        cov_cwp8plf6().b[6][1]++;\n      }\n    });\n  }\n  handleEditContract(contract) {\n    cov_cwp8plf6().f[9]++;\n    cov_cwp8plf6().s[31]++;\n    this.router.navigate(['/contratos', contract.id]);\n  }\n  exportToCSV() {\n    cov_cwp8plf6().f[10]++;\n    cov_cwp8plf6().s[32]++;\n    if (!this.selectedYear) {\n      cov_cwp8plf6().b[8][0]++;\n      cov_cwp8plf6().s[33]++;\n      this.alert.warning('Por favor seleccione un año para exportar');\n      cov_cwp8plf6().s[34]++;\n      return;\n    } else {\n      cov_cwp8plf6().b[8][1]++;\n    }\n    cov_cwp8plf6().s[35]++;\n    this.spinner.show();\n    cov_cwp8plf6().s[36]++;\n    this.contractService.exportContracts(this.selectedYear).pipe(finalize(() => {\n      cov_cwp8plf6().f[11]++;\n      cov_cwp8plf6().s[37]++;\n      return this.spinner.hide();\n    })).subscribe({\n      next: blob => {\n        cov_cwp8plf6().f[12]++;\n        const url = (cov_cwp8plf6().s[38]++, window.URL.createObjectURL(blob));\n        const link = (cov_cwp8plf6().s[39]++, document.createElement('a'));\n        cov_cwp8plf6().s[40]++;\n        link.href = url;\n        cov_cwp8plf6().s[41]++;\n        link.download = `Contratos_MADS_${this.selectedYear}_${new Date().toISOString().split('T')[0]}.csv`;\n        cov_cwp8plf6().s[42]++;\n        link.click();\n        cov_cwp8plf6().s[43]++;\n        window.URL.revokeObjectURL(url);\n        cov_cwp8plf6().s[44]++;\n        this.alert.success('Exportación completada exitosamente');\n      },\n      error: error => {\n        cov_cwp8plf6().f[13]++;\n        cov_cwp8plf6().s[45]++;\n        this.alert.error((cov_cwp8plf6().b[9][0]++, error.error?.detail) ?? (cov_cwp8plf6().b[9][1]++, 'Error al exportar contratos.'));\n      }\n    });\n  }\n  loadContractYears() {\n    cov_cwp8plf6().f[14]++;\n    cov_cwp8plf6().s[46]++;\n    this.spinner.show();\n    cov_cwp8plf6().s[47]++;\n    this.contractYearService.getAll().pipe(finalize(() => {\n      cov_cwp8plf6().f[15]++;\n      cov_cwp8plf6().s[48]++;\n      return this.spinner.hide();\n    })).subscribe({\n      next: years => {\n        cov_cwp8plf6().f[16]++;\n        cov_cwp8plf6().s[49]++;\n        this.contractYears = years;\n        const currentYear = (cov_cwp8plf6().s[50]++, new Date().getFullYear());\n        const yearExists = (cov_cwp8plf6().s[51]++, years.some(y => {\n          cov_cwp8plf6().f[17]++;\n          cov_cwp8plf6().s[52]++;\n          return y.year === currentYear;\n        }));\n        cov_cwp8plf6().s[53]++;\n        if (yearExists) {\n          cov_cwp8plf6().b[10][0]++;\n          cov_cwp8plf6().s[54]++;\n          this.selectedYear = currentYear;\n        } else {\n          cov_cwp8plf6().b[10][1]++;\n          cov_cwp8plf6().s[55]++;\n          if (years.length > 0) {\n            cov_cwp8plf6().b[11][0]++;\n            cov_cwp8plf6().s[56]++;\n            this.selectedYear = years.map(y => {\n              cov_cwp8plf6().f[18]++;\n              cov_cwp8plf6().s[57]++;\n              return y.year;\n            }).sort((a, b) => {\n              cov_cwp8plf6().f[19]++;\n              cov_cwp8plf6().s[58]++;\n              return b - a;\n            })[0];\n          } else {\n            cov_cwp8plf6().b[11][1]++;\n          }\n        }\n        cov_cwp8plf6().s[59]++;\n        this.applyIndividualFilters();\n      },\n      error: error => {\n        cov_cwp8plf6().f[20]++;\n        cov_cwp8plf6().s[60]++;\n        this.alert.error((cov_cwp8plf6().b[12][0]++, error.error?.detail) ?? (cov_cwp8plf6().b[12][1]++, 'Error al cargar los años de contrato'));\n      }\n    });\n  }\n  loadContractList() {\n    cov_cwp8plf6().f[21]++;\n    cov_cwp8plf6().s[61]++;\n    this.spinner.show();\n    cov_cwp8plf6().s[62]++;\n    this.contractService.getContractList().pipe(finalize(() => {\n      cov_cwp8plf6().f[22]++;\n      cov_cwp8plf6().s[63]++;\n      return this.spinner.hide();\n    })).subscribe({\n      next: contracts => {\n        cov_cwp8plf6().f[23]++;\n        cov_cwp8plf6().s[64]++;\n        this.allContractsData = contracts;\n        cov_cwp8plf6().s[65]++;\n        this.dataSource.data = contracts;\n      },\n      error: error => {\n        cov_cwp8plf6().f[24]++;\n        cov_cwp8plf6().s[66]++;\n        this.alert.error((cov_cwp8plf6().b[13][0]++, error.error?.detail) ?? (cov_cwp8plf6().b[13][1]++, 'Error al cargar la lista de contratos'));\n      }\n    });\n  }\n  onYearChange() {\n    cov_cwp8plf6().f[25]++;\n    cov_cwp8plf6().s[67]++;\n    this.applyIndividualFilters();\n  }\n  clearFilters() {\n    cov_cwp8plf6().f[26]++;\n    cov_cwp8plf6().s[68]++;\n    this.selectedYear = null;\n    cov_cwp8plf6().s[69]++;\n    this.contractNumberFilter = '';\n    cov_cwp8plf6().s[70]++;\n    this.contractorNameFilter = '';\n    cov_cwp8plf6().s[71]++;\n    this.contractorIdFilter = '';\n    cov_cwp8plf6().s[72]++;\n    this.applyIndividualFilters();\n  }\n  static {\n    cov_cwp8plf6().s[73]++;\n    this.ctorParameters = () => {\n      cov_cwp8plf6().f[27]++;\n      cov_cwp8plf6().s[74]++;\n      return [{\n        type: ContractService\n      }, {\n        type: ContractYearService\n      }, {\n        type: MatDialog\n      }, {\n        type: Router\n      }, {\n        type: NgxSpinnerService\n      }, {\n        type: AlertService\n      }];\n    };\n  }\n  static {\n    cov_cwp8plf6().s[75]++;\n    this.propDecorators = {\n      paginator: [{\n        type: ViewChild,\n        args: [MatPaginator]\n      }],\n      sort: [{\n        type: ViewChild,\n        args: [MatSort]\n      }]\n    };\n  }\n};\ncov_cwp8plf6().s[76]++;\nContractsListPageComponent = __decorate([Component({\n  selector: 'app-contracts-list-page',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: true,\n  imports: [MatButtonModule, MatCardModule, MatIconModule, MatFormFieldModule, MatInputModule, MatTableModule, MatSortModule, MatPaginatorModule, MatTooltipModule, MatSelectModule, FormsModule, RouterModule, DatePipe, CurrencyPipe],\n  styles: [__NG_CLI_RESOURCE__1]\n})], ContractsListPageComponent);\nexport { ContractsListPageComponent };", "map": {"version": 3, "names": ["cov_cwp8plf6", "actualCoverage", "C<PERSON><PERSON>cyPipe", "DatePipe", "Component", "ViewChild", "FormsModule", "MatButtonModule", "MatCardModule", "MatDialog", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatPaginator", "MatPaginatorModule", "MatSelectModule", "MatSort", "MatSortModule", "MatTableDataSource", "MatTableModule", "MatTooltipModule", "Router", "RouterModule", "ContractDialogComponent", "ContractYearService", "ContractService", "AlertService", "NgxSpinnerService", "finalize", "s", "ContractsListPageComponent", "constructor", "contractService", "contractYearService", "dialog", "router", "spinner", "alert", "f", "displayedColumns", "dataSource", "selected<PERSON>ear", "Date", "getFullYear", "contractYears", "allContractsData", "contractNumberFilter", "contractorNameFilter", "contractorIdFilter", "ngOnInit", "loadContractList", "loadContractYears", "ngAfterViewInit", "paginator", "sort", "applyIndividualFilters", "b", "data", "filter", "contract", "matchesIndividualFilters", "contractYear", "year", "matchesContractNumber", "contractNumber", "toString", "toUpperCase", "includes", "matchesContractorName", "fullName", "matchesContractorId", "contractorIdNumber", "Boolean", "openContractDialog", "open", "width", "afterClosed", "subscribe", "result", "handleEditContract", "navigate", "id", "exportToCSV", "warning", "show", "exportContracts", "pipe", "hide", "next", "blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "toISOString", "split", "click", "revokeObjectURL", "success", "error", "detail", "getAll", "years", "currentYear", "yearExists", "some", "y", "length", "map", "a", "getContractList", "contracts", "onYearChange", "clearFilters", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\pages\\contracts-list-page\\contracts-list-page.component.ts"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>cy<PERSON>ip<PERSON>, DatePipe } from '@angular/common';\nimport { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';\nimport { FormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatDialog } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatSort, MatSortModule } from '@angular/material/sort';\nimport { MatTableDataSource, MatTableModule } from '@angular/material/table';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { Router, RouterModule } from '@angular/router';\nimport { ContractDialogComponent } from '@contract-management/components/contract-dialog/contract-dialog.component';\nimport { ContractList } from '@contract-management/models/contract-list.model';\nimport { ContractYear } from '@contract-management/models/contract-year.model';\nimport { ContractYearService } from '@contract-management/services/contract-year.service';\nimport { ContractService } from '@contract-management/services/contract.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { finalize } from 'rxjs';\n\n@Component({\n  selector: 'app-contracts-list-page',\n  templateUrl: './contracts-list-page.component.html',\n  styleUrl: './contracts-list-page.component.scss',\n  standalone: true,\n  imports: [\n    MatButtonModule,\n    MatCardModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatTableModule,\n    MatSortModule,\n    MatPaginatorModule,\n    MatTooltipModule,\n    MatSelectModule,\n    FormsModule,\n    RouterModule,\n    DatePipe,\n    CurrencyPipe,\n  ],\n})\nexport class ContractsListPageComponent implements OnInit, AfterViewInit {\n  displayedColumns: string[] = [\n    'contractNumber',\n    'contractYear',\n    'contractorIdNumber',\n    'fullName',\n    'subscriptionDate',\n    'startDate',\n    'endDate',\n    'durationDays',\n    'initialValue',\n    'totalValue',\n    'monthlyPayment',\n    'addition',\n    'hasCcp',\n    'actions',\n  ];\n  dataSource = new MatTableDataSource<ContractList>();\n  selectedYear: number | null = new Date().getFullYear();\n  contractYears: ContractYear[] = [];\n  allContractsData: ContractList[] = [];\n\n  contractNumberFilter = '';\n  contractorNameFilter = '';\n  contractorIdFilter = '';\n\n  @ViewChild(MatPaginator) paginator!: MatPaginator;\n  @ViewChild(MatSort) sort!: MatSort;\n\n  constructor(\n    private readonly contractService: ContractService,\n    private readonly contractYearService: ContractYearService,\n    private readonly dialog: MatDialog,\n    private readonly router: Router,\n    private readonly spinner: NgxSpinnerService,\n    private readonly alert: AlertService,\n  ) {}\n\n  ngOnInit(): void {\n    this.loadContractList();\n    this.loadContractYears();\n  }\n\n  ngAfterViewInit() {\n    this.dataSource.paginator = this.paginator;\n    this.dataSource.sort = this.sort;\n  }\n\n  applyIndividualFilters() {\n    if (!this.selectedYear) {\n      this.dataSource.data = this.allContractsData.filter((contract) =>\n        this.matchesIndividualFilters(contract),\n      );\n    } else {\n      this.dataSource.data = this.allContractsData.filter(\n        (contract) =>\n          contract.contractYear?.year === this.selectedYear &&\n          this.matchesIndividualFilters(contract),\n      );\n    }\n  }\n\n  private matchesIndividualFilters(contract: ContractList): boolean {\n    const matchesContractNumber =\n      this.contractNumberFilter === '' ||\n      (contract.contractNumber &&\n        contract.contractNumber\n          .toString()\n          .toUpperCase()\n          .includes(this.contractNumberFilter.toUpperCase()));\n\n    const matchesContractorName =\n      this.contractorNameFilter === '' ||\n      (contract.fullName &&\n        contract.fullName\n          .toUpperCase()\n          .includes(this.contractorNameFilter.toUpperCase()));\n\n    const matchesContractorId =\n      this.contractorIdFilter === '' ||\n      (contract.contractorIdNumber &&\n        contract.contractorIdNumber\n          .toString()\n          .toUpperCase()\n          .includes(this.contractorIdFilter.toUpperCase()));\n\n    return Boolean(\n      matchesContractNumber && matchesContractorName && matchesContractorId,\n    );\n  }\n\n  openContractDialog(): void {\n    this.dialog\n      .open(ContractDialogComponent, { width: '1000px' })\n      .afterClosed()\n      .subscribe((result: 'created' | 'updated' | undefined) => {\n        if (result === 'created' || result === 'updated') {\n          this.loadContractList();\n        }\n      });\n  }\n\n  handleEditContract(contract: ContractList): void {\n    this.router.navigate(['/contratos', contract.id]);\n  }\n\n  exportToCSV(): void {\n    if (!this.selectedYear) {\n      this.alert.warning('Por favor seleccione un año para exportar');\n      return;\n    }\n\n    this.spinner.show();\n    this.contractService\n      .exportContracts(this.selectedYear)\n      .pipe(finalize(() => this.spinner.hide()))\n      .subscribe({\n        next: (blob) => {\n          const url = window.URL.createObjectURL(blob);\n          const link = document.createElement('a');\n          link.href = url;\n          link.download = `Contratos_MADS_${this.selectedYear}_${\n            new Date().toISOString().split('T')[0]\n          }.csv`;\n          link.click();\n          window.URL.revokeObjectURL(url);\n          this.alert.success('Exportación completada exitosamente');\n        },\n        error: (error) => {\n          this.alert.error(error.error?.detail ?? 'Error al exportar contratos.');\n        },\n      });\n  }\n\n  private loadContractYears(): void {\n    this.spinner.show();\n    this.contractYearService\n      .getAll()\n      .pipe(finalize(() => this.spinner.hide()))\n      .subscribe({\n        next: (years) => {\n          this.contractYears = years;\n\n          const currentYear = new Date().getFullYear();\n          const yearExists = years.some((y) => y.year === currentYear);\n\n          if (yearExists) {\n            this.selectedYear = currentYear;\n          } else if (years.length > 0) {\n            this.selectedYear = years\n              .map((y) => y.year)\n              .sort((a, b) => b - a)[0];\n          }\n\n          this.applyIndividualFilters();\n        },\n        error: (error) => {\n          this.alert.error(error.error?.detail ?? 'Error al cargar los años de contrato');\n        },\n      });\n  }\n\n  private loadContractList(): void {\n    this.spinner.show();\n    this.contractService\n      .getContractList()\n      .pipe(finalize(() => this.spinner.hide()))\n      .subscribe({\n        next: (contracts) => {\n          this.allContractsData = contracts;\n          this.dataSource.data = contracts;\n        },\n        error: (error) => {\n          this.alert.error(error.error?.detail ?? 'Error al cargar la lista de contratos');\n        },\n      });\n  }\n\n  onYearChange(): void {\n    this.applyIndividualFilters();\n  }\n\n  clearFilters(): void {\n    this.selectedYear = null;\n    this.contractNumberFilter = '';\n    this.contractorNameFilter = '';\n    this.contractorIdFilter = '';\n    this.applyIndividualFilters();\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAYS;IAAAA,YAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,YAAA;;;;AAZT,SAASE,YAAY,EAAEC,QAAQ,QAAQ,iBAAiB;AACxD,SAAwBC,SAAS,EAAUC,SAAS,QAAQ,eAAe;AAC3E,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,6BAA6B;AAC9E,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,OAAO,EAAEC,aAAa,QAAQ,wBAAwB;AAC/D,SAASC,kBAAkB,EAAEC,cAAc,QAAQ,yBAAyB;AAC5E,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,MAAM,EAAEC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,uBAAuB,QAAQ,2EAA2E;AAGnH,SAASC,mBAAmB,QAAQ,qDAAqD;AACzF,SAASC,eAAe,QAAQ,gDAAgD;AAChF,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,iBAAiB,QAAQ,aAAa;AAC/C,SAASC,QAAQ,QAAQ,MAAM;AAAC5B,YAAA,GAAA6B,CAAA;AAwBzB,IAAMC,0BAA0B,GAAhC,MAAMA,0BAA0B;EA6BrCC,YACmBC,eAAgC,EAChCC,mBAAwC,EACxCC,MAAiB,EACjBC,MAAc,EACdC,OAA0B,EAC1BC,KAAmB;IAAArC,YAAA,GAAAsC,CAAA;IAAAtC,YAAA,GAAA6B,CAAA;IALnB,KAAAG,eAAe,GAAfA,eAAe;IAAiBhC,YAAA,GAAA6B,CAAA;IAChC,KAAAI,mBAAmB,GAAnBA,mBAAmB;IAAqBjC,YAAA,GAAA6B,CAAA;IACxC,KAAAK,MAAM,GAANA,MAAM;IAAWlC,YAAA,GAAA6B,CAAA;IACjB,KAAAM,MAAM,GAANA,MAAM;IAAQnC,YAAA,GAAA6B,CAAA;IACd,KAAAO,OAAO,GAAPA,OAAO;IAAmBpC,YAAA,GAAA6B,CAAA;IAC1B,KAAAQ,KAAK,GAALA,KAAK;IAAcrC,YAAA,GAAA6B,CAAA;IAlCtC,KAAAU,gBAAgB,GAAa,CAC3B,gBAAgB,EAChB,cAAc,EACd,oBAAoB,EACpB,UAAU,EACV,kBAAkB,EAClB,WAAW,EACX,SAAS,EACT,cAAc,EACd,cAAc,EACd,YAAY,EACZ,gBAAgB,EAChB,UAAU,EACV,QAAQ,EACR,SAAS,CACV;IAACvC,YAAA,GAAA6B,CAAA;IACF,KAAAW,UAAU,GAAG,IAAItB,kBAAkB,EAAgB;IAAClB,YAAA,GAAA6B,CAAA;IACpD,KAAAY,YAAY,GAAkB,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;IAAC3C,YAAA,GAAA6B,CAAA;IACvD,KAAAe,aAAa,GAAmB,EAAE;IAAC5C,YAAA,GAAA6B,CAAA;IACnC,KAAAgB,gBAAgB,GAAmB,EAAE;IAAC7C,YAAA,GAAA6B,CAAA;IAEtC,KAAAiB,oBAAoB,GAAG,EAAE;IAAC9C,YAAA,GAAA6B,CAAA;IAC1B,KAAAkB,oBAAoB,GAAG,EAAE;IAAC/C,YAAA,GAAA6B,CAAA;IAC1B,KAAAmB,kBAAkB,GAAG,EAAE;EAYpB;EAEHC,QAAQA,CAAA;IAAAjD,YAAA,GAAAsC,CAAA;IAAAtC,YAAA,GAAA6B,CAAA;IACN,IAAI,CAACqB,gBAAgB,EAAE;IAAClD,YAAA,GAAA6B,CAAA;IACxB,IAAI,CAACsB,iBAAiB,EAAE;EAC1B;EAEAC,eAAeA,CAAA;IAAApD,YAAA,GAAAsC,CAAA;IAAAtC,YAAA,GAAA6B,CAAA;IACb,IAAI,CAACW,UAAU,CAACa,SAAS,GAAG,IAAI,CAACA,SAAS;IAACrD,YAAA,GAAA6B,CAAA;IAC3C,IAAI,CAACW,UAAU,CAACc,IAAI,GAAG,IAAI,CAACA,IAAI;EAClC;EAEAC,sBAAsBA,CAAA;IAAAvD,YAAA,GAAAsC,CAAA;IAAAtC,YAAA,GAAA6B,CAAA;IACpB,IAAI,CAAC,IAAI,CAACY,YAAY,EAAE;MAAAzC,YAAA,GAAAwD,CAAA;MAAAxD,YAAA,GAAA6B,CAAA;MACtB,IAAI,CAACW,UAAU,CAACiB,IAAI,GAAG,IAAI,CAACZ,gBAAgB,CAACa,MAAM,CAAEC,QAAQ,IAC3D;QAAA3D,YAAA,GAAAsC,CAAA;QAAAtC,YAAA,GAAA6B,CAAA;QAAA,WAAI,CAAC+B,wBAAwB,CAACD,QAAQ,CAAC;MAAD,CAAC,CACxC;IACH,CAAC,MAAM;MAAA3D,YAAA,GAAAwD,CAAA;MAAAxD,YAAA,GAAA6B,CAAA;MACL,IAAI,CAACW,UAAU,CAACiB,IAAI,GAAG,IAAI,CAACZ,gBAAgB,CAACa,MAAM,CAChDC,QAAQ,IACP;QAAA3D,YAAA,GAAAsC,CAAA;QAAAtC,YAAA,GAAA6B,CAAA;QAAA,QAAA7B,YAAA,GAAAwD,CAAA,UAAAG,QAAQ,CAACE,YAAY,EAAEC,IAAI,KAAK,IAAI,CAACrB,YAAY,MAAAzC,YAAA,GAAAwD,CAAA,UACjD,IAAI,CAACI,wBAAwB,CAACD,QAAQ,CAAC;MAAD,CAAC,CAC1C;IACH;EACF;EAEQC,wBAAwBA,CAACD,QAAsB;IAAA3D,YAAA,GAAAsC,CAAA;IACrD,MAAMyB,qBAAqB,IAAA/D,YAAA,GAAA6B,CAAA,QACzB,CAAA7B,YAAA,GAAAwD,CAAA,cAAI,CAACV,oBAAoB,KAAK,EAAE,KAC/B,CAAA9C,YAAA,GAAAwD,CAAA,UAAAG,QAAQ,CAACK,cAAc,MAAAhE,YAAA,GAAAwD,CAAA,UACtBG,QAAQ,CAACK,cAAc,CACpBC,QAAQ,EAAE,CACVC,WAAW,EAAE,CACbC,QAAQ,CAAC,IAAI,CAACrB,oBAAoB,CAACoB,WAAW,EAAE,CAAC,CAAC;IAEzD,MAAME,qBAAqB,IAAApE,YAAA,GAAA6B,CAAA,QACzB,CAAA7B,YAAA,GAAAwD,CAAA,cAAI,CAACT,oBAAoB,KAAK,EAAE,KAC/B,CAAA/C,YAAA,GAAAwD,CAAA,UAAAG,QAAQ,CAACU,QAAQ,MAAArE,YAAA,GAAAwD,CAAA,UAChBG,QAAQ,CAACU,QAAQ,CACdH,WAAW,EAAE,CACbC,QAAQ,CAAC,IAAI,CAACpB,oBAAoB,CAACmB,WAAW,EAAE,CAAC,CAAC;IAEzD,MAAMI,mBAAmB,IAAAtE,YAAA,GAAA6B,CAAA,QACvB,CAAA7B,YAAA,GAAAwD,CAAA,cAAI,CAACR,kBAAkB,KAAK,EAAE,KAC7B,CAAAhD,YAAA,GAAAwD,CAAA,UAAAG,QAAQ,CAACY,kBAAkB,MAAAvE,YAAA,GAAAwD,CAAA,UAC1BG,QAAQ,CAACY,kBAAkB,CACxBN,QAAQ,EAAE,CACVC,WAAW,EAAE,CACbC,QAAQ,CAAC,IAAI,CAACnB,kBAAkB,CAACkB,WAAW,EAAE,CAAC,CAAC;IAAClE,YAAA,GAAA6B,CAAA;IAExD,OAAO2C,OAAO,CACZ,CAAAxE,YAAA,GAAAwD,CAAA,UAAAO,qBAAqB,MAAA/D,YAAA,GAAAwD,CAAA,UAAIY,qBAAqB,MAAApE,YAAA,GAAAwD,CAAA,UAAIc,mBAAmB,EACtE;EACH;EAEAG,kBAAkBA,CAAA;IAAAzE,YAAA,GAAAsC,CAAA;IAAAtC,YAAA,GAAA6B,CAAA;IAChB,IAAI,CAACK,MAAM,CACRwC,IAAI,CAACnD,uBAAuB,EAAE;MAAEoD,KAAK,EAAE;IAAQ,CAAE,CAAC,CAClDC,WAAW,EAAE,CACbC,SAAS,CAAEC,MAAyC,IAAI;MAAA9E,YAAA,GAAAsC,CAAA;MAAAtC,YAAA,GAAA6B,CAAA;MACvD,IAAI,CAAA7B,YAAA,GAAAwD,CAAA,UAAAsB,MAAM,KAAK,SAAS,MAAA9E,YAAA,GAAAwD,CAAA,UAAIsB,MAAM,KAAK,SAAS,GAAE;QAAA9E,YAAA,GAAAwD,CAAA;QAAAxD,YAAA,GAAA6B,CAAA;QAChD,IAAI,CAACqB,gBAAgB,EAAE;MACzB,CAAC;QAAAlD,YAAA,GAAAwD,CAAA;MAAA;IACH,CAAC,CAAC;EACN;EAEAuB,kBAAkBA,CAACpB,QAAsB;IAAA3D,YAAA,GAAAsC,CAAA;IAAAtC,YAAA,GAAA6B,CAAA;IACvC,IAAI,CAACM,MAAM,CAAC6C,QAAQ,CAAC,CAAC,YAAY,EAAErB,QAAQ,CAACsB,EAAE,CAAC,CAAC;EACnD;EAEAC,WAAWA,CAAA;IAAAlF,YAAA,GAAAsC,CAAA;IAAAtC,YAAA,GAAA6B,CAAA;IACT,IAAI,CAAC,IAAI,CAACY,YAAY,EAAE;MAAAzC,YAAA,GAAAwD,CAAA;MAAAxD,YAAA,GAAA6B,CAAA;MACtB,IAAI,CAACQ,KAAK,CAAC8C,OAAO,CAAC,2CAA2C,CAAC;MAACnF,YAAA,GAAA6B,CAAA;MAChE;IACF,CAAC;MAAA7B,YAAA,GAAAwD,CAAA;IAAA;IAAAxD,YAAA,GAAA6B,CAAA;IAED,IAAI,CAACO,OAAO,CAACgD,IAAI,EAAE;IAACpF,YAAA,GAAA6B,CAAA;IACpB,IAAI,CAACG,eAAe,CACjBqD,eAAe,CAAC,IAAI,CAAC5C,YAAY,CAAC,CAClC6C,IAAI,CAAC1D,QAAQ,CAAC,MAAM;MAAA5B,YAAA,GAAAsC,CAAA;MAAAtC,YAAA,GAAA6B,CAAA;MAAA,WAAI,CAACO,OAAO,CAACmD,IAAI,EAAE;IAAF,CAAE,CAAC,CAAC,CACzCV,SAAS,CAAC;MACTW,IAAI,EAAGC,IAAI,IAAI;QAAAzF,YAAA,GAAAsC,CAAA;QACb,MAAMoD,GAAG,IAAA1F,YAAA,GAAA6B,CAAA,QAAG8D,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;QAC5C,MAAMK,IAAI,IAAA9F,YAAA,GAAA6B,CAAA,QAAGkE,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QAAChG,YAAA,GAAA6B,CAAA;QACzCiE,IAAI,CAACG,IAAI,GAAGP,GAAG;QAAC1F,YAAA,GAAA6B,CAAA;QAChBiE,IAAI,CAACI,QAAQ,GAAG,kBAAkB,IAAI,CAACzD,YAAY,IACjD,IAAIC,IAAI,EAAE,CAACyD,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CACvC,MAAM;QAACpG,YAAA,GAAA6B,CAAA;QACPiE,IAAI,CAACO,KAAK,EAAE;QAACrG,YAAA,GAAA6B,CAAA;QACb8D,MAAM,CAACC,GAAG,CAACU,eAAe,CAACZ,GAAG,CAAC;QAAC1F,YAAA,GAAA6B,CAAA;QAChC,IAAI,CAACQ,KAAK,CAACkE,OAAO,CAAC,qCAAqC,CAAC;MAC3D,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QAAAxG,YAAA,GAAAsC,CAAA;QAAAtC,YAAA,GAAA6B,CAAA;QACf,IAAI,CAACQ,KAAK,CAACmE,KAAK,CAAC,CAAAxG,YAAA,GAAAwD,CAAA,UAAAgD,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAAzG,YAAA,GAAAwD,CAAA,UAAI,8BAA8B,EAAC;MACzE;KACD,CAAC;EACN;EAEQL,iBAAiBA,CAAA;IAAAnD,YAAA,GAAAsC,CAAA;IAAAtC,YAAA,GAAA6B,CAAA;IACvB,IAAI,CAACO,OAAO,CAACgD,IAAI,EAAE;IAACpF,YAAA,GAAA6B,CAAA;IACpB,IAAI,CAACI,mBAAmB,CACrByE,MAAM,EAAE,CACRpB,IAAI,CAAC1D,QAAQ,CAAC,MAAM;MAAA5B,YAAA,GAAAsC,CAAA;MAAAtC,YAAA,GAAA6B,CAAA;MAAA,WAAI,CAACO,OAAO,CAACmD,IAAI,EAAE;IAAF,CAAE,CAAC,CAAC,CACzCV,SAAS,CAAC;MACTW,IAAI,EAAGmB,KAAK,IAAI;QAAA3G,YAAA,GAAAsC,CAAA;QAAAtC,YAAA,GAAA6B,CAAA;QACd,IAAI,CAACe,aAAa,GAAG+D,KAAK;QAE1B,MAAMC,WAAW,IAAA5G,YAAA,GAAA6B,CAAA,QAAG,IAAIa,IAAI,EAAE,CAACC,WAAW,EAAE;QAC5C,MAAMkE,UAAU,IAAA7G,YAAA,GAAA6B,CAAA,QAAG8E,KAAK,CAACG,IAAI,CAAEC,CAAC,IAAK;UAAA/G,YAAA,GAAAsC,CAAA;UAAAtC,YAAA,GAAA6B,CAAA;UAAA,OAAAkF,CAAC,CAACjD,IAAI,KAAK8C,WAAW;QAAX,CAAW,CAAC;QAAC5G,YAAA,GAAA6B,CAAA;QAE7D,IAAIgF,UAAU,EAAE;UAAA7G,YAAA,GAAAwD,CAAA;UAAAxD,YAAA,GAAA6B,CAAA;UACd,IAAI,CAACY,YAAY,GAAGmE,WAAW;QACjC,CAAC,MAAM;UAAA5G,YAAA,GAAAwD,CAAA;UAAAxD,YAAA,GAAA6B,CAAA;UAAA,IAAI8E,KAAK,CAACK,MAAM,GAAG,CAAC,EAAE;YAAAhH,YAAA,GAAAwD,CAAA;YAAAxD,YAAA,GAAA6B,CAAA;YAC3B,IAAI,CAACY,YAAY,GAAGkE,KAAK,CACtBM,GAAG,CAAEF,CAAC,IAAK;cAAA/G,YAAA,GAAAsC,CAAA;cAAAtC,YAAA,GAAA6B,CAAA;cAAA,OAAAkF,CAAC,CAACjD,IAAI;YAAJ,CAAI,CAAC,CAClBR,IAAI,CAAC,CAAC4D,CAAC,EAAE1D,CAAC,KAAK;cAAAxD,YAAA,GAAAsC,CAAA;cAAAtC,YAAA,GAAA6B,CAAA;cAAA,OAAA2B,CAAC,GAAG0D,CAAC;YAAD,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC;YAAAlH,YAAA,GAAAwD,CAAA;UAAA;QAAD;QAACxD,YAAA,GAAA6B,CAAA;QAED,IAAI,CAAC0B,sBAAsB,EAAE;MAC/B,CAAC;MACDiD,KAAK,EAAGA,KAAK,IAAI;QAAAxG,YAAA,GAAAsC,CAAA;QAAAtC,YAAA,GAAA6B,CAAA;QACf,IAAI,CAACQ,KAAK,CAACmE,KAAK,CAAC,CAAAxG,YAAA,GAAAwD,CAAA,WAAAgD,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAAzG,YAAA,GAAAwD,CAAA,WAAI,sCAAsC,EAAC;MACjF;KACD,CAAC;EACN;EAEQN,gBAAgBA,CAAA;IAAAlD,YAAA,GAAAsC,CAAA;IAAAtC,YAAA,GAAA6B,CAAA;IACtB,IAAI,CAACO,OAAO,CAACgD,IAAI,EAAE;IAACpF,YAAA,GAAA6B,CAAA;IACpB,IAAI,CAACG,eAAe,CACjBmF,eAAe,EAAE,CACjB7B,IAAI,CAAC1D,QAAQ,CAAC,MAAM;MAAA5B,YAAA,GAAAsC,CAAA;MAAAtC,YAAA,GAAA6B,CAAA;MAAA,WAAI,CAACO,OAAO,CAACmD,IAAI,EAAE;IAAF,CAAE,CAAC,CAAC,CACzCV,SAAS,CAAC;MACTW,IAAI,EAAG4B,SAAS,IAAI;QAAApH,YAAA,GAAAsC,CAAA;QAAAtC,YAAA,GAAA6B,CAAA;QAClB,IAAI,CAACgB,gBAAgB,GAAGuE,SAAS;QAACpH,YAAA,GAAA6B,CAAA;QAClC,IAAI,CAACW,UAAU,CAACiB,IAAI,GAAG2D,SAAS;MAClC,CAAC;MACDZ,KAAK,EAAGA,KAAK,IAAI;QAAAxG,YAAA,GAAAsC,CAAA;QAAAtC,YAAA,GAAA6B,CAAA;QACf,IAAI,CAACQ,KAAK,CAACmE,KAAK,CAAC,CAAAxG,YAAA,GAAAwD,CAAA,WAAAgD,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAAzG,YAAA,GAAAwD,CAAA,WAAI,uCAAuC,EAAC;MAClF;KACD,CAAC;EACN;EAEA6D,YAAYA,CAAA;IAAArH,YAAA,GAAAsC,CAAA;IAAAtC,YAAA,GAAA6B,CAAA;IACV,IAAI,CAAC0B,sBAAsB,EAAE;EAC/B;EAEA+D,YAAYA,CAAA;IAAAtH,YAAA,GAAAsC,CAAA;IAAAtC,YAAA,GAAA6B,CAAA;IACV,IAAI,CAACY,YAAY,GAAG,IAAI;IAACzC,YAAA,GAAA6B,CAAA;IACzB,IAAI,CAACiB,oBAAoB,GAAG,EAAE;IAAC9C,YAAA,GAAA6B,CAAA;IAC/B,IAAI,CAACkB,oBAAoB,GAAG,EAAE;IAAC/C,YAAA,GAAA6B,CAAA;IAC/B,IAAI,CAACmB,kBAAkB,GAAG,EAAE;IAAChD,YAAA,GAAA6B,CAAA;IAC7B,IAAI,CAAC0B,sBAAsB,EAAE;EAC/B;;;;;;;;;;;;;;;;;;;;;;;;;cAlKClD,SAAS;QAAAkH,IAAA,GAAC1G,YAAY;MAAA;;cACtBR,SAAS;QAAAkH,IAAA,GAACvG,OAAO;MAAA;;;;;AA3BPc,0BAA0B,GAAA0F,UAAA,EAtBtCpH,SAAS,CAAC;EACTqH,QAAQ,EAAE,yBAAyB;EACnCC,QAAA,EAAAC,oBAAmD;EAEnDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPtH,eAAe,EACfC,aAAa,EACbG,aAAa,EACbD,kBAAkB,EAClBE,cAAc,EACdO,cAAc,EACdF,aAAa,EACbH,kBAAkB,EAClBM,gBAAgB,EAChBL,eAAe,EACfT,WAAW,EACXgB,YAAY,EACZnB,QAAQ,EACRD,YAAY,CACb;;CACF,CAAC,C,EACW4B,0BAA0B,CA6LtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}