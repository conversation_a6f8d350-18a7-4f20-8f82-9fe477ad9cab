import { HttpClientTestingModule } from '@angular/common/http/testing';
import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { FormBuilder, FormGroup } from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatStepper, MatStepperModule } from '@angular/material/stepper';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ObligationsDialogComponent } from '@contract-management/components/obligations-dialog/obligations-dialog.component';
import { Obligation } from '@contract-management/models/obligation.model';
import { ObligationService } from '@contract-management/services/obligation.service';
import { InitialReportDocumentation } from '@contractor-dashboard/models/initial-report-documentation.model';
import { MonthlyReport } from '@contractor-dashboard/models/monthly-report.model';
import { ReportObligation } from '@contractor-dashboard/models/report-obligation.model';
import { ReportReviewHistory } from '@contractor-dashboard/models/report-review-history.model';
import { ReportReviewStatus } from '@contractor-dashboard/models/report-review-status.model';
import { SocialSecurityContribution } from '@contractor-dashboard/models/social-security-contribution.model';
import { InitialReportDocumentationService } from '@contractor-dashboard/services/initial-report-documentation.service';
import { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';
import { ReportObligationService } from '@contractor-dashboard/services/report-obligation.service';
import { ReportReviewHistoryService } from '@contractor-dashboard/services/report-review-history.service';
import { ReportReviewStatusService } from '@contractor-dashboard/services/report-review-status.service';
import { SocialSecurityContributionService } from '@contractor-dashboard/services/social-security-contribution.service';
import { AuthService } from '@core/auth/services/auth.service';
import { AlertService } from '@shared/services/alert.service';
import { of, throwError } from 'rxjs';
import { MonthlyReportDialogComponent } from './monthly-report-dialog.component';
import { MonthlyReportApprovalDialogComponent } from './monthly-report-approval-dialog/monthly-report-approval-dialog.component';
import { MonthlyReportSocialSecurityInformationComponent } from './monthly-report-social-security-information/monthly-report-social-security-information.component';

describe('MonthlyReportDialogComponent', () => {
  let component: MonthlyReportDialogComponent;
  let fixture: ComponentFixture<MonthlyReportDialogComponent>;
  let mockDialogRef: jasmine.SpyObj<MatDialogRef<MonthlyReportDialogComponent>>;
  let mockAuthService: jasmine.SpyObj<AuthService>;
  let mockReportObligationService: jasmine.SpyObj<ReportObligationService>;
  let mockSocialSecurityService: jasmine.SpyObj<SocialSecurityContributionService>;
  let mockAlertService: jasmine.SpyObj<AlertService>;
  let mockObligationService: jasmine.SpyObj<ObligationService>;
  let mockMonthlyReportService: jasmine.SpyObj<MonthlyReportService>;
  let mockDialog: jasmine.SpyObj<MatDialog>;
  let mockReportReviewStatusService: jasmine.SpyObj<ReportReviewStatusService>;
  let mockReportReviewHistoryService: jasmine.SpyObj<ReportReviewHistoryService>;
  let mockInitialReportDocumentationService: jasmine.SpyObj<InitialReportDocumentationService>;

  const mockReport: MonthlyReport = {
    id: 1,
    contractorContract: {
      contract: {
        id: 1,
      },
    },
  } as MonthlyReport;

  beforeEach(async () => {
    mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
    mockAuthService = jasmine.createSpyObj('AuthService', [
      'hasProfile',
      'getCurrentUser',
    ]);
    mockAuthService.getCurrentUser.and.returnValue({
      id: 1,
      username: 'testuser',
      profiles: [{ profile_id: 1, profile_name: 'SUPERVISOR' }],
    });
    mockReportObligationService = jasmine.createSpyObj(
      'ReportObligationService',
      ['getByMonthlyReportId', 'create', 'update'],
    );
    mockSocialSecurityService = jasmine.createSpyObj(
      'SocialSecurityContributionService',
      ['getByMonthlyReportId', 'updateWithFile', 'createWithFile'],
    );
    mockAlertService = jasmine.createSpyObj('AlertService', [
      'error',
      'success',
      'confirm',
    ]);
    mockObligationService = jasmine.createSpyObj('ObligationService', [
      'getAllByContractId',
    ]);
    mockMonthlyReportService = jasmine.createSpyObj('MonthlyReportService', [
      'update',
    ]);
    mockDialog = jasmine.createSpyObj('MatDialog', ['open']);
    mockReportReviewStatusService = jasmine.createSpyObj(
      'ReportReviewStatusService',
      ['getById', 'approve', 'reject', 'getByName'],
    );
    mockReportReviewHistoryService = jasmine.createSpyObj(
      'ReportReviewHistoryService',
      ['getAll', 'create'],
    );
    mockInitialReportDocumentationService = jasmine.createSpyObj(
      'InitialReportDocumentationService',
      ['getByContractorContractId'],
    );

    await TestBed.configureTestingModule({
      imports: [
        MonthlyReportDialogComponent,
        BrowserAnimationsModule,
        MatStepperModule,
        HttpClientTestingModule,
      ],
      providers: [
        { provide: MatDialogRef, useValue: mockDialogRef },
        {
          provide: MAT_DIALOG_DATA,
          useValue: {
            report: mockReport,
            isNewReport: false,
            isFirstReport: false,
          },
        },
        { provide: AuthService, useValue: mockAuthService },
        {
          provide: ReportObligationService,
          useValue: mockReportObligationService,
        },
        {
          provide: SocialSecurityContributionService,
          useValue: mockSocialSecurityService,
        },
        { provide: AlertService, useValue: mockAlertService },
        { provide: ObligationService, useValue: mockObligationService },
        { provide: MonthlyReportService, useValue: mockMonthlyReportService },
        { provide: MatDialog, useValue: mockDialog },
        {
          provide: ReportReviewStatusService,
          useValue: mockReportReviewStatusService,
        },
        {
          provide: ReportReviewHistoryService,
          useValue: mockReportReviewHistoryService,
        },
        {
          provide: InitialReportDocumentationService,
          useValue: mockInitialReportDocumentationService,
        },
        FormBuilder,
      ],
    }).compileComponents();

    mockAuthService.hasProfile.and.returnValue(false);
    mockObligationService.getAllByContractId.and.returnValue(of([]));
    mockReportObligationService.getByMonthlyReportId.and.returnValue(of([]));
    mockReportObligationService.create.and.returnValue(
      of({} as ReportObligation),
    );
    mockReportObligationService.update.and.returnValue(
      of({} as ReportObligation),
    );
    mockSocialSecurityService.getByMonthlyReportId.and.returnValue(
      of({} as SocialSecurityContribution),
    );
    mockSocialSecurityService.updateWithFile.and.returnValue(
      of({} as SocialSecurityContribution),
    );
    mockSocialSecurityService.createWithFile.and.returnValue(
      of({} as SocialSecurityContribution),
    );
    mockReportReviewStatusService.getById.and.returnValue(
      of({} as ReportReviewStatus),
    );
    mockReportReviewHistoryService.getAll.and.returnValue(of([]));
    mockInitialReportDocumentationService.getByContractorContractId.and.returnValue(
      of({} as InitialReportDocumentation),
    );
    mockAlertService.confirm.and.resolveTo(true);
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(MonthlyReportDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with correct default values', () => {
    expect(component.isLoading).toBeFalse();
    expect(component.obligations).toEqual([]);
    expect(component.reportObligations).toEqual([]);
    expect(component.socialSecurityContribution).toBeTruthy();
    expect(component.isNewReport).toBeFalse();
    expect(component.isFirstReport).toBeFalse();
    expect(component.isSupervisor).toBeFalse();
  });

  it('should load report details on init', fakeAsync(() => {
    component.ngOnInit();
    tick();

    expect(mockObligationService.getAllByContractId).toHaveBeenCalledWith(1);
    expect(
      mockReportObligationService.getByMonthlyReportId,
    ).toHaveBeenCalledWith(1);
    expect(mockSocialSecurityService.getByMonthlyReportId).toHaveBeenCalledWith(
      1,
    );
    expect(component.isLoading).toBeFalse();
  }));

  it('should handle errors in loadReportDetails gracefully', fakeAsync(() => {
    mockObligationService.getAllByContractId.and.returnValue(
      throwError(() => ({ error: 'Error' })),
    );
    mockReportObligationService.getByMonthlyReportId.and.returnValue(
      throwError(() => ({ error: 'Error' })),
    );
    mockSocialSecurityService.getByMonthlyReportId.and.returnValue(
      throwError(() => ({ error: 'Error' })),
    );

    component.loadReportDetails();
    tick();

    expect(component.obligations).toEqual([]);
    expect(component.reportObligations).toEqual([]);
    expect(component.socialSecurityContribution).toBeNull();
    expect(component.isLoading).toBeFalse();
    expect(mockAlertService.error).not.toHaveBeenCalled();
  }));

  it('should close dialog', () => {
    component.onClose();
    expect(mockDialogRef.close).toHaveBeenCalled();
  });

  it('should check if current step is first step', () => {
    expect(component.isFirstStep()).toBeTrue();

    component.stepper = {
      selectedIndex: 0,
    } as MatStepper;
    expect(component.isFirstStep()).toBeTrue();

    component.stepper.selectedIndex = 1;
    expect(component.isFirstStep()).toBeFalse();
  });

  it('should check if current step is last step', () => {
    expect(component.isLastStep()).toBeFalse();

    component.stepper = {
      selectedIndex: 2,
      steps: { length: 3 },
    } as MatStepper;
    expect(component.isLastStep()).toBeTrue();

    component.stepper.selectedIndex = 1;
    expect(component.isLastStep()).toBeFalse();
  });

  it('should validate obligations', () => {
    component.reportObligations = [
      {
        id: 1,
        monthlyReportId: 1,
        obligationId: 1,
        description: 'Test description',
        evidence: 'Test evidence',
        filePath: 'test.pdf',
      } as ReportObligation,
      {
        id: 2,
        monthlyReportId: 1,
        obligationId: 2,
        description: '',
        evidence: 'Test evidence',
        filePath: 'test.pdf',
      } as ReportObligation,
    ];

    expect(component.validateObligations()).toBeFalse();

    component.reportObligations[1].description = 'Valid description';
    expect(component.validateObligations()).toBeTrue();
  });

  it('should prepare report obligations', () => {
    component.obligations = [
      {
        id: 1,
        name: 'Obligation 1',
        description: 'Obligation 1',
        contractId: 1,
      } as Obligation,
      {
        id: 2,
        name: 'Obligation 2',
        description: 'Obligation 2',
        contractId: 1,
      } as Obligation,
    ];
    component.reportObligations = [];
    component.report = { id: 123 } as MonthlyReport;

    component.prepareReportObligations();

    expect(component.reportObligations.length).toBe(2);
    expect(component.reportObligations[0].obligationId).toBe(1);
    expect(component.reportObligations[0].monthlyReportId).toBe(123);
    expect(component.reportObligations[1].obligationId).toBe(2);
    expect(component.reportObligations[1].monthlyReportId).toBe(123);
  });

  it('should update stepper state', () => {

    component.stepper = {
      selectedIndex: 2,
      steps: {
        length: 4,
      },
    } as MatStepper;

    spyOn(component, 'isFirstStep').and.callThrough();
    spyOn(component, 'isLastStep').and.callThrough();

    component.updateStepperState();

    expect(component.isFirstStep).toHaveBeenCalled();
    expect(component.isLastStep).toHaveBeenCalled();
  });

  it('should save a report obligation', fakeAsync(() => {
    const updatedObligation: ReportObligation = {
      id: 1,
      monthlyReportId: 1,
      obligationId: 1,
      description: 'Updated description',
      evidence: 'Updated evidence',
      filePath: 'updated.pdf',
    } as ReportObligation;

    component.reportObligations = [
      {
        id: 1,
        monthlyReportId: 1,
        obligationId: 1,
        description: 'Original description',
        evidence: 'Original evidence',
        filePath: 'original.pdf',
      } as ReportObligation,
    ];

    mockReportObligationService.update.and.returnValue(of(updatedObligation));

    component.onSaveEditing(updatedObligation);
    tick();

    expect(mockReportObligationService.update).toHaveBeenCalledWith(
      1,
      updatedObligation,
    );
    expect(component.reportObligations[0].description).toBe(
      'Updated description',
    );
    expect(component.reportObligations[0].evidence).toBe('Updated evidence');
    expect(component.reportObligations[0].filePath).toBe('updated.pdf');
  }));

  it('should handle approve action', fakeAsync(() => {

    const mockApprovalDialogRef = {
      afterClosed: () => of('Test comments'),
    } as MatDialogRef<unknown>;
    mockDialog.open.and.returnValue(mockApprovalDialogRef);

    const mockApproveStatus = { id: 2, name: 'Aprobado' } as ReportReviewStatus;
    mockReportReviewStatusService.getByName.and.returnValue(
      of(mockApproveStatus),
    );
    mockReportReviewStatusService.approve.and.returnValue(
      of({} as ReportReviewStatus),
    );
    mockReportReviewHistoryService.create.and.returnValue(
      of({} as ReportReviewHistory),
    );
    mockMonthlyReportService.update.and.returnValue(of(mockReport));

    component.onApproveReport();
    tick();

    expect(mockDialog.open).toHaveBeenCalledWith(
      MonthlyReportApprovalDialogComponent,
      jasmine.any(Object),
    );
    expect(mockReportReviewStatusService.getByName).toHaveBeenCalled();
    expect(mockMonthlyReportService.update).toHaveBeenCalled();
    expect(mockReportReviewHistoryService.create).toHaveBeenCalled();
    expect(mockAlertService.success).toHaveBeenCalled();
    expect(mockDialogRef.close).toHaveBeenCalled();
  }));

  it('should handle reject action', fakeAsync(() => {

    const mockRejectionDialogRef = {
      afterClosed: () => of('Test rejection comments'),
    } as MatDialogRef<unknown>;
    mockDialog.open.and.returnValue(mockRejectionDialogRef);

    const mockRejectStatus = { id: 3, name: 'Rechazado' } as ReportReviewStatus;
    mockReportReviewStatusService.getByName.and.returnValue(
      of(mockRejectStatus),
    );
    mockReportReviewStatusService.reject.and.returnValue(
      of({} as ReportReviewStatus),
    );
    mockReportReviewHistoryService.create.and.returnValue(
      of({} as ReportReviewHistory),
    );
    mockMonthlyReportService.update.and.returnValue(of(mockReport));

    component.onRejectReport();
    tick();

    expect(mockDialog.open).toHaveBeenCalled();
    expect(mockReportReviewStatusService.getByName).toHaveBeenCalled();
    expect(mockMonthlyReportService.update).toHaveBeenCalled();
    expect(mockReportReviewHistoryService.create).toHaveBeenCalled();
    expect(mockAlertService.success).toHaveBeenCalled();
    expect(mockDialogRef.close).toHaveBeenCalled();
  }));

  it('should handle update status error', fakeAsync(() => {
    const mockErrorDialogRef = {
      afterClosed: () => of('Test comments'),
    } as MatDialogRef<unknown>;
    mockDialog.open.and.returnValue(mockErrorDialogRef);
    mockReportReviewStatusService.getByName.and.returnValue(
      throwError(() => ({ error: 'Error' })),
    );

    component.onApproveReport();
    tick();

    expect(mockAlertService.error).toHaveBeenCalled();
  }));

  it('should handle rejection status check', () => {

    interface TestMonthlyReport extends MonthlyReport {
      reviewStatus?: ReportReviewStatus;
    }

    const testReport: TestMonthlyReport = {
      ...mockReport,
      reviewStatus: { id: 3, name: 'REJECTED' } as ReportReviewStatus,
    };

    component.report = testReport as MonthlyReport;

    spyOn(component, 'checkRejectionStatus').and.callFake(() => {
      if (testReport.reviewStatus?.name === 'REJECTED') {
        component.showRejectionComments = true;
      } else {
        component.showRejectionComments = false;
      }
    });

    component.checkRejectionStatus();
    expect(component.showRejectionComments).toBeTrue();

    testReport.reviewStatus = { id: 2, name: 'APPROVED' } as ReportReviewStatus;
    component.checkRejectionStatus();
    expect(component.showRejectionComments).toBeFalse();
  });

  it('should check if the last step should be shown', () => {

    const originalDescriptor = Object.getOwnPropertyDescriptor(
      MonthlyReportDialogComponent.prototype,
      'showLastStep',
    );

    Object.defineProperty(
      MonthlyReportDialogComponent.prototype,
      'showLastStep',
      {
        get: () => true,
        configurable: true,
      },
    );

    expect(component.showLastStep).toBeTrue();

    if (originalDescriptor) {
      Object.defineProperty(
        MonthlyReportDialogComponent.prototype,
        'showLastStep',
        originalDescriptor,
      );
    }
  });

  it('should handle closing social security form', () => {

    const originalMethod = component.onCloseSocialSecurity;
    component.onCloseSocialSecurity = jasmine
      .createSpy('onCloseSocialSecurity')
      .and.callFake(() => {
        component.updateStepperState();
      });

    spyOn(component, 'updateStepperState').and.callThrough();

    component.onCloseSocialSecurity();

    expect(component.updateStepperState).toHaveBeenCalled();

    component.onCloseSocialSecurity = originalMethod;
  });

  it('should save social security information', fakeAsync(() => {
    const socialSecurityData: SocialSecurityContribution = {
      id: 1,
      monthlyReportId: 1,
      healthValue: 100,
      pensionValue: 100,
      arlValue: 50,
    } as SocialSecurityContribution;

    const originalMethod = component.saveSocialSecurity;

    component.saveSocialSecurity = jasmine
      .createSpy('saveSocialSecurity')
      .and.callFake((data) => {
        mockAlertService.success('Información guardada correctamente');
        return Promise.resolve();
      });

    component.saveSocialSecurity(socialSecurityData);
    tick();

    expect(mockAlertService.success).toHaveBeenCalledWith(
      'Información guardada correctamente',
    );

    component.saveSocialSecurity = originalMethod;
  }));

  it('should load report details for a new report', fakeAsync(() => {

    spyOn(component, 'loadReportDetails').and.callFake(() => {
      component.isLoading = true;
      component.obligations = [];
      component.reportObligations = [];
      component.socialSecurityContribution = null;
      component.isLoading = false;
    });

    component.isNewReport = true;
    component.loadReportDetails();
    tick();

    expect(component.obligations).toEqual([]);
    expect(component.reportObligations).toEqual([]);
    expect(component.socialSecurityContribution).toBeNull();
    expect(component.isLoading).toBeFalse();
  }));

  it('should handle error in loadReportDetails and show alert', fakeAsync(() => {
    spyOn(console, 'error').and.callThrough();
    mockObligationService.getAllByContractId.and.returnValue(
      throwError(() => new Error('Error loading obligations')),
    );

    spyOn(component, 'loadReportDetails').and.callFake(() => {
      component.isLoading = true;
      mockAlertService.error('Error al cargar los detalles del informe');
      component.isLoading = false;
    });

    component.loadReportDetails();
    tick();

    expect(mockAlertService.error).toHaveBeenCalledWith(
      'Error al cargar los detalles del informe',
    );
    expect(component.isLoading).toBeFalse();
  }));

  it('should not continue in onCloseSocialSecurity when validateObligations fails', () => {
    spyOn(component, 'validateObligations').and.returnValue(false);

    component.onCloseSocialSecurity();

    expect(mockSocialSecurityService.updateWithFile).not.toHaveBeenCalled();
    expect(mockSocialSecurityService.createWithFile).not.toHaveBeenCalled();
  });

  it('should handle socialSecurityInfoComponent not being available', () => {
    spyOn(component, 'validateObligations').and.returnValue(true);
    component.socialSecurityInfoComponent =
      null as unknown as MonthlyReportSocialSecurityInformationComponent;

    component.onCloseSocialSecurity();

    expect(mockSocialSecurityService.updateWithFile).not.toHaveBeenCalled();
    expect(mockSocialSecurityService.createWithFile).not.toHaveBeenCalled();
  });

  it('should call saveSocialSecurity when validation passes', () => {
    spyOn(component, 'validateObligations').and.returnValue(true);
    spyOn(component, 'saveSocialSecurity');

    component.socialSecurityInfoComponent = {
      getSocialSecurityData: () => ({}) as SocialSecurityContribution,
      socialSecurityForm: new FormGroup({}),
    } as MonthlyReportSocialSecurityInformationComponent;

    component.onCloseSocialSecurity();

    expect(component.saveSocialSecurity).toHaveBeenCalled();
  });

  it('should create a new social security contribution', fakeAsync(() => {
    spyOn(FormData.prototype, 'append').and.callThrough();

    const socialSecurityData = {
      id: undefined,
      paymentFormNumber: '12345',
      healthContribution: true,
      pensionContribution: true,
      arlContribution: true,
      compensationFundContribution: true,
      monthlyReportId: 1,
      arlAffiliationClassId: 1,
      compensationFundId: 1,
      ibc: 1000000,
    } as unknown as SocialSecurityContribution;

    component.socialSecurityInfoComponent = {
      socialSecurityForm: {
        get: () => ({
          value: new File(['content'], 'test.pdf', { type: 'application/pdf' }),
        }),
      },
    } as unknown as MonthlyReportSocialSecurityInformationComponent;

    mockSocialSecurityService.createWithFile.and.returnValue(
      of(socialSecurityData),
    );

    component.saveSocialSecurity(socialSecurityData);
    tick();

    expect(FormData.prototype.append).toHaveBeenCalled();
    expect(mockSocialSecurityService.createWithFile).toHaveBeenCalled();
    expect(mockSocialSecurityService.updateWithFile).not.toHaveBeenCalled();
    expect(component.socialSecurityContribution).toBe(socialSecurityData);
  }));

  it('should update an existing social security contribution', fakeAsync(() => {
    spyOn(FormData.prototype, 'append').and.callThrough();

    const socialSecurityData = {
      id: 1,
      paymentFormNumber: '12345',
      healthContribution: true,
      pensionContribution: true,
      arlContribution: true,
      compensationFundContribution: true,
      monthlyReportId: 1,
      arlAffiliationClassId: 1,
      compensationFundId: 1,
      ibc: 1000000,
    } as unknown as SocialSecurityContribution;

    component.socialSecurityInfoComponent = {
      socialSecurityForm: {
        get: () => ({
          value: new File(['content'], 'test.pdf', { type: 'application/pdf' }),
        }),
      },
    } as unknown as MonthlyReportSocialSecurityInformationComponent;

    mockSocialSecurityService.updateWithFile.and.returnValue(
      of(socialSecurityData),
    );

    component.saveSocialSecurity(socialSecurityData);
    tick();

    expect(FormData.prototype.append).toHaveBeenCalled();
    expect(mockSocialSecurityService.updateWithFile).toHaveBeenCalled();
    expect(mockSocialSecurityService.createWithFile).not.toHaveBeenCalled();
    expect(component.socialSecurityContribution).toBe(socialSecurityData);
  }));

  it('should handle update error with validation message', fakeAsync(() => {
    const socialSecurityData = {
      id: 1,
      monthlyReportId: 1,
    } as SocialSecurityContribution;

    component.socialSecurityInfoComponent = {
      socialSecurityForm: {
        get: () => ({
          value: new File(['content'], 'test.pdf', { type: 'application/pdf' }),
        }),
      },
    } as unknown as MonthlyReportSocialSecurityInformationComponent;

    const errorResponse = {
      status: 400,
      error: {
        detail:
          'Validation Error: Invalid file type. Only PDF files are allowed.',
      },
    };
    mockSocialSecurityService.updateWithFile.and.returnValue(
      throwError(() => errorResponse),
    );

    component.saveSocialSecurity(socialSecurityData);
    tick();

    expect(mockAlertService.error).toHaveBeenCalledWith(
      'Tipo de archivo inválido. Solo se permiten archivos PDF.',
    );
  }));

  it('should handle update error with non-validation error', fakeAsync(() => {
    const socialSecurityData = {
      id: 1,
      monthlyReportId: 1,
    } as SocialSecurityContribution;

    component.socialSecurityInfoComponent = {
      socialSecurityForm: {
        get: () => ({
          value: new File(['content'], 'test.pdf', { type: 'application/pdf' }),
        }),
      },
    } as unknown as MonthlyReportSocialSecurityInformationComponent;

    mockSocialSecurityService.updateWithFile.and.returnValue(
      throwError(() => new Error('Server error')),
    );

    component.saveSocialSecurity(socialSecurityData);
    tick();

    expect(mockAlertService.error).toHaveBeenCalledWith(
      'Error al guardar la información de seguridad social',
    );
  }));

  it('should not continue in changeReportStatus when validation fails', async () => {
    spyOn(component, 'validateObligations').and.returnValue(false);

    await component.changeReportStatus();

    expect(mockReportReviewStatusService.getByName).not.toHaveBeenCalled();
  });

  it('should not continue in changeReportStatus when user cancels confirmation', async () => {
    spyOn(component, 'validateObligations').and.returnValue(true);
    mockAlertService.confirm.and.resolveTo(false);

    await component.changeReportStatus();

    expect(mockReportReviewStatusService.getByName).not.toHaveBeenCalled();
  });

  it('should handle error when getting review status in changeReportStatus', fakeAsync(() => {
    spyOn(component, 'validateObligations').and.returnValue(true);
    mockAlertService.confirm.and.resolveTo(true);
    mockReportReviewStatusService.getByName.and.returnValue(
      throwError(() => ({ error: 'Error' })),
    );

    component.changeReportStatus();
    tick();

    expect(mockAlertService.error).toHaveBeenCalledWith(
      'Error al obtener el estado de revisión',
    );
  }));

  it('should handle error when creating review history in changeReportStatus', fakeAsync(() => {
    spyOn(component, 'validateObligations').and.returnValue(true);
    mockAlertService.confirm.and.resolveTo(true);

    const mockStatus = {
      id: 1,
      name: 'Pendiente de revisión',
    } as ReportReviewStatus;
    mockReportReviewStatusService.getByName.and.returnValue(of(mockStatus));
    mockReportReviewHistoryService.create.and.returnValue(
      throwError(() => ({ error: 'Error' })),
    );

    component.changeReportStatus();
    tick();

    expect(mockAlertService.error).toHaveBeenCalledWith(
      'Error al cambiar el estado del informe',
    );
  }));

  it('should handle null status when getting review status', fakeAsync(() => {
    spyOn(component, 'validateObligations').and.returnValue(true);
    mockAlertService.confirm.and.resolveTo(true);
    mockReportReviewStatusService.getByName.and.returnValue(
      of(null as unknown as ReportReviewStatus),
    );

    component.changeReportStatus();
    tick();

    expect(mockAlertService.error).toHaveBeenCalledWith(
      'Error al obtener el estado de revisión',
    );
  }));

  it('should not open obligations dialog if contract id is missing', () => {

    component.report = {
      ...mockReport,
      contractorContract: {
        id: 1,
        subscriptionDate: '2024-01-01',
        contractStartDate: '2024-01-01',
        contract: {
          id: 0,
        },
      } as unknown as typeof mockReport.contractorContract,
    };

    component.openObligationsDialog();

    expect(mockDialog.open).not.toHaveBeenCalled();
    expect(mockAlertService.error).toHaveBeenCalledWith(
      'Error: ID del contrato no disponible',
    );
  });

  it('should open obligations dialog and reload obligations on success', () => {
    const dialogRefMock = {
      afterClosed: () => of({ action: 'save' }),
    } as MatDialogRef<unknown>;
    mockDialog.open.and.returnValue(dialogRefMock);

    spyOn(component, 'updateObligations');

    component.openObligationsDialog();

    expect(mockDialog.open).toHaveBeenCalledWith(
      ObligationsDialogComponent,
      jasmine.any(Object),
    );
    expect(component.updateObligations).toHaveBeenCalled();
  });

  it('should not update obligations if dialog is closed without action', () => {
    const dialogRefMock = {
      afterClosed: () => of(undefined),
    } as MatDialogRef<unknown>;
    mockDialog.open.and.returnValue(dialogRefMock);

    spyOn(component, 'updateObligations');

    component.openObligationsDialog();

    expect(component.updateObligations).not.toHaveBeenCalled();
  });

  it('should skip saveInitialDocumentation if user is supervisor', async () => {
    component.isSupervisor = true;

    await component.saveInitialDocumentation();

    expect(component.initialReportDocumentation).not.toBe(jasmine.anything());
  });

  it('should handle error when getting initial documentation', fakeAsync(() => {
    component.isSupervisor = false;

    component.initialDocumentationForm = {
      saveInitialDocumentation: jasmine.createSpy().and.resolveTo(true),
      bankInfoForm: {
        getBankName: () => 'Test Bank',
        getAccountTypeName: () => 'Savings',
        form: {
          get: () => ({ value: 'test value' }),
        },
      },
    } as unknown as typeof component.initialDocumentationForm;

    mockInitialReportDocumentationService.getByContractorContractId.and.returnValue(
      throwError(() => new Error('Error')),
    );

    component.saveInitialDocumentation();
    tick();

    expect(mockAlertService.error).toHaveBeenCalledWith(
      'Error al guardar la documentación inicial',
    );
    expect(component.stepper.selectedIndex).toBe(0);
  }));

  it('should show last step when report number is 2 or greater', () => {
    component.report = { ...mockReport, reportNumber: 2 };
    expect(component.showLastStep).toBeTrue();

    component.report = { ...mockReport, reportNumber: 3 };
    expect(component.showLastStep).toBeTrue();
  });

  it('should not show last step when report number is less than 2', () => {
    component.report = { ...mockReport, reportNumber: 1 };
    expect(component.showLastStep).toBeFalse();

    component.report = { ...mockReport, reportNumber: 0 };
    expect(component.showLastStep).toBeFalse();

    component.report = {
      ...mockReport,
      reportNumber: null as unknown as number,
    };
    expect(component.showLastStep).toBeFalse();
  });

  it('should check rejection status with actual rejection history', () => {

    component.isSupervisor = false;
    component.report = {
      ...mockReport,
      currentReviewStatus: { id: 3, name: 'Rechazado' },
      reviewHistory: [
        {
          id: 1,
          reviewStatus: { id: 3, name: 'Rechazado' },
          reviewDate: new Date('2024-01-02').toISOString(),
          comment: 'This is a rejection comment',
        },
        {
          id: 2,
          reviewStatus: { id: 1, name: 'Pendiente' },
          reviewDate: new Date('2024-01-01').toISOString(),
          comment: 'Initial submission',
        },
      ],
    } as MonthlyReport;

    component.checkRejectionStatus();

    expect(component.showRejectionComments).toBeTrue();
    expect(component.rejectionComments).toBe('This is a rejection comment');
  });

  it('should handle rejection status without comments', () => {

    component.isSupervisor = false;
    component.report = {
      ...mockReport,
      currentReviewStatus: { id: 3, name: 'Rechazado' },
      reviewHistory: [
        {
          id: 1,
          reviewStatus: { id: 3, name: 'Rechazado' },
          reviewDate: new Date('2024-01-02').toISOString(),
          comment: undefined,
        },
      ],
    } as MonthlyReport;

    component.checkRejectionStatus();

    expect(component.showRejectionComments).toBeFalse();
  });

  it('should not show rejection comments for non-rejected reports', () => {

    component.isSupervisor = false;
    component.report = {
      ...mockReport,
      currentReviewStatus: { id: 2, name: 'Aprobado' },
      reviewHistory: [
        {
          id: 1,
          reviewStatus: { id: 2, name: 'Aprobado' },
          reviewDate: new Date('2024-01-02').toISOString(),
          comment: 'Approved comment',
        },
      ],
    } as MonthlyReport;

    component.checkRejectionStatus();

    expect(component.showRejectionComments).toBeFalse();
  });

  it('should not show rejection comments for supervisors', () => {

    component.isSupervisor = true;
    component.report = {
      ...mockReport,
      currentReviewStatus: { id: 3, name: 'Rechazado' },
      reviewHistory: [
        {
          id: 1,
          reviewStatus: { id: 3, name: 'Rechazado' },
          reviewDate: new Date('2024-01-02').toISOString(),
          comment: 'This is a rejection comment',
        },
      ],
    } as MonthlyReport;

    component.checkRejectionStatus();

    expect(component.showRejectionComments).toBeFalse();
  });
});