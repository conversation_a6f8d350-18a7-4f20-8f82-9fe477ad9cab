{"ast": null, "code": "function cov_2lvjpt91iu() {\n  var path = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-dashboard\\\\components\\\\monthly-reports-tab\\\\monthly-report-dialog\\\\monthly-report-initial-documentation\\\\bank-info\\\\bank-info.component.ts\";\n  var hash = \"a002e040292eac04e1fbd06a33aabab5954cce23\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-dashboard\\\\components\\\\monthly-reports-tab\\\\monthly-report-dialog\\\\monthly-report-initial-documentation\\\\bank-info\\\\bank-info.component.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 18,\n          column: 24\n        },\n        end: {\n          line: 117,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 20,\n          column: 8\n        },\n        end: {\n          line: 20,\n          column: 21\n        }\n      },\n      \"2\": {\n        start: {\n          line: 21,\n          column: 8\n        },\n        end: {\n          line: 21,\n          column: 39\n        }\n      },\n      \"3\": {\n        start: {\n          line: 22,\n          column: 8\n        },\n        end: {\n          line: 22,\n          column: 61\n        }\n      },\n      \"4\": {\n        start: {\n          line: 23,\n          column: 8\n        },\n        end: {\n          line: 23,\n          column: 27\n        }\n      },\n      \"5\": {\n        start: {\n          line: 24,\n          column: 8\n        },\n        end: {\n          line: 24,\n          column: 34\n        }\n      },\n      \"6\": {\n        start: {\n          line: 25,\n          column: 8\n        },\n        end: {\n          line: 25,\n          column: 45\n        }\n      },\n      \"7\": {\n        start: {\n          line: 26,\n          column: 8\n        },\n        end: {\n          line: 31,\n          column: 11\n        }\n      },\n      \"8\": {\n        start: {\n          line: 32,\n          column: 8\n        },\n        end: {\n          line: 32,\n          column: 27\n        }\n      },\n      \"9\": {\n        start: {\n          line: 33,\n          column: 8\n        },\n        end: {\n          line: 33,\n          column: 31\n        }\n      },\n      \"10\": {\n        start: {\n          line: 34,\n          column: 8\n        },\n        end: {\n          line: 34,\n          column: 40\n        }\n      },\n      \"11\": {\n        start: {\n          line: 35,\n          column: 8\n        },\n        end: {\n          line: 35,\n          column: 44\n        }\n      },\n      \"12\": {\n        start: {\n          line: 38,\n          column: 8\n        },\n        end: {\n          line: 49,\n          column: 11\n        }\n      },\n      \"13\": {\n        start: {\n          line: 43,\n          column: 16\n        },\n        end: {\n          line: 43,\n          column: 38\n        }\n      },\n      \"14\": {\n        start: {\n          line: 44,\n          column: 16\n        },\n        end: {\n          line: 44,\n          column: 49\n        }\n      },\n      \"15\": {\n        start: {\n          line: 47,\n          column: 16\n        },\n        end: {\n          line: 47,\n          column: 100\n        }\n      },\n      \"16\": {\n        start: {\n          line: 50,\n          column: 8\n        },\n        end: {\n          line: 52,\n          column: 11\n        }\n      },\n      \"17\": {\n        start: {\n          line: 51,\n          column: 12\n        },\n        end: {\n          line: 51,\n          column: 35\n        }\n      },\n      \"18\": {\n        start: {\n          line: 55,\n          column: 8\n        },\n        end: {\n          line: 62,\n          column: 9\n        }\n      },\n      \"19\": {\n        start: {\n          line: 56,\n          column: 25\n        },\n        end: {\n          line: 56,\n          column: 60\n        }\n      },\n      \"20\": {\n        start: {\n          line: 57,\n          column: 12\n        },\n        end: {\n          line: 61,\n          column: 15\n        }\n      },\n      \"21\": {\n        start: {\n          line: 65,\n          column: 8\n        },\n        end: {\n          line: 65,\n          column: 47\n        }\n      },\n      \"22\": {\n        start: {\n          line: 68,\n          column: 21\n        },\n        end: {\n          line: 68,\n          column: 44\n        }\n      },\n      \"23\": {\n        start: {\n          line: 69,\n          column: 8\n        },\n        end: {\n          line: 85,\n          column: 9\n        }\n      },\n      \"24\": {\n        start: {\n          line: 70,\n          column: 12\n        },\n        end: {\n          line: 83,\n          column: 13\n        }\n      },\n      \"25\": {\n        start: {\n          line: 71,\n          column: 16\n        },\n        end: {\n          line: 79,\n          column: 17\n        }\n      },\n      \"26\": {\n        start: {\n          line: 73,\n          column: 20\n        },\n        end: {\n          line: 73,\n          column: 72\n        }\n      },\n      \"27\": {\n        start: {\n          line: 74,\n          column: 20\n        },\n        end: {\n          line: 74,\n          column: 52\n        }\n      },\n      \"28\": {\n        start: {\n          line: 75,\n          column: 20\n        },\n        end: {\n          line: 75,\n          column: 61\n        }\n      },\n      \"29\": {\n        start: {\n          line: 78,\n          column: 20\n        },\n        end: {\n          line: 78,\n          column: 71\n        }\n      },\n      \"30\": {\n        start: {\n          line: 82,\n          column: 16\n        },\n        end: {\n          line: 82,\n          column: 66\n        }\n      },\n      \"31\": {\n        start: {\n          line: 84,\n          column: 12\n        },\n        end: {\n          line: 84,\n          column: 75\n        }\n      },\n      \"32\": {\n        start: {\n          line: 88,\n          column: 8\n        },\n        end: {\n          line: 90,\n          column: 9\n        }\n      },\n      \"33\": {\n        start: {\n          line: 89,\n          column: 12\n        },\n        end: {\n          line: 89,\n          column: 75\n        }\n      },\n      \"34\": {\n        start: {\n          line: 93,\n          column: 33\n        },\n        end: {\n          line: 95,\n          column: 50\n        }\n      },\n      \"35\": {\n        start: {\n          line: 96,\n          column: 27\n        },\n        end: {\n          line: 96,\n          column: 79\n        }\n      },\n      \"36\": {\n        start: {\n          line: 97,\n          column: 32\n        },\n        end: {\n          line: 97,\n          column: 81\n        }\n      },\n      \"37\": {\n        start: {\n          line: 98,\n          column: 8\n        },\n        end: {\n          line: 98,\n          column: 67\n        }\n      },\n      \"38\": {\n        start: {\n          line: 101,\n          column: 8\n        },\n        end: {\n          line: 101,\n          column: 72\n        }\n      },\n      \"39\": {\n        start: {\n          line: 101,\n          column: 44\n        },\n        end: {\n          line: 101,\n          column: 58\n        }\n      },\n      \"40\": {\n        start: {\n          line: 104,\n          column: 8\n        },\n        end: {\n          line: 104,\n          column: 76\n        }\n      },\n      \"41\": {\n        start: {\n          line: 104,\n          column: 48\n        },\n        end: {\n          line: 104,\n          column: 62\n        }\n      },\n      \"42\": {\n        start: {\n          line: 106,\n          column: 13\n        },\n        end: {\n          line: 111,\n          column: 6\n        }\n      },\n      \"43\": {\n        start: {\n          line: 106,\n          column: 41\n        },\n        end: {\n          line: 111,\n          column: 5\n        }\n      },\n      \"44\": {\n        start: {\n          line: 112,\n          column: 13\n        },\n        end: {\n          line: 116,\n          column: 6\n        }\n      },\n      \"45\": {\n        start: {\n          line: 118,\n          column: 0\n        },\n        end: {\n          line: 138,\n          column: 22\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 19,\n            column: 4\n          },\n          end: {\n            line: 19,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 19,\n            column: 64\n          },\n          end: {\n            line: 36,\n            column: 5\n          }\n        },\n        line: 19\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 37,\n            column: 4\n          },\n          end: {\n            line: 37,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 37,\n            column: 15\n          },\n          end: {\n            line: 53,\n            column: 5\n          }\n        },\n        line: 37\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 42,\n            column: 18\n          },\n          end: {\n            line: 42,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 42,\n            column: 47\n          },\n          end: {\n            line: 45,\n            column: 13\n          }\n        },\n        line: 42\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 46,\n            column: 19\n          },\n          end: {\n            line: 46,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 46,\n            column: 30\n          },\n          end: {\n            line: 48,\n            column: 13\n          }\n        },\n        line: 46\n      },\n      \"4\": {\n        name: \"(anonymous_4)\",\n        decl: {\n          start: {\n            line: 50,\n            column: 41\n          },\n          end: {\n            line: 50,\n            column: 42\n          }\n        },\n        loc: {\n          start: {\n            line: 50,\n            column: 47\n          },\n          end: {\n            line: 52,\n            column: 9\n          }\n        },\n        line: 50\n      },\n      \"5\": {\n        name: \"(anonymous_5)\",\n        decl: {\n          start: {\n            line: 54,\n            column: 4\n          },\n          end: {\n            line: 54,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 54,\n            column: 25\n          },\n          end: {\n            line: 63,\n            column: 5\n          }\n        },\n        line: 54\n      },\n      \"6\": {\n        name: \"(anonymous_6)\",\n        decl: {\n          start: {\n            line: 64,\n            column: 4\n          },\n          end: {\n            line: 64,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 64,\n            column: 20\n          },\n          end: {\n            line: 66,\n            column: 5\n          }\n        },\n        line: 64\n      },\n      \"7\": {\n        name: \"(anonymous_7)\",\n        decl: {\n          start: {\n            line: 67,\n            column: 4\n          },\n          end: {\n            line: 67,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 67,\n            column: 26\n          },\n          end: {\n            line: 86,\n            column: 5\n          }\n        },\n        line: 67\n      },\n      \"8\": {\n        name: \"(anonymous_8)\",\n        decl: {\n          start: {\n            line: 87,\n            column: 4\n          },\n          end: {\n            line: 87,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 87,\n            column: 19\n          },\n          end: {\n            line: 91,\n            column: 5\n          }\n        },\n        line: 87\n      },\n      \"9\": {\n        name: \"(anonymous_9)\",\n        decl: {\n          start: {\n            line: 92,\n            column: 4\n          },\n          end: {\n            line: 92,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 92,\n            column: 18\n          },\n          end: {\n            line: 99,\n            column: 5\n          }\n        },\n        line: 92\n      },\n      \"10\": {\n        name: \"(anonymous_10)\",\n        decl: {\n          start: {\n            line: 100,\n            column: 4\n          },\n          end: {\n            line: 100,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 100,\n            column: 20\n          },\n          end: {\n            line: 102,\n            column: 5\n          }\n        },\n        line: 100\n      },\n      \"11\": {\n        name: \"(anonymous_11)\",\n        decl: {\n          start: {\n            line: 101,\n            column: 34\n          },\n          end: {\n            line: 101,\n            column: 35\n          }\n        },\n        loc: {\n          start: {\n            line: 101,\n            column: 44\n          },\n          end: {\n            line: 101,\n            column: 58\n          }\n        },\n        line: 101\n      },\n      \"12\": {\n        name: \"(anonymous_12)\",\n        decl: {\n          start: {\n            line: 103,\n            column: 4\n          },\n          end: {\n            line: 103,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 103,\n            column: 27\n          },\n          end: {\n            line: 105,\n            column: 5\n          }\n        },\n        line: 103\n      },\n      \"13\": {\n        name: \"(anonymous_13)\",\n        decl: {\n          start: {\n            line: 104,\n            column: 38\n          },\n          end: {\n            line: 104,\n            column: 39\n          }\n        },\n        loc: {\n          start: {\n            line: 104,\n            column: 48\n          },\n          end: {\n            line: 104,\n            column: 62\n          }\n        },\n        line: 104\n      },\n      \"14\": {\n        name: \"(anonymous_14)\",\n        decl: {\n          start: {\n            line: 106,\n            column: 35\n          },\n          end: {\n            line: 106,\n            column: 36\n          }\n        },\n        loc: {\n          start: {\n            line: 106,\n            column: 41\n          },\n          end: {\n            line: 111,\n            column: 5\n          }\n        },\n        line: 106\n      }\n    },\n    branchMap: {\n      \"0\": {\n        loc: {\n          start: {\n            line: 47,\n            column: 33\n          },\n          end: {\n            line: 47,\n            column: 98\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 47,\n            column: 33\n          },\n          end: {\n            line: 47,\n            column: 52\n          }\n        }, {\n          start: {\n            line: 47,\n            column: 56\n          },\n          end: {\n            line: 47,\n            column: 98\n          }\n        }],\n        line: 47\n      },\n      \"1\": {\n        loc: {\n          start: {\n            line: 55,\n            column: 8\n          },\n          end: {\n            line: 62,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 55,\n            column: 8\n          },\n          end: {\n            line: 62,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 55\n      },\n      \"2\": {\n        loc: {\n          start: {\n            line: 69,\n            column: 8\n          },\n          end: {\n            line: 85,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 69,\n            column: 8\n          },\n          end: {\n            line: 85,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 69\n      },\n      \"3\": {\n        loc: {\n          start: {\n            line: 70,\n            column: 12\n          },\n          end: {\n            line: 83,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 70,\n            column: 12\n          },\n          end: {\n            line: 83,\n            column: 13\n          }\n        }, {\n          start: {\n            line: 81,\n            column: 17\n          },\n          end: {\n            line: 83,\n            column: 13\n          }\n        }],\n        line: 70\n      },\n      \"4\": {\n        loc: {\n          start: {\n            line: 71,\n            column: 16\n          },\n          end: {\n            line: 79,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 71,\n            column: 16\n          },\n          end: {\n            line: 79,\n            column: 17\n          }\n        }, {\n          start: {\n            line: 77,\n            column: 21\n          },\n          end: {\n            line: 79,\n            column: 17\n          }\n        }],\n        line: 71\n      },\n      \"5\": {\n        loc: {\n          start: {\n            line: 88,\n            column: 8\n          },\n          end: {\n            line: 90,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 88,\n            column: 8\n          },\n          end: {\n            line: 90,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 88\n      },\n      \"6\": {\n        loc: {\n          start: {\n            line: 93,\n            column: 41\n          },\n          end: {\n            line: 95,\n            column: 49\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 93,\n            column: 41\n          },\n          end: {\n            line: 93,\n            column: 69\n          }\n        }, {\n          start: {\n            line: 94,\n            column: 12\n          },\n          end: {\n            line: 94,\n            column: 47\n          }\n        }, {\n          start: {\n            line: 95,\n            column: 12\n          },\n          end: {\n            line: 95,\n            column: 49\n          }\n        }],\n        line: 93\n      },\n      \"7\": {\n        loc: {\n          start: {\n            line: 98,\n            column: 15\n          },\n          end: {\n            line: 98,\n            column: 66\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 98,\n            column: 15\n          },\n          end: {\n            line: 98,\n            column: 31\n          }\n        }, {\n          start: {\n            line: 98,\n            column: 36\n          },\n          end: {\n            line: 98,\n            column: 46\n          }\n        }, {\n          start: {\n            line: 98,\n            column: 50\n          },\n          end: {\n            line: 98,\n            column: 65\n          }\n        }],\n        line: 98\n      },\n      \"8\": {\n        loc: {\n          start: {\n            line: 101,\n            column: 15\n          },\n          end: {\n            line: 101,\n            column: 71\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 101,\n            column: 15\n          },\n          end: {\n            line: 101,\n            column: 65\n          }\n        }, {\n          start: {\n            line: 101,\n            column: 69\n          },\n          end: {\n            line: 101,\n            column: 71\n          }\n        }],\n        line: 101\n      },\n      \"9\": {\n        loc: {\n          start: {\n            line: 104,\n            column: 15\n          },\n          end: {\n            line: 104,\n            column: 75\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 104,\n            column: 15\n          },\n          end: {\n            line: 104,\n            column: 69\n          }\n        }, {\n          start: {\n            line: 104,\n            column: 73\n          },\n          end: {\n            line: 104,\n            column: 75\n          }\n        }],\n        line: 104\n      }\n    },\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0,\n      \"36\": 0,\n      \"37\": 0,\n      \"38\": 0,\n      \"39\": 0,\n      \"40\": 0,\n      \"41\": 0,\n      \"42\": 0,\n      \"43\": 0,\n      \"44\": 0,\n      \"45\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0\n    },\n    b: {\n      \"0\": [0, 0],\n      \"1\": [0, 0],\n      \"2\": [0, 0],\n      \"3\": [0, 0],\n      \"4\": [0, 0],\n      \"5\": [0, 0],\n      \"6\": [0, 0, 0],\n      \"7\": [0, 0, 0],\n      \"8\": [0, 0],\n      \"9\": [0, 0]\n    },\n    inputSourceMap: {\n      version: 3,\n      file: \"bank-info.component.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-dashboard\\\\components\\\\monthly-reports-tab\\\\monthly-report-dialog\\\\monthly-report-initial-documentation\\\\bank-info\\\\bank-info.component.ts\"],\n      names: [],\n      mappings: \";;;AAAA,OAAO,EACL,SAAS,EACT,YAAY,EACZ,KAAK,EAGL,MAAM,GAEP,MAAM,eAAe,CAAC;AACvB,OAAO,EACL,WAAW,EAEX,mBAAmB,EACnB,UAAU,GACX,MAAM,gBAAgB,CAAC;AACxB,OAAO,EAAE,QAAQ,EAAE,MAAM,MAAM,CAAC;AAEhC,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AACpE,OAAO,EAAE,SAAS,EAAE,MAAM,wBAAwB,CAAC;AACnD,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,8BAA8B,CAAC;AAChF,OAAO,EAAE,OAAO,EAAE,MAAM,wBAAwB,CAAC;AACjD,OAAO,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AACnD,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AACrD,OAAO,EAAE,UAAU,EAAE,MAAM,2BAA2B,CAAC;AAIvD,OAAO,EAAE,sBAAsB,EAAE,MAAM,0DAA0D,CAAC;AAClG,OAAO,EAAE,WAAW,EAAE,MAAM,6CAA6C,CAAC;AAC1E,OAAO,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAC9D,OAAO,EACL,iBAAiB,EACjB,gBAAgB,GACjB,MAAM,oCAAoC,CAAC;AAqBrC,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IAiB5B,YACU,EAAe,EACf,WAAwB,EACxB,sBAA8C,EAC9C,KAAmB;QAHnB,OAAE,GAAF,EAAE,CAAa;QACf,gBAAW,GAAX,WAAW,CAAa;QACxB,2BAAsB,GAAtB,sBAAsB,CAAwB;QAC9C,UAAK,GAAL,KAAK,CAAc;QAnBpB,iBAAY,GAAG,KAAK;QACnB,eAAU,GAAG,IAAI,YAAY,EAAQ;QAE/C,SAAI,GAAc,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;YAC9B,IAAI,EAAE,CAAC,EAAE,EAAE,UAAU,CAAC,QAAQ,CAAC;YAC/B,WAAW,EAAE,CAAC,EAAE,EAAE,UAAU,CAAC,QAAQ,CAAC;YACtC,aAAa,EAAE,CAAC,EAAE,EAAE,UAAU,CAAC,QAAQ,CAAC;YACxC,mBAAmB,EAAE,CAAC,IAAI,EAAE,CAAC,iBAAiB,EAAE,EAAE,gBAAgB,EAAE,CAAC,CAAC;SACvE,CAAC,CAAC;QAEH,aAAQ,GAAW,EAAE,CAAC;QACtB,iBAAY,GAAsB,EAAE,CAAC;QACrC,wBAAmB,GAAgB,IAAI,CAAC;QACxC,4BAAuB,GAAkB,IAAI,CAAC;IAO3C,CAAC;IAEJ,QAAQ;QACN,QAAQ,CAAC;YACP,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;YAChC,YAAY,EAAE,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE;SACnD,CAAC,CAAC,SAAS,CAAC;YACX,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE,EAAE;gBAChC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;gBACtB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;YACnC,CAAC;YACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,0CAA0C,CAAC,CAAC;YACtF,CAAC;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,EAAE;YACpC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,WAAW,CAAC,OAAsB;QAChC,IAAI,OAAO,CAAC,aAAa,CAAC,EAAE,YAAY,EAAE,CAAC;YACzC,MAAM,IAAI,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC;YACjD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;gBACnB,IAAI,EAAE,IAAI,CAAC,MAAM;gBACjB,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,IAAI,CAAC,iBAAiB;aACpC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,SAAS,CAAC,IAAU;QAC1B,OAAO,IAAI,CAAC,IAAI,KAAK,iBAAiB,CAAC;IACzC,CAAC;IAED,cAAc,CAAC,KAAY;QACzB,MAAM,IAAI,GAAI,KAAK,CAAC,MAA2B,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;QAC3D,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;gBACzB,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC;oBAC7B,MAAM;oBACN,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,mBAAmB,EAAE,IAAI,EAAE,CAAC,CAAC;oBACpD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;oBAChC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,IAAI,CAAC;gBAC3C,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;gBACrD,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;YACpD,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,qBAAqB,CAAC,EAAE,sBAAsB,EAAE,CAAC;QACjE,CAAC;IACH,CAAC;IAED,YAAY;QACV,IAAI,IAAI,CAAC,WAAW,EAAE,sBAAsB,EAAE,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,sBAAsB,EAAE,QAAQ,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAED,IAAI,OAAO;QACT,MAAM,gBAAgB,GAAG,OAAO,CAC9B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,KAAK;YAC1B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,KAAK;YACnC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,KAAK,CACxC,CAAC;QAEF,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,qBAAqB,CAAC,EAAE,KAAK,CAAC,CAAC;QACxE,MAAM,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,sBAAsB,CAAC,CAAC;QAE1E,OAAO,gBAAgB,IAAI,CAAC,UAAU,IAAI,eAAe,CAAC,CAAC;IAC7D,CAAC;IAED,WAAW,CAAC,EAAU;QACpB,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC;IAClE,CAAC;IAED,kBAAkB,CAAC,EAAU;QAC3B,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC;IACtE,CAAC;;;;;;;;8BArGA,KAAK;+BACL,KAAK;6BACL,MAAM;;;AAHI,iBAAiB;IAnB7B,SAAS,CAAC;QACT,QAAQ,EAAE,eAAe;QACzB,8BAAyC;QAEzC,UAAU,EAAE,IAAI;QAChB,OAAO,EAAE;YACP,mBAAmB;YACnB,OAAO;YACP,YAAY;YACZ,QAAQ;YACR,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,SAAS;YACT,aAAa;YACb,UAAU;SACX;;KACF,CAAC;GACW,iBAAiB,CAuG7B\",\n      sourcesContent: [\"import {\\n  Component,\\n  EventEmitter,\\n  Input,\\n  OnChanges,\\n  OnInit,\\n  Output,\\n  SimpleChanges,\\n} from '@angular/core';\\nimport {\\n  FormBuilder,\\n  FormGroup,\\n  ReactiveFormsModule,\\n  Validators,\\n} from '@angular/forms';\\nimport { forkJoin } from 'rxjs';\\n\\nimport { MatButton, MatIconButton } from '@angular/material/button';\\nimport { MatOption } from '@angular/material/core';\\nimport { MatError, MatFormField, MatLabel } from '@angular/material/form-field';\\nimport { MatIcon } from '@angular/material/icon';\\nimport { MatInput } from '@angular/material/input';\\nimport { MatSelect } from '@angular/material/select';\\nimport { MatTooltip } from '@angular/material/tooltip';\\nimport { BankAccountType } from '@contractor-dashboard/models/bank-account-type.model';\\nimport { Bank } from '@contractor-dashboard/models/bank.model';\\nimport { InitialReportDocumentation } from '@contractor-dashboard/models/initial-report-documentation.model';\\nimport { BankAccountTypeService } from '@contractor-dashboard/services/bank-account-type.service';\\nimport { BankService } from '@contractor-dashboard/services/bank.service';\\nimport { AlertService } from '@shared/services/alert.service';\\nimport {\\n  fileSizeValidator,\\n  pdfFileValidator,\\n} from '@shared/validators/file.validators';\\n\\n@Component({\\n  selector: 'app-bank-info',\\n  templateUrl: './bank-info.component.html',\\n  styleUrl: './bank-info.component.scss',\\n  standalone: true,\\n  imports: [\\n    ReactiveFormsModule,\\n    MatIcon,\\n    MatFormField,\\n    MatLabel,\\n    MatSelect,\\n    MatOption,\\n    MatInput,\\n    MatError,\\n    MatButton,\\n    MatIconButton,\\n    MatTooltip,\\n  ],\\n})\\nexport class BankInfoComponent implements OnInit, OnChanges {\\n  @Input() initialData?: InitialReportDocumentation;\\n  @Input() isSupervisor = false;\\n  @Output() formChange = new EventEmitter<void>();\\n\\n  form: FormGroup = this.fb.group({\\n    bank: ['', Validators.required],\\n    accountType: ['', Validators.required],\\n    accountNumber: ['', Validators.required],\\n    bankCertificateFile: [null, [fileSizeValidator(), pdfFileValidator()]],\\n  });\\n\\n  bankList: Bank[] = [];\\n  accountTypes: BankAccountType[] = [];\\n  bankCertificateFile: File | null = null;\\n  bankCertificateFileName: string | null = null;\\n\\n  constructor(\\n    private fb: FormBuilder,\\n    private bankService: BankService,\\n    private bankAccountTypeService: BankAccountTypeService,\\n    private alert: AlertService,\\n  ) {}\\n\\n  ngOnInit(): void {\\n    forkJoin({\\n      banks: this.bankService.getAll(),\\n      accountTypes: this.bankAccountTypeService.getAll(),\\n    }).subscribe({\\n      next: ({ banks, accountTypes }) => {\\n        this.bankList = banks;\\n        this.accountTypes = accountTypes;\\n      },\\n      error: (error) => {\\n        this.alert.error(error.error?.detail ?? 'Error al cargar los datos del formulario');\\n      },\\n    });\\n\\n    this.form.valueChanges.subscribe(() => {\\n      this.formChange.emit();\\n    });\\n  }\\n\\n  ngOnChanges(changes: SimpleChanges): void {\\n    if (changes['initialData']?.currentValue) {\\n      const data = changes['initialData'].currentValue;\\n      this.form.patchValue({\\n        bank: data.bankId,\\n        accountNumber: data.accountNumber,\\n        accountType: data.bankAccountTypeId,\\n      });\\n    }\\n  }\\n\\n  private isPdfFile(file: File): boolean {\\n    return file.type === 'application/pdf';\\n  }\\n\\n  onFileSelected(event: Event): void {\\n    const file = (event.target as HTMLInputElement).files?.[0];\\n    if (file) {\\n      if (this.isPdfFile(file)) {\\n        if (file.size <= 1024 * 1024) {\\n          // 1MB\\n          this.form.patchValue({ bankCertificateFile: file });\\n          this.bankCertificateFile = file;\\n          this.bankCertificateFileName = file.name;\\n        } else {\\n          this.alert.error('El archivo no debe superar 1MB');\\n        }\\n      } else {\\n        this.alert.error('Solo se permiten archivos PDF');\\n      }\\n      this.form.get('bankCertificateFile')?.updateValueAndValidity();\\n    }\\n  }\\n\\n  downloadFile(): void {\\n    if (this.initialData?.bankCertificateFileUrl) {\\n      window.open(this.initialData.bankCertificateFileUrl, '_blank');\\n    }\\n  }\\n\\n  get isValid(): boolean {\\n    const basicFieldsValid = Boolean(\\n      this.form.get('bank')?.valid &&\\n        this.form.get('accountType')?.valid &&\\n        this.form.get('accountNumber')?.valid,\\n    );\\n\\n    const hasNewFile = Boolean(this.form.get('bankCertificateFile')?.value);\\n    const hasExistingFile = Boolean(this.initialData?.bankCertificateFileUrl);\\n\\n    return basicFieldsValid && (hasNewFile || hasExistingFile);\\n  }\\n\\n  getBankName(id: number): string {\\n    return this.bankList.find((bank) => bank.id === id)?.name || '';\\n  }\\n\\n  getAccountTypeName(id: number): string {\\n    return this.accountTypes.find((type) => type.id === id)?.name || '';\\n  }\\n}\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"a002e040292eac04e1fbd06a33aabab5954cce23\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_2lvjpt91iu = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_2lvjpt91iu();\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./bank-info.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./bank-info.component.scss?ngResource\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { forkJoin } from 'rxjs';\nimport { MatButton, MatIconButton } from '@angular/material/button';\nimport { MatOption } from '@angular/material/core';\nimport { MatError, MatFormField, MatLabel } from '@angular/material/form-field';\nimport { MatIcon } from '@angular/material/icon';\nimport { MatInput } from '@angular/material/input';\nimport { MatSelect } from '@angular/material/select';\nimport { MatTooltip } from '@angular/material/tooltip';\nimport { BankAccountTypeService } from '@contractor-dashboard/services/bank-account-type.service';\nimport { BankService } from '@contractor-dashboard/services/bank.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { fileSizeValidator, pdfFileValidator } from '@shared/validators/file.validators';\ncov_2lvjpt91iu().s[0]++;\nlet BankInfoComponent = class BankInfoComponent {\n  constructor(fb, bankService, bankAccountTypeService, alert) {\n    cov_2lvjpt91iu().f[0]++;\n    cov_2lvjpt91iu().s[1]++;\n    this.fb = fb;\n    cov_2lvjpt91iu().s[2]++;\n    this.bankService = bankService;\n    cov_2lvjpt91iu().s[3]++;\n    this.bankAccountTypeService = bankAccountTypeService;\n    cov_2lvjpt91iu().s[4]++;\n    this.alert = alert;\n    cov_2lvjpt91iu().s[5]++;\n    this.isSupervisor = false;\n    cov_2lvjpt91iu().s[6]++;\n    this.formChange = new EventEmitter();\n    cov_2lvjpt91iu().s[7]++;\n    this.form = this.fb.group({\n      bank: ['', Validators.required],\n      accountType: ['', Validators.required],\n      accountNumber: ['', Validators.required],\n      bankCertificateFile: [null, [fileSizeValidator(), pdfFileValidator()]]\n    });\n    cov_2lvjpt91iu().s[8]++;\n    this.bankList = [];\n    cov_2lvjpt91iu().s[9]++;\n    this.accountTypes = [];\n    cov_2lvjpt91iu().s[10]++;\n    this.bankCertificateFile = null;\n    cov_2lvjpt91iu().s[11]++;\n    this.bankCertificateFileName = null;\n  }\n  ngOnInit() {\n    cov_2lvjpt91iu().f[1]++;\n    cov_2lvjpt91iu().s[12]++;\n    forkJoin({\n      banks: this.bankService.getAll(),\n      accountTypes: this.bankAccountTypeService.getAll()\n    }).subscribe({\n      next: ({\n        banks,\n        accountTypes\n      }) => {\n        cov_2lvjpt91iu().f[2]++;\n        cov_2lvjpt91iu().s[13]++;\n        this.bankList = banks;\n        cov_2lvjpt91iu().s[14]++;\n        this.accountTypes = accountTypes;\n      },\n      error: error => {\n        cov_2lvjpt91iu().f[3]++;\n        cov_2lvjpt91iu().s[15]++;\n        this.alert.error((cov_2lvjpt91iu().b[0][0]++, error.error?.detail) ?? (cov_2lvjpt91iu().b[0][1]++, 'Error al cargar los datos del formulario'));\n      }\n    });\n    cov_2lvjpt91iu().s[16]++;\n    this.form.valueChanges.subscribe(() => {\n      cov_2lvjpt91iu().f[4]++;\n      cov_2lvjpt91iu().s[17]++;\n      this.formChange.emit();\n    });\n  }\n  ngOnChanges(changes) {\n    cov_2lvjpt91iu().f[5]++;\n    cov_2lvjpt91iu().s[18]++;\n    if (changes['initialData']?.currentValue) {\n      cov_2lvjpt91iu().b[1][0]++;\n      const data = (cov_2lvjpt91iu().s[19]++, changes['initialData'].currentValue);\n      cov_2lvjpt91iu().s[20]++;\n      this.form.patchValue({\n        bank: data.bankId,\n        accountNumber: data.accountNumber,\n        accountType: data.bankAccountTypeId\n      });\n    } else {\n      cov_2lvjpt91iu().b[1][1]++;\n    }\n  }\n  isPdfFile(file) {\n    cov_2lvjpt91iu().f[6]++;\n    cov_2lvjpt91iu().s[21]++;\n    return file.type === 'application/pdf';\n  }\n  onFileSelected(event) {\n    cov_2lvjpt91iu().f[7]++;\n    const file = (cov_2lvjpt91iu().s[22]++, event.target.files?.[0]);\n    cov_2lvjpt91iu().s[23]++;\n    if (file) {\n      cov_2lvjpt91iu().b[2][0]++;\n      cov_2lvjpt91iu().s[24]++;\n      if (this.isPdfFile(file)) {\n        cov_2lvjpt91iu().b[3][0]++;\n        cov_2lvjpt91iu().s[25]++;\n        if (file.size <= 1024 * 1024) {\n          cov_2lvjpt91iu().b[4][0]++;\n          cov_2lvjpt91iu().s[26]++;\n          // 1MB\n          this.form.patchValue({\n            bankCertificateFile: file\n          });\n          cov_2lvjpt91iu().s[27]++;\n          this.bankCertificateFile = file;\n          cov_2lvjpt91iu().s[28]++;\n          this.bankCertificateFileName = file.name;\n        } else {\n          cov_2lvjpt91iu().b[4][1]++;\n          cov_2lvjpt91iu().s[29]++;\n          this.alert.error('El archivo no debe superar 1MB');\n        }\n      } else {\n        cov_2lvjpt91iu().b[3][1]++;\n        cov_2lvjpt91iu().s[30]++;\n        this.alert.error('Solo se permiten archivos PDF');\n      }\n      cov_2lvjpt91iu().s[31]++;\n      this.form.get('bankCertificateFile')?.updateValueAndValidity();\n    } else {\n      cov_2lvjpt91iu().b[2][1]++;\n    }\n  }\n  downloadFile() {\n    cov_2lvjpt91iu().f[8]++;\n    cov_2lvjpt91iu().s[32]++;\n    if (this.initialData?.bankCertificateFileUrl) {\n      cov_2lvjpt91iu().b[5][0]++;\n      cov_2lvjpt91iu().s[33]++;\n      window.open(this.initialData.bankCertificateFileUrl, '_blank');\n    } else {\n      cov_2lvjpt91iu().b[5][1]++;\n    }\n  }\n  get isValid() {\n    cov_2lvjpt91iu().f[9]++;\n    const basicFieldsValid = (cov_2lvjpt91iu().s[34]++, Boolean((cov_2lvjpt91iu().b[6][0]++, this.form.get('bank')?.valid) && (cov_2lvjpt91iu().b[6][1]++, this.form.get('accountType')?.valid) && (cov_2lvjpt91iu().b[6][2]++, this.form.get('accountNumber')?.valid)));\n    const hasNewFile = (cov_2lvjpt91iu().s[35]++, Boolean(this.form.get('bankCertificateFile')?.value));\n    const hasExistingFile = (cov_2lvjpt91iu().s[36]++, Boolean(this.initialData?.bankCertificateFileUrl));\n    cov_2lvjpt91iu().s[37]++;\n    return (cov_2lvjpt91iu().b[7][0]++, basicFieldsValid) && ((cov_2lvjpt91iu().b[7][1]++, hasNewFile) || (cov_2lvjpt91iu().b[7][2]++, hasExistingFile));\n  }\n  getBankName(id) {\n    cov_2lvjpt91iu().f[10]++;\n    cov_2lvjpt91iu().s[38]++;\n    return (cov_2lvjpt91iu().b[8][0]++, this.bankList.find(bank => {\n      cov_2lvjpt91iu().f[11]++;\n      cov_2lvjpt91iu().s[39]++;\n      return bank.id === id;\n    })?.name) || (cov_2lvjpt91iu().b[8][1]++, '');\n  }\n  getAccountTypeName(id) {\n    cov_2lvjpt91iu().f[12]++;\n    cov_2lvjpt91iu().s[40]++;\n    return (cov_2lvjpt91iu().b[9][0]++, this.accountTypes.find(type => {\n      cov_2lvjpt91iu().f[13]++;\n      cov_2lvjpt91iu().s[41]++;\n      return type.id === id;\n    })?.name) || (cov_2lvjpt91iu().b[9][1]++, '');\n  }\n  static {\n    cov_2lvjpt91iu().s[42]++;\n    this.ctorParameters = () => {\n      cov_2lvjpt91iu().f[14]++;\n      cov_2lvjpt91iu().s[43]++;\n      return [{\n        type: FormBuilder\n      }, {\n        type: BankService\n      }, {\n        type: BankAccountTypeService\n      }, {\n        type: AlertService\n      }];\n    };\n  }\n  static {\n    cov_2lvjpt91iu().s[44]++;\n    this.propDecorators = {\n      initialData: [{\n        type: Input\n      }],\n      isSupervisor: [{\n        type: Input\n      }],\n      formChange: [{\n        type: Output\n      }]\n    };\n  }\n};\ncov_2lvjpt91iu().s[45]++;\nBankInfoComponent = __decorate([Component({\n  selector: 'app-bank-info',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: true,\n  imports: [ReactiveFormsModule, MatIcon, MatFormField, MatLabel, MatSelect, MatOption, MatInput, MatError, MatButton, MatIconButton, MatTooltip],\n  styles: [__NG_CLI_RESOURCE__1]\n})], BankInfoComponent);\nexport { BankInfoComponent };", "map": {"version": 3, "names": ["cov_2lvjpt91iu", "actualCoverage", "Component", "EventEmitter", "Input", "Output", "FormBuilder", "ReactiveFormsModule", "Validators", "fork<PERSON><PERSON>n", "MatButton", "MatIconButton", "MatOption", "<PERSON><PERSON><PERSON><PERSON>", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatIcon", "MatInput", "MatSelect", "MatTooltip", "BankAccountTypeService", "BankService", "AlertService", "fileSizeValidator", "pdfFileValidator", "s", "BankInfoComponent", "constructor", "fb", "bankService", "bankAccountTypeService", "alert", "f", "isSupervisor", "formChange", "form", "group", "bank", "required", "accountType", "accountNumber", "bankCertificateFile", "bankList", "accountTypes", "bankCertificateFileName", "ngOnInit", "banks", "getAll", "subscribe", "next", "error", "b", "detail", "valueChanges", "emit", "ngOnChanges", "changes", "currentValue", "data", "patchValue", "bankId", "bankAccountTypeId", "isPdfFile", "file", "type", "onFileSelected", "event", "target", "files", "size", "name", "get", "updateValueAndValidity", "downloadFile", "initialData", "bankCertificateFileUrl", "window", "open", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Boolean", "valid", "hasNewFile", "value", "hasExistingFile", "getBankName", "id", "find", "getAccountTypeName", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\components\\monthly-reports-tab\\monthly-report-dialog\\monthly-report-initial-documentation\\bank-info\\bank-info.component.ts"], "sourcesContent": ["import {\n  Component,\n  EventEmitter,\n  Input,\n  OnChanges,\n  OnInit,\n  Output,\n  SimpleChanges,\n} from '@angular/core';\nimport {\n  FormBuilder,\n  FormGroup,\n  ReactiveFormsModule,\n  Validators,\n} from '@angular/forms';\nimport { forkJoin } from 'rxjs';\n\nimport { <PERSON><PERSON><PERSON>on, MatIconButton } from '@angular/material/button';\nimport { MatOption } from '@angular/material/core';\nimport { Mat<PERSON><PERSON>r, MatFormField, MatLabel } from '@angular/material/form-field';\nimport { MatIcon } from '@angular/material/icon';\nimport { MatInput } from '@angular/material/input';\nimport { MatSelect } from '@angular/material/select';\nimport { MatTooltip } from '@angular/material/tooltip';\nimport { BankAccountType } from '@contractor-dashboard/models/bank-account-type.model';\nimport { Bank } from '@contractor-dashboard/models/bank.model';\nimport { InitialReportDocumentation } from '@contractor-dashboard/models/initial-report-documentation.model';\nimport { BankAccountTypeService } from '@contractor-dashboard/services/bank-account-type.service';\nimport { BankService } from '@contractor-dashboard/services/bank.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport {\n  fileSizeValidator,\n  pdfFileValidator,\n} from '@shared/validators/file.validators';\n\n@Component({\n  selector: 'app-bank-info',\n  templateUrl: './bank-info.component.html',\n  styleUrl: './bank-info.component.scss',\n  standalone: true,\n  imports: [\n    ReactiveFormsModule,\n    MatIcon,\n    MatFormField,\n    MatLabel,\n    MatSelect,\n    MatOption,\n    MatInput,\n    MatError,\n    MatButton,\n    MatIconButton,\n    MatTooltip,\n  ],\n})\nexport class BankInfoComponent implements OnInit, OnChanges {\n  @Input() initialData?: InitialReportDocumentation;\n  @Input() isSupervisor = false;\n  @Output() formChange = new EventEmitter<void>();\n\n  form: FormGroup = this.fb.group({\n    bank: ['', Validators.required],\n    accountType: ['', Validators.required],\n    accountNumber: ['', Validators.required],\n    bankCertificateFile: [null, [fileSizeValidator(), pdfFileValidator()]],\n  });\n\n  bankList: Bank[] = [];\n  accountTypes: BankAccountType[] = [];\n  bankCertificateFile: File | null = null;\n  bankCertificateFileName: string | null = null;\n\n  constructor(\n    private fb: FormBuilder,\n    private bankService: BankService,\n    private bankAccountTypeService: BankAccountTypeService,\n    private alert: AlertService,\n  ) {}\n\n  ngOnInit(): void {\n    forkJoin({\n      banks: this.bankService.getAll(),\n      accountTypes: this.bankAccountTypeService.getAll(),\n    }).subscribe({\n      next: ({ banks, accountTypes }) => {\n        this.bankList = banks;\n        this.accountTypes = accountTypes;\n      },\n      error: (error) => {\n        this.alert.error(error.error?.detail ?? 'Error al cargar los datos del formulario');\n      },\n    });\n\n    this.form.valueChanges.subscribe(() => {\n      this.formChange.emit();\n    });\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    if (changes['initialData']?.currentValue) {\n      const data = changes['initialData'].currentValue;\n      this.form.patchValue({\n        bank: data.bankId,\n        accountNumber: data.accountNumber,\n        accountType: data.bankAccountTypeId,\n      });\n    }\n  }\n\n  private isPdfFile(file: File): boolean {\n    return file.type === 'application/pdf';\n  }\n\n  onFileSelected(event: Event): void {\n    const file = (event.target as HTMLInputElement).files?.[0];\n    if (file) {\n      if (this.isPdfFile(file)) {\n        if (file.size <= 1024 * 1024) {\n          // 1MB\n          this.form.patchValue({ bankCertificateFile: file });\n          this.bankCertificateFile = file;\n          this.bankCertificateFileName = file.name;\n        } else {\n          this.alert.error('El archivo no debe superar 1MB');\n        }\n      } else {\n        this.alert.error('Solo se permiten archivos PDF');\n      }\n      this.form.get('bankCertificateFile')?.updateValueAndValidity();\n    }\n  }\n\n  downloadFile(): void {\n    if (this.initialData?.bankCertificateFileUrl) {\n      window.open(this.initialData.bankCertificateFileUrl, '_blank');\n    }\n  }\n\n  get isValid(): boolean {\n    const basicFieldsValid = Boolean(\n      this.form.get('bank')?.valid &&\n        this.form.get('accountType')?.valid &&\n        this.form.get('accountNumber')?.valid,\n    );\n\n    const hasNewFile = Boolean(this.form.get('bankCertificateFile')?.value);\n    const hasExistingFile = Boolean(this.initialData?.bankCertificateFileUrl);\n\n    return basicFieldsValid && (hasNewFile || hasExistingFile);\n  }\n\n  getBankName(id: number): string {\n    return this.bankList.find((bank) => bank.id === id)?.name || '';\n  }\n\n  getAccountTypeName(id: number): string {\n    return this.accountTypes.find((type) => type.id === id)?.name || '';\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA6BS;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;AA7BT,SACEE,SAAS,EACTC,YAAY,EACZC,KAAK,EAGLC,MAAM,QAED,eAAe;AACtB,SACEC,WAAW,EAEXC,mBAAmB,EACnBC,UAAU,QACL,gBAAgB;AACvB,SAASC,QAAQ,QAAQ,MAAM;AAE/B,SAASC,SAAS,EAAEC,aAAa,QAAQ,0BAA0B;AACnE,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,QAAQ,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,8BAA8B;AAC/E,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,UAAU,QAAQ,2BAA2B;AAItD,SAASC,sBAAsB,QAAQ,0DAA0D;AACjG,SAASC,WAAW,QAAQ,6CAA6C;AACzE,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SACEC,iBAAiB,EACjBC,gBAAgB,QACX,oCAAoC;AAACxB,cAAA,GAAAyB,CAAA;AAqBrC,IAAMC,iBAAiB,GAAvB,MAAMA,iBAAiB;EAiB5BC,YACUC,EAAe,EACfC,WAAwB,EACxBC,sBAA8C,EAC9CC,KAAmB;IAAA/B,cAAA,GAAAgC,CAAA;IAAAhC,cAAA,GAAAyB,CAAA;IAHnB,KAAAG,EAAE,GAAFA,EAAE;IAAa5B,cAAA,GAAAyB,CAAA;IACf,KAAAI,WAAW,GAAXA,WAAW;IAAa7B,cAAA,GAAAyB,CAAA;IACxB,KAAAK,sBAAsB,GAAtBA,sBAAsB;IAAwB9B,cAAA,GAAAyB,CAAA;IAC9C,KAAAM,KAAK,GAALA,KAAK;IAAc/B,cAAA,GAAAyB,CAAA;IAnBpB,KAAAQ,YAAY,GAAG,KAAK;IAAAjC,cAAA,GAAAyB,CAAA;IACnB,KAAAS,UAAU,GAAG,IAAI/B,YAAY,EAAQ;IAAAH,cAAA,GAAAyB,CAAA;IAE/C,KAAAU,IAAI,GAAc,IAAI,CAACP,EAAE,CAACQ,KAAK,CAAC;MAC9BC,IAAI,EAAE,CAAC,EAAE,EAAE7B,UAAU,CAAC8B,QAAQ,CAAC;MAC/BC,WAAW,EAAE,CAAC,EAAE,EAAE/B,UAAU,CAAC8B,QAAQ,CAAC;MACtCE,aAAa,EAAE,CAAC,EAAE,EAAEhC,UAAU,CAAC8B,QAAQ,CAAC;MACxCG,mBAAmB,EAAE,CAAC,IAAI,EAAE,CAAClB,iBAAiB,EAAE,EAAEC,gBAAgB,EAAE,CAAC;KACtE,CAAC;IAACxB,cAAA,GAAAyB,CAAA;IAEH,KAAAiB,QAAQ,GAAW,EAAE;IAAC1C,cAAA,GAAAyB,CAAA;IACtB,KAAAkB,YAAY,GAAsB,EAAE;IAAC3C,cAAA,GAAAyB,CAAA;IACrC,KAAAgB,mBAAmB,GAAgB,IAAI;IAACzC,cAAA,GAAAyB,CAAA;IACxC,KAAAmB,uBAAuB,GAAkB,IAAI;EAO1C;EAEHC,QAAQA,CAAA;IAAA7C,cAAA,GAAAgC,CAAA;IAAAhC,cAAA,GAAAyB,CAAA;IACNhB,QAAQ,CAAC;MACPqC,KAAK,EAAE,IAAI,CAACjB,WAAW,CAACkB,MAAM,EAAE;MAChCJ,YAAY,EAAE,IAAI,CAACb,sBAAsB,CAACiB,MAAM;KACjD,CAAC,CAACC,SAAS,CAAC;MACXC,IAAI,EAAEA,CAAC;QAAEH,KAAK;QAAEH;MAAY,CAAE,KAAI;QAAA3C,cAAA,GAAAgC,CAAA;QAAAhC,cAAA,GAAAyB,CAAA;QAChC,IAAI,CAACiB,QAAQ,GAAGI,KAAK;QAAC9C,cAAA,GAAAyB,CAAA;QACtB,IAAI,CAACkB,YAAY,GAAGA,YAAY;MAClC,CAAC;MACDO,KAAK,EAAGA,KAAK,IAAI;QAAAlD,cAAA,GAAAgC,CAAA;QAAAhC,cAAA,GAAAyB,CAAA;QACf,IAAI,CAACM,KAAK,CAACmB,KAAK,CAAC,CAAAlD,cAAA,GAAAmD,CAAA,UAAAD,KAAK,CAACA,KAAK,EAAEE,MAAM,MAAApD,cAAA,GAAAmD,CAAA,UAAI,0CAA0C,EAAC;MACrF;KACD,CAAC;IAACnD,cAAA,GAAAyB,CAAA;IAEH,IAAI,CAACU,IAAI,CAACkB,YAAY,CAACL,SAAS,CAAC,MAAK;MAAAhD,cAAA,GAAAgC,CAAA;MAAAhC,cAAA,GAAAyB,CAAA;MACpC,IAAI,CAACS,UAAU,CAACoB,IAAI,EAAE;IACxB,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAACC,OAAsB;IAAAxD,cAAA,GAAAgC,CAAA;IAAAhC,cAAA,GAAAyB,CAAA;IAChC,IAAI+B,OAAO,CAAC,aAAa,CAAC,EAAEC,YAAY,EAAE;MAAAzD,cAAA,GAAAmD,CAAA;MACxC,MAAMO,IAAI,IAAA1D,cAAA,GAAAyB,CAAA,QAAG+B,OAAO,CAAC,aAAa,CAAC,CAACC,YAAY;MAACzD,cAAA,GAAAyB,CAAA;MACjD,IAAI,CAACU,IAAI,CAACwB,UAAU,CAAC;QACnBtB,IAAI,EAAEqB,IAAI,CAACE,MAAM;QACjBpB,aAAa,EAAEkB,IAAI,CAAClB,aAAa;QACjCD,WAAW,EAAEmB,IAAI,CAACG;OACnB,CAAC;IACJ,CAAC;MAAA7D,cAAA,GAAAmD,CAAA;IAAA;EACH;EAEQW,SAASA,CAACC,IAAU;IAAA/D,cAAA,GAAAgC,CAAA;IAAAhC,cAAA,GAAAyB,CAAA;IAC1B,OAAOsC,IAAI,CAACC,IAAI,KAAK,iBAAiB;EACxC;EAEAC,cAAcA,CAACC,KAAY;IAAAlE,cAAA,GAAAgC,CAAA;IACzB,MAAM+B,IAAI,IAAA/D,cAAA,GAAAyB,CAAA,QAAIyC,KAAK,CAACC,MAA2B,CAACC,KAAK,GAAG,CAAC,CAAC;IAACpE,cAAA,GAAAyB,CAAA;IAC3D,IAAIsC,IAAI,EAAE;MAAA/D,cAAA,GAAAmD,CAAA;MAAAnD,cAAA,GAAAyB,CAAA;MACR,IAAI,IAAI,CAACqC,SAAS,CAACC,IAAI,CAAC,EAAE;QAAA/D,cAAA,GAAAmD,CAAA;QAAAnD,cAAA,GAAAyB,CAAA;QACxB,IAAIsC,IAAI,CAACM,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;UAAArE,cAAA,GAAAmD,CAAA;UAAAnD,cAAA,GAAAyB,CAAA;UAC5B;UACA,IAAI,CAACU,IAAI,CAACwB,UAAU,CAAC;YAAElB,mBAAmB,EAAEsB;UAAI,CAAE,CAAC;UAAC/D,cAAA,GAAAyB,CAAA;UACpD,IAAI,CAACgB,mBAAmB,GAAGsB,IAAI;UAAC/D,cAAA,GAAAyB,CAAA;UAChC,IAAI,CAACmB,uBAAuB,GAAGmB,IAAI,CAACO,IAAI;QAC1C,CAAC,MAAM;UAAAtE,cAAA,GAAAmD,CAAA;UAAAnD,cAAA,GAAAyB,CAAA;UACL,IAAI,CAACM,KAAK,CAACmB,KAAK,CAAC,gCAAgC,CAAC;QACpD;MACF,CAAC,MAAM;QAAAlD,cAAA,GAAAmD,CAAA;QAAAnD,cAAA,GAAAyB,CAAA;QACL,IAAI,CAACM,KAAK,CAACmB,KAAK,CAAC,+BAA+B,CAAC;MACnD;MAAClD,cAAA,GAAAyB,CAAA;MACD,IAAI,CAACU,IAAI,CAACoC,GAAG,CAAC,qBAAqB,CAAC,EAAEC,sBAAsB,EAAE;IAChE,CAAC;MAAAxE,cAAA,GAAAmD,CAAA;IAAA;EACH;EAEAsB,YAAYA,CAAA;IAAAzE,cAAA,GAAAgC,CAAA;IAAAhC,cAAA,GAAAyB,CAAA;IACV,IAAI,IAAI,CAACiD,WAAW,EAAEC,sBAAsB,EAAE;MAAA3E,cAAA,GAAAmD,CAAA;MAAAnD,cAAA,GAAAyB,CAAA;MAC5CmD,MAAM,CAACC,IAAI,CAAC,IAAI,CAACH,WAAW,CAACC,sBAAsB,EAAE,QAAQ,CAAC;IAChE,CAAC;MAAA3E,cAAA,GAAAmD,CAAA;IAAA;EACH;EAEA,IAAI2B,OAAOA,CAAA;IAAA9E,cAAA,GAAAgC,CAAA;IACT,MAAM+C,gBAAgB,IAAA/E,cAAA,GAAAyB,CAAA,QAAGuD,OAAO,CAC9B,CAAAhF,cAAA,GAAAmD,CAAA,cAAI,CAAChB,IAAI,CAACoC,GAAG,CAAC,MAAM,CAAC,EAAEU,KAAK,MAAAjF,cAAA,GAAAmD,CAAA,UAC1B,IAAI,CAAChB,IAAI,CAACoC,GAAG,CAAC,aAAa,CAAC,EAAEU,KAAK,MAAAjF,cAAA,GAAAmD,CAAA,UACnC,IAAI,CAAChB,IAAI,CAACoC,GAAG,CAAC,eAAe,CAAC,EAAEU,KAAK,EACxC;IAED,MAAMC,UAAU,IAAAlF,cAAA,GAAAyB,CAAA,QAAGuD,OAAO,CAAC,IAAI,CAAC7C,IAAI,CAACoC,GAAG,CAAC,qBAAqB,CAAC,EAAEY,KAAK,CAAC;IACvE,MAAMC,eAAe,IAAApF,cAAA,GAAAyB,CAAA,QAAGuD,OAAO,CAAC,IAAI,CAACN,WAAW,EAAEC,sBAAsB,CAAC;IAAC3E,cAAA,GAAAyB,CAAA;IAE1E,OAAO,CAAAzB,cAAA,GAAAmD,CAAA,UAAA4B,gBAAgB,MAAK,CAAA/E,cAAA,GAAAmD,CAAA,UAAA+B,UAAU,MAAAlF,cAAA,GAAAmD,CAAA,UAAIiC,eAAe,EAAC;EAC5D;EAEAC,WAAWA,CAACC,EAAU;IAAAtF,cAAA,GAAAgC,CAAA;IAAAhC,cAAA,GAAAyB,CAAA;IACpB,OAAO,CAAAzB,cAAA,GAAAmD,CAAA,cAAI,CAACT,QAAQ,CAAC6C,IAAI,CAAElD,IAAI,IAAK;MAAArC,cAAA,GAAAgC,CAAA;MAAAhC,cAAA,GAAAyB,CAAA;MAAA,OAAAY,IAAI,CAACiD,EAAE,KAAKA,EAAE;IAAF,CAAE,CAAC,EAAEhB,IAAI,MAAAtE,cAAA,GAAAmD,CAAA,UAAI,EAAE;EACjE;EAEAqC,kBAAkBA,CAACF,EAAU;IAAAtF,cAAA,GAAAgC,CAAA;IAAAhC,cAAA,GAAAyB,CAAA;IAC3B,OAAO,CAAAzB,cAAA,GAAAmD,CAAA,cAAI,CAACR,YAAY,CAAC4C,IAAI,CAAEvB,IAAI,IAAK;MAAAhE,cAAA,GAAAgC,CAAA;MAAAhC,cAAA,GAAAyB,CAAA;MAAA,OAAAuC,IAAI,CAACsB,EAAE,KAAKA,EAAE;IAAF,CAAE,CAAC,EAAEhB,IAAI,MAAAtE,cAAA,GAAAmD,CAAA,UAAI,EAAE;EACrE;;;;;;;;;;;;;;;;;;;;;cArGC/C;MAAK;;cACLA;MAAK;;cACLC;MAAM;;;;;AAHIqB,iBAAiB,GAAA+D,UAAA,EAnB7BvF,SAAS,CAAC;EACTwF,QAAQ,EAAE,eAAe;EACzBC,QAAA,EAAAC,oBAAyC;EAEzCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPvF,mBAAmB,EACnBS,OAAO,EACPF,YAAY,EACZC,QAAQ,EACRG,SAAS,EACTN,SAAS,EACTK,QAAQ,EACRJ,QAAQ,EACRH,SAAS,EACTC,aAAa,EACbQ,UAAU,CACX;;CACF,CAAC,C,EACWO,iBAAiB,CAuG7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}