{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/Github/SICMA/sicma-ui/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { MonthlyReportApprovalDialogComponent } from './monthly-report-approval-dialog.component';\nimport { AlertService } from '@shared/services/alert.service';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { FormBuilder } from '@angular/forms';\ndescribe('MonthlyReportApprovalDialogComponent', () => {\n  let component;\n  let fixture;\n  let dialogRefSpy;\n  let alertServiceSpy;\n  beforeEach(() => {\n    dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close']);\n    alertServiceSpy = jasmine.createSpyObj('AlertService', ['confirm']);\n    TestBed.configureTestingModule({\n      imports: [MonthlyReportApprovalDialogComponent, HttpClientTestingModule, BrowserAnimationsModule, ReactiveFormsModule],\n      providers: [{\n        provide: MatDialogRef,\n        useValue: dialogRefSpy\n      }, {\n        provide: MAT_DIALOG_DATA,\n        useValue: {\n          isRejection: false\n        }\n      }, {\n        provide: AlertService,\n        useValue: alertServiceSpy\n      }, FormBuilder]\n    });\n    fixture = TestBed.createComponent(MonthlyReportApprovalDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should initialize with correct title and button text for approval', () => {\n    expect(component.dialogTitle).toBe('Aprobar Informe');\n    expect(component.confirmButtonText).toBe('Aprobar');\n    expect(component.confirmButtonColor).toBe('primary');\n  });\n  it('should initialize with correct title and button text for rejection', () => {\n    TestBed.resetTestingModule();\n    TestBed.configureTestingModule({\n      imports: [MonthlyReportApprovalDialogComponent, HttpClientTestingModule, BrowserAnimationsModule, ReactiveFormsModule],\n      providers: [{\n        provide: MatDialogRef,\n        useValue: dialogRefSpy\n      }, {\n        provide: MAT_DIALOG_DATA,\n        useValue: {\n          isRejection: true\n        }\n      }, {\n        provide: AlertService,\n        useValue: alertServiceSpy\n      }, FormBuilder]\n    });\n    fixture = TestBed.createComponent(MonthlyReportApprovalDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n    expect(component.dialogTitle).toBe('Rechazar Informe');\n    expect(component.confirmButtonText).toBe('Rechazar');\n    expect(component.confirmButtonColor).toBe('warn');\n  });\n  it('should close dialog when cancel is clicked', () => {\n    component.onCancel();\n    expect(dialogRefSpy.close).toHaveBeenCalled();\n  });\n  it('should mark form as touched when submitted with invalid data', /*#__PURE__*/_asyncToGenerator(function* () {\n    spyOn(component.form, 'markAllAsTouched');\n    component.form.controls['comments'].setValue('');\n    yield component.onSubmit();\n    expect(component.form.markAllAsTouched).toHaveBeenCalled();\n    expect(dialogRefSpy.close).not.toHaveBeenCalled();\n  }));\n  it('should close with comments when form is valid and user confirms', /*#__PURE__*/_asyncToGenerator(function* () {\n    const comments = 'Test comments';\n    component.form.controls['comments'].setValue(comments);\n    alertServiceSpy.confirm.and.resolveTo(true);\n    yield component.onSubmit();\n    expect(alertServiceSpy.confirm).toHaveBeenCalled();\n    expect(dialogRefSpy.close).toHaveBeenCalledWith(comments);\n  }));\n  it('should not close when user cancels the confirmation', /*#__PURE__*/_asyncToGenerator(function* () {\n    const comments = 'Test comments';\n    component.form.controls['comments'].setValue(comments);\n    alertServiceSpy.confirm.and.resolveTo(false);\n    yield component.onSubmit();\n    expect(alertServiceSpy.confirm).toHaveBeenCalled();\n    expect(dialogRefSpy.close).not.toHaveBeenCalled();\n  }));\n  it('should use correct confirmation message for approval', /*#__PURE__*/_asyncToGenerator(function* () {\n    component.form.controls['comments'].setValue('Test comments');\n    alertServiceSpy.confirm.and.resolveTo(true);\n    yield component.onSubmit();\n    expect(alertServiceSpy.confirm).toHaveBeenCalledWith('¿Está seguro de aprobar este informe?');\n  }));\n  it('should use correct confirmation message for rejection', /*#__PURE__*/_asyncToGenerator(function* () {\n    TestBed.resetTestingModule();\n    TestBed.configureTestingModule({\n      imports: [MonthlyReportApprovalDialogComponent, HttpClientTestingModule, BrowserAnimationsModule, ReactiveFormsModule],\n      providers: [{\n        provide: MatDialogRef,\n        useValue: dialogRefSpy\n      }, {\n        provide: MAT_DIALOG_DATA,\n        useValue: {\n          isRejection: true\n        }\n      }, {\n        provide: AlertService,\n        useValue: alertServiceSpy\n      }, FormBuilder]\n    });\n    fixture = TestBed.createComponent(MonthlyReportApprovalDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n    component.form.controls['comments'].setValue('Test comments');\n    alertServiceSpy.confirm.and.resolveTo(true);\n    yield component.onSubmit();\n    expect(alertServiceSpy.confirm).toHaveBeenCalledWith('¿Está seguro de rechazar este informe?');\n  }));\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "TestBed", "MAT_DIALOG_DATA", "MatDialogRef", "BrowserAnimationsModule", "MonthlyReportApprovalDialogComponent", "AlertService", "ReactiveFormsModule", "FormBuilder", "describe", "component", "fixture", "dialogRefSpy", "alertServiceSpy", "beforeEach", "jasmine", "createSpyObj", "configureTestingModule", "imports", "providers", "provide", "useValue", "isRejection", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy", "dialogTitle", "toBe", "confirmButtonText", "confirmButtonColor", "resetTestingModule", "onCancel", "close", "toHaveBeenCalled", "_asyncToGenerator", "spyOn", "form", "controls", "setValue", "onSubmit", "mark<PERSON>llAsTouched", "not", "comments", "confirm", "and", "resolveTo", "toHaveBeenCalledWith"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\components\\monthly-reports-tab\\monthly-report-dialog\\monthly-report-approval-dialog\\monthly-report-approval-dialog.component.spec.ts"], "sourcesContent": ["import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { ComponentFixture, TestBed } from '@angular/core/testing';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { MonthlyReportApprovalDialogComponent } from './monthly-report-approval-dialog.component';\nimport { AlertService } from '@shared/services/alert.service';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { FormBuilder } from '@angular/forms';\n\ndescribe('MonthlyReportApprovalDialogComponent', () => {\n  let component: MonthlyReportApprovalDialogComponent;\n  let fixture: ComponentFixture<MonthlyReportApprovalDialogComponent>;\n  let dialogRefSpy: jasmine.SpyObj<\n    MatDialogRef<MonthlyReportApprovalDialogComponent>\n  >;\n  let alertServiceSpy: jasmine.SpyObj<AlertService>;\n\n  beforeEach(() => {\n    dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close']);\n    alertServiceSpy = jasmine.createSpyObj('AlertService', ['confirm']);\n\n    TestBed.configureTestingModule({\n      imports: [\n        MonthlyReportApprovalDialogComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n        ReactiveFormsModule,\n      ],\n      providers: [\n        { provide: MatDialogRef, useValue: dialogRefSpy },\n        { provide: MAT_DIALOG_DATA, useValue: { isRejection: false } },\n        { provide: AlertService, useValue: alertServiceSpy },\n        FormBuilder,\n      ],\n    });\n    fixture = TestBed.createComponent(MonthlyReportApprovalDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should initialize with correct title and button text for approval', () => {\n    expect(component.dialogTitle).toBe('Aprobar Informe');\n    expect(component.confirmButtonText).toBe('Aprobar');\n    expect(component.confirmButtonColor).toBe('primary');\n  });\n\n  it('should initialize with correct title and button text for rejection', () => {\n    TestBed.resetTestingModule();\n    TestBed.configureTestingModule({\n      imports: [\n        MonthlyReportApprovalDialogComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n        ReactiveFormsModule,\n      ],\n      providers: [\n        { provide: MatDialogRef, useValue: dialogRefSpy },\n        { provide: MAT_DIALOG_DATA, useValue: { isRejection: true } },\n        { provide: AlertService, useValue: alertServiceSpy },\n        FormBuilder,\n      ],\n    });\n    fixture = TestBed.createComponent(MonthlyReportApprovalDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n\n    expect(component.dialogTitle).toBe('Rechazar Informe');\n    expect(component.confirmButtonText).toBe('Rechazar');\n    expect(component.confirmButtonColor).toBe('warn');\n  });\n\n  it('should close dialog when cancel is clicked', () => {\n    component.onCancel();\n    expect(dialogRefSpy.close).toHaveBeenCalled();\n  });\n\n  it('should mark form as touched when submitted with invalid data', async () => {\n    spyOn(component.form, 'markAllAsTouched');\n    component.form.controls['comments'].setValue('');\n    await component.onSubmit();\n    expect(component.form.markAllAsTouched).toHaveBeenCalled();\n    expect(dialogRefSpy.close).not.toHaveBeenCalled();\n  });\n\n  it('should close with comments when form is valid and user confirms', async () => {\n    const comments = 'Test comments';\n    component.form.controls['comments'].setValue(comments);\n    alertServiceSpy.confirm.and.resolveTo(true);\n\n    await component.onSubmit();\n\n    expect(alertServiceSpy.confirm).toHaveBeenCalled();\n    expect(dialogRefSpy.close).toHaveBeenCalledWith(comments);\n  });\n\n  it('should not close when user cancels the confirmation', async () => {\n    const comments = 'Test comments';\n    component.form.controls['comments'].setValue(comments);\n    alertServiceSpy.confirm.and.resolveTo(false);\n\n    await component.onSubmit();\n\n    expect(alertServiceSpy.confirm).toHaveBeenCalled();\n    expect(dialogRefSpy.close).not.toHaveBeenCalled();\n  });\n\n  it('should use correct confirmation message for approval', async () => {\n    component.form.controls['comments'].setValue('Test comments');\n    alertServiceSpy.confirm.and.resolveTo(true);\n\n    await component.onSubmit();\n\n    expect(alertServiceSpy.confirm).toHaveBeenCalledWith(\n      '¿Está seguro de aprobar este informe?',\n    );\n  });\n\n  it('should use correct confirmation message for rejection', async () => {\n    TestBed.resetTestingModule();\n    TestBed.configureTestingModule({\n      imports: [\n        MonthlyReportApprovalDialogComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n        ReactiveFormsModule,\n      ],\n      providers: [\n        { provide: MatDialogRef, useValue: dialogRefSpy },\n        { provide: MAT_DIALOG_DATA, useValue: { isRejection: true } },\n        { provide: AlertService, useValue: alertServiceSpy },\n        FormBuilder,\n      ],\n    });\n    fixture = TestBed.createComponent(MonthlyReportApprovalDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n\n    component.form.controls['comments'].setValue('Test comments');\n    alertServiceSpy.confirm.and.resolveTo(true);\n\n    await component.onSubmit();\n\n    expect(alertServiceSpy.confirm).toHaveBeenCalledWith(\n      '¿Está seguro de rechazar este informe?',\n    );\n  });\n});"], "mappings": ";AAAA,SAASA,uBAAuB,QAAQ,8BAA8B;AACtE,SAA2BC,OAAO,QAAQ,uBAAuB;AACjE,SAASC,eAAe,EAAEC,YAAY,QAAQ,0BAA0B;AACxE,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,oCAAoC,QAAQ,4CAA4C;AACjG,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,WAAW,QAAQ,gBAAgB;AAE5CC,QAAQ,CAAC,sCAAsC,EAAE,MAAK;EACpD,IAAIC,SAA+C;EACnD,IAAIC,OAA+D;EACnE,IAAIC,YAEH;EACD,IAAIC,eAA6C;EAEjDC,UAAU,CAAC,MAAK;IACdF,YAAY,GAAGG,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC;IAC9DH,eAAe,GAAGE,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,CAAC;IAEnEf,OAAO,CAACgB,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPb,oCAAoC,EACpCL,uBAAuB,EACvBI,uBAAuB,EACvBG,mBAAmB,CACpB;MACDY,SAAS,EAAE,CACT;QAAEC,OAAO,EAAEjB,YAAY;QAAEkB,QAAQ,EAAET;MAAY,CAAE,EACjD;QAAEQ,OAAO,EAAElB,eAAe;QAAEmB,QAAQ,EAAE;UAAEC,WAAW,EAAE;QAAK;MAAE,CAAE,EAC9D;QAAEF,OAAO,EAAEd,YAAY;QAAEe,QAAQ,EAAER;MAAe,CAAE,EACpDL,WAAW;KAEd,CAAC;IACFG,OAAO,GAAGV,OAAO,CAACsB,eAAe,CAAClB,oCAAoC,CAAC;IACvEK,SAAS,GAAGC,OAAO,CAACa,iBAAiB;IACrCb,OAAO,CAACc,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACjB,SAAS,CAAC,CAACkB,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,mEAAmE,EAAE,MAAK;IAC3EC,MAAM,CAACjB,SAAS,CAACmB,WAAW,CAAC,CAACC,IAAI,CAAC,iBAAiB,CAAC;IACrDH,MAAM,CAACjB,SAAS,CAACqB,iBAAiB,CAAC,CAACD,IAAI,CAAC,SAAS,CAAC;IACnDH,MAAM,CAACjB,SAAS,CAACsB,kBAAkB,CAAC,CAACF,IAAI,CAAC,SAAS,CAAC;EACtD,CAAC,CAAC;EAEFJ,EAAE,CAAC,oEAAoE,EAAE,MAAK;IAC5EzB,OAAO,CAACgC,kBAAkB,EAAE;IAC5BhC,OAAO,CAACgB,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPb,oCAAoC,EACpCL,uBAAuB,EACvBI,uBAAuB,EACvBG,mBAAmB,CACpB;MACDY,SAAS,EAAE,CACT;QAAEC,OAAO,EAAEjB,YAAY;QAAEkB,QAAQ,EAAET;MAAY,CAAE,EACjD;QAAEQ,OAAO,EAAElB,eAAe;QAAEmB,QAAQ,EAAE;UAAEC,WAAW,EAAE;QAAI;MAAE,CAAE,EAC7D;QAAEF,OAAO,EAAEd,YAAY;QAAEe,QAAQ,EAAER;MAAe,CAAE,EACpDL,WAAW;KAEd,CAAC;IACFG,OAAO,GAAGV,OAAO,CAACsB,eAAe,CAAClB,oCAAoC,CAAC;IACvEK,SAAS,GAAGC,OAAO,CAACa,iBAAiB;IACrCb,OAAO,CAACc,aAAa,EAAE;IAEvBE,MAAM,CAACjB,SAAS,CAACmB,WAAW,CAAC,CAACC,IAAI,CAAC,kBAAkB,CAAC;IACtDH,MAAM,CAACjB,SAAS,CAACqB,iBAAiB,CAAC,CAACD,IAAI,CAAC,UAAU,CAAC;IACpDH,MAAM,CAACjB,SAAS,CAACsB,kBAAkB,CAAC,CAACF,IAAI,CAAC,MAAM,CAAC;EACnD,CAAC,CAAC;EAEFJ,EAAE,CAAC,4CAA4C,EAAE,MAAK;IACpDhB,SAAS,CAACwB,QAAQ,EAAE;IACpBP,MAAM,CAACf,YAAY,CAACuB,KAAK,CAAC,CAACC,gBAAgB,EAAE;EAC/C,CAAC,CAAC;EAEFV,EAAE,CAAC,8DAA8D,eAAAW,iBAAA,CAAE,aAAW;IAC5EC,KAAK,CAAC5B,SAAS,CAAC6B,IAAI,EAAE,kBAAkB,CAAC;IACzC7B,SAAS,CAAC6B,IAAI,CAACC,QAAQ,CAAC,UAAU,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;IAChD,MAAM/B,SAAS,CAACgC,QAAQ,EAAE;IAC1Bf,MAAM,CAACjB,SAAS,CAAC6B,IAAI,CAACI,gBAAgB,CAAC,CAACP,gBAAgB,EAAE;IAC1DT,MAAM,CAACf,YAAY,CAACuB,KAAK,CAAC,CAACS,GAAG,CAACR,gBAAgB,EAAE;EACnD,CAAC,EAAC;EAEFV,EAAE,CAAC,iEAAiE,eAAAW,iBAAA,CAAE,aAAW;IAC/E,MAAMQ,QAAQ,GAAG,eAAe;IAChCnC,SAAS,CAAC6B,IAAI,CAACC,QAAQ,CAAC,UAAU,CAAC,CAACC,QAAQ,CAACI,QAAQ,CAAC;IACtDhC,eAAe,CAACiC,OAAO,CAACC,GAAG,CAACC,SAAS,CAAC,IAAI,CAAC;IAE3C,MAAMtC,SAAS,CAACgC,QAAQ,EAAE;IAE1Bf,MAAM,CAACd,eAAe,CAACiC,OAAO,CAAC,CAACV,gBAAgB,EAAE;IAClDT,MAAM,CAACf,YAAY,CAACuB,KAAK,CAAC,CAACc,oBAAoB,CAACJ,QAAQ,CAAC;EAC3D,CAAC,EAAC;EAEFnB,EAAE,CAAC,qDAAqD,eAAAW,iBAAA,CAAE,aAAW;IACnE,MAAMQ,QAAQ,GAAG,eAAe;IAChCnC,SAAS,CAAC6B,IAAI,CAACC,QAAQ,CAAC,UAAU,CAAC,CAACC,QAAQ,CAACI,QAAQ,CAAC;IACtDhC,eAAe,CAACiC,OAAO,CAACC,GAAG,CAACC,SAAS,CAAC,KAAK,CAAC;IAE5C,MAAMtC,SAAS,CAACgC,QAAQ,EAAE;IAE1Bf,MAAM,CAACd,eAAe,CAACiC,OAAO,CAAC,CAACV,gBAAgB,EAAE;IAClDT,MAAM,CAACf,YAAY,CAACuB,KAAK,CAAC,CAACS,GAAG,CAACR,gBAAgB,EAAE;EACnD,CAAC,EAAC;EAEFV,EAAE,CAAC,sDAAsD,eAAAW,iBAAA,CAAE,aAAW;IACpE3B,SAAS,CAAC6B,IAAI,CAACC,QAAQ,CAAC,UAAU,CAAC,CAACC,QAAQ,CAAC,eAAe,CAAC;IAC7D5B,eAAe,CAACiC,OAAO,CAACC,GAAG,CAACC,SAAS,CAAC,IAAI,CAAC;IAE3C,MAAMtC,SAAS,CAACgC,QAAQ,EAAE;IAE1Bf,MAAM,CAACd,eAAe,CAACiC,OAAO,CAAC,CAACG,oBAAoB,CAClD,uCAAuC,CACxC;EACH,CAAC,EAAC;EAEFvB,EAAE,CAAC,uDAAuD,eAAAW,iBAAA,CAAE,aAAW;IACrEpC,OAAO,CAACgC,kBAAkB,EAAE;IAC5BhC,OAAO,CAACgB,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPb,oCAAoC,EACpCL,uBAAuB,EACvBI,uBAAuB,EACvBG,mBAAmB,CACpB;MACDY,SAAS,EAAE,CACT;QAAEC,OAAO,EAAEjB,YAAY;QAAEkB,QAAQ,EAAET;MAAY,CAAE,EACjD;QAAEQ,OAAO,EAAElB,eAAe;QAAEmB,QAAQ,EAAE;UAAEC,WAAW,EAAE;QAAI;MAAE,CAAE,EAC7D;QAAEF,OAAO,EAAEd,YAAY;QAAEe,QAAQ,EAAER;MAAe,CAAE,EACpDL,WAAW;KAEd,CAAC;IACFG,OAAO,GAAGV,OAAO,CAACsB,eAAe,CAAClB,oCAAoC,CAAC;IACvEK,SAAS,GAAGC,OAAO,CAACa,iBAAiB;IACrCb,OAAO,CAACc,aAAa,EAAE;IAEvBf,SAAS,CAAC6B,IAAI,CAACC,QAAQ,CAAC,UAAU,CAAC,CAACC,QAAQ,CAAC,eAAe,CAAC;IAC7D5B,eAAe,CAACiC,OAAO,CAACC,GAAG,CAACC,SAAS,CAAC,IAAI,CAAC;IAE3C,MAAMtC,SAAS,CAACgC,QAAQ,EAAE;IAE1Bf,MAAM,CAACd,eAAe,CAACiC,OAAO,CAAC,CAACG,oBAAoB,CAClD,wCAAwC,CACzC;EACH,CAAC,EAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}