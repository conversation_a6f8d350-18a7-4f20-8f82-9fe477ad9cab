{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { Router } from '@angular/router';\nimport { AuthStatus } from '@core/auth/enums/auth-status.enum';\nimport { authGuard } from './auth.guard';\ndescribe('authGuard', () => {\n  let router;\n  let mockRoute;\n  let mockState;\n  beforeEach(() => {\n    const routerSpy = jasmine.createSpyObj('Router', ['navigateByUrl']);\n    mockRoute = {};\n    mockState = {\n      url: '/test'\n    };\n    TestBed.configureTestingModule({\n      providers: [{\n        provide: Router,\n        useValue: routerSpy\n      }]\n    });\n    router = TestBed.inject(Router);\n  });\n  beforeEach(() => {\n    localStorage.clear();\n  });\n  it('should be created', () => {\n    expect(authGuard).toBeTruthy();\n  });\n  it('should allow access when user is authenticated', () => {\n    localStorage.setItem('status', AuthStatus.authenticated);\n    const result = TestBed.runInInjectionContext(() => authGuard(mockRoute, mockState));\n    expect(result).toBe(true);\n    expect(router.navigateByUrl).not.toHaveBeenCalled();\n  });\n  it('should redirect to home and deny access when user is not authenticated', () => {\n    const result = TestBed.runInInjectionContext(() => authGuard(mockRoute, mockState));\n    expect(result).toBe(false);\n    expect(router.navigateByUrl).toHaveBeenCalledWith('/');\n  });\n  it('should redirect to home and deny access when status is invalid', () => {\n    localStorage.setItem('status', 'invalid_status');\n    const result = TestBed.runInInjectionContext(() => authGuard(mockRoute, mockState));\n    expect(result).toBe(false);\n    expect(router.navigateByUrl).toHaveBeenCalledWith('/');\n  });\n});", "map": {"version": 3, "names": ["TestBed", "Router", "AuthStatus", "<PERSON>th<PERSON><PERSON>", "describe", "router", "mockRoute", "mockState", "beforeEach", "routerSpy", "jasmine", "createSpyObj", "url", "configureTestingModule", "providers", "provide", "useValue", "inject", "localStorage", "clear", "it", "expect", "toBeTruthy", "setItem", "authenticated", "result", "runInInjectionContext", "toBe", "navigateByUrl", "not", "toHaveBeenCalled", "toHaveBeenCalledWith"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\core\\auth\\guards\\auth.guard.spec.ts"], "sourcesContent": ["import { TestBed } from '@angular/core/testing';\nimport {\n  ActivatedRouteSnapshot,\n  Router,\n  RouterStateSnapshot,\n} from '@angular/router';\nimport { AuthStatus } from '@core/auth/enums/auth-status.enum';\nimport { authGuard } from './auth.guard';\n\ndescribe('authGuard', () => {\n  let router: jasmine.SpyObj<Router>;\n  let mockRoute: ActivatedRouteSnapshot;\n  let mockState: RouterStateSnapshot;\n\n  beforeEach(() => {\n    const routerSpy = jasmine.createSpyObj('Router', ['navigateByUrl']);\n    mockRoute = {} as ActivatedRouteSnapshot;\n    mockState = { url: '/test' } as RouterStateSnapshot;\n\n    TestBed.configureTestingModule({\n      providers: [{ provide: Router, useValue: routerSpy }],\n    });\n\n    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;\n  });\n\n  beforeEach(() => {\n    localStorage.clear();\n  });\n\n  it('should be created', () => {\n    expect(authGuard).toBeTruthy();\n  });\n\n  it('should allow access when user is authenticated', () => {\n    localStorage.setItem('status', AuthStatus.authenticated);\n\n    const result = TestBed.runInInjectionContext(() =>\n      authGuard(mockRoute, mockState),\n    );\n\n    expect(result).toBe(true);\n    expect(router.navigateByUrl).not.toHaveBeenCalled();\n  });\n\n  it('should redirect to home and deny access when user is not authenticated', () => {\n    const result = TestBed.runInInjectionContext(() =>\n      authGuard(mockRoute, mockState),\n    );\n\n    expect(result).toBe(false);\n    expect(router.navigateByUrl).toHaveBeenCalledWith('/');\n  });\n\n  it('should redirect to home and deny access when status is invalid', () => {\n    localStorage.setItem('status', 'invalid_status');\n\n    const result = TestBed.runInInjectionContext(() =>\n      authGuard(mockRoute, mockState),\n    );\n\n    expect(result).toBe(false);\n    expect(router.navigateByUrl).toHaveBeenCalledWith('/');\n  });\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,uBAAuB;AAC/C,SAEEC,MAAM,QAED,iBAAiB;AACxB,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,SAAS,QAAQ,cAAc;AAExCC,QAAQ,CAAC,WAAW,EAAE,MAAK;EACzB,IAAIC,MAA8B;EAClC,IAAIC,SAAiC;EACrC,IAAIC,SAA8B;EAElCC,UAAU,CAAC,MAAK;IACd,MAAMC,SAAS,GAAGC,OAAO,CAACC,YAAY,CAAC,QAAQ,EAAE,CAAC,eAAe,CAAC,CAAC;IACnEL,SAAS,GAAG,EAA4B;IACxCC,SAAS,GAAG;MAAEK,GAAG,EAAE;IAAO,CAAyB;IAEnDZ,OAAO,CAACa,sBAAsB,CAAC;MAC7BC,SAAS,EAAE,CAAC;QAAEC,OAAO,EAAEd,MAAM;QAAEe,QAAQ,EAAEP;MAAS,CAAE;KACrD,CAAC;IAEFJ,MAAM,GAAGL,OAAO,CAACiB,MAAM,CAAChB,MAAM,CAA2B;EAC3D,CAAC,CAAC;EAEFO,UAAU,CAAC,MAAK;IACdU,YAAY,CAACC,KAAK,EAAE;EACtB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAAClB,SAAS,CAAC,CAACmB,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,gDAAgD,EAAE,MAAK;IACxDF,YAAY,CAACK,OAAO,CAAC,QAAQ,EAAErB,UAAU,CAACsB,aAAa,CAAC;IAExD,MAAMC,MAAM,GAAGzB,OAAO,CAAC0B,qBAAqB,CAAC,MAC3CvB,SAAS,CAACG,SAAS,EAAEC,SAAS,CAAC,CAChC;IAEDc,MAAM,CAACI,MAAM,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC;IACzBN,MAAM,CAAChB,MAAM,CAACuB,aAAa,CAAC,CAACC,GAAG,CAACC,gBAAgB,EAAE;EACrD,CAAC,CAAC;EAEFV,EAAE,CAAC,wEAAwE,EAAE,MAAK;IAChF,MAAMK,MAAM,GAAGzB,OAAO,CAAC0B,qBAAqB,CAAC,MAC3CvB,SAAS,CAACG,SAAS,EAAEC,SAAS,CAAC,CAChC;IAEDc,MAAM,CAACI,MAAM,CAAC,CAACE,IAAI,CAAC,KAAK,CAAC;IAC1BN,MAAM,CAAChB,MAAM,CAACuB,aAAa,CAAC,CAACG,oBAAoB,CAAC,GAAG,CAAC;EACxD,CAAC,CAAC;EAEFX,EAAE,CAAC,gEAAgE,EAAE,MAAK;IACxEF,YAAY,CAACK,OAAO,CAAC,QAAQ,EAAE,gBAAgB,CAAC;IAEhD,MAAME,MAAM,GAAGzB,OAAO,CAAC0B,qBAAqB,CAAC,MAC3CvB,SAAS,CAACG,SAAS,EAAEC,SAAS,CAAC,CAChC;IAEDc,MAAM,CAACI,MAAM,CAAC,CAACE,IAAI,CAAC,KAAK,CAAC;IAC1BN,MAAM,CAAChB,MAAM,CAACuB,aAAa,CAAC,CAACG,oBAAoB,CAAC,GAAG,CAAC;EACxD,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}