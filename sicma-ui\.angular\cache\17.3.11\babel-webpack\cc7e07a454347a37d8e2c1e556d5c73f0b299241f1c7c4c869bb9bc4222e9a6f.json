{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { ReportReviewHistoryService } from './report-review-history.service';\ndescribe('ReportReviewHistoryService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/report-review-histories`;\n  const mockReportReviewHistory = {\n    id: 1,\n    comment: 'Test comments',\n    reviewDate: new Date(),\n    monthlyReportId: 1,\n    reviewStatusId: 1,\n    reviewerId: 1\n  };\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [ReportReviewHistoryService]\n    });\n    service = TestBed.inject(ReportReviewHistoryService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all report review histories', () => {\n      const mockHistories = [mockReportReviewHistory];\n      service.getAll().subscribe(histories => {\n        expect(histories).toEqual(mockHistories);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockHistories);\n    });\n    it('should handle error when getting all report review histories', () => {\n      service.getAll().subscribe({\n        error: error => {\n          expect(error.status).toBe(500);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Server error'), {\n        status: 500\n      });\n    });\n  });\n  describe('getById', () => {\n    it('should return a report review history by id', () => {\n      const id = 1;\n      service.getById(id).subscribe(history => {\n        expect(history).toEqual(mockReportReviewHistory);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockReportReviewHistory);\n    });\n    it('should handle error when getting report review history by id', () => {\n      const id = 999;\n      service.getById(id).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n  describe('create', () => {\n    it('should create a new report review history', () => {\n      const newHistory = {\n        comment: 'Test comments',\n        reviewDate: new Date(),\n        monthlyReportId: 1,\n        reviewStatusId: 1,\n        reviewerId: 1\n      };\n      service.create(newHistory).subscribe(history => {\n        expect(history).toEqual(mockReportReviewHistory);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newHistory);\n      req.flush(mockReportReviewHistory);\n    });\n    it('should handle error when creating report review history', () => {\n      const newHistory = {\n        comment: 'Test comments',\n        reviewDate: new Date(),\n        monthlyReportId: 1,\n        reviewStatusId: 1,\n        reviewerId: 1\n      };\n      service.create(newHistory).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n  describe('update', () => {\n    it('should update a report review history', () => {\n      const id = 1;\n      const updateData = {\n        comment: 'Updated comments'\n      };\n      service.update(id, updateData).subscribe(history => {\n        expect(history).toEqual(mockReportReviewHistory);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockReportReviewHistory);\n    });\n    it('should handle error when updating report review history', () => {\n      const id = 1;\n      const updateData = {\n        comment: 'Updated comments'\n      };\n      service.update(id, updateData).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n  describe('delete', () => {\n    it('should delete a report review history', () => {\n      const id = 1;\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n    it('should handle error when deleting report review history', () => {\n      const id = 1;\n      service.delete(id).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "ReportReviewHistoryService", "describe", "service", "httpMock", "apiUrl", "mockReportReviewHistory", "id", "comment", "reviewDate", "Date", "monthlyReportId", "reviewStatusId", "reviewerId", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockHistories", "getAll", "subscribe", "histories", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "error", "status", "ErrorEvent", "getById", "history", "newHistory", "create", "body", "updateData", "update", "delete", "nothing"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\services\\report-review-history.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { ReportReviewHistory } from '@contractor-dashboard/models/report-review-history.model';\nimport { environment } from '@env';\nimport { ReportReviewHistoryService } from './report-review-history.service';\n\ndescribe('ReportReviewHistoryService', () => {\n  let service: ReportReviewHistoryService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/report-review-histories`;\n\n  const mockReportReviewHistory: ReportReviewHistory = {\n    id: 1,\n    comment: 'Test comments',\n    reviewDate: new Date(),\n    monthlyReportId: 1,\n    reviewStatusId: 1,\n    reviewerId: 1,\n  };\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [ReportReviewHistoryService],\n    });\n    service = TestBed.inject(ReportReviewHistoryService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all report review histories', () => {\n      const mockHistories = [mockReportReviewHistory];\n\n      service.getAll().subscribe((histories) => {\n        expect(histories).toEqual(mockHistories);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockHistories);\n    });\n\n    it('should handle error when getting all report review histories', () => {\n      service.getAll().subscribe({\n        error: (error) => {\n          expect(error.status).toBe(500);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Server error'), { status: 500 });\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a report review history by id', () => {\n      const id = 1;\n\n      service.getById(id).subscribe((history) => {\n        expect(history).toEqual(mockReportReviewHistory);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockReportReviewHistory);\n    });\n\n    it('should handle error when getting report review history by id', () => {\n      const id = 999;\n\n      service.getById(id).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n\n  describe('create', () => {\n    it('should create a new report review history', () => {\n      const newHistory: Omit<ReportReviewHistory, 'id'> = {\n        comment: 'Test comments',\n        reviewDate: new Date(),\n        monthlyReportId: 1,\n        reviewStatusId: 1,\n        reviewerId: 1,\n      };\n\n      service.create(newHistory).subscribe((history) => {\n        expect(history).toEqual(mockReportReviewHistory);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newHistory);\n      req.flush(mockReportReviewHistory);\n    });\n\n    it('should handle error when creating report review history', () => {\n      const newHistory: Omit<ReportReviewHistory, 'id'> = {\n        comment: 'Test comments',\n        reviewDate: new Date(),\n        monthlyReportId: 1,\n        reviewStatusId: 1,\n        reviewerId: 1,\n      };\n\n      service.create(newHistory).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n\n  describe('update', () => {\n    it('should update a report review history', () => {\n      const id = 1;\n      const updateData: Partial<ReportReviewHistory> = {\n        comment: 'Updated comments',\n      };\n\n      service.update(id, updateData).subscribe((history) => {\n        expect(history).toEqual(mockReportReviewHistory);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockReportReviewHistory);\n    });\n\n    it('should handle error when updating report review history', () => {\n      const id = 1;\n      const updateData: Partial<ReportReviewHistory> = {\n        comment: 'Updated comments',\n      };\n\n      service.update(id, updateData).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete a report review history', () => {\n      const id = 1;\n\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n\n    it('should handle error when deleting report review history', () => {\n      const id = 1;\n\n      service.delete(id).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,0BAA0B,QAAQ,iCAAiC;AAE5EC,QAAQ,CAAC,4BAA4B,EAAE,MAAK;EAC1C,IAAIC,OAAmC;EACvC,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,0BAA0B;EAE9D,MAAMC,uBAAuB,GAAwB;IACnDC,EAAE,EAAE,CAAC;IACLC,OAAO,EAAE,eAAe;IACxBC,UAAU,EAAE,IAAIC,IAAI,EAAE;IACtBC,eAAe,EAAE,CAAC;IAClBC,cAAc,EAAE,CAAC;IACjBC,UAAU,EAAE;GACb;EAEDC,UAAU,CAAC,MAAK;IACdf,OAAO,CAACgB,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACnB,uBAAuB,CAAC;MAClCoB,SAAS,EAAE,CAAChB,0BAA0B;KACvC,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACmB,MAAM,CAACjB,0BAA0B,CAAC;IACpDG,QAAQ,GAAGL,OAAO,CAACmB,MAAM,CAACpB,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFqB,SAAS,CAAC,MAAK;IACbf,QAAQ,CAACgB,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACnB,OAAO,CAAC,CAACoB,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFrB,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBmB,EAAE,CAAC,2CAA2C,EAAE,MAAK;MACnD,MAAMG,aAAa,GAAG,CAAClB,uBAAuB,CAAC;MAE/CH,OAAO,CAACsB,MAAM,EAAE,CAACC,SAAS,CAAEC,SAAS,IAAI;QACvCL,MAAM,CAACK,SAAS,CAAC,CAACC,OAAO,CAACJ,aAAa,CAAC;MAC1C,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAACzB,MAAM,CAAC;MACtCiB,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,aAAa,CAAC;IAC1B,CAAC,CAAC;IAEFH,EAAE,CAAC,8DAA8D,EAAE,MAAK;MACtElB,OAAO,CAACsB,MAAM,EAAE,CAACC,SAAS,CAAC;QACzBS,KAAK,EAAGA,KAAK,IAAI;UACfb,MAAM,CAACa,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAACzB,MAAM,CAAC;MACtCwB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,cAAc,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC5D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFlC,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBmB,EAAE,CAAC,6CAA6C,EAAE,MAAK;MACrD,MAAMd,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAACmC,OAAO,CAAC/B,EAAE,CAAC,CAACmB,SAAS,CAAEa,OAAO,IAAI;QACxCjB,MAAM,CAACiB,OAAO,CAAC,CAACX,OAAO,CAACtB,uBAAuB,CAAC;MAClD,CAAC,CAAC;MAEF,MAAMuB,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAAC,GAAGzB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDe,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAAC5B,uBAAuB,CAAC;IACpC,CAAC,CAAC;IAEFe,EAAE,CAAC,8DAA8D,EAAE,MAAK;MACtE,MAAMd,EAAE,GAAG,GAAG;MAEdJ,OAAO,CAACmC,OAAO,CAAC/B,EAAE,CAAC,CAACmB,SAAS,CAAC;QAC5BS,KAAK,EAAGA,KAAK,IAAI;UACfb,MAAM,CAACa,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAAC,GAAGzB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDsB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFlC,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBmB,EAAE,CAAC,2CAA2C,EAAE,MAAK;MACnD,MAAMmB,UAAU,GAAoC;QAClDhC,OAAO,EAAE,eAAe;QACxBC,UAAU,EAAE,IAAIC,IAAI,EAAE;QACtBC,eAAe,EAAE,CAAC;QAClBC,cAAc,EAAE,CAAC;QACjBC,UAAU,EAAE;OACb;MAEDV,OAAO,CAACsC,MAAM,CAACD,UAAU,CAAC,CAACd,SAAS,CAAEa,OAAO,IAAI;QAC/CjB,MAAM,CAACiB,OAAO,CAAC,CAACX,OAAO,CAACtB,uBAAuB,CAAC;MAClD,CAAC,CAAC;MAEF,MAAMuB,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAACzB,MAAM,CAAC;MACtCiB,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACW,IAAI,CAAC,CAACd,OAAO,CAACY,UAAU,CAAC;MAC5CX,GAAG,CAACK,KAAK,CAAC5B,uBAAuB,CAAC;IACpC,CAAC,CAAC;IAEFe,EAAE,CAAC,yDAAyD,EAAE,MAAK;MACjE,MAAMmB,UAAU,GAAoC;QAClDhC,OAAO,EAAE,eAAe;QACxBC,UAAU,EAAE,IAAIC,IAAI,EAAE;QACtBC,eAAe,EAAE,CAAC;QAClBC,cAAc,EAAE,CAAC;QACjBC,UAAU,EAAE;OACb;MAEDV,OAAO,CAACsC,MAAM,CAACD,UAAU,CAAC,CAACd,SAAS,CAAC;QACnCS,KAAK,EAAGA,KAAK,IAAI;UACfb,MAAM,CAACa,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAACzB,MAAM,CAAC;MACtCwB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFlC,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBmB,EAAE,CAAC,uCAAuC,EAAE,MAAK;MAC/C,MAAMd,EAAE,GAAG,CAAC;MACZ,MAAMoC,UAAU,GAAiC;QAC/CnC,OAAO,EAAE;OACV;MAEDL,OAAO,CAACyC,MAAM,CAACrC,EAAE,EAAEoC,UAAU,CAAC,CAACjB,SAAS,CAAEa,OAAO,IAAI;QACnDjB,MAAM,CAACiB,OAAO,CAAC,CAACX,OAAO,CAACtB,uBAAuB,CAAC;MAClD,CAAC,CAAC;MAEF,MAAMuB,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAAC,GAAGzB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDe,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACW,IAAI,CAAC,CAACd,OAAO,CAACe,UAAU,CAAC;MAC5Cd,GAAG,CAACK,KAAK,CAAC5B,uBAAuB,CAAC;IACpC,CAAC,CAAC;IAEFe,EAAE,CAAC,yDAAyD,EAAE,MAAK;MACjE,MAAMd,EAAE,GAAG,CAAC;MACZ,MAAMoC,UAAU,GAAiC;QAC/CnC,OAAO,EAAE;OACV;MAEDL,OAAO,CAACyC,MAAM,CAACrC,EAAE,EAAEoC,UAAU,CAAC,CAACjB,SAAS,CAAC;QACvCS,KAAK,EAAGA,KAAK,IAAI;UACfb,MAAM,CAACa,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAAC,GAAGzB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDsB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFlC,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBmB,EAAE,CAAC,uCAAuC,EAAE,MAAK;MAC/C,MAAMd,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAAC0C,MAAM,CAACtC,EAAE,CAAC,CAACmB,SAAS,CAAC,MAAK;QAChCJ,MAAM,EAAE,CAACwB,OAAO,EAAE;MACpB,CAAC,CAAC;MAEF,MAAMjB,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAAC,GAAGzB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDe,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;IAEFb,EAAE,CAAC,yDAAyD,EAAE,MAAK;MACjE,MAAMd,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAAC0C,MAAM,CAACtC,EAAE,CAAC,CAACmB,SAAS,CAAC;QAC3BS,KAAK,EAAGA,KAAK,IAAI;UACfb,MAAM,CAACa,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAAC,GAAGzB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDsB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}