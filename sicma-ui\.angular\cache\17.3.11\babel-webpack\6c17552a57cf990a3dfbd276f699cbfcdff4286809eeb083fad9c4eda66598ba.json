{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { EpsService } from './eps.service';\ndescribe('EpsService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/eps`;\n  const mockEps = {\n    id: 1,\n    name: 'Test EPS'\n  };\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [EpsService]\n    });\n    service = TestBed.inject(EpsService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all EPS', () => {\n      const mockEpsList = [mockEps];\n      service.getAll().subscribe(epsList => {\n        expect(epsList).toEqual(mockEpsList);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockEpsList);\n    });\n  });\n  describe('getById', () => {\n    it('should return an EPS by id', () => {\n      const id = 1;\n      service.getById(id).subscribe(eps => {\n        expect(eps).toEqual(mockEps);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockEps);\n    });\n  });\n  describe('create', () => {\n    it('should create a new EPS', () => {\n      const newEps = {\n        name: 'New EPS'\n      };\n      service.create(newEps).subscribe(eps => {\n        expect(eps).toEqual(mockEps);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newEps);\n      req.flush(mockEps);\n    });\n  });\n  describe('update', () => {\n    it('should update an EPS', () => {\n      const id = 1;\n      const updateData = {\n        name: 'Updated EPS'\n      };\n      service.update(id, updateData).subscribe(eps => {\n        expect(eps).toEqual(mockEps);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockEps);\n    });\n  });\n  describe('delete', () => {\n    it('should delete an EPS', () => {\n      const id = 1;\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n  describe('getByName', () => {\n    it('should return an EPS by name', () => {\n      const name = 'Test EPS';\n      service.getByName(name).subscribe(eps => {\n        expect(eps).toEqual(mockEps);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockEps);\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "EpsService", "describe", "service", "httpMock", "apiUrl", "mockEps", "id", "name", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockEpsList", "getAll", "subscribe", "epsList", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "getById", "eps", "newEps", "create", "body", "updateData", "update", "delete", "nothing", "getByName"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-management\\services\\eps.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { Eps } from '@contractor-management/models/eps.model';\nimport { environment } from '@env';\nimport { EpsService } from './eps.service';\n\ndescribe('EpsService', () => {\n  let service: EpsService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/eps`;\n\n  const mockEps: Eps = {\n    id: 1,\n    name: 'Test EPS',\n  };\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [EpsService],\n    });\n    service = TestBed.inject(EpsService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all EPS', () => {\n      const mockEpsList = [mockEps];\n\n      service.getAll().subscribe((epsList) => {\n        expect(epsList).toEqual(mockEpsList);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockEpsList);\n    });\n  });\n\n  describe('getById', () => {\n    it('should return an EPS by id', () => {\n      const id = 1;\n\n      service.getById(id).subscribe((eps) => {\n        expect(eps).toEqual(mockEps);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockEps);\n    });\n  });\n\n  describe('create', () => {\n    it('should create a new EPS', () => {\n      const newEps: Omit<Eps, 'id'> = {\n        name: 'New EPS',\n      };\n\n      service.create(newEps).subscribe((eps) => {\n        expect(eps).toEqual(mockEps);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newEps);\n      req.flush(mockEps);\n    });\n  });\n\n  describe('update', () => {\n    it('should update an EPS', () => {\n      const id = 1;\n      const updateData: Partial<Eps> = {\n        name: 'Updated EPS',\n      };\n\n      service.update(id, updateData).subscribe((eps) => {\n        expect(eps).toEqual(mockEps);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockEps);\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete an EPS', () => {\n      const id = 1;\n\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n\n  describe('getByName', () => {\n    it('should return an EPS by name', () => {\n      const name = 'Test EPS';\n\n      service.getByName(name).subscribe((eps) => {\n        expect(eps).toEqual(mockEps);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockEps);\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,UAAU,QAAQ,eAAe;AAE1CC,QAAQ,CAAC,YAAY,EAAE,MAAK;EAC1B,IAAIC,OAAmB;EACvB,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,MAAM;EAE1C,MAAMC,OAAO,GAAQ;IACnBC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE;GACP;EAEDC,UAAU,CAAC,MAAK;IACdV,OAAO,CAACW,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACd,uBAAuB,CAAC;MAClCe,SAAS,EAAE,CAACX,UAAU;KACvB,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACc,MAAM,CAACZ,UAAU,CAAC;IACpCG,QAAQ,GAAGL,OAAO,CAACc,MAAM,CAACf,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFgB,SAAS,CAAC,MAAK;IACbV,QAAQ,CAACW,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACd,OAAO,CAAC,CAACe,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFhB,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,uBAAuB,EAAE,MAAK;MAC/B,MAAMG,WAAW,GAAG,CAACb,OAAO,CAAC;MAE7BH,OAAO,CAACiB,MAAM,EAAE,CAACC,SAAS,CAAEC,OAAO,IAAI;QACrCL,MAAM,CAACK,OAAO,CAAC,CAACC,OAAO,CAACJ,WAAW,CAAC;MACtC,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,WAAW,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjB,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBc,EAAE,CAAC,4BAA4B,EAAE,MAAK;MACpC,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAAC2B,OAAO,CAACvB,EAAE,CAAC,CAACc,SAAS,CAAEU,GAAG,IAAI;QACpCd,MAAM,CAACc,GAAG,CAAC,CAACR,OAAO,CAACjB,OAAO,CAAC;MAC9B,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvB,OAAO,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,yBAAyB,EAAE,MAAK;MACjC,MAAMgB,MAAM,GAAoB;QAC9BxB,IAAI,EAAE;OACP;MAEDL,OAAO,CAAC8B,MAAM,CAACD,MAAM,CAAC,CAACX,SAAS,CAAEU,GAAG,IAAI;QACvCd,MAAM,CAACc,GAAG,CAAC,CAACR,OAAO,CAACjB,OAAO,CAAC;MAC9B,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACS,MAAM,CAAC;MACxCR,GAAG,CAACK,KAAK,CAACvB,OAAO,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,sBAAsB,EAAE,MAAK;MAC9B,MAAMT,EAAE,GAAG,CAAC;MACZ,MAAM4B,UAAU,GAAiB;QAC/B3B,IAAI,EAAE;OACP;MAEDL,OAAO,CAACiC,MAAM,CAAC7B,EAAE,EAAE4B,UAAU,CAAC,CAACd,SAAS,CAAEU,GAAG,IAAI;QAC/Cd,MAAM,CAACc,GAAG,CAAC,CAACR,OAAO,CAACjB,OAAO,CAAC;MAC9B,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACY,UAAU,CAAC;MAC5CX,GAAG,CAACK,KAAK,CAACvB,OAAO,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,sBAAsB,EAAE,MAAK;MAC9B,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAACkC,MAAM,CAAC9B,EAAE,CAAC,CAACc,SAAS,CAAC,MAAK;QAChCJ,MAAM,EAAE,CAACqB,OAAO,EAAE;MACpB,CAAC,CAAC;MAEF,MAAMd,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF3B,QAAQ,CAAC,WAAW,EAAE,MAAK;IACzBc,EAAE,CAAC,8BAA8B,EAAE,MAAK;MACtC,MAAMR,IAAI,GAAG,UAAU;MAEvBL,OAAO,CAACoC,SAAS,CAAC/B,IAAI,CAAC,CAACa,SAAS,CAAEU,GAAG,IAAI;QACxCd,MAAM,CAACc,GAAG,CAAC,CAACR,OAAO,CAACjB,OAAO,CAAC;MAC9B,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,SAASG,IAAI,EAAE,CAAC;MACxDS,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvB,OAAO,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}