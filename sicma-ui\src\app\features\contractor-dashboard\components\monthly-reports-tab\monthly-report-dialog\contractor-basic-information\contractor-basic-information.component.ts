import { Contractor } from '@contractor-management/models/contractor.model';
import { ContractorService } from '@contractor-management/services/contractor.service';
import { AlertService } from '@shared/services/alert.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { finalize, firstValue<PERSON>rom, forkJoin } from 'rxjs';

import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';

import { MatOption } from '@angular/material/core';
import {
  MatDatepicker,
  MatDatepickerInput,
  MatDatepickerToggle,
} from '@angular/material/datepicker';
import {
  MatError,
  MatFormField,
  MatHint,
  MatLabel,
  MatSuffix,
} from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { MatSelect } from '@angular/material/select';
import { EducationLevel } from '@contractor-management/models/education-level.model';
import { Eps } from '@contractor-management/models/eps.model';
import { Gender } from '@contractor-management/models/gender.model';
import { Profession } from '@contractor-management/models/profession.model';
import { EducationLevelService } from '@contractor-management/services/education-level.service';
import { EpsService } from '@contractor-management/services/eps.service';
import { GenderService } from '@contractor-management/services/gender.service';
import { ProfessionService } from '@contractor-management/services/profession.service';
import { Department } from '@shared/models/department.model';
import { Municipality } from '@shared/models/municipality.model';
import { DepartmentService } from '@shared/services/department.service';
import { MunicipalityService } from '@shared/services/municipality.service';

@Component({
  selector: 'app-contractor-basic-information',
  templateUrl: './contractor-basic-information.component.html',
  styleUrl: './contractor-basic-information.component.scss',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatFormField,
    MatLabel,
    MatInput,
    MatError,
    MatSelect,
    MatOption,
    MatDatepickerInput,
    MatHint,
    MatDatepickerToggle,
    MatSuffix,
    MatDatepicker,
  ],
})
export class ContractorBasicInformationComponent implements OnInit, OnChanges {
  @Input() contractor: Contractor | undefined;
  @Input() isSupervisor = false;
  @Output() formValidityChange = new EventEmitter<boolean>();

  contractorForm: FormGroup = this.fb.group({
    fullName: [''],
    idTypeName: [''],
    idNumber: [''],
    personalEmail: ['', [Validators.required, Validators.email]],
    corporateEmail: ['', [Validators.email]],
    phone: ['', [Validators.required]],
    epsId: ['', Validators.required],
    birthDate: [null, Validators.required],
    genderId: ['', Validators.required],
    educationLevelId: ['', Validators.required],
    professionId: ['', Validators.required],
    lastObtainedDegree: [''],
    departmentId: ['', Validators.required],
    municipalityId: ['', Validators.required],
  });

  epss: Eps[] = [];
  genders: Gender[] = [];
  educationLevels: EducationLevel[] = [];
  professions: Profession[] = [];
  departments: Department[] = [];
  municipalities: Municipality[] = [];

  isCCIDType = false;
  isLastObtainedDegreeEnabled = false;
  isProfessionEditable = true;
  today: Date = new Date();

  private readonly EDUCATION_LEVELS_WITH_DEGREE = [
    'BACHILLER',
    'TÉCNICO',
    'TECNÓLOGO',
  ];

  private readonly ADVANCED_EDUCATION_LEVELS = [
    'ESPECIALIZACIÓN',
    'MAESTRIA',
    'DOCTORADO',
    'POST DOCTORADO',
  ];

  constructor(
    private fb: FormBuilder,
    private spinner: NgxSpinnerService,
    private contractorService: ContractorService,
    private epsService: EpsService,
    private genderService: GenderService,
    private educationLevelService: EducationLevelService,
    private professionService: ProfessionService,
    private departmentService: DepartmentService,
    private municipalityService: MunicipalityService,
    private alert: AlertService,
  ) {}

  ngOnInit(): void {
    if (this.contractor) {
      this.loadFormData();
    }
    this.setupFormListeners();
    this.contractorForm.statusChanges.subscribe(() => {
      this.formValidityChange.emit(this.contractorForm.valid);
    });
  }

  setupFormListeners(): void {
    this.contractorForm
      .get('educationLevelId')
      ?.valueChanges.subscribe((value: number | null) => {
        const educationLevel = this.educationLevels.find(
          (el) => el.id === value,
        );
        this.onEducationLevelChange(educationLevel || null);
      });

    this.contractorForm
      .get('departmentId')
      ?.valueChanges.subscribe((value: number | null) => {
        const department = this.departments.find((d) => d.id === value);
        this.updateMunicipalitiesList(department || null);
      });

    this.contractorForm
      .get('idTypeName')
      ?.valueChanges.subscribe((value: string | null) => {
        this.updateIDTypeFields(value);
      });
  }

  updateMunicipalitiesList(department: Department | null): void {
    if (department) {
      this.municipalityService.getAllByDepartmentId(department.id).subscribe({
        next: (municipalities) => {
          this.municipalities = municipalities;
          this.contractorForm.get('municipalityId')?.enable();
        },
        error: (error) => {
          this.alert.error(error.error?.detail ?? 'Error al cargar municipios');
        },
      });
    } else {
      this.municipalities = [];
      this.contractorForm.get('municipalityId')?.disable();
    }
  }

  updateIDTypeFields(idTypeName: string | null): void {
    this.isCCIDType = idTypeName === 'CC';
  }

  onEducationLevelChange(educationLevel: EducationLevel | null): void {
    if (educationLevel) {
      const educationLevelName = educationLevel.name.toUpperCase();
      const isBachillerTecnicoTecnologo =
        this.EDUCATION_LEVELS_WITH_DEGREE.includes(educationLevelName);
      const isAdvancedEducationLevel =
        this.ADVANCED_EDUCATION_LEVELS.includes(educationLevelName);
      const lastObtainedDegreeControl =
        this.contractorForm.get('lastObtainedDegree');
      const professionControl = this.contractorForm.get('professionId');

      if (isBachillerTecnicoTecnologo) {
        this.isProfessionEditable = false;
        professionControl?.disable();

        if (educationLevelName === 'BACHILLER') {
          const bachillerProfession = this.professions.find(
            (p) => p.name.toUpperCase() === 'BACHILLER',
          );
          professionControl?.setValue(bachillerProfession?.id || null);
          lastObtainedDegreeControl?.disable();
          lastObtainedDegreeControl?.clearValidators();
          lastObtainedDegreeControl?.setValue(null);
          this.isLastObtainedDegreeEnabled = false;
        } else {
          lastObtainedDegreeControl?.enable();
          lastObtainedDegreeControl?.setValidators([Validators.required]);
          this.isLastObtainedDegreeEnabled = true;
        }
      } else if (isAdvancedEducationLevel) {
        this.isProfessionEditable = true;
        professionControl?.enable();
        professionControl?.setValidators([Validators.required]);

        lastObtainedDegreeControl?.enable();
        lastObtainedDegreeControl?.setValidators([Validators.required]);
        this.isLastObtainedDegreeEnabled = true;
      } else {
        this.isProfessionEditable = true;
        professionControl?.enable();
        professionControl?.setValidators([Validators.required]);

        lastObtainedDegreeControl?.disable();
        lastObtainedDegreeControl?.clearValidators();
        lastObtainedDegreeControl?.setValue(null);
        this.isLastObtainedDegreeEnabled = false;
      }

      lastObtainedDegreeControl?.updateValueAndValidity();
      professionControl?.updateValueAndValidity();
    } else {
      this.isProfessionEditable = true;
      const lastObtainedDegreeControl =
        this.contractorForm.get('lastObtainedDegree');
      const professionControl = this.contractorForm.get('professionId');

      lastObtainedDegreeControl?.disable();
      lastObtainedDegreeControl?.clearValidators();
      lastObtainedDegreeControl?.setValue(null);
      this.isLastObtainedDegreeEnabled = false;

      professionControl?.enable();
      professionControl?.setValidators([Validators.required]);
      if (!professionControl?.value) {
        professionControl?.setValue(null);
      }

      lastObtainedDegreeControl?.updateValueAndValidity();
      professionControl?.updateValueAndValidity();
    }
  }

  private loadFormData(): void {
    this.spinner.show();
    forkJoin({
      epss: this.epsService.getAll(),
      genders: this.genderService.getAll(),
      educationLevels: this.educationLevelService.getAll(),
      professions: this.professionService.getAll(),
      departments: this.departmentService.getAll(),
    })
      .pipe(finalize(() => this.spinner.hide()))
      .subscribe({
        next: ({
          epss,
          genders,
          educationLevels,
          professions,
          departments,
        }) => {
          this.epss = epss;
          this.genders = genders;
          this.educationLevels = educationLevels;
          this.professions = professions;
          this.departments = departments;
          if (this.contractor) {
            this.patchFormWithContractor(this.contractor);
          }
        },
        error: (error) => {
          this.alert.error(error.error?.detail ?? 'Error al cargar los datos del formulario');
        },
      });
  }

  private patchFormWithContractor(contractor: Contractor): void {
    this.contractorForm.patchValue({
      fullName: contractor.fullName || '',
      idTypeName: contractor.idType?.name || '',
      idNumber: contractor.idNumber?.toString() || '',
      personalEmail: contractor.personalEmail || '',
      corporateEmail: contractor.corporateEmail || '',
      phone: contractor.phone?.toString() || '',
      epsId: contractor.eps?.id || '',
      birthDate: contractor.birthDate
        ? new Date(contractor.birthDate + 'T00:00:00')
        : null,
      genderId: contractor.gender?.id || '',
      educationLevelId: contractor.educationLevel?.id || '',
      professionId: contractor.profession?.id || '',
      lastObtainedDegree: contractor.lastObtainedDegree || '',
      departmentId: contractor.department?.id || '',
      municipalityId: contractor.municipality?.id || '',
    });

    this.contractorForm.get('professionId')?.enable();

    this.updateMunicipalitiesList(contractor.department || null);
    this.onEducationLevelChange(contractor.educationLevel || null);
    this.updateIDTypeFields(contractor.idType?.name || null);

    if (contractor.profession) {
      this.contractorForm.patchValue({
        professionId: contractor.profession.id,
      });
      if (!this.isProfessionEditable) {
        this.contractorForm.get('professionId')?.disable();
      }
    }
  }

  isValid(): boolean {
    return this.contractorForm.valid;
  }

  getFormValue(): Partial<Contractor> {
    return {
      ...this.contractorForm.value,
      birthDate: this.contractorForm.get('birthDate')?.value
        ? new Date(
            this.contractorForm.get('birthDate')?.value.getTime() -
              this.contractorForm.get('birthDate')?.value.getTimezoneOffset() *
                60000,
          )
            .toISOString()
            .slice(0, 10)
        : null,
    } as Partial<Contractor>;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['contractor'] && !changes['contractor'].firstChange) {
      this.loadFormData();
    }
  }

  async updateContractor(): Promise<boolean> {
    if (this.contractorForm.valid && this.contractor) {
      this.spinner.show();
      const updatedContractor = this.getFormValue();
      try {
        await firstValueFrom(
          this.contractorService.update(this.contractor.id, updatedContractor),
        );
        this.alert.success('Los datos han sido actualizados correctamente.');
        return true;
      } catch {
        this.alert.error('Error al actualizar los datos del contratista');
        return false;
      } finally {
        this.spinner.hide();
      }
    } else {
      this.alert.warning(
        'Por favor, complete todos los campos requeridos antes de actualizar.',
      );
      return false;
    }
  }

  getEpsName(id: number): string {
    return this.epss.find((eps) => eps.id === id)?.name || '';
  }

  getGenderName(id: number): string {
    return this.genders.find((gender) => gender.id === id)?.name || '';
  }

  getEducationLevelName(id: number): string {
    return this.educationLevels.find((level) => level.id === id)?.name || '';
  }

  getProfessionName(id: number): string {
    return (
      this.professions.find((profession) => profession.id === id)?.name || ''
    );
  }

  getDepartmentName(id: number): string {
    return (
      this.departments.find((department) => department.id === id)?.name || ''
    );
  }

  getMunicipalityName(id: number): string {
    return (
      this.municipalities.find((municipality) => municipality.id === id)
        ?.name || ''
    );
  }
}
