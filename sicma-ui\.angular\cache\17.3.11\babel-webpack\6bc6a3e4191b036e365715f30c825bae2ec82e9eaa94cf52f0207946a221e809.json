{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { GroupService } from './group.service';\ndescribe('GroupService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/groups`;\n  const mockGroup = {\n    id: 1,\n    name: 'Test Group',\n    dependencyId: 1,\n    dependency: {\n      id: 1,\n      name: 'Test Dependency'\n    }\n  };\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [GroupService]\n    });\n    service = TestBed.inject(GroupService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all groups', () => {\n      const mockGroups = [mockGroup];\n      service.getAll().subscribe(groups => {\n        expect(groups).toEqual(mockGroups);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockGroups);\n    });\n  });\n  describe('getById', () => {\n    it('should return a group by id', () => {\n      const id = 1;\n      service.getById(id).subscribe(group => {\n        expect(group).toEqual(mockGroup);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockGroup);\n    });\n  });\n  describe('create', () => {\n    it('should create a new group', () => {\n      const newGroup = {\n        name: 'New Group',\n        dependencyId: 1,\n        dependency: {\n          id: 1,\n          name: 'Test Dependency'\n        }\n      };\n      service.create(newGroup).subscribe(group => {\n        expect(group).toEqual(mockGroup);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newGroup);\n      req.flush(mockGroup);\n    });\n  });\n  describe('update', () => {\n    it('should update a group', () => {\n      const id = 1;\n      const updateData = {\n        name: 'Updated Group'\n      };\n      service.update(id, updateData).subscribe(group => {\n        expect(group).toEqual(mockGroup);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockGroup);\n    });\n  });\n  describe('delete', () => {\n    it('should delete a group', () => {\n      const id = 1;\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n  describe('getAllByDependencyId', () => {\n    it('should return all groups by dependency id', () => {\n      const dependencyId = 1;\n      const mockGroups = [mockGroup];\n      service.getAllByDependencyId(dependencyId).subscribe(groups => {\n        expect(groups).toEqual(mockGroups);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/dependency/${dependencyId}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockGroups);\n    });\n  });\n  describe('getByName', () => {\n    it('should return a group by name', () => {\n      const name = 'Test Group';\n      service.getByName(name).subscribe(group => {\n        expect(group).toEqual(mockGroup);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockGroup);\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "GroupService", "describe", "service", "httpMock", "apiUrl", "mockGroup", "id", "name", "dependencyId", "dependency", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockGroups", "getAll", "subscribe", "groups", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "getById", "group", "newGroup", "create", "body", "updateData", "update", "delete", "nothing", "getAllByDependencyId", "getByName"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\services\\group.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { Group } from '@contract-management/models/group.model';\nimport { environment } from '@env';\nimport { GroupService } from './group.service';\n\ndescribe('GroupService', () => {\n  let service: GroupService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/groups`;\n\n  const mockGroup: Group = {\n    id: 1,\n    name: 'Test Group',\n    dependencyId: 1,\n    dependency: {\n      id: 1,\n      name: 'Test Dependency',\n    },\n  };\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [GroupService],\n    });\n    service = TestBed.inject(GroupService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all groups', () => {\n      const mockGroups = [mockGroup];\n\n      service.getAll().subscribe((groups) => {\n        expect(groups).toEqual(mockGroups);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockGroups);\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a group by id', () => {\n      const id = 1;\n\n      service.getById(id).subscribe((group) => {\n        expect(group).toEqual(mockGroup);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockGroup);\n    });\n  });\n\n  describe('create', () => {\n    it('should create a new group', () => {\n      const newGroup: Omit<Group, 'id'> = {\n        name: 'New Group',\n        dependencyId: 1,\n        dependency: {\n          id: 1,\n          name: 'Test Dependency',\n        },\n      };\n\n      service.create(newGroup).subscribe((group) => {\n        expect(group).toEqual(mockGroup);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newGroup);\n      req.flush(mockGroup);\n    });\n  });\n\n  describe('update', () => {\n    it('should update a group', () => {\n      const id = 1;\n      const updateData: Partial<Group> = {\n        name: 'Updated Group',\n      };\n\n      service.update(id, updateData).subscribe((group) => {\n        expect(group).toEqual(mockGroup);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockGroup);\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete a group', () => {\n      const id = 1;\n\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n\n  describe('getAllByDependencyId', () => {\n    it('should return all groups by dependency id', () => {\n      const dependencyId = 1;\n      const mockGroups = [mockGroup];\n\n      service.getAllByDependencyId(dependencyId).subscribe((groups) => {\n        expect(groups).toEqual(mockGroups);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/dependency/${dependencyId}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockGroups);\n    });\n  });\n\n  describe('getByName', () => {\n    it('should return a group by name', () => {\n      const name = 'Test Group';\n\n      service.getByName(name).subscribe((group) => {\n        expect(group).toEqual(mockGroup);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockGroup);\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,YAAY,QAAQ,iBAAiB;AAE9CC,QAAQ,CAAC,cAAc,EAAE,MAAK;EAC5B,IAAIC,OAAqB;EACzB,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,SAAS;EAE7C,MAAMC,SAAS,GAAU;IACvBC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,YAAY,EAAE,CAAC;IACfC,UAAU,EAAE;MACVH,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE;;GAET;EAEDG,UAAU,CAAC,MAAK;IACdZ,OAAO,CAACa,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAAChB,uBAAuB,CAAC;MAClCiB,SAAS,EAAE,CAACb,YAAY;KACzB,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACgB,MAAM,CAACd,YAAY,CAAC;IACtCG,QAAQ,GAAGL,OAAO,CAACgB,MAAM,CAACjB,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFkB,SAAS,CAAC,MAAK;IACbZ,QAAQ,CAACa,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAAChB,OAAO,CAAC,CAACiB,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFlB,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBgB,EAAE,CAAC,0BAA0B,EAAE,MAAK;MAClC,MAAMG,UAAU,GAAG,CAACf,SAAS,CAAC;MAE9BH,OAAO,CAACmB,MAAM,EAAE,CAACC,SAAS,CAAEC,MAAM,IAAI;QACpCL,MAAM,CAACK,MAAM,CAAC,CAACC,OAAO,CAACJ,UAAU,CAAC;MACpC,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGtB,QAAQ,CAACuB,SAAS,CAACtB,MAAM,CAAC;MACtCc,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,UAAU,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFnB,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBgB,EAAE,CAAC,6BAA6B,EAAE,MAAK;MACrC,MAAMX,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAAC6B,OAAO,CAACzB,EAAE,CAAC,CAACgB,SAAS,CAAEU,KAAK,IAAI;QACtCd,MAAM,CAACc,KAAK,CAAC,CAACR,OAAO,CAACnB,SAAS,CAAC;MAClC,CAAC,CAAC;MAEF,MAAMoB,GAAG,GAAGtB,QAAQ,CAACuB,SAAS,CAAC,GAAGtB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACzB,SAAS,CAAC;IACtB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBgB,EAAE,CAAC,2BAA2B,EAAE,MAAK;MACnC,MAAMgB,QAAQ,GAAsB;QAClC1B,IAAI,EAAE,WAAW;QACjBC,YAAY,EAAE,CAAC;QACfC,UAAU,EAAE;UACVH,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE;;OAET;MAEDL,OAAO,CAACgC,MAAM,CAACD,QAAQ,CAAC,CAACX,SAAS,CAAEU,KAAK,IAAI;QAC3Cd,MAAM,CAACc,KAAK,CAAC,CAACR,OAAO,CAACnB,SAAS,CAAC;MAClC,CAAC,CAAC;MAEF,MAAMoB,GAAG,GAAGtB,QAAQ,CAACuB,SAAS,CAACtB,MAAM,CAAC;MACtCc,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACS,QAAQ,CAAC;MAC1CR,GAAG,CAACK,KAAK,CAACzB,SAAS,CAAC;IACtB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBgB,EAAE,CAAC,uBAAuB,EAAE,MAAK;MAC/B,MAAMX,EAAE,GAAG,CAAC;MACZ,MAAM8B,UAAU,GAAmB;QACjC7B,IAAI,EAAE;OACP;MAEDL,OAAO,CAACmC,MAAM,CAAC/B,EAAE,EAAE8B,UAAU,CAAC,CAACd,SAAS,CAAEU,KAAK,IAAI;QACjDd,MAAM,CAACc,KAAK,CAAC,CAACR,OAAO,CAACnB,SAAS,CAAC;MAClC,CAAC,CAAC;MAEF,MAAMoB,GAAG,GAAGtB,QAAQ,CAACuB,SAAS,CAAC,GAAGtB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACY,UAAU,CAAC;MAC5CX,GAAG,CAACK,KAAK,CAACzB,SAAS,CAAC;IACtB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBgB,EAAE,CAAC,uBAAuB,EAAE,MAAK;MAC/B,MAAMX,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAACoC,MAAM,CAAChC,EAAE,CAAC,CAACgB,SAAS,CAAC,MAAK;QAChCJ,MAAM,EAAE,CAACqB,OAAO,EAAE;MACpB,CAAC,CAAC;MAEF,MAAMd,GAAG,GAAGtB,QAAQ,CAACuB,SAAS,CAAC,GAAGtB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,sBAAsB,EAAE,MAAK;IACpCgB,EAAE,CAAC,2CAA2C,EAAE,MAAK;MACnD,MAAMT,YAAY,GAAG,CAAC;MACtB,MAAMY,UAAU,GAAG,CAACf,SAAS,CAAC;MAE9BH,OAAO,CAACsC,oBAAoB,CAAChC,YAAY,CAAC,CAACc,SAAS,CAAEC,MAAM,IAAI;QAC9DL,MAAM,CAACK,MAAM,CAAC,CAACC,OAAO,CAACJ,UAAU,CAAC;MACpC,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGtB,QAAQ,CAACuB,SAAS,CAAC,GAAGtB,MAAM,eAAeI,YAAY,EAAE,CAAC;MACtEU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,UAAU,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFnB,QAAQ,CAAC,WAAW,EAAE,MAAK;IACzBgB,EAAE,CAAC,+BAA+B,EAAE,MAAK;MACvC,MAAMV,IAAI,GAAG,YAAY;MAEzBL,OAAO,CAACuC,SAAS,CAAClC,IAAI,CAAC,CAACe,SAAS,CAAEU,KAAK,IAAI;QAC1Cd,MAAM,CAACc,KAAK,CAAC,CAACR,OAAO,CAACnB,SAAS,CAAC;MAClC,CAAC,CAAC;MAEF,MAAMoB,GAAG,GAAGtB,QAAQ,CAACuB,SAAS,CAAC,GAAGtB,MAAM,SAASG,IAAI,EAAE,CAAC;MACxDW,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACzB,SAAS,CAAC;IACtB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}