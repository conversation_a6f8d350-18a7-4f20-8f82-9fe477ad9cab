import { FormBuilder, FormGroup } from '@angular/forms';
import { createDateValidator, DateValidatorConfig } from './date.validator';

describe('DateValidator', () => {
  let fb: FormBuilder;
  let form: FormGroup;
  let config: DateValidatorConfig;

  beforeEach(() => {
    fb = new FormBuilder();
    form = fb.group({
      startDate: [null],
      endDate: [null],
    });

    config = {
      lastSuspensionEndDate: null,
      contractStartDate: null,
      contractEndDate: null,
      suspensionForm: form,
      isEdit: false,
    };
  });

  it('should return required error when value is null', () => {
    const validator = createDateValidator(config);
    const result = validator(form.get('startDate')!);
    expect(result).toEqual({ required: true });
  });

  describe('Start Date Validation', () => {
    it('should return startDateBeforeLastSuspension error when start date is before last suspension', () => {
      config.lastSuspensionEndDate = new Date('2024-06-01');
      const validator = createDateValidator(config);
      form.get('startDate')?.setValue(new Date('2024-05-01'));
      const result = validator(form.get('startDate')!);
      expect(result).toEqual({ startDateBeforeLastSuspension: true });
    });

    it('should not return startDateBeforeLastSuspension error in edit mode', () => {
      config.lastSuspensionEndDate = new Date('2024-06-01');
      config.isEdit = true;
      const validator = createDateValidator(config);
      form.get('startDate')?.setValue(new Date('2024-05-01'));
      const result = validator(form.get('startDate')!);
      expect(result).not.toEqual({ startDateBeforeLastSuspension: true });
    });

    it('should return startDateBeforeContractStart error when start date is before contract start', () => {
      config.contractStartDate = new Date('2024-06-01');
      const validator = createDateValidator(config);
      form.get('startDate')?.setValue(new Date('2024-05-01'));
      const result = validator(form.get('startDate')!);
      expect(result).toEqual({ startDateBeforeContractStart: true });
    });

    it('should return null when start date is valid', () => {
      config.contractStartDate = new Date('2024-06-01');
      config.lastSuspensionEndDate = new Date('2024-06-15');
      const validator = createDateValidator(config);
      form.get('startDate')?.setValue(new Date('2024-07-01'));
      const result = validator(form.get('startDate')!);
      expect(result).toBeNull();
    });
  });

  describe('End Date Validation', () => {
    it('should return endDateBeforeStartDate error when end date is before start date', () => {
      const validator = createDateValidator(config);
      form.get('startDate')?.setValue(new Date('2024-06-01'));
      form.get('endDate')?.setValue(new Date('2024-05-01'));
      const result = validator(form.get('endDate')!);
      expect(result).toEqual({ endDateBeforeStartDate: true });
    });

    it('should return endDateAfterContractEnd error when end date is after contract end', () => {
      config.contractEndDate = new Date('2024-12-31');
      const validator = createDateValidator(config);
      form.get('endDate')?.setValue(new Date('2025-01-02'));
      const result = validator(form.get('endDate')!);
      expect(result).toEqual({ endDateAfterContractEnd: true });
    });

    it('should return null when end date is valid', () => {
      config.contractEndDate = new Date('2024-12-31');
      const validator = createDateValidator(config);
      form.get('startDate')?.setValue(new Date('2024-06-01'));
      form.get('endDate')?.setValue(new Date('2024-07-01'));
      const result = validator(form.get('endDate')!);
      expect(result).toBeNull();
    });
  });

  describe('Edge Cases', () => {
    it('should handle undefined contract dates', () => {
      config.contractStartDate = undefined;
      config.contractEndDate = undefined;
      const validator = createDateValidator(config);
      form.get('startDate')?.setValue(new Date('2024-06-01'));
      form.get('endDate')?.setValue(new Date('2024-07-01'));
      expect(validator(form.get('startDate')!)).toBeNull();
      expect(validator(form.get('endDate')!)).toBeNull();
    });

    it('should handle null contract dates', () => {
      config.contractStartDate = null;
      config.contractEndDate = null;
      const validator = createDateValidator(config);
      form.get('startDate')?.setValue(new Date('2024-06-01'));
      form.get('endDate')?.setValue(new Date('2024-07-01'));
      expect(validator(form.get('startDate')!)).toBeNull();
      expect(validator(form.get('endDate')!)).toBeNull();
    });
  });
});