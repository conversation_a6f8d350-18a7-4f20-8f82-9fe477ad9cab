import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { environment } from '@env';
import { ContractAuditHistory } from '../models/contract-audit-history.model';
import { ContractAuditHistoryService } from './contract-audit-history.service';

describe('ContractAuditHistoryService', () => {
  let service: ContractAuditHistoryService;
  let httpTestingController: HttpTestingController;
  const API_URL = `${environment.apiUrl}/contract-audit-histories`;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ContractAuditHistoryService],
    });
    service = TestBed.inject(ContractAuditHistoryService);
    httpTestingController = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all contract audit histories', () => {
      const mockAuditHistories: ContractAuditHistory[] = [
        {
          id: 1,
          contractId: 101,
          auditDate: new Date(),
          auditorId: 1,
          comment: 'Initial audit',
          auditStatusId: 1,
        },
        {
          id: 2,
          contractId: 102,
          auditDate: new Date(),
          auditorId: 2,
          comment: 'Follow-up audit',
          auditStatusId: 2,
        },
      ];

      service.getAll().subscribe((histories) => {
        expect(histories).toEqual(mockAuditHistories);
      });

      const req = httpTestingController.expectOne(API_URL);
      expect(req.request.method).toBe('GET');
      req.flush(mockAuditHistories);
    });
  });

  describe('getById', () => {
    it('should return a single contract audit history by id', () => {
      const mockAuditHistory: ContractAuditHistory = {
        id: 1,
        contractId: 101,
        auditDate: new Date(),
        auditorId: 1,
        comment: 'Initial audit',
        auditStatusId: 1,
      };

      service.getById(1).subscribe((history) => {
        expect(history).toEqual(mockAuditHistory);
      });

      const req = httpTestingController.expectOne(`${API_URL}/1`);
      expect(req.request.method).toBe('GET');
      req.flush(mockAuditHistory);
    });
  });

  describe('getByContractId', () => {
    it('should return contract audit histories by contract id', () => {
      const contractId = 101;
      const mockAuditHistories: ContractAuditHistory[] = [
        {
          id: 1,
          contractId: contractId,
          auditDate: new Date(),
          auditorId: 1,
          comment: 'Initial audit',
          auditStatusId: 1,
        },
        {
          id: 3,
          contractId: contractId,
          auditDate: new Date(),
          auditorId: 2,
          comment: 'Follow-up audit',
          auditStatusId: 2,
        },
      ];

      service.getByContractId(contractId).subscribe((histories) => {
        expect(histories).toEqual(mockAuditHistories);
      });

      const req = httpTestingController.expectOne(
        `${API_URL}/contract/${contractId}`,
      );
      expect(req.request.method).toBe('GET');
      req.flush(mockAuditHistories);
    });
  });

  describe('create', () => {
    it('should create a new contract audit history', () => {
      const newAuditHistory: Omit<ContractAuditHistory, 'id'> = {
        contractId: 101,
        auditDate: new Date(),
        auditorId: 1,
        comment: 'Initial audit',
        auditStatusId: 1,
      };

      const mockResponse: ContractAuditHistory = {
        id: 1,
        ...newAuditHistory,
      };

      service.create(newAuditHistory).subscribe((history) => {
        expect(history).toEqual(mockResponse);
      });

      const req = httpTestingController.expectOne(API_URL);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newAuditHistory);
      req.flush(mockResponse);
    });
  });

  describe('update', () => {
    it('should update an existing contract audit history', () => {
      const id = 1;
      const updateAuditHistory: Partial<ContractAuditHistory> = {
        comment: 'Updated audit comment',
        auditStatusId: 2,
      };

      const mockResponse: ContractAuditHistory = {
        id: id,
        contractId: 101,
        auditDate: new Date(),
        auditorId: 1,
        comment: 'Updated audit comment',
        auditStatusId: 2,
      };

      service.update(id, updateAuditHistory).subscribe((history) => {
        expect(history).toEqual(mockResponse);
      });

      const req = httpTestingController.expectOne(`${API_URL}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateAuditHistory);
      req.flush(mockResponse);
    });
  });

  describe('delete', () => {
    it('should delete a contract audit history', () => {
      const id = 1;

      service.delete(id).subscribe((response) => {
        expect(response).toBeNull();
      });

      const req = httpTestingController.expectOne(`${API_URL}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });
});