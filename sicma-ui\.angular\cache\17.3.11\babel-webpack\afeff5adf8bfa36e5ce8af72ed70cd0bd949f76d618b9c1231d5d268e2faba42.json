{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { SupervisorService } from './supervisor.service';\ndescribe('SupervisorService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/supervisors`;\n  const mockSupervisor = {\n    id: 1,\n    fullName: '<PERSON>',\n    idNumber: 123456789,\n    position: 'Manager',\n    email: '<EMAIL>',\n    idType: {\n      id: 1,\n      name: 'CC'\n    }\n  };\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [SupervisorService]\n    });\n    service = TestBed.inject(SupervisorService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  it('should get all supervisors', () => {\n    const mockSupervisors = [mockSupervisor];\n    service.getAll().subscribe(supervisors => {\n      expect(supervisors).toEqual(mockSupervisors);\n    });\n    const req = httpMock.expectOne(apiUrl);\n    expect(req.request.method).toBe('GET');\n    req.flush(mockSupervisors);\n  });\n  it('should get supervisor by id', () => {\n    const id = 1;\n    service.getById(id).subscribe(supervisor => {\n      expect(supervisor).toEqual(mockSupervisor);\n    });\n    const req = httpMock.expectOne(`${apiUrl}/${id}`);\n    expect(req.request.method).toBe('GET');\n    req.flush(mockSupervisor);\n  });\n  it('should get supervisor by id number', () => {\n    const idNumber = 123456789;\n    service.getByIdNumber(idNumber).subscribe(supervisor => {\n      expect(supervisor).toEqual(mockSupervisor);\n    });\n    const req = httpMock.expectOne(`${apiUrl}/id-number/${idNumber}`);\n    expect(req.request.method).toBe('GET');\n    req.flush(mockSupervisor);\n  });\n  it('should get supervisor by email', () => {\n    const email = '<EMAIL>';\n    service.getByEmail(email).subscribe(supervisor => {\n      expect(supervisor).toEqual(mockSupervisor);\n    });\n    const req = httpMock.expectOne(`${apiUrl}/email/${email}`);\n    expect(req.request.method).toBe('GET');\n    req.flush(mockSupervisor);\n  });\n  it('should create supervisor', () => {\n    const newSupervisor = {\n      fullName: 'John Doe',\n      idNumber: 123456789,\n      position: 'Manager',\n      email: '<EMAIL>',\n      idType: {\n        id: 1,\n        name: 'CC'\n      }\n    };\n    service.create(newSupervisor).subscribe(supervisor => {\n      expect(supervisor).toEqual(mockSupervisor);\n    });\n    const req = httpMock.expectOne(apiUrl);\n    expect(req.request.method).toBe('POST');\n    expect(req.request.body).toEqual(newSupervisor);\n    req.flush(mockSupervisor);\n  });\n  it('should create supervisor with signature', () => {\n    const newSupervisor = {\n      fullName: 'John Doe',\n      idNumber: 123456789,\n      position: 'Manager',\n      email: '<EMAIL>',\n      idType: {\n        id: 1,\n        name: 'CC'\n      }\n    };\n    const signatureFile = new File([''], 'signature.png', {\n      type: 'image/png'\n    });\n    service.createWithSignature(newSupervisor, signatureFile).subscribe(supervisor => {\n      expect(supervisor).toEqual(mockSupervisor);\n    });\n    const req = httpMock.expectOne(`${apiUrl}/with-signature`);\n    expect(req.request.method).toBe('POST');\n    expect(req.request.body instanceof FormData).toBeTruthy();\n    req.flush(mockSupervisor);\n  });\n  it('should update supervisor', () => {\n    const id = 1;\n    const updateData = {\n      fullName: 'John Updated',\n      position: 'Senior Manager'\n    };\n    service.update(id, updateData).subscribe(supervisor => {\n      expect(supervisor).toEqual(mockSupervisor);\n    });\n    const req = httpMock.expectOne(`${apiUrl}/${id}`);\n    expect(req.request.method).toBe('PUT');\n    expect(req.request.body).toEqual(updateData);\n    req.flush(mockSupervisor);\n  });\n  it('should update supervisor with signature', () => {\n    const id = 1;\n    const updateData = {\n      fullName: 'John Updated',\n      position: 'Senior Manager'\n    };\n    const signatureFile = new File([''], 'signature.png', {\n      type: 'image/png'\n    });\n    service.updateWithSignature(id, updateData, signatureFile).subscribe(supervisor => {\n      expect(supervisor).toEqual(mockSupervisor);\n    });\n    const req = httpMock.expectOne(`${apiUrl}/${id}/with-signature`);\n    expect(req.request.method).toBe('PUT');\n    expect(req.request.body instanceof FormData).toBeTruthy();\n    req.flush(mockSupervisor);\n  });\n  it('should delete supervisor', () => {\n    const id = 1;\n    service.delete(id).subscribe(() => {\n      expect().nothing();\n    });\n    const req = httpMock.expectOne(`${apiUrl}/${id}`);\n    expect(req.request.method).toBe('DELETE');\n    req.flush(null);\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "SupervisorService", "describe", "service", "httpMock", "apiUrl", "mockSupervisor", "id", "fullName", "idNumber", "position", "email", "idType", "name", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockSupervisors", "getAll", "subscribe", "supervisors", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "getById", "supervisor", "getByIdNumber", "getByEmail", "newSupervisor", "create", "body", "signatureFile", "File", "type", "createWithSignature", "FormData", "updateData", "update", "updateWithSignature", "delete", "nothing"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\supervisor-management\\services\\supervisor.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { Supervisor } from '@supervisor-management/models/supervisor.model';\nimport { SupervisorService } from './supervisor.service';\n\ndescribe('SupervisorService', () => {\n  let service: SupervisorService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/supervisors`;\n\n  const mockSupervisor: Supervisor = {\n    id: 1,\n    fullName: '<PERSON> Doe',\n    idNumber: 123456789,\n    position: 'Manager',\n    email: '<EMAIL>',\n    idType: {\n      id: 1,\n      name: 'CC',\n    },\n  };\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [SupervisorService],\n    });\n    service = TestBed.inject(SupervisorService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  it('should get all supervisors', () => {\n    const mockSupervisors = [mockSupervisor];\n\n    service.getAll().subscribe((supervisors) => {\n      expect(supervisors).toEqual(mockSupervisors);\n    });\n\n    const req = httpMock.expectOne(apiUrl);\n    expect(req.request.method).toBe('GET');\n    req.flush(mockSupervisors);\n  });\n\n  it('should get supervisor by id', () => {\n    const id = 1;\n\n    service.getById(id).subscribe((supervisor) => {\n      expect(supervisor).toEqual(mockSupervisor);\n    });\n\n    const req = httpMock.expectOne(`${apiUrl}/${id}`);\n    expect(req.request.method).toBe('GET');\n    req.flush(mockSupervisor);\n  });\n\n  it('should get supervisor by id number', () => {\n    const idNumber = 123456789;\n\n    service.getByIdNumber(idNumber).subscribe((supervisor) => {\n      expect(supervisor).toEqual(mockSupervisor);\n    });\n\n    const req = httpMock.expectOne(`${apiUrl}/id-number/${idNumber}`);\n    expect(req.request.method).toBe('GET');\n    req.flush(mockSupervisor);\n  });\n\n  it('should get supervisor by email', () => {\n    const email = '<EMAIL>';\n\n    service.getByEmail(email).subscribe((supervisor) => {\n      expect(supervisor).toEqual(mockSupervisor);\n    });\n\n    const req = httpMock.expectOne(`${apiUrl}/email/${email}`);\n    expect(req.request.method).toBe('GET');\n    req.flush(mockSupervisor);\n  });\n\n  it('should create supervisor', () => {\n    const newSupervisor: Omit<Supervisor, 'id'> = {\n      fullName: 'John Doe',\n      idNumber: 123456789,\n      position: 'Manager',\n      email: '<EMAIL>',\n      idType: {\n        id: 1,\n        name: 'CC',\n      },\n    };\n\n    service.create(newSupervisor).subscribe((supervisor) => {\n      expect(supervisor).toEqual(mockSupervisor);\n    });\n\n    const req = httpMock.expectOne(apiUrl);\n    expect(req.request.method).toBe('POST');\n    expect(req.request.body).toEqual(newSupervisor);\n    req.flush(mockSupervisor);\n  });\n\n  it('should create supervisor with signature', () => {\n    const newSupervisor: Omit<Supervisor, 'id'> = {\n      fullName: 'John Doe',\n      idNumber: 123456789,\n      position: 'Manager',\n      email: '<EMAIL>',\n      idType: {\n        id: 1,\n        name: 'CC',\n      },\n    };\n    const signatureFile = new File([''], 'signature.png', {\n      type: 'image/png',\n    });\n\n    service\n      .createWithSignature(newSupervisor, signatureFile)\n      .subscribe((supervisor) => {\n        expect(supervisor).toEqual(mockSupervisor);\n      });\n\n    const req = httpMock.expectOne(`${apiUrl}/with-signature`);\n    expect(req.request.method).toBe('POST');\n    expect(req.request.body instanceof FormData).toBeTruthy();\n    req.flush(mockSupervisor);\n  });\n\n  it('should update supervisor', () => {\n    const id = 1;\n    const updateData: Partial<Supervisor> = {\n      fullName: 'John Updated',\n      position: 'Senior Manager',\n    };\n\n    service.update(id, updateData).subscribe((supervisor) => {\n      expect(supervisor).toEqual(mockSupervisor);\n    });\n\n    const req = httpMock.expectOne(`${apiUrl}/${id}`);\n    expect(req.request.method).toBe('PUT');\n    expect(req.request.body).toEqual(updateData);\n    req.flush(mockSupervisor);\n  });\n\n  it('should update supervisor with signature', () => {\n    const id = 1;\n    const updateData: Partial<Supervisor> = {\n      fullName: 'John Updated',\n      position: 'Senior Manager',\n    };\n    const signatureFile = new File([''], 'signature.png', {\n      type: 'image/png',\n    });\n\n    service\n      .updateWithSignature(id, updateData, signatureFile)\n      .subscribe((supervisor) => {\n        expect(supervisor).toEqual(mockSupervisor);\n      });\n\n    const req = httpMock.expectOne(`${apiUrl}/${id}/with-signature`);\n    expect(req.request.method).toBe('PUT');\n    expect(req.request.body instanceof FormData).toBeTruthy();\n    req.flush(mockSupervisor);\n  });\n\n  it('should delete supervisor', () => {\n    const id = 1;\n\n    service.delete(id).subscribe(() => {\n      expect().nothing();\n    });\n\n    const req = httpMock.expectOne(`${apiUrl}/${id}`);\n    expect(req.request.method).toBe('DELETE');\n    req.flush(null);\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,WAAW,QAAQ,MAAM;AAElC,SAASC,iBAAiB,QAAQ,sBAAsB;AAExDC,QAAQ,CAAC,mBAAmB,EAAE,MAAK;EACjC,IAAIC,OAA0B;EAC9B,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,cAAc;EAElD,MAAMC,cAAc,GAAe;IACjCC,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,SAAS;IACnBC,QAAQ,EAAE,SAAS;IACnBC,KAAK,EAAE,kBAAkB;IACzBC,MAAM,EAAE;MACNL,EAAE,EAAE,CAAC;MACLM,IAAI,EAAE;;GAET;EAEDC,UAAU,CAAC,MAAK;IACdf,OAAO,CAACgB,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACnB,uBAAuB,CAAC;MAClCoB,SAAS,EAAE,CAAChB,iBAAiB;KAC9B,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACmB,MAAM,CAACjB,iBAAiB,CAAC;IAC3CG,QAAQ,GAAGL,OAAO,CAACmB,MAAM,CAACpB,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFqB,SAAS,CAAC,MAAK;IACbf,QAAQ,CAACgB,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACnB,OAAO,CAAC,CAACoB,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFF,EAAE,CAAC,4BAA4B,EAAE,MAAK;IACpC,MAAMG,eAAe,GAAG,CAAClB,cAAc,CAAC;IAExCH,OAAO,CAACsB,MAAM,EAAE,CAACC,SAAS,CAAEC,WAAW,IAAI;MACzCL,MAAM,CAACK,WAAW,CAAC,CAACC,OAAO,CAACJ,eAAe,CAAC;IAC9C,CAAC,CAAC;IAEF,MAAMK,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAACzB,MAAM,CAAC;IACtCiB,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;IACtCJ,GAAG,CAACK,KAAK,CAACV,eAAe,CAAC;EAC5B,CAAC,CAAC;EAEFH,EAAE,CAAC,6BAA6B,EAAE,MAAK;IACrC,MAAMd,EAAE,GAAG,CAAC;IAEZJ,OAAO,CAACgC,OAAO,CAAC5B,EAAE,CAAC,CAACmB,SAAS,CAAEU,UAAU,IAAI;MAC3Cd,MAAM,CAACc,UAAU,CAAC,CAACR,OAAO,CAACtB,cAAc,CAAC;IAC5C,CAAC,CAAC;IAEF,MAAMuB,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAAC,GAAGzB,MAAM,IAAIE,EAAE,EAAE,CAAC;IACjDe,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;IACtCJ,GAAG,CAACK,KAAK,CAAC5B,cAAc,CAAC;EAC3B,CAAC,CAAC;EAEFe,EAAE,CAAC,oCAAoC,EAAE,MAAK;IAC5C,MAAMZ,QAAQ,GAAG,SAAS;IAE1BN,OAAO,CAACkC,aAAa,CAAC5B,QAAQ,CAAC,CAACiB,SAAS,CAAEU,UAAU,IAAI;MACvDd,MAAM,CAACc,UAAU,CAAC,CAACR,OAAO,CAACtB,cAAc,CAAC;IAC5C,CAAC,CAAC;IAEF,MAAMuB,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAAC,GAAGzB,MAAM,cAAcI,QAAQ,EAAE,CAAC;IACjEa,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;IACtCJ,GAAG,CAACK,KAAK,CAAC5B,cAAc,CAAC;EAC3B,CAAC,CAAC;EAEFe,EAAE,CAAC,gCAAgC,EAAE,MAAK;IACxC,MAAMV,KAAK,GAAG,kBAAkB;IAEhCR,OAAO,CAACmC,UAAU,CAAC3B,KAAK,CAAC,CAACe,SAAS,CAAEU,UAAU,IAAI;MACjDd,MAAM,CAACc,UAAU,CAAC,CAACR,OAAO,CAACtB,cAAc,CAAC;IAC5C,CAAC,CAAC;IAEF,MAAMuB,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAAC,GAAGzB,MAAM,UAAUM,KAAK,EAAE,CAAC;IAC1DW,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;IACtCJ,GAAG,CAACK,KAAK,CAAC5B,cAAc,CAAC;EAC3B,CAAC,CAAC;EAEFe,EAAE,CAAC,0BAA0B,EAAE,MAAK;IAClC,MAAMkB,aAAa,GAA2B;MAC5C/B,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,SAAS;MACnBC,QAAQ,EAAE,SAAS;MACnBC,KAAK,EAAE,kBAAkB;MACzBC,MAAM,EAAE;QACNL,EAAE,EAAE,CAAC;QACLM,IAAI,EAAE;;KAET;IAEDV,OAAO,CAACqC,MAAM,CAACD,aAAa,CAAC,CAACb,SAAS,CAAEU,UAAU,IAAI;MACrDd,MAAM,CAACc,UAAU,CAAC,CAACR,OAAO,CAACtB,cAAc,CAAC;IAC5C,CAAC,CAAC;IAEF,MAAMuB,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAACzB,MAAM,CAAC;IACtCiB,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;IACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACU,IAAI,CAAC,CAACb,OAAO,CAACW,aAAa,CAAC;IAC/CV,GAAG,CAACK,KAAK,CAAC5B,cAAc,CAAC;EAC3B,CAAC,CAAC;EAEFe,EAAE,CAAC,yCAAyC,EAAE,MAAK;IACjD,MAAMkB,aAAa,GAA2B;MAC5C/B,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,SAAS;MACnBC,QAAQ,EAAE,SAAS;MACnBC,KAAK,EAAE,kBAAkB;MACzBC,MAAM,EAAE;QACNL,EAAE,EAAE,CAAC;QACLM,IAAI,EAAE;;KAET;IACD,MAAM6B,aAAa,GAAG,IAAIC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,eAAe,EAAE;MACpDC,IAAI,EAAE;KACP,CAAC;IAEFzC,OAAO,CACJ0C,mBAAmB,CAACN,aAAa,EAAEG,aAAa,CAAC,CACjDhB,SAAS,CAAEU,UAAU,IAAI;MACxBd,MAAM,CAACc,UAAU,CAAC,CAACR,OAAO,CAACtB,cAAc,CAAC;IAC5C,CAAC,CAAC;IAEJ,MAAMuB,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAAC,GAAGzB,MAAM,iBAAiB,CAAC;IAC1DiB,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;IACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACU,IAAI,YAAYK,QAAQ,CAAC,CAACvB,UAAU,EAAE;IACzDM,GAAG,CAACK,KAAK,CAAC5B,cAAc,CAAC;EAC3B,CAAC,CAAC;EAEFe,EAAE,CAAC,0BAA0B,EAAE,MAAK;IAClC,MAAMd,EAAE,GAAG,CAAC;IACZ,MAAMwC,UAAU,GAAwB;MACtCvC,QAAQ,EAAE,cAAc;MACxBE,QAAQ,EAAE;KACX;IAEDP,OAAO,CAAC6C,MAAM,CAACzC,EAAE,EAAEwC,UAAU,CAAC,CAACrB,SAAS,CAAEU,UAAU,IAAI;MACtDd,MAAM,CAACc,UAAU,CAAC,CAACR,OAAO,CAACtB,cAAc,CAAC;IAC5C,CAAC,CAAC;IAEF,MAAMuB,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAAC,GAAGzB,MAAM,IAAIE,EAAE,EAAE,CAAC;IACjDe,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;IACtCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACU,IAAI,CAAC,CAACb,OAAO,CAACmB,UAAU,CAAC;IAC5ClB,GAAG,CAACK,KAAK,CAAC5B,cAAc,CAAC;EAC3B,CAAC,CAAC;EAEFe,EAAE,CAAC,yCAAyC,EAAE,MAAK;IACjD,MAAMd,EAAE,GAAG,CAAC;IACZ,MAAMwC,UAAU,GAAwB;MACtCvC,QAAQ,EAAE,cAAc;MACxBE,QAAQ,EAAE;KACX;IACD,MAAMgC,aAAa,GAAG,IAAIC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,eAAe,EAAE;MACpDC,IAAI,EAAE;KACP,CAAC;IAEFzC,OAAO,CACJ8C,mBAAmB,CAAC1C,EAAE,EAAEwC,UAAU,EAAEL,aAAa,CAAC,CAClDhB,SAAS,CAAEU,UAAU,IAAI;MACxBd,MAAM,CAACc,UAAU,CAAC,CAACR,OAAO,CAACtB,cAAc,CAAC;IAC5C,CAAC,CAAC;IAEJ,MAAMuB,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAAC,GAAGzB,MAAM,IAAIE,EAAE,iBAAiB,CAAC;IAChEe,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;IACtCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACU,IAAI,YAAYK,QAAQ,CAAC,CAACvB,UAAU,EAAE;IACzDM,GAAG,CAACK,KAAK,CAAC5B,cAAc,CAAC;EAC3B,CAAC,CAAC;EAEFe,EAAE,CAAC,0BAA0B,EAAE,MAAK;IAClC,MAAMd,EAAE,GAAG,CAAC;IAEZJ,OAAO,CAAC+C,MAAM,CAAC3C,EAAE,CAAC,CAACmB,SAAS,CAAC,MAAK;MAChCJ,MAAM,EAAE,CAAC6B,OAAO,EAAE;IACpB,CAAC,CAAC;IAEF,MAAMtB,GAAG,GAAGzB,QAAQ,CAAC0B,SAAS,CAAC,GAAGzB,MAAM,IAAIE,EAAE,EAAE,CAAC;IACjDe,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;IACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;EACjB,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}