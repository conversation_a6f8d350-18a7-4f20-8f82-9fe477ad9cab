{"ast": null, "code": "import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { MatDialog } from '@angular/material/dialog';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { AlertService } from '@shared/services/alert.service';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { of, throwError } from 'rxjs';\nimport { ProfileManagementComponent } from './profile-management.component';\nimport { ProfileService } from './services/profile.service';\ndescribe('ProfileManagementComponent', () => {\n  let component;\n  let fixture;\n  let profileService;\n  let alertService;\n  let spinnerService;\n  let dialog;\n  const mockProfiles = [{\n    id: 1,\n    name: 'Admin'\n  }, {\n    id: 2,\n    name: 'User'\n  }];\n  const mockUsers = [{\n    userId: 1,\n    username: 'test',\n    profiles: [{\n      id: 1,\n      name: 'Admin'\n    }]\n  }];\n  beforeEach(() => {\n    const profileServiceSpy = jasmine.createSpyObj('ProfileService', ['getAllProfiles', 'getUsersWithProfiles', 'assignProfile', 'removeProfile']);\n    const alertServiceSpy = jasmine.createSpyObj('AlertService', ['success', 'error', 'warning']);\n    const spinnerServiceSpy = jasmine.createSpyObj('NgxSpinnerService', ['show', 'hide']);\n    const dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);\n    profileServiceSpy.getAllProfiles.and.returnValue(of(mockProfiles));\n    profileServiceSpy.getUsersWithProfiles.and.returnValue(of(mockUsers));\n    dialogSpy.open.and.returnValue({\n      afterClosed: () => of(undefined)\n    });\n    TestBed.configureTestingModule({\n      imports: [ProfileManagementComponent, HttpClientTestingModule, BrowserAnimationsModule],\n      providers: [{\n        provide: ProfileService,\n        useValue: profileServiceSpy\n      }, {\n        provide: AlertService,\n        useValue: alertServiceSpy\n      }, {\n        provide: NgxSpinnerService,\n        useValue: spinnerServiceSpy\n      }, {\n        provide: MatDialog,\n        useValue: dialogSpy\n      }]\n    });\n    fixture = TestBed.createComponent(ProfileManagementComponent);\n    component = fixture.componentInstance;\n    profileService = TestBed.inject(ProfileService);\n    alertService = TestBed.inject(AlertService);\n    spinnerService = TestBed.inject(NgxSpinnerService);\n    dialog = TestBed.inject(MatDialog);\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should load data on init', () => {\n    component.ngOnInit();\n    expect(spinnerService.show).toHaveBeenCalled();\n    expect(profileService.getAllProfiles).toHaveBeenCalled();\n    expect(profileService.getUsersWithProfiles).toHaveBeenCalled();\n    expect(component.allProfiles).toEqual(mockProfiles);\n    expect(component.dataSource.data).toEqual(mockUsers);\n  });\n  it('should handle error when loading data', () => {\n    profileService.getAllProfiles.and.returnValue(throwError(() => ({\n      error: 'Error'\n    })));\n    component.ngOnInit();\n    expect(alertService.error).toHaveBeenCalledWith('Error al cargar los datos');\n  });\n  it('should assign profile successfully', () => {\n    profileService.assignProfile.and.returnValue(of(undefined));\n    component.assignProfile(1, 2);\n    expect(spinnerService.show).toHaveBeenCalled();\n    expect(profileService.assignProfile).toHaveBeenCalledWith({\n      user_id: 1,\n      profile_id: 2\n    });\n    expect(alertService.success).toHaveBeenCalledWith('Perfil asignado correctamente');\n  });\n  it('should handle error when assigning profile', () => {\n    profileService.assignProfile.and.returnValue(throwError(() => ({\n      error: 'Error'\n    })));\n    component.assignProfile(1, 2);\n    expect(alertService.error).toHaveBeenCalledWith('Error al asignar el perfil');\n  });\n  it('should prevent removing last profile', () => {\n    component.dataSource.data = mockUsers;\n    component.removeProfile(1, 1);\n    expect(alertService.warning).toHaveBeenCalledWith('El usuario debe tener al menos un perfil');\n    expect(profileService.removeProfile).not.toHaveBeenCalled();\n  });\n  it('should remove profile successfully', () => {\n    const userWithMultipleProfiles = {\n      userId: 1,\n      username: 'test',\n      profiles: [{\n        id: 1,\n        name: 'Admin'\n      }, {\n        id: 2,\n        name: 'User'\n      }]\n    };\n    component.dataSource.data = [userWithMultipleProfiles];\n    profileService.removeProfile.and.returnValue(of(undefined));\n    component.removeProfile(1, 1);\n    expect(spinnerService.show).toHaveBeenCalled();\n    expect(profileService.removeProfile).toHaveBeenCalledWith({\n      user_id: 1,\n      profile_id: 1\n    });\n    expect(alertService.success).toHaveBeenCalledWith('Perfil removido correctamente');\n  });\n  it('should handle error when removing profile', () => {\n    const userWithMultipleProfiles = {\n      userId: 1,\n      username: 'test',\n      profiles: [{\n        id: 1,\n        name: 'Admin'\n      }, {\n        id: 2,\n        name: 'User'\n      }]\n    };\n    component.dataSource.data = [userWithMultipleProfiles];\n    profileService.removeProfile.and.returnValue(throwError(() => ({\n      error: 'Error'\n    })));\n    component.removeProfile(1, 1);\n    expect(alertService.error).toHaveBeenCalledWith('Error al remover el perfil');\n  });\n  it('should get available profiles', () => {\n    component.allProfiles = mockProfiles;\n    const user = mockUsers[0];\n    const availableProfiles = component.getAvailableProfiles(user);\n    expect(availableProfiles).toEqual([{\n      id: 2,\n      name: 'User'\n    }]);\n  });\n  it('should open user dialog and reload data on success', () => {\n    dialog.open.and.returnValue({\n      afterClosed: () => of(mockUsers[0])\n    });\n    component.openUserDialog();\n    expect(dialog.open).toHaveBeenCalled();\n    expect(profileService.getAllProfiles).toHaveBeenCalled();\n    expect(profileService.getUsersWithProfiles).toHaveBeenCalled();\n  });\n  it('should apply filter', () => {\n    const event = {\n      target: {\n        value: 'test'\n      }\n    };\n    component.dataSource.paginator = component.paginator;\n    component.applyFilter(event);\n    expect(component.dataSource.filter).toBe('test');\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "TestBed", "MatDialog", "BrowserAnimationsModule", "AlertService", "NgxSpinnerService", "of", "throwError", "ProfileManagementComponent", "ProfileService", "describe", "component", "fixture", "profileService", "alertService", "spinnerService", "dialog", "mockProfiles", "id", "name", "mockUsers", "userId", "username", "profiles", "beforeEach", "profileServiceSpy", "jasmine", "createSpyObj", "alertServiceSpy", "spinnerServiceSpy", "dialogSpy", "getAllProfiles", "and", "returnValue", "getUsersWithProfiles", "open", "afterClosed", "undefined", "configureTestingModule", "imports", "providers", "provide", "useValue", "createComponent", "componentInstance", "inject", "it", "expect", "toBeTruthy", "ngOnInit", "show", "toHaveBeenCalled", "allProfiles", "toEqual", "dataSource", "data", "error", "toHaveBeenCalledWith", "assignProfile", "user_id", "profile_id", "success", "removeProfile", "warning", "not", "userWithMultipleProfiles", "user", "availableProfiles", "getAvailableProfiles", "openUserDialog", "event", "target", "value", "paginator", "applyFilter", "filter", "toBe"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\profile-management\\profile-management.component.spec.ts"], "sourcesContent": ["import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { ComponentFixture, TestBed } from '@angular/core/testing';\nimport { MatDialog, MatDialogRef } from '@angular/material/dialog';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { AlertService } from '@shared/services/alert.service';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { of, throwError } from 'rxjs';\nimport { UserProfileAssignment } from './models/profile.model';\nimport { ProfileManagementComponent } from './profile-management.component';\nimport { ProfileService } from './services/profile.service';\n\ndescribe('ProfileManagementComponent', () => {\n  let component: ProfileManagementComponent;\n  let fixture: ComponentFixture<ProfileManagementComponent>;\n  let profileService: jasmine.SpyObj<ProfileService>;\n  let alertService: jasmine.SpyObj<AlertService>;\n  let spinnerService: jasmine.SpyObj<NgxSpinnerService>;\n  let dialog: jasmine.SpyObj<MatDialog>;\n\n  const mockProfiles = [\n    { id: 1, name: 'Admin' },\n    { id: 2, name: 'User' },\n  ];\n\n  const mockUsers: UserProfileAssignment[] = [\n    {\n      userId: 1,\n      username: 'test',\n      profiles: [{ id: 1, name: 'Admin' }],\n    },\n  ];\n\n  beforeEach(() => {\n    const profileServiceSpy = jasmine.createSpyObj('ProfileService', [\n      'getAllProfiles',\n      'getUsersWithProfiles',\n      'assignProfile',\n      'removeProfile',\n    ]);\n    const alertServiceSpy = jasmine.createSpyObj('AlertService', [\n      'success',\n      'error',\n      'warning',\n    ]);\n    const spinnerServiceSpy = jasmine.createSpyObj('NgxSpinnerService', [\n      'show',\n      'hide',\n    ]);\n    const dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);\n\n    profileServiceSpy.getAllProfiles.and.returnValue(of(mockProfiles));\n    profileServiceSpy.getUsersWithProfiles.and.returnValue(of(mockUsers));\n    dialogSpy.open.and.returnValue({\n      afterClosed: () => of(undefined),\n    } as MatDialogRef<unknown>);\n\n    TestBed.configureTestingModule({\n      imports: [\n        ProfileManagementComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n      ],\n      providers: [\n        { provide: ProfileService, useValue: profileServiceSpy },\n        { provide: AlertService, useValue: alertServiceSpy },\n        { provide: NgxSpinnerService, useValue: spinnerServiceSpy },\n        { provide: MatDialog, useValue: dialogSpy },\n      ],\n    });\n\n    fixture = TestBed.createComponent(ProfileManagementComponent);\n    component = fixture.componentInstance;\n    profileService = TestBed.inject(\n      ProfileService,\n    ) as jasmine.SpyObj<ProfileService>;\n    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;\n    spinnerService = TestBed.inject(\n      NgxSpinnerService,\n    ) as jasmine.SpyObj<NgxSpinnerService>;\n    dialog = TestBed.inject(MatDialog) as jasmine.SpyObj<MatDialog>;\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should load data on init', () => {\n    component.ngOnInit();\n    expect(spinnerService.show).toHaveBeenCalled();\n    expect(profileService.getAllProfiles).toHaveBeenCalled();\n    expect(profileService.getUsersWithProfiles).toHaveBeenCalled();\n    expect(component.allProfiles).toEqual(mockProfiles);\n    expect(component.dataSource.data).toEqual(mockUsers);\n  });\n\n  it('should handle error when loading data', () => {\n    profileService.getAllProfiles.and.returnValue(\n      throwError(() => ({ error: 'Error' })),\n    );\n    component.ngOnInit();\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al cargar los datos',\n    );\n  });\n\n  it('should assign profile successfully', () => {\n    profileService.assignProfile.and.returnValue(of(undefined));\n    component.assignProfile(1, 2);\n    expect(spinnerService.show).toHaveBeenCalled();\n    expect(profileService.assignProfile).toHaveBeenCalledWith({\n      user_id: 1,\n      profile_id: 2,\n    });\n    expect(alertService.success).toHaveBeenCalledWith(\n      'Perfil asignado correctamente',\n    );\n  });\n\n  it('should handle error when assigning profile', () => {\n    profileService.assignProfile.and.returnValue(\n      throwError(() => ({ error: 'Error' })),\n    );\n    component.assignProfile(1, 2);\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al asignar el perfil',\n    );\n  });\n\n  it('should prevent removing last profile', () => {\n    component.dataSource.data = mockUsers;\n    component.removeProfile(1, 1);\n    expect(alertService.warning).toHaveBeenCalledWith(\n      'El usuario debe tener al menos un perfil',\n    );\n    expect(profileService.removeProfile).not.toHaveBeenCalled();\n  });\n\n  it('should remove profile successfully', () => {\n    const userWithMultipleProfiles = {\n      userId: 1,\n      username: 'test',\n      profiles: [\n        { id: 1, name: 'Admin' },\n        { id: 2, name: 'User' },\n      ],\n    };\n    component.dataSource.data = [userWithMultipleProfiles];\n    profileService.removeProfile.and.returnValue(of(undefined));\n\n    component.removeProfile(1, 1);\n    expect(spinnerService.show).toHaveBeenCalled();\n    expect(profileService.removeProfile).toHaveBeenCalledWith({\n      user_id: 1,\n      profile_id: 1,\n    });\n    expect(alertService.success).toHaveBeenCalledWith(\n      'Perfil removido correctamente',\n    );\n  });\n\n  it('should handle error when removing profile', () => {\n    const userWithMultipleProfiles = {\n      userId: 1,\n      username: 'test',\n      profiles: [\n        { id: 1, name: 'Admin' },\n        { id: 2, name: 'User' },\n      ],\n    };\n    component.dataSource.data = [userWithMultipleProfiles];\n    profileService.removeProfile.and.returnValue(\n      throwError(() => ({ error: 'Error' })),\n    );\n\n    component.removeProfile(1, 1);\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al remover el perfil',\n    );\n  });\n\n  it('should get available profiles', () => {\n    component.allProfiles = mockProfiles;\n    const user = mockUsers[0];\n    const availableProfiles = component.getAvailableProfiles(user);\n    expect(availableProfiles).toEqual([{ id: 2, name: 'User' }]);\n  });\n\n  it('should open user dialog and reload data on success', () => {\n    dialog.open.and.returnValue({\n      afterClosed: () => of(mockUsers[0]),\n    } as MatDialogRef<unknown>);\n    component.openUserDialog();\n    expect(dialog.open).toHaveBeenCalled();\n    expect(profileService.getAllProfiles).toHaveBeenCalled();\n    expect(profileService.getUsersWithProfiles).toHaveBeenCalled();\n  });\n\n  it('should apply filter', () => {\n    const event = { target: { value: 'test' } } as unknown as Event;\n    component.dataSource.paginator = component.paginator;\n    component.applyFilter(event);\n    expect(component.dataSource.filter).toBe('test');\n  });\n});"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,8BAA8B;AACtE,SAA2BC,OAAO,QAAQ,uBAAuB;AACjE,SAASC,SAAS,QAAsB,0BAA0B;AAClE,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,iBAAiB,QAAQ,aAAa;AAC/C,SAASC,EAAE,EAAEC,UAAU,QAAQ,MAAM;AAErC,SAASC,0BAA0B,QAAQ,gCAAgC;AAC3E,SAASC,cAAc,QAAQ,4BAA4B;AAE3DC,QAAQ,CAAC,4BAA4B,EAAE,MAAK;EAC1C,IAAIC,SAAqC;EACzC,IAAIC,OAAqD;EACzD,IAAIC,cAA8C;EAClD,IAAIC,YAA0C;EAC9C,IAAIC,cAAiD;EACrD,IAAIC,MAAiC;EAErC,MAAMC,YAAY,GAAG,CACnB;IAAEC,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAO,CAAE,EACxB;IAAED,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAM,CAAE,CACxB;EAED,MAAMC,SAAS,GAA4B,CACzC;IACEC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,CAAC;MAAEL,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAO,CAAE;GACpC,CACF;EAEDK,UAAU,CAAC,MAAK;IACd,MAAMC,iBAAiB,GAAGC,OAAO,CAACC,YAAY,CAAC,gBAAgB,EAAE,CAC/D,gBAAgB,EAChB,sBAAsB,EACtB,eAAe,EACf,eAAe,CAChB,CAAC;IACF,MAAMC,eAAe,GAAGF,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAC3D,SAAS,EACT,OAAO,EACP,SAAS,CACV,CAAC;IACF,MAAME,iBAAiB,GAAGH,OAAO,CAACC,YAAY,CAAC,mBAAmB,EAAE,CAClE,MAAM,EACN,MAAM,CACP,CAAC;IACF,MAAMG,SAAS,GAAGJ,OAAO,CAACC,YAAY,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,CAAC;IAE7DF,iBAAiB,CAACM,cAAc,CAACC,GAAG,CAACC,WAAW,CAAC3B,EAAE,CAACW,YAAY,CAAC,CAAC;IAClEQ,iBAAiB,CAACS,oBAAoB,CAACF,GAAG,CAACC,WAAW,CAAC3B,EAAE,CAACc,SAAS,CAAC,CAAC;IACrEU,SAAS,CAACK,IAAI,CAACH,GAAG,CAACC,WAAW,CAAC;MAC7BG,WAAW,EAAEA,CAAA,KAAM9B,EAAE,CAAC+B,SAAS;KACP,CAAC;IAE3BpC,OAAO,CAACqC,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACP/B,0BAA0B,EAC1BR,uBAAuB,EACvBG,uBAAuB,CACxB;MACDqC,SAAS,EAAE,CACT;QAAEC,OAAO,EAAEhC,cAAc;QAAEiC,QAAQ,EAAEjB;MAAiB,CAAE,EACxD;QAAEgB,OAAO,EAAErC,YAAY;QAAEsC,QAAQ,EAAEd;MAAe,CAAE,EACpD;QAAEa,OAAO,EAAEpC,iBAAiB;QAAEqC,QAAQ,EAAEb;MAAiB,CAAE,EAC3D;QAAEY,OAAO,EAAEvC,SAAS;QAAEwC,QAAQ,EAAEZ;MAAS,CAAE;KAE9C,CAAC;IAEFlB,OAAO,GAAGX,OAAO,CAAC0C,eAAe,CAACnC,0BAA0B,CAAC;IAC7DG,SAAS,GAAGC,OAAO,CAACgC,iBAAiB;IACrC/B,cAAc,GAAGZ,OAAO,CAAC4C,MAAM,CAC7BpC,cAAc,CACmB;IACnCK,YAAY,GAAGb,OAAO,CAAC4C,MAAM,CAACzC,YAAY,CAAiC;IAC3EW,cAAc,GAAGd,OAAO,CAAC4C,MAAM,CAC7BxC,iBAAiB,CACmB;IACtCW,MAAM,GAAGf,OAAO,CAAC4C,MAAM,CAAC3C,SAAS,CAA8B;EACjE,CAAC,CAAC;EAEF4C,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACpC,SAAS,CAAC,CAACqC,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,0BAA0B,EAAE,MAAK;IAClCnC,SAAS,CAACsC,QAAQ,EAAE;IACpBF,MAAM,CAAChC,cAAc,CAACmC,IAAI,CAAC,CAACC,gBAAgB,EAAE;IAC9CJ,MAAM,CAAClC,cAAc,CAACkB,cAAc,CAAC,CAACoB,gBAAgB,EAAE;IACxDJ,MAAM,CAAClC,cAAc,CAACqB,oBAAoB,CAAC,CAACiB,gBAAgB,EAAE;IAC9DJ,MAAM,CAACpC,SAAS,CAACyC,WAAW,CAAC,CAACC,OAAO,CAACpC,YAAY,CAAC;IACnD8B,MAAM,CAACpC,SAAS,CAAC2C,UAAU,CAACC,IAAI,CAAC,CAACF,OAAO,CAACjC,SAAS,CAAC;EACtD,CAAC,CAAC;EAEF0B,EAAE,CAAC,uCAAuC,EAAE,MAAK;IAC/CjC,cAAc,CAACkB,cAAc,CAACC,GAAG,CAACC,WAAW,CAC3C1B,UAAU,CAAC,OAAO;MAAEiD,KAAK,EAAE;IAAO,CAAE,CAAC,CAAC,CACvC;IACD7C,SAAS,CAACsC,QAAQ,EAAE;IACpBF,MAAM,CAACjC,YAAY,CAAC0C,KAAK,CAAC,CAACC,oBAAoB,CAC7C,2BAA2B,CAC5B;EACH,CAAC,CAAC;EAEFX,EAAE,CAAC,oCAAoC,EAAE,MAAK;IAC5CjC,cAAc,CAAC6C,aAAa,CAAC1B,GAAG,CAACC,WAAW,CAAC3B,EAAE,CAAC+B,SAAS,CAAC,CAAC;IAC3D1B,SAAS,CAAC+C,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7BX,MAAM,CAAChC,cAAc,CAACmC,IAAI,CAAC,CAACC,gBAAgB,EAAE;IAC9CJ,MAAM,CAAClC,cAAc,CAAC6C,aAAa,CAAC,CAACD,oBAAoB,CAAC;MACxDE,OAAO,EAAE,CAAC;MACVC,UAAU,EAAE;KACb,CAAC;IACFb,MAAM,CAACjC,YAAY,CAAC+C,OAAO,CAAC,CAACJ,oBAAoB,CAC/C,+BAA+B,CAChC;EACH,CAAC,CAAC;EAEFX,EAAE,CAAC,4CAA4C,EAAE,MAAK;IACpDjC,cAAc,CAAC6C,aAAa,CAAC1B,GAAG,CAACC,WAAW,CAC1C1B,UAAU,CAAC,OAAO;MAAEiD,KAAK,EAAE;IAAO,CAAE,CAAC,CAAC,CACvC;IACD7C,SAAS,CAAC+C,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7BX,MAAM,CAACjC,YAAY,CAAC0C,KAAK,CAAC,CAACC,oBAAoB,CAC7C,4BAA4B,CAC7B;EACH,CAAC,CAAC;EAEFX,EAAE,CAAC,sCAAsC,EAAE,MAAK;IAC9CnC,SAAS,CAAC2C,UAAU,CAACC,IAAI,GAAGnC,SAAS;IACrCT,SAAS,CAACmD,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7Bf,MAAM,CAACjC,YAAY,CAACiD,OAAO,CAAC,CAACN,oBAAoB,CAC/C,0CAA0C,CAC3C;IACDV,MAAM,CAAClC,cAAc,CAACiD,aAAa,CAAC,CAACE,GAAG,CAACb,gBAAgB,EAAE;EAC7D,CAAC,CAAC;EAEFL,EAAE,CAAC,oCAAoC,EAAE,MAAK;IAC5C,MAAMmB,wBAAwB,GAAG;MAC/B5C,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE,CACR;QAAEL,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE;MAAO,CAAE,EACxB;QAAED,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE;MAAM,CAAE;KAE1B;IACDR,SAAS,CAAC2C,UAAU,CAACC,IAAI,GAAG,CAACU,wBAAwB,CAAC;IACtDpD,cAAc,CAACiD,aAAa,CAAC9B,GAAG,CAACC,WAAW,CAAC3B,EAAE,CAAC+B,SAAS,CAAC,CAAC;IAE3D1B,SAAS,CAACmD,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7Bf,MAAM,CAAChC,cAAc,CAACmC,IAAI,CAAC,CAACC,gBAAgB,EAAE;IAC9CJ,MAAM,CAAClC,cAAc,CAACiD,aAAa,CAAC,CAACL,oBAAoB,CAAC;MACxDE,OAAO,EAAE,CAAC;MACVC,UAAU,EAAE;KACb,CAAC;IACFb,MAAM,CAACjC,YAAY,CAAC+C,OAAO,CAAC,CAACJ,oBAAoB,CAC/C,+BAA+B,CAChC;EACH,CAAC,CAAC;EAEFX,EAAE,CAAC,2CAA2C,EAAE,MAAK;IACnD,MAAMmB,wBAAwB,GAAG;MAC/B5C,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE,CACR;QAAEL,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE;MAAO,CAAE,EACxB;QAAED,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE;MAAM,CAAE;KAE1B;IACDR,SAAS,CAAC2C,UAAU,CAACC,IAAI,GAAG,CAACU,wBAAwB,CAAC;IACtDpD,cAAc,CAACiD,aAAa,CAAC9B,GAAG,CAACC,WAAW,CAC1C1B,UAAU,CAAC,OAAO;MAAEiD,KAAK,EAAE;IAAO,CAAE,CAAC,CAAC,CACvC;IAED7C,SAAS,CAACmD,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7Bf,MAAM,CAACjC,YAAY,CAAC0C,KAAK,CAAC,CAACC,oBAAoB,CAC7C,4BAA4B,CAC7B;EACH,CAAC,CAAC;EAEFX,EAAE,CAAC,+BAA+B,EAAE,MAAK;IACvCnC,SAAS,CAACyC,WAAW,GAAGnC,YAAY;IACpC,MAAMiD,IAAI,GAAG9C,SAAS,CAAC,CAAC,CAAC;IACzB,MAAM+C,iBAAiB,GAAGxD,SAAS,CAACyD,oBAAoB,CAACF,IAAI,CAAC;IAC9DnB,MAAM,CAACoB,iBAAiB,CAAC,CAACd,OAAO,CAAC,CAAC;MAAEnC,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAM,CAAE,CAAC,CAAC;EAC9D,CAAC,CAAC;EAEF2B,EAAE,CAAC,oDAAoD,EAAE,MAAK;IAC5D9B,MAAM,CAACmB,IAAI,CAACH,GAAG,CAACC,WAAW,CAAC;MAC1BG,WAAW,EAAEA,CAAA,KAAM9B,EAAE,CAACc,SAAS,CAAC,CAAC,CAAC;KACV,CAAC;IAC3BT,SAAS,CAAC0D,cAAc,EAAE;IAC1BtB,MAAM,CAAC/B,MAAM,CAACmB,IAAI,CAAC,CAACgB,gBAAgB,EAAE;IACtCJ,MAAM,CAAClC,cAAc,CAACkB,cAAc,CAAC,CAACoB,gBAAgB,EAAE;IACxDJ,MAAM,CAAClC,cAAc,CAACqB,oBAAoB,CAAC,CAACiB,gBAAgB,EAAE;EAChE,CAAC,CAAC;EAEFL,EAAE,CAAC,qBAAqB,EAAE,MAAK;IAC7B,MAAMwB,KAAK,GAAG;MAAEC,MAAM,EAAE;QAAEC,KAAK,EAAE;MAAM;IAAE,CAAsB;IAC/D7D,SAAS,CAAC2C,UAAU,CAACmB,SAAS,GAAG9D,SAAS,CAAC8D,SAAS;IACpD9D,SAAS,CAAC+D,WAAW,CAACJ,KAAK,CAAC;IAC5BvB,MAAM,CAACpC,SAAS,CAAC2C,UAAU,CAACqB,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;EAClD,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}