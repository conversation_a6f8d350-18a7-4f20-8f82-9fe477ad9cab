import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Validators } from '@angular/forms';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ContractorDetailFormComponent } from '@contract-management/components/contractor-detail-form/contractor-detail-form.component';
import { Contract } from '@contract-management/models/contract.model';
import { ContractorContract } from '@contract-management/models/contractor-contract.model';
import { InsuredRisks } from '@contract-management/models/insured_risks.model';
import { TypeWarranty } from '@contract-management/models/type_warranty.model';
import { ContractValuesService } from '@contract-management/services/contract-values.service';
import { ContractService } from '@contract-management/services/contract.service';
import { ContractorContractService } from '@contract-management/services/contractor-contract.service';
import { InsuredRisksService } from '@contract-management/services/insured_risks.service';
import { TypeWarrantyService } from '@contract-management/services/type_warranty.service';
import { AlertService } from '@shared/services/alert.service';
import { of, throwError } from 'rxjs';
import { AssociateContractorDialogComponent } from './associate-contractor-dialog.component';

describe('AssociateContractorDialogComponent', () => {
  let component: AssociateContractorDialogComponent;
  let fixture: ComponentFixture<AssociateContractorDialogComponent>;
  let dialogRef: jasmine.SpyObj<
    MatDialogRef<AssociateContractorDialogComponent>
  >;
  let contractorContractService: jasmine.SpyObj<ContractorContractService>;
  let contractService: jasmine.SpyObj<ContractService>;
  let contractValuesService: jasmine.SpyObj<ContractValuesService>;
  let typeWarrantyService: jasmine.SpyObj<TypeWarrantyService>;
  let insuredRisksService: jasmine.SpyObj<InsuredRisksService>;
  let alertService: jasmine.SpyObj<AlertService>;

  const mockTypeWarranty: TypeWarranty[] = [
    { id: 1, name: 'Type 1' },
    { id: 2, name: 'Type 2' },
  ];

  const mockInsuredRisks: InsuredRisks[] = [
    { id: 1, name: 'Risk 1' },
    { id: 2, name: 'Risk 2' },
  ];

  const mockContractorContract: ContractorContract = {
    id: 1,
    contractorId: 1,
    contractId: 1,
    subscriptionDate: new Date('2024-02-19').toISOString().slice(0, 10),
    contractStartDate: new Date('2024-02-21').toISOString().slice(0, 10),
    warranty: true,
    dateExpeditionWarranty: new Date('2024-02-19').toISOString().slice(0, 10),
    typeWarrantyId: 1,
    insuredRisksId: 1,
  };

  const mockContract: Contract = {
    id: 1,
    contractNumber: 123,
    monthlyPayment: 1000000,
    object: 'Test Contract',
    rup: true,
    secopCode: 123456,
    addition: false,
    cession: true,
    settled: false,
    contractTypeId: 1,
    statusId: 1,
    causesSelectionId: 1,
    managementSupportId: 1,
    contractClassId: 1,
  };

  beforeEach(() => {
    const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close']);
    const contractorContractServiceSpy = jasmine.createSpyObj(
      'ContractorContractService',
      ['getLatestTerminationDate', 'create'],
    );
    const contractServiceSpy = jasmine.createSpyObj('ContractService', [
      'update',
    ]);
    const contractValuesServiceSpy = jasmine.createSpyObj(
      'ContractValuesService',
      ['getLatestEndDateByContractId'],
    );
    const typeWarrantyServiceSpy = jasmine.createSpyObj('TypeWarrantyService', [
      'getAll',
    ]);
    const insuredRisksServiceSpy = jasmine.createSpyObj('InsuredRisksService', [
      'getAll',
    ]);
    const alertServiceSpy = jasmine.createSpyObj('AlertService', [
      'success',
      'error',
      'warning',
    ]);

    TestBed.configureTestingModule({
      imports: [
        AssociateContractorDialogComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
      ],
      providers: [
        { provide: MatDialogRef, useValue: dialogRefSpy },
        { provide: MAT_DIALOG_DATA, useValue: { contractId: 1 } },
        {
          provide: ContractorContractService,
          useValue: contractorContractServiceSpy,
        },
        { provide: ContractService, useValue: contractServiceSpy },
        { provide: ContractValuesService, useValue: contractValuesServiceSpy },
        { provide: TypeWarrantyService, useValue: typeWarrantyServiceSpy },
        { provide: InsuredRisksService, useValue: insuredRisksServiceSpy },
        { provide: AlertService, useValue: alertServiceSpy },
        provideNativeDateAdapter(),
      ],
    });

    fixture = TestBed.createComponent(AssociateContractorDialogComponent);
    component = fixture.componentInstance;
    dialogRef = TestBed.inject(MatDialogRef) as jasmine.SpyObj<
      MatDialogRef<AssociateContractorDialogComponent>
    >;
    contractorContractService = TestBed.inject(
      ContractorContractService,
    ) as jasmine.SpyObj<ContractorContractService>;
    contractService = TestBed.inject(
      ContractService,
    ) as jasmine.SpyObj<ContractService>;
    contractValuesService = TestBed.inject(
      ContractValuesService,
    ) as jasmine.SpyObj<ContractValuesService>;
    typeWarrantyService = TestBed.inject(
      TypeWarrantyService,
    ) as jasmine.SpyObj<TypeWarrantyService>;
    insuredRisksService = TestBed.inject(
      InsuredRisksService,
    ) as jasmine.SpyObj<InsuredRisksService>;
    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;

    typeWarrantyService.getAll.and.returnValue(of(mockTypeWarranty));
    insuredRisksService.getAll.and.returnValue(of(mockInsuredRisks));
    contractorContractService.getLatestTerminationDate.and.returnValue(
      of('2024-02-19'),
    );
    contractValuesService.getLatestEndDateByContractId.and.returnValue(
      of(new Date('2024-12-31')),
    );

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Initial Data Loading', () => {
    it('should load type warranty and insured risks data', () => {
      expect(component.typeWarranty).toEqual(mockTypeWarranty);
      expect(component.insuredRisks).toEqual(mockInsuredRisks);
    });

    it('should handle error when loading initial data', () => {
      typeWarrantyService.getAll.and.returnValue(throwError(() => new Error()));
      component.ngOnInit();
      expect(alertService.error).toHaveBeenCalledWith(
        'Error al cargar los datos iniciales',
      );
    });

    it('should set minStartDate based on latest termination date', () => {
      const expectedDate = new Date('2024-02-21');
      expect(component.minStartDate?.toISOString().slice(0, 10)).toBe(
        expectedDate.toISOString().slice(0, 10),
      );
    });

    it('should set latestContractEndDate', () => {
      const expectedDate = new Date('2024-12-31');
      expect(component.latestContractEndDate?.toISOString()).toBe(
        expectedDate.toISOString(),
      );
    });
  });

  describe('Form Validation', () => {
    it('should validate required fields', () => {
      expect(
        component.cessionForm.get('subscriptionDate')?.errors?.['required'],
      ).toBeTruthy();
      expect(
        component.cessionForm.get('startDate')?.errors?.['required'],
      ).toBeTruthy();
    });

    it('should update warranty-related validations when warranty is toggled', () => {
      component.cessionForm.patchValue({ warranty: true });
      expect(
        component.cessionForm
          .get('dateExpeditionWarranty')
          ?.hasValidator(Validators.required),
      ).toBeTrue();
      expect(
        component.cessionForm
          .get('typeWarrantyId')
          ?.hasValidator(Validators.required),
      ).toBeTrue();
      expect(
        component.cessionForm
          .get('insuredRisksId')
          ?.hasValidator(Validators.required),
      ).toBeTrue();

      component.cessionForm.patchValue({ warranty: false });
      expect(
        component.cessionForm
          .get('dateExpeditionWarranty')
          ?.hasValidator(Validators.required),
      ).toBeFalse();
      expect(
        component.cessionForm
          .get('typeWarrantyId')
          ?.hasValidator(Validators.required),
      ).toBeFalse();
      expect(
        component.cessionForm
          .get('insuredRisksId')
          ?.hasValidator(Validators.required),
      ).toBeFalse();
    });
  });

  describe('Form Submission', () => {
    beforeEach(() => {
      component.contractorDetailForm = {
        isValid: () => true,
        getValue: () => ({ id: 1, name: 'Test Contractor' }),
      } as unknown as ContractorDetailFormComponent;

      spyOn(component, 'isFormValid').and.returnValue(true);
    });

    it('should not submit if form is invalid', () => {
      component.isFormValid = jasmine.createSpy().and.returnValue(false);
      component.onSubmit();
      expect(contractorContractService.create).not.toHaveBeenCalled();
    });

    it('should submit form successfully', () => {
      const today = new Date();
      component.cessionForm.patchValue({
        subscriptionDate: today,
        startDate: today,
        warranty: true,
        dateExpeditionWarranty: today,
        typeWarrantyId: 1,
        insuredRisksId: 1,
      });

      contractorContractService.create.and.returnValue(
        of(mockContractorContract),
      );
      contractService.update.and.returnValue(of(mockContract));

      component.onSubmit();

      expect(contractorContractService.create).toHaveBeenCalledWith({
        contractorId: 1,
        contractId: 1,
        subscriptionDate: today.toISOString().slice(0, 10),
        contractStartDate: today.toISOString().slice(0, 10),
        warranty: true,
        dateExpeditionWarranty: today.toISOString().slice(0, 10),
        typeWarrantyId: 1,
        insuredRisksId: 1,
      });

      expect(contractService.update).toHaveBeenCalledWith(1, {
        earlyTermination: false,
        cession: true,
      });

      expect(alertService.success).toHaveBeenCalledWith(
        'Contratista asociado exitosamente y contrato actualizado',
      );

      expect(dialogRef.close).toHaveBeenCalledWith({
        success: true,
        contractorAdded: true,
      });
    });

    it('should handle error when creating contractor contract', () => {
      const today = new Date();
      component.cessionForm.patchValue({
        subscriptionDate: today,
        startDate: today,
        warranty: true,
        dateExpeditionWarranty: today,
        typeWarrantyId: 1,
        insuredRisksId: 1,
      });

      contractorContractService.create.and.returnValue(
        throwError(() => ({ error: 'Error' })),
      );

      component.onSubmit();

      expect(alertService.error).toHaveBeenCalledWith(
        'Error al asociar el contratista.',
      );
    });

    it('should handle error when updating contract', () => {
      const today = new Date();
      component.cessionForm.patchValue({
        subscriptionDate: today,
        startDate: today,
        warranty: true,
        dateExpeditionWarranty: today,
        typeWarrantyId: 1,
        insuredRisksId: 1,
      });

      contractorContractService.create.and.returnValue(
        of(mockContractorContract),
      );
      contractService.update.and.returnValue(throwError(() => new Error()));

      component.onSubmit();

      expect(alertService.warning).toHaveBeenCalledWith(
        'Contratista asociado, pero hubo un error al actualizar el contrato',
      );
      expect(dialogRef.close).toHaveBeenCalledWith({
        success: true,
        contractorAdded: true,
      });
    });
  });
});
