import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { SupervisorContract } from '@contract-management/models/supervisor-contract.model';
import { environment } from '@env';
import { SupervisorContractService } from './supervisor-contract.service';

describe('SupervisorContractService', () => {
  let service: SupervisorContractService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/supervisors_contract`;

  const mockSupervisorContract: SupervisorContract = {
    id: 1,
    supervisorId: 1,
    contractId: 1,
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [SupervisorContractService],
    });
    service = TestBed.inject(SupervisorContractService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should get all supervisor contracts', () => {
    const mockSupervisorContracts = [mockSupervisorContract];

    service.getAll().subscribe((supervisorContracts) => {
      expect(supervisorContracts).toEqual(mockSupervisorContracts);
    });

    const req = httpMock.expectOne(apiUrl);
    expect(req.request.method).toBe('GET');
    req.flush(mockSupervisorContracts);
  });

  it('should get supervisor contract by id', () => {
    const id = 1;

    service.getById(id).subscribe((supervisorContract) => {
      expect(supervisorContract).toEqual(mockSupervisorContract);
    });

    const req = httpMock.expectOne(`${apiUrl}/${id}`);
    expect(req.request.method).toBe('GET');
    req.flush(mockSupervisorContract);
  });

  it('should get supervisor contract by id number', () => {
    const idNumber = '123456789';

    service.getByIdNumber(idNumber).subscribe((supervisorContract) => {
      expect(supervisorContract).toEqual(mockSupervisorContract);
    });

    const req = httpMock.expectOne(`${apiUrl}/id-number/${idNumber}`);
    expect(req.request.method).toBe('GET');
    req.flush(mockSupervisorContract);
  });

  it('should get supervisor contracts by contract id', () => {
    const contractId = 1;
    const mockSupervisorContracts = [mockSupervisorContract];

    service.getByContractId(contractId).subscribe((supervisorContracts) => {
      expect(supervisorContracts).toEqual(mockSupervisorContracts);
    });

    const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}`);
    expect(req.request.method).toBe('GET');
    req.flush(mockSupervisorContracts);
  });

  it('should create supervisor contract', () => {
    const newSupervisorContract: Omit<SupervisorContract, 'id'> = {
      supervisorId: 1,
      contractId: 1,
    };

    service.create(newSupervisorContract).subscribe((supervisorContract) => {
      expect(supervisorContract).toEqual(mockSupervisorContract);
    });

    const req = httpMock.expectOne(apiUrl);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(newSupervisorContract);
    req.flush(mockSupervisorContract);
  });

  it('should update supervisor contract', () => {
    const id = 1;
    const updateData: Partial<SupervisorContract> = {
      supervisorId: 2,
    };

    service.update(id, updateData).subscribe((supervisorContract) => {
      expect(supervisorContract).toEqual(mockSupervisorContract);
    });

    const req = httpMock.expectOne(`${apiUrl}/${id}`);
    expect(req.request.method).toBe('PUT');
    expect(req.request.body).toEqual(updateData);
    req.flush(mockSupervisorContract);
  });

  it('should delete supervisor contract', () => {
    const id = 1;

    service.delete(id).subscribe(() => {
      expect().nothing();
    });

    const req = httpMock.expectOne(`${apiUrl}/${id}`);
    expect(req.request.method).toBe('DELETE');
    req.flush(null);
  });
});