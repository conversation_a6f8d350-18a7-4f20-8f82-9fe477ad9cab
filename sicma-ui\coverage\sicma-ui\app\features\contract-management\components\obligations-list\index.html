
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for app/features/contract-management/components/obligations-list</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../../index.html">All files</a> app/features/contract-management/components/obligations-list</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">85.07% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>57/67</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">58.53% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>24/41</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">73.68% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>14/19</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">86.36% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>57/66</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="obligations-list.component.ts"><a href="obligations-list.component.ts.html">obligations-list.component.ts</a></td>
	<td data-value="85.07" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 85%"></div><div class="cover-empty" style="width: 15%"></div></div>
	</td>
	<td data-value="85.07" class="pct high">85.07%</td>
	<td data-value="67" class="abs high">57/67</td>
	<td data-value="58.53" class="pct medium">58.53%</td>
	<td data-value="41" class="abs medium">24/41</td>
	<td data-value="73.68" class="pct medium">73.68%</td>
	<td data-value="19" class="abs medium">14/19</td>
	<td data-value="86.36" class="pct high">86.36%</td>
	<td data-value="66" class="abs high">57/66</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-15T19:39:21.965Z
            </div>
        <script src="../../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../../sorter.js"></script>
        <script src="../../../../../block-navigation.js"></script>
    </body>
</html>
    