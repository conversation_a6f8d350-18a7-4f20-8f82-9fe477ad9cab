import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { InitialReportDocumentation } from '@contractor-dashboard/models/initial-report-documentation.model';
import { BankAccountTypeService } from '@contractor-dashboard/services/bank-account-type.service';
import { BankService } from '@contractor-dashboard/services/bank.service';
import { AlertService } from '@shared/services/alert.service';
import { of, throwError } from 'rxjs';
import { BankInfoComponent } from './bank-info.component';

describe('BankInfoComponent', () => {
  let component: BankInfoComponent;
  let fixture: ComponentFixture<BankInfoComponent>;
  let bankService: jasmine.SpyObj<BankService>;
  let bankAccountTypeService: jasmine.SpyObj<BankAccountTypeService>;
  let alertService: jasmine.SpyObj<AlertService>;

  beforeEach(async () => {
    const bankServiceSpy = jasmine.createSpyObj('BankService', ['getAll']);
    const bankAccountTypeServiceSpy = jasmine.createSpyObj(
      'BankAccountTypeService',
      ['getAll'],
    );
    const alertServiceSpy = jasmine.createSpyObj('AlertService', ['error']);

    await TestBed.configureTestingModule({
      imports: [BankInfoComponent, NoopAnimationsModule],
      providers: [
        { provide: BankService, useValue: bankServiceSpy },
        {
          provide: BankAccountTypeService,
          useValue: bankAccountTypeServiceSpy,
        },
        { provide: AlertService, useValue: alertServiceSpy },
      ],
    }).compileComponents();

    bankService = TestBed.inject(BankService) as jasmine.SpyObj<BankService>;
    bankAccountTypeService = TestBed.inject(
      BankAccountTypeService,
    ) as jasmine.SpyObj<BankAccountTypeService>;
    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;

    bankService.getAll.and.returnValue(of([]));
    bankAccountTypeService.getAll.and.returnValue(of([]));
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(BankInfoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Initialization', () => {
    it('should initialize form with empty values', () => {
      expect(component.form.get('bank')?.value).toBe('');
      expect(component.form.get('accountType')?.value).toBe('');
      expect(component.form.get('accountNumber')?.value).toBe('');
      expect(component.bankCertificateFileName).toBeNull();
    });

    it('should handle error when loading data', () => {
      bankService.getAll.and.returnValue(throwError(() => new Error()));
      component.ngOnInit();
      expect(alertService.error).toHaveBeenCalledWith(
        'Error al cargar los datos del formulario',
      );
    });
  });

  describe('Form Validation', () => {
    it('should validate required fields', () => {
      expect(component.form.get('bank')?.hasError('required')).toBeTrue();
      expect(
        component.form.get('accountType')?.hasError('required'),
      ).toBeTrue();
      expect(
        component.form.get('accountNumber')?.hasError('required'),
      ).toBeTrue();
    });

    it('should be valid when all required fields are filled', () => {
      const form = component.form;
      form.patchValue({
        bank: 1,
        accountType: 1,
        accountNumber: '*********',
      });
      expect(form.valid).toBeTruthy();
    });

    it('should return valid status when form and existing file are valid', () => {
      component.initialData = {
        id: 1,
        contractorContractId: 1,
        bankId: 1,
        bankAccountTypeId: 1,
        accountNumber: '*********',
        bankCertificateFileUrl: 'http://example.com/file.pdf',
        taxRegimeId: 1,
        epsId: 1,
        arlId: 1,
        pensionFundId: 1,
        hasDependents: false,
        hasHousingInterest: false,
        housingInterestAnnualPayment: 0,
        hasPrepaidMedicine: false,
        prepaidMedicineAnnualPayment: 0,
        hasAfcAccount: false,
        hasVoluntarySavings: false,
        afcAccountAnnualPayment: 0,
        voluntarySavingsAnnualPayment: 0,
      };
      component.form.patchValue({
        bank: 1,
        accountType: 1,
        accountNumber: '*********',
      });

      expect(component.isValid).toBeTrue();
    });
  });

  describe('File Handling', () => {
    it('should accept PDF file and set filename', () => {
      const file = new File([''], 'test.pdf', { type: 'application/pdf' });
      const event = { target: { files: [file] } } as unknown as Event;

      component.onFileSelected(event);
      expect(component.bankCertificateFileName).toBe('test.pdf');
      expect(alertService.error).not.toHaveBeenCalled();
    });

    it('should reject non-PDF file', () => {
      const file = new File([''], 'test.txt', { type: 'text/plain' });
      const event = { target: { files: [file] } } as unknown as Event;

      component.onFileSelected(event);
      expect(component.bankCertificateFileName).toBeNull();
      expect(alertService.error).toHaveBeenCalledWith(
        'Solo se permiten archivos PDF',
      );
    });

    it('should reject file larger than 1MB', () => {
      const largeFile = new File(
        [''.padStart(1024 * 1024 + 1, 'x')],
        'large.pdf',
        { type: 'application/pdf' },
      );
      const event = { target: { files: [largeFile] } } as unknown as Event;

      component.onFileSelected(event);
      expect(component.bankCertificateFileName).toBeNull();
      expect(alertService.error).toHaveBeenCalledWith(
        'El archivo no debe superar 1MB',
      );
    });
  });

  describe('Initial Data Loading', () => {
    it('should load initial data correctly', () => {
      const initialData: InitialReportDocumentation = {
        id: 1,
        contractorContractId: 1,
        bankId: 1,
        bankAccountTypeId: 1,
        accountNumber: '*********',
        bankCertificateFileUrl: 'test.pdf',
        taxRegimeId: 1,
        epsId: 1,
        arlId: 1,
        pensionFundId: 1,
        hasDependents: false,
        hasHousingInterest: false,
        housingInterestAnnualPayment: 0,
        hasPrepaidMedicine: false,
        prepaidMedicineAnnualPayment: 0,
        hasAfcAccount: false,
        hasVoluntarySavings: false,
        afcAccountAnnualPayment: 0,
        voluntarySavingsAnnualPayment: 0,
      };

      component.initialData = initialData;
      component.ngOnChanges({
        initialData: {
          currentValue: initialData,
          previousValue: undefined,
          firstChange: true,
          isFirstChange: () => true,
        },
      });

      expect(component.form.get('bank')?.value).toBe(1);
      expect(component.form.get('accountType')?.value).toBe(1);
      expect(component.form.get('accountNumber')?.value).toBe('*********');
    });

    it('should handle undefined initial data', () => {
      component.ngOnChanges({
        initialData: {
          currentValue: undefined,
          previousValue: undefined,
          firstChange: true,
          isFirstChange: () => true,
        },
      });

      expect(component.form.get('bank')?.value).toBe('');
      expect(component.form.get('accountType')?.value).toBe('');
      expect(component.form.get('accountNumber')?.value).toBe('');
      expect(component.bankCertificateFileName).toBeNull();
    });
  });

  describe('Supervisor Mode', () => {
    it('should show readonly inputs in supervisor mode', () => {
      component.isSupervisor = true;
      fixture.detectChanges();
      expect(component.isSupervisor).toBeTrue();
    });
  });

  describe('File Download', () => {
    it('should open file URL in new window when available', () => {
      spyOn(window, 'open');
      component.initialData = {
        id: 1,
        contractorContractId: 1,
        bankId: 1,
        bankAccountTypeId: 1,
        accountNumber: '*********',
        bankCertificateFileUrl: 'http://example.com/file.pdf',
        taxRegimeId: 1,
        epsId: 1,
        arlId: 1,
        pensionFundId: 1,
        hasDependents: false,
        hasHousingInterest: false,
        housingInterestAnnualPayment: 0,
        hasPrepaidMedicine: false,
        prepaidMedicineAnnualPayment: 0,
        hasAfcAccount: false,
        hasVoluntarySavings: false,
        afcAccountAnnualPayment: 0,
        voluntarySavingsAnnualPayment: 0,
      };

      component.downloadFile();

      expect(window.open).toHaveBeenCalledWith(
        'http://example.com/file.pdf',
        '_blank',
      );
    });

    it('should not attempt to open file when url is not defined', () => {
      spyOn(window, 'open');
      component.initialData = {
        id: 1,
        contractorContractId: 1,
        bankId: 1,
        bankAccountTypeId: 1,
        accountNumber: '*********',
        bankCertificateFileUrl: undefined,
        taxRegimeId: 1,
        epsId: 1,
        arlId: 1,
        pensionFundId: 1,
        hasDependents: false,
        hasHousingInterest: false,
        housingInterestAnnualPayment: 0,
        hasPrepaidMedicine: false,
        prepaidMedicineAnnualPayment: 0,
        hasAfcAccount: false,
        hasVoluntarySavings: false,
        afcAccountAnnualPayment: 0,
        voluntarySavingsAnnualPayment: 0,
      };

      component.downloadFile();

      expect(window.open).not.toHaveBeenCalled();
    });
  });
});
