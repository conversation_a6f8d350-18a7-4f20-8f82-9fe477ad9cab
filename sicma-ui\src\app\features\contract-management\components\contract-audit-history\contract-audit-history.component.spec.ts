import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { MatCardModule } from '@angular/material/card';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { ContractAuditHistoryComponent } from './contract-audit-history.component';
import { ContractAuditHistoryService } from '@contract-management/services/contract-audit-history.service';
import { AlertService } from '@shared/services/alert.service';
import { of } from 'rxjs';
import { Contract } from '@contract-management/models/contract.model';

describe('ContractAuditHistoryComponent', () => {
  let component: ContractAuditHistoryComponent;
  let fixture: ComponentFixture<ContractAuditHistoryComponent>;
  let contractAuditHistoryServiceSpy: jasmine.SpyObj<ContractAuditHistoryService>;
  let alertServiceSpy: jasmine.SpyObj<AlertService>;

  beforeEach(async () => {
    contractAuditHistoryServiceSpy = jasmine.createSpyObj(
      'ContractAuditHistoryService',
      ['getByContractId'],
    );
    alertServiceSpy = jasmine.createSpyObj('AlertService', ['error']);

    contractAuditHistoryServiceSpy.getByContractId.and.returnValue(of([]));

    await TestBed.configureTestingModule({
      imports: [
        ContractAuditHistoryComponent,
        HttpClientTestingModule,
        MatCardModule,
        MatTableModule,
        MatIconModule,
        MatExpansionModule,
        MatProgressSpinnerModule,
        NoopAnimationsModule,
      ],
      providers: [
        {
          provide: ContractAuditHistoryService,
          useValue: contractAuditHistoryServiceSpy,
        },
        { provide: AlertService, useValue: alertServiceSpy },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ContractAuditHistoryComponent);
    component = fixture.componentInstance;
    component.contract = { id: 1 } as Contract;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load audit history', () => {
    component.loadAuditHistory();
    expect(contractAuditHistoryServiceSpy.getByContractId).toHaveBeenCalledWith(
      1,
    );
  });
});