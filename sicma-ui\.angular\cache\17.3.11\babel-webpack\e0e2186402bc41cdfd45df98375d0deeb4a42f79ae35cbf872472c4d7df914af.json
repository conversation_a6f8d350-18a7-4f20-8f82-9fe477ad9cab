{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { PeriodService } from './period.service';\ndescribe('PeriodService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/periods`;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [PeriodService]\n    });\n    service = TestBed.inject(PeriodService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  const mockPeriod = {\n    num_payment: 1,\n    payment: 1000000,\n    start_date: '2024-02-01',\n    end_date: '2024-02-29',\n    days_in_month: 29\n  };\n  describe('getCantPeriodsById', () => {\n    it('should return the count of periods by id', () => {\n      const mockCount = {\n        count: 12\n      };\n      service.getCantPeriodsById(1).subscribe(result => {\n        expect(result).toEqual(mockCount);\n      });\n      const req = httpMock.expectOne(`${apiUrl}cantidad/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockCount);\n    });\n    it('should handle error when getting count of periods', () => {\n      service.getCantPeriodsById(999).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}cantidad/999`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n  describe('create', () => {\n    const newPeriod = {\n      id_contrat: 1,\n      num_payment: 1\n    };\n    it('should create a new period', () => {\n      service.create(newPeriod).subscribe(period => {\n        expect(period).toEqual(mockPeriod);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newPeriod);\n      req.flush(mockPeriod);\n    });\n    it('should handle error when creating period', () => {\n      service.create(newPeriod).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "PeriodService", "describe", "service", "httpMock", "apiUrl", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockPeriod", "num_payment", "payment", "start_date", "end_date", "days_in_month", "mockCount", "count", "getCantPeriodsById", "subscribe", "result", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "error", "status", "ErrorEvent", "newPeriod", "id_contrat", "create", "period", "body"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\services\\period.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { ConsultPeriod } from '@contractor-dashboard/models/ConsultPeriod.model';\nimport { Period } from '@contractor-dashboard/models/Period.model';\nimport { environment } from '@env';\nimport { PeriodService } from './period.service';\n\ndescribe('PeriodService', () => {\n  let service: PeriodService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/periods`;\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [PeriodService],\n    });\n    service = TestBed.inject(PeriodService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  const mockPeriod: Period = {\n    num_payment: 1,\n    payment: 1000000,\n    start_date: '2024-02-01',\n    end_date: '2024-02-29',\n    days_in_month: 29,\n  };\n\n  describe('getCantPeriodsById', () => {\n    it('should return the count of periods by id', () => {\n      const mockCount = { count: 12 };\n\n      service.getCantPeriodsById(1).subscribe((result) => {\n        expect(result).toEqual(mockCount);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}cantidad/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockCount);\n    });\n\n    it('should handle error when getting count of periods', () => {\n      service.getCantPeriodsById(999).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}cantidad/999`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n\n  describe('create', () => {\n    const newPeriod: Omit<ConsultPeriod, 'id'> = {\n      id_contrat: 1,\n      num_payment: 1,\n    };\n\n    it('should create a new period', () => {\n      service.create(newPeriod).subscribe((period) => {\n        expect(period).toEqual(mockPeriod);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newPeriod);\n      req.flush(mockPeriod);\n    });\n\n    it('should handle error when creating period', () => {\n      service.create(newPeriod).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAG/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,aAAa,QAAQ,kBAAkB;AAEhDC,QAAQ,CAAC,eAAe,EAAE,MAAK;EAC7B,IAAIC,OAAsB;EAC1B,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,UAAU;EAE9CC,UAAU,CAAC,MAAK;IACdP,OAAO,CAACQ,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACX,uBAAuB,CAAC;MAClCY,SAAS,EAAE,CAACR,aAAa;KAC1B,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACW,MAAM,CAACT,aAAa,CAAC;IACvCG,QAAQ,GAAGL,OAAO,CAACW,MAAM,CAACZ,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFa,SAAS,CAAC,MAAK;IACbP,QAAQ,CAACQ,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACX,OAAO,CAAC,CAACY,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEF,MAAMC,UAAU,GAAW;IACzBC,WAAW,EAAE,CAAC;IACdC,OAAO,EAAE,OAAO;IAChBC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,YAAY;IACtBC,aAAa,EAAE;GAChB;EAEDnB,QAAQ,CAAC,oBAAoB,EAAE,MAAK;IAClCW,EAAE,CAAC,0CAA0C,EAAE,MAAK;MAClD,MAAMS,SAAS,GAAG;QAAEC,KAAK,EAAE;MAAE,CAAE;MAE/BpB,OAAO,CAACqB,kBAAkB,CAAC,CAAC,CAAC,CAACC,SAAS,CAAEC,MAAM,IAAI;QACjDZ,MAAM,CAACY,MAAM,CAAC,CAACC,OAAO,CAACL,SAAS,CAAC;MACnC,CAAC,CAAC;MAEF,MAAMM,GAAG,GAAGxB,QAAQ,CAACyB,SAAS,CAAC,GAAGxB,MAAM,YAAY,CAAC;MACrDS,MAAM,CAACc,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACX,SAAS,CAAC;IACtB,CAAC,CAAC;IAEFT,EAAE,CAAC,mDAAmD,EAAE,MAAK;MAC3DV,OAAO,CAACqB,kBAAkB,CAAC,GAAG,CAAC,CAACC,SAAS,CAAC;QACxCS,KAAK,EAAGA,KAAK,IAAI;UACfpB,MAAM,CAACoB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGxB,QAAQ,CAACyB,SAAS,CAAC,GAAGxB,MAAM,cAAc,CAAC;MACvDuB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjC,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB,MAAMmC,SAAS,GAA8B;MAC3CC,UAAU,EAAE,CAAC;MACbrB,WAAW,EAAE;KACd;IAEDJ,EAAE,CAAC,4BAA4B,EAAE,MAAK;MACpCV,OAAO,CAACoC,MAAM,CAACF,SAAS,CAAC,CAACZ,SAAS,CAAEe,MAAM,IAAI;QAC7C1B,MAAM,CAAC0B,MAAM,CAAC,CAACb,OAAO,CAACX,UAAU,CAAC;MACpC,CAAC,CAAC;MAEF,MAAMY,GAAG,GAAGxB,QAAQ,CAACyB,SAAS,CAACxB,MAAM,CAAC;MACtCS,MAAM,CAACc,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvClB,MAAM,CAACc,GAAG,CAACE,OAAO,CAACW,IAAI,CAAC,CAACd,OAAO,CAACU,SAAS,CAAC;MAC3CT,GAAG,CAACK,KAAK,CAACjB,UAAU,CAAC;IACvB,CAAC,CAAC;IAEFH,EAAE,CAAC,0CAA0C,EAAE,MAAK;MAClDV,OAAO,CAACoC,MAAM,CAACF,SAAS,CAAC,CAACZ,SAAS,CAAC;QAClCS,KAAK,EAAGA,KAAK,IAAI;UACfpB,MAAM,CAACoB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGxB,QAAQ,CAACyB,SAAS,CAACxB,MAAM,CAAC;MACtCuB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}