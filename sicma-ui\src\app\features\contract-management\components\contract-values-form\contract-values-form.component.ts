import { Cur<PERSON>cyPipe, DatePipe } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { CDPEntity } from '@contract-management/models/cdp-entity.model';
import { ContractValues } from '@contract-management/models/contract-values.model';
import { Contract } from '@contract-management/models/contract.model';
import { Entity } from '@contract-management/models/entity.model';
import { CdpEntityService } from '@contract-management/services/cdp-entity.service';
import { EntityService } from '@contract-management/services/entity.service';
import { AlertService } from '@shared/services/alert.service';
import { NgxCurrencyDirective } from 'ngx-currency';
import { debounceTime, forkJoin, startWith } from 'rxjs';
import { createDateRangeValidator } from './validators/date-range.validator';

@Component({
  selector: 'app-contract-values-form',
  templateUrl: './contract-values-form.component.html',
  styleUrl: './contract-values-form.component.scss',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatSlideToggleModule,
    MatIconModule,
    MatButtonModule,
    MatDatepickerModule,
    NgxCurrencyDirective,
    CurrencyPipe,
    DatePipe,
  ],
})
export class ContractValuesFormComponent implements OnInit {
  @Input() contract!: Contract;
  @Input() contractValue?: ContractValues;
  @Input() isInitialValue = false;
  @Output() valuesSubmitted = new EventEmitter<Omit<
    ContractValues,
    'id'
  > | null>();

  showFutureValidity = false;
  timeOnlyMode = false;
  moneyOnlyMode = false;

  valuesFormGroup: FormGroup = this.fb.group({
    numericValue: [null, Validators.min(1)],
    futureValidityValue: [null, Validators.min(0)],
    madsValue: [null, Validators.min(0)],
    otherValue: [null, Validators.min(0)],
    startDate: [null, [Validators.required]],
    endDate: [null, [Validators.required]],
    cdp: ['', [Validators.required]],
    rp: [''],
    subscriptionDate: [null, [Validators.required]],
    isOtherEntity: [{ value: false, disabled: true }],
    entityId: [null],
    cdpEntityId: [null as number | null, [Validators.required]],
  });

  entities: Entity[] = [];
  cdpEntities: CDPEntity[] = [];
  latestEndDate: Date | null | undefined = null;
  contractDuration: { days: number | null; months: number | null } = {
    days: null,
    months: null,
  };
  maxAllowedValue = 0;

  constructor(
    private readonly entityService: EntityService,
    private readonly cdpEntityService: CdpEntityService,
    private readonly alert: AlertService,
    private readonly fb: FormBuilder,
  ) {}

  ngOnInit(): void {
    this.loadInitialData();

    if (this.contractValue) {
      this.timeOnlyMode = !!this.contractValue.isTimeOnlyMode;
      this.moneyOnlyMode = !!this.contractValue.isMoneyOnlyMode;

      if (this.timeOnlyMode) {
        this.updateFormForTimeOnlyMode();
      } else if (this.moneyOnlyMode) {
        this.updateFormForMoneyOnlyMode();
      }
    }
  }

  toggleFutureValidity(): void {
    this.showFutureValidity = !this.showFutureValidity;
    const futureValidityValueControl = this.valuesFormGroup.get(
      'futureValidityValue',
    );

    if (!this.showFutureValidity) {
      futureValidityValueControl?.setValue(0);
    } else {
      const currentValue = futureValidityValueControl?.value || 0;
      if (currentValue === 0 && this.contractValue?.futureValidityValue) {
        futureValidityValueControl?.setValue(
          this.contractValue.futureValidityValue,
        );
      }
    }

    this.onOtherValueChange();
  }

  toggleTimeOnlyMode(): void {
    this.timeOnlyMode = !this.timeOnlyMode;

    if (this.timeOnlyMode) {
      this.moneyOnlyMode = false;
      this.updateFormForTimeOnlyMode();
    } else {
      this.updateFormForRegularMode();
    }
  }

  toggleMoneyOnlyMode(): void {
    this.moneyOnlyMode = !this.moneyOnlyMode;

    if (this.moneyOnlyMode) {
      this.timeOnlyMode = false;
      this.updateFormForMoneyOnlyMode();
    } else {
      this.updateFormForRegularMode();
    }
  }

  private updateFormForMoneyOnlyMode(): void {
    const startDate = this.contract?.contractValues?.[0]?.startDate
      ? new Date(this.contract.contractValues[0].startDate)
      : new Date();
    const endDate = this.contract?.contractValues?.[0]?.endDate
      ? new Date(this.contract.contractValues[0].endDate)
      : new Date();
    const subscriptionDate = new Date();

    this.valuesFormGroup.patchValue({
      startDate: startDate,
      endDate: endDate,
      subscriptionDate: subscriptionDate,
    });

    const startDateControl = this.valuesFormGroup.get('startDate');
    const endDateControl = this.valuesFormGroup.get('endDate');
    const subscriptionDateControl =
      this.valuesFormGroup.get('subscriptionDate');

    if (startDateControl) {
      startDateControl.clearValidators();
      startDateControl.setValue(null);
      startDateControl.updateValueAndValidity();
    }

    if (endDateControl) {
      endDateControl.clearValidators();
      endDateControl.setValue(null);
      endDateControl.updateValueAndValidity();
    }

    if (subscriptionDateControl) {
      subscriptionDateControl.clearValidators();
      subscriptionDateControl.setValue(null);
      subscriptionDateControl.updateValueAndValidity();
    }

    this.valuesFormGroup.get('madsValue')?.setValidators(Validators.min(0));
    this.valuesFormGroup.get('otherValue')?.setValidators(Validators.min(0));
    this.valuesFormGroup.get('numericValue')?.setValidators(Validators.min(1));
    this.valuesFormGroup.get('cdp')?.setValidators([Validators.required]);
    this.valuesFormGroup
      .get('cdpEntityId')
      ?.setValidators([Validators.required]);

    this.updateFormControlsValidity();
  }

  private updateFormForTimeOnlyMode(): void {
    this.valuesFormGroup.patchValue({
      madsValue: 0,
      otherValue: 0,
      numericValue: 0,
      cdp: '',
      cdpEntityId: null,
    });

    this.valuesFormGroup.get('madsValue')?.clearValidators();
    this.valuesFormGroup.get('otherValue')?.clearValidators();
    this.valuesFormGroup.get('numericValue')?.clearValidators();
    this.valuesFormGroup.get('cdp')?.clearValidators();
    this.valuesFormGroup.get('cdpEntityId')?.clearValidators();

    this.updateFormControlsValidity();
  }

  private updateFormForRegularMode(): void {
    this.valuesFormGroup.get('madsValue')?.setValidators(Validators.min(0));
    this.valuesFormGroup.get('otherValue')?.setValidators(Validators.min(0));
    this.valuesFormGroup.get('numericValue')?.setValidators(Validators.min(1));
    this.valuesFormGroup.get('cdp')?.setValidators([Validators.required]);
    this.valuesFormGroup
      .get('cdpEntityId')
      ?.setValidators([Validators.required]);

    const formValues = this.valuesFormGroup.getRawValue();
    const defaultUpdates: Record<string, string | number> = {};

    if (formValues.cdp === null || formValues.cdp === '') {
      defaultUpdates['cdp'] = '';
    }

    if (formValues.cdpEntityId === null && this.cdpEntities.length > 0) {
      defaultUpdates['cdpEntityId'] = this.cdpEntities[0].id;
    }

    if (Object.keys(defaultUpdates).length > 0) {
      this.valuesFormGroup.patchValue(defaultUpdates);
    }

    const madsValue = formValues.madsValue || 0;
    const otherValue = formValues.otherValue || 0;
    const numericValueControl = this.valuesFormGroup.get('numericValue');
    if (numericValueControl) {
      numericValueControl.setValue(madsValue + otherValue);
    }

    this.updateFormControlsValidity();
  }

  private updateFormControlsValidity(): void {
    Object.keys(this.valuesFormGroup.controls).forEach((key) => {
      const control = this.valuesFormGroup.get(key);
      if (control) {
        control.updateValueAndValidity();
      }
    });
  }

  private getLatestEndDate(): Date | null {
    if (!this.contract?.contractValues?.length) {
      return null;
    }

    const valuesToConsider = this.contractValue
      ? this.contract.contractValues.filter(
          (cv) => cv.id !== this.contractValue?.id,
        )
      : this.contract.contractValues;

    const endDates = valuesToConsider
      .map((cv) => (cv.endDate ? new Date(cv.endDate) : null))
      .filter((d): d is Date => d instanceof Date && !isNaN(d.getTime()));

    if (endDates.length === 0) {
      return null;
    }

    return new Date(Math.max(...endDates.map((d) => d.getTime())));
  }

  private loadInitialData(): void {
    if (
      this.contractValue?.futureValidityValue !== undefined &&
      this.contractValue.futureValidityValue > 0
    ) {
      this.showFutureValidity = true;
    }

    this.latestEndDate = this.getLatestEndDate();

    const requests = {
      entities: this.entityService.getAll(),
      cdpEntities: this.cdpEntityService.getAll(),
    };

    forkJoin(requests).subscribe({
      next: (response) => {
        this.entities = response.entities;
        this.cdpEntities = response.cdpEntities;

        this.setupValidators();

        this.setupDateValidationListeners();
        this.setupDateRangeListener();
        this.setupNumericValueCalculation();
        this.setupOtherValueListener();
        this.setupMaxValueValidation();

        this.listenToFormChanges();

        if (this.contractValue) {
          const futureValidityValue =
            this.contractValue.futureValidityValue || 0;

          this.valuesFormGroup.patchValue({
            ...this.contractValue,
            entityId: this.contractValue.entity?.id,
            cdpEntityId: this.contractValue.cdpEntity?.id,
            startDate: this.contractValue.startDate
              ? new Date(this.contractValue.startDate)
              : null,
            endDate: this.contractValue.endDate
              ? new Date(this.contractValue.endDate)
              : null,
            subscriptionDate: new Date(this.contractValue.subscriptionDate),
            futureValidityValue: futureValidityValue,
          });

          if (futureValidityValue > 0) {
            this.showFutureValidity = true;
          }

          this.validateDates();
        }
      },
      error: (error) => {
        this.alert.error(
          error.error?.detail ?? 'Error al cargar los datos iniciales',
        );
      },
    });
  }

  private getInitialContractValue(): ContractValues | undefined {
    if (!this.contract?.contractValues?.length) {
      return undefined;
    }

    return this.contract.contractValues.reduce(
      (lowest: ContractValues | undefined, current: ContractValues) => {
        if (
          !lowest ||
          (current.id !== undefined &&
            lowest.id !== undefined &&
            current.id < lowest.id)
        ) {
          return current;
        }
        return lowest;
      },
      undefined,
    );
  }

  private setupMaxValueValidation(): void {
    if (!this.contract) {
      this.maxAllowedValue = 0;
      return;
    }

    const initialContractValue = this.getInitialContractValue();
    if (initialContractValue) {
      this.maxAllowedValue = initialContractValue.numericValue / 2;
    } else {
      this.maxAllowedValue = 0;
    }
  }

  private setupDateValidationListeners(): void {
    const subscriptionDateControl =
      this.valuesFormGroup.get('subscriptionDate');
    const startDateControl = this.valuesFormGroup.get('startDate');

    subscriptionDateControl?.valueChanges.subscribe((value) => {
      if (value && startDateControl?.value) {
        startDateControl.markAsTouched();
        startDateControl.updateValueAndValidity({ emitEvent: false });
      }
    });

    startDateControl?.valueChanges.subscribe((value) => {
      if (value && subscriptionDateControl?.value) {
        subscriptionDateControl.markAsTouched();
        subscriptionDateControl.updateValueAndValidity({ emitEvent: false });
      }
    });
  }

  private setupValidators(): void {
    const dateRangeValidator = createDateRangeValidator({
      latestEndDate: this.latestEndDate,
      isEdit: !!this.contractValue,
      subscriptionDate: this.valuesFormGroup.get('subscriptionDate')?.value,
      valuesForm: this.valuesFormGroup,
    });

    this.valuesFormGroup
      .get('startDate')
      ?.setValidators([Validators.required, dateRangeValidator]);
    this.valuesFormGroup
      .get('endDate')
      ?.setValidators([Validators.required, dateRangeValidator]);
    this.valuesFormGroup
      .get('subscriptionDate')
      ?.setValidators([Validators.required, dateRangeValidator]);

    this.valuesFormGroup
      .get('startDate')
      ?.updateValueAndValidity({ emitEvent: false });
    this.valuesFormGroup
      .get('endDate')
      ?.updateValueAndValidity({ emitEvent: false });
    this.valuesFormGroup
      .get('subscriptionDate')
      ?.updateValueAndValidity({ emitEvent: false });
  }

  private setupDateRangeListener(): void {
    const startDateControl = this.valuesFormGroup.get('startDate');
    const endDateControl = this.valuesFormGroup.get('endDate');

    startDateControl?.valueChanges.subscribe(() => {
      this.updateContractDuration();
      if (endDateControl?.value) {
        endDateControl.updateValueAndValidity({ emitEvent: false });
      }
    });

    endDateControl?.valueChanges.subscribe(() => {
      this.updateContractDuration();
      if (startDateControl?.value) {
        startDateControl.updateValueAndValidity({ emitEvent: false });
      }
    });

    if (startDateControl?.value && endDateControl?.value) {
      this.updateContractDuration();
    }
  }

  private updateContractDuration(): void {
    const startDate = this.valuesFormGroup.get('startDate')?.value;
    const endDate = this.valuesFormGroup.get('endDate')?.value;
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);

      let totalDays = 0;
      const tempDate = new Date(start);

      while (tempDate <= end) {
        if (tempDate.getDate() !== 31) {
          totalDays++;
        }
        tempDate.setDate(tempDate.getDate() + 1);
      }

      const yearDiff = end.getFullYear() - start.getFullYear();
      const monthDiff = end.getMonth() - start.getMonth();
      let exactMonths = yearDiff * 12 + monthDiff;

      if (end.getDate() < start.getDate()) {
        exactMonths--;
      }

      const months = Math.max(exactMonths, 0);

      this.contractDuration = {
        days: totalDays,
        months: months,
      };
    } else {
      this.contractDuration = {
        days: null,
        months: null,
      };
    }
  }

  private listenToFormChanges(): void {
    this.valuesFormGroup.valueChanges.pipe(debounceTime(300)).subscribe(() => {
      try {
        if (this.valuesFormGroup.valid) {
          const contractValues = this.getContractValuesFromForm();
          this.valuesSubmitted.emit(contractValues);
        } else {
          this.valuesSubmitted.emit(null);
        }
      } catch {
        this.valuesSubmitted.emit(null);
      }
    });
  }

  getContractValuesFromForm(): Omit<ContractValues, 'id'> {
    const formValues = this.valuesFormGroup.getRawValue();

    if (this.timeOnlyMode) {
      return {
        numericValue: 0,
        madsValue: 0,
        otherValue: 0,
        futureValidityValue: 0,
        cdp: '',
        cdpEntityId: null,
        rp: '',
        startDate: formValues.startDate?.toISOString().slice(0, 10),
        endDate: formValues.endDate?.toISOString().slice(0, 10),
        subscriptionDate: formValues.subscriptionDate
          .toISOString()
          .slice(0, 10),
        contractId: this.contract?.id,
        isOtherEntity: false,
        isTimeOnlyMode: true,
        isMoneyOnlyMode: false,
        entityId: undefined,
        cdpEntity: null,
        entity: undefined,
      };
    } else if (this.moneyOnlyMode) {
      return {
        ...formValues,
        rp: formValues.rp || undefined,
        entityId:
          formValues.isOtherEntity && formValues.entityId
            ? formValues.entityId
            : undefined,
        cdpEntityId: formValues.cdpEntityId,
        startDate: null,
        endDate: null,
        subscriptionDate: null,
        contractId: this.contract?.id,
        isTimeOnlyMode: false,
        isMoneyOnlyMode: true,
      };
    }

    return {
      ...formValues,
      rp: formValues.rp || undefined,
      entityId:
        formValues.isOtherEntity && formValues.entityId
          ? formValues.entityId
          : undefined,
      cdpEntityId: formValues.cdpEntityId,
      startDate: formValues.startDate?.toISOString().slice(0, 10),
      endDate: formValues.endDate?.toISOString().slice(0, 10),
      subscriptionDate: formValues.subscriptionDate.toISOString().slice(0, 10),
      contractId: this.contract?.id,
      isTimeOnlyMode: false,
      isMoneyOnlyMode: false,
    };
  }

  onOtherValueChange(): void {
    const otherValueControl = this.valuesFormGroup.get('otherValue');
    const isOtherEntityControl = this.valuesFormGroup.get('isOtherEntity');
    const entityIdControl = this.valuesFormGroup.get('entityId');

    if (otherValueControl && isOtherEntityControl && entityIdControl) {
      const otherValue = otherValueControl.value || 0;
      if (otherValue > 0) {
        isOtherEntityControl.setValue(true);
        entityIdControl.setValidators([Validators.required]);
        entityIdControl.enable();
      } else {
        isOtherEntityControl.setValue(false);
        entityIdControl.clearValidators();
        entityIdControl.disable();
      }
      entityIdControl.updateValueAndValidity();
    }
  }

  private setupNumericValueCalculation(): void {
    const madsValueControl = this.valuesFormGroup.get('madsValue');
    const otherValueControl = this.valuesFormGroup.get('otherValue');
    const numericValueControl = this.valuesFormGroup.get('numericValue');
    const futureValidityValueControl = this.valuesFormGroup.get(
      'futureValidityValue',
    );

    if (
      madsValueControl &&
      otherValueControl &&
      numericValueControl &&
      futureValidityValueControl
    ) {
      const calculateTotal = () => {
        const madsValue = madsValueControl.value || 0;
        const otherValue = otherValueControl.value || 0;
        const total = madsValue + otherValue;

        numericValueControl.setValue(total, { emitEvent: false });

        if (this.timeOnlyMode) {
          numericValueControl.setErrors(null);
          return;
        }

        if (total <= 0) {
          numericValueControl.setErrors({ min: true });
        } else if (this.maxAllowedValue > 0 && !this.isInitialValue) {
          if (
            this.contract?.contractValues &&
            this.contract.contractValues.length > 0
          ) {
            const initialContractValue = this.getInitialContractValue();

            if (!initialContractValue) {
              numericValueControl.setErrors(null);
              return;
            }

            const existingAdditions = this.contract.contractValues
              .filter(
                (cv) =>
                  cv.id !== initialContractValue.id &&
                  (!this.contractValue || cv.id !== this.contractValue.id),
              )
              .reduce((sum, cv) => sum + cv.numericValue, 0);

            const totalAdditions = existingAdditions + total;

            if (totalAdditions > this.maxAllowedValue) {
              numericValueControl.setErrors({ exceedsMaxValue: true });
              numericValueControl.markAsTouched();
            } else {
              numericValueControl.setErrors(null);
            }
          } else {
            numericValueControl.setErrors(null);
          }
        } else {
          numericValueControl.setErrors(null);
        }
      };

      madsValueControl.valueChanges
        .pipe(startWith(madsValueControl.value))
        .subscribe(calculateTotal);

      otherValueControl.valueChanges
        .pipe(startWith(otherValueControl.value))
        .subscribe(calculateTotal);

      futureValidityValueControl.valueChanges
        .pipe(startWith(futureValidityValueControl.value))
        .subscribe(calculateTotal);
    }
  }

  private setupOtherValueListener(): void {
    const otherValueControl = this.valuesFormGroup.get('otherValue');
    const isOtherEntityControl = this.valuesFormGroup.get('isOtherEntity');
    const entityIdControl = this.valuesFormGroup.get('entityId');

    if (otherValueControl && isOtherEntityControl && entityIdControl) {
      otherValueControl.valueChanges
        .pipe(debounceTime(300))
        .subscribe((value) => {
          const otherValue = value || 0;
          if (otherValue > 0) {
            isOtherEntityControl.setValue(true);
            entityIdControl.setValidators([Validators.required]);
          } else {
            isOtherEntityControl.setValue(false);
            entityIdControl.setValue(null);
            entityIdControl.clearValidators();
          }
          entityIdControl.updateValueAndValidity();
        });
    }
  }

  isValid(): boolean {
    if (this.moneyOnlyMode) {
      const numericValueValid =
        this.valuesFormGroup.get('numericValue')?.valid ?? false;
      const cdpValid = this.valuesFormGroup.get('cdp')?.valid ?? false;
      const cdpEntityIdValid =
        this.valuesFormGroup.get('cdpEntityId')?.valid ?? false;

      return numericValueValid && cdpValid && cdpEntityIdValid;
    }

    return this.valuesFormGroup.valid;
  }

  getValue(): Omit<ContractValues, 'id'> {
    Object.keys(this.valuesFormGroup.controls).forEach((key) => {
      const control = this.valuesFormGroup.get(key);
      if (control) {
        control.markAsTouched();
      }
    });

    try {
      return this.getContractValuesFromForm();
    } catch {
      const formValues = this.valuesFormGroup.getRawValue();

      const defaultCdpEntity =
        this.cdpEntities.length > 0 ? this.cdpEntities[0] : null;

      return {
        numericValue: formValues.numericValue || 0,
        madsValue: formValues.madsValue || 0,
        otherValue: formValues.otherValue || 0,
        futureValidityValue: formValues.futureValidityValue || 0,
        cdp: formValues.cdp || '',
        cdpEntityId: this.timeOnlyMode
          ? null
          : formValues.cdpEntityId || defaultCdpEntity?.id || 1,
        startDate: this.moneyOnlyMode
          ? null
          : formValues.startDate?.toISOString().slice(0, 10) ||
            new Date().toISOString().slice(0, 10),
        endDate: this.moneyOnlyMode
          ? null
          : formValues.endDate?.toISOString().slice(0, 10),
        subscriptionDate: this.moneyOnlyMode
          ? null
          : formValues.subscriptionDate?.toISOString().slice(0, 10) ||
            new Date().toISOString().slice(0, 10),
        contractId: this.contract?.id,
        isOtherEntity: formValues.isOtherEntity || false,
        isTimeOnlyMode: this.timeOnlyMode,
        isMoneyOnlyMode: this.moneyOnlyMode,
        entityId: formValues.entityId,
        rp: formValues.rp || '',
        cdpEntity: this.timeOnlyMode
          ? null
          : defaultCdpEntity || {
              id: 1,
              name: 'Default',
            },
        entity: formValues.entityId
          ? this.entities.find((e) => e.id === formValues.entityId)
          : undefined,
      };
    }
  }

  private validateDates(): void {
    const startDateControl = this.valuesFormGroup.get('startDate');
    const subscriptionDateControl =
      this.valuesFormGroup.get('subscriptionDate');

    if (startDateControl?.value && subscriptionDateControl?.value) {
      startDateControl.markAsTouched();
      subscriptionDateControl.markAsTouched();
      startDateControl.updateValueAndValidity({ emitEvent: false });
      subscriptionDateControl.updateValueAndValidity({ emitEvent: false });
    }
  }
}
