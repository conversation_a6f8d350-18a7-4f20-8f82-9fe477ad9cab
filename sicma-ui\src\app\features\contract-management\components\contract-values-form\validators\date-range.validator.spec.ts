import { FormBuilder, FormGroup } from '@angular/forms';
import {
  createDateRangeValidator,
  DateRangeValidatorConfig,
} from './date-range.validator';

describe('DateRangeValidator', () => {
  let fb: FormBuilder;
  let form: FormGroup;
  let config: DateRangeValidatorConfig;
  let currentYear: number;

  beforeEach(() => {
    fb = new FormBuilder();
    form = fb.group({
      startDate: [null],
      endDate: [null],
      subscriptionDate: [null],
    });

    currentYear = new Date().getFullYear();
    config = {
      latestEndDate: null,
      subscriptionDate: null,
      isEdit: false,
      valuesForm: form,
    };
  });

  it('should not validate when value is null', () => {
    const validator = createDateRangeValidator(config);
    const control = form.get('startDate')!;

    const result = validator(control);
    expect(result).toBeNull();
  });

  describe('Start Date Validation', () => {
    it('should not return error when date is not in current year', () => {
      const validator = createDateRangeValidator(config);
      const lastYear = new Date();
      lastYear.setFullYear(currentYear - 1);
      form.get('startDate')?.setValue(lastYear);
      const result = validator(form.get('startDate')!);
      expect(result).toBeNull();
    });

    it('should return dateTooEarly error when date is before latestEndDate', () => {
      const currentYearDate = new Date();
      currentYearDate.setFullYear(currentYear);
      config.latestEndDate = new Date(currentYearDate.setMonth(5));
      const validator = createDateRangeValidator(config);
      const startDate = new Date(currentYearDate);
      startDate.setMonth(4);
      form.get('startDate')?.setValue(startDate);
      const result = validator(form.get('startDate')!);
      expect(result).toEqual({ dateTooEarly: true });
    });

    it('should return startDateBeforeSubscription error when date is before subscription date', () => {
      const currentYearDate = new Date();
      currentYearDate.setFullYear(currentYear);

      config.subscriptionDate = new Date(currentYear, 5, 15);

      form.get('subscriptionDate')?.setValue(new Date(currentYear, 5, 15));

      const startDate = new Date(currentYear, 4, 15);
      form.get('startDate')?.setValue(startDate);

      const validator = createDateRangeValidator(config);
      const result = validator(form.get('startDate')!);

      expect(result).toEqual({ startDateBeforeSubscription: true });
    });

    it('should return null when start date is valid', () => {
      const currentYearDate = new Date();
      currentYearDate.setFullYear(currentYear);
      config.latestEndDate = new Date(currentYearDate.setMonth(4));
      config.subscriptionDate = new Date(currentYearDate);
      const validator = createDateRangeValidator(config);
      const startDate = new Date(currentYearDate);
      startDate.setMonth(5);
      form.get('startDate')?.setValue(startDate);
      const result = validator(form.get('startDate')!);
      expect(result).toBeNull();
    });

    it('should not return dateTooEarly error in edit mode', () => {
      const currentYearDate = new Date();
      currentYearDate.setFullYear(currentYear);
      config.latestEndDate = new Date(currentYearDate.setMonth(5));
      config.isEdit = true;
      const validator = createDateRangeValidator(config);
      const startDate = new Date(currentYearDate);
      startDate.setMonth(4);
      form.get('startDate')?.setValue(startDate);
      const result = validator(form.get('startDate')!);
      expect(result?.['dateTooEarly']).toBeUndefined();
    });
  });

  describe('End Date Validation', () => {
    it('should return endDateBeforeStartDate error when end date is before start date', () => {
      const currentYearDate = new Date();
      currentYearDate.setFullYear(currentYear);
      const validator = createDateRangeValidator(config);
      form.get('startDate')?.setValue(new Date(currentYearDate.setMonth(5)));
      form.get('endDate')?.setValue(new Date(currentYearDate.setMonth(4)));
      const result = validator(form.get('endDate')!);
      expect(result).toEqual({ endDateBeforeStartDate: true });
    });

    it('should return null when end date is valid', () => {
      const currentYearDate = new Date();
      currentYearDate.setFullYear(currentYear);
      const validator = createDateRangeValidator(config);
      form.get('startDate')?.setValue(new Date(currentYearDate.setMonth(5)));
      form.get('endDate')?.setValue(new Date(currentYearDate.setMonth(6)));
      const result = validator(form.get('endDate')!);
      expect(result).toBeNull();
    });
  });

  describe('Subscription Date Validation', () => {
    it('should return subscriptionDateAfterStartDate error when subscription date is after start date', () => {
      const validator = createDateRangeValidator(config);
      form.get('startDate')?.setValue(new Date('2024-06-01'));
      form.get('subscriptionDate')?.setValue(new Date('2024-07-01'));
      const result = validator(form.get('subscriptionDate')!);
      expect(result).toEqual({ subscriptionDateAfterStartDate: true });
    });

    it('should return null when subscription date is valid', () => {
      const validator = createDateRangeValidator(config);
      form.get('startDate')?.setValue(new Date('2024-06-01'));
      form.get('subscriptionDate')?.setValue(new Date('2024-05-01'));
      const result = validator(form.get('subscriptionDate')!);
      expect(result).toBeNull();
    });
  });
});