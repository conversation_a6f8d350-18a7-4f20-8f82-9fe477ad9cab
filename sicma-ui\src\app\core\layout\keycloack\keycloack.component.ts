import {
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  OnInit,
  ViewEncapsulation,
} from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '@core/auth/services/auth.service';
import { AlertService } from '@shared/services/alert.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { UserService } from '@core/auth/services/user.service';
import { finalize } from 'rxjs';

interface CustomEvent {
  detail: string;
}

interface ContractorToken {
  token: string;
}

@Component({
  selector: 'app-keycloack',
  templateUrl: './keycloack.component.html',
  styleUrl: './keycloack.component.scss',
  standalone: true,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  encapsulation: ViewEncapsulation.None,
})
export class KeycloackComponent implements OnInit {
  constructor(
    private authService: AuthService,
    private router: Router,
    private spinner: NgxSpinnerService,
    private userService: UserService,
    private alert: AlertService,
  ) {}

  ngOnInit(): void {
    this.monitorToken();
  }

  onTokenResolved(event: CustomEvent | Event): void {
    const keycloakEvent = event as CustomEvent;
    this.spinner.show();

    this.authService
      .login(keycloakEvent.detail)
      .pipe(finalize(() => this.spinner.hide()))
      .subscribe({
        next: (success) => {
          if (success) {
            this.handleUserProfiles();
          } else {
            this.handleContractorLogin(keycloakEvent.detail);
          }
        },
        error: () => {
          this.alert.error('Error al intentar autenticarse');
        },
      });
  }

  private handleUserProfiles(): void {
    const profiles = this.authService.getUserProfiles();

    if (profiles.some((profile) => profile.profile_name === 'ADMINISTRATOR')) {
      this.router.navigate(['/contratistas']);
    } else if (profiles.some((profile) => profile.profile_name === 'SUPERVISOR')) {
      this.router.navigate(['/revisar-informes']);
    } else if (profiles.some((profile) => profile.profile_name === 'CONTRACTOR')) {
      this.router.navigate(['/mis-contratos']);
    } else if (
      profiles.some((profile) => profile.profile_name === 'CONTRACT-MANAGER')
    ) {
      this.router.navigate(['/contratos']);
    } else {
      this.router.navigate(['/']);
    }

    this.alert.success('Autenticación exitosa');
  }

  private handleContractorLogin(token: string): void {
    const contractorToken: ContractorToken = { token };

    this.userService
      .createLogin(contractorToken)
      .pipe(finalize(() => this.spinner.hide()))
      .subscribe({
        next: (success) => {
          if (success) {
            this.authService
              .login(token)
              .pipe(finalize(() => this.spinner.hide()))
              .subscribe({
                next: (success) => {
                  if (success) {
                    this.router.navigate(['/mis-contratos']);
                    this.alert.success('Autenticación exitosa');
                  } else {
                    this.alert.error('Credenciales inválidas');
                  }
                },
                error: () => {
                  this.alert.error('Error al intentar autenticarse');
                },
              });
          } else {
            this.alert.error('Credenciales inválidas');
          }
        },
        error: (err) => {
          if (err.status === 400) {
            if (err.error.detail !== "Error: Autenticacion key(401)") {
            this.alert.error(err.error.detail);
            }
          } else if (err.status === 404) {
            this.alert.error('El contratista no fue encontrado');
          } else {
            this.alert.error(err.error?.detail ?? 'Error al intentar autenticarse');
          }
        },
      });
  }

  getUrlLogOut(): void {
    this.router.navigate(['/login']);
  }

  monitorToken(): void {
    setInterval(() => {
      const token = localStorage.getItem('keycloak-token');
      if (!token) {
        localStorage.clear();
        this.router.navigate(['/login']);
      }
    }, 1000);
  }
}
