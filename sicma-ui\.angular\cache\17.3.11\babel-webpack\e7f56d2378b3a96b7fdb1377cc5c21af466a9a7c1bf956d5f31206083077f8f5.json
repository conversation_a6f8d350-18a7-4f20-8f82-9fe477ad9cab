{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { ArlService } from './arl.service';\ndescribe('ArlService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/arls`;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [ArlService]\n    });\n    service = TestBed.inject(ArlService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  const mockArl = {\n    id: 1,\n    name: 'Sura'\n  };\n  describe('getAll', () => {\n    it('should return all ARLs', () => {\n      const mockArls = [mockArl];\n      service.getAll().subscribe(arls => {\n        expect(arls).toEqual(mockArls);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockArls);\n    });\n    it('should handle error when getting all ARLs', () => {\n      service.getAll().subscribe({\n        error: error => {\n          expect(error.status).toBe(500);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Server error'), {\n        status: 500\n      });\n    });\n  });\n  describe('getById', () => {\n    it('should return an ARL by id', () => {\n      service.getById(1).subscribe(arl => {\n        expect(arl).toEqual(mockArl);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockArl);\n    });\n    it('should handle error when getting ARL by id', () => {\n      service.getById(999).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/999`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n  describe('create', () => {\n    const newArl = {\n      name: 'New ARL'\n    };\n    it('should create a new ARL', () => {\n      service.create(newArl).subscribe(arl => {\n        expect(arl).toEqual(mockArl);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newArl);\n      req.flush(mockArl);\n    });\n    it('should handle error when creating ARL', () => {\n      service.create(newArl).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n  describe('update', () => {\n    const updateData = {\n      name: 'Updated Name'\n    };\n    it('should update an ARL', () => {\n      service.update(1, updateData).subscribe(arl => {\n        expect(arl).toEqual({\n          ...mockArl,\n          ...updateData\n        });\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush({\n        ...mockArl,\n        ...updateData\n      });\n    });\n    it('should handle error when updating ARL', () => {\n      service.update(1, updateData).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n  describe('delete', () => {\n    it('should delete an ARL', () => {\n      service.delete(1).subscribe(response => {\n        expect(response).toBeNull();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n    it('should handle error when deleting ARL', () => {\n      service.delete(1).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n  describe('getByName', () => {\n    it('should return an ARL by name', () => {\n      const name = 'Sura';\n      service.getByName(name).subscribe(arl => {\n        expect(arl).toEqual(mockArl);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockArl);\n    });\n    it('should handle error when getting ARL by name', () => {\n      const name = 'NonExistent';\n      service.getByName(name).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "ArlService", "describe", "service", "httpMock", "apiUrl", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockArl", "id", "name", "mockArls", "getAll", "subscribe", "arls", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "error", "status", "ErrorEvent", "getById", "arl", "newArl", "create", "body", "updateData", "update", "delete", "response", "toBeNull", "getByName"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\services\\arl.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { ARL } from '@contractor-dashboard/models/arl.model';\nimport { environment } from '@env';\nimport { ArlService } from './arl.service';\n\ndescribe('ArlService', () => {\n  let service: ArlService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/arls`;\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [ArlService],\n    });\n    service = TestBed.inject(ArlService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  const mockArl: ARL = {\n    id: 1,\n    name: 'Sura',\n  };\n\n  describe('getAll', () => {\n    it('should return all ARLs', () => {\n      const mockArls = [mockArl];\n\n      service.getAll().subscribe((arls) => {\n        expect(arls).toEqual(mockArls);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockArls);\n    });\n\n    it('should handle error when getting all ARLs', () => {\n      service.getAll().subscribe({\n        error: (error) => {\n          expect(error.status).toBe(500);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Server error'), { status: 500 });\n    });\n  });\n\n  describe('getById', () => {\n    it('should return an ARL by id', () => {\n      service.getById(1).subscribe((arl) => {\n        expect(arl).toEqual(mockArl);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockArl);\n    });\n\n    it('should handle error when getting ARL by id', () => {\n      service.getById(999).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/999`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n\n  describe('create', () => {\n    const newArl = {\n      name: 'New ARL',\n    };\n\n    it('should create a new ARL', () => {\n      service.create(newArl).subscribe((arl) => {\n        expect(arl).toEqual(mockArl);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newArl);\n      req.flush(mockArl);\n    });\n\n    it('should handle error when creating ARL', () => {\n      service.create(newArl).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n\n  describe('update', () => {\n    const updateData = {\n      name: 'Updated Name',\n    };\n\n    it('should update an ARL', () => {\n      service.update(1, updateData).subscribe((arl) => {\n        expect(arl).toEqual({ ...mockArl, ...updateData });\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush({ ...mockArl, ...updateData });\n    });\n\n    it('should handle error when updating ARL', () => {\n      service.update(1, updateData).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete an ARL', () => {\n      service.delete(1).subscribe((response) => {\n        expect(response).toBeNull();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n\n    it('should handle error when deleting ARL', () => {\n      service.delete(1).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n\n  describe('getByName', () => {\n    it('should return an ARL by name', () => {\n      const name = 'Sura';\n\n      service.getByName(name).subscribe((arl) => {\n        expect(arl).toEqual(mockArl);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockArl);\n    });\n\n    it('should handle error when getting ARL by name', () => {\n      const name = 'NonExistent';\n\n      service.getByName(name).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,UAAU,QAAQ,eAAe;AAE1CC,QAAQ,CAAC,YAAY,EAAE,MAAK;EAC1B,IAAIC,OAAmB;EACvB,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,OAAO;EAE3CC,UAAU,CAAC,MAAK;IACdP,OAAO,CAACQ,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACX,uBAAuB,CAAC;MAClCY,SAAS,EAAE,CAACR,UAAU;KACvB,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACW,MAAM,CAACT,UAAU,CAAC;IACpCG,QAAQ,GAAGL,OAAO,CAACW,MAAM,CAACZ,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFa,SAAS,CAAC,MAAK;IACbP,QAAQ,CAACQ,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACX,OAAO,CAAC,CAACY,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEF,MAAMC,OAAO,GAAQ;IACnBC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE;GACP;EAEDhB,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBW,EAAE,CAAC,wBAAwB,EAAE,MAAK;MAChC,MAAMM,QAAQ,GAAG,CAACH,OAAO,CAAC;MAE1Bb,OAAO,CAACiB,MAAM,EAAE,CAACC,SAAS,CAAEC,IAAI,IAAI;QAClCR,MAAM,CAACQ,IAAI,CAAC,CAACC,OAAO,CAACJ,QAAQ,CAAC;MAChC,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCS,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,QAAQ,CAAC;IACrB,CAAC,CAAC;IAEFN,EAAE,CAAC,2CAA2C,EAAE,MAAK;MACnDV,OAAO,CAACiB,MAAM,EAAE,CAACC,SAAS,CAAC;QACzBS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,cAAc,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC5D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBW,EAAE,CAAC,4BAA4B,EAAE,MAAK;MACpCV,OAAO,CAAC8B,OAAO,CAAC,CAAC,CAAC,CAACZ,SAAS,CAAEa,GAAG,IAAI;QACnCpB,MAAM,CAACoB,GAAG,CAAC,CAACX,OAAO,CAACP,OAAO,CAAC;MAC9B,CAAC,CAAC;MAEF,MAAMQ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAI,CAAC;MAC7CS,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACb,OAAO,CAAC;IACpB,CAAC,CAAC;IAEFH,EAAE,CAAC,4CAA4C,EAAE,MAAK;MACpDV,OAAO,CAAC8B,OAAO,CAAC,GAAG,CAAC,CAACZ,SAAS,CAAC;QAC7BS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,MAAM,CAAC;MAC/CmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB,MAAMiC,MAAM,GAAG;MACbjB,IAAI,EAAE;KACP;IAEDL,EAAE,CAAC,yBAAyB,EAAE,MAAK;MACjCV,OAAO,CAACiC,MAAM,CAACD,MAAM,CAAC,CAACd,SAAS,CAAEa,GAAG,IAAI;QACvCpB,MAAM,CAACoB,GAAG,CAAC,CAACX,OAAO,CAACP,OAAO,CAAC;MAC9B,CAAC,CAAC;MAEF,MAAMQ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCS,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCd,MAAM,CAACU,GAAG,CAACE,OAAO,CAACW,IAAI,CAAC,CAACd,OAAO,CAACY,MAAM,CAAC;MACxCX,GAAG,CAACK,KAAK,CAACb,OAAO,CAAC;IACpB,CAAC,CAAC;IAEFH,EAAE,CAAC,uCAAuC,EAAE,MAAK;MAC/CV,OAAO,CAACiC,MAAM,CAACD,MAAM,CAAC,CAACd,SAAS,CAAC;QAC/BS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB,MAAMoC,UAAU,GAAG;MACjBpB,IAAI,EAAE;KACP;IAEDL,EAAE,CAAC,sBAAsB,EAAE,MAAK;MAC9BV,OAAO,CAACoC,MAAM,CAAC,CAAC,EAAED,UAAU,CAAC,CAACjB,SAAS,CAAEa,GAAG,IAAI;QAC9CpB,MAAM,CAACoB,GAAG,CAAC,CAACX,OAAO,CAAC;UAAE,GAAGP,OAAO;UAAE,GAAGsB;QAAU,CAAE,CAAC;MACpD,CAAC,CAAC;MAEF,MAAMd,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAI,CAAC;MAC7CS,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCd,MAAM,CAACU,GAAG,CAACE,OAAO,CAACW,IAAI,CAAC,CAACd,OAAO,CAACe,UAAU,CAAC;MAC5Cd,GAAG,CAACK,KAAK,CAAC;QAAE,GAAGb,OAAO;QAAE,GAAGsB;MAAU,CAAE,CAAC;IAC1C,CAAC,CAAC;IAEFzB,EAAE,CAAC,uCAAuC,EAAE,MAAK;MAC/CV,OAAO,CAACoC,MAAM,CAAC,CAAC,EAAED,UAAU,CAAC,CAACjB,SAAS,CAAC;QACtCS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAI,CAAC;MAC7CmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBW,EAAE,CAAC,sBAAsB,EAAE,MAAK;MAC9BV,OAAO,CAACqC,MAAM,CAAC,CAAC,CAAC,CAACnB,SAAS,CAAEoB,QAAQ,IAAI;QACvC3B,MAAM,CAAC2B,QAAQ,CAAC,CAACC,QAAQ,EAAE;MAC7B,CAAC,CAAC;MAEF,MAAMlB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAI,CAAC;MAC7CS,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;IAEFhB,EAAE,CAAC,uCAAuC,EAAE,MAAK;MAC/CV,OAAO,CAACqC,MAAM,CAAC,CAAC,CAAC,CAACnB,SAAS,CAAC;QAC1BS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAI,CAAC;MAC7CmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,WAAW,EAAE,MAAK;IACzBW,EAAE,CAAC,8BAA8B,EAAE,MAAK;MACtC,MAAMK,IAAI,GAAG,MAAM;MAEnBf,OAAO,CAACwC,SAAS,CAACzB,IAAI,CAAC,CAACG,SAAS,CAAEa,GAAG,IAAI;QACxCpB,MAAM,CAACoB,GAAG,CAAC,CAACX,OAAO,CAACP,OAAO,CAAC;MAC9B,CAAC,CAAC;MAEF,MAAMQ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,SAASa,IAAI,EAAE,CAAC;MACxDJ,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACb,OAAO,CAAC;IACpB,CAAC,CAAC;IAEFH,EAAE,CAAC,8CAA8C,EAAE,MAAK;MACtD,MAAMK,IAAI,GAAG,aAAa;MAE1Bf,OAAO,CAACwC,SAAS,CAACzB,IAAI,CAAC,CAACG,SAAS,CAAC;QAChCS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,SAASa,IAAI,EAAE,CAAC;MACxDM,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}