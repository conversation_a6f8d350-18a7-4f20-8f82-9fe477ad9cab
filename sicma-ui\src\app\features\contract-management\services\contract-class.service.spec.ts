import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { ContractClass } from '@contract-management/models/contract-class.model';
import { environment } from '@env';
import { ContractClassService } from './contract-class.service';

describe('ContractClassService', () => {
  let service: ContractClassService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/contract_class`;

  const mockContractClass: ContractClass = {
    id: 1,
    name: 'Test Contract Class',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ContractClassService],
    });
    service = TestBed.inject(ContractClassService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all contract classes', () => {
      const mockContractClasses = [mockContractClass];

      service.getAll().subscribe((contractClasses) => {
        expect(contractClasses).toEqual(mockContractClasses);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockContractClasses);
    });
  });

  describe('getById', () => {
    it('should return a contract class by id', () => {
      const id = 1;

      service.getById(id).subscribe((contractClass) => {
        expect(contractClass).toEqual(mockContractClass);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockContractClass);
    });
  });

  describe('create', () => {
    it('should create a new contract class', () => {
      const newContractClass: Omit<ContractClass, 'id'> = {
        name: 'New Contract Class',
      };

      service.create(newContractClass).subscribe((contractClass) => {
        expect(contractClass).toEqual(mockContractClass);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newContractClass);
      req.flush(mockContractClass);
    });
  });

  describe('update', () => {
    it('should update a contract class', () => {
      const id = 1;
      const updateData: Partial<ContractClass> = {
        name: 'Updated Contract Class',
      };

      service.update(id, updateData).subscribe((contractClass) => {
        expect(contractClass).toEqual(mockContractClass);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockContractClass);
    });
  });

  describe('delete', () => {
    it('should delete a contract class', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });

  describe('getByName', () => {
    it('should return a contract class by name', () => {
      const name = 'Test Contract Class';

      service.getByName(name).subscribe((contractClass) => {
        expect(contractClass).toEqual(mockContractClass);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockContractClass);
    });
  });
});