import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { ContractType } from '@contract-management/models/contract-type.model';
import { environment } from '@env';
import { ContractTypeService } from './contract-type.service';

describe('ContractTypeService', () => {
  let service: ContractTypeService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/contract-types`;

  const mockContractType: ContractType = {
    id: 1,
    name: 'Test Contract Type',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ContractTypeService],
    });
    service = TestBed.inject(ContractTypeService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all contract types', () => {
      const mockContractTypes = [mockContractType];

      service.getAll().subscribe((contractTypes) => {
        expect(contractTypes).toEqual(mockContractTypes);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockContractTypes);
    });
  });

  describe('getById', () => {
    it('should return a contract type by id', () => {
      const id = 1;

      service.getById(id).subscribe((contractType) => {
        expect(contractType).toEqual(mockContractType);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockContractType);
    });
  });

  describe('create', () => {
    it('should create a new contract type', () => {
      const newContractType: Omit<ContractType, 'id'> = {
        name: 'New Contract Type',
      };

      service.create(newContractType).subscribe((contractType) => {
        expect(contractType).toEqual(mockContractType);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newContractType);
      req.flush(mockContractType);
    });
  });

  describe('update', () => {
    it('should update a contract type', () => {
      const id = 1;
      const updateData: Partial<ContractType> = {
        name: 'Updated Contract Type',
      };

      service.update(id, updateData).subscribe((contractType) => {
        expect(contractType).toEqual(mockContractType);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockContractType);
    });
  });

  describe('delete', () => {
    it('should delete a contract type', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });

  describe('getByName', () => {
    it('should return a contract type by name', () => {
      const name = 'Test Contract Type';

      service.getByName(name).subscribe((contractType) => {
        expect(contractType).toEqual(mockContractType);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockContractType);
    });
  });
});