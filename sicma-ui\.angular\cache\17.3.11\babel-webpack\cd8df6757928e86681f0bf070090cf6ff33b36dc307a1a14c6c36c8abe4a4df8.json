{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/Github/SICMA/sicma-ui/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { MatDialog } from '@angular/material/dialog';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatTableModule } from '@angular/material/table';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { of } from 'rxjs';\nimport { CcpDialogComponent } from './ccp-dialog/ccp-dialog.component';\nimport { CcpsListComponent } from './ccps-list.component';\ndescribe('CcpsListComponent', () => {\n  let component;\n  let fixture;\n  let dialog;\n  const mockCcps = [{\n    id: 1,\n    expenseObjectUseCcp: 'Object 1',\n    expenseObjectDescription: 'Description 1',\n    value: 1000,\n    contractId: 1\n  }, {\n    id: 2,\n    expenseObjectUseCcp: 'Object 2',\n    expenseObjectDescription: 'Description 2',\n    value: 2000,\n    contractId: 1\n  }];\n  const mockContract = {\n    id: 1,\n    contractNumber: 123,\n    monthlyPayment: 1000000,\n    object: 'Test Contract',\n    rup: true,\n    secopCode: 123456,\n    addition: false,\n    cession: false,\n    settled: false,\n    contractTypeId: 1,\n    statusId: 1,\n    causesSelectionId: 1,\n    managementSupportId: 1,\n    contractClassId: 1,\n    ccps: mockCcps\n  };\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    const dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);\n    dialogSpy.open.and.returnValue({\n      afterClosed: () => of(undefined)\n    });\n    yield TestBed.configureTestingModule({\n      imports: [CcpsListComponent, HttpClientTestingModule, BrowserAnimationsModule, MatTableModule, MatPaginatorModule, MatSortModule],\n      providers: [{\n        provide: MatDialog,\n        useValue: dialogSpy\n      }]\n    }).compileComponents();\n    fixture = TestBed.createComponent(CcpsListComponent);\n    component = fixture.componentInstance;\n    dialog = TestBed.inject(MatDialog);\n  }));\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should initialize with contract ccps', () => {\n    component.contract = mockContract;\n    component.ngOnInit();\n    expect(component.dataSource.data).toEqual(mockCcps);\n  });\n  it('should initialize with empty array when contract has no ccps', () => {\n    component.contract = {\n      ...mockContract,\n      ccps: undefined\n    };\n    component.ngOnInit();\n    expect(component.dataSource.data).toEqual([]);\n  });\n  it('should set up sort after view init', () => {\n    component.contract = mockContract;\n    component.ngOnInit();\n    fixture.detectChanges();\n    component.ngAfterViewInit();\n    expect(component.dataSource.sort).toBeTruthy();\n  });\n  it('should open dialog for new ccp', () => {\n    component.contract = mockContract;\n    component.ngOnInit();\n    const newCcp = {\n      id: 3,\n      expenseObjectUseCcp: 'New Object',\n      expenseObjectDescription: 'New Description',\n      value: 3000,\n      contractId: 1\n    };\n    const mockDialogRef = {\n      afterClosed: () => of(newCcp)\n    };\n    dialog.open.and.returnValue(mockDialogRef);\n    component.openCcpForm();\n    expect(dialog.open).toHaveBeenCalledWith(CcpDialogComponent, {\n      width: '580px',\n      data: {\n        ccp: undefined,\n        contractId: mockContract.id,\n        totalCcpValue: 3000\n      }\n    });\n    expect(component.dataSource.data.length).toBe(3);\n    expect(component.dataSource.data).toContain(newCcp);\n  });\n  it('should open dialog for existing ccp', () => {\n    component.contract = mockContract;\n    component.ngOnInit();\n    const updatedCcp = {\n      ...mockCcps[0],\n      expenseObjectDescription: 'Updated Description'\n    };\n    const mockDialogRef = {\n      afterClosed: () => of(updatedCcp)\n    };\n    dialog.open.and.returnValue(mockDialogRef);\n    component.openCcpForm(mockCcps[0]);\n    expect(dialog.open).toHaveBeenCalledWith(CcpDialogComponent, {\n      width: '580px',\n      data: {\n        ccp: mockCcps[0],\n        contractId: mockContract.id,\n        totalCcpValue: 3000\n      }\n    });\n    expect(component.dataSource.data[0]).toEqual(updatedCcp);\n  });\n  it('should not update data when dialog is closed without result', () => {\n    component.contract = mockContract;\n    component.ngOnInit();\n    const initialData = [...component.dataSource.data];\n    const mockDialogRef = {\n      afterClosed: () => of(undefined)\n    };\n    dialog.open.and.returnValue(mockDialogRef);\n    component.openCcpForm();\n    expect(component.dataSource.data).toEqual(initialData);\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "TestBed", "MatDialog", "MatPaginatorModule", "MatSortModule", "MatTableModule", "BrowserAnimationsModule", "of", "CcpDialogComponent", "CcpsListComponent", "describe", "component", "fixture", "dialog", "mockCcps", "id", "expenseObjectUseCcp", "expenseObjectDescription", "value", "contractId", "mockContract", "contractNumber", "monthlyPayment", "object", "rup", "secopCode", "addition", "cession", "settled", "contractTypeId", "statusId", "causesSelectionId", "managementSupportId", "contractClassId", "ccps", "beforeEach", "_asyncToGenerator", "dialogSpy", "jasmine", "createSpyObj", "open", "and", "returnValue", "afterClosed", "undefined", "configureTestingModule", "imports", "providers", "provide", "useValue", "compileComponents", "createComponent", "componentInstance", "inject", "it", "expect", "toBeTruthy", "contract", "ngOnInit", "dataSource", "data", "toEqual", "detectChanges", "ngAfterViewInit", "sort", "newCcp", "mockDialogRef", "openCcpForm", "toHaveBeenCalledWith", "width", "ccp", "totalCcpValue", "length", "toBe", "toContain", "updatedCcp", "initialData"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\components\\ccps-list\\ccps-list.component.spec.ts"], "sourcesContent": ["import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { ComponentFixture, TestBed } from '@angular/core/testing';\nimport { MatDialog, MatDialogRef } from '@angular/material/dialog';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatTableModule } from '@angular/material/table';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { CCP } from '@contract-management/models/ccp.model';\nimport { Contract } from '@contract-management/models/contract.model';\nimport { of } from 'rxjs';\nimport { CcpDialogComponent } from './ccp-dialog/ccp-dialog.component';\nimport { CcpsListComponent } from './ccps-list.component';\n\ndescribe('CcpsListComponent', () => {\n  let component: CcpsListComponent;\n  let fixture: ComponentFixture<CcpsListComponent>;\n  let dialog: jasmine.SpyObj<MatDialog>;\n\n  const mockCcps: CCP[] = [\n    {\n      id: 1,\n      expenseObjectUseCcp: 'Object 1',\n      expenseObjectDescription: 'Description 1',\n      value: 1000,\n      contractId: 1,\n    },\n    {\n      id: 2,\n      expenseObjectUseCcp: 'Object 2',\n      expenseObjectDescription: 'Description 2',\n      value: 2000,\n      contractId: 1,\n    },\n  ];\n\n  const mockContract: Contract = {\n    id: 1,\n    contractNumber: 123,\n    monthlyPayment: 1000000,\n    object: 'Test Contract',\n    rup: true,\n    secopCode: 123456,\n    addition: false,\n    cession: false,\n    settled: false,\n    contractTypeId: 1,\n    statusId: 1,\n    causesSelectionId: 1,\n    managementSupportId: 1,\n    contractClassId: 1,\n    ccps: mockCcps,\n  };\n\n  beforeEach(async () => {\n    const dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);\n    dialogSpy.open.and.returnValue({\n      afterClosed: () => of(undefined),\n    } as MatDialogRef<unknown>);\n\n    await TestBed.configureTestingModule({\n      imports: [\n        CcpsListComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n        MatTableModule,\n        MatPaginatorModule,\n        MatSortModule,\n      ],\n      providers: [{ provide: MatDialog, useValue: dialogSpy }],\n    }).compileComponents();\n\n    fixture = TestBed.createComponent(CcpsListComponent);\n    component = fixture.componentInstance;\n    dialog = TestBed.inject(MatDialog) as jasmine.SpyObj<MatDialog>;\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should initialize with contract ccps', () => {\n    component.contract = mockContract;\n    component.ngOnInit();\n    expect(component.dataSource.data).toEqual(mockCcps);\n  });\n\n  it('should initialize with empty array when contract has no ccps', () => {\n    component.contract = { ...mockContract, ccps: undefined };\n    component.ngOnInit();\n    expect(component.dataSource.data).toEqual([]);\n  });\n\n  it('should set up sort after view init', () => {\n    component.contract = mockContract;\n    component.ngOnInit();\n    fixture.detectChanges();\n    component.ngAfterViewInit();\n\n    expect(component.dataSource.sort).toBeTruthy();\n  });\n\n  it('should open dialog for new ccp', () => {\n    component.contract = mockContract;\n    component.ngOnInit();\n\n    const newCcp: CCP = {\n      id: 3,\n      expenseObjectUseCcp: 'New Object',\n      expenseObjectDescription: 'New Description',\n      value: 3000,\n      contractId: 1,\n    };\n\n    const mockDialogRef = {\n      afterClosed: () => of(newCcp),\n    } as MatDialogRef<unknown>;\n\n    dialog.open.and.returnValue(mockDialogRef);\n\n    component.openCcpForm();\n\n    expect(dialog.open).toHaveBeenCalledWith(CcpDialogComponent, {\n      width: '580px',\n      data: {\n        ccp: undefined,\n        contractId: mockContract.id,\n        totalCcpValue: 3000,\n      },\n    });\n\n    expect(component.dataSource.data.length).toBe(3);\n    expect(component.dataSource.data).toContain(newCcp);\n  });\n\n  it('should open dialog for existing ccp', () => {\n    component.contract = mockContract;\n    component.ngOnInit();\n\n    const updatedCcp: CCP = {\n      ...mockCcps[0],\n      expenseObjectDescription: 'Updated Description',\n    };\n\n    const mockDialogRef = {\n      afterClosed: () => of(updatedCcp),\n    } as MatDialogRef<unknown>;\n\n    dialog.open.and.returnValue(mockDialogRef);\n\n    component.openCcpForm(mockCcps[0]);\n\n    expect(dialog.open).toHaveBeenCalledWith(CcpDialogComponent, {\n      width: '580px',\n      data: {\n        ccp: mockCcps[0],\n        contractId: mockContract.id,\n        totalCcpValue: 3000,\n      },\n    });\n\n    expect(component.dataSource.data[0]).toEqual(updatedCcp);\n  });\n\n  it('should not update data when dialog is closed without result', () => {\n    component.contract = mockContract;\n    component.ngOnInit();\n    const initialData = [...component.dataSource.data];\n\n    const mockDialogRef = {\n      afterClosed: () => of(undefined),\n    } as MatDialogRef<unknown>;\n\n    dialog.open.and.returnValue(mockDialogRef);\n\n    component.openCcpForm();\n\n    expect(component.dataSource.data).toEqual(initialData);\n  });\n});"], "mappings": ";AAAA,SAASA,uBAAuB,QAAQ,8BAA8B;AACtE,SAA2BC,OAAO,QAAQ,uBAAuB;AACjE,SAASC,SAAS,QAAsB,0BAA0B;AAClE,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,uBAAuB,QAAQ,sCAAsC;AAG9E,SAASC,EAAE,QAAQ,MAAM;AACzB,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,iBAAiB,QAAQ,uBAAuB;AAEzDC,QAAQ,CAAC,mBAAmB,EAAE,MAAK;EACjC,IAAIC,SAA4B;EAChC,IAAIC,OAA4C;EAChD,IAAIC,MAAiC;EAErC,MAAMC,QAAQ,GAAU,CACtB;IACEC,EAAE,EAAE,CAAC;IACLC,mBAAmB,EAAE,UAAU;IAC/BC,wBAAwB,EAAE,eAAe;IACzCC,KAAK,EAAE,IAAI;IACXC,UAAU,EAAE;GACb,EACD;IACEJ,EAAE,EAAE,CAAC;IACLC,mBAAmB,EAAE,UAAU;IAC/BC,wBAAwB,EAAE,eAAe;IACzCC,KAAK,EAAE,IAAI;IACXC,UAAU,EAAE;GACb,CACF;EAED,MAAMC,YAAY,GAAa;IAC7BL,EAAE,EAAE,CAAC;IACLM,cAAc,EAAE,GAAG;IACnBC,cAAc,EAAE,OAAO;IACvBC,MAAM,EAAE,eAAe;IACvBC,GAAG,EAAE,IAAI;IACTC,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,KAAK;IACdC,cAAc,EAAE,CAAC;IACjBC,QAAQ,EAAE,CAAC;IACXC,iBAAiB,EAAE,CAAC;IACpBC,mBAAmB,EAAE,CAAC;IACtBC,eAAe,EAAE,CAAC;IAClBC,IAAI,EAAEpB;GACP;EAEDqB,UAAU,eAAAC,iBAAA,CAAC,aAAW;IACpB,MAAMC,SAAS,GAAGC,OAAO,CAACC,YAAY,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,CAAC;IAC7DF,SAAS,CAACG,IAAI,CAACC,GAAG,CAACC,WAAW,CAAC;MAC7BC,WAAW,EAAEA,CAAA,KAAMpC,EAAE,CAACqC,SAAS;KACP,CAAC;IAE3B,MAAM3C,OAAO,CAAC4C,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CACPrC,iBAAiB,EACjBT,uBAAuB,EACvBM,uBAAuB,EACvBD,cAAc,EACdF,kBAAkB,EAClBC,aAAa,CACd;MACD2C,SAAS,EAAE,CAAC;QAAEC,OAAO,EAAE9C,SAAS;QAAE+C,QAAQ,EAAEZ;MAAS,CAAE;KACxD,CAAC,CAACa,iBAAiB,EAAE;IAEtBtC,OAAO,GAAGX,OAAO,CAACkD,eAAe,CAAC1C,iBAAiB,CAAC;IACpDE,SAAS,GAAGC,OAAO,CAACwC,iBAAiB;IACrCvC,MAAM,GAAGZ,OAAO,CAACoD,MAAM,CAACnD,SAAS,CAA8B;EACjE,CAAC,EAAC;EAEFoD,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAAC5C,SAAS,CAAC,CAAC6C,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,sCAAsC,EAAE,MAAK;IAC9C3C,SAAS,CAAC8C,QAAQ,GAAGrC,YAAY;IACjCT,SAAS,CAAC+C,QAAQ,EAAE;IACpBH,MAAM,CAAC5C,SAAS,CAACgD,UAAU,CAACC,IAAI,CAAC,CAACC,OAAO,CAAC/C,QAAQ,CAAC;EACrD,CAAC,CAAC;EAEFwC,EAAE,CAAC,8DAA8D,EAAE,MAAK;IACtE3C,SAAS,CAAC8C,QAAQ,GAAG;MAAE,GAAGrC,YAAY;MAAEc,IAAI,EAAEU;IAAS,CAAE;IACzDjC,SAAS,CAAC+C,QAAQ,EAAE;IACpBH,MAAM,CAAC5C,SAAS,CAACgD,UAAU,CAACC,IAAI,CAAC,CAACC,OAAO,CAAC,EAAE,CAAC;EAC/C,CAAC,CAAC;EAEFP,EAAE,CAAC,oCAAoC,EAAE,MAAK;IAC5C3C,SAAS,CAAC8C,QAAQ,GAAGrC,YAAY;IACjCT,SAAS,CAAC+C,QAAQ,EAAE;IACpB9C,OAAO,CAACkD,aAAa,EAAE;IACvBnD,SAAS,CAACoD,eAAe,EAAE;IAE3BR,MAAM,CAAC5C,SAAS,CAACgD,UAAU,CAACK,IAAI,CAAC,CAACR,UAAU,EAAE;EAChD,CAAC,CAAC;EAEFF,EAAE,CAAC,gCAAgC,EAAE,MAAK;IACxC3C,SAAS,CAAC8C,QAAQ,GAAGrC,YAAY;IACjCT,SAAS,CAAC+C,QAAQ,EAAE;IAEpB,MAAMO,MAAM,GAAQ;MAClBlD,EAAE,EAAE,CAAC;MACLC,mBAAmB,EAAE,YAAY;MACjCC,wBAAwB,EAAE,iBAAiB;MAC3CC,KAAK,EAAE,IAAI;MACXC,UAAU,EAAE;KACb;IAED,MAAM+C,aAAa,GAAG;MACpBvB,WAAW,EAAEA,CAAA,KAAMpC,EAAE,CAAC0D,MAAM;KACJ;IAE1BpD,MAAM,CAAC2B,IAAI,CAACC,GAAG,CAACC,WAAW,CAACwB,aAAa,CAAC;IAE1CvD,SAAS,CAACwD,WAAW,EAAE;IAEvBZ,MAAM,CAAC1C,MAAM,CAAC2B,IAAI,CAAC,CAAC4B,oBAAoB,CAAC5D,kBAAkB,EAAE;MAC3D6D,KAAK,EAAE,OAAO;MACdT,IAAI,EAAE;QACJU,GAAG,EAAE1B,SAAS;QACdzB,UAAU,EAAEC,YAAY,CAACL,EAAE;QAC3BwD,aAAa,EAAE;;KAElB,CAAC;IAEFhB,MAAM,CAAC5C,SAAS,CAACgD,UAAU,CAACC,IAAI,CAACY,MAAM,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;IAChDlB,MAAM,CAAC5C,SAAS,CAACgD,UAAU,CAACC,IAAI,CAAC,CAACc,SAAS,CAACT,MAAM,CAAC;EACrD,CAAC,CAAC;EAEFX,EAAE,CAAC,qCAAqC,EAAE,MAAK;IAC7C3C,SAAS,CAAC8C,QAAQ,GAAGrC,YAAY;IACjCT,SAAS,CAAC+C,QAAQ,EAAE;IAEpB,MAAMiB,UAAU,GAAQ;MACtB,GAAG7D,QAAQ,CAAC,CAAC,CAAC;MACdG,wBAAwB,EAAE;KAC3B;IAED,MAAMiD,aAAa,GAAG;MACpBvB,WAAW,EAAEA,CAAA,KAAMpC,EAAE,CAACoE,UAAU;KACR;IAE1B9D,MAAM,CAAC2B,IAAI,CAACC,GAAG,CAACC,WAAW,CAACwB,aAAa,CAAC;IAE1CvD,SAAS,CAACwD,WAAW,CAACrD,QAAQ,CAAC,CAAC,CAAC,CAAC;IAElCyC,MAAM,CAAC1C,MAAM,CAAC2B,IAAI,CAAC,CAAC4B,oBAAoB,CAAC5D,kBAAkB,EAAE;MAC3D6D,KAAK,EAAE,OAAO;MACdT,IAAI,EAAE;QACJU,GAAG,EAAExD,QAAQ,CAAC,CAAC,CAAC;QAChBK,UAAU,EAAEC,YAAY,CAACL,EAAE;QAC3BwD,aAAa,EAAE;;KAElB,CAAC;IAEFhB,MAAM,CAAC5C,SAAS,CAACgD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO,CAACc,UAAU,CAAC;EAC1D,CAAC,CAAC;EAEFrB,EAAE,CAAC,6DAA6D,EAAE,MAAK;IACrE3C,SAAS,CAAC8C,QAAQ,GAAGrC,YAAY;IACjCT,SAAS,CAAC+C,QAAQ,EAAE;IACpB,MAAMkB,WAAW,GAAG,CAAC,GAAGjE,SAAS,CAACgD,UAAU,CAACC,IAAI,CAAC;IAElD,MAAMM,aAAa,GAAG;MACpBvB,WAAW,EAAEA,CAAA,KAAMpC,EAAE,CAACqC,SAAS;KACP;IAE1B/B,MAAM,CAAC2B,IAAI,CAACC,GAAG,CAACC,WAAW,CAACwB,aAAa,CAAC;IAE1CvD,SAAS,CAACwD,WAAW,EAAE;IAEvBZ,MAAM,CAAC5C,SAAS,CAACgD,UAAU,CAACC,IAAI,CAAC,CAACC,OAAO,CAACe,WAAW,CAAC;EACxD,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}