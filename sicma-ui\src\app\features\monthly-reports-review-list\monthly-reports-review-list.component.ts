import { <PERSON><PERSON><PERSON>cy<PERSON><PERSON>e, DatePipe } from '@angular/common';
import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule, Sort } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MonthlyReportDialogComponent } from '@contractor-dashboard/components/monthly-reports-tab/monthly-report-dialog/monthly-report-dialog.component';
import { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';
import { AuthService } from '@core/auth/services/auth.service';
import { AlertService } from '@shared/services/alert.service';
import { SupervisorService } from '@supervisor-management/services/supervisor.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { finalize } from 'rxjs';
import { MonthlyReportSupervisorExportModel } from '@contractor-dashboard/models/monthly_report_supervisor_export.model';

@Component({
  selector: 'app-monthly-reports-review-list',
  templateUrl: './monthly-reports-review-list.component.html',
  styleUrl: './monthly-reports-review-list.component.scss',
  standalone: true,
  imports: [
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    MatTooltipModule,
    DatePipe,
    CurrencyPipe,
  ],
})
export class MonthlyReportsReviewListComponent
  implements OnInit, AfterViewInit
{
  displayedColumns: string[] = [
    'reportNumber',
    'contractNumber',
    'contractYear',
    'contractorFullName',
    'startDate',
    'endDate',
    'contractDependency',
    'contractGroup',
    'totalValue',
    'currentReviewStatus',
    'actions',
  ];
  dataSource = new MatTableDataSource<MonthlyReportSupervisorExportModel>();

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  supervisorFullName = '';

  constructor(
    private readonly dialog: MatDialog,
    private readonly monthlyReportService: MonthlyReportService,
    private readonly spinner: NgxSpinnerService,
    private readonly alert: AlertService,
    private readonly supervisorService: SupervisorService,
    private readonly authService: AuthService,
  ) {}

  ngOnInit(): void {
    this.loadMonthlyReports();
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;

    this.dataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'currentReviewStatus':
          if (item.review_status_name?.toLowerCase().includes('pendiente')) {
            return '0' + item.review_status_name;
          }
          return '1' + (item.review_status_name || '');
        default:
          return item[
            property as keyof MonthlyReportSupervisorExportModel
          ] as string;
      }
    };
  }

  loadMonthlyReports(): void {
    const currentUser = this.authService.getCurrentUser();
    if (!currentUser) {
      this.alert.error('No se encontró información del usuario actual');
      return;
    }

    this.spinner.show();
    this.supervisorService
      .getByEmail(currentUser.username)
      .pipe(finalize(() => this.spinner.hide()))
      .subscribe({
        next: (supervisor) => {
          this.supervisorFullName = supervisor.fullName || '';
          this.monthlyReportService
            .getFilteredReportsBySupervisorEmail(currentUser.username)
            .subscribe({
              next: (reports) => {
                this.dataSource.data = reports;

                this.setDefaultSort();
              },
              error: (error) => {
                this.alert.error(
                  error.error?.detail ?? 'Error al cargar los informes',
                );
              },
            });
        },
        error: (error) => {
          this.alert.error(
            error.error?.detail ?? 'Error al cargar información del supervisor',
          );
        },
      });
  }

  setDefaultSort(): void {
    setTimeout(() => {
      if (this.sort) {
        const statusSort: Sort = {
          active: 'currentReviewStatus',
          direction: 'asc',
        };
        this.sort.active = statusSort.active;
        this.sort.direction = statusSort.direction;
        this.sort.sortChange.emit(statusSort);

        this.dataSource.sort = this.sort;
      }
    });
  }

  applyFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  openReportDetails(report: MonthlyReportSupervisorExportModel): void {
    if (!report) {
      this.alert.error('No se encontró información del informe');
      return;
    }

    const reportId = report.report_id;
    if (!reportId) {
      this.alert.error('No se encontró el identificador del informe');
      return;
    }

    this.spinner.show();
    this.monthlyReportService
      .getById(reportId)
      .pipe(finalize(() => this.spinner.hide()))
      .subscribe({
        next: (fullReport) => {
          if (!fullReport.currentReviewStatus) {
            fullReport.currentReviewStatus = {
              id: report.review_status_id ?? 0,
              name: report.review_status_name ?? '',
            };
          } else {
            fullReport.currentReviewStatus.id = report.review_status_id ?? 0;
            fullReport.currentReviewStatus.name =
              report.review_status_name ?? '';
          }

          const dialogRef = this.dialog.open(MonthlyReportDialogComponent, {
            width: '90vw',
            height: '90vh',
            maxWidth: '100vw',
            maxHeight: '100vh',
            data: { report: fullReport },
          });

          dialogRef.afterClosed().subscribe((result) => {
            if (result) {
              this.loadMonthlyReports();
            }
          });
        },
        error: (error) => {
          this.alert.error(
            error.error?.detail ?? 'Error al cargar el informe completo',
          );
        },
      });
  }
}
