import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ObligationsDialogComponent } from './obligations-dialog.component';
import { Contract } from '@contract-management/models/contract.model';

describe('ObligationsDialogComponent', () => {
  let component: ObligationsDialogComponent;
  let fixture: ComponentFixture<ObligationsDialogComponent>;
  let dialogRefSpy: jasmine.SpyObj<MatDialogRef<ObligationsDialogComponent>>;

  const mockContract: Partial<Contract> = {
    id: 1,
    contractNumber: 123,
    object: 'Test Contract',
    rup: false,
    secopCode: 12345,
    addition: false,
    cession: false,
    settled: false,
    causesSelectionId: 1,
    managementSupportId: 1,
    contractClassId: 1,
    monthlyPayment: 1000000,
    obligations: [
      { id: 1, name: 'Obligation 1', contractId: 1 },
      { id: 2, name: 'Obligation 2', contractId: 1 },
    ],
  };

  beforeEach(() => {
    dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close']);

    TestBed.configureTestingModule({
      imports: [
        ObligationsDialogComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
      ],
      providers: [
        { provide: MatDialogRef, useValue: dialogRefSpy },
        { provide: MAT_DIALOG_DATA, useValue: mockContract },
      ],
    });
    fixture = TestBed.createComponent(ObligationsDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should be initialized with the correct contract data', () => {
    expect(component.contract).toEqual(
      jasmine.objectContaining({
        id: 1,
        contractNumber: 123,
        object: 'Test Contract',
      }),
    );
  });

  it('should initialize actionPerformed as false', () => {
    const privateComponent = component as unknown as {
      actionPerformed: boolean;
    };
    expect(privateComponent.actionPerformed).toBeFalse();
  });

  it('should set actionPerformed to true when onObligationChanged is called', () => {
    component.onObligationChanged();
    const privateComponent = component as unknown as {
      actionPerformed: boolean;
    };
    expect(privateComponent.actionPerformed).toBeTrue();
  });

  it('should close the dialog with action:true when changes were made', () => {
    component.onObligationChanged();
    component.onClose();
    expect(dialogRefSpy.close).toHaveBeenCalledWith({ action: true });
  });

  it('should close the dialog with action:false when no changes were made', () => {
    component.onClose();
    expect(dialogRefSpy.close).toHaveBeenCalledWith({ action: false });
  });

  it('should pass the contract to the obligations list component', () => {
    fixture.detectChanges();
    const obligationsListEl = fixture.nativeElement.querySelector(
      'app-obligations-list',
    );
    expect(obligationsListEl).toBeTruthy();
  });
});