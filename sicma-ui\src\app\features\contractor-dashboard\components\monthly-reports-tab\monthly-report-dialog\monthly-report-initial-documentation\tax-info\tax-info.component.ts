import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButton, MatIconButton } from '@angular/material/button';
import { MatOption } from '@angular/material/core';
import { MatError, MatFormField, MatLabel } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput } from '@angular/material/input';
import { MatSelect } from '@angular/material/select';
import { MatTooltip } from '@angular/material/tooltip';
import { InitialReportDocumentation } from '@contractor-dashboard/models/initial-report-documentation.model';
import { TaxRegime } from '@contractor-dashboard/models/tax-regime.model';
import { TaxRegimeService } from '@contractor-dashboard/services/tax-regime.service';
import { AlertService } from '@shared/services/alert.service';
import {
  fileSizeValidator,
  pdfFileValidator,
} from '@shared/validators/file.validators';

@Component({
  selector: 'app-tax-info',
  templateUrl: './tax-info.component.html',
  styleUrl: './tax-info.component.scss',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatIcon,
    MatFormField,
    MatLabel,
    MatSelect,
    MatOption,
    MatInput,
    MatError,
    MatButton,
    MatIconButton,
    MatTooltip,
  ],
})
export class TaxInfoComponent implements OnInit, OnChanges {
  @Input() initialData?: InitialReportDocumentation;
  @Input() isSupervisor = false;
  @Output() formChange = new EventEmitter<void>();

  form: FormGroup = this.fb.group({
    regimeType: ['', Validators.required],
    regimeSupportFile: [null, [fileSizeValidator(), pdfFileValidator()]],
  });

  taxFormFile: File | null = null;
  taxFormFileName: string | null = null;
  taxRegimes: TaxRegime[] = [];

  constructor(
    private fb: FormBuilder,
    private taxRegimeService: TaxRegimeService,
    private alert: AlertService,
  ) {}

  ngOnInit(): void {
    this.loadTaxRegimes();
    this.form.valueChanges.subscribe(() => {
      this.formChange.emit();
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['initialData']?.currentValue) {
      const data = changes['initialData'].currentValue;
      this.form.patchValue({
        regimeType: data.taxRegimeId,
      });
    }
  }

  loadTaxRegimes(): void {
    this.taxRegimeService.getAll().subscribe({
      next: (regimes) => (this.taxRegimes = regimes),
      error: (error) => {
        this.alert.error(error.error?.detail ?? 'Error al cargar los regímenes tributarios');
      },
    });
  }

  private isPdfFile(file: File): boolean {
    return file.type === 'application/pdf';
  }

  onFileSelected(event: Event, controlName: string): void {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (file) {
      if (this.isPdfFile(file)) {
        if (file.size <= 1024 * 1024) {
          // 1MB
          this.form.patchValue({ [controlName]: file });
          this.taxFormFile = file;
          this.taxFormFileName = file.name;
        } else {
          this.alert.error('El archivo no debe superar 1MB');
        }
      } else {
        this.alert.error('Solo se permiten archivos PDF');
      }
      this.form.get(controlName)?.updateValueAndValidity();
    }
  }

  downloadFile(): void {
    if (this.initialData?.taxFormFileUrl) {
      window.open(this.initialData.taxFormFileUrl, '_blank');
    }
  }

  get isValid(): boolean {
    const basicFieldsValid = Boolean(this.form.get('regimeType')?.valid);
    const hasNewFile = Boolean(this.form.get('regimeSupportFile')?.value);
    const hasExistingFile = Boolean(this.initialData?.taxFormFileUrl);

    return basicFieldsValid && (hasNewFile || hasExistingFile);
  }

  getTaxRegimeName(id: number): string {
    return this.taxRegimes.find((regime) => regime.id === id)?.name || '';
  }
}
