{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/Github/SICMA/sicma-ui/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport Swal from 'sweetalert2';\nimport { AlertService } from './alert.service';\ndescribe('AlertService', () => {\n  let service;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      providers: [AlertService]\n    });\n    service = TestBed.inject(AlertService);\n    spyOn(Swal, 'fire').and.returnValue(Promise.resolve({\n      isConfirmed: true\n    }));\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('success', () => {\n    it('should call Swal.fire with success parameters', () => {\n      const title = 'Success Title';\n      const text = 'Success Message';\n      service.success(title, text);\n      expect(Swal.fire).toHaveBeenCalledWith(jasmine.objectContaining({\n        title,\n        text,\n        icon: 'success',\n        timer: 3000,\n        position: 'top-end',\n        showConfirmButton: false\n      }));\n    });\n  });\n  describe('info', () => {\n    it('should call Swal.fire with info parameters', () => {\n      const title = 'Info Title';\n      const text = 'Info Message';\n      service.info(title, text);\n      expect(Swal.fire).toHaveBeenCalledWith(jasmine.objectContaining({\n        title,\n        text,\n        icon: 'info',\n        position: 'center',\n        confirmButtonColor: '#5f9af3'\n      }));\n    });\n  });\n  describe('warning', () => {\n    it('should call Swal.fire with warning parameters', () => {\n      const title = 'Warning Title';\n      const text = 'Warning Message';\n      service.warning(title, text);\n      expect(Swal.fire).toHaveBeenCalledWith(jasmine.objectContaining({\n        title,\n        text,\n        icon: 'warning',\n        position: 'center',\n        confirmButtonColor: '#5f9af3'\n      }));\n    });\n  });\n  describe('error', () => {\n    it('should call Swal.fire with error parameters and default text', () => {\n      const title = 'Error Title';\n      service.error(title);\n      expect(Swal.fire).toHaveBeenCalledWith(jasmine.objectContaining({\n        title,\n        text: 'Por favor, intente nuevamente.',\n        icon: 'error',\n        position: 'center',\n        confirmButtonColor: '#5f9af3'\n      }));\n    });\n    it('should call Swal.fire with error parameters and custom text', () => {\n      const title = 'Error Title';\n      const text = 'Custom Error Message';\n      service.error(title, text);\n      expect(Swal.fire).toHaveBeenCalledWith(jasmine.objectContaining({\n        title,\n        text,\n        icon: 'error',\n        position: 'center',\n        confirmButtonColor: '#5f9af3'\n      }));\n    });\n  });\n  describe('confirm', () => {\n    it('should call Swal.fire with confirm parameters and return true when confirmed', /*#__PURE__*/_asyncToGenerator(function* () {\n      const title = 'Confirm Title';\n      const text = 'Custom Confirm Message';\n      const result = yield service.confirm(title, text);\n      expect(Swal.fire).toHaveBeenCalledWith(jasmine.objectContaining({\n        title,\n        text,\n        icon: 'question',\n        showCancelButton: true,\n        confirmButtonText: 'Sí',\n        cancelButtonText: 'No',\n        confirmButtonColor: '#5f9af3',\n        cancelButtonColor: '#d33'\n      }));\n      expect(result).toBe(true);\n    }));\n    it('should call Swal.fire with confirm parameters and default text', /*#__PURE__*/_asyncToGenerator(function* () {\n      const title = 'Confirm Title';\n      const result = yield service.confirm(title);\n      expect(Swal.fire).toHaveBeenCalledWith(jasmine.objectContaining({\n        title,\n        text: 'Esta acción no se puede deshacer',\n        icon: 'question',\n        showCancelButton: true,\n        confirmButtonText: 'Sí',\n        cancelButtonText: 'No',\n        confirmButtonColor: '#5f9af3',\n        cancelButtonColor: '#d33'\n      }));\n      expect(result).toBe(true);\n    }));\n    it('should return false when confirmation is cancelled', /*#__PURE__*/_asyncToGenerator(function* () {\n      Swal.fire.and.returnValue(Promise.resolve({\n        isConfirmed: false\n      }));\n      const title = 'Confirm Title';\n      const result = yield service.confirm(title);\n      expect(result).toBe(false);\n    }));\n  });\n});", "map": {"version": 3, "names": ["TestBed", "<PERSON><PERSON>", "AlertService", "describe", "service", "beforeEach", "configureTestingModule", "providers", "inject", "spyOn", "and", "returnValue", "Promise", "resolve", "isConfirmed", "it", "expect", "toBeTruthy", "title", "text", "success", "fire", "toHaveBeenCalledWith", "jasmine", "objectContaining", "icon", "timer", "position", "showConfirmButton", "info", "confirmButtonColor", "warning", "error", "_asyncToGenerator", "result", "confirm", "showCancelButton", "confirmButtonText", "cancelButtonText", "cancelButtonColor", "toBe"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\shared\\services\\alert.service.spec.ts"], "sourcesContent": ["import { TestBed } from '@angular/core/testing';\nimport Swal, { SweetAlertResult } from 'sweetalert2';\nimport { AlertService } from './alert.service';\n\ndescribe('AlertService', () => {\n  let service: AlertService;\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      providers: [AlertService],\n    });\n    service = TestBed.inject(AlertService);\n    spyOn(Swal, 'fire').and.returnValue(\n      Promise.resolve({ isConfirmed: true } as SweetAlertResult<unknown>),\n    );\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('success', () => {\n    it('should call Swal.fire with success parameters', () => {\n      const title = 'Success Title';\n      const text = 'Success Message';\n\n      service.success(title, text);\n\n      expect(Swal.fire).toHaveBeenCalledWith(\n        jasmine.objectContaining({\n          title,\n          text,\n          icon: 'success',\n          timer: 3000,\n          position: 'top-end',\n          showConfirmButton: false,\n        }),\n      );\n    });\n  });\n\n  describe('info', () => {\n    it('should call Swal.fire with info parameters', () => {\n      const title = 'Info Title';\n      const text = 'Info Message';\n\n      service.info(title, text);\n\n      expect(Swal.fire).toHaveBeenCalledWith(\n        jasmine.objectContaining({\n          title,\n          text,\n          icon: 'info',\n          position: 'center',\n          confirmButtonColor: '#5f9af3',\n        }),\n      );\n    });\n  });\n\n  describe('warning', () => {\n    it('should call Swal.fire with warning parameters', () => {\n      const title = 'Warning Title';\n      const text = 'Warning Message';\n\n      service.warning(title, text);\n\n      expect(Swal.fire).toHaveBeenCalledWith(\n        jasmine.objectContaining({\n          title,\n          text,\n          icon: 'warning',\n          position: 'center',\n          confirmButtonColor: '#5f9af3',\n        }),\n      );\n    });\n  });\n\n  describe('error', () => {\n    it('should call Swal.fire with error parameters and default text', () => {\n      const title = 'Error Title';\n\n      service.error(title);\n\n      expect(Swal.fire).toHaveBeenCalledWith(\n        jasmine.objectContaining({\n          title,\n          text: 'Por favor, intente nuevamente.',\n          icon: 'error',\n          position: 'center',\n          confirmButtonColor: '#5f9af3',\n        }),\n      );\n    });\n\n    it('should call Swal.fire with error parameters and custom text', () => {\n      const title = 'Error Title';\n      const text = 'Custom Error Message';\n\n      service.error(title, text);\n\n      expect(Swal.fire).toHaveBeenCalledWith(\n        jasmine.objectContaining({\n          title,\n          text,\n          icon: 'error',\n          position: 'center',\n          confirmButtonColor: '#5f9af3',\n        }),\n      );\n    });\n  });\n\n  describe('confirm', () => {\n    it('should call Swal.fire with confirm parameters and return true when confirmed', async () => {\n      const title = 'Confirm Title';\n      const text = 'Custom Confirm Message';\n\n      const result = await service.confirm(title, text);\n\n      expect(Swal.fire).toHaveBeenCalledWith(\n        jasmine.objectContaining({\n          title,\n          text,\n          icon: 'question',\n          showCancelButton: true,\n          confirmButtonText: 'Sí',\n          cancelButtonText: 'No',\n          confirmButtonColor: '#5f9af3',\n          cancelButtonColor: '#d33',\n        }),\n      );\n      expect(result).toBe(true);\n    });\n\n    it('should call Swal.fire with confirm parameters and default text', async () => {\n      const title = 'Confirm Title';\n\n      const result = await service.confirm(title);\n\n      expect(Swal.fire).toHaveBeenCalledWith(\n        jasmine.objectContaining({\n          title,\n          text: 'Esta acción no se puede deshacer',\n          icon: 'question',\n          showCancelButton: true,\n          confirmButtonText: 'Sí',\n          cancelButtonText: 'No',\n          confirmButtonColor: '#5f9af3',\n          cancelButtonColor: '#d33',\n        }),\n      );\n      expect(result).toBe(true);\n    });\n\n    it('should return false when confirmation is cancelled', async () => {\n      (Swal.fire as jasmine.Spy).and.returnValue(\n        Promise.resolve({ isConfirmed: false } as SweetAlertResult<unknown>),\n      );\n      const title = 'Confirm Title';\n\n      const result = await service.confirm(title);\n\n      expect(result).toBe(false);\n    });\n  });\n});"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,uBAAuB;AAC/C,OAAOC,IAA0B,MAAM,aAAa;AACpD,SAASC,YAAY,QAAQ,iBAAiB;AAE9CC,QAAQ,CAAC,cAAc,EAAE,MAAK;EAC5B,IAAIC,OAAqB;EAEzBC,UAAU,CAAC,MAAK;IACdL,OAAO,CAACM,sBAAsB,CAAC;MAC7BC,SAAS,EAAE,CAACL,YAAY;KACzB,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACQ,MAAM,CAACN,YAAY,CAAC;IACtCO,KAAK,CAACR,IAAI,EAAE,MAAM,CAAC,CAACS,GAAG,CAACC,WAAW,CACjCC,OAAO,CAACC,OAAO,CAAC;MAAEC,WAAW,EAAE;IAAI,CAA+B,CAAC,CACpE;EACH,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACZ,OAAO,CAAC,CAACa,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFd,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBY,EAAE,CAAC,+CAA+C,EAAE,MAAK;MACvD,MAAMG,KAAK,GAAG,eAAe;MAC7B,MAAMC,IAAI,GAAG,iBAAiB;MAE9Bf,OAAO,CAACgB,OAAO,CAACF,KAAK,EAAEC,IAAI,CAAC;MAE5BH,MAAM,CAACf,IAAI,CAACoB,IAAI,CAAC,CAACC,oBAAoB,CACpCC,OAAO,CAACC,gBAAgB,CAAC;QACvBN,KAAK;QACLC,IAAI;QACJM,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,SAAS;QACnBC,iBAAiB,EAAE;OACpB,CAAC,CACH;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFzB,QAAQ,CAAC,MAAM,EAAE,MAAK;IACpBY,EAAE,CAAC,4CAA4C,EAAE,MAAK;MACpD,MAAMG,KAAK,GAAG,YAAY;MAC1B,MAAMC,IAAI,GAAG,cAAc;MAE3Bf,OAAO,CAACyB,IAAI,CAACX,KAAK,EAAEC,IAAI,CAAC;MAEzBH,MAAM,CAACf,IAAI,CAACoB,IAAI,CAAC,CAACC,oBAAoB,CACpCC,OAAO,CAACC,gBAAgB,CAAC;QACvBN,KAAK;QACLC,IAAI;QACJM,IAAI,EAAE,MAAM;QACZE,QAAQ,EAAE,QAAQ;QAClBG,kBAAkB,EAAE;OACrB,CAAC,CACH;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF3B,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBY,EAAE,CAAC,+CAA+C,EAAE,MAAK;MACvD,MAAMG,KAAK,GAAG,eAAe;MAC7B,MAAMC,IAAI,GAAG,iBAAiB;MAE9Bf,OAAO,CAAC2B,OAAO,CAACb,KAAK,EAAEC,IAAI,CAAC;MAE5BH,MAAM,CAACf,IAAI,CAACoB,IAAI,CAAC,CAACC,oBAAoB,CACpCC,OAAO,CAACC,gBAAgB,CAAC;QACvBN,KAAK;QACLC,IAAI;QACJM,IAAI,EAAE,SAAS;QACfE,QAAQ,EAAE,QAAQ;QAClBG,kBAAkB,EAAE;OACrB,CAAC,CACH;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF3B,QAAQ,CAAC,OAAO,EAAE,MAAK;IACrBY,EAAE,CAAC,8DAA8D,EAAE,MAAK;MACtE,MAAMG,KAAK,GAAG,aAAa;MAE3Bd,OAAO,CAAC4B,KAAK,CAACd,KAAK,CAAC;MAEpBF,MAAM,CAACf,IAAI,CAACoB,IAAI,CAAC,CAACC,oBAAoB,CACpCC,OAAO,CAACC,gBAAgB,CAAC;QACvBN,KAAK;QACLC,IAAI,EAAE,gCAAgC;QACtCM,IAAI,EAAE,OAAO;QACbE,QAAQ,EAAE,QAAQ;QAClBG,kBAAkB,EAAE;OACrB,CAAC,CACH;IACH,CAAC,CAAC;IAEFf,EAAE,CAAC,6DAA6D,EAAE,MAAK;MACrE,MAAMG,KAAK,GAAG,aAAa;MAC3B,MAAMC,IAAI,GAAG,sBAAsB;MAEnCf,OAAO,CAAC4B,KAAK,CAACd,KAAK,EAAEC,IAAI,CAAC;MAE1BH,MAAM,CAACf,IAAI,CAACoB,IAAI,CAAC,CAACC,oBAAoB,CACpCC,OAAO,CAACC,gBAAgB,CAAC;QACvBN,KAAK;QACLC,IAAI;QACJM,IAAI,EAAE,OAAO;QACbE,QAAQ,EAAE,QAAQ;QAClBG,kBAAkB,EAAE;OACrB,CAAC,CACH;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF3B,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBY,EAAE,CAAC,8EAA8E,eAAAkB,iBAAA,CAAE,aAAW;MAC5F,MAAMf,KAAK,GAAG,eAAe;MAC7B,MAAMC,IAAI,GAAG,wBAAwB;MAErC,MAAMe,MAAM,SAAS9B,OAAO,CAAC+B,OAAO,CAACjB,KAAK,EAAEC,IAAI,CAAC;MAEjDH,MAAM,CAACf,IAAI,CAACoB,IAAI,CAAC,CAACC,oBAAoB,CACpCC,OAAO,CAACC,gBAAgB,CAAC;QACvBN,KAAK;QACLC,IAAI;QACJM,IAAI,EAAE,UAAU;QAChBW,gBAAgB,EAAE,IAAI;QACtBC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBR,kBAAkB,EAAE,SAAS;QAC7BS,iBAAiB,EAAE;OACpB,CAAC,CACH;MACDvB,MAAM,CAACkB,MAAM,CAAC,CAACM,IAAI,CAAC,IAAI,CAAC;IAC3B,CAAC,EAAC;IAEFzB,EAAE,CAAC,gEAAgE,eAAAkB,iBAAA,CAAE,aAAW;MAC9E,MAAMf,KAAK,GAAG,eAAe;MAE7B,MAAMgB,MAAM,SAAS9B,OAAO,CAAC+B,OAAO,CAACjB,KAAK,CAAC;MAE3CF,MAAM,CAACf,IAAI,CAACoB,IAAI,CAAC,CAACC,oBAAoB,CACpCC,OAAO,CAACC,gBAAgB,CAAC;QACvBN,KAAK;QACLC,IAAI,EAAE,kCAAkC;QACxCM,IAAI,EAAE,UAAU;QAChBW,gBAAgB,EAAE,IAAI;QACtBC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBR,kBAAkB,EAAE,SAAS;QAC7BS,iBAAiB,EAAE;OACpB,CAAC,CACH;MACDvB,MAAM,CAACkB,MAAM,CAAC,CAACM,IAAI,CAAC,IAAI,CAAC;IAC3B,CAAC,EAAC;IAEFzB,EAAE,CAAC,oDAAoD,eAAAkB,iBAAA,CAAE,aAAW;MACjEhC,IAAI,CAACoB,IAAoB,CAACX,GAAG,CAACC,WAAW,CACxCC,OAAO,CAACC,OAAO,CAAC;QAAEC,WAAW,EAAE;MAAK,CAA+B,CAAC,CACrE;MACD,MAAMI,KAAK,GAAG,eAAe;MAE7B,MAAMgB,MAAM,SAAS9B,OAAO,CAAC+B,OAAO,CAACjB,KAAK,CAAC;MAE3CF,MAAM,CAACkB,MAAM,CAAC,CAACM,IAAI,CAAC,KAAK,CAAC;IAC5B,CAAC,EAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}