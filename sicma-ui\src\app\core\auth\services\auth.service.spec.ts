import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { AuthStatus } from '@core/auth/enums/auth-status.enum';
import { User } from '@core/auth/models/user.model';
import { UserProfile } from '@core/auth/models/user_profile.model';
import { environment } from '@env';
import { AuthService } from './auth.service';
import { UserService } from './user.service';

describe('AuthService', () => {
  let service: AuthService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/users`;

  const mockUserProfile: UserProfile = {
    profile_id: 1,
    profile_name: 'admin',
  };

  const mockUser: User = {
    id: 1,
    username: 'testuser',
    profiles: [mockUserProfile],
  };

  const mockAuthResponse = {
    token: 'mock-jwt-token',
    user: mockUser,
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [AuthService, UserService],
    });
    service = TestBed.inject(AuthService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
    localStorage.clear();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('login', () => {
    it('should authenticate user and store token', () => {
      const token = 'test-token';

      service.login(token).subscribe((success) => {
        expect(success).toBeTrue();
        expect(localStorage.getItem('token')).toBe(mockAuthResponse.token);
        expect(localStorage.getItem('user')).toBe(
          JSON.stringify(mockAuthResponse.user),
        );
        expect(localStorage.getItem('status')).toBe(AuthStatus.authenticated);
        expect(localStorage.getItem('user_profiles')).toBe(
          JSON.stringify(mockUser.profiles),
        );
      });

      const req = httpMock.expectOne(`${apiUrl}/authenticate`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual({ token });
      req.flush(mockAuthResponse);
    });

    it('should handle login error', () => {
      const token = 'invalid-token';

      service.login(token).subscribe((success) => {
        expect(success).toBeFalse();
        expect(localStorage.getItem('token')).toBeNull();
        expect(localStorage.getItem('user')).toBeNull();
        expect(localStorage.getItem('status')).toBeNull();
      });

      const req = httpMock.expectOne(`${apiUrl}/authenticate`);
      req.error(new ErrorEvent('Unauthorized'));
    });
  });

  describe('getUserProfiles', () => {
    it('should return user profiles from localStorage', () => {
      localStorage.setItem('user_profiles', JSON.stringify([mockUserProfile]));
      const profiles = service.getUserProfiles();
      expect(profiles).toEqual([mockUserProfile]);
    });

    it('should return empty array when no profiles in localStorage', () => {
      const profiles = service.getUserProfiles();
      expect(profiles).toEqual([]);
    });
  });

  describe('hasProfile', () => {
    it('should return true when user has profile', () => {
      localStorage.setItem('user_profiles', JSON.stringify([mockUserProfile]));
      expect(service.hasProfile('admin')).toBeTrue();
    });

    it('should return false when user does not have profile', () => {
      localStorage.setItem('user_profiles', JSON.stringify([mockUserProfile]));
      expect(service.hasProfile('user')).toBeFalse();
    });
  });

  describe('getCurrentUser', () => {
    it('should return current user from localStorage', () => {
      const mockStoredUser = {
        id: 1,
        username: 'testuser',
        profiles: [],
      };
      localStorage.setItem('user', JSON.stringify(mockStoredUser));
      expect(service.getCurrentUser()).toEqual(mockStoredUser);
    });

    it('should return null when no user in localStorage', () => {
      expect(service.getCurrentUser()).toBeNull();
    });
  });

  describe('setCurrentUser', () => {
    it('should store user in localStorage', () => {
      const user = { email: '<EMAIL>' };
      service.setCurrentUser(user);
      expect(localStorage.getItem('user')).toBe(JSON.stringify(user));
    });
  });

  describe('logout', () => {
    it('should clear storage and update status', () => {
      localStorage.setItem('token', 'test-token');
      localStorage.setItem('user', JSON.stringify(mockUser));
      localStorage.setItem('status', AuthStatus.authenticated);

      service.logout();

      expect(localStorage.getItem('token')).toBeNull();
      expect(service.currentUser()).toBeNull();
      expect(service.authStatus()).toBe(AuthStatus.notAuthenticated);
    });
  });
});