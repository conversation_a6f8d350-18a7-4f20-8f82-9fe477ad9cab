import { ComponentFixture, TestBed } from '@angular/core/testing';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { NgxSpinnerModule } from 'ngx-spinner';
import { ReportReviewHistoryService } from '@contractor-dashboard/services/report-review-history.service';
import { AlertService } from '@shared/services/alert.service';
import { of } from 'rxjs';

import { MonthlyReportReviewHistoryDialogComponent } from './monthly-report-review-history-dialog.component';

describe('MonthlyReportReviewHistoryDialogComponent', () => {
  let component: MonthlyReportReviewHistoryDialogComponent;
  let fixture: ComponentFixture<MonthlyReportReviewHistoryDialogComponent>;
  let mockDialogRef: jasmine.SpyObj<
    MatDialogRef<MonthlyReportReviewHistoryDialogComponent>
  >;
  let mockReviewHistoryService: jasmine.SpyObj<ReportReviewHistoryService>;
  let mockAlertService: jasmine.SpyObj<AlertService>;

  beforeEach(async () => {
    mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
    mockReviewHistoryService = jasmine.createSpyObj(
      'ReportReviewHistoryService',
      ['getByMonthlyReportId'],
    );
    mockAlertService = jasmine.createSpyObj('AlertService', ['error']);

    mockReviewHistoryService.getByMonthlyReportId.and.returnValue(of([]));

    await TestBed.configureTestingModule({
      imports: [
        MonthlyReportReviewHistoryDialogComponent,
        MatDialogModule,
        HttpClientTestingModule,
        NoopAnimationsModule,
        MatTableModule,
        MatPaginatorModule,
        MatSortModule,
        MatProgressSpinnerModule,
        NgxSpinnerModule,
      ],
      providers: [
        { provide: MatDialogRef, useValue: mockDialogRef },
        { provide: MAT_DIALOG_DATA, useValue: { monthlyReportId: 1 } },
        {
          provide: ReportReviewHistoryService,
          useValue: mockReviewHistoryService,
        },
        { provide: AlertService, useValue: mockAlertService },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(
      MonthlyReportReviewHistoryDialogComponent,
    );
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load review history on init', () => {
    component.ngOnInit();
    expect(mockReviewHistoryService.getByMonthlyReportId).toHaveBeenCalledWith(
      1,
    );
  });

  it('should close dialog', () => {
    component.close();
    expect(mockDialogRef.close).toHaveBeenCalled();
  });
});