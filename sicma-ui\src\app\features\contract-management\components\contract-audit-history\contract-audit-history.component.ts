import { Component, Input } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { DatePipe } from '@angular/common';
import { Contract } from '@contract-management/models/contract.model';
import { ContractAuditHistory } from '@contract-management/models/contract-audit-history.model';
import { ContractAuditHistoryService } from '@contract-management/services/contract-audit-history.service';
import { AlertService } from '@shared/services/alert.service';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { finalize } from 'rxjs';

@Component({
  selector: 'app-contract-audit-history',
  standalone: true,
  imports: [
    MatCardModule,
    MatTableModule,
    MatIconModule,
    DatePipe,
    MatExpansionModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './contract-audit-history.component.html',
  styleUrl: './contract-audit-history.component.scss',
})
export class ContractAuditHistoryComponent {
  @Input() contract!: Contract;

  auditHistoryList: ContractAuditHistory[] = [];
  displayedColumns: string[] = [
    'index',
    'auditDate',
    'status',
    'auditor',
    'comment',
  ];
  isLoading = false;

  constructor(
    private readonly contractAuditHistoryService: ContractAuditHistoryService,
    private readonly alert: AlertService,
  ) {}

  loadAuditHistory(): void {
    if (this.contract) {
      this.isLoading = true;
      this.contractAuditHistoryService
        .getByContractId(this.contract.id)
        .pipe(finalize(() => (this.isLoading = false)))
        .subscribe({
          next: (history) => {
            this.auditHistoryList = history;
          },
          error: (error) => {
            this.alert.error(error.error?.detail ?? 'Error al cargar el historial de auditoría');
          },
        });
    }
  }
}
