{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { ReportObligationService } from './report-obligation.service';\ndescribe('ReportObligationService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/report-obligations`;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [ReportObligationService]\n    });\n    service = TestBed.inject(ReportObligationService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  const mockReportObligation = {\n    id: 1,\n    description: 'Test obligation',\n    evidence: 'Test evidence',\n    filePath: '/path/to/file',\n    monthlyReportId: 1,\n    obligationId: 1\n  };\n  describe('getAll', () => {\n    it('should return all report obligations', () => {\n      const mockReportObligations = [mockReportObligation];\n      service.getAll().subscribe(reportObligations => {\n        expect(reportObligations).toEqual(mockReportObligations);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockReportObligations);\n    });\n    it('should handle error when getting all report obligations', () => {\n      service.getAll().subscribe({\n        error: error => {\n          expect(error.status).toBe(500);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Server error'), {\n        status: 500\n      });\n    });\n  });\n  describe('getById', () => {\n    it('should return a report obligation by id', () => {\n      service.getById(1).subscribe(reportObligation => {\n        expect(reportObligation).toEqual(mockReportObligation);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockReportObligation);\n    });\n    it('should handle error when getting report obligation by id', () => {\n      service.getById(999).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/999`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n  describe('create', () => {\n    const newReportObligation = {\n      description: 'New obligation',\n      evidence: 'New evidence',\n      filePath: '/path/to/new/file',\n      monthlyReportId: 1,\n      obligationId: 1\n    };\n    it('should create a new report obligation', () => {\n      service.create(newReportObligation).subscribe(reportObligation => {\n        expect(reportObligation).toEqual(mockReportObligation);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newReportObligation);\n      req.flush(mockReportObligation);\n    });\n    it('should handle error when creating report obligation', () => {\n      service.create(newReportObligation).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n  describe('update', () => {\n    const updateData = {\n      description: 'Updated description',\n      evidence: 'Updated evidence'\n    };\n    it('should update a report obligation', () => {\n      service.update(1, updateData).subscribe(reportObligation => {\n        expect(reportObligation).toEqual({\n          ...mockReportObligation,\n          ...updateData\n        });\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush({\n        ...mockReportObligation,\n        ...updateData\n      });\n    });\n    it('should handle error when updating report obligation', () => {\n      service.update(1, updateData).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n  describe('delete', () => {\n    it('should delete a report obligation', () => {\n      service.delete(1).subscribe(response => {\n        expect(response).toBeNull();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n    it('should handle error when deleting report obligation', () => {\n      service.delete(1).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n  describe('getByMonthlyReportId', () => {\n    it('should return report obligations by monthly report id', () => {\n      const monthlyReportId = 1;\n      const mockReportObligations = [mockReportObligation];\n      service.getByMonthlyReportId(monthlyReportId).subscribe(reportObligations => {\n        expect(reportObligations).toEqual(mockReportObligations);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/monthly-report/${monthlyReportId}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockReportObligations);\n    });\n    it('should handle error when getting report obligations by monthly report id', () => {\n      const monthlyReportId = 999;\n      service.getByMonthlyReportId(monthlyReportId).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/monthly-report/${monthlyReportId}`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "ReportObligationService", "describe", "service", "httpMock", "apiUrl", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockReportObligation", "id", "description", "evidence", "filePath", "monthlyReportId", "obligationId", "mockReportObligations", "getAll", "subscribe", "reportObligations", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "error", "status", "ErrorEvent", "getById", "reportObligation", "newReportObligation", "create", "body", "updateData", "update", "delete", "response", "toBeNull", "getByMonthlyReportId"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\services\\report-obligation.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { ReportObligation } from '@contractor-dashboard/models/report-obligation.model';\nimport { environment } from '@env';\nimport { ReportObligationService } from './report-obligation.service';\n\ndescribe('ReportObligationService', () => {\n  let service: ReportObligationService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/report-obligations`;\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [ReportObligationService],\n    });\n    service = TestBed.inject(ReportObligationService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  const mockReportObligation: ReportObligation = {\n    id: 1,\n    description: 'Test obligation',\n    evidence: 'Test evidence',\n    filePath: '/path/to/file',\n    monthlyReportId: 1,\n    obligationId: 1,\n  };\n\n  describe('getAll', () => {\n    it('should return all report obligations', () => {\n      const mockReportObligations = [mockReportObligation];\n\n      service.getAll().subscribe((reportObligations) => {\n        expect(reportObligations).toEqual(mockReportObligations);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockReportObligations);\n    });\n\n    it('should handle error when getting all report obligations', () => {\n      service.getAll().subscribe({\n        error: (error) => {\n          expect(error.status).toBe(500);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Server error'), { status: 500 });\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a report obligation by id', () => {\n      service.getById(1).subscribe((reportObligation) => {\n        expect(reportObligation).toEqual(mockReportObligation);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockReportObligation);\n    });\n\n    it('should handle error when getting report obligation by id', () => {\n      service.getById(999).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/999`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n\n  describe('create', () => {\n    const newReportObligation: Omit<ReportObligation, 'id'> = {\n      description: 'New obligation',\n      evidence: 'New evidence',\n      filePath: '/path/to/new/file',\n      monthlyReportId: 1,\n      obligationId: 1,\n    };\n\n    it('should create a new report obligation', () => {\n      service.create(newReportObligation).subscribe((reportObligation) => {\n        expect(reportObligation).toEqual(mockReportObligation);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newReportObligation);\n      req.flush(mockReportObligation);\n    });\n\n    it('should handle error when creating report obligation', () => {\n      service.create(newReportObligation).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n\n  describe('update', () => {\n    const updateData: Partial<ReportObligation> = {\n      description: 'Updated description',\n      evidence: 'Updated evidence',\n    };\n\n    it('should update a report obligation', () => {\n      service.update(1, updateData).subscribe((reportObligation) => {\n        expect(reportObligation).toEqual({\n          ...mockReportObligation,\n          ...updateData,\n        });\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush({ ...mockReportObligation, ...updateData });\n    });\n\n    it('should handle error when updating report obligation', () => {\n      service.update(1, updateData).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete a report obligation', () => {\n      service.delete(1).subscribe((response) => {\n        expect(response).toBeNull();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n\n    it('should handle error when deleting report obligation', () => {\n      service.delete(1).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n\n  describe('getByMonthlyReportId', () => {\n    it('should return report obligations by monthly report id', () => {\n      const monthlyReportId = 1;\n      const mockReportObligations = [mockReportObligation];\n\n      service\n        .getByMonthlyReportId(monthlyReportId)\n        .subscribe((reportObligations) => {\n          expect(reportObligations).toEqual(mockReportObligations);\n        });\n\n      const req = httpMock.expectOne(\n        `${apiUrl}/monthly-report/${monthlyReportId}`,\n      );\n      expect(req.request.method).toBe('GET');\n      req.flush(mockReportObligations);\n    });\n\n    it('should handle error when getting report obligations by monthly report id', () => {\n      const monthlyReportId = 999;\n\n      service.getByMonthlyReportId(monthlyReportId).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(\n        `${apiUrl}/monthly-report/${monthlyReportId}`,\n      );\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,uBAAuB,QAAQ,6BAA6B;AAErEC,QAAQ,CAAC,yBAAyB,EAAE,MAAK;EACvC,IAAIC,OAAgC;EACpC,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,qBAAqB;EAEzDC,UAAU,CAAC,MAAK;IACdP,OAAO,CAACQ,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACX,uBAAuB,CAAC;MAClCY,SAAS,EAAE,CAACR,uBAAuB;KACpC,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACW,MAAM,CAACT,uBAAuB,CAAC;IACjDG,QAAQ,GAAGL,OAAO,CAACW,MAAM,CAACZ,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFa,SAAS,CAAC,MAAK;IACbP,QAAQ,CAACQ,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACX,OAAO,CAAC,CAACY,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEF,MAAMC,oBAAoB,GAAqB;IAC7CC,EAAE,EAAE,CAAC;IACLC,WAAW,EAAE,iBAAiB;IAC9BC,QAAQ,EAAE,eAAe;IACzBC,QAAQ,EAAE,eAAe;IACzBC,eAAe,EAAE,CAAC;IAClBC,YAAY,EAAE;GACf;EAEDpB,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBW,EAAE,CAAC,sCAAsC,EAAE,MAAK;MAC9C,MAAMU,qBAAqB,GAAG,CAACP,oBAAoB,CAAC;MAEpDb,OAAO,CAACqB,MAAM,EAAE,CAACC,SAAS,CAAEC,iBAAiB,IAAI;QAC/CZ,MAAM,CAACY,iBAAiB,CAAC,CAACC,OAAO,CAACJ,qBAAqB,CAAC;MAC1D,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGxB,QAAQ,CAACyB,SAAS,CAACxB,MAAM,CAAC;MACtCS,MAAM,CAACc,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,qBAAqB,CAAC;IAClC,CAAC,CAAC;IAEFV,EAAE,CAAC,yDAAyD,EAAE,MAAK;MACjEV,OAAO,CAACqB,MAAM,EAAE,CAACC,SAAS,CAAC;QACzBS,KAAK,EAAGA,KAAK,IAAI;UACfpB,MAAM,CAACoB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGxB,QAAQ,CAACyB,SAAS,CAACxB,MAAM,CAAC;MACtCuB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,cAAc,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC5D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjC,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBW,EAAE,CAAC,yCAAyC,EAAE,MAAK;MACjDV,OAAO,CAACkC,OAAO,CAAC,CAAC,CAAC,CAACZ,SAAS,CAAEa,gBAAgB,IAAI;QAChDxB,MAAM,CAACwB,gBAAgB,CAAC,CAACX,OAAO,CAACX,oBAAoB,CAAC;MACxD,CAAC,CAAC;MAEF,MAAMY,GAAG,GAAGxB,QAAQ,CAACyB,SAAS,CAAC,GAAGxB,MAAM,IAAI,CAAC;MAC7CS,MAAM,CAACc,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACjB,oBAAoB,CAAC;IACjC,CAAC,CAAC;IAEFH,EAAE,CAAC,0DAA0D,EAAE,MAAK;MAClEV,OAAO,CAACkC,OAAO,CAAC,GAAG,CAAC,CAACZ,SAAS,CAAC;QAC7BS,KAAK,EAAGA,KAAK,IAAI;UACfpB,MAAM,CAACoB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGxB,QAAQ,CAACyB,SAAS,CAAC,GAAGxB,MAAM,MAAM,CAAC;MAC/CuB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjC,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB,MAAMqC,mBAAmB,GAAiC;MACxDrB,WAAW,EAAE,gBAAgB;MAC7BC,QAAQ,EAAE,cAAc;MACxBC,QAAQ,EAAE,mBAAmB;MAC7BC,eAAe,EAAE,CAAC;MAClBC,YAAY,EAAE;KACf;IAEDT,EAAE,CAAC,uCAAuC,EAAE,MAAK;MAC/CV,OAAO,CAACqC,MAAM,CAACD,mBAAmB,CAAC,CAACd,SAAS,CAAEa,gBAAgB,IAAI;QACjExB,MAAM,CAACwB,gBAAgB,CAAC,CAACX,OAAO,CAACX,oBAAoB,CAAC;MACxD,CAAC,CAAC;MAEF,MAAMY,GAAG,GAAGxB,QAAQ,CAACyB,SAAS,CAACxB,MAAM,CAAC;MACtCS,MAAM,CAACc,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvClB,MAAM,CAACc,GAAG,CAACE,OAAO,CAACW,IAAI,CAAC,CAACd,OAAO,CAACY,mBAAmB,CAAC;MACrDX,GAAG,CAACK,KAAK,CAACjB,oBAAoB,CAAC;IACjC,CAAC,CAAC;IAEFH,EAAE,CAAC,qDAAqD,EAAE,MAAK;MAC7DV,OAAO,CAACqC,MAAM,CAACD,mBAAmB,CAAC,CAACd,SAAS,CAAC;QAC5CS,KAAK,EAAGA,KAAK,IAAI;UACfpB,MAAM,CAACoB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGxB,QAAQ,CAACyB,SAAS,CAACxB,MAAM,CAAC;MACtCuB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjC,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB,MAAMwC,UAAU,GAA8B;MAC5CxB,WAAW,EAAE,qBAAqB;MAClCC,QAAQ,EAAE;KACX;IAEDN,EAAE,CAAC,mCAAmC,EAAE,MAAK;MAC3CV,OAAO,CAACwC,MAAM,CAAC,CAAC,EAAED,UAAU,CAAC,CAACjB,SAAS,CAAEa,gBAAgB,IAAI;QAC3DxB,MAAM,CAACwB,gBAAgB,CAAC,CAACX,OAAO,CAAC;UAC/B,GAAGX,oBAAoB;UACvB,GAAG0B;SACJ,CAAC;MACJ,CAAC,CAAC;MAEF,MAAMd,GAAG,GAAGxB,QAAQ,CAACyB,SAAS,CAAC,GAAGxB,MAAM,IAAI,CAAC;MAC7CS,MAAM,CAACc,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtClB,MAAM,CAACc,GAAG,CAACE,OAAO,CAACW,IAAI,CAAC,CAACd,OAAO,CAACe,UAAU,CAAC;MAC5Cd,GAAG,CAACK,KAAK,CAAC;QAAE,GAAGjB,oBAAoB;QAAE,GAAG0B;MAAU,CAAE,CAAC;IACvD,CAAC,CAAC;IAEF7B,EAAE,CAAC,qDAAqD,EAAE,MAAK;MAC7DV,OAAO,CAACwC,MAAM,CAAC,CAAC,EAAED,UAAU,CAAC,CAACjB,SAAS,CAAC;QACtCS,KAAK,EAAGA,KAAK,IAAI;UACfpB,MAAM,CAACoB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGxB,QAAQ,CAACyB,SAAS,CAAC,GAAGxB,MAAM,IAAI,CAAC;MAC7CuB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjC,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBW,EAAE,CAAC,mCAAmC,EAAE,MAAK;MAC3CV,OAAO,CAACyC,MAAM,CAAC,CAAC,CAAC,CAACnB,SAAS,CAAEoB,QAAQ,IAAI;QACvC/B,MAAM,CAAC+B,QAAQ,CAAC,CAACC,QAAQ,EAAE;MAC7B,CAAC,CAAC;MAEF,MAAMlB,GAAG,GAAGxB,QAAQ,CAACyB,SAAS,CAAC,GAAGxB,MAAM,IAAI,CAAC;MAC7CS,MAAM,CAACc,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;IAEFpB,EAAE,CAAC,qDAAqD,EAAE,MAAK;MAC7DV,OAAO,CAACyC,MAAM,CAAC,CAAC,CAAC,CAACnB,SAAS,CAAC;QAC1BS,KAAK,EAAGA,KAAK,IAAI;UACfpB,MAAM,CAACoB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGxB,QAAQ,CAACyB,SAAS,CAAC,GAAGxB,MAAM,IAAI,CAAC;MAC7CuB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjC,QAAQ,CAAC,sBAAsB,EAAE,MAAK;IACpCW,EAAE,CAAC,uDAAuD,EAAE,MAAK;MAC/D,MAAMQ,eAAe,GAAG,CAAC;MACzB,MAAME,qBAAqB,GAAG,CAACP,oBAAoB,CAAC;MAEpDb,OAAO,CACJ4C,oBAAoB,CAAC1B,eAAe,CAAC,CACrCI,SAAS,CAAEC,iBAAiB,IAAI;QAC/BZ,MAAM,CAACY,iBAAiB,CAAC,CAACC,OAAO,CAACJ,qBAAqB,CAAC;MAC1D,CAAC,CAAC;MAEJ,MAAMK,GAAG,GAAGxB,QAAQ,CAACyB,SAAS,CAC5B,GAAGxB,MAAM,mBAAmBgB,eAAe,EAAE,CAC9C;MACDP,MAAM,CAACc,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,qBAAqB,CAAC;IAClC,CAAC,CAAC;IAEFV,EAAE,CAAC,0EAA0E,EAAE,MAAK;MAClF,MAAMQ,eAAe,GAAG,GAAG;MAE3BlB,OAAO,CAAC4C,oBAAoB,CAAC1B,eAAe,CAAC,CAACI,SAAS,CAAC;QACtDS,KAAK,EAAGA,KAAK,IAAI;UACfpB,MAAM,CAACoB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGxB,QAAQ,CAACyB,SAAS,CAC5B,GAAGxB,MAAM,mBAAmBgB,eAAe,EAAE,CAC9C;MACDO,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}