
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for app/features/contract-management/components/contract-values-form</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../../index.html">All files</a> app/features/contract-management/components/contract-values-form</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">54.32% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>157/289</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">34.37% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>66/192</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">59.09% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>26/44</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">54.51% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>157/288</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="contract-values-form.component.ts"><a href="contract-values-form.component.ts.html">contract-values-form.component.ts</a></td>
	<td data-value="54.32" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 54%"></div><div class="cover-empty" style="width: 46%"></div></div>
	</td>
	<td data-value="54.32" class="pct medium">54.32%</td>
	<td data-value="289" class="abs medium">157/289</td>
	<td data-value="34.37" class="pct low">34.37%</td>
	<td data-value="192" class="abs low">66/192</td>
	<td data-value="59.09" class="pct medium">59.09%</td>
	<td data-value="44" class="abs medium">26/44</td>
	<td data-value="54.51" class="pct medium">54.51%</td>
	<td data-value="288" class="abs medium">157/288</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-15T19:25:34.130Z
            </div>
        <script src="../../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../../sorter.js"></script>
        <script src="../../../../../block-navigation.js"></script>
    </body>
</html>
    