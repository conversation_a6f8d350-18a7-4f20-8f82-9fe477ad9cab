{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { PensionFundService } from './pension-fund.service';\ndescribe('PensionFundService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/pension-funds`;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [PensionFundService]\n    });\n    service = TestBed.inject(PensionFundService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  const mockPensionFund = {\n    id: 1,\n    name: 'Porvenir'\n  };\n  describe('getAll', () => {\n    it('should return all pension funds', () => {\n      const mockPensionFunds = [mockPensionFund];\n      service.getAll().subscribe(pensionFunds => {\n        expect(pensionFunds).toEqual(mockPensionFunds);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockPensionFunds);\n    });\n    it('should handle error when getting all pension funds', () => {\n      service.getAll().subscribe({\n        error: error => {\n          expect(error.status).toBe(500);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Server error'), {\n        status: 500\n      });\n    });\n  });\n  describe('getById', () => {\n    it('should return a pension fund by id', () => {\n      service.getById(1).subscribe(pensionFund => {\n        expect(pensionFund).toEqual(mockPensionFund);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockPensionFund);\n    });\n    it('should handle error when getting pension fund by id', () => {\n      service.getById(999).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/999`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n  describe('create', () => {\n    const newPensionFund = {\n      name: 'New Fund'\n    };\n    it('should create a new pension fund', () => {\n      service.create(newPensionFund).subscribe(pensionFund => {\n        expect(pensionFund).toEqual(mockPensionFund);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newPensionFund);\n      req.flush(mockPensionFund);\n    });\n    it('should handle error when creating pension fund', () => {\n      service.create(newPensionFund).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n  describe('update', () => {\n    const updateData = {\n      name: 'Updated Name'\n    };\n    it('should update a pension fund', () => {\n      service.update(1, updateData).subscribe(pensionFund => {\n        expect(pensionFund).toEqual({\n          ...mockPensionFund,\n          ...updateData\n        });\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush({\n        ...mockPensionFund,\n        ...updateData\n      });\n    });\n    it('should handle error when updating pension fund', () => {\n      service.update(1, updateData).subscribe({\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Bad request'), {\n        status: 400\n      });\n    });\n  });\n  describe('delete', () => {\n    it('should delete a pension fund', () => {\n      service.delete(1).subscribe(response => {\n        expect(response).toBeNull();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n    it('should handle error when deleting pension fund', () => {\n      service.delete(1).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n  describe('getByName', () => {\n    it('should return a pension fund by name', () => {\n      const name = 'Porvenir';\n      service.getByName(name).subscribe(pensionFund => {\n        expect(pensionFund).toEqual(mockPensionFund);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockPensionFund);\n    });\n    it('should handle error when getting pension fund by name', () => {\n      const name = 'NonExistent';\n      service.getByName(name).subscribe({\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      req.error(new ErrorEvent('Not found'), {\n        status: 404\n      });\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "PensionFundService", "describe", "service", "httpMock", "apiUrl", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockPensionFund", "id", "name", "mockPensionFunds", "getAll", "subscribe", "pensionFunds", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "error", "status", "ErrorEvent", "getById", "pensionFund", "newPensionFund", "create", "body", "updateData", "update", "delete", "response", "toBeNull", "getByName"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\services\\pension-fund.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { PensionFund } from '@contractor-dashboard/models/pension-fund.model';\nimport { environment } from '@env';\nimport { PensionFundService } from './pension-fund.service';\n\ndescribe('PensionFundService', () => {\n  let service: PensionFundService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/pension-funds`;\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [PensionFundService],\n    });\n    service = TestBed.inject(PensionFundService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  const mockPensionFund: PensionFund = {\n    id: 1,\n    name: 'Porvenir',\n  };\n\n  describe('getAll', () => {\n    it('should return all pension funds', () => {\n      const mockPensionFunds = [mockPensionFund];\n\n      service.getAll().subscribe((pensionFunds) => {\n        expect(pensionFunds).toEqual(mockPensionFunds);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockPensionFunds);\n    });\n\n    it('should handle error when getting all pension funds', () => {\n      service.getAll().subscribe({\n        error: (error) => {\n          expect(error.status).toBe(500);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Server error'), { status: 500 });\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a pension fund by id', () => {\n      service.getById(1).subscribe((pensionFund) => {\n        expect(pensionFund).toEqual(mockPensionFund);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockPensionFund);\n    });\n\n    it('should handle error when getting pension fund by id', () => {\n      service.getById(999).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/999`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n\n  describe('create', () => {\n    const newPensionFund: Omit<PensionFund, 'id'> = {\n      name: 'New Fund',\n    };\n\n    it('should create a new pension fund', () => {\n      service.create(newPensionFund).subscribe((pensionFund) => {\n        expect(pensionFund).toEqual(mockPensionFund);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newPensionFund);\n      req.flush(mockPensionFund);\n    });\n\n    it('should handle error when creating pension fund', () => {\n      service.create(newPensionFund).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n\n  describe('update', () => {\n    const updateData: Partial<PensionFund> = {\n      name: 'Updated Name',\n    };\n\n    it('should update a pension fund', () => {\n      service.update(1, updateData).subscribe((pensionFund) => {\n        expect(pensionFund).toEqual({ ...mockPensionFund, ...updateData });\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush({ ...mockPensionFund, ...updateData });\n    });\n\n    it('should handle error when updating pension fund', () => {\n      service.update(1, updateData).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Bad request'), { status: 400 });\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete a pension fund', () => {\n      service.delete(1).subscribe((response) => {\n        expect(response).toBeNull();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n\n    it('should handle error when deleting pension fund', () => {\n      service.delete(1).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/1`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n\n  describe('getByName', () => {\n    it('should return a pension fund by name', () => {\n      const name = 'Porvenir';\n\n      service.getByName(name).subscribe((pensionFund) => {\n        expect(pensionFund).toEqual(mockPensionFund);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockPensionFund);\n    });\n\n    it('should handle error when getting pension fund by name', () => {\n      const name = 'NonExistent';\n\n      service.getByName(name).subscribe({\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      req.error(new ErrorEvent('Not found'), { status: 404 });\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,kBAAkB,QAAQ,wBAAwB;AAE3DC,QAAQ,CAAC,oBAAoB,EAAE,MAAK;EAClC,IAAIC,OAA2B;EAC/B,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,gBAAgB;EAEpDC,UAAU,CAAC,MAAK;IACdP,OAAO,CAACQ,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACX,uBAAuB,CAAC;MAClCY,SAAS,EAAE,CAACR,kBAAkB;KAC/B,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACW,MAAM,CAACT,kBAAkB,CAAC;IAC5CG,QAAQ,GAAGL,OAAO,CAACW,MAAM,CAACZ,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFa,SAAS,CAAC,MAAK;IACbP,QAAQ,CAACQ,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACX,OAAO,CAAC,CAACY,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEF,MAAMC,eAAe,GAAgB;IACnCC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE;GACP;EAEDhB,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBW,EAAE,CAAC,iCAAiC,EAAE,MAAK;MACzC,MAAMM,gBAAgB,GAAG,CAACH,eAAe,CAAC;MAE1Cb,OAAO,CAACiB,MAAM,EAAE,CAACC,SAAS,CAAEC,YAAY,IAAI;QAC1CR,MAAM,CAACQ,YAAY,CAAC,CAACC,OAAO,CAACJ,gBAAgB,CAAC;MAChD,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCS,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,gBAAgB,CAAC;IAC7B,CAAC,CAAC;IAEFN,EAAE,CAAC,oDAAoD,EAAE,MAAK;MAC5DV,OAAO,CAACiB,MAAM,EAAE,CAACC,SAAS,CAAC;QACzBS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,cAAc,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC5D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBW,EAAE,CAAC,oCAAoC,EAAE,MAAK;MAC5CV,OAAO,CAAC8B,OAAO,CAAC,CAAC,CAAC,CAACZ,SAAS,CAAEa,WAAW,IAAI;QAC3CpB,MAAM,CAACoB,WAAW,CAAC,CAACX,OAAO,CAACP,eAAe,CAAC;MAC9C,CAAC,CAAC;MAEF,MAAMQ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAI,CAAC;MAC7CS,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACb,eAAe,CAAC;IAC5B,CAAC,CAAC;IAEFH,EAAE,CAAC,qDAAqD,EAAE,MAAK;MAC7DV,OAAO,CAAC8B,OAAO,CAAC,GAAG,CAAC,CAACZ,SAAS,CAAC;QAC7BS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,MAAM,CAAC;MAC/CmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB,MAAMiC,cAAc,GAA4B;MAC9CjB,IAAI,EAAE;KACP;IAEDL,EAAE,CAAC,kCAAkC,EAAE,MAAK;MAC1CV,OAAO,CAACiC,MAAM,CAACD,cAAc,CAAC,CAACd,SAAS,CAAEa,WAAW,IAAI;QACvDpB,MAAM,CAACoB,WAAW,CAAC,CAACX,OAAO,CAACP,eAAe,CAAC;MAC9C,CAAC,CAAC;MAEF,MAAMQ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCS,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCd,MAAM,CAACU,GAAG,CAACE,OAAO,CAACW,IAAI,CAAC,CAACd,OAAO,CAACY,cAAc,CAAC;MAChDX,GAAG,CAACK,KAAK,CAACb,eAAe,CAAC;IAC5B,CAAC,CAAC;IAEFH,EAAE,CAAC,gDAAgD,EAAE,MAAK;MACxDV,OAAO,CAACiC,MAAM,CAACD,cAAc,CAAC,CAACd,SAAS,CAAC;QACvCS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB,MAAMoC,UAAU,GAAyB;MACvCpB,IAAI,EAAE;KACP;IAEDL,EAAE,CAAC,8BAA8B,EAAE,MAAK;MACtCV,OAAO,CAACoC,MAAM,CAAC,CAAC,EAAED,UAAU,CAAC,CAACjB,SAAS,CAAEa,WAAW,IAAI;QACtDpB,MAAM,CAACoB,WAAW,CAAC,CAACX,OAAO,CAAC;UAAE,GAAGP,eAAe;UAAE,GAAGsB;QAAU,CAAE,CAAC;MACpE,CAAC,CAAC;MAEF,MAAMd,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAI,CAAC;MAC7CS,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCd,MAAM,CAACU,GAAG,CAACE,OAAO,CAACW,IAAI,CAAC,CAACd,OAAO,CAACe,UAAU,CAAC;MAC5Cd,GAAG,CAACK,KAAK,CAAC;QAAE,GAAGb,eAAe;QAAE,GAAGsB;MAAU,CAAE,CAAC;IAClD,CAAC,CAAC;IAEFzB,EAAE,CAAC,gDAAgD,EAAE,MAAK;MACxDV,OAAO,CAACoC,MAAM,CAAC,CAAC,EAAED,UAAU,CAAC,CAACjB,SAAS,CAAC;QACtCS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAI,CAAC;MAC7CmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,aAAa,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBW,EAAE,CAAC,8BAA8B,EAAE,MAAK;MACtCV,OAAO,CAACqC,MAAM,CAAC,CAAC,CAAC,CAACnB,SAAS,CAAEoB,QAAQ,IAAI;QACvC3B,MAAM,CAAC2B,QAAQ,CAAC,CAACC,QAAQ,EAAE;MAC7B,CAAC,CAAC;MAEF,MAAMlB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAI,CAAC;MAC7CS,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;IAEFhB,EAAE,CAAC,gDAAgD,EAAE,MAAK;MACxDV,OAAO,CAACqC,MAAM,CAAC,CAAC,CAAC,CAACnB,SAAS,CAAC;QAC1BS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAI,CAAC;MAC7CmB,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,WAAW,EAAE,MAAK;IACzBW,EAAE,CAAC,sCAAsC,EAAE,MAAK;MAC9C,MAAMK,IAAI,GAAG,UAAU;MAEvBf,OAAO,CAACwC,SAAS,CAACzB,IAAI,CAAC,CAACG,SAAS,CAAEa,WAAW,IAAI;QAChDpB,MAAM,CAACoB,WAAW,CAAC,CAACX,OAAO,CAACP,eAAe,CAAC;MAC9C,CAAC,CAAC;MAEF,MAAMQ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,SAASa,IAAI,EAAE,CAAC;MACxDJ,MAAM,CAACU,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACb,eAAe,CAAC;IAC5B,CAAC,CAAC;IAEFH,EAAE,CAAC,uDAAuD,EAAE,MAAK;MAC/D,MAAMK,IAAI,GAAG,aAAa;MAE1Bf,OAAO,CAACwC,SAAS,CAACzB,IAAI,CAAC,CAACG,SAAS,CAAC;QAChCS,KAAK,EAAGA,KAAK,IAAI;UACfhB,MAAM,CAACgB,KAAK,CAACC,MAAM,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,SAASa,IAAI,EAAE,CAAC;MACxDM,GAAG,CAACM,KAAK,CAAC,IAAIE,UAAU,CAAC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAE;MAAG,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}