import { Component, Inject, OnInit } from '@angular/core';
import {
  <PERSON><PERSON><PERSON>er,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { CCP } from '@contract-management/models/ccp.model';
import { CcpService } from '@contract-management/services/ccp.service';
import { ContractValuesService } from '@contract-management/services/contract-values.service';
import { AlertService } from '@shared/services/alert.service';
import { NgxCurrencyDirective } from 'ngx-currency';
import { map } from 'rxjs/operators';
import { CurrencyPipe } from '@angular/common';

@Component({
  selector: 'app-ccp-dialog',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    NgxCurrencyDirective,
    CurrencyPipe,
  ],
  templateUrl: './ccp-dialog.component.html',
  styleUrl: './ccp-dialog.component.scss',
})
export class CcpDialogComponent implements OnInit {
  ccpForm: FormGroup = this.formBuilder.group({
    expenseObjectUseCcp: ['', Validators.required],
    expenseObjectDescription: ['', Validators.required],
    value: [null, [Validators.required, Validators.min(0)]],
    additionalValue: [null, [Validators.min(0)]],
  });

  totalContractValue = 0;
  currentTotalCcpValue = 0;
  remainingValue = 0;

  constructor(
    private readonly dialogRef: MatDialogRef<CcpDialogComponent>,
    private readonly formBuilder: FormBuilder,
    private readonly ccpService: CcpService,
    private readonly contractValuesService: ContractValuesService,
    private readonly alert: AlertService,
    @Inject(MAT_DIALOG_DATA)
    public data: { ccp?: CCP; contractId: number; totalCcpValue: number },
  ) {
    if (this.data.ccp) {
      this.ccpForm.patchValue({
        expenseObjectUseCcp: this.data.ccp.expenseObjectUseCcp,
        expenseObjectDescription: this.data.ccp.expenseObjectDescription,
        value: this.data.ccp.value,
        additionalValue: this.data.ccp.additionalValue,
      });
      this.ccpForm.get('value')?.disable();

      this.ccpForm.get('additionalValue')?.addValidators((control) => {
        const newValue = control.value || 0;
        const previousValue = this.data.ccp?.additionalValue || 0;
        if (newValue < previousValue) {
          return { lessThanPrevious: true };
        }
        const totalCcpValue = (this.data.ccp?.value || 0) + newValue;
        const availableValue =
          this.totalContractValue -
          (this.currentTotalCcpValue -
            (this.data.ccp?.value || 0) -
            previousValue);
        return totalCcpValue <= availableValue
          ? null
          : { maxValueExceeded: true };
      });
    } else {
      this.ccpForm.get('additionalValue')?.disable();
      this.ccpForm.get('additionalValue')?.setValue(0);
    }
    this.currentTotalCcpValue = this.data.totalCcpValue;
  }

  ngOnInit(): void {
    this.loadContractValues();

    if (!this.data.ccp) {
      this.ccpForm.get('value')?.valueChanges.subscribe((value) => {
        if (value !== null) {
          const maxAllowed = this.remainingValue + (this.data.ccp?.value || 0);
          if (value > maxAllowed) {
            this.ccpForm.get('value')?.setErrors({ maxValueExceeded: true });
          }
        }
      });
    }
  }

  private loadContractValues(): void {
    this.contractValuesService
      .getAllByContractId(this.data.contractId)
      .pipe(
        map((contractValues) => {
          return contractValues.reduce((sum, cv) => sum + cv.numericValue, 0);
        }),
      )
      .subscribe({
        next: (totalValue) => {
          this.totalContractValue = totalValue;
          const currentCcpValue = this.data.ccp?.value || 0;
          this.remainingValue =
            this.totalContractValue -
            (this.currentTotalCcpValue - currentCcpValue);
        },
        error: (_) => {
          this.alert.error(error.error?.detail ?? 'Error al cargar los valores del contrato');
        },
      });
  }

  onSubmit(): void {
    if (this.ccpForm.valid) {
      const formValue = this.ccpForm.getRawValue();
      const ccpData = {
        ...formValue,
        contractId: this.data.contractId,
      };

      const operation = this.data.ccp
        ? this.ccpService.update(this.data.ccp.id, ccpData)
        : this.ccpService.create(ccpData);

      operation.subscribe({
        next: (ccp) => {
          this.alert.success(
            `CCP ${this.data.ccp ? 'editado' : 'creado'} exitosamente`,
          );
          this.dialogRef.close(ccp);
        },
        error: (_) => {
          this.alert.error(
            error.error?.detail ?? `Error al ${this.data.ccp ? 'editar' : 'crear'} el CCP`
          );
        },
      });
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
