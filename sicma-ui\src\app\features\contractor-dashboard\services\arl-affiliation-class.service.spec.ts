import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { ARLAffiliationClass } from '@contractor-dashboard/models/arl-affiliation-class.model';
import { environment } from '@env';
import { ArlAffiliationClassService } from './arl-affiliation-class.service';

describe('ArlAffiliationClassService', () => {
  let service: ArlAffiliationClassService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/arl-affiliation-classes`;

  const mockArlAffiliationClass: ARLAffiliationClass = {
    id: 1,
    name: 'Class I',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ArlAffiliationClassService],
    });
    service = TestBed.inject(ArlAffiliationClassService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all ARL affiliation classes', () => {
      const mockClasses = [mockArlAffiliationClass];

      service.getAll().subscribe((classes) => {
        expect(classes).toEqual(mockClasses);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockClasses);
    });

    it('should handle error when getting all classes', () => {
      service.getAll().subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('getById', () => {
    it('should return a single ARL affiliation class by id', () => {
      const id = 1;

      service.getById(id).subscribe((affiliationClass) => {
        expect(affiliationClass).toEqual(mockArlAffiliationClass);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockArlAffiliationClass);
    });

    it('should handle error when getting class by id', () => {
      const id = 999;

      service.getById(id).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('create', () => {
    it('should create a new ARL affiliation class', () => {
      const newClass: Omit<ARLAffiliationClass, 'id'> = {
        name: 'New Class',
      };

      service.create(newClass).subscribe((affiliationClass) => {
        expect(affiliationClass).toEqual(mockArlAffiliationClass);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newClass);
      req.flush(mockArlAffiliationClass);
    });

    it('should handle error when creating class', () => {
      const newClass: Omit<ARLAffiliationClass, 'id'> = {
        name: 'New Class',
      };

      service.create(newClass).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('update', () => {
    it('should update an existing ARL affiliation class', () => {
      const id = 1;
      const updateData: Partial<ARLAffiliationClass> = {
        name: 'Updated Class',
      };

      service.update(id, updateData).subscribe((affiliationClass) => {
        expect(affiliationClass).toEqual(mockArlAffiliationClass);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockArlAffiliationClass);
    });

    it('should handle error when updating class', () => {
      const id = 1;
      const updateData: Partial<ARLAffiliationClass> = {
        name: 'Updated Class',
      };

      service.update(id, updateData).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('delete', () => {
    it('should delete an ARL affiliation class', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });

    it('should handle error when deleting class', () => {
      const id = 1;

      service.delete(id).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });
});