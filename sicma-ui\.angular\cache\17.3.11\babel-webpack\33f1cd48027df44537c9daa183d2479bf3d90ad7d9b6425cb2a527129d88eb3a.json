{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/Github/SICMA/sicma-ui/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nfunction cov_10pleoxbv6() {\n  var path = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contract-management\\\\components\\\\obligations-list\\\\obligations-list.component.ts\";\n  var hash = \"ea9263e1d80e9bc0699ee24a5e670a0f5c1b013e\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contract-management\\\\components\\\\obligations-list\\\\obligations-list.component.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 15,\n          column: 31\n        },\n        end: {\n          line: 148,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 17,\n          column: 8\n        },\n        end: {\n          line: 17,\n          column: 51\n        }\n      },\n      \"2\": {\n        start: {\n          line: 18,\n          column: 8\n        },\n        end: {\n          line: 18,\n          column: 27\n        }\n      },\n      \"3\": {\n        start: {\n          line: 19,\n          column: 8\n        },\n        end: {\n          line: 19,\n          column: 40\n        }\n      },\n      \"4\": {\n        start: {\n          line: 20,\n          column: 8\n        },\n        end: {\n          line: 20,\n          column: 52\n        }\n      },\n      \"5\": {\n        start: {\n          line: 21,\n          column: 8\n        },\n        end: {\n          line: 21,\n          column: 34\n        }\n      },\n      \"6\": {\n        start: {\n          line: 22,\n          column: 8\n        },\n        end: {\n          line: 22,\n          column: 64\n        }\n      },\n      \"7\": {\n        start: {\n          line: 23,\n          column: 8\n        },\n        end: {\n          line: 23,\n          column: 33\n        }\n      },\n      \"8\": {\n        start: {\n          line: 26,\n          column: 8\n        },\n        end: {\n          line: 33,\n          column: 9\n        }\n      },\n      \"9\": {\n        start: {\n          line: 27,\n          column: 12\n        },\n        end: {\n          line: 32,\n          column: 81\n        }\n      },\n      \"10\": {\n        start: {\n          line: 28,\n          column: 38\n        },\n        end: {\n          line: 31,\n          column: 13\n        }\n      },\n      \"11\": {\n        start: {\n          line: 32,\n          column: 32\n        },\n        end: {\n          line: 32,\n          column: 79\n        }\n      },\n      \"12\": {\n        start: {\n          line: 36,\n          column: 30\n        },\n        end: {\n          line: 40,\n          column: 9\n        }\n      },\n      \"13\": {\n        start: {\n          line: 41,\n          column: 8\n        },\n        end: {\n          line: 41,\n          column: 72\n        }\n      },\n      \"14\": {\n        start: {\n          line: 44,\n          column: 8\n        },\n        end: {\n          line: 49,\n          column: 9\n        }\n      },\n      \"15\": {\n        start: {\n          line: 45,\n          column: 12\n        },\n        end: {\n          line: 48,\n          column: 14\n        }\n      },\n      \"16\": {\n        start: {\n          line: 50,\n          column: 8\n        },\n        end: {\n          line: 50,\n          column: 34\n        }\n      },\n      \"17\": {\n        start: {\n          line: 53,\n          column: 38\n        },\n        end: {\n          line: 53,\n          column: 48\n        }\n      },\n      \"18\": {\n        start: {\n          line: 54,\n          column: 8\n        },\n        end: {\n          line: 54,\n          column: 38\n        }\n      },\n      \"19\": {\n        start: {\n          line: 55,\n          column: 26\n        },\n        end: {\n          line: 57,\n          column: 59\n        }\n      },\n      \"20\": {\n        start: {\n          line: 58,\n          column: 8\n        },\n        end: {\n          line: 96,\n          column: 11\n        }\n      },\n      \"21\": {\n        start: {\n          line: 61,\n          column: 16\n        },\n        end: {\n          line: 72,\n          column: 17\n        }\n      },\n      \"22\": {\n        start: {\n          line: 62,\n          column: 20\n        },\n        end: {\n          line: 64,\n          column: 30\n        }\n      },\n      \"23\": {\n        start: {\n          line: 62,\n          column: 67\n        },\n        end: {\n          line: 64,\n          column: 28\n        }\n      },\n      \"24\": {\n        start: {\n          line: 65,\n          column: 20\n        },\n        end: {\n          line: 65,\n          column: 66\n        }\n      },\n      \"25\": {\n        start: {\n          line: 68,\n          column: 20\n        },\n        end: {\n          line: 71,\n          column: 22\n        }\n      },\n      \"26\": {\n        start: {\n          line: 69,\n          column: 63\n        },\n        end: {\n          line: 69,\n          column: 68\n        }\n      },\n      \"27\": {\n        start: {\n          line: 73,\n          column: 16\n        },\n        end: {\n          line: 73,\n          column: 115\n        }\n      },\n      \"28\": {\n        start: {\n          line: 73,\n          column: 66\n        },\n        end: {\n          line: 73,\n          column: 113\n        }\n      },\n      \"29\": {\n        start: {\n          line: 74,\n          column: 16\n        },\n        end: {\n          line: 74,\n          column: 43\n        }\n      },\n      \"30\": {\n        start: {\n          line: 75,\n          column: 16\n        },\n        end: {\n          line: 75,\n          column: 113\n        }\n      },\n      \"31\": {\n        start: {\n          line: 76,\n          column: 16\n        },\n        end: {\n          line: 76,\n          column: 46\n        }\n      },\n      \"32\": {\n        start: {\n          line: 79,\n          column: 16\n        },\n        end: {\n          line: 93,\n          column: 17\n        }\n      },\n      \"33\": {\n        start: {\n          line: 81,\n          column: 24\n        },\n        end: {\n          line: 86,\n          column: 25\n        }\n      },\n      \"34\": {\n        start: {\n          line: 82,\n          column: 28\n        },\n        end: {\n          line: 82,\n          column: 124\n        }\n      },\n      \"35\": {\n        start: {\n          line: 85,\n          column: 28\n        },\n        end: {\n          line: 85,\n          column: 142\n        }\n      },\n      \"36\": {\n        start: {\n          line: 87,\n          column: 24\n        },\n        end: {\n          line: 87,\n          column: 30\n        }\n      },\n      \"37\": {\n        start: {\n          line: 89,\n          column: 24\n        },\n        end: {\n          line: 89,\n          column: 131\n        }\n      },\n      \"38\": {\n        start: {\n          line: 90,\n          column: 24\n        },\n        end: {\n          line: 90,\n          column: 30\n        }\n      },\n      \"39\": {\n        start: {\n          line: 92,\n          column: 24\n        },\n        end: {\n          line: 92,\n          column: 136\n        }\n      },\n      \"40\": {\n        start: {\n          line: 94,\n          column: 16\n        },\n        end: {\n          line: 94,\n          column: 42\n        }\n      },\n      \"41\": {\n        start: {\n          line: 99,\n          column: 8\n        },\n        end: {\n          line: 113,\n          column: 9\n        }\n      },\n      \"42\": {\n        start: {\n          line: 100,\n          column: 12\n        },\n        end: {\n          line: 100,\n          column: 90\n        }\n      },\n      \"43\": {\n        start: {\n          line: 100,\n          column: 71\n        },\n        end: {\n          line: 100,\n          column: 88\n        }\n      },\n      \"44\": {\n        start: {\n          line: 101,\n          column: 12\n        },\n        end: {\n          line: 101,\n          column: 42\n        }\n      },\n      \"45\": {\n        start: {\n          line: 103,\n          column: 13\n        },\n        end: {\n          line: 113,\n          column: 9\n        }\n      },\n      \"46\": {\n        start: {\n          line: 104,\n          column: 34\n        },\n        end: {\n          line: 104,\n          column: 68\n        }\n      },\n      \"47\": {\n        start: {\n          line: 105,\n          column: 12\n        },\n        end: {\n          line: 105,\n          column: 49\n        }\n      },\n      \"48\": {\n        start: {\n          line: 106,\n          column: 12\n        },\n        end: {\n          line: 106,\n          column: 53\n        }\n      },\n      \"49\": {\n        start: {\n          line: 107,\n          column: 12\n        },\n        end: {\n          line: 107,\n          column: 54\n        }\n      },\n      \"50\": {\n        start: {\n          line: 108,\n          column: 12\n        },\n        end: {\n          line: 108,\n          column: 39\n        }\n      },\n      \"51\": {\n        start: {\n          line: 109,\n          column: 12\n        },\n        end: {\n          line: 109,\n          column: 42\n        }\n      },\n      \"52\": {\n        start: {\n          line: 112,\n          column: 12\n        },\n        end: {\n          line: 112,\n          column: 39\n        }\n      },\n      \"53\": {\n        start: {\n          line: 116,\n          column: 8\n        },\n        end: {\n          line: 117,\n          column: 19\n        }\n      },\n      \"54\": {\n        start: {\n          line: 117,\n          column: 12\n        },\n        end: {\n          line: 117,\n          column: 19\n        }\n      },\n      \"55\": {\n        start: {\n          line: 118,\n          column: 26\n        },\n        end: {\n          line: 118,\n          column: 102\n        }\n      },\n      \"56\": {\n        start: {\n          line: 119,\n          column: 8\n        },\n        end: {\n          line: 137,\n          column: 9\n        }\n      },\n      \"57\": {\n        start: {\n          line: 120,\n          column: 12\n        },\n        end: {\n          line: 136,\n          column: 15\n        }\n      },\n      \"58\": {\n        start: {\n          line: 122,\n          column: 20\n        },\n        end: {\n          line: 124,\n          column: 89\n        }\n      },\n      \"59\": {\n        start: {\n          line: 123,\n          column: 40\n        },\n        end: {\n          line: 123,\n          column: 63\n        }\n      },\n      \"60\": {\n        start: {\n          line: 124,\n          column: 40\n        },\n        end: {\n          line: 124,\n          column: 87\n        }\n      },\n      \"61\": {\n        start: {\n          line: 125,\n          column: 20\n        },\n        end: {\n          line: 125,\n          column: 77\n        }\n      },\n      \"62\": {\n        start: {\n          line: 126,\n          column: 20\n        },\n        end: {\n          line: 126,\n          column: 50\n        }\n      },\n      \"63\": {\n        start: {\n          line: 129,\n          column: 20\n        },\n        end: {\n          line: 134,\n          column: 21\n        }\n      },\n      \"64\": {\n        start: {\n          line: 130,\n          column: 24\n        },\n        end: {\n          line: 130,\n          column: 156\n        }\n      },\n      \"65\": {\n        start: {\n          line: 133,\n          column: 24\n        },\n        end: {\n          line: 133,\n          column: 102\n        }\n      },\n      \"66\": {\n        start: {\n          line: 139,\n          column: 13\n        },\n        end: {\n          line: 142,\n          column: 6\n        }\n      },\n      \"67\": {\n        start: {\n          line: 139,\n          column: 41\n        },\n        end: {\n          line: 142,\n          column: 5\n        }\n      },\n      \"68\": {\n        start: {\n          line: 143,\n          column: 13\n        },\n        end: {\n          line: 147,\n          column: 6\n        }\n      },\n      \"69\": {\n        start: {\n          line: 149,\n          column: 0\n        },\n        end: {\n          line: 166,\n          column: 29\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 16,\n            column: 4\n          },\n          end: {\n            line: 16,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 16,\n            column: 42\n          },\n          end: {\n            line: 24,\n            column: 5\n          }\n        },\n        line: 16\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 25,\n            column: 4\n          },\n          end: {\n            line: 25,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 25,\n            column: 15\n          },\n          end: {\n            line: 34,\n            column: 5\n          }\n        },\n        line: 25\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 28,\n            column: 21\n          },\n          end: {\n            line: 28,\n            column: 22\n          }\n        },\n        loc: {\n          start: {\n            line: 28,\n            column: 38\n          },\n          end: {\n            line: 31,\n            column: 13\n          }\n        },\n        line: 28\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 32,\n            column: 22\n          },\n          end: {\n            line: 32,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 32,\n            column: 32\n          },\n          end: {\n            line: 32,\n            column: 79\n          }\n        },\n        line: 32\n      },\n      \"4\": {\n        name: \"(anonymous_4)\",\n        decl: {\n          start: {\n            line: 35,\n            column: 4\n          },\n          end: {\n            line: 35,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 35,\n            column: 23\n          },\n          end: {\n            line: 42,\n            column: 5\n          }\n        },\n        line: 35\n      },\n      \"5\": {\n        name: \"(anonymous_5)\",\n        decl: {\n          start: {\n            line: 43,\n            column: 4\n          },\n          end: {\n            line: 43,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 43,\n            column: 31\n          },\n          end: {\n            line: 51,\n            column: 5\n          }\n        },\n        line: 43\n      },\n      \"6\": {\n        name: \"(anonymous_6)\",\n        decl: {\n          start: {\n            line: 52,\n            column: 4\n          },\n          end: {\n            line: 52,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 52,\n            column: 31\n          },\n          end: {\n            line: 97,\n            column: 5\n          }\n        },\n        line: 52\n      },\n      \"7\": {\n        name: \"(anonymous_7)\",\n        decl: {\n          start: {\n            line: 59,\n            column: 18\n          },\n          end: {\n            line: 59,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 59,\n            column: 39\n          },\n          end: {\n            line: 77,\n            column: 13\n          }\n        },\n        line: 59\n      },\n      \"8\": {\n        name: \"(anonymous_8)\",\n        decl: {\n          start: {\n            line: 62,\n            column: 59\n          },\n          end: {\n            line: 62,\n            column: 60\n          }\n        },\n        loc: {\n          start: {\n            line: 62,\n            column: 67\n          },\n          end: {\n            line: 64,\n            column: 28\n          }\n        },\n        line: 62\n      },\n      \"9\": {\n        name: \"(anonymous_9)\",\n        decl: {\n          start: {\n            line: 69,\n            column: 55\n          },\n          end: {\n            line: 69,\n            column: 56\n          }\n        },\n        loc: {\n          start: {\n            line: 69,\n            column: 63\n          },\n          end: {\n            line: 69,\n            column: 68\n          }\n        },\n        line: 69\n      },\n      \"10\": {\n        name: \"(anonymous_10)\",\n        decl: {\n          start: {\n            line: 73,\n            column: 56\n          },\n          end: {\n            line: 73,\n            column: 57\n          }\n        },\n        loc: {\n          start: {\n            line: 73,\n            column: 66\n          },\n          end: {\n            line: 73,\n            column: 113\n          }\n        },\n        line: 73\n      },\n      \"11\": {\n        name: \"(anonymous_11)\",\n        decl: {\n          start: {\n            line: 78,\n            column: 19\n          },\n          end: {\n            line: 78,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 78,\n            column: 35\n          },\n          end: {\n            line: 95,\n            column: 13\n          }\n        },\n        line: 78\n      },\n      \"12\": {\n        name: \"(anonymous_12)\",\n        decl: {\n          start: {\n            line: 98,\n            column: 4\n          },\n          end: {\n            line: 98,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 98,\n            column: 27\n          },\n          end: {\n            line: 114,\n            column: 5\n          }\n        },\n        line: 98\n      },\n      \"13\": {\n        name: \"(anonymous_13)\",\n        decl: {\n          start: {\n            line: 100,\n            column: 63\n          },\n          end: {\n            line: 100,\n            column: 64\n          }\n        },\n        loc: {\n          start: {\n            line: 100,\n            column: 71\n          },\n          end: {\n            line: 100,\n            column: 88\n          }\n        },\n        line: 100\n      },\n      \"14\": {\n        name: \"(anonymous_14)\",\n        decl: {\n          start: {\n            line: 115,\n            column: 4\n          },\n          end: {\n            line: 115,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 115,\n            column: 39\n          },\n          end: {\n            line: 138,\n            column: 5\n          }\n        },\n        line: 115\n      },\n      \"15\": {\n        name: \"(anonymous_15)\",\n        decl: {\n          start: {\n            line: 121,\n            column: 22\n          },\n          end: {\n            line: 121,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 121,\n            column: 28\n          },\n          end: {\n            line: 127,\n            column: 17\n          }\n        },\n        line: 121\n      },\n      \"16\": {\n        name: \"(anonymous_16)\",\n        decl: {\n          start: {\n            line: 123,\n            column: 32\n          },\n          end: {\n            line: 123,\n            column: 33\n          }\n        },\n        loc: {\n          start: {\n            line: 123,\n            column: 40\n          },\n          end: {\n            line: 123,\n            column: 63\n          }\n        },\n        line: 123\n      },\n      \"17\": {\n        name: \"(anonymous_17)\",\n        decl: {\n          start: {\n            line: 124,\n            column: 30\n          },\n          end: {\n            line: 124,\n            column: 31\n          }\n        },\n        loc: {\n          start: {\n            line: 124,\n            column: 40\n          },\n          end: {\n            line: 124,\n            column: 87\n          }\n        },\n        line: 124\n      },\n      \"18\": {\n        name: \"(anonymous_18)\",\n        decl: {\n          start: {\n            line: 128,\n            column: 23\n          },\n          end: {\n            line: 128,\n            column: 24\n          }\n        },\n        loc: {\n          start: {\n            line: 128,\n            column: 34\n          },\n          end: {\n            line: 135,\n            column: 17\n          }\n        },\n        line: 128\n      },\n      \"19\": {\n        name: \"(anonymous_19)\",\n        decl: {\n          start: {\n            line: 139,\n            column: 35\n          },\n          end: {\n            line: 139,\n            column: 36\n          }\n        },\n        loc: {\n          start: {\n            line: 139,\n            column: 41\n          },\n          end: {\n            line: 142,\n            column: 5\n          }\n        },\n        line: 139\n      }\n    },\n    branchMap: {\n      \"0\": {\n        loc: {\n          start: {\n            line: 26,\n            column: 8\n          },\n          end: {\n            line: 33,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 26,\n            column: 8\n          },\n          end: {\n            line: 33,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 26\n      },\n      \"1\": {\n        loc: {\n          start: {\n            line: 32,\n            column: 33\n          },\n          end: {\n            line: 32,\n            column: 53\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 32,\n            column: 33\n          },\n          end: {\n            line: 32,\n            column: 41\n          }\n        }, {\n          start: {\n            line: 32,\n            column: 45\n          },\n          end: {\n            line: 32,\n            column: 53\n          }\n        }],\n        line: 32\n      },\n      \"2\": {\n        loc: {\n          start: {\n            line: 32,\n            column: 58\n          },\n          end: {\n            line: 32,\n            column: 78\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 32,\n            column: 58\n          },\n          end: {\n            line: 32,\n            column: 66\n          }\n        }, {\n          start: {\n            line: 32,\n            column: 70\n          },\n          end: {\n            line: 32,\n            column: 78\n          }\n        }],\n        line: 32\n      },\n      \"3\": {\n        loc: {\n          start: {\n            line: 44,\n            column: 8\n          },\n          end: {\n            line: 49,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 44,\n            column: 8\n          },\n          end: {\n            line: 49,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 44\n      },\n      \"4\": {\n        loc: {\n          start: {\n            line: 55,\n            column: 26\n          },\n          end: {\n            line: 57,\n            column: 59\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 56,\n            column: 14\n          },\n          end: {\n            line: 56,\n            column: 78\n          }\n        }, {\n          start: {\n            line: 57,\n            column: 14\n          },\n          end: {\n            line: 57,\n            column: 59\n          }\n        }],\n        line: 55\n      },\n      \"5\": {\n        loc: {\n          start: {\n            line: 61,\n            column: 16\n          },\n          end: {\n            line: 72,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 61,\n            column: 16\n          },\n          end: {\n            line: 72,\n            column: 17\n          }\n        }, {\n          start: {\n            line: 67,\n            column: 21\n          },\n          end: {\n            line: 72,\n            column: 17\n          }\n        }],\n        line: 61\n      },\n      \"6\": {\n        loc: {\n          start: {\n            line: 62,\n            column: 67\n          },\n          end: {\n            line: 64,\n            column: 28\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 63,\n            column: 26\n          },\n          end: {\n            line: 63,\n            column: 64\n          }\n        }, {\n          start: {\n            line: 64,\n            column: 26\n          },\n          end: {\n            line: 64,\n            column: 28\n          }\n        }],\n        line: 62\n      },\n      \"7\": {\n        loc: {\n          start: {\n            line: 73,\n            column: 67\n          },\n          end: {\n            line: 73,\n            column: 87\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 73,\n            column: 67\n          },\n          end: {\n            line: 73,\n            column: 75\n          }\n        }, {\n          start: {\n            line: 73,\n            column: 79\n          },\n          end: {\n            line: 73,\n            column: 87\n          }\n        }],\n        line: 73\n      },\n      \"8\": {\n        loc: {\n          start: {\n            line: 73,\n            column: 92\n          },\n          end: {\n            line: 73,\n            column: 112\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 73,\n            column: 92\n          },\n          end: {\n            line: 73,\n            column: 100\n          }\n        }, {\n          start: {\n            line: 73,\n            column: 104\n          },\n          end: {\n            line: 73,\n            column: 112\n          }\n        }],\n        line: 73\n      },\n      \"9\": {\n        loc: {\n          start: {\n            line: 75,\n            column: 49\n          },\n          end: {\n            line: 75,\n            column: 95\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 75,\n            column: 69\n          },\n          end: {\n            line: 75,\n            column: 82\n          }\n        }, {\n          start: {\n            line: 75,\n            column: 85\n          },\n          end: {\n            line: 75,\n            column: 95\n          }\n        }],\n        line: 75\n      },\n      \"10\": {\n        loc: {\n          start: {\n            line: 79,\n            column: 16\n          },\n          end: {\n            line: 93,\n            column: 17\n          }\n        },\n        type: \"switch\",\n        locations: [{\n          start: {\n            line: 80,\n            column: 20\n          },\n          end: {\n            line: 87,\n            column: 30\n          }\n        }, {\n          start: {\n            line: 88,\n            column: 20\n          },\n          end: {\n            line: 90,\n            column: 30\n          }\n        }, {\n          start: {\n            line: 91,\n            column: 20\n          },\n          end: {\n            line: 92,\n            column: 136\n          }\n        }],\n        line: 79\n      },\n      \"11\": {\n        loc: {\n          start: {\n            line: 81,\n            column: 24\n          },\n          end: {\n            line: 86,\n            column: 25\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 81,\n            column: 24\n          },\n          end: {\n            line: 86,\n            column: 25\n          }\n        }, {\n          start: {\n            line: 84,\n            column: 29\n          },\n          end: {\n            line: 86,\n            column: 25\n          }\n        }],\n        line: 81\n      },\n      \"12\": {\n        loc: {\n          start: {\n            line: 92,\n            column: 78\n          },\n          end: {\n            line: 92,\n            column: 118\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 92,\n            column: 94\n          },\n          end: {\n            line: 92,\n            column: 106\n          }\n        }, {\n          start: {\n            line: 92,\n            column: 109\n          },\n          end: {\n            line: 92,\n            column: 118\n          }\n        }],\n        line: 92\n      },\n      \"13\": {\n        loc: {\n          start: {\n            line: 99,\n            column: 8\n          },\n          end: {\n            line: 113,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 99,\n            column: 8\n          },\n          end: {\n            line: 113,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 103,\n            column: 13\n          },\n          end: {\n            line: 113,\n            column: 9\n          }\n        }],\n        line: 99\n      },\n      \"14\": {\n        loc: {\n          start: {\n            line: 103,\n            column: 13\n          },\n          end: {\n            line: 113,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 103,\n            column: 13\n          },\n          end: {\n            line: 113,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 111,\n            column: 13\n          },\n          end: {\n            line: 113,\n            column: 9\n          }\n        }],\n        line: 103\n      },\n      \"15\": {\n        loc: {\n          start: {\n            line: 103,\n            column: 17\n          },\n          end: {\n            line: 103,\n            column: 68\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 103,\n            column: 17\n          },\n          end: {\n            line: 103,\n            column: 30\n          }\n        }, {\n          start: {\n            line: 103,\n            column: 34\n          },\n          end: {\n            line: 103,\n            column: 68\n          }\n        }],\n        line: 103\n      },\n      \"16\": {\n        loc: {\n          start: {\n            line: 116,\n            column: 8\n          },\n          end: {\n            line: 117,\n            column: 19\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 116,\n            column: 8\n          },\n          end: {\n            line: 117,\n            column: 19\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 116\n      },\n      \"17\": {\n        loc: {\n          start: {\n            line: 119,\n            column: 8\n          },\n          end: {\n            line: 137,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 119,\n            column: 8\n          },\n          end: {\n            line: 137,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 119\n      },\n      \"18\": {\n        loc: {\n          start: {\n            line: 124,\n            column: 41\n          },\n          end: {\n            line: 124,\n            column: 61\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 124,\n            column: 41\n          },\n          end: {\n            line: 124,\n            column: 49\n          }\n        }, {\n          start: {\n            line: 124,\n            column: 53\n          },\n          end: {\n            line: 124,\n            column: 61\n          }\n        }],\n        line: 124\n      },\n      \"19\": {\n        loc: {\n          start: {\n            line: 124,\n            column: 66\n          },\n          end: {\n            line: 124,\n            column: 86\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 124,\n            column: 66\n          },\n          end: {\n            line: 124,\n            column: 74\n          }\n        }, {\n          start: {\n            line: 124,\n            column: 78\n          },\n          end: {\n            line: 124,\n            column: 86\n          }\n        }],\n        line: 124\n      },\n      \"20\": {\n        loc: {\n          start: {\n            line: 129,\n            column: 20\n          },\n          end: {\n            line: 134,\n            column: 21\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 129,\n            column: 20\n          },\n          end: {\n            line: 134,\n            column: 21\n          }\n        }, {\n          start: {\n            line: 132,\n            column: 25\n          },\n          end: {\n            line: 134,\n            column: 21\n          }\n        }],\n        line: 129\n      },\n      \"21\": {\n        loc: {\n          start: {\n            line: 133,\n            column: 41\n          },\n          end: {\n            line: 133,\n            column: 100\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 133,\n            column: 41\n          },\n          end: {\n            line: 133,\n            column: 60\n          }\n        }, {\n          start: {\n            line: 133,\n            column: 64\n          },\n          end: {\n            line: 133,\n            column: 100\n          }\n        }],\n        line: 133\n      }\n    },\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0,\n      \"36\": 0,\n      \"37\": 0,\n      \"38\": 0,\n      \"39\": 0,\n      \"40\": 0,\n      \"41\": 0,\n      \"42\": 0,\n      \"43\": 0,\n      \"44\": 0,\n      \"45\": 0,\n      \"46\": 0,\n      \"47\": 0,\n      \"48\": 0,\n      \"49\": 0,\n      \"50\": 0,\n      \"51\": 0,\n      \"52\": 0,\n      \"53\": 0,\n      \"54\": 0,\n      \"55\": 0,\n      \"56\": 0,\n      \"57\": 0,\n      \"58\": 0,\n      \"59\": 0,\n      \"60\": 0,\n      \"61\": 0,\n      \"62\": 0,\n      \"63\": 0,\n      \"64\": 0,\n      \"65\": 0,\n      \"66\": 0,\n      \"67\": 0,\n      \"68\": 0,\n      \"69\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0\n    },\n    b: {\n      \"0\": [0, 0],\n      \"1\": [0, 0],\n      \"2\": [0, 0],\n      \"3\": [0, 0],\n      \"4\": [0, 0],\n      \"5\": [0, 0],\n      \"6\": [0, 0],\n      \"7\": [0, 0],\n      \"8\": [0, 0],\n      \"9\": [0, 0],\n      \"10\": [0, 0, 0],\n      \"11\": [0, 0],\n      \"12\": [0, 0],\n      \"13\": [0, 0],\n      \"14\": [0, 0],\n      \"15\": [0, 0],\n      \"16\": [0, 0],\n      \"17\": [0, 0],\n      \"18\": [0, 0],\n      \"19\": [0, 0],\n      \"20\": [0, 0],\n      \"21\": [0, 0]\n    },\n    inputSourceMap: {\n      version: 3,\n      file: \"obligations-list.component.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contract-management\\\\components\\\\obligations-list\\\\obligations-list.component.ts\"],\n      names: [],\n      mappings: \";;;AACA,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,KAAK,EAAU,MAAM,EAAE,MAAM,eAAe,CAAC;AAC/E,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAC7C,OAAO,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAC3D,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,kBAAkB,EAAE,MAAM,8BAA8B,CAAC;AAClE,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACzD,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACzD,OAAO,EAAE,gBAAgB,EAAE,MAAM,2BAA2B,CAAC;AAG7D,OAAO,EAAE,iBAAiB,EAAE,MAAM,kDAAkD,CAAC;AACrF,OAAO,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAoBvD,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;IAUnC,YACmB,iBAAoC,EACpC,KAAmB;QADnB,sBAAiB,GAAjB,iBAAiB,CAAmB;QACpC,UAAK,GAAL,KAAK,CAAc;QAV7B,uBAAkB,GAAG,KAAK;QACzB,sBAAiB,GAAG,IAAI,YAAY,EAAQ;QAEtD,oBAAe,GAAsB,EAAE,CAAC;QACxC,uBAAkB,GAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;QACrD,mBAAc,GACpB,EAAE,CAAC;IAKF,CAAC;IAEJ,QAAQ;QACN,IAAI,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE,CAAC;YAC/B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW;iBAC7C,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;gBACpB,GAAG,UAAU;gBACb,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;iBACF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,IAAI,QAAQ,CAAC,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAED,gBAAgB;QACd,MAAM,aAAa,GAAoB;YACrC,IAAI,EAAE,EAAE;YACR,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE;YAC5B,OAAO,EAAE,IAAI;SACd,CAAC;QACF,IAAI,CAAC,eAAe,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC;IAClE,CAAC;IAED,cAAc,CAAC,UAA2B;QACxC,IAAI,UAAU,CAAC,EAAE,EAAE,CAAC;YAClB,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,CAAC,GAAG;gBACnC,IAAI,EAAE,UAAU,CAAC,IAAI;gBACrB,MAAM,EAAE,UAAU,CAAC,MAAM;aAC1B,CAAC;QACJ,CAAC;QACD,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC;IAC5B,CAAC;IAED,cAAc,CAAC,UAA2B;QACxC,MAAM,EAAE,GAAG,cAAc,EAAE,GAAG,UAAU,CAAC;QACzC,OAAQ,cAA2C,CAAC,OAAO,CAAC;QAE5D,MAAM,SAAS,GAAG,cAAc,CAAC,EAAE;YACjC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE,EAAE,cAAc,CAAC;YAClE,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QAElD,SAAS,CAAC,SAAS,CAAC;YAClB,IAAI,EAAE,CAAC,eAA2B,EAAE,EAAE;gBACpC,IAAI,WAA8B,CAAC;gBACnC,IAAI,cAAc,CAAC,EAAE,EAAE,CAAC;oBACtB,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAC5C,EAAE,CAAC,EAAE,KAAK,eAAe,CAAC,EAAE;wBAC1B,CAAC,CAAC,EAAE,GAAG,eAAe,EAAE,OAAO,EAAE,KAAK,EAAE;wBACxC,CAAC,CAAC,EAAE,CACP,CAAC;oBACF,OAAO,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;gBAChD,CAAC;qBAAM,CAAC;oBACN,WAAW,GAAG;wBACZ,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;wBAC7C,EAAE,GAAG,eAAe,EAAE,OAAO,EAAE,KAAK,EAAE;qBACvC,CAAC;gBACJ,CAAC;gBACD,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,IAAI,CACrC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,IAAI,QAAQ,CAAC,CAC1D,CAAC;gBACF,UAAU,CAAC,OAAO,GAAG,KAAK,CAAC;gBAE3B,IAAI,CAAC,KAAK,CAAC,OAAO,CAChB,cACE,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,UACtC,gBAAgB,CACjB,CAAC;gBACF,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;YAChC,CAAC;YACD,KAAK,EAAE,CAAC,EAAE,MAAM,EAAqB,EAAE,EAAE;gBACvC,QAAQ,MAAM,EAAE,CAAC;oBACf,KAAK,GAAG;wBACN,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;4BAClC,IAAI,CAAC,KAAK,CAAC,OAAO,CAChB,oBAAoB,EACpB,qDAAqD,CACtD,CAAC;wBACJ,CAAC;6BAAM,CAAC;4BACN,IAAI,CAAC,KAAK,CAAC,OAAO,CAChB,oBAAoB,EACpB,uEAAuE,CACxE,CAAC;wBACJ,CAAC;wBACD,MAAM;oBACR,KAAK,GAAG;wBACN,IAAI,CAAC,KAAK,CAAC,OAAO,CAChB,sBAAsB,EACtB,8DAA8D,CAC/D,CAAC;wBACF,MAAM;oBACR;wBACE,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,uBAAuB,EACvB,YAAY,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,gBAAgB,CACrE,CAAC;gBACN,CAAC;gBACD,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC;YAC5B,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAED,UAAU,CAAC,UAA2B;QACpC,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC;YACnB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAChD,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,UAAU,CAC1B,CAAC;YACF,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;QAChC,CAAC;aAAM,IAAI,UAAU,CAAC,EAAE,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC;YAC/D,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YACzD,UAAU,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC;YACrC,UAAU,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC;YACzC,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YAC1C,UAAU,CAAC,OAAO,GAAG,KAAK,CAAC;YAC3B,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;QAChC,CAAC;aAAM,CAAC;YACN,UAAU,CAAC,OAAO,GAAG,KAAK,CAAC;QAC7B,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,UAA2B;QAChD,IAAI,CAAC,UAAU,CAAC,EAAE;YAAE,OAAO;QAE3B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CACxC,kDAAkD,CACnD,CAAC;QAEF,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC;gBACrD,IAAI,EAAE,GAAG,EAAE;oBACT,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe;yBACxC,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,CAAC;yBACvC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,IAAI,QAAQ,CAAC,CAAC,CAAC;oBACnE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;oBACzD,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;gBAChC,CAAC;gBACD,KAAK,EAAE,CAAC,KAAwB,EAAE,EAAE;oBAClC,IAAI,KAAK,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;wBACzB,IAAI,CAAC,KAAK,CAAC,OAAO,CAChB,sBAAsB,EACtB,uFAAuF,CACxF,CAAC;oBACJ,CAAC;yBAAM,CAAC;wBACN,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,oCAAoC,CAAC,CAAC;oBAChF,CAAC;gBACH,CAAC;aACF,CAAC,CAAC;QACL,CAAC;IACH,CAAC;;;;;;2BA9JA,KAAK;qCACL,KAAK;oCACL,MAAM;;;AAHI,wBAAwB;IAhBpC,SAAS,CAAC;QACT,QAAQ,EAAE,sBAAsB;QAChC,8BAAgD;QAEhD,UAAU,EAAE,IAAI;QAChB,OAAO,EAAE;YACP,aAAa;YACb,eAAe;YACf,aAAa;YACb,cAAc;YACd,kBAAkB;YAClB,cAAc;YACd,WAAW;YACX,gBAAgB;SACjB;;KACF,CAAC;GACW,wBAAwB,CAgKpC\",\n      sourcesContent: [\"import { HttpErrorResponse } from '@angular/common/http';\\nimport { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\\nimport { FormsModule } from '@angular/forms';\\nimport { MatButtonModule } from '@angular/material/button';\\nimport { MatCardModule } from '@angular/material/card';\\nimport { MatFormFieldModule } from '@angular/material/form-field';\\nimport { MatIconModule } from '@angular/material/icon';\\nimport { MatInputModule } from '@angular/material/input';\\nimport { MatTableModule } from '@angular/material/table';\\nimport { MatTooltipModule } from '@angular/material/tooltip';\\nimport { Contract } from '@contract-management/models/contract.model';\\nimport { Obligation } from '@contract-management/models/obligation.model';\\nimport { ObligationService } from '@contract-management/services/obligation.service';\\nimport { AlertService } from '@shared/services/alert.service';\\n\\ntype TableObligation = Obligation & { editing: boolean };\\n\\n@Component({\\n  selector: 'app-obligations-list',\\n  templateUrl: './obligations-list.component.html',\\n  styleUrl: './obligations-list.component.scss',\\n  standalone: true,\\n  imports: [\\n    MatCardModule,\\n    MatButtonModule,\\n    MatIconModule,\\n    MatTableModule,\\n    MatFormFieldModule,\\n    MatInputModule,\\n    FormsModule,\\n    MatTooltipModule,\\n  ],\\n})\\nexport class ObligationsListComponent implements OnInit {\\n  @Input() contract!: Contract;\\n  @Input() isContractFinished = false;\\n  @Output() obligationChanged = new EventEmitter<void>();\\n\\n  obligationsList: TableObligation[] = [];\\n  obligationsColumns: string[] = ['number', 'name', 'actions'];\\n  private originalStates: Record<number, { name: string; number?: number }> =\\n    {};\\n\\n  constructor(\\n    private readonly obligationService: ObligationService,\\n    private readonly alert: AlertService,\\n  ) {}\\n\\n  ngOnInit(): void {\\n    if (this.contract?.obligations) {\\n      this.obligationsList = this.contract.obligations\\n        .map((obligation) => ({\\n          ...obligation,\\n          editing: false,\\n        }))\\n        .sort((a, b) => (a.number ?? Infinity) - (b.number ?? Infinity));\\n    }\\n  }\\n\\n  addNewObligation(): void {\\n    const newObligation: TableObligation = {\\n      name: '',\\n      contractId: this.contract.id,\\n      editing: true,\\n    };\\n    this.obligationsList = [...this.obligationsList, newObligation];\\n  }\\n\\n  editObligation(obligation: TableObligation): void {\\n    if (obligation.id) {\\n      this.originalStates[obligation.id] = {\\n        name: obligation.name,\\n        number: obligation.number,\\n      };\\n    }\\n    obligation.editing = true;\\n  }\\n\\n  saveObligation(obligation: TableObligation): void {\\n    const { ...obligationData } = obligation;\\n    delete (obligationData as Partial<TableObligation>).editing;\\n\\n    const operation = obligationData.id\\n      ? this.obligationService.update(obligationData.id, obligationData)\\n      : this.obligationService.create(obligationData);\\n\\n    operation.subscribe({\\n      next: (savedObligation: Obligation) => {\\n        let updatedList: TableObligation[];\\n        if (obligationData.id) {\\n          updatedList = this.obligationsList.map((ob) =>\\n            ob.id === savedObligation.id\\n              ? { ...savedObligation, editing: false }\\n              : ob,\\n          );\\n          delete this.originalStates[obligationData.id];\\n        } else {\\n          updatedList = [\\n            ...this.obligationsList.filter((ob) => ob.id),\\n            { ...savedObligation, editing: false },\\n          ];\\n        }\\n        this.obligationsList = updatedList.sort(\\n          (a, b) => (a.number ?? Infinity) - (b.number ?? Infinity),\\n        );\\n        obligation.editing = false;\\n\\n        this.alert.success(\\n          `Obligaci\\xF3n ${\\n            obligationData.id ? 'actualizada' : 'agregada'\\n          } correctamente`,\\n        );\\n        this.obligationChanged.emit();\\n      },\\n      error: ({ status }: HttpErrorResponse) => {\\n        switch (status) {\\n          case 400:\\n            if (obligation.name.length > 1000) {\\n              this.alert.warning(\\n                'Validaci\\xF3n Fallida',\\n                'La obligaci\\xF3n no puede tener m\\xE1s de 1000 caracteres',\\n              );\\n            } else {\\n              this.alert.warning(\\n                'Validaci\\xF3n Fallida',\\n                'La obligaci\\xF3n no puede estar vac\\xEDa o contener solo espacios en blanco',\\n              );\\n            }\\n            break;\\n          case 409:\\n            this.alert.warning(\\n              'Obligaci\\xF3n Duplicada',\\n              'Ya existe una obligaci\\xF3n con esta descripci\\xF3n en el contrato',\\n            );\\n            break;\\n          default:\\n            this.alert.error(\\n              'Error en la Operaci\\xF3n',\\n              `Error al ${obligation.id ? 'actualizar' : 'agregar'} la obligaci\\xF3n`,\\n            );\\n        }\\n        obligation.editing = true;\\n      },\\n    });\\n  }\\n\\n  cancelEdit(obligation: TableObligation): void {\\n    if (!obligation.id) {\\n      this.obligationsList = this.obligationsList.filter(\\n        (ob) => ob !== obligation,\\n      );\\n      this.obligationChanged.emit();\\n    } else if (obligation.id && this.originalStates[obligation.id]) {\\n      const originalState = this.originalStates[obligation.id];\\n      obligation.name = originalState.name;\\n      obligation.number = originalState.number;\\n      delete this.originalStates[obligation.id];\\n      obligation.editing = false;\\n      this.obligationChanged.emit();\\n    } else {\\n      obligation.editing = false;\\n    }\\n  }\\n\\n  async deleteObligation(obligation: TableObligation): Promise<void> {\\n    if (!obligation.id) return;\\n\\n    const confirmed = await this.alert.confirm(\\n      '\\xBFEst\\xE1 seguro que desea eliminar esta obligaci\\xF3n?',\\n    );\\n\\n    if (confirmed) {\\n      this.obligationService.delete(obligation.id).subscribe({\\n        next: () => {\\n          this.obligationsList = this.obligationsList\\n            .filter((ob) => ob.id !== obligation.id)\\n            .sort((a, b) => (a.number ?? Infinity) - (b.number ?? Infinity));\\n          this.alert.success('Obligaci\\xF3n eliminada correctamente');\\n          this.obligationChanged.emit();\\n        },\\n        error: (error: HttpErrorResponse) => {\\n          if (error.status === 409) {\\n            this.alert.warning(\\n              'No se puede eliminar',\\n              'Esta obligaci\\xF3n est\\xE1 siendo utilizada en informes mensuales y no puede ser eliminada.',\\n            );\\n          } else {\\n            this.alert.error(error.error?.detail ?? 'No se pudo eliminar la obligaci\\xF3n.');\\n          }\\n        },\\n      });\\n    }\\n  }\\n}\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"ea9263e1d80e9bc0699ee24a5e670a0f5c1b013e\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_10pleoxbv6 = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_10pleoxbv6();\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./obligations-list.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./obligations-list.component.scss?ngResource\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { FormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { ObligationService } from '@contract-management/services/obligation.service';\nimport { AlertService } from '@shared/services/alert.service';\ncov_10pleoxbv6().s[0]++;\nlet ObligationsListComponent = class ObligationsListComponent {\n  constructor(obligationService, alert) {\n    cov_10pleoxbv6().f[0]++;\n    cov_10pleoxbv6().s[1]++;\n    this.obligationService = obligationService;\n    cov_10pleoxbv6().s[2]++;\n    this.alert = alert;\n    cov_10pleoxbv6().s[3]++;\n    this.isContractFinished = false;\n    cov_10pleoxbv6().s[4]++;\n    this.obligationChanged = new EventEmitter();\n    cov_10pleoxbv6().s[5]++;\n    this.obligationsList = [];\n    cov_10pleoxbv6().s[6]++;\n    this.obligationsColumns = ['number', 'name', 'actions'];\n    cov_10pleoxbv6().s[7]++;\n    this.originalStates = {};\n  }\n  ngOnInit() {\n    cov_10pleoxbv6().f[1]++;\n    cov_10pleoxbv6().s[8]++;\n    if (this.contract?.obligations) {\n      cov_10pleoxbv6().b[0][0]++;\n      cov_10pleoxbv6().s[9]++;\n      this.obligationsList = this.contract.obligations.map(obligation => {\n        cov_10pleoxbv6().f[2]++;\n        cov_10pleoxbv6().s[10]++;\n        return {\n          ...obligation,\n          editing: false\n        };\n      }).sort((a, b) => {\n        cov_10pleoxbv6().f[3]++;\n        cov_10pleoxbv6().s[11]++;\n        return ((cov_10pleoxbv6().b[1][0]++, a.number) ?? (cov_10pleoxbv6().b[1][1]++, Infinity)) - ((cov_10pleoxbv6().b[2][0]++, b.number) ?? (cov_10pleoxbv6().b[2][1]++, Infinity));\n      });\n    } else {\n      cov_10pleoxbv6().b[0][1]++;\n    }\n  }\n  addNewObligation() {\n    cov_10pleoxbv6().f[4]++;\n    const newObligation = (cov_10pleoxbv6().s[12]++, {\n      name: '',\n      contractId: this.contract.id,\n      editing: true\n    });\n    cov_10pleoxbv6().s[13]++;\n    this.obligationsList = [...this.obligationsList, newObligation];\n  }\n  editObligation(obligation) {\n    cov_10pleoxbv6().f[5]++;\n    cov_10pleoxbv6().s[14]++;\n    if (obligation.id) {\n      cov_10pleoxbv6().b[3][0]++;\n      cov_10pleoxbv6().s[15]++;\n      this.originalStates[obligation.id] = {\n        name: obligation.name,\n        number: obligation.number\n      };\n    } else {\n      cov_10pleoxbv6().b[3][1]++;\n    }\n    cov_10pleoxbv6().s[16]++;\n    obligation.editing = true;\n  }\n  saveObligation(obligation) {\n    cov_10pleoxbv6().f[6]++;\n    const {\n      ...obligationData\n    } = (cov_10pleoxbv6().s[17]++, obligation);\n    cov_10pleoxbv6().s[18]++;\n    delete obligationData.editing;\n    const operation = (cov_10pleoxbv6().s[19]++, obligationData.id ? (cov_10pleoxbv6().b[4][0]++, this.obligationService.update(obligationData.id, obligationData)) : (cov_10pleoxbv6().b[4][1]++, this.obligationService.create(obligationData)));\n    cov_10pleoxbv6().s[20]++;\n    operation.subscribe({\n      next: savedObligation => {\n        cov_10pleoxbv6().f[7]++;\n        let updatedList;\n        cov_10pleoxbv6().s[21]++;\n        if (obligationData.id) {\n          cov_10pleoxbv6().b[5][0]++;\n          cov_10pleoxbv6().s[22]++;\n          updatedList = this.obligationsList.map(ob => {\n            cov_10pleoxbv6().f[8]++;\n            cov_10pleoxbv6().s[23]++;\n            return ob.id === savedObligation.id ? (cov_10pleoxbv6().b[6][0]++, {\n              ...savedObligation,\n              editing: false\n            }) : (cov_10pleoxbv6().b[6][1]++, ob);\n          });\n          cov_10pleoxbv6().s[24]++;\n          delete this.originalStates[obligationData.id];\n        } else {\n          cov_10pleoxbv6().b[5][1]++;\n          cov_10pleoxbv6().s[25]++;\n          updatedList = [...this.obligationsList.filter(ob => {\n            cov_10pleoxbv6().f[9]++;\n            cov_10pleoxbv6().s[26]++;\n            return ob.id;\n          }), {\n            ...savedObligation,\n            editing: false\n          }];\n        }\n        cov_10pleoxbv6().s[27]++;\n        this.obligationsList = updatedList.sort((a, b) => {\n          cov_10pleoxbv6().f[10]++;\n          cov_10pleoxbv6().s[28]++;\n          return ((cov_10pleoxbv6().b[7][0]++, a.number) ?? (cov_10pleoxbv6().b[7][1]++, Infinity)) - ((cov_10pleoxbv6().b[8][0]++, b.number) ?? (cov_10pleoxbv6().b[8][1]++, Infinity));\n        });\n        cov_10pleoxbv6().s[29]++;\n        obligation.editing = false;\n        cov_10pleoxbv6().s[30]++;\n        this.alert.success(`Obligación ${obligationData.id ? (cov_10pleoxbv6().b[9][0]++, 'actualizada') : (cov_10pleoxbv6().b[9][1]++, 'agregada')} correctamente`);\n        cov_10pleoxbv6().s[31]++;\n        this.obligationChanged.emit();\n      },\n      error: ({\n        status\n      }) => {\n        cov_10pleoxbv6().f[11]++;\n        cov_10pleoxbv6().s[32]++;\n        switch (status) {\n          case 400:\n            cov_10pleoxbv6().b[10][0]++;\n            cov_10pleoxbv6().s[33]++;\n            if (obligation.name.length > 1000) {\n              cov_10pleoxbv6().b[11][0]++;\n              cov_10pleoxbv6().s[34]++;\n              this.alert.warning('Validación Fallida', 'La obligación no puede tener más de 1000 caracteres');\n            } else {\n              cov_10pleoxbv6().b[11][1]++;\n              cov_10pleoxbv6().s[35]++;\n              this.alert.warning('Validación Fallida', 'La obligación no puede estar vacía o contener solo espacios en blanco');\n            }\n            cov_10pleoxbv6().s[36]++;\n            break;\n          case 409:\n            cov_10pleoxbv6().b[10][1]++;\n            cov_10pleoxbv6().s[37]++;\n            this.alert.warning('Obligación Duplicada', 'Ya existe una obligación con esta descripción en el contrato');\n            cov_10pleoxbv6().s[38]++;\n            break;\n          default:\n            cov_10pleoxbv6().b[10][2]++;\n            cov_10pleoxbv6().s[39]++;\n            this.alert.error('Error en la Operación', `Error al ${obligation.id ? (cov_10pleoxbv6().b[12][0]++, 'actualizar') : (cov_10pleoxbv6().b[12][1]++, 'agregar')} la obligación`);\n        }\n        cov_10pleoxbv6().s[40]++;\n        obligation.editing = true;\n      }\n    });\n  }\n  cancelEdit(obligation) {\n    cov_10pleoxbv6().f[12]++;\n    cov_10pleoxbv6().s[41]++;\n    if (!obligation.id) {\n      cov_10pleoxbv6().b[13][0]++;\n      cov_10pleoxbv6().s[42]++;\n      this.obligationsList = this.obligationsList.filter(ob => {\n        cov_10pleoxbv6().f[13]++;\n        cov_10pleoxbv6().s[43]++;\n        return ob !== obligation;\n      });\n      cov_10pleoxbv6().s[44]++;\n      this.obligationChanged.emit();\n    } else {\n      cov_10pleoxbv6().b[13][1]++;\n      cov_10pleoxbv6().s[45]++;\n      if ((cov_10pleoxbv6().b[15][0]++, obligation.id) && (cov_10pleoxbv6().b[15][1]++, this.originalStates[obligation.id])) {\n        cov_10pleoxbv6().b[14][0]++;\n        const originalState = (cov_10pleoxbv6().s[46]++, this.originalStates[obligation.id]);\n        cov_10pleoxbv6().s[47]++;\n        obligation.name = originalState.name;\n        cov_10pleoxbv6().s[48]++;\n        obligation.number = originalState.number;\n        cov_10pleoxbv6().s[49]++;\n        delete this.originalStates[obligation.id];\n        cov_10pleoxbv6().s[50]++;\n        obligation.editing = false;\n        cov_10pleoxbv6().s[51]++;\n        this.obligationChanged.emit();\n      } else {\n        cov_10pleoxbv6().b[14][1]++;\n        cov_10pleoxbv6().s[52]++;\n        obligation.editing = false;\n      }\n    }\n  }\n  deleteObligation(obligation) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      cov_10pleoxbv6().f[14]++;\n      cov_10pleoxbv6().s[53]++;\n      if (!obligation.id) {\n        cov_10pleoxbv6().b[16][0]++;\n        cov_10pleoxbv6().s[54]++;\n        return;\n      } else {\n        cov_10pleoxbv6().b[16][1]++;\n      }\n      const confirmed = (cov_10pleoxbv6().s[55]++, yield _this.alert.confirm('¿Está seguro que desea eliminar esta obligación?'));\n      cov_10pleoxbv6().s[56]++;\n      if (confirmed) {\n        cov_10pleoxbv6().b[17][0]++;\n        cov_10pleoxbv6().s[57]++;\n        _this.obligationService.delete(obligation.id).subscribe({\n          next: () => {\n            cov_10pleoxbv6().f[15]++;\n            cov_10pleoxbv6().s[58]++;\n            _this.obligationsList = _this.obligationsList.filter(ob => {\n              cov_10pleoxbv6().f[16]++;\n              cov_10pleoxbv6().s[59]++;\n              return ob.id !== obligation.id;\n            }).sort((a, b) => {\n              cov_10pleoxbv6().f[17]++;\n              cov_10pleoxbv6().s[60]++;\n              return ((cov_10pleoxbv6().b[18][0]++, a.number) ?? (cov_10pleoxbv6().b[18][1]++, Infinity)) - ((cov_10pleoxbv6().b[19][0]++, b.number) ?? (cov_10pleoxbv6().b[19][1]++, Infinity));\n            });\n            cov_10pleoxbv6().s[61]++;\n            _this.alert.success('Obligación eliminada correctamente');\n            cov_10pleoxbv6().s[62]++;\n            _this.obligationChanged.emit();\n          },\n          error: error => {\n            cov_10pleoxbv6().f[18]++;\n            cov_10pleoxbv6().s[63]++;\n            if (error.status === 409) {\n              cov_10pleoxbv6().b[20][0]++;\n              cov_10pleoxbv6().s[64]++;\n              _this.alert.warning('No se puede eliminar', 'Esta obligación está siendo utilizada en informes mensuales y no puede ser eliminada.');\n            } else {\n              cov_10pleoxbv6().b[20][1]++;\n              cov_10pleoxbv6().s[65]++;\n              _this.alert.error((cov_10pleoxbv6().b[21][0]++, error.error?.detail) ?? (cov_10pleoxbv6().b[21][1]++, 'No se pudo eliminar la obligación.'));\n            }\n          }\n        });\n      } else {\n        cov_10pleoxbv6().b[17][1]++;\n      }\n    })();\n  }\n  static {\n    cov_10pleoxbv6().s[66]++;\n    this.ctorParameters = () => {\n      cov_10pleoxbv6().f[19]++;\n      cov_10pleoxbv6().s[67]++;\n      return [{\n        type: ObligationService\n      }, {\n        type: AlertService\n      }];\n    };\n  }\n  static {\n    cov_10pleoxbv6().s[68]++;\n    this.propDecorators = {\n      contract: [{\n        type: Input\n      }],\n      isContractFinished: [{\n        type: Input\n      }],\n      obligationChanged: [{\n        type: Output\n      }]\n    };\n  }\n};\ncov_10pleoxbv6().s[69]++;\nObligationsListComponent = __decorate([Component({\n  selector: 'app-obligations-list',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: true,\n  imports: [MatCardModule, MatButtonModule, MatIconModule, MatTableModule, MatFormFieldModule, MatInputModule, FormsModule, MatTooltipModule],\n  styles: [__NG_CLI_RESOURCE__1]\n})], ObligationsListComponent);\nexport { ObligationsListComponent };", "map": {"version": 3, "names": ["cov_10pleoxbv6", "actualCoverage", "Component", "EventEmitter", "Input", "Output", "FormsModule", "MatButtonModule", "MatCardModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatTableModule", "MatTooltipModule", "ObligationService", "AlertService", "s", "ObligationsListComponent", "constructor", "obligationService", "alert", "f", "isContractFinished", "obligationChanged", "obligationsList", "obligationsColumns", "originalStates", "ngOnInit", "contract", "obligations", "b", "map", "obligation", "editing", "sort", "a", "number", "Infinity", "addNewObligation", "newObligation", "name", "contractId", "id", "editObligation", "saveObligation", "obligationData", "operation", "update", "create", "subscribe", "next", "savedObligation", "updatedList", "ob", "filter", "success", "emit", "error", "status", "length", "warning", "cancelEdit", "originalState", "deleteObligation", "_this", "_asyncToGenerator", "confirmed", "confirm", "delete", "detail", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\components\\obligations-list\\obligations-list.component.ts"], "sourcesContent": ["import { HttpErrorResponse } from '@angular/common/http';\nimport { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\nimport { FormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { Contract } from '@contract-management/models/contract.model';\nimport { Obligation } from '@contract-management/models/obligation.model';\nimport { ObligationService } from '@contract-management/services/obligation.service';\nimport { AlertService } from '@shared/services/alert.service';\n\ntype TableObligation = Obligation & { editing: boolean };\n\n@Component({\n  selector: 'app-obligations-list',\n  templateUrl: './obligations-list.component.html',\n  styleUrl: './obligations-list.component.scss',\n  standalone: true,\n  imports: [\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatTableModule,\n    MatFormFieldModule,\n    MatInputModule,\n    FormsModule,\n    MatTooltipModule,\n  ],\n})\nexport class ObligationsListComponent implements OnInit {\n  @Input() contract!: Contract;\n  @Input() isContractFinished = false;\n  @Output() obligationChanged = new EventEmitter<void>();\n\n  obligationsList: TableObligation[] = [];\n  obligationsColumns: string[] = ['number', 'name', 'actions'];\n  private originalStates: Record<number, { name: string; number?: number }> =\n    {};\n\n  constructor(\n    private readonly obligationService: ObligationService,\n    private readonly alert: AlertService,\n  ) {}\n\n  ngOnInit(): void {\n    if (this.contract?.obligations) {\n      this.obligationsList = this.contract.obligations\n        .map((obligation) => ({\n          ...obligation,\n          editing: false,\n        }))\n        .sort((a, b) => (a.number ?? Infinity) - (b.number ?? Infinity));\n    }\n  }\n\n  addNewObligation(): void {\n    const newObligation: TableObligation = {\n      name: '',\n      contractId: this.contract.id,\n      editing: true,\n    };\n    this.obligationsList = [...this.obligationsList, newObligation];\n  }\n\n  editObligation(obligation: TableObligation): void {\n    if (obligation.id) {\n      this.originalStates[obligation.id] = {\n        name: obligation.name,\n        number: obligation.number,\n      };\n    }\n    obligation.editing = true;\n  }\n\n  saveObligation(obligation: TableObligation): void {\n    const { ...obligationData } = obligation;\n    delete (obligationData as Partial<TableObligation>).editing;\n\n    const operation = obligationData.id\n      ? this.obligationService.update(obligationData.id, obligationData)\n      : this.obligationService.create(obligationData);\n\n    operation.subscribe({\n      next: (savedObligation: Obligation) => {\n        let updatedList: TableObligation[];\n        if (obligationData.id) {\n          updatedList = this.obligationsList.map((ob) =>\n            ob.id === savedObligation.id\n              ? { ...savedObligation, editing: false }\n              : ob,\n          );\n          delete this.originalStates[obligationData.id];\n        } else {\n          updatedList = [\n            ...this.obligationsList.filter((ob) => ob.id),\n            { ...savedObligation, editing: false },\n          ];\n        }\n        this.obligationsList = updatedList.sort(\n          (a, b) => (a.number ?? Infinity) - (b.number ?? Infinity),\n        );\n        obligation.editing = false;\n\n        this.alert.success(\n          `Obligación ${\n            obligationData.id ? 'actualizada' : 'agregada'\n          } correctamente`,\n        );\n        this.obligationChanged.emit();\n      },\n      error: ({ status }: HttpErrorResponse) => {\n        switch (status) {\n          case 400:\n            if (obligation.name.length > 1000) {\n              this.alert.warning(\n                'Validación Fallida',\n                'La obligación no puede tener más de 1000 caracteres',\n              );\n            } else {\n              this.alert.warning(\n                'Validación Fallida',\n                'La obligación no puede estar vacía o contener solo espacios en blanco',\n              );\n            }\n            break;\n          case 409:\n            this.alert.warning(\n              'Obligación Duplicada',\n              'Ya existe una obligación con esta descripción en el contrato',\n            );\n            break;\n          default:\n            this.alert.error(\n              'Error en la Operación',\n              `Error al ${obligation.id ? 'actualizar' : 'agregar'} la obligación`,\n            );\n        }\n        obligation.editing = true;\n      },\n    });\n  }\n\n  cancelEdit(obligation: TableObligation): void {\n    if (!obligation.id) {\n      this.obligationsList = this.obligationsList.filter(\n        (ob) => ob !== obligation,\n      );\n      this.obligationChanged.emit();\n    } else if (obligation.id && this.originalStates[obligation.id]) {\n      const originalState = this.originalStates[obligation.id];\n      obligation.name = originalState.name;\n      obligation.number = originalState.number;\n      delete this.originalStates[obligation.id];\n      obligation.editing = false;\n      this.obligationChanged.emit();\n    } else {\n      obligation.editing = false;\n    }\n  }\n\n  async deleteObligation(obligation: TableObligation): Promise<void> {\n    if (!obligation.id) return;\n\n    const confirmed = await this.alert.confirm(\n      '¿Está seguro que desea eliminar esta obligación?',\n    );\n\n    if (confirmed) {\n      this.obligationService.delete(obligation.id).subscribe({\n        next: () => {\n          this.obligationsList = this.obligationsList\n            .filter((ob) => ob.id !== obligation.id)\n            .sort((a, b) => (a.number ?? Infinity) - (b.number ?? Infinity));\n          this.alert.success('Obligación eliminada correctamente');\n          this.obligationChanged.emit();\n        },\n        error: (error: HttpErrorResponse) => {\n          if (error.status === 409) {\n            this.alert.warning(\n              'No se puede eliminar',\n              'Esta obligación está siendo utilizada en informes mensuales y no puede ser eliminada.',\n            );\n          } else {\n            this.alert.error(error.error?.detail ?? 'No se pudo eliminar la obligación.');\n          }\n        },\n      });\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2CE;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;AA1CF,SAASE,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAUC,MAAM,QAAQ,eAAe;AAC9E,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAG5D,SAASC,iBAAiB,QAAQ,kDAAkD;AACpF,SAASC,YAAY,QAAQ,gCAAgC;AAACf,cAAA,GAAAgB,CAAA;AAoBvD,IAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB;EAUnCC,YACmBC,iBAAoC,EACpCC,KAAmB;IAAApB,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAgB,CAAA;IADnB,KAAAG,iBAAiB,GAAjBA,iBAAiB;IAAmBnB,cAAA,GAAAgB,CAAA;IACpC,KAAAI,KAAK,GAALA,KAAK;IAAcpB,cAAA,GAAAgB,CAAA;IAV7B,KAAAM,kBAAkB,GAAG,KAAK;IAAAtB,cAAA,GAAAgB,CAAA;IACzB,KAAAO,iBAAiB,GAAG,IAAIpB,YAAY,EAAQ;IAAAH,cAAA,GAAAgB,CAAA;IAEtD,KAAAQ,eAAe,GAAsB,EAAE;IAACxB,cAAA,GAAAgB,CAAA;IACxC,KAAAS,kBAAkB,GAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;IAACzB,cAAA,GAAAgB,CAAA;IACrD,KAAAU,cAAc,GACpB,EAAE;EAKD;EAEHC,QAAQA,CAAA;IAAA3B,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAgB,CAAA;IACN,IAAI,IAAI,CAACY,QAAQ,EAAEC,WAAW,EAAE;MAAA7B,cAAA,GAAA8B,CAAA;MAAA9B,cAAA,GAAAgB,CAAA;MAC9B,IAAI,CAACQ,eAAe,GAAG,IAAI,CAACI,QAAQ,CAACC,WAAW,CAC7CE,GAAG,CAAEC,UAAU,IAAM;QAAAhC,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAgB,CAAA;QAAA;UACpB,GAAGgB,UAAU;UACbC,OAAO,EAAE;SACV;OAAC,CAAC,CACFC,IAAI,CAAC,CAACC,CAAC,EAAEL,CAAC,KAAK;QAAA9B,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAgB,CAAA;QAAA,QAAC,CAAAhB,cAAA,GAAA8B,CAAA,UAAAK,CAAC,CAACC,MAAM,MAAApC,cAAA,GAAA8B,CAAA,UAAIO,QAAQ,MAAK,CAAArC,cAAA,GAAA8B,CAAA,UAAAA,CAAC,CAACM,MAAM,MAAApC,cAAA,GAAA8B,CAAA,UAAIO,QAAQ,EAAC;MAAD,CAAC,CAAC;IACpE,CAAC;MAAArC,cAAA,GAAA8B,CAAA;IAAA;EACH;EAEAQ,gBAAgBA,CAAA;IAAAtC,cAAA,GAAAqB,CAAA;IACd,MAAMkB,aAAa,IAAAvC,cAAA,GAAAgB,CAAA,QAAoB;MACrCwB,IAAI,EAAE,EAAE;MACRC,UAAU,EAAE,IAAI,CAACb,QAAQ,CAACc,EAAE;MAC5BT,OAAO,EAAE;KACV;IAACjC,cAAA,GAAAgB,CAAA;IACF,IAAI,CAACQ,eAAe,GAAG,CAAC,GAAG,IAAI,CAACA,eAAe,EAAEe,aAAa,CAAC;EACjE;EAEAI,cAAcA,CAACX,UAA2B;IAAAhC,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAgB,CAAA;IACxC,IAAIgB,UAAU,CAACU,EAAE,EAAE;MAAA1C,cAAA,GAAA8B,CAAA;MAAA9B,cAAA,GAAAgB,CAAA;MACjB,IAAI,CAACU,cAAc,CAACM,UAAU,CAACU,EAAE,CAAC,GAAG;QACnCF,IAAI,EAAER,UAAU,CAACQ,IAAI;QACrBJ,MAAM,EAAEJ,UAAU,CAACI;OACpB;IACH,CAAC;MAAApC,cAAA,GAAA8B,CAAA;IAAA;IAAA9B,cAAA,GAAAgB,CAAA;IACDgB,UAAU,CAACC,OAAO,GAAG,IAAI;EAC3B;EAEAW,cAAcA,CAACZ,UAA2B;IAAAhC,cAAA,GAAAqB,CAAA;IACxC,MAAM;MAAE,GAAGwB;IAAc,CAAE,IAAA7C,cAAA,GAAAgB,CAAA,QAAGgB,UAAU;IAAChC,cAAA,GAAAgB,CAAA;IACzC,OAAQ6B,cAA2C,CAACZ,OAAO;IAE3D,MAAMa,SAAS,IAAA9C,cAAA,GAAAgB,CAAA,QAAG6B,cAAc,CAACH,EAAE,IAAA1C,cAAA,GAAA8B,CAAA,UAC/B,IAAI,CAACX,iBAAiB,CAAC4B,MAAM,CAACF,cAAc,CAACH,EAAE,EAAEG,cAAc,CAAC,KAAA7C,cAAA,GAAA8B,CAAA,UAChE,IAAI,CAACX,iBAAiB,CAAC6B,MAAM,CAACH,cAAc,CAAC;IAAC7C,cAAA,GAAAgB,CAAA;IAElD8B,SAAS,CAACG,SAAS,CAAC;MAClBC,IAAI,EAAGC,eAA2B,IAAI;QAAAnD,cAAA,GAAAqB,CAAA;QACpC,IAAI+B,WAA8B;QAACpD,cAAA,GAAAgB,CAAA;QACnC,IAAI6B,cAAc,CAACH,EAAE,EAAE;UAAA1C,cAAA,GAAA8B,CAAA;UAAA9B,cAAA,GAAAgB,CAAA;UACrBoC,WAAW,GAAG,IAAI,CAAC5B,eAAe,CAACO,GAAG,CAAEsB,EAAE,IACxC;YAAArD,cAAA,GAAAqB,CAAA;YAAArB,cAAA,GAAAgB,CAAA;YAAA,OAAAqC,EAAE,CAACX,EAAE,KAAKS,eAAe,CAACT,EAAE,IAAA1C,cAAA,GAAA8B,CAAA,UACxB;cAAE,GAAGqB,eAAe;cAAElB,OAAO,EAAE;YAAK,CAAE,KAAAjC,cAAA,GAAA8B,CAAA,UACtCuB,EAAE;UAAF,CAAE,CACP;UAACrD,cAAA,GAAAgB,CAAA;UACF,OAAO,IAAI,CAACU,cAAc,CAACmB,cAAc,CAACH,EAAE,CAAC;QAC/C,CAAC,MAAM;UAAA1C,cAAA,GAAA8B,CAAA;UAAA9B,cAAA,GAAAgB,CAAA;UACLoC,WAAW,GAAG,CACZ,GAAG,IAAI,CAAC5B,eAAe,CAAC8B,MAAM,CAAED,EAAE,IAAK;YAAArD,cAAA,GAAAqB,CAAA;YAAArB,cAAA,GAAAgB,CAAA;YAAA,OAAAqC,EAAE,CAACX,EAAE;UAAF,CAAE,CAAC,EAC7C;YAAE,GAAGS,eAAe;YAAElB,OAAO,EAAE;UAAK,CAAE,CACvC;QACH;QAACjC,cAAA,GAAAgB,CAAA;QACD,IAAI,CAACQ,eAAe,GAAG4B,WAAW,CAAClB,IAAI,CACrC,CAACC,CAAC,EAAEL,CAAC,KAAK;UAAA9B,cAAA,GAAAqB,CAAA;UAAArB,cAAA,GAAAgB,CAAA;UAAA,QAAC,CAAAhB,cAAA,GAAA8B,CAAA,UAAAK,CAAC,CAACC,MAAM,MAAApC,cAAA,GAAA8B,CAAA,UAAIO,QAAQ,MAAK,CAAArC,cAAA,GAAA8B,CAAA,UAAAA,CAAC,CAACM,MAAM,MAAApC,cAAA,GAAA8B,CAAA,UAAIO,QAAQ,EAAC;QAAD,CAAC,CAC1D;QAACrC,cAAA,GAAAgB,CAAA;QACFgB,UAAU,CAACC,OAAO,GAAG,KAAK;QAACjC,cAAA,GAAAgB,CAAA;QAE3B,IAAI,CAACI,KAAK,CAACmC,OAAO,CAChB,cACEV,cAAc,CAACH,EAAE,IAAA1C,cAAA,GAAA8B,CAAA,UAAG,aAAa,KAAA9B,cAAA,GAAA8B,CAAA,UAAG,UACtC,iBAAgB,CACjB;QAAC9B,cAAA,GAAAgB,CAAA;QACF,IAAI,CAACO,iBAAiB,CAACiC,IAAI,EAAE;MAC/B,CAAC;MACDC,KAAK,EAAEA,CAAC;QAAEC;MAAM,CAAqB,KAAI;QAAA1D,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAgB,CAAA;QACvC,QAAQ0C,MAAM;UACZ,KAAK,GAAG;YAAA1D,cAAA,GAAA8B,CAAA;YAAA9B,cAAA,GAAAgB,CAAA;YACN,IAAIgB,UAAU,CAACQ,IAAI,CAACmB,MAAM,GAAG,IAAI,EAAE;cAAA3D,cAAA,GAAA8B,CAAA;cAAA9B,cAAA,GAAAgB,CAAA;cACjC,IAAI,CAACI,KAAK,CAACwC,OAAO,CAChB,oBAAoB,EACpB,qDAAqD,CACtD;YACH,CAAC,MAAM;cAAA5D,cAAA,GAAA8B,CAAA;cAAA9B,cAAA,GAAAgB,CAAA;cACL,IAAI,CAACI,KAAK,CAACwC,OAAO,CAChB,oBAAoB,EACpB,uEAAuE,CACxE;YACH;YAAC5D,cAAA,GAAAgB,CAAA;YACD;UACF,KAAK,GAAG;YAAAhB,cAAA,GAAA8B,CAAA;YAAA9B,cAAA,GAAAgB,CAAA;YACN,IAAI,CAACI,KAAK,CAACwC,OAAO,CAChB,sBAAsB,EACtB,8DAA8D,CAC/D;YAAC5D,cAAA,GAAAgB,CAAA;YACF;UACF;YAAAhB,cAAA,GAAA8B,CAAA;YAAA9B,cAAA,GAAAgB,CAAA;YACE,IAAI,CAACI,KAAK,CAACqC,KAAK,CACd,uBAAuB,EACvB,YAAYzB,UAAU,CAACU,EAAE,IAAA1C,cAAA,GAAA8B,CAAA,WAAG,YAAY,KAAA9B,cAAA,GAAA8B,CAAA,WAAG,SAAS,iBAAgB,CACrE;QACL;QAAC9B,cAAA,GAAAgB,CAAA;QACDgB,UAAU,CAACC,OAAO,GAAG,IAAI;MAC3B;KACD,CAAC;EACJ;EAEA4B,UAAUA,CAAC7B,UAA2B;IAAAhC,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAgB,CAAA;IACpC,IAAI,CAACgB,UAAU,CAACU,EAAE,EAAE;MAAA1C,cAAA,GAAA8B,CAAA;MAAA9B,cAAA,GAAAgB,CAAA;MAClB,IAAI,CAACQ,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC8B,MAAM,CAC/CD,EAAE,IAAK;QAAArD,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAgB,CAAA;QAAA,OAAAqC,EAAE,KAAKrB,UAAU;MAAV,CAAU,CAC1B;MAAChC,cAAA,GAAAgB,CAAA;MACF,IAAI,CAACO,iBAAiB,CAACiC,IAAI,EAAE;IAC/B,CAAC,MAAM;MAAAxD,cAAA,GAAA8B,CAAA;MAAA9B,cAAA,GAAAgB,CAAA;MAAA,IAAI,CAAAhB,cAAA,GAAA8B,CAAA,WAAAE,UAAU,CAACU,EAAE,MAAA1C,cAAA,GAAA8B,CAAA,WAAI,IAAI,CAACJ,cAAc,CAACM,UAAU,CAACU,EAAE,CAAC,GAAE;QAAA1C,cAAA,GAAA8B,CAAA;QAC9D,MAAMgC,aAAa,IAAA9D,cAAA,GAAAgB,CAAA,QAAG,IAAI,CAACU,cAAc,CAACM,UAAU,CAACU,EAAE,CAAC;QAAC1C,cAAA,GAAAgB,CAAA;QACzDgB,UAAU,CAACQ,IAAI,GAAGsB,aAAa,CAACtB,IAAI;QAACxC,cAAA,GAAAgB,CAAA;QACrCgB,UAAU,CAACI,MAAM,GAAG0B,aAAa,CAAC1B,MAAM;QAACpC,cAAA,GAAAgB,CAAA;QACzC,OAAO,IAAI,CAACU,cAAc,CAACM,UAAU,CAACU,EAAE,CAAC;QAAC1C,cAAA,GAAAgB,CAAA;QAC1CgB,UAAU,CAACC,OAAO,GAAG,KAAK;QAACjC,cAAA,GAAAgB,CAAA;QAC3B,IAAI,CAACO,iBAAiB,CAACiC,IAAI,EAAE;MAC/B,CAAC,MAAM;QAAAxD,cAAA,GAAA8B,CAAA;QAAA9B,cAAA,GAAAgB,CAAA;QACLgB,UAAU,CAACC,OAAO,GAAG,KAAK;MAC5B;IAAA;EACF;EAEM8B,gBAAgBA,CAAC/B,UAA2B;IAAA,IAAAgC,KAAA;IAAA,OAAAC,iBAAA;MAAAjE,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAgB,CAAA;MAChD,IAAI,CAACgB,UAAU,CAACU,EAAE,EAAE;QAAA1C,cAAA,GAAA8B,CAAA;QAAA9B,cAAA,GAAAgB,CAAA;QAAA;MAAA,CAAO;QAAAhB,cAAA,GAAA8B,CAAA;MAAA;MAE3B,MAAMoC,SAAS,IAAAlE,cAAA,GAAAgB,CAAA,cAASgD,KAAI,CAAC5C,KAAK,CAAC+C,OAAO,CACxC,kDAAkD,CACnD;MAACnE,cAAA,GAAAgB,CAAA;MAEF,IAAIkD,SAAS,EAAE;QAAAlE,cAAA,GAAA8B,CAAA;QAAA9B,cAAA,GAAAgB,CAAA;QACbgD,KAAI,CAAC7C,iBAAiB,CAACiD,MAAM,CAACpC,UAAU,CAACU,EAAE,CAAC,CAACO,SAAS,CAAC;UACrDC,IAAI,EAAEA,CAAA,KAAK;YAAAlD,cAAA,GAAAqB,CAAA;YAAArB,cAAA,GAAAgB,CAAA;YACTgD,KAAI,CAACxC,eAAe,GAAGwC,KAAI,CAACxC,eAAe,CACxC8B,MAAM,CAAED,EAAE,IAAK;cAAArD,cAAA,GAAAqB,CAAA;cAAArB,cAAA,GAAAgB,CAAA;cAAA,OAAAqC,EAAE,CAACX,EAAE,KAAKV,UAAU,CAACU,EAAE;YAAF,CAAE,CAAC,CACvCR,IAAI,CAAC,CAACC,CAAC,EAAEL,CAAC,KAAK;cAAA9B,cAAA,GAAAqB,CAAA;cAAArB,cAAA,GAAAgB,CAAA;cAAA,QAAC,CAAAhB,cAAA,GAAA8B,CAAA,WAAAK,CAAC,CAACC,MAAM,MAAApC,cAAA,GAAA8B,CAAA,WAAIO,QAAQ,MAAK,CAAArC,cAAA,GAAA8B,CAAA,WAAAA,CAAC,CAACM,MAAM,MAAApC,cAAA,GAAA8B,CAAA,WAAIO,QAAQ,EAAC;YAAD,CAAC,CAAC;YAACrC,cAAA,GAAAgB,CAAA;YACnEgD,KAAI,CAAC5C,KAAK,CAACmC,OAAO,CAAC,oCAAoC,CAAC;YAACvD,cAAA,GAAAgB,CAAA;YACzDgD,KAAI,CAACzC,iBAAiB,CAACiC,IAAI,EAAE;UAC/B,CAAC;UACDC,KAAK,EAAGA,KAAwB,IAAI;YAAAzD,cAAA,GAAAqB,CAAA;YAAArB,cAAA,GAAAgB,CAAA;YAClC,IAAIyC,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;cAAA1D,cAAA,GAAA8B,CAAA;cAAA9B,cAAA,GAAAgB,CAAA;cACxBgD,KAAI,CAAC5C,KAAK,CAACwC,OAAO,CAChB,sBAAsB,EACtB,uFAAuF,CACxF;YACH,CAAC,MAAM;cAAA5D,cAAA,GAAA8B,CAAA;cAAA9B,cAAA,GAAAgB,CAAA;cACLgD,KAAI,CAAC5C,KAAK,CAACqC,KAAK,CAAC,CAAAzD,cAAA,GAAA8B,CAAA,WAAA2B,KAAK,CAACA,KAAK,EAAEY,MAAM,MAAArE,cAAA,GAAA8B,CAAA,WAAI,oCAAoC,EAAC;YAC/E;UACF;SACD,CAAC;MACJ,CAAC;QAAA9B,cAAA,GAAA8B,CAAA;MAAA;IAAA;EACH;;;;;;;;;;;;;;;;;cA9JC1B;MAAK;;cACLA;MAAK;;cACLC;MAAM;;;;;AAHIY,wBAAwB,GAAAqD,UAAA,EAhBpCpE,SAAS,CAAC;EACTqE,QAAQ,EAAE,sBAAsB;EAChCC,QAAA,EAAAC,oBAAgD;EAEhDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPnE,aAAa,EACbD,eAAe,EACfG,aAAa,EACbE,cAAc,EACdH,kBAAkB,EAClBE,cAAc,EACdL,WAAW,EACXO,gBAAgB,CACjB;;CACF,CAAC,C,EACWI,wBAAwB,CAgKpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}