import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { ReportReviewStatus } from '@contractor-dashboard/models/report-review-status.model';
import { environment } from '@env';
import { ReportReviewStatusService } from './report-review-status.service';

describe('ReportReviewStatusService', () => {
  let service: ReportReviewStatusService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/report-review-statuses`;

  const mockReportReviewStatus: ReportReviewStatus = {
    id: 1,
    name: 'Test Status',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ReportReviewStatusService],
    });
    service = TestBed.inject(ReportReviewStatusService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all report review statuses', () => {
      const mockStatuses = [mockReportReviewStatus];

      service.getAll().subscribe((statuses) => {
        expect(statuses).toEqual(mockStatuses);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockStatuses);
    });

    it('should handle error when getting all report review statuses', () => {
      service.getAll().subscribe({
        error: (error) => {
          expect(error.status).toBe(500);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Server error'), { status: 500 });
    });
  });

  describe('getById', () => {
    it('should return a report review status by id', () => {
      const id = 1;

      service.getById(id).subscribe((status) => {
        expect(status).toEqual(mockReportReviewStatus);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockReportReviewStatus);
    });

    it('should handle error when getting report review status by id', () => {
      const id = 999;

      service.getById(id).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('create', () => {
    it('should create a new report review status', () => {
      const newStatus: Omit<ReportReviewStatus, 'id'> = {
        name: 'New Status',
      };

      service.create(newStatus).subscribe((status) => {
        expect(status).toEqual(mockReportReviewStatus);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newStatus);
      req.flush(mockReportReviewStatus);
    });

    it('should handle error when creating report review status', () => {
      const newStatus: Omit<ReportReviewStatus, 'id'> = {
        name: 'New Status',
      };

      service.create(newStatus).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('update', () => {
    it('should update a report review status', () => {
      const id = 1;
      const updateData: Partial<ReportReviewStatus> = {
        name: 'Updated Status',
      };

      service.update(id, updateData).subscribe((status) => {
        expect(status).toEqual(mockReportReviewStatus);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockReportReviewStatus);
    });

    it('should handle error when updating report review status', () => {
      const id = 1;
      const updateData: Partial<ReportReviewStatus> = {
        name: 'Updated Status',
      };

      service.update(id, updateData).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('delete', () => {
    it('should delete a report review status', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });

    it('should handle error when deleting report review status', () => {
      const id = 1;

      service.delete(id).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('getByName', () => {
    it('should return a report review status by name', () => {
      const name = 'Test Status';

      service.getByName(name).subscribe((status) => {
        expect(status).toEqual(mockReportReviewStatus);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockReportReviewStatus);
    });

    it('should handle error when getting report review status by name', () => {
      const name = 'INVALID_STATUS';

      service.getByName(name).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('approve', () => {
    it('should approve a report', () => {
      const reportId = 1;

      service.approve(reportId).subscribe((status) => {
        expect(status).toEqual(mockReportReviewStatus);
      });

      const req = httpMock.expectOne(`${apiUrl}/approve/${reportId}`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual({});
      req.flush(mockReportReviewStatus);
    });

    it('should handle error when approving report', () => {
      const reportId = 999;

      service.approve(reportId).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/approve/${reportId}`);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('reject', () => {
    it('should reject a report', () => {
      const reportId = 1;
      const comments = 'Rejection comments';

      service.reject(reportId, comments).subscribe((status) => {
        expect(status).toEqual(mockReportReviewStatus);
      });

      const req = httpMock.expectOne(`${apiUrl}/reject/${reportId}`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual({ comments });
      req.flush(mockReportReviewStatus);
    });

    it('should handle error when rejecting report', () => {
      const reportId = 999;
      const comments = 'Rejection comments';

      service.reject(reportId, comments).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/reject/${reportId}`);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });
});