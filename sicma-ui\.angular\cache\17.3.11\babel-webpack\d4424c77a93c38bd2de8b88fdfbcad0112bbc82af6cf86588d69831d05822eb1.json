{"ast": null, "code": "import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { MatDialog } from '@angular/material/dialog';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { of } from 'rxjs';\nimport { SuspensionDialogComponent } from './suspension-dialog/suspension-dialog.component';\nimport { SuspensionsListComponent } from './suspensions-list.component';\ndescribe('SuspensionsListComponent', () => {\n  let component;\n  let fixture;\n  let dialog;\n  const mockSuspension = {\n    id: 1,\n    startDate: new Date('2024-01-01'),\n    endDate: new Date('2024-01-31'),\n    reason: 'Test Suspension',\n    contractId: 1,\n    suspensionDays: 30\n  };\n  const mockContract = {\n    id: 1,\n    contractNumber: 123,\n    contractYearId: 2024,\n    object: 'Test Contract',\n    rup: false,\n    sigepLink: '',\n    secopLink: '',\n    addition: false,\n    cession: false,\n    settled: false,\n    selectionModalityId: 1,\n    trackingTypeId: 1,\n    contractTypeId: 1,\n    statusId: 1,\n    dependencyId: 1,\n    groupId: 1,\n    monthlyPayment: 1000,\n    municipalityId: 1,\n    departmentId: 1,\n    secopCode: 123,\n    causesSelectionId: 1,\n    managementSupportId: 1,\n    contractClassId: 1,\n    suspensions: [mockSuspension]\n  };\n  beforeEach(() => {\n    const dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);\n    dialogSpy.open.and.returnValue({\n      afterClosed: () => of(undefined)\n    });\n    TestBed.configureTestingModule({\n      imports: [SuspensionsListComponent, HttpClientTestingModule, BrowserAnimationsModule],\n      providers: [{\n        provide: MatDialog,\n        useValue: dialogSpy\n      }]\n    });\n    fixture = TestBed.createComponent(SuspensionsListComponent);\n    component = fixture.componentInstance;\n    dialog = TestBed.inject(MatDialog);\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should initialize with contract suspensions', () => {\n    component.contract = mockContract;\n    component.ngOnInit();\n    expect(component.dataSource.data).toEqual(mockContract.suspensions || []);\n  });\n  it('should open suspension form for new suspension', () => {\n    component.contract = mockContract;\n    component.ngOnInit();\n    const newSuspension = {\n      id: 2,\n      startDate: new Date('2024-02-01'),\n      endDate: new Date('2024-02-28'),\n      reason: 'New Suspension',\n      contractId: 1,\n      suspensionDays: 27\n    };\n    const mockDialogRef = {\n      afterClosed: () => of(newSuspension)\n    };\n    dialog.open.and.returnValue(mockDialogRef);\n    component.openSuspensionForm();\n    expect(dialog.open).toHaveBeenCalledWith(SuspensionDialogComponent, {\n      width: '600px',\n      data: {\n        suspension: undefined,\n        contractId: mockContract.id,\n        lastSuspensionEndDate: new Date('2024-01-31')\n      }\n    });\n    expect(component.dataSource.data.length).toBe(2);\n  });\n  it('should open suspension form for existing suspension', () => {\n    component.contract = mockContract;\n    component.ngOnInit();\n    const updatedSuspension = {\n      ...mockSuspension,\n      reason: 'Updated Suspension'\n    };\n    const mockDialogRef = {\n      afterClosed: () => of(updatedSuspension)\n    };\n    dialog.open.and.returnValue(mockDialogRef);\n    component.openSuspensionForm(mockSuspension);\n    expect(dialog.open).toHaveBeenCalledWith(SuspensionDialogComponent, {\n      width: '600px',\n      data: {\n        suspension: mockSuspension,\n        contractId: mockContract.id,\n        lastSuspensionEndDate: null\n      }\n    });\n    expect(component.dataSource.data[0].reason).toBe('Updated Suspension');\n  });\n  it('should not update data when dialog is closed without result', () => {\n    component.contract = mockContract;\n    component.ngOnInit();\n    const initialData = [...component.dataSource.data];\n    const mockDialogRef = {\n      afterClosed: () => of(undefined)\n    };\n    dialog.open.and.returnValue(mockDialogRef);\n    component.openSuspensionForm();\n    expect(component.dataSource.data).toEqual(initialData);\n  });\n  it('should get last suspension end date', () => {\n    const secondSuspension = {\n      id: 2,\n      startDate: new Date('2024-02-01'),\n      endDate: new Date('2024-02-28'),\n      reason: 'Second Suspension',\n      contractId: 1,\n      suspensionDays: 27\n    };\n    component.contract = {\n      ...mockContract,\n      suspensions: [mockSuspension, secondSuspension]\n    };\n    component.ngOnInit();\n    const lastEndDate = component['getLastSuspensionEndDate']();\n    expect(lastEndDate).toEqual(new Date('2024-02-28'));\n  });\n  it('should return null when no suspensions exist', () => {\n    component.contract = {\n      ...mockContract,\n      suspensions: []\n    };\n    component.ngOnInit();\n    const lastEndDate = component['getLastSuspensionEndDate']();\n    expect(lastEndDate).toBeNull();\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "TestBed", "MatDialog", "BrowserAnimationsModule", "of", "SuspensionDialogComponent", "SuspensionsListComponent", "describe", "component", "fixture", "dialog", "mockSuspension", "id", "startDate", "Date", "endDate", "reason", "contractId", "suspensionDays", "mockContract", "contractNumber", "contractYearId", "object", "rup", "sigepLink", "secopLink", "addition", "cession", "settled", "selectionModalityId", "trackingTypeId", "contractTypeId", "statusId", "dependencyId", "groupId", "monthlyPayment", "municipalityId", "departmentId", "secopCode", "causesSelectionId", "managementSupportId", "contractClassId", "suspensions", "beforeEach", "dialogSpy", "jasmine", "createSpyObj", "open", "and", "returnValue", "afterClosed", "undefined", "configureTestingModule", "imports", "providers", "provide", "useValue", "createComponent", "componentInstance", "inject", "it", "expect", "toBeTruthy", "contract", "ngOnInit", "dataSource", "data", "toEqual", "newSuspension", "mockDialogRef", "openSuspensionForm", "toHaveBeenCalledWith", "width", "suspension", "lastSuspensionEndDate", "length", "toBe", "updatedSuspension", "initialData", "secondSuspension", "lastEndDate", "toBeNull"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\components\\suspensions-list\\suspensions-list.component.spec.ts"], "sourcesContent": ["import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { ComponentFixture, TestBed } from '@angular/core/testing';\nimport { MatDialog, MatDialogRef } from '@angular/material/dialog';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { Contract } from '@contract-management/models/contract.model';\nimport { Suspension } from '@contract-management/models/suspension.model';\nimport { of } from 'rxjs';\nimport { SuspensionDialogComponent } from './suspension-dialog/suspension-dialog.component';\nimport { SuspensionsListComponent } from './suspensions-list.component';\n\ndescribe('SuspensionsListComponent', () => {\n  let component: SuspensionsListComponent;\n  let fixture: ComponentFixture<SuspensionsListComponent>;\n  let dialog: jasmine.SpyObj<MatDialog>;\n\n  const mockSuspension: Suspension = {\n    id: 1,\n    startDate: new Date('2024-01-01'),\n    endDate: new Date('2024-01-31'),\n    reason: 'Test Suspension',\n    contractId: 1,\n    suspensionDays: 30,\n  };\n\n  const mockContract: Contract = {\n    id: 1,\n    contractNumber: 123,\n    contractYearId: 2024,\n    object: 'Test Contract',\n    rup: false,\n    sigepLink: '',\n    secopLink: '',\n    addition: false,\n    cession: false,\n    settled: false,\n    selectionModalityId: 1,\n    trackingTypeId: 1,\n    contractTypeId: 1,\n    statusId: 1,\n    dependencyId: 1,\n    groupId: 1,\n    monthlyPayment: 1000,\n    municipalityId: 1,\n    departmentId: 1,\n    secopCode: 123,\n    causesSelectionId: 1,\n    managementSupportId: 1,\n    contractClassId: 1,\n    suspensions: [mockSuspension],\n  };\n\n  beforeEach(() => {\n    const dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);\n    dialogSpy.open.and.returnValue({\n      afterClosed: () => of(undefined),\n    } as MatDialogRef<unknown>);\n\n    TestBed.configureTestingModule({\n      imports: [\n        SuspensionsListComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n      ],\n      providers: [{ provide: MatDialog, useValue: dialogSpy }],\n    });\n\n    fixture = TestBed.createComponent(SuspensionsListComponent);\n    component = fixture.componentInstance;\n    dialog = TestBed.inject(MatDialog) as jasmine.SpyObj<MatDialog>;\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should initialize with contract suspensions', () => {\n    component.contract = mockContract;\n    component.ngOnInit();\n    expect(component.dataSource.data).toEqual(mockContract.suspensions || []);\n  });\n\n  it('should open suspension form for new suspension', () => {\n    component.contract = mockContract;\n    component.ngOnInit();\n\n    const newSuspension: Suspension = {\n      id: 2,\n      startDate: new Date('2024-02-01'),\n      endDate: new Date('2024-02-28'),\n      reason: 'New Suspension',\n      contractId: 1,\n      suspensionDays: 27,\n    };\n\n    const mockDialogRef = {\n      afterClosed: () => of(newSuspension),\n    } as MatDialogRef<unknown>;\n\n    dialog.open.and.returnValue(mockDialogRef);\n\n    component.openSuspensionForm();\n\n    expect(dialog.open).toHaveBeenCalledWith(SuspensionDialogComponent, {\n      width: '600px',\n      data: {\n        suspension: undefined,\n        contractId: mockContract.id,\n        lastSuspensionEndDate: new Date('2024-01-31'),\n      },\n    });\n\n    expect(component.dataSource.data.length).toBe(2);\n  });\n\n  it('should open suspension form for existing suspension', () => {\n    component.contract = mockContract;\n    component.ngOnInit();\n\n    const updatedSuspension: Suspension = {\n      ...mockSuspension,\n      reason: 'Updated Suspension',\n    };\n\n    const mockDialogRef = {\n      afterClosed: () => of(updatedSuspension),\n    } as MatDialogRef<unknown>;\n\n    dialog.open.and.returnValue(mockDialogRef);\n\n    component.openSuspensionForm(mockSuspension);\n\n    expect(dialog.open).toHaveBeenCalledWith(SuspensionDialogComponent, {\n      width: '600px',\n      data: {\n        suspension: mockSuspension,\n        contractId: mockContract.id,\n        lastSuspensionEndDate: null,\n      },\n    });\n\n    expect(component.dataSource.data[0].reason).toBe('Updated Suspension');\n  });\n\n  it('should not update data when dialog is closed without result', () => {\n    component.contract = mockContract;\n    component.ngOnInit();\n    const initialData = [...component.dataSource.data];\n\n    const mockDialogRef = {\n      afterClosed: () => of(undefined),\n    } as MatDialogRef<unknown>;\n\n    dialog.open.and.returnValue(mockDialogRef);\n\n    component.openSuspensionForm();\n\n    expect(component.dataSource.data).toEqual(initialData);\n  });\n\n  it('should get last suspension end date', () => {\n    const secondSuspension: Suspension = {\n      id: 2,\n      startDate: new Date('2024-02-01'),\n      endDate: new Date('2024-02-28'),\n      reason: 'Second Suspension',\n      contractId: 1,\n      suspensionDays: 27,\n    };\n\n    component.contract = {\n      ...mockContract,\n      suspensions: [mockSuspension, secondSuspension],\n    };\n    component.ngOnInit();\n\n    const lastEndDate = component['getLastSuspensionEndDate']();\n    expect(lastEndDate).toEqual(new Date('2024-02-28'));\n  });\n\n  it('should return null when no suspensions exist', () => {\n    component.contract = {\n      ...mockContract,\n      suspensions: [],\n    };\n    component.ngOnInit();\n\n    const lastEndDate = component['getLastSuspensionEndDate']();\n    expect(lastEndDate).toBeNull();\n  });\n});"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,8BAA8B;AACtE,SAA2BC,OAAO,QAAQ,uBAAuB;AACjE,SAASC,SAAS,QAAsB,0BAA0B;AAClE,SAASC,uBAAuB,QAAQ,sCAAsC;AAG9E,SAASC,EAAE,QAAQ,MAAM;AACzB,SAASC,yBAAyB,QAAQ,iDAAiD;AAC3F,SAASC,wBAAwB,QAAQ,8BAA8B;AAEvEC,QAAQ,CAAC,0BAA0B,EAAE,MAAK;EACxC,IAAIC,SAAmC;EACvC,IAAIC,OAAmD;EACvD,IAAIC,MAAiC;EAErC,MAAMC,cAAc,GAAe;IACjCC,EAAE,EAAE,CAAC;IACLC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;IACjCC,OAAO,EAAE,IAAID,IAAI,CAAC,YAAY,CAAC;IAC/BE,MAAM,EAAE,iBAAiB;IACzBC,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE;GACjB;EAED,MAAMC,YAAY,GAAa;IAC7BP,EAAE,EAAE,CAAC;IACLQ,cAAc,EAAE,GAAG;IACnBC,cAAc,EAAE,IAAI;IACpBC,MAAM,EAAE,eAAe;IACvBC,GAAG,EAAE,KAAK;IACVC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,KAAK;IACdC,mBAAmB,EAAE,CAAC;IACtBC,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE,CAAC;IACjBC,QAAQ,EAAE,CAAC;IACXC,YAAY,EAAE,CAAC;IACfC,OAAO,EAAE,CAAC;IACVC,cAAc,EAAE,IAAI;IACpBC,cAAc,EAAE,CAAC;IACjBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,GAAG;IACdC,iBAAiB,EAAE,CAAC;IACpBC,mBAAmB,EAAE,CAAC;IACtBC,eAAe,EAAE,CAAC;IAClBC,WAAW,EAAE,CAAC/B,cAAc;GAC7B;EAEDgC,UAAU,CAAC,MAAK;IACd,MAAMC,SAAS,GAAGC,OAAO,CAACC,YAAY,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,CAAC;IAC7DF,SAAS,CAACG,IAAI,CAACC,GAAG,CAACC,WAAW,CAAC;MAC7BC,WAAW,EAAEA,CAAA,KAAM9C,EAAE,CAAC+C,SAAS;KACP,CAAC;IAE3BlD,OAAO,CAACmD,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACP/C,wBAAwB,EACxBN,uBAAuB,EACvBG,uBAAuB,CACxB;MACDmD,SAAS,EAAE,CAAC;QAAEC,OAAO,EAAErD,SAAS;QAAEsD,QAAQ,EAAEZ;MAAS,CAAE;KACxD,CAAC;IAEFnC,OAAO,GAAGR,OAAO,CAACwD,eAAe,CAACnD,wBAAwB,CAAC;IAC3DE,SAAS,GAAGC,OAAO,CAACiD,iBAAiB;IACrChD,MAAM,GAAGT,OAAO,CAAC0D,MAAM,CAACzD,SAAS,CAA8B;EACjE,CAAC,CAAC;EAEF0D,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACrD,SAAS,CAAC,CAACsD,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,6CAA6C,EAAE,MAAK;IACrDpD,SAAS,CAACuD,QAAQ,GAAG5C,YAAY;IACjCX,SAAS,CAACwD,QAAQ,EAAE;IACpBH,MAAM,CAACrD,SAAS,CAACyD,UAAU,CAACC,IAAI,CAAC,CAACC,OAAO,CAAChD,YAAY,CAACuB,WAAW,IAAI,EAAE,CAAC;EAC3E,CAAC,CAAC;EAEFkB,EAAE,CAAC,gDAAgD,EAAE,MAAK;IACxDpD,SAAS,CAACuD,QAAQ,GAAG5C,YAAY;IACjCX,SAAS,CAACwD,QAAQ,EAAE;IAEpB,MAAMI,aAAa,GAAe;MAChCxD,EAAE,EAAE,CAAC;MACLC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACjCC,OAAO,EAAE,IAAID,IAAI,CAAC,YAAY,CAAC;MAC/BE,MAAM,EAAE,gBAAgB;MACxBC,UAAU,EAAE,CAAC;MACbC,cAAc,EAAE;KACjB;IAED,MAAMmD,aAAa,GAAG;MACpBnB,WAAW,EAAEA,CAAA,KAAM9C,EAAE,CAACgE,aAAa;KACX;IAE1B1D,MAAM,CAACqC,IAAI,CAACC,GAAG,CAACC,WAAW,CAACoB,aAAa,CAAC;IAE1C7D,SAAS,CAAC8D,kBAAkB,EAAE;IAE9BT,MAAM,CAACnD,MAAM,CAACqC,IAAI,CAAC,CAACwB,oBAAoB,CAAClE,yBAAyB,EAAE;MAClEmE,KAAK,EAAE,OAAO;MACdN,IAAI,EAAE;QACJO,UAAU,EAAEtB,SAAS;QACrBlC,UAAU,EAAEE,YAAY,CAACP,EAAE;QAC3B8D,qBAAqB,EAAE,IAAI5D,IAAI,CAAC,YAAY;;KAE/C,CAAC;IAEF+C,MAAM,CAACrD,SAAS,CAACyD,UAAU,CAACC,IAAI,CAACS,MAAM,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;EAClD,CAAC,CAAC;EAEFhB,EAAE,CAAC,qDAAqD,EAAE,MAAK;IAC7DpD,SAAS,CAACuD,QAAQ,GAAG5C,YAAY;IACjCX,SAAS,CAACwD,QAAQ,EAAE;IAEpB,MAAMa,iBAAiB,GAAe;MACpC,GAAGlE,cAAc;MACjBK,MAAM,EAAE;KACT;IAED,MAAMqD,aAAa,GAAG;MACpBnB,WAAW,EAAEA,CAAA,KAAM9C,EAAE,CAACyE,iBAAiB;KACf;IAE1BnE,MAAM,CAACqC,IAAI,CAACC,GAAG,CAACC,WAAW,CAACoB,aAAa,CAAC;IAE1C7D,SAAS,CAAC8D,kBAAkB,CAAC3D,cAAc,CAAC;IAE5CkD,MAAM,CAACnD,MAAM,CAACqC,IAAI,CAAC,CAACwB,oBAAoB,CAAClE,yBAAyB,EAAE;MAClEmE,KAAK,EAAE,OAAO;MACdN,IAAI,EAAE;QACJO,UAAU,EAAE9D,cAAc;QAC1BM,UAAU,EAAEE,YAAY,CAACP,EAAE;QAC3B8D,qBAAqB,EAAE;;KAE1B,CAAC;IAEFb,MAAM,CAACrD,SAAS,CAACyD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,CAAClD,MAAM,CAAC,CAAC4D,IAAI,CAAC,oBAAoB,CAAC;EACxE,CAAC,CAAC;EAEFhB,EAAE,CAAC,6DAA6D,EAAE,MAAK;IACrEpD,SAAS,CAACuD,QAAQ,GAAG5C,YAAY;IACjCX,SAAS,CAACwD,QAAQ,EAAE;IACpB,MAAMc,WAAW,GAAG,CAAC,GAAGtE,SAAS,CAACyD,UAAU,CAACC,IAAI,CAAC;IAElD,MAAMG,aAAa,GAAG;MACpBnB,WAAW,EAAEA,CAAA,KAAM9C,EAAE,CAAC+C,SAAS;KACP;IAE1BzC,MAAM,CAACqC,IAAI,CAACC,GAAG,CAACC,WAAW,CAACoB,aAAa,CAAC;IAE1C7D,SAAS,CAAC8D,kBAAkB,EAAE;IAE9BT,MAAM,CAACrD,SAAS,CAACyD,UAAU,CAACC,IAAI,CAAC,CAACC,OAAO,CAACW,WAAW,CAAC;EACxD,CAAC,CAAC;EAEFlB,EAAE,CAAC,qCAAqC,EAAE,MAAK;IAC7C,MAAMmB,gBAAgB,GAAe;MACnCnE,EAAE,EAAE,CAAC;MACLC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACjCC,OAAO,EAAE,IAAID,IAAI,CAAC,YAAY,CAAC;MAC/BE,MAAM,EAAE,mBAAmB;MAC3BC,UAAU,EAAE,CAAC;MACbC,cAAc,EAAE;KACjB;IAEDV,SAAS,CAACuD,QAAQ,GAAG;MACnB,GAAG5C,YAAY;MACfuB,WAAW,EAAE,CAAC/B,cAAc,EAAEoE,gBAAgB;KAC/C;IACDvE,SAAS,CAACwD,QAAQ,EAAE;IAEpB,MAAMgB,WAAW,GAAGxE,SAAS,CAAC,0BAA0B,CAAC,EAAE;IAC3DqD,MAAM,CAACmB,WAAW,CAAC,CAACb,OAAO,CAAC,IAAIrD,IAAI,CAAC,YAAY,CAAC,CAAC;EACrD,CAAC,CAAC;EAEF8C,EAAE,CAAC,8CAA8C,EAAE,MAAK;IACtDpD,SAAS,CAACuD,QAAQ,GAAG;MACnB,GAAG5C,YAAY;MACfuB,WAAW,EAAE;KACd;IACDlC,SAAS,CAACwD,QAAQ,EAAE;IAEpB,MAAMgB,WAAW,GAAGxE,SAAS,CAAC,0BAA0B,CAAC,EAAE;IAC3DqD,MAAM,CAACmB,WAAW,CAAC,CAACC,QAAQ,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}