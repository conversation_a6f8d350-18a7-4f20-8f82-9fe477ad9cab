import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import {
  AbstractControl,
  FormBuilder,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { CCP } from '@contract-management/models/ccp.model';
import { CcpService } from '@contract-management/services/ccp.service';
import { ContractValuesService } from '@contract-management/services/contract-values.service';
import { AlertService } from '@shared/services/alert.service';
import { NgxCurrencyDirective } from 'ngx-currency';
import { of, throwError } from 'rxjs';
import { CcpDialogComponent } from './ccp-dialog.component';

describe('CcpDialogComponent', () => {
  let component: CcpDialogComponent;
  let fixture: ComponentFixture<CcpDialogComponent>;
  let dialogRef: jasmine.SpyObj<MatDialogRef<CcpDialogComponent>>;
  let ccpService: jasmine.SpyObj<CcpService>;
  let contractValuesService: jasmine.SpyObj<ContractValuesService>;
  let alertService: jasmine.SpyObj<AlertService>;

  const mockCcp: CCP = {
    id: 1,
    expenseObjectUseCcp: 'Test Object',
    expenseObjectDescription: 'Test Description',
    value: 1000,
    additionalValue: 500,
    contractId: 1,
  };

  const mockDialogData = {
    contractId: 1,
    totalCcpValue: 3000,
  };

  const mockContractValues = [
    {
      id: 1,
      numericValue: 5000,
      madsValue: 0,
      isOtherEntity: false,
      subscriptionDate: '2024-01-01',
      cdp: 123,
      cdpEntityId: 1,
      cdpEntity: { id: 1, name: 'Test Entity' },
    },
  ];

  beforeEach(async () => {
    dialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
    ccpService = jasmine.createSpyObj('CcpService', ['create', 'update']);
    contractValuesService = jasmine.createSpyObj('ContractValuesService', [
      'getAllByContractId',
    ]);
    alertService = jasmine.createSpyObj('AlertService', ['success', 'error']);

    contractValuesService.getAllByContractId.and.returnValue(
      of(mockContractValues),
    );

    await TestBed.configureTestingModule({
      imports: [
        CcpDialogComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        ReactiveFormsModule,
        MatDialogModule,
        MatButtonModule,
        MatFormFieldModule,
        MatInputModule,
        MatIconModule,
        NgxCurrencyDirective,
      ],
      providers: [
        FormBuilder,
        { provide: MatDialogRef, useValue: dialogRef },
        { provide: MAT_DIALOG_DATA, useValue: mockDialogData },
        { provide: CcpService, useValue: ccpService },
        { provide: ContractValuesService, useValue: contractValuesService },
        { provide: AlertService, useValue: alertService },
        {
          provide: 'NGX_CURRENCY_CONFIG',
          useValue: {
            align: 'right',
            allowNegative: false,
            allowZero: true,
            decimal: '.',
            precision: 2,
            prefix: '$ ',
            suffix: '',
            thousands: ',',
            nullable: true,
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CcpDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with empty values for new ccp', () => {
    expect(component.ccpForm.get('expenseObjectUseCcp')?.value).toBe('');
    expect(component.ccpForm.get('expenseObjectDescription')?.value).toBe('');
    expect(component.ccpForm.get('value')?.value).toBeNull();
    expect(contractValuesService.getAllByContractId).toHaveBeenCalledWith(
      mockDialogData.contractId,
    );
  });

  it('should initialize form with existing ccp values', () => {
    TestBed.resetTestingModule();
    TestBed.configureTestingModule({
      imports: [
        CcpDialogComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        ReactiveFormsModule,
        MatDialogModule,
        MatButtonModule,
        MatFormFieldModule,
        MatInputModule,
        MatIconModule,
        NgxCurrencyDirective,
      ],
      providers: [
        FormBuilder,
        { provide: MatDialogRef, useValue: dialogRef },
        {
          provide: MAT_DIALOG_DATA,
          useValue: { ...mockDialogData, ccp: mockCcp },
        },
        { provide: CcpService, useValue: ccpService },
        { provide: ContractValuesService, useValue: contractValuesService },
        { provide: AlertService, useValue: alertService },
        {
          provide: 'NGX_CURRENCY_CONFIG',
          useValue: {
            align: 'right',
            allowNegative: false,
            allowZero: true,
            decimal: '.',
            precision: 2,
            prefix: '$ ',
            suffix: '',
            thousands: ',',
            nullable: true,
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CcpDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    expect(component.ccpForm.get('expenseObjectUseCcp')?.value).toBe(
      mockCcp.expenseObjectUseCcp,
    );
    expect(component.ccpForm.get('expenseObjectDescription')?.value).toBe(
      mockCcp.expenseObjectDescription,
    );
    expect(component.ccpForm.get('value')?.value).toBe(mockCcp.value);
    expect(component.ccpForm.get('additionalValue')?.value).toBe(
      mockCcp.additionalValue,
    );
    expect(contractValuesService.getAllByContractId).toHaveBeenCalledWith(
      mockDialogData.contractId,
    );
  });

  it('should validate required fields', () => {
    component.ccpForm.controls['expenseObjectUseCcp'].setValue('');
    component.ccpForm.controls['expenseObjectDescription'].setValue('');
    component.ccpForm.controls['value'].setValue(null);

    expect(component.ccpForm.valid).toBeFalse();
    expect(
      component.ccpForm.get('expenseObjectUseCcp')?.errors?.['required'],
    ).toBeTruthy();
    expect(
      component.ccpForm.get('expenseObjectDescription')?.errors?.['required'],
    ).toBeTruthy();
    expect(component.ccpForm.get('value')?.errors?.['required']).toBeTruthy();
  });

  it('should validate minimum value constraints', () => {
    component.ccpForm.controls['value'].setValue(-1);
    expect(component.ccpForm.get('value')?.errors?.['min']).toBeTruthy();

    component.ccpForm.controls['additionalValue'].enable();
    component.ccpForm.controls['additionalValue'].setValue(-1);
    expect(
      component.ccpForm.get('additionalValue')?.errors?.['min'],
    ).toBeTruthy();
  });

  it('should close dialog without changes', () => {
    component.onCancel();
    expect(dialogRef.close).toHaveBeenCalled();
  });

  it('should create new ccp successfully', () => {
    const newCcp = {
      expenseObjectUseCcp: 'New Object',
      expenseObjectDescription: 'New Description',
      value: 2000,
      contractId: 1,
      additionalValue: 0,
    };

    component.ccpForm.patchValue({
      expenseObjectUseCcp: 'New Object',
      expenseObjectDescription: 'New Description',
      value: 2000,
    });

    ccpService.create.and.returnValue(of({ ...newCcp, id: 2 }));

    component.onSubmit();

    expect(ccpService.create).toHaveBeenCalledWith(newCcp);
    expect(alertService.success).toHaveBeenCalledWith(
      'CCP creado exitosamente',
    );
    expect(dialogRef.close).toHaveBeenCalledWith({ ...newCcp, id: 2 });
  });

  it('should handle error when creating ccp', () => {
    const newCcp = {
      expenseObjectUseCcp: 'New Object',
      expenseObjectDescription: 'New Description',
      value: 2000,
      contractId: 1,
      additionalValue: 0,
    };

    component.ccpForm.patchValue({
      expenseObjectUseCcp: 'New Object',
      expenseObjectDescription: 'New Description',
      value: 2000,
    });

    ccpService.create.and.returnValue(throwError(() => ({ error: 'Error' })));

    component.onSubmit();

    expect(ccpService.create).toHaveBeenCalledWith(newCcp);
    expect(alertService.error).toHaveBeenCalledWith('Error al crear el CCP');
    expect(dialogRef.close).not.toHaveBeenCalled();
  });

  it('should update existing ccp successfully', () => {
    TestBed.resetTestingModule();

    dialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
    ccpService = jasmine.createSpyObj('CcpService', ['create', 'update']);
    contractValuesService = jasmine.createSpyObj('ContractValuesService', [
      'getAllByContractId',
    ]);
    alertService = jasmine.createSpyObj('AlertService', ['success', 'error']);

    contractValuesService.getAllByContractId.and.returnValue(
      of(mockContractValues),
    );

    TestBed.configureTestingModule({
      imports: [
        CcpDialogComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        ReactiveFormsModule,
        MatDialogModule,
        MatButtonModule,
        MatFormFieldModule,
        MatInputModule,
        MatIconModule,
        NgxCurrencyDirective,
      ],
      providers: [
        FormBuilder,
        { provide: MatDialogRef, useValue: dialogRef },
        {
          provide: MAT_DIALOG_DATA,
          useValue: { ...mockDialogData, ccp: mockCcp },
        },
        { provide: CcpService, useValue: ccpService },
        { provide: ContractValuesService, useValue: contractValuesService },
        { provide: AlertService, useValue: alertService },
        {
          provide: 'NGX_CURRENCY_CONFIG',
          useValue: {
            align: 'right',
            allowNegative: false,
            allowZero: true,
            decimal: '.',
            precision: 2,
            prefix: '$ ',
            suffix: '',
            thousands: ',',
            nullable: true,
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CcpDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    component.ccpForm.setValue({
      expenseObjectUseCcp: 'Updated Object',
      expenseObjectDescription: 'Updated Description',
      value: 1000,
      additionalValue: 3000,
    });

    spyOn(component.ccpForm, 'get').and.returnValue({
      value: 'test value',
      valid: true,
      disabled: false,
      setValue: jasmine.createSpy('setValue'),
      patchValue: jasmine.createSpy('patchValue'),
      reset: jasmine.createSpy('reset'),
      updateValueAndValidity: jasmine.createSpy('updateValueAndValidity'),
    } as unknown as AbstractControl);

    const updatedCcp = {
      id: 1,
      expenseObjectUseCcp: 'Updated Object',
      expenseObjectDescription: 'Updated Description',
      value: 1000,
      additionalValue: 3000,
      contractId: 1,
    };
    ccpService.update.and.returnValue(of(updatedCcp));

    Object.defineProperty(component.ccpForm, 'valid', { get: () => true });

    component.onSubmit();

    expect(ccpService.update).toHaveBeenCalled();
    expect(alertService.success).toHaveBeenCalledWith(
      'CCP editado exitosamente',
    );
    expect(dialogRef.close).toHaveBeenCalled();
  });

  it('should handle error when updating ccp', () => {
    TestBed.resetTestingModule();

    dialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
    ccpService = jasmine.createSpyObj('CcpService', ['create', 'update']);
    contractValuesService = jasmine.createSpyObj('ContractValuesService', [
      'getAllByContractId',
    ]);
    alertService = jasmine.createSpyObj('AlertService', ['success', 'error']);

    contractValuesService.getAllByContractId.and.returnValue(
      of(mockContractValues),
    );

    TestBed.configureTestingModule({
      imports: [
        CcpDialogComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        ReactiveFormsModule,
        MatDialogModule,
        MatButtonModule,
        MatFormFieldModule,
        MatInputModule,
        MatIconModule,
        NgxCurrencyDirective,
      ],
      providers: [
        FormBuilder,
        { provide: MatDialogRef, useValue: dialogRef },
        {
          provide: MAT_DIALOG_DATA,
          useValue: { ...mockDialogData, ccp: mockCcp },
        },
        { provide: CcpService, useValue: ccpService },
        { provide: ContractValuesService, useValue: contractValuesService },
        { provide: AlertService, useValue: alertService },
        {
          provide: 'NGX_CURRENCY_CONFIG',
          useValue: {
            align: 'right',
            allowNegative: false,
            allowZero: true,
            decimal: '.',
            precision: 2,
            prefix: '$ ',
            suffix: '',
            thousands: ',',
            nullable: true,
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CcpDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    component.ccpForm.setValue({
      expenseObjectUseCcp: 'Updated Object',
      expenseObjectDescription: 'Updated Description',
      value: 1000,
      additionalValue: 3000,
    });

    Object.defineProperty(component.ccpForm, 'valid', { get: () => true });

    ccpService.update.and.returnValue(throwError(() => ({ error: 'Error' })));

    component.onSubmit();

    expect(ccpService.update).toHaveBeenCalled();
    expect(alertService.error).toHaveBeenCalledWith('Error al editar el CCP');
    expect(dialogRef.close).not.toHaveBeenCalled();
  });

  it('should handle error when loading contract values', () => {
    TestBed.resetTestingModule();

    contractValuesService = jasmine.createSpyObj('ContractValuesService', [
      'getAllByContractId',
    ]);
    contractValuesService.getAllByContractId.and.returnValue(
      throwError(() => ({ error: 'Error' })),
    );

    TestBed.configureTestingModule({
      imports: [
        CcpDialogComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        ReactiveFormsModule,
        MatDialogModule,
        MatButtonModule,
        MatFormFieldModule,
        MatInputModule,
        MatIconModule,
        NgxCurrencyDirective,
      ],
      providers: [
        FormBuilder,
        { provide: MatDialogRef, useValue: dialogRef },
        { provide: MAT_DIALOG_DATA, useValue: mockDialogData },
        { provide: CcpService, useValue: ccpService },
        { provide: ContractValuesService, useValue: contractValuesService },
        { provide: AlertService, useValue: alertService },
        {
          provide: 'NGX_CURRENCY_CONFIG',
          useValue: {
            align: 'right',
            allowNegative: false,
            allowZero: true,
            decimal: '.',
            precision: 2,
            prefix: '$ ',
            suffix: '',
            thousands: ',',
            nullable: true,
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CcpDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar los valores del contrato',
    );
  });

  it('should validate maxValueExceeded for new CCP', () => {

    component.remainingValue = 1000;

    component.ccpForm.get('value')?.setValue(1500);
    fixture.detectChanges();

    expect(
      component.ccpForm.get('value')?.errors?.['maxValueExceeded'],
    ).toBeTruthy();

    component.ccpForm.get('value')?.setValue(800);
    fixture.detectChanges();

    expect(
      component.ccpForm.get('value')?.errors?.['maxValueExceeded'],
    ).toBeFalsy();
  });

  it('should validate additionalValue for existing CCP - less than previous value', () => {
    TestBed.resetTestingModule();

    const existingCcp = {
      ...mockCcp,
      additionalValue: 1000,
    };

    TestBed.configureTestingModule({
      imports: [
        CcpDialogComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        ReactiveFormsModule,
        MatDialogModule,
        MatButtonModule,
        MatFormFieldModule,
        MatInputModule,
        MatIconModule,
        NgxCurrencyDirective,
      ],
      providers: [
        FormBuilder,
        { provide: MatDialogRef, useValue: dialogRef },
        {
          provide: MAT_DIALOG_DATA,
          useValue: { ...mockDialogData, ccp: existingCcp },
        },
        { provide: CcpService, useValue: ccpService },
        { provide: ContractValuesService, useValue: contractValuesService },
        { provide: AlertService, useValue: alertService },
        {
          provide: 'NGX_CURRENCY_CONFIG',
          useValue: {
            align: 'right',
            allowNegative: false,
            allowZero: true,
            decimal: '.',
            precision: 2,
            prefix: '$ ',
            suffix: '',
            thousands: ',',
            nullable: true,
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CcpDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    component.ccpForm.get('additionalValue')?.setValue(500);
    fixture.detectChanges();

    expect(
      component.ccpForm.get('additionalValue')?.errors?.['lessThanPrevious'],
    ).toBeTruthy();
  });

  it('should validate additionalValue for existing CCP - exceeds available value', () => {
    TestBed.resetTestingModule();

    const existingCcp = {
      ...mockCcp,
      additionalValue: 1000,
    };

    TestBed.configureTestingModule({
      imports: [
        CcpDialogComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        ReactiveFormsModule,
        MatDialogModule,
        MatButtonModule,
        MatFormFieldModule,
        MatInputModule,
        MatIconModule,
        NgxCurrencyDirective,
      ],
      providers: [
        FormBuilder,
        { provide: MatDialogRef, useValue: dialogRef },
        {
          provide: MAT_DIALOG_DATA,
          useValue: { ...mockDialogData, ccp: existingCcp },
        },
        { provide: CcpService, useValue: ccpService },
        { provide: ContractValuesService, useValue: contractValuesService },
        { provide: AlertService, useValue: alertService },
        {
          provide: 'NGX_CURRENCY_CONFIG',
          useValue: {
            align: 'right',
            allowNegative: false,
            allowZero: true,
            decimal: '.',
            precision: 2,
            prefix: '$ ',
            suffix: '',
            thousands: ',',
            nullable: true,
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CcpDialogComponent);
    component = fixture.componentInstance;

    component.totalContractValue = 5000;
    component.currentTotalCcpValue = 4000;
    component.remainingValue = 2000;

    fixture.detectChanges();

    component.ccpForm.get('additionalValue')?.setValue(4000);
    fixture.detectChanges();

    expect(
      component.ccpForm.get('additionalValue')?.errors?.['maxValueExceeded'],
    ).toBeTruthy();

    component.ccpForm.get('additionalValue')?.setValue(1500);
    fixture.detectChanges();

    expect(
      component.ccpForm.get('additionalValue')?.errors?.['maxValueExceeded'],
    ).toBeFalsy();
  });

  it('should not submit form when it is invalid', () => {

    component.ccpForm.controls['expenseObjectUseCcp'].setValue('');
    component.ccpForm.controls['expenseObjectDescription'].setValue('');
    component.ccpForm.controls['value'].setValue(null);

    component.onSubmit();

    expect(ccpService.create).not.toHaveBeenCalled();
    expect(dialogRef.close).not.toHaveBeenCalled();
  });

  it('should subscribe to value changes for new CCPs', () => {

    TestBed.resetTestingModule();

    dialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
    ccpService = jasmine.createSpyObj('CcpService', ['create', 'update']);
    contractValuesService = jasmine.createSpyObj('ContractValuesService', [
      'getAllByContractId',
    ]);
    alertService = jasmine.createSpyObj('AlertService', ['success', 'error']);

    contractValuesService.getAllByContractId.and.returnValue(
      of(mockContractValues),
    );

    TestBed.configureTestingModule({
      imports: [
        CcpDialogComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        ReactiveFormsModule,
        MatDialogModule,
        MatButtonModule,
        MatFormFieldModule,
        MatInputModule,
        MatIconModule,
        NgxCurrencyDirective,
      ],
      providers: [
        FormBuilder,
        { provide: MatDialogRef, useValue: dialogRef },
        { provide: MAT_DIALOG_DATA, useValue: mockDialogData },
        { provide: CcpService, useValue: ccpService },
        { provide: ContractValuesService, useValue: contractValuesService },
        { provide: AlertService, useValue: alertService },
        {
          provide: 'NGX_CURRENCY_CONFIG',
          useValue: {
            align: 'right',
            allowNegative: false,
            allowZero: true,
            decimal: '.',
            precision: 2,
            prefix: '$ ',
            suffix: '',
            thousands: ',',
            nullable: true,
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CcpDialogComponent);
    component = fixture.componentInstance;

    component.remainingValue = 3000;

    const valueControl = component.ccpForm.get('value');
    spyOn(valueControl!, 'setErrors');

    fixture.detectChanges();

    valueControl!.setValue(4000);

    expect(valueControl!.setErrors).toHaveBeenCalledWith({
      maxValueExceeded: true,
    });

    valueControl!.setValue(2000);

    expect(valueControl!.setErrors).toHaveBeenCalledTimes(1);
  });

  it('should calculate contract values properly', () => {

    contractValuesService.getAllByContractId.and.returnValue(
      of([
        {
          id: 1,
          numericValue: 5000,
          madsValue: 0,
          isOtherEntity: false,
          subscriptionDate: '2024-01-01',
          cdp: 123,
          cdpEntityId: 1,
          cdpEntity: { id: 1, name: 'Test Entity' },
        },
        {
          id: 2,
          numericValue: 3000,
          madsValue: 0,
          isOtherEntity: false,
          subscriptionDate: '2024-01-01',
          cdp: 456,
          cdpEntityId: 1,
          cdpEntity: { id: 1, name: 'Test Entity' },
        },
      ]),
    );

    fixture = TestBed.createComponent(CcpDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    expect(component.totalContractValue).toBe(8000);
    expect(component.remainingValue).toBe(5000);
  });
});