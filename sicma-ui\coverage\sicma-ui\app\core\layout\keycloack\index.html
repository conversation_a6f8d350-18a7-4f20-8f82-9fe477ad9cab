
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for app/core/layout/keycloack</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../index.html">All files</a> app/core/layout/keycloack</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">81.48% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>44/54</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">59.09% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>13/22</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">90.47% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>19/21</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">80.39% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>41/51</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="keycloack.component.ts"><a href="keycloack.component.ts.html">keycloack.component.ts</a></td>
	<td data-value="81.48" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 81%"></div><div class="cover-empty" style="width: 19%"></div></div>
	</td>
	<td data-value="81.48" class="pct high">81.48%</td>
	<td data-value="54" class="abs high">44/54</td>
	<td data-value="59.09" class="pct medium">59.09%</td>
	<td data-value="22" class="abs medium">13/22</td>
	<td data-value="90.47" class="pct high">90.47%</td>
	<td data-value="21" class="abs high">19/21</td>
	<td data-value="80.39" class="pct high">80.39%</td>
	<td data-value="51" class="abs high">41/51</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-16T16:33:29.688Z
            </div>
        <script src="../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../sorter.js"></script>
        <script src="../../../../block-navigation.js"></script>
    </body>
</html>
    