import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { Contract } from '@contract-management/models/contract.model';
import { ContractorContract } from '@contract-management/models/contractor-contract.model';
import { ContractorContractService } from '@contract-management/services/contractor-contract.service';
import { AlertService } from '@shared/services/alert.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { of, throwError } from 'rxjs';
import { AssociateContractorDialogComponent } from './associate-contractor-dialog/associate-contractor-dialog.component';
import { AssociatedContractorsListComponent } from './associated-contractors-list.component';

describe('AssociatedContractorsListComponent', () => {
  let component: AssociatedContractorsListComponent;
  let fixture: ComponentFixture<AssociatedContractorsListComponent>;
  let dialog: jasmine.SpyObj<MatDialog>;
  let contractorContractService: jasmine.SpyObj<ContractorContractService>;
  let alertService: jasmine.SpyObj<AlertService>;
  let spinner: jasmine.SpyObj<NgxSpinnerService>;

  const mockContract: Contract = {
    id: 1,
    contractNumber: 123,
    monthlyPayment: 1000000,
    object: 'Test Contract',
    rup: true,
    secopCode: 123456,
    addition: false,
    cession: false,
    settled: false,
    contractTypeId: 1,
    statusId: 1,
    causesSelectionId: 1,
    managementSupportId: 1,
    contractClassId: 1,
  };

  const mockContractorContracts: ContractorContract[] = [
    {
      id: 1,
      subscriptionDate: '2024-01-01',
      contractStartDate: '2024-01-01',
      contractEndDate: '2024-12-31',
      contractId: 1,
      contractorId: 1,
      contractor: {
        id: 1,
        fullName: 'John Doe',
        idNumber: 123456789,
        personalEmail: '<EMAIL>',
        corporateEmail: '<EMAIL>',
        idType: { id: 1, name: 'CC' },
      },
    },
    {
      id: 2,
      subscriptionDate: '2024-01-01',
      contractStartDate: '2024-01-01',
      contractEndDate: '2024-12-31',
      contractId: 1,
      contractorId: 2,
      contractor: {
        id: 2,
        fullName: 'Jane Smith',
        idNumber: 987654321,
        personalEmail: '<EMAIL>',
        corporateEmail: '<EMAIL>',
        idType: { id: 1, name: 'CC' },
      },
    },
  ];

  beforeEach(async () => {
    const dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);
    const contractorContractServiceSpy = jasmine.createSpyObj(
      'ContractorContractService',
      ['getAllByContractId'],
    );
    const alertServiceSpy = jasmine.createSpyObj('AlertService', [
      'success',
      'error',
    ]);
    const spinnerSpy = jasmine.createSpyObj('NgxSpinnerService', [
      'show',
      'hide',
    ]);

    await TestBed.configureTestingModule({
      imports: [
        AssociatedContractorsListComponent,
        BrowserAnimationsModule,
        MatTableModule,
        MatPaginatorModule,
        MatSortModule,
      ],
      providers: [
        { provide: MatDialog, useValue: dialogSpy },
        {
          provide: ContractorContractService,
          useValue: contractorContractServiceSpy,
        },
        { provide: AlertService, useValue: alertServiceSpy },
        { provide: NgxSpinnerService, useValue: spinnerSpy },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AssociatedContractorsListComponent);
    component = fixture.componentInstance;
    dialog = TestBed.inject(MatDialog) as jasmine.SpyObj<MatDialog>;
    contractorContractService = TestBed.inject(
      ContractorContractService,
    ) as jasmine.SpyObj<ContractorContractService>;
    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;
    spinner = TestBed.inject(
      NgxSpinnerService,
    ) as jasmine.SpyObj<NgxSpinnerService>;

    component.contract = mockContract;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load contractor contracts on init', () => {
    contractorContractService.getAllByContractId.and.returnValue(
      of(mockContractorContracts),
    );

    component.ngOnInit();

    expect(spinner.show).toHaveBeenCalled();
    expect(contractorContractService.getAllByContractId).toHaveBeenCalledWith(
      mockContract.id,
    );
    expect(component.dataSource.data).toEqual(mockContractorContracts);
    expect(spinner.hide).toHaveBeenCalled();
  });

  it('should handle error when loading contractor contracts', () => {
    contractorContractService.getAllByContractId.and.returnValue(
      throwError(() => ({ error: 'Error' })),
    );

    component.ngOnInit();

    expect(spinner.show).toHaveBeenCalled();
    expect(contractorContractService.getAllByContractId).toHaveBeenCalledWith(
      mockContract.id,
    );
    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar los contratos de contratistas',
    );
    expect(spinner.hide).toHaveBeenCalled();
  });

  it('should set up sort after view init', () => {
    contractorContractService.getAllByContractId.and.returnValue(
      of(mockContractorContracts),
    );
    component.ngOnInit();
    fixture.detectChanges();
    component.ngAfterViewInit();

    expect(component.dataSource.sort).toBeTruthy();
  });

  it('should update contractor contract end date', () => {
    contractorContractService.getAllByContractId.and.returnValue(
      of(mockContractorContracts),
    );
    component.ngOnInit();

    const newEndDate = '2024-06-30';
    component.updateContractorContracts(
      newEndDate,
      mockContractorContracts[0].id,
    );

    const updatedContract = component.dataSource.data.find(
      (cc) => cc.id === mockContractorContracts[0].id,
    );
    expect(updatedContract?.contractEndDate).toBe(newEndDate);
    expect(component.isEarlyTerminationDisabled).toBeFalse();
  });

  it('should get latest contractor contract', () => {
    contractorContractService.getAllByContractId.and.returnValue(
      of(mockContractorContracts),
    );
    component.ngOnInit();

    const latestContract = component.getLatestContractorContract();
    expect(latestContract).toEqual(
      mockContractorContracts[mockContractorContracts.length - 1],
    );
  });

  it('should open associate contractor dialog and handle success with new contractor', () => {
    const mockDialogRef = {
      afterClosed: () => of({ success: true, contractorAdded: true }),
    } as MatDialogRef<unknown>;

    dialog.open.and.returnValue(mockDialogRef);
    contractorContractService.getAllByContractId.and.returnValue(
      of(mockContractorContracts),
    );

    const contractorContractsChangedSpy = spyOn(
      component.contractorContractsChanged,
      'emit',
    );
    const contractorAddedSpy = spyOn(component.contractorAdded, 'emit');

    component.openAssociateContractorDialog();

    expect(dialog.open).toHaveBeenCalledWith(
      AssociateContractorDialogComponent,
      {
        width: '800px',
        data: { contractId: mockContract.id },
      },
    );
    expect(contractorContractsChangedSpy).toHaveBeenCalled();
    expect(contractorAddedSpy).toHaveBeenCalled();
    expect(component.isEarlyTerminationDisabled).toBeTrue();
  });

  it('should open associate contractor dialog and handle success without new contractor', () => {
    const mockDialogRef = {
      afterClosed: () => of({ success: true, contractorAdded: false }),
    } as MatDialogRef<unknown>;

    dialog.open.and.returnValue(mockDialogRef);
    contractorContractService.getAllByContractId.and.returnValue(
      of(mockContractorContracts),
    );

    const contractorContractsChangedSpy = spyOn(
      component.contractorContractsChanged,
      'emit',
    );
    const contractorAddedSpy = spyOn(component.contractorAdded, 'emit');

    component.openAssociateContractorDialog();

    expect(dialog.open).toHaveBeenCalledWith(
      AssociateContractorDialogComponent,
      {
        width: '800px',
        data: { contractId: mockContract.id },
      },
    );
    expect(contractorContractsChangedSpy).toHaveBeenCalled();
    expect(contractorAddedSpy).not.toHaveBeenCalled();
    expect(component.isEarlyTerminationDisabled).toBeFalse();
  });

  it('should open associate contractor dialog and handle dialog close without result', () => {
    const mockDialogRef = {
      afterClosed: () => of(undefined),
    } as MatDialogRef<unknown>;

    dialog.open.and.returnValue(mockDialogRef);
    contractorContractService.getAllByContractId.and.returnValue(
      of(mockContractorContracts),
    );

    const contractorContractsChangedSpy = spyOn(
      component.contractorContractsChanged,
      'emit',
    );
    const contractorAddedSpy = spyOn(component.contractorAdded, 'emit');

    component.openAssociateContractorDialog();

    expect(dialog.open).toHaveBeenCalledWith(
      AssociateContractorDialogComponent,
      {
        width: '800px',
        data: { contractId: mockContract.id },
      },
    );
    expect(contractorContractsChangedSpy).not.toHaveBeenCalled();
    expect(contractorAddedSpy).not.toHaveBeenCalled();
    expect(component.isEarlyTerminationDisabled).toBeFalse();
  });
});
