import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { MatSlideToggleChange } from '@angular/material/slide-toggle';
import { of } from 'rxjs';
import { ContractDetailFormComponent } from './contract-detail-form.component';
import { TrackingTypeService } from '@contract-management/services/tracking-type.service';
import { SelectionModalityService } from '@contract-management/services/selection-modality.service';
import { DependencyService } from '@contract-management/services/dependency.service';
import { ContractTypeService } from '@contract-management/services/contract-type.service';
import { GroupService } from '@contract-management/services/group.service';
import { DepartmentService } from '@shared/services/department.service';
import { MunicipalityService } from '@shared/services/municipality.service';
import { SupervisorService } from '@supervisor-management/services/supervisor.service';
import { Group } from '@contract-management/models/group.model';
import { Supervisor } from '@supervisor-management/models/supervisor.model';
import { IDType } from '@shared/models/id-type.model';
import { ContractYearService } from '../../services/contract-year.service';
import { TypeWarrantyService } from '@contract-management/services/type_warranty.service';
import { InsuredRisksService } from '@contract-management/services/insured_risks.service';
import { ManagementSupportService } from '@contract-management/services/management-support.service';
import { CausesSelectionService } from '@contract-management/services/causes-selection.service';
import { ContractClassService } from '@contract-management/services/contract-class.service';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatCardModule } from '@angular/material/card';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { Contract } from '@contract-management/models/contract.model';

describe('ContractDetailFormComponent', () => {
  let component: ContractDetailFormComponent;
  let fixture: ComponentFixture<ContractDetailFormComponent>;
  let supervisorService: jasmine.SpyObj<SupervisorService>;
  let dependencyService: jasmine.SpyObj<DependencyService>;
  let groupService: jasmine.SpyObj<GroupService>;
  let trackingTypeService: jasmine.SpyObj<TrackingTypeService>;
  let selectionModalityService: jasmine.SpyObj<SelectionModalityService>;
  let contractTypeService: jasmine.SpyObj<ContractTypeService>;
  let departmentService: jasmine.SpyObj<DepartmentService>;
  let municipalityService: jasmine.SpyObj<MunicipalityService>;
  let contractYearService: jasmine.SpyObj<ContractYearService>;
  let typeWarrantyService: jasmine.SpyObj<TypeWarrantyService>;
  let insuredRisksService: jasmine.SpyObj<InsuredRisksService>;
  let managementSupportService: jasmine.SpyObj<ManagementSupportService>;
  let causesSelectionService: jasmine.SpyObj<CausesSelectionService>;
  let contractClassService: jasmine.SpyObj<ContractClassService>;

  beforeEach(async () => {
    supervisorService = jasmine.createSpyObj('SupervisorService', ['getAll']);
    dependencyService = jasmine.createSpyObj('DependencyService', ['getAll']);
    groupService = jasmine.createSpyObj('GroupService', ['getAll']);
    trackingTypeService = jasmine.createSpyObj('TrackingTypeService', [
      'getAll',
    ]);
    selectionModalityService = jasmine.createSpyObj(
      'SelectionModalityService',
      ['getAll'],
    );
    contractTypeService = jasmine.createSpyObj('ContractTypeService', [
      'getAll',
    ]);
    departmentService = jasmine.createSpyObj('DepartmentService', ['getAll']);
    municipalityService = jasmine.createSpyObj('MunicipalityService', [
      'getAllByDepartmentId',
      'getAll',
    ]);
    contractYearService = jasmine.createSpyObj('ContractYearService', [
      'getAll',
    ]);
    typeWarrantyService = jasmine.createSpyObj('TypeWarrantyService', [
      'getAll',
    ]);
    insuredRisksService = jasmine.createSpyObj('InsuredRisksService', [
      'getAll',
    ]);
    managementSupportService = jasmine.createSpyObj(
      'ManagementSupportService',
      ['getAll'],
    );
    causesSelectionService = jasmine.createSpyObj('CausesSelectionService', [
      'getAll',
    ]);
    contractClassService = jasmine.createSpyObj('ContractClassService', [
      'getAll',
    ]);

    supervisorService.getAll.and.returnValue(of([]));
    dependencyService.getAll.and.returnValue(of([]));
    groupService.getAll.and.returnValue(of([]));
    trackingTypeService.getAll.and.returnValue(of([]));
    selectionModalityService.getAll.and.returnValue(of([]));
    contractTypeService.getAll.and.returnValue(of([]));
    departmentService.getAll.and.returnValue(of([]));
    municipalityService.getAll.and.returnValue(of([]));
    municipalityService.getAllByDepartmentId.and.returnValue(of([]));
    contractYearService.getAll.and.returnValue(of([]));
    typeWarrantyService.getAll.and.returnValue(of([]));
    insuredRisksService.getAll.and.returnValue(of([]));
    managementSupportService.getAll.and.returnValue(of([]));
    causesSelectionService.getAll.and.returnValue(of([]));
    contractClassService.getAll.and.returnValue(of([]));

    await TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        BrowserAnimationsModule,
        ReactiveFormsModule,
        NoopAnimationsModule,
        MatAutocompleteModule,
        MatCardModule,
        MatSelectModule,
        MatInputModule,
        MatFormFieldModule,
        MatDatepickerModule,
        MatNativeDateModule,
        MatCheckboxModule,
        MatSlideToggleModule,
        ContractDetailFormComponent,
      ],
      providers: [
        { provide: SupervisorService, useValue: supervisorService },
        { provide: DependencyService, useValue: dependencyService },
        { provide: GroupService, useValue: groupService },
        { provide: TrackingTypeService, useValue: trackingTypeService },
        {
          provide: SelectionModalityService,
          useValue: selectionModalityService,
        },
        { provide: ContractTypeService, useValue: contractTypeService },
        { provide: DepartmentService, useValue: departmentService },
        { provide: MunicipalityService, useValue: municipalityService },
        { provide: ContractYearService, useValue: contractYearService },
        { provide: TypeWarrantyService, useValue: typeWarrantyService },
        { provide: InsuredRisksService, useValue: insuredRisksService },
        {
          provide: ManagementSupportService,
          useValue: managementSupportService,
        },
        { provide: CausesSelectionService, useValue: causesSelectionService },
        { provide: ContractClassService, useValue: contractClassService },
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ContractDetailFormComponent);
    component = fixture.componentInstance;
    component.contract = { id: 1 } as Contract;

    spyOn(component, 'onRupChange').and.callFake(
      (event: MatSlideToggleChange) => {
        if (event.checked) {
          component.contractForm.get('addition')?.enable();
          component.contractForm.get('cession')?.enable();
          component.contractForm.get('settled')?.enable();
        } else {
          component.contractForm.get('addition')?.disable();
          component.contractForm.get('cession')?.disable();
          component.contractForm.get('settled')?.disable();
        }
        return Promise.resolve();
      },
    );

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize the form with default values', () => {
    expect(component.contractForm).toBeDefined();
    expect(component.contractForm.get('contractNumber')).toBeDefined();
    expect(component.contractForm.get('contractYear')).toBeDefined();
    expect(component.contractForm.get('object')).toBeDefined();
    expect(component.contractForm.get('warranty')).toBeDefined();
    expect(component.contractForm.get('warranty')?.value).toBeFalse();
  });

  it('should load data on init', () => {

    expect(component).toBeTruthy();
  });

  it('should handle dependency change', () => {
    const mockDependency = { id: 1, name: 'Dependency 1' };
    const mockGroup1 = {
      id: 1,
      name: 'Group 1',
      dependencyId: 1,
      dependency: mockDependency,
    } as Group;
    const mockGroup2 = {
      id: 2,
      name: 'Group 2',
      dependencyId: 1,
      dependency: mockDependency,
    } as Group;
    const mockGroup3 = {
      id: 3,
      name: 'Group 3',
      dependencyId: 2,
      dependency: { id: 2, name: 'Dependency 2' },
    } as Group;

    component.groups = [mockGroup1, mockGroup2, mockGroup3];
    component.onDependencyChange(1);
    expect(component.filteredGroups.length).toBe(2);
    expect(component.filteredGroups[0].id).toBe(1);
    expect(component.filteredGroups[1].id).toBe(2);
  });

  it('should handle department change', () => {

    const mockMunicipalities = [
      {
        id: 1,
        name: 'Municipality 1',
        departmentId: 1,
        department: { id: 1, name: 'Department 1' },
      },
    ];

    component.municipalities = [...mockMunicipalities];

    component.onDepartmentChange(1);

    expect(component.filteredMunicipalities).toEqual([
      {
        id: 1,
        name: 'Municipality 1',
        departmentId: 1,
        department: { id: 1, name: 'Department 1' },
      },
    ]);
  });

  it('should handle RUP change', () => {

    component.contractForm.get('addition')?.disable();
    component.contractForm.get('cession')?.disable();
    component.contractForm.get('settled')?.disable();

    const event = { checked: true } as MatSlideToggleChange;

    component.onRupChange(event);

    expect(component.contractForm.get('addition')?.enabled).toBeTrue();
    expect(component.contractForm.get('cession')?.enabled).toBeTrue();
    expect(component.contractForm.get('settled')?.enabled).toBeTrue();

    const disableEvent = { checked: false } as MatSlideToggleChange;
    component.onRupChange(disableEvent);

    expect(component.contractForm.get('addition')?.enabled).toBeFalse();
    expect(component.contractForm.get('cession')?.enabled).toBeFalse();
    expect(component.contractForm.get('settled')?.enabled).toBeFalse();
  });

  it('should handle supervisor selection', () => {

    const mockSupervisor = {
      id: 1,
      fullName: 'Test Supervisor',
      idNumber: 123456789,
      position: 'Test Position',
      email: '<EMAIL>',
      idType: { id: 1, name: 'CC' } as IDType,
    } as Supervisor;

    const event = {
      option: { value: mockSupervisor },
    } as MatAutocompleteSelectedEvent;
    component.onSupervisorSelected(event);
    expect(component.supervisor).toBe(mockSupervisor);
    expect(component.contractForm.get('supervisorId')?.value).toBe(1);
  });

  it('should clear supervisor', () => {

    const mockSupervisor = {
      id: 1,
      fullName: 'Test Supervisor',
      idNumber: 123456789,
      position: 'Test Position',
      email: '<EMAIL>',
      idType: { id: 1, name: 'CC' } as IDType,
    } as Supervisor;

    component.supervisor = mockSupervisor;
    component.contractForm.patchValue({
      supervisorId: 1,
      supervisorFullName: 'Test Supervisor',
    });
    component.clearSupervisor();
    expect(component.supervisor).toBeNull();
    expect(component.contractForm.get('supervisorId')?.value).toBeNull();
    expect(component.contractForm.get('supervisorFullName')?.value).toBe('');
  });

  it('should check form validity', () => {

    expect(component.isValid()).toBeFalse();

    component.contractForm.patchValue({
      contractNumber: 123,
      contractYear: 2023,
      object: 'Test Contract',
      selectionModality: 1,
      trackingType: 1,
      contractType: 1,
      dependency: 1,
      group: 1,
      monthlyPayment: 1000,
      municipalityId: 1,
      departmentId: 1,
      secopCode: 'TEST123',
      supervisorId: 1,
      supervisorFullName: 'Test Supervisor',
      causesSelectionId: 1,
      contractClassId: 1,
      managementSupportId: 1,
    });

    expect(component.isValid()).toBeTrue();
  });

  it('should get form values', () => {
    component.contractForm.patchValue({
      contractNumber: 123,
      contractYear: 2023,
      object: 'Test Contract',
      selectionModality: 1,
      warranty: true,
      dateExpeditionWarranty: '2023-01-01',
      typeWarrantyId: 1,
      insuredRisksId: 1,
    });

    const formValues = component.getValue();

    expect(formValues.contractNumber).toBe(123);
    expect(formValues.contractYear).toBe(2023);
    expect(formValues.object).toBe('Test Contract');
    expect(formValues.selectionModality).toBe(1);
    expect(formValues.warranty).toBeTrue();
    expect(formValues.dateExpeditionWarranty).toBe('2023-01-01');
    expect(formValues.typeWarrantyId).toBe(1);
    expect(formValues.insuredRisksId).toBe(1);
  });
});