import { HttpClientTestingModule } from '@angular/common/http/testing';
import { Component } from '@angular/core';
import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { Contract } from '@contract-management/models/contract.model';
import { ContractService } from '@contract-management/services/contract.service';
import { InitialReportDocumentation } from '@contractor-dashboard/models/initial-report-documentation.model';
import { SocialSecurityContribution } from '@contractor-dashboard/models/social-security-contribution.model';
import { ArlAffiliationClassService } from '@contractor-dashboard/services/arl-affiliation-class.service';
import { CompensationFundService } from '@contractor-dashboard/services/compensation-fund.service';
import { InitialReportDocumentationService } from '@contractor-dashboard/services/initial-report-documentation.service';
import { PeriodService } from '@contractor-dashboard/services/period.service';
import { SocialSecurityContributionService } from '@contractor-dashboard/services/social-security-contribution.service';
import { AlertService } from '@shared/services/alert.service';
import { of, throwError } from 'rxjs';
import { MonthlyReportSocialSecurityInformationComponent } from './monthly-report-social-security-information.component';

@Component({
  selector: 'app-monthly-report-social-security-information',
  template: `
    <form [formGroup]="socialSecurityForm">
      <input formControlName="arlContribution" />
      <input formControlName="compensationFundContribution" />
    </form>
  `,
})
class MockMonthlyReportSocialSecurityInformationComponent extends MonthlyReportSocialSecurityInformationComponent {}

describe('MonthlyReportSocialSecurityInformationComponent', () => {
  let component: MockMonthlyReportSocialSecurityInformationComponent;
  let fixture: ComponentFixture<MockMonthlyReportSocialSecurityInformationComponent>;
  let contractService: jasmine.SpyObj<ContractService>;
  let initialReportDocumentationService: jasmine.SpyObj<InitialReportDocumentationService>;
  let alertService: jasmine.SpyObj<AlertService>;
  let arlAffiliationClassService: jasmine.SpyObj<ArlAffiliationClassService>;
  let compensationFundService: jasmine.SpyObj<CompensationFundService>;
  let socialSecurityContributionService: jasmine.SpyObj<SocialSecurityContributionService>;

  const mockContract: Contract = {
    id: 1,
    contractNumber: 1,
    monthlyPayment: 1000000,
    object: 'Test contract',
    rup: true,
    secopCode: 123,
    addition: false,
    cession: false,
    settled: false,
    causesSelectionId: 1,
    managementSupportId: 1,
    contractClassId: 1,
  };

  const mockInitialReportDocumentation: InitialReportDocumentation = {
    id: 1,
    contractorContractId: 1,
    epsId: 1,
    arlId: 1,
    pensionFundId: 1,
    bankId: 1,
    bankAccountTypeId: 1,
    taxRegimeId: 1,
    accountNumber: '123',
    hasDependents: false,
    hasHousingInterest: false,
    hasPrepaidMedicine: false,
    hasAfcAccount: false,
    hasVoluntarySavings: false,
  };

  const mockArlAffiliationClasses = [
    { id: 1, name: 'Class 1', percentage: 0.522 },
    { id: 2, name: 'Class 2', percentage: 1.044 },
  ];

  const mockCompensationFunds = [
    { id: 1, name: 'Fund 1' },
    { id: 2, name: 'Fund 2' },
  ];

  const mockSocialSecurityContribution: SocialSecurityContribution = {
    id: 1,
    monthlyReportId: 1,
    paymentFormNumber: 12345,
    certificateFileUrl: 'http://example.com/file.pdf',
    arlAffiliationClassId: 1,
    arlContribution: 5000,
    compensationFundId: 1,
    compensationFundContribution: 4000,
    healthContribution: 50000,
    pensionContribution: 64000,
    ibc: 400000,
  };

  beforeEach(async () => {
    const contractServiceSpy = jasmine.createSpyObj('ContractService', [
      'getById',
    ]);
    const initialReportDocServiceSpy = jasmine.createSpyObj(
      'InitialReportDocumentationService',
      ['getByContractorContractId'],
    );
    const alertServiceSpy = jasmine.createSpyObj('AlertService', ['error']);
    const arlServiceSpy = jasmine.createSpyObj('ArlAffiliationClassService', [
      'getAll',
    ]);
    const compensationFundServiceSpy = jasmine.createSpyObj(
      'CompensationFundService',
      ['getAll'],
    );
    const socialSecurityServiceSpy = jasmine.createSpyObj(
      'SocialSecurityContributionService',
      ['getByMonthlyReportId', 'updateWithFile'],
    );
    const periodServiceSpy = jasmine.createSpyObj('PeriodService', [
      'getCurrentPeriod',
    ]);

    await TestBed.configureTestingModule({
      declarations: [MockMonthlyReportSocialSecurityInformationComponent],
      imports: [
        HttpClientTestingModule,
        BrowserAnimationsModule,
        ReactiveFormsModule,
      ],
      providers: [
        FormBuilder,
        { provide: ContractService, useValue: contractServiceSpy },
        {
          provide: InitialReportDocumentationService,
          useValue: initialReportDocServiceSpy,
        },
        { provide: AlertService, useValue: alertServiceSpy },
        { provide: ArlAffiliationClassService, useValue: arlServiceSpy },
        {
          provide: CompensationFundService,
          useValue: compensationFundServiceSpy,
        },
        {
          provide: SocialSecurityContributionService,
          useValue: socialSecurityServiceSpy,
        },
        { provide: PeriodService, useValue: periodServiceSpy },
      ],
    }).compileComponents();

    contractService = TestBed.inject(
      ContractService,
    ) as jasmine.SpyObj<ContractService>;
    initialReportDocumentationService = TestBed.inject(
      InitialReportDocumentationService,
    ) as jasmine.SpyObj<InitialReportDocumentationService>;
    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;
    arlAffiliationClassService = TestBed.inject(
      ArlAffiliationClassService,
    ) as jasmine.SpyObj<ArlAffiliationClassService>;
    compensationFundService = TestBed.inject(
      CompensationFundService,
    ) as jasmine.SpyObj<CompensationFundService>;
    socialSecurityContributionService = TestBed.inject(
      SocialSecurityContributionService,
    ) as jasmine.SpyObj<SocialSecurityContributionService>;

    fixture = TestBed.createComponent(
      MockMonthlyReportSocialSecurityInformationComponent,
    );
    component = fixture.componentInstance;
    component.contractorContractId = 1;
    component.report = {
      id: 1,
      contractorContractId: 1,
      reportNumber: 1,
      startDate: new Date(),
      endDate: new Date(),
      creationDate: new Date(),
    };

    contractService.getById.and.returnValue(of(mockContract));
    initialReportDocumentationService.getByContractorContractId.and.returnValue(
      of(mockInitialReportDocumentation),
    );
    arlAffiliationClassService.getAll.and.returnValue(
      of(mockArlAffiliationClasses),
    );
    compensationFundService.getAll.and.returnValue(of(mockCompensationFunds));
    socialSecurityContributionService.getByMonthlyReportId.and.returnValue(
      of(mockSocialSecurityContribution),
    );
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load initial data on init', fakeAsync(() => {
    fixture.detectChanges();
    tick();

    expect(contractService.getById).toHaveBeenCalledWith(1);
    expect(
      initialReportDocumentationService.getByContractorContractId,
    ).toHaveBeenCalledWith(1);
    expect(arlAffiliationClassService.getAll).toHaveBeenCalled();
    expect(compensationFundService.getAll).toHaveBeenCalled();
    expect(
      socialSecurityContributionService.getByMonthlyReportId,
    ).toHaveBeenCalledWith(1);

    expect(component.contract).toEqual(mockContract);
    expect(component.initialReportDocumentation).toEqual(
      mockInitialReportDocumentation,
    );
    expect(component.arlAffiliationClasses).toEqual(mockArlAffiliationClasses);
    expect(component.compensationFunds).toEqual(mockCompensationFunds);
  }));

  it('should handle errors when loading data', fakeAsync(() => {
    contractService.getById.and.returnValue(throwError(() => new Error()));
    initialReportDocumentationService.getByContractorContractId.and.returnValue(
      throwError(() => new Error()),
    );
    arlAffiliationClassService.getAll.and.returnValue(
      throwError(() => new Error()),
    );
    compensationFundService.getAll.and.returnValue(
      throwError(() => new Error()),
    );

    fixture.detectChanges();
    tick();

    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar la información del contrato',
    );
    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar la documentación inicial',
    );
    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar las clases de afiliación ARL',
    );
    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar las cajas de compensación',
    );
  }));

  it('should calculate contributions correctly', fakeAsync(() => {
    component.previousPaymentValue = 1000000;
    component.calculateContributions();

    expect(component.ibc).toBe(400000);
    expect(component.healthContribution).toBe(50000);
    expect(component.pensionContribution).toBe(64000);
  }));

  it('should handle file selection', () => {
    const mockFile = new File([''], 'test.pdf', { type: 'application/pdf' });
    const event = { target: { files: [mockFile] } } as unknown as Event;

    component.onFileSelected(event);

    expect(component.selectedFile).toBe(mockFile);
    expect(component.fileName).toBe('test.pdf');
  });

  it('should update compensation fund validation when checkbox changes', () => {
    component.isSupervisor = false;
    fixture.detectChanges();

    const hasCompensationFundControl = component.socialSecurityForm.get(
      'hasCompensationFund',
    );
    const compensationFundControl =
      component.socialSecurityForm.get('compensationFund');
    const compensationFundContributionControl =
      component.socialSecurityForm.get('compensationFundContribution');

    hasCompensationFundControl?.setValue(true);
    expect(
      compensationFundControl?.hasValidator(Validators.required),
    ).toBeTrue();
    expect(
      compensationFundContributionControl?.hasValidator(Validators.required),
    ).toBeTrue();

    hasCompensationFundControl?.setValue(false);
    expect(
      compensationFundControl?.hasValidator(Validators.required),
    ).toBeFalse();
    expect(
      compensationFundContributionControl?.hasValidator(Validators.required),
    ).toBeFalse();
  });

  it('should emit form validity changes', () => {
    spyOn(component.formValidityChange, 'emit');
    fixture.detectChanges();

    component.socialSecurityForm.patchValue({
      paymentFormNumber: '12345',
      arlAffiliationClass: 1,
      arlContribution: 5000,
    });

    expect(component.formValidityChange.emit).toHaveBeenCalled();
  });

  it('should download certificate', () => {
    const mockUrl = 'http://example.com/file.pdf';
    spyOn(window, 'open');

    component.socialSecurityContribution = {
      ...mockSocialSecurityContribution,
      certificateFileUrl: mockUrl,
    };

    component.downloadCertificate();

    expect(window.open).toHaveBeenCalledWith(mockUrl, '_blank');
  });

  it('should initialize form differently for supervisor', () => {
    component.isSupervisor = true;

    fixture = TestBed.createComponent(
      MockMonthlyReportSocialSecurityInformationComponent,
    );
    component = fixture.componentInstance;
    component.isSupervisor = true;
    component.contractorContractId = 1;
    component.report = {
      id: 1,
      contractorContractId: 1,
      reportNumber: 1,
      startDate: new Date(),
      endDate: new Date(),
      creationDate: new Date(),
    };

    fixture.detectChanges();

    expect(
      component.socialSecurityForm.get('hasCompensationFund')?.disabled,
    ).toBeTrue();
  });

  it('should apply validators differently based on existing certificate file', () => {
    component.isSupervisor = false;
    component.socialSecurityContribution = {
      ...mockSocialSecurityContribution,
      certificateFileUrl: undefined,
    };
    component.ngOnChanges({
      socialSecurityContribution: {
        currentValue: component.socialSecurityContribution,
        firstChange: true,
        previousValue: null,
        isFirstChange: () => true,
      },
    });

    const certificateFileControl =
      component.socialSecurityForm.get('certificateFile');
    expect(
      certificateFileControl?.hasValidator(Validators.required),
    ).toBeTrue();
  });

  it('should not require certificate file when supervisor', () => {
    component.isSupervisor = true;
    component.ngOnChanges({
      socialSecurityContribution: {
        currentValue: component.socialSecurityContribution,
        firstChange: true,
        previousValue: null,
        isFirstChange: () => true,
      },
    });

    const certificateFileControl =
      component.socialSecurityForm.get('certificateFile');
    expect(
      certificateFileControl?.hasValidator(Validators.required),
    ).toBeFalse();
  });

  it('should not require certificate file when file already exists', () => {
    component.isSupervisor = false;
    component.socialSecurityContribution = {
      ...mockSocialSecurityContribution,
      certificateFileUrl: 'http://example.com/file.pdf',
    };
    component.ngOnChanges({
      socialSecurityContribution: {
        currentValue: component.socialSecurityContribution,
        firstChange: true,
        previousValue: null,
        isFirstChange: () => true,
      },
    });

    const certificateFileControl =
      component.socialSecurityForm.get('certificateFile');
    expect(
      certificateFileControl?.hasValidator(Validators.required),
    ).toBeFalse();
  });

  it('should handle 404 error gracefully when loading initial report documentation', fakeAsync(() => {
    const error404 = { status: 404 };
    initialReportDocumentationService.getByContractorContractId.and.returnValue(
      throwError(() => error404),
    );

    component.loadInitialReportDocumentation();
    tick();

    expect(alertService.error).not.toHaveBeenCalledWith(
      'Error al cargar la documentación inicial',
    );
  }));

  it('should handle non-404 errors when loading initial report documentation', fakeAsync(() => {
    const error500 = { status: 500 };
    initialReportDocumentationService.getByContractorContractId.and.returnValue(
      throwError(() => error500),
    );

    component.loadInitialReportDocumentation();
    tick();

    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar la documentación inicial',
    );
  }));

  it('should emit true for form validity when isSupervisor is true, regardless of form validity', () => {
    component.isSupervisor = true;
    spyOn(component.formValidityChange, 'emit');
    fixture.detectChanges();

    component.socialSecurityForm.get('arlContribution')?.setValue(null);

    expect(component.formValidityChange.emit).toHaveBeenCalledWith(true);
  });

  it('should correctly prepare social security data with compensation fund', () => {
    component.isSupervisor = false;
    component.selectedFile = new File([''], 'test.pdf', {
      type: 'application/pdf',
    });
    component.socialSecurityForm.patchValue({
      paymentFormNumber: 123456,
      arlAffiliationClass: 1,
      arlContribution: 10000,
      hasCompensationFund: true,
      compensationFund: 1,
      compensationFundContribution: 5000,
    });

    const result = component.getSocialSecurityData();

    expect(result.paymentFormNumber).toBe(123456);
    expect(result.arlAffiliationClassId).toBe(1);
    expect(result.arlContribution).toBe(10000);
    expect(result.compensationFundId).toBe(1);
    expect(result.compensationFundContribution).toBe(5000);
  });

  it('should correctly prepare social security data without compensation fund', () => {
    component.isSupervisor = false;
    component.selectedFile = new File([''], 'test.pdf', {
      type: 'application/pdf',
    });
    component.socialSecurityForm.patchValue({
      paymentFormNumber: 123456,
      arlAffiliationClass: 1,
      arlContribution: 10000,
      hasCompensationFund: false,
    });

    const result = component.getSocialSecurityData();

    expect(result.paymentFormNumber).toBe(123456);
    expect(result.arlAffiliationClassId).toBe(1);
    expect(result.arlContribution).toBe(10000);
    expect(result.compensationFundId).toBeNull();
    expect(result.compensationFundContribution).toBeNull();
  });

  it('should emit social security data when saving', () => {
    component.isSupervisor = false;
    component.socialSecurityContribution = mockSocialSecurityContribution;
    component.socialSecurityForm.patchValue({
      paymentFormNumber: 123456,
      arlAffiliationClass: 1,
      arlContribution: 10000,
      hasCompensationFund: true,
      compensationFund: 1,
      compensationFundContribution: 5000,
    });
    spyOn(component.saveSocialSecurity, 'emit');

    component.onSave();

    expect(component.saveSocialSecurity.emit).toHaveBeenCalled();
  });

  it('should handle error when loading ARL affiliation classes', fakeAsync(() => {
    arlAffiliationClassService.getAll.and.returnValue(
      throwError(() => new Error()),
    );

    component.loadARLAffiliationClasses();
    tick();

    expect(alertService.error).toHaveBeenCalledWith(
      'Error al cargar las clases de afiliación ARL',
    );
  }));

  it('should not update compensation fund validation when isSupervisor is true', () => {
    component.isSupervisor = true;
    fixture.detectChanges();

    const compensationFundControl =
      component.socialSecurityForm.get('compensationFund');
    const compensationFundContributionControl =
      component.socialSecurityForm.get('compensationFundContribution');

    spyOn(compensationFundControl!, 'setValidators');
    spyOn(compensationFundContributionControl!, 'setValidators');

    component.socialSecurityForm.get('hasCompensationFund')?.setValue(true);

    expect(compensationFundControl!.setValidators).not.toHaveBeenCalled();
    expect(
      compensationFundContributionControl!.setValidators,
    ).not.toHaveBeenCalled();
  });
});
