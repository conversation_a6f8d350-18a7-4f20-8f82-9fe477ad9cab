import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { Group } from '@contract-management/models/group.model';
import { environment } from '@env';
import { GroupService } from './group.service';

describe('GroupService', () => {
  let service: GroupService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/groups`;

  const mockGroup: Group = {
    id: 1,
    name: 'Test Group',
    dependencyId: 1,
    dependency: {
      id: 1,
      name: 'Test Dependency',
    },
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [GroupService],
    });
    service = TestBed.inject(GroupService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all groups', () => {
      const mockGroups = [mockGroup];

      service.getAll().subscribe((groups) => {
        expect(groups).toEqual(mockGroups);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockGroups);
    });
  });

  describe('getById', () => {
    it('should return a group by id', () => {
      const id = 1;

      service.getById(id).subscribe((group) => {
        expect(group).toEqual(mockGroup);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockGroup);
    });
  });

  describe('create', () => {
    it('should create a new group', () => {
      const newGroup: Omit<Group, 'id'> = {
        name: 'New Group',
        dependencyId: 1,
        dependency: {
          id: 1,
          name: 'Test Dependency',
        },
      };

      service.create(newGroup).subscribe((group) => {
        expect(group).toEqual(mockGroup);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newGroup);
      req.flush(mockGroup);
    });
  });

  describe('update', () => {
    it('should update a group', () => {
      const id = 1;
      const updateData: Partial<Group> = {
        name: 'Updated Group',
      };

      service.update(id, updateData).subscribe((group) => {
        expect(group).toEqual(mockGroup);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockGroup);
    });
  });

  describe('delete', () => {
    it('should delete a group', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });

  describe('getAllByDependencyId', () => {
    it('should return all groups by dependency id', () => {
      const dependencyId = 1;
      const mockGroups = [mockGroup];

      service.getAllByDependencyId(dependencyId).subscribe((groups) => {
        expect(groups).toEqual(mockGroups);
      });

      const req = httpMock.expectOne(`${apiUrl}/dependency/${dependencyId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockGroups);
    });
  });

  describe('getByName', () => {
    it('should return a group by name', () => {
      const name = 'Test Group';

      service.getByName(name).subscribe((group) => {
        expect(group).toEqual(mockGroup);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockGroup);
    });
  });
});