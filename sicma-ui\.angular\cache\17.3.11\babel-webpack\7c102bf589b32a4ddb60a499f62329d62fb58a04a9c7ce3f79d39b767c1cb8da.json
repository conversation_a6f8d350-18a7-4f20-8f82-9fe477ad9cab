{"ast": null, "code": "import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { LayoutComponent } from './layout.component';\ndescribe('LayoutComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [LayoutComponent, HttpClientTestingModule, BrowserAnimationsModule]\n    });\n    fixture = TestBed.createComponent(LayoutComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "TestBed", "BrowserAnimationsModule", "LayoutComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "imports", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\core\\layout\\layout.component.spec.ts"], "sourcesContent": ["import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { ComponentFixture, TestBed } from '@angular/core/testing';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { LayoutComponent } from './layout.component';\n\ndescribe('LayoutComponent', () => {\n  let component: LayoutComponent;\n  let fixture: ComponentFixture<LayoutComponent>;\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [\n        LayoutComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n      ],\n    });\n    fixture = TestBed.createComponent(LayoutComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,8BAA8B;AACtE,SAA2BC,OAAO,QAAQ,uBAAuB;AACjE,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,eAAe,QAAQ,oBAAoB;AAEpDC,QAAQ,CAAC,iBAAiB,EAAE,MAAK;EAC/B,IAAIC,SAA0B;EAC9B,IAAIC,OAA0C;EAE9CC,UAAU,CAAC,MAAK;IACdN,OAAO,CAACO,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPN,eAAe,EACfH,uBAAuB,EACvBE,uBAAuB;KAE1B,CAAC;IACFI,OAAO,GAAGL,OAAO,CAACS,eAAe,CAACP,eAAe,CAAC;IAClDE,SAAS,GAAGC,OAAO,CAACK,iBAAiB;IACrCL,OAAO,CAACM,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACT,SAAS,CAAC,CAACU,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}