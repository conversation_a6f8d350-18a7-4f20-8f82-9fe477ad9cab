import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { TrackingType } from '@contract-management/models/tracking-type.model';
import { environment } from '@env';
import { TrackingTypeService } from './tracking-type.service';

describe('TrackingTypeService', () => {
  let service: TrackingTypeService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/tracking-types`;

  const mockTrackingType: TrackingType = {
    id: 1,
    name: 'Test Tracking Type',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [TrackingTypeService],
    });
    service = TestBed.inject(TrackingTypeService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all tracking types', () => {
      const mockTrackingTypes = [mockTrackingType];

      service.getAll().subscribe((trackingTypes) => {
        expect(trackingTypes).toEqual(mockTrackingTypes);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockTrackingTypes);
    });
  });

  describe('getById', () => {
    it('should return a tracking type by id', () => {
      const id = 1;

      service.getById(id).subscribe((trackingType) => {
        expect(trackingType).toEqual(mockTrackingType);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockTrackingType);
    });
  });

  describe('create', () => {
    it('should create a new tracking type', () => {
      const newTrackingType: Omit<TrackingType, 'id'> = {
        name: 'New Tracking Type',
      };

      service.create(newTrackingType).subscribe((trackingType) => {
        expect(trackingType).toEqual(mockTrackingType);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newTrackingType);
      req.flush(mockTrackingType);
    });
  });

  describe('update', () => {
    it('should update a tracking type', () => {
      const id = 1;
      const updateData: Partial<TrackingType> = {
        name: 'Updated Tracking Type',
      };

      service.update(id, updateData).subscribe((trackingType) => {
        expect(trackingType).toEqual(mockTrackingType);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockTrackingType);
    });
  });

  describe('delete', () => {
    it('should delete a tracking type', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });

  describe('getByName', () => {
    it('should return a tracking type by name', () => {
      const name = 'Test Tracking Type';

      service.getByName(name).subscribe((trackingType) => {
        expect(trackingType).toEqual(mockTrackingType);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockTrackingType);
    });
  });
});