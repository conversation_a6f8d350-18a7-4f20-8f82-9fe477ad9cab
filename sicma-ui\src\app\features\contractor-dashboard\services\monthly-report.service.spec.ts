import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { MonthlyReport } from '@contractor-dashboard/models/monthly-report.model';
import { MonthlyReview } from '@contractor-dashboard/models/monthly_review.model';
import { MonthlyReportSupervisorExportModel } from '@contractor-dashboard/models/monthly_report_supervisor_export.model';
import { environment } from '@env';
import { MonthlyReportService } from './monthly-report.service';

describe('MonthlyReportService', () => {
  let service: MonthlyReportService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/monthly-reports`;

  const mockMonthlyReport: MonthlyReport = {
    id: 1,
    reportNumber: 1,
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-01-31'),
    creationDate: new Date('2024-01-01'),
    contractorContractId: 1,
    currentReviewStatus: { id: 1, name: 'Pendiente de revisión' },
  };

  const mockSupervisorExportReport: MonthlyReportSupervisorExportModel = {
    report_id: 1,
    report_number: 1,
    start_date: '2024-01-01',
    end_date: '2024-01-31',
    total_value: 1000000,
    review_status_name: 'Pendiente de revisión',
  };

  const mockMonthlyReview: MonthlyReview = {
    contractId: 1,
    contractNumber: '123',
    year: 2024,
    supervisorName: 'John Doe',
    reportNumber: 1,
    totalValue: 1000,
    contractorName: 'Jane Doe',
    status: 'Pending',
    idReport: 1,
    startDate: '2024-01-01',
    endDate: '2024-01-31',
    groupName: 'Group 1',
    dependencyName: 'Dependency 1',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [MonthlyReportService],
    });
    service = TestBed.inject(MonthlyReportService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should get all monthly reports', () => {
    const mockReports = [mockMonthlyReport];

    service.getAll().subscribe((reports) => {
      expect(reports).toEqual(mockReports);
    });

    const req = httpMock.expectOne(apiUrl);
    expect(req.request.method).toBe('GET');
    req.flush(mockReports);
  });

  it('should handle error when getting all monthly reports', () => {
    service.getAll().subscribe({
      error: (error) => {
        expect(error.status).toBe(500);
      },
    });

    const req = httpMock.expectOne(apiUrl);
    req.flush('Error', { status: 500, statusText: 'Server Error' });
  });

  it('should get monthly report by id', () => {
    const id = 1;

    service.getById(id).subscribe((report) => {
      expect(report).toEqual(mockMonthlyReport);
    });

    const req = httpMock.expectOne(`${apiUrl}/${id}`);
    expect(req.request.method).toBe('GET');
    req.flush(mockMonthlyReport);
  });

  it('should handle error when getting monthly report by id', () => {
    const id = 999;

    service.getById(id).subscribe({
      error: (error) => {
        expect(error.status).toBe(404);
      },
    });

    const req = httpMock.expectOne(`${apiUrl}/${id}`);
    req.flush('Not found', { status: 404, statusText: 'Not Found' });
  });

  it('should get monthly reports by contractor contract id', () => {
    const contractorContractId = 1;
    const mockReports = [mockMonthlyReport];

    service
      .getByContractorContractId(contractorContractId)
      .subscribe((reports) => {
        expect(reports).toEqual(mockReports);
      });

    const req = httpMock.expectOne(
      `${apiUrl}/contractor-contract/${contractorContractId}`,
    );
    expect(req.request.method).toBe('GET');
    req.flush(mockReports);
  });

  it('should get monthly reports review', () => {
    const contractorContractId = 1;
    const mockReviews = [mockMonthlyReview];

    service
      .getMonthlyReportsRewiev(contractorContractId)
      .subscribe((reviews) => {
        expect(reviews).toEqual(mockReviews);
      });

    const req = httpMock.expectOne(
      `${apiUrl}/list-rewiev/${contractorContractId}`,
    );
    expect(req.request.method).toBe('GET');
    req.flush(mockReviews);
  });

  it('should create monthly report', () => {
    const newReport: Omit<MonthlyReport, 'id'> = {
      reportNumber: 1,
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-01-31'),
      creationDate: new Date('2024-01-01'),
      contractorContractId: 1,
    };

    service.create(newReport).subscribe((report) => {
      expect(report).toEqual(mockMonthlyReport);
    });

    const req = httpMock.expectOne(apiUrl);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(newReport);
    req.flush(mockMonthlyReport);
  });

  it('should handle error when creating monthly report', () => {
    const invalidReport = {} as Omit<MonthlyReport, 'id'>;

    service.create(invalidReport).subscribe({
      error: (error) => {
        expect(error.status).toBe(400);
      },
    });

    const req = httpMock.expectOne(apiUrl);
    req.flush('Invalid data', { status: 400, statusText: 'Bad Request' });
  });

  it('should update monthly report', () => {
    const id = 1;
    const updateData: Partial<MonthlyReport> = {
      reportNumber: 2,
    };

    service.update(id, updateData).subscribe((report) => {
      expect(report).toEqual({ ...mockMonthlyReport, ...updateData });
    });

    const req = httpMock.expectOne(`${apiUrl}/${id}`);
    expect(req.request.method).toBe('PUT');
    expect(req.request.body).toEqual(updateData);
    req.flush({ ...mockMonthlyReport, ...updateData });
  });

  it('should handle error when updating monthly report', () => {
    const id = 999;
    const updateData: Partial<MonthlyReport> = {
      reportNumber: 2,
    };

    service.update(id, updateData).subscribe({
      error: (error) => {
        expect(error.status).toBe(404);
      },
    });

    const req = httpMock.expectOne(`${apiUrl}/${id}`);
    req.flush('Not found', { status: 404, statusText: 'Not Found' });
  });

  it('should delete monthly report', () => {
    const id = 1;

    service.delete(id).subscribe(() => {
      expect().nothing();
    });

    const req = httpMock.expectOne(`${apiUrl}/${id}`);
    expect(req.request.method).toBe('DELETE');
    req.flush(null);
  });

  it('should handle error when deleting monthly report', () => {
    const id = 999;

    service.delete(id).subscribe({
      error: (error) => {
        expect(error.status).toBe(404);
      },
    });

    const req = httpMock.expectOne(`${apiUrl}/${id}`);
    req.flush('Not found', { status: 404, statusText: 'Not Found' });
  });

  it('should download PDF', () => {
    const id = 1;
    const mockBlob = new Blob(['test'], { type: 'application/pdf' });

    service.downloadPdf(id).subscribe((blob) => {
      expect(blob).toEqual(mockBlob);
    });

    const req = httpMock.expectOne(`${apiUrl}/${id}/pdf`);
    expect(req.request.method).toBe('GET');
    expect(req.request.responseType).toBe('blob');
    req.flush(mockBlob);
  });

  it('should handle error when downloading PDF', () => {
    const id = 999;

    service.downloadPdf(id).subscribe({
      error: (error) => {
        expect(error.status).toBe(404);
      },
    });

    const req = httpMock.expectOne(`${apiUrl}/${id}/pdf`);
    expect(req.request.method).toBe('GET');
    expect(req.request.responseType).toBe('blob');
    req.error(new ErrorEvent('Not Found'), { status: 404 });
  });

  it('should get monthly reports by supervisor email', () => {
    const supervisorEmail = '<EMAIL>';
    const mockReports = [mockSupervisorExportReport];

    service.getBySupervisorEmail(supervisorEmail).subscribe((reports) => {
      expect(reports).toEqual(mockReports);
    });

    const req = httpMock.expectOne(`${apiUrl}/supervisor/${supervisorEmail}`);
    expect(req.request.method).toBe('GET');
    req.flush(mockReports);
  });

  it('should get filtered reports by supervisor email', () => {
    const supervisorEmail = '<EMAIL>';
    const mockReports = [
      {
        ...mockSupervisorExportReport,
        review_status_name: 'Pendiente de revisión',
      },
      {
        ...mockSupervisorExportReport,
        report_id: 2,
        review_status_name: 'Aprobado',
      },
      {
        ...mockSupervisorExportReport,
        report_id: 3,
        review_status_name: 'Rechazado',
      },
      {
        ...mockSupervisorExportReport,
        report_id: 4,
        review_status_name: 'Otro estado',
      },
    ];

    service
      .getFilteredReportsBySupervisorEmail(supervisorEmail)
      .subscribe((reports) => {
        expect(reports.length).toBe(4);
        expect(
          reports.every((report) =>
            [
              'Pendiente de revisión',
              'Aprobado',
              'Rechazado',
              'Otro estado',
            ].includes(report.review_status_name || ''),
          ),
        ).toBeTrue();
      });

    const req = httpMock.expectOne(`${apiUrl}/supervisor/${supervisorEmail}`);
    expect(req.request.method).toBe('GET');
    req.flush(mockReports);
  });

  it('should handle error when getting reports by supervisor email', () => {
    const supervisorEmail = '<EMAIL>';

    service.getBySupervisorEmail(supervisorEmail).subscribe({
      error: (error) => {
        expect(error.status).toBe(404);
      },
    });

    const req = httpMock.expectOne(`${apiUrl}/supervisor/${supervisorEmail}`);
    req.flush('Not found', { status: 404, statusText: 'Not Found' });
  });
});