import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormControl, FormGroup } from '@angular/forms';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ActivatedRoute } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { AssociatedContractorsListComponent } from '@contract-management/components/associated-contractors-list/associated-contractors-list.component';
import { ContractDetailFormComponent } from '@contract-management/components/contract-detail-form/contract-detail-form.component';
import { Contract } from '@contract-management/models/contract.model';
import { ContractDetails } from '@contract-management/models/contract-details.model';
import { ContractService } from '@contract-management/services/contract.service';
import { AlertService } from '@shared/services/alert.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { of, throwError } from 'rxjs';
import { ContractDetailPageComponent } from './contract-detail-page.component';
import { ContractorDetailFormComponent } from '@contract-management/components/contractor-detail-form/contractor-detail-form.component';
import { NO_ERRORS_SCHEMA } from '@angular/core';

interface DialogResult {
  endDate: string;
}

describe('ContractDetailPageComponent', () => {
  let component: ContractDetailPageComponent;
  let fixture: ComponentFixture<ContractDetailPageComponent>;
  let contractServiceSpy: jasmine.SpyObj<ContractService>;
  let alertServiceSpy: jasmine.SpyObj<AlertService>;
  let spinnerServiceSpy: jasmine.SpyObj<NgxSpinnerService>;
  let dialogSpy: jasmine.SpyObj<MatDialog>;

  const mockContract: Contract = {
    id: 1,
    contractNumber: 123,
    object: 'Test Contract',
    rup: false,
    sigepLink: '',
    secopLink: '',
    addition: false,
    cession: false,
    settled: false,
    monthlyPayment: 1000,
    secopCode: 123,
    selectionModalityId: 1,
    trackingTypeId: 1,
    contractTypeId: 1,
    statusId: 1,
    dependencyId: 1,
    groupId: 1,
    municipalityId: 1,
    departmentId: 1,
    causesSelectionId: 1,
    managementSupportId: 1,
    contractClassId: 1,
    contractYearId: 2024,
    earlyTermination: false,
  };

  const mockContractorContract: ContractDetails = {
    id: 2,
    contractNumber: 123,
    contractorId: 3,
    fullName: 'Test Contractor',
    contractorIdNumber: 12345,
    object: 'Test Contract',
    rup: true,
    addition: false,
    cession: false,
    settled: false,
    hasCcp: false,
    selectionModalityName: 'Test Modality',
    trackingTypeName: 'Test Tracking',
    contractTypeName: 'Test Type',
    statusName: 'Active',
    dependencyName: 'Test Dependency',
    groupName: 'Test Group',
    contractorEmail: '<EMAIL>',
    monthlyPayment: 1000,
    supervisorFullName: 'Test Supervisor',
    supervisorIdNumber: '98765',
    supervisorPosition: 'Test Position',
  };

  beforeEach(async () => {
    contractServiceSpy = jasmine.createSpyObj('ContractService', [
      'getById',
      'getDetailsById',
    ]);
    alertServiceSpy = jasmine.createSpyObj('AlertService', [
      'error',
      'warning',
    ]);
    spinnerServiceSpy = jasmine.createSpyObj('NgxSpinnerService', [
      'show',
      'hide',
    ]);
    dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);

    await TestBed.configureTestingModule({
      imports: [
        ContractDetailPageComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        RouterTestingModule,
        MatDialogModule,
      ],
      providers: [
        provideNativeDateAdapter(),
        {
          provide: ActivatedRoute,
          useValue: { params: of({ id: '1' }) },
        },
        { provide: ContractService, useValue: contractServiceSpy },
        { provide: AlertService, useValue: alertServiceSpy },
        { provide: NgxSpinnerService, useValue: spinnerServiceSpy },
        { provide: MatDialog, useValue: dialogSpy },
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();

    fixture = TestBed.createComponent(ContractDetailPageComponent);
    component = fixture.componentInstance;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ContractDetailPageComponent);
    component = fixture.componentInstance;
    contractServiceSpy.getById.and.returnValue(of(mockContract));
    contractServiceSpy.getDetailsById.and.returnValue(
      of(mockContractorContract),
    );
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load contract on init', () => {
    expect(spinnerServiceSpy.show).toHaveBeenCalled();
    expect(contractServiceSpy.getById).toHaveBeenCalledWith(1);
    expect(component.contract).toEqual(mockContract);
    expect(spinnerServiceSpy.hide).toHaveBeenCalled();
  });

  it('should show error if contract fails to load', () => {
    contractServiceSpy.getById.and.returnValue(
      throwError(() => new Error('Failed to load')),
    );
    component.ngOnInit();
    expect(alertServiceSpy.error).toHaveBeenCalledWith(
      'Error al cargar el contrato',
    );
    expect(spinnerServiceSpy.hide).toHaveBeenCalled();
  });

  describe('openEarlyTerminationDialog', () => {
    it('should show warning if no contracts or no contractors associated', () => {

      component.contract = null;
      component.openEarlyTerminationDialog();
      expect(alertServiceSpy.warning).not.toHaveBeenCalled();
      expect(dialogSpy.open).not.toHaveBeenCalled();

      component.contract = mockContract;
      component.AssociatedContractorsListComponent =
        undefined as unknown as AssociatedContractorsListComponent;
      component.openEarlyTerminationDialog();
      expect(alertServiceSpy.warning).not.toHaveBeenCalled();
      expect(dialogSpy.open).not.toHaveBeenCalled();

      component.contract = mockContract;
      component.AssociatedContractorsListComponent = {
        getLatestContractorContract: () => null,
      } as unknown as AssociatedContractorsListComponent;
      component.openEarlyTerminationDialog();
      expect(alertServiceSpy.warning).toHaveBeenCalledWith(
        'No hay contratistas asociados a este contrato',
      );
    });
  });

  describe('dialog interactions', () => {
    it('should handle dialog result correctly', () => {

      component.contract = mockContract;

      component.AssociatedContractorsListComponent = {
        getLatestContractorContract: () => mockContractorContract,
        updateContractorContracts: jasmine.createSpy(
          'updateContractorContracts',
        ),
      } as unknown as AssociatedContractorsListComponent;

      const earlyTerminationControl = new FormControl(true);
      component.contractDetailForm = {
        contractForm: new FormGroup({
          earlyTermination: earlyTerminationControl,
        }),
      } as ContractDetailFormComponent;

      const dialogResult: DialogResult = { endDate: '2023-12-31' };

      const handleDialogResult = (result: DialogResult | null) => {
        if (result) {
          if (component.contractDetailForm) {
            component.contractDetailForm.contractForm
              .get('earlyTermination')
              ?.disable();
          }
          if (
            component.AssociatedContractorsListComponent &&
            mockContractorContract.id
          ) {
            component.AssociatedContractorsListComponent.updateContractorContracts(
              result.endDate,
              mockContractorContract.id,
            );
          }
        } else {
          if (component.contractDetailForm) {
            component.contractDetailForm.contractForm
              .get('earlyTermination')
              ?.setValue(false);
          }
        }
      };

      handleDialogResult(dialogResult);
      expect(earlyTerminationControl.disabled).toBeTrue();
      expect(
        component.AssociatedContractorsListComponent.updateContractorContracts,
      ).toHaveBeenCalledWith('2023-12-31', mockContractorContract.id);

      earlyTerminationControl.enable();
      earlyTerminationControl.setValue(true);

      handleDialogResult(null);
      expect(earlyTerminationControl.value).toBeFalse();
      expect(earlyTerminationControl.enabled).toBeTrue();
    });
  });

  describe('onContractorContractsChanged', () => {
    it('should update contract cession property', () => {
      component.contract = { ...mockContract, cession: false };
      component.onContractorContractsChanged();
      expect(component.contract.cession).toBeTrue();
    });

    it('should not throw error if contract is null', () => {
      component.contract = null;
      expect(() => component.onContractorContractsChanged()).not.toThrow();
    });

    it('should update contract form with cession value', () => {
      component.contract = mockContract;
      component.contractDetailForm = {
        contractForm: new FormGroup({
          cession: new FormControl(false),
        }),
      } as ContractDetailFormComponent;

      component.onContractorContractsChanged();

      expect(
        component.contractDetailForm.contractForm.get('cession')?.value,
      ).toBeTrue();
    });

    it('should not throw error if contractDetailForm is null', () => {
      component.contract = mockContract;
      component.contractDetailForm =
        undefined as unknown as ContractDetailFormComponent;
      expect(() => component.onContractorContractsChanged()).not.toThrow();
    });
  });

  describe('uncheckEarlyTermination', () => {
    it('should uncheck and enable early termination', () => {
      const earlyTerminationControl = new FormControl(true);
      component.contractDetailForm = {
        contractForm: new FormGroup({
          earlyTermination: earlyTerminationControl,
        }),
      } as ContractDetailFormComponent;

      component.uncheckEarlyTermination();

      expect(earlyTerminationControl.value).toBeFalse();
      expect(earlyTerminationControl.enabled).toBeTrue();
    });

    it('should not throw error if contractDetailForm is null', () => {
      component.contractDetailForm =
        undefined as unknown as ContractDetailFormComponent;
      expect(() => component.uncheckEarlyTermination()).not.toThrow();
    });

    it('should not throw error if earlyTermination control is missing', () => {
      component.contractDetailForm = {
        contractForm: new FormGroup({}),
      } as ContractDetailFormComponent;
      expect(() => component.uncheckEarlyTermination()).not.toThrow();
    });
  });

  describe('ViewChild components', () => {
    it('should set ViewChild components after view initialization', () => {

      const mockAssociatedContractorsListComponent =
        {} as AssociatedContractorsListComponent;
      const mockContractDetailForm = {} as ContractDetailFormComponent;
      const mockContractorDetailForm = {} as ContractorDetailFormComponent;

      component.AssociatedContractorsListComponent =
        mockAssociatedContractorsListComponent;
      component.contractDetailForm = mockContractDetailForm;
      component.contractorDetailForm = mockContractorDetailForm;

      expect(component.AssociatedContractorsListComponent).toBe(
        mockAssociatedContractorsListComponent,
      );
      expect(component.contractDetailForm).toBe(mockContractDetailForm);
      expect(component.contractorDetailForm).toBe(mockContractorDetailForm);
    });
  });
});