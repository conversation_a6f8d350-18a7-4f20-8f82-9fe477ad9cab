{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/Github/SICMA/sicma-ui/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { DatePipe } from '@angular/common';\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { TestBed, discardPeriodicTasks, fakeAsync, tick } from '@angular/core/testing';\nimport { FormBuilder, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { provideNativeDateAdapter } from '@angular/material/core';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { ContractValuesService } from '@contract-management/services/contract-values.service';\nimport { SuspensionService } from '@contract-management/services/suspension.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { of, throwError } from 'rxjs';\nimport { SuspensionDialogComponent } from './suspension-dialog.component';\ndescribe('SuspensionDialogComponent', () => {\n  let component;\n  let fixture;\n  let dialogRef;\n  let contractValuesService;\n  let suspensionService;\n  let alertService;\n  let spinnerService;\n  const mockDialogData = {\n    contractId: 1,\n    lastSuspensionEndDate: null\n  };\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    dialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);\n    contractValuesService = jasmine.createSpyObj('ContractValuesService', ['getLatestEndDateByContractId', 'getStartDateByContractId']);\n    suspensionService = jasmine.createSpyObj('SuspensionService', ['create', 'update']);\n    alertService = jasmine.createSpyObj('AlertService', ['success', 'error']);\n    spinnerService = jasmine.createSpyObj('NgxSpinnerService', ['show', 'hide']);\n    contractValuesService.getLatestEndDateByContractId.and.returnValue(of(new Date('2024-12-31')));\n    contractValuesService.getStartDateByContractId.and.returnValue(of(new Date('2024-01-01')));\n    yield TestBed.configureTestingModule({\n      imports: [SuspensionDialogComponent, HttpClientTestingModule, BrowserAnimationsModule, ReactiveFormsModule, MatDialogModule, MatButtonModule, MatFormFieldModule, MatInputModule, MatIconModule, MatDatepickerModule, DatePipe],\n      providers: [FormBuilder, {\n        provide: MatDialogRef,\n        useValue: dialogRef\n      }, {\n        provide: MAT_DIALOG_DATA,\n        useValue: mockDialogData\n      }, {\n        provide: ContractValuesService,\n        useValue: contractValuesService\n      }, {\n        provide: SuspensionService,\n        useValue: suspensionService\n      }, {\n        provide: AlertService,\n        useValue: alertService\n      }, {\n        provide: NgxSpinnerService,\n        useValue: spinnerService\n      }, provideNativeDateAdapter()]\n    }).compileComponents();\n    fixture = TestBed.createComponent(SuspensionDialogComponent);\n    component = fixture.componentInstance;\n  }));\n  it('should load contract dates on init', fakeAsync(() => {\n    component.ngOnInit();\n    tick();\n    fixture.detectChanges();\n    expect(contractValuesService.getLatestEndDateByContractId).toHaveBeenCalledWith(1);\n    expect(contractValuesService.getStartDateByContractId).toHaveBeenCalledWith(1);\n    expect(component.contractEndDate).toEqual(new Date('2024-12-31'));\n    expect(component.contractStartDate).toEqual(new Date('2024-01-01'));\n  }));\n  it('should handle error when loading contract dates', fakeAsync(() => {\n    contractValuesService.getLatestEndDateByContractId.and.returnValue(throwError(() => new Error()));\n    contractValuesService.getStartDateByContractId.and.returnValue(throwError(() => new Error()));\n    component.ngOnInit();\n    tick();\n    fixture.detectChanges();\n    expect(alertService.error).toHaveBeenCalledWith('Error al obtener las fechas del contrato');\n  }));\n  it('should validate form fields', fakeAsync(() => {\n    component.ngOnInit();\n    tick();\n    fixture.detectChanges();\n    expect(component.suspensionForm.valid).toBeFalse();\n    component.suspensionForm.patchValue({\n      startDate: new Date('2024-01-01'),\n      endDate: new Date('2024-01-31'),\n      reason: 'Test reason that is long enough'\n    });\n    tick();\n    fixture.detectChanges();\n    tick();\n    fixture.detectChanges();\n    expect(component.suspensionForm.valid).toBeTrue();\n  }));\n  it('should handle error when creating suspension', fakeAsync(() => {\n    suspensionService.create.and.returnValue(throwError(() => new Error()));\n    component.ngOnInit();\n    tick();\n    fixture.detectChanges();\n    component.suspensionForm.patchValue({\n      startDate: new Date('2024-02-20'),\n      endDate: new Date('2024-03-20'),\n      reason: 'New suspension reason'\n    });\n    component.updateSuspensionDays();\n    tick();\n    fixture.detectChanges();\n    component.onSubmit();\n    tick();\n    expect(alertService.error).toHaveBeenCalledWith('Error al crear la suspensión');\n    expect(spinnerService.hide).toHaveBeenCalled();\n  }));\n  afterEach(fakeAsync(() => {\n    tick();\n    discardPeriodicTasks();\n  }));\n});", "map": {"version": 3, "names": ["DatePipe", "HttpClientTestingModule", "TestBed", "discardPeriodicTasks", "fakeAsync", "tick", "FormBuilder", "ReactiveFormsModule", "MatButtonModule", "provideNativeDateAdapter", "MatDatepickerModule", "MAT_DIALOG_DATA", "MatDialogModule", "MatDialogRef", "MatFormFieldModule", "MatIconModule", "MatInputModule", "BrowserAnimationsModule", "ContractValuesService", "SuspensionService", "AlertService", "NgxSpinnerService", "of", "throwError", "SuspensionDialogComponent", "describe", "component", "fixture", "dialogRef", "contractValuesService", "suspensionService", "alertService", "spinnerService", "mockDialogData", "contractId", "lastSuspensionEndDate", "beforeEach", "_asyncToGenerator", "jasmine", "createSpyObj", "getLatestEndDateByContractId", "and", "returnValue", "Date", "getStartDateByContractId", "configureTestingModule", "imports", "providers", "provide", "useValue", "compileComponents", "createComponent", "componentInstance", "it", "ngOnInit", "detectChanges", "expect", "toHaveBeenCalledWith", "contractEndDate", "toEqual", "contractStartDate", "Error", "error", "suspensionForm", "valid", "toBeFalse", "patchValue", "startDate", "endDate", "reason", "toBeTrue", "create", "updateSuspensionDays", "onSubmit", "hide", "toHaveBeenCalled", "after<PERSON>ach"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\components\\suspensions-list\\suspension-dialog\\suspension-dialog.component.spec.ts"], "sourcesContent": ["import { DatePipe } from '@angular/common';\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport {\n  ComponentFixture,\n  TestBed,\n  discardPeriodicTasks,\n  fakeAsync,\n  tick,\n} from '@angular/core/testing';\nimport { FormBuilder, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { provideNativeDateAdapter } from '@angular/material/core';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport {\n  MAT_DIALOG_DATA,\n  MatDialogModule,\n  MatDialogRef,\n} from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { ContractValuesService } from '@contract-management/services/contract-values.service';\nimport { SuspensionService } from '@contract-management/services/suspension.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { of, throwError } from 'rxjs';\nimport { SuspensionDialogComponent } from './suspension-dialog.component';\n\ndescribe('SuspensionDialogComponent', () => {\n  let component: SuspensionDialogComponent;\n  let fixture: ComponentFixture<SuspensionDialogComponent>;\n  let dialogRef: jasmine.SpyObj<MatDialogRef<SuspensionDialogComponent>>;\n  let contractValuesService: jasmine.SpyObj<ContractValuesService>;\n  let suspensionService: jasmine.SpyObj<SuspensionService>;\n  let alertService: jasmine.SpyObj<AlertService>;\n  let spinnerService: jasmine.SpyObj<NgxSpinnerService>;\n\n  const mockDialogData = {\n    contractId: 1,\n    lastSuspensionEndDate: null,\n  };\n\n  beforeEach(async () => {\n    dialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);\n    contractValuesService = jasmine.createSpyObj('ContractValuesService', [\n      'getLatestEndDateByContractId',\n      'getStartDateByContractId',\n    ]);\n    suspensionService = jasmine.createSpyObj('SuspensionService', [\n      'create',\n      'update',\n    ]);\n    alertService = jasmine.createSpyObj('AlertService', ['success', 'error']);\n    spinnerService = jasmine.createSpyObj('NgxSpinnerService', [\n      'show',\n      'hide',\n    ]);\n\n    contractValuesService.getLatestEndDateByContractId.and.returnValue(\n      of(new Date('2024-12-31')),\n    );\n    contractValuesService.getStartDateByContractId.and.returnValue(\n      of(new Date('2024-01-01')),\n    );\n\n    await TestBed.configureTestingModule({\n      imports: [\n        SuspensionDialogComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n        ReactiveFormsModule,\n        MatDialogModule,\n        MatButtonModule,\n        MatFormFieldModule,\n        MatInputModule,\n        MatIconModule,\n        MatDatepickerModule,\n        DatePipe,\n      ],\n      providers: [\n        FormBuilder,\n        { provide: MatDialogRef, useValue: dialogRef },\n        { provide: MAT_DIALOG_DATA, useValue: mockDialogData },\n        { provide: ContractValuesService, useValue: contractValuesService },\n        { provide: SuspensionService, useValue: suspensionService },\n        { provide: AlertService, useValue: alertService },\n        { provide: NgxSpinnerService, useValue: spinnerService },\n        provideNativeDateAdapter(),\n      ],\n    }).compileComponents();\n\n    fixture = TestBed.createComponent(SuspensionDialogComponent);\n    component = fixture.componentInstance;\n  });\n\n  it('should load contract dates on init', fakeAsync(() => {\n    component.ngOnInit();\n    tick();\n    fixture.detectChanges();\n\n    expect(\n      contractValuesService.getLatestEndDateByContractId,\n    ).toHaveBeenCalledWith(1);\n    expect(contractValuesService.getStartDateByContractId).toHaveBeenCalledWith(\n      1,\n    );\n    expect(component.contractEndDate).toEqual(new Date('2024-12-31'));\n    expect(component.contractStartDate).toEqual(new Date('2024-01-01'));\n  }));\n\n  it('should handle error when loading contract dates', fakeAsync(() => {\n    contractValuesService.getLatestEndDateByContractId.and.returnValue(\n      throwError(() => new Error()),\n    );\n    contractValuesService.getStartDateByContractId.and.returnValue(\n      throwError(() => new Error()),\n    );\n\n    component.ngOnInit();\n    tick();\n    fixture.detectChanges();\n\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al obtener las fechas del contrato',\n    );\n  }));\n\n  it('should validate form fields', fakeAsync(() => {\n    component.ngOnInit();\n    tick();\n    fixture.detectChanges();\n\n    expect(component.suspensionForm.valid).toBeFalse();\n\n    component.suspensionForm.patchValue({\n      startDate: new Date('2024-01-01'),\n      endDate: new Date('2024-01-31'),\n      reason: 'Test reason that is long enough',\n    });\n\n    tick();\n    fixture.detectChanges();\n    tick();\n    fixture.detectChanges();\n\n    expect(component.suspensionForm.valid).toBeTrue();\n  }));\n\n  it('should handle error when creating suspension', fakeAsync(() => {\n    suspensionService.create.and.returnValue(throwError(() => new Error()));\n\n    component.ngOnInit();\n    tick();\n    fixture.detectChanges();\n\n    component.suspensionForm.patchValue({\n      startDate: new Date('2024-02-20'),\n      endDate: new Date('2024-03-20'),\n      reason: 'New suspension reason',\n    });\n\n    component.updateSuspensionDays();\n    tick();\n    fixture.detectChanges();\n\n    component.onSubmit();\n    tick();\n\n    expect(alertService.error).toHaveBeenCalledWith(\n      'Error al crear la suspensión',\n    );\n    expect(spinnerService.hide).toHaveBeenCalled();\n  }));\n\n  afterEach(fakeAsync(() => {\n    tick();\n    discardPeriodicTasks();\n  }));\n});"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAEEC,OAAO,EACPC,oBAAoB,EACpBC,SAAS,EACTC,IAAI,QACC,uBAAuB;AAC9B,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,wBAAwB,QAAQ,wBAAwB;AACjE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SACEC,eAAe,EACfC,eAAe,EACfC,YAAY,QACP,0BAA0B;AACjC,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,qBAAqB,QAAQ,uDAAuD;AAC7F,SAASC,iBAAiB,QAAQ,kDAAkD;AACpF,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,iBAAiB,QAAQ,aAAa;AAC/C,SAASC,EAAE,EAAEC,UAAU,QAAQ,MAAM;AACrC,SAASC,yBAAyB,QAAQ,+BAA+B;AAEzEC,QAAQ,CAAC,2BAA2B,EAAE,MAAK;EACzC,IAAIC,SAAoC;EACxC,IAAIC,OAAoD;EACxD,IAAIC,SAAkE;EACtE,IAAIC,qBAA4D;EAChE,IAAIC,iBAAoD;EACxD,IAAIC,YAA0C;EAC9C,IAAIC,cAAiD;EAErD,MAAMC,cAAc,GAAG;IACrBC,UAAU,EAAE,CAAC;IACbC,qBAAqB,EAAE;GACxB;EAEDC,UAAU,eAAAC,iBAAA,CAAC,aAAW;IACpBT,SAAS,GAAGU,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC;IAC3DV,qBAAqB,GAAGS,OAAO,CAACC,YAAY,CAAC,uBAAuB,EAAE,CACpE,8BAA8B,EAC9B,0BAA0B,CAC3B,CAAC;IACFT,iBAAiB,GAAGQ,OAAO,CAACC,YAAY,CAAC,mBAAmB,EAAE,CAC5D,QAAQ,EACR,QAAQ,CACT,CAAC;IACFR,YAAY,GAAGO,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACzEP,cAAc,GAAGM,OAAO,CAACC,YAAY,CAAC,mBAAmB,EAAE,CACzD,MAAM,EACN,MAAM,CACP,CAAC;IAEFV,qBAAqB,CAACW,4BAA4B,CAACC,GAAG,CAACC,WAAW,CAChEpB,EAAE,CAAC,IAAIqB,IAAI,CAAC,YAAY,CAAC,CAAC,CAC3B;IACDd,qBAAqB,CAACe,wBAAwB,CAACH,GAAG,CAACC,WAAW,CAC5DpB,EAAE,CAAC,IAAIqB,IAAI,CAAC,YAAY,CAAC,CAAC,CAC3B;IAED,MAAMzC,OAAO,CAAC2C,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CACPtB,yBAAyB,EACzBvB,uBAAuB,EACvBgB,uBAAuB,EACvBV,mBAAmB,EACnBK,eAAe,EACfJ,eAAe,EACfM,kBAAkB,EAClBE,cAAc,EACdD,aAAa,EACbL,mBAAmB,EACnBV,QAAQ,CACT;MACD+C,SAAS,EAAE,CACTzC,WAAW,EACX;QAAE0C,OAAO,EAAEnC,YAAY;QAAEoC,QAAQ,EAAErB;MAAS,CAAE,EAC9C;QAAEoB,OAAO,EAAErC,eAAe;QAAEsC,QAAQ,EAAEhB;MAAc,CAAE,EACtD;QAAEe,OAAO,EAAE9B,qBAAqB;QAAE+B,QAAQ,EAAEpB;MAAqB,CAAE,EACnE;QAAEmB,OAAO,EAAE7B,iBAAiB;QAAE8B,QAAQ,EAAEnB;MAAiB,CAAE,EAC3D;QAAEkB,OAAO,EAAE5B,YAAY;QAAE6B,QAAQ,EAAElB;MAAY,CAAE,EACjD;QAAEiB,OAAO,EAAE3B,iBAAiB;QAAE4B,QAAQ,EAAEjB;MAAc,CAAE,EACxDvB,wBAAwB,EAAE;KAE7B,CAAC,CAACyC,iBAAiB,EAAE;IAEtBvB,OAAO,GAAGzB,OAAO,CAACiD,eAAe,CAAC3B,yBAAyB,CAAC;IAC5DE,SAAS,GAAGC,OAAO,CAACyB,iBAAiB;EACvC,CAAC,EAAC;EAEFC,EAAE,CAAC,oCAAoC,EAAEjD,SAAS,CAAC,MAAK;IACtDsB,SAAS,CAAC4B,QAAQ,EAAE;IACpBjD,IAAI,EAAE;IACNsB,OAAO,CAAC4B,aAAa,EAAE;IAEvBC,MAAM,CACJ3B,qBAAqB,CAACW,4BAA4B,CACnD,CAACiB,oBAAoB,CAAC,CAAC,CAAC;IACzBD,MAAM,CAAC3B,qBAAqB,CAACe,wBAAwB,CAAC,CAACa,oBAAoB,CACzE,CAAC,CACF;IACDD,MAAM,CAAC9B,SAAS,CAACgC,eAAe,CAAC,CAACC,OAAO,CAAC,IAAIhB,IAAI,CAAC,YAAY,CAAC,CAAC;IACjEa,MAAM,CAAC9B,SAAS,CAACkC,iBAAiB,CAAC,CAACD,OAAO,CAAC,IAAIhB,IAAI,CAAC,YAAY,CAAC,CAAC;EACrE,CAAC,CAAC,CAAC;EAEHU,EAAE,CAAC,iDAAiD,EAAEjD,SAAS,CAAC,MAAK;IACnEyB,qBAAqB,CAACW,4BAA4B,CAACC,GAAG,CAACC,WAAW,CAChEnB,UAAU,CAAC,MAAM,IAAIsC,KAAK,EAAE,CAAC,CAC9B;IACDhC,qBAAqB,CAACe,wBAAwB,CAACH,GAAG,CAACC,WAAW,CAC5DnB,UAAU,CAAC,MAAM,IAAIsC,KAAK,EAAE,CAAC,CAC9B;IAEDnC,SAAS,CAAC4B,QAAQ,EAAE;IACpBjD,IAAI,EAAE;IACNsB,OAAO,CAAC4B,aAAa,EAAE;IAEvBC,MAAM,CAACzB,YAAY,CAAC+B,KAAK,CAAC,CAACL,oBAAoB,CAC7C,0CAA0C,CAC3C;EACH,CAAC,CAAC,CAAC;EAEHJ,EAAE,CAAC,6BAA6B,EAAEjD,SAAS,CAAC,MAAK;IAC/CsB,SAAS,CAAC4B,QAAQ,EAAE;IACpBjD,IAAI,EAAE;IACNsB,OAAO,CAAC4B,aAAa,EAAE;IAEvBC,MAAM,CAAC9B,SAAS,CAACqC,cAAc,CAACC,KAAK,CAAC,CAACC,SAAS,EAAE;IAElDvC,SAAS,CAACqC,cAAc,CAACG,UAAU,CAAC;MAClCC,SAAS,EAAE,IAAIxB,IAAI,CAAC,YAAY,CAAC;MACjCyB,OAAO,EAAE,IAAIzB,IAAI,CAAC,YAAY,CAAC;MAC/B0B,MAAM,EAAE;KACT,CAAC;IAEFhE,IAAI,EAAE;IACNsB,OAAO,CAAC4B,aAAa,EAAE;IACvBlD,IAAI,EAAE;IACNsB,OAAO,CAAC4B,aAAa,EAAE;IAEvBC,MAAM,CAAC9B,SAAS,CAACqC,cAAc,CAACC,KAAK,CAAC,CAACM,QAAQ,EAAE;EACnD,CAAC,CAAC,CAAC;EAEHjB,EAAE,CAAC,8CAA8C,EAAEjD,SAAS,CAAC,MAAK;IAChE0B,iBAAiB,CAACyC,MAAM,CAAC9B,GAAG,CAACC,WAAW,CAACnB,UAAU,CAAC,MAAM,IAAIsC,KAAK,EAAE,CAAC,CAAC;IAEvEnC,SAAS,CAAC4B,QAAQ,EAAE;IACpBjD,IAAI,EAAE;IACNsB,OAAO,CAAC4B,aAAa,EAAE;IAEvB7B,SAAS,CAACqC,cAAc,CAACG,UAAU,CAAC;MAClCC,SAAS,EAAE,IAAIxB,IAAI,CAAC,YAAY,CAAC;MACjCyB,OAAO,EAAE,IAAIzB,IAAI,CAAC,YAAY,CAAC;MAC/B0B,MAAM,EAAE;KACT,CAAC;IAEF3C,SAAS,CAAC8C,oBAAoB,EAAE;IAChCnE,IAAI,EAAE;IACNsB,OAAO,CAAC4B,aAAa,EAAE;IAEvB7B,SAAS,CAAC+C,QAAQ,EAAE;IACpBpE,IAAI,EAAE;IAENmD,MAAM,CAACzB,YAAY,CAAC+B,KAAK,CAAC,CAACL,oBAAoB,CAC7C,8BAA8B,CAC/B;IACDD,MAAM,CAACxB,cAAc,CAAC0C,IAAI,CAAC,CAACC,gBAAgB,EAAE;EAChD,CAAC,CAAC,CAAC;EAEHC,SAAS,CAACxE,SAAS,CAAC,MAAK;IACvBC,IAAI,EAAE;IACNF,oBAAoB,EAAE;EACxB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}