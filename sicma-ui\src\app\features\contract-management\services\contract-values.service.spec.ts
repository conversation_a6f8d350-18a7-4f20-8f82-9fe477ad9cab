import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { ContractValues } from '@contract-management/models/contract-values.model';
import { environment } from '@env';
import { ContractValuesService } from './contract-values.service';
import { AuthService } from '@core/auth/services/auth.service';
import { ContractAuditHistoryService } from './contract-audit-history.service';
import { ContractAuditStatusService } from './contract-audit-status.service';
import { of } from 'rxjs';
import { User } from '@core/auth/models/user.model';
import { ContractAuditStatus } from '../models/contract-audit-status.model';
import { ContractAuditHistory } from '../models/contract-audit-history.model';

describe('ContractValuesService', () => {
  let service: ContractValuesService;
  let httpMock: HttpTestingController;
  let authServiceSpy: jasmine.SpyObj<AuthService>;
  let contractAuditHistoryServiceSpy: jasmine.SpyObj<ContractAuditHistoryService>;
  let contractAuditStatusServiceSpy: jasmine.SpyObj<ContractAuditStatusService>;
  const apiUrl = `${environment.apiUrl}/contract-values`;

  const mockContractValues: ContractValues = {
    id: 1,
    contractId: 1,
    numericValue: 1000000,
    startDate: '2023-01-01',
    endDate: '2023-12-31',
    subscriptionDate: '2023-01-01',
    cdp: 12345,
    cdpEntityId: 1,
    madsValue: 0,
    isOtherEntity: false,
    cdpEntity: { id: 1, name: 'Test CDP Entity' },
  };

  const mockUser: User = {
    id: 1,
    username: 'testuser',
    profiles: [],
  };

  const mockAuditStatus: ContractAuditStatus = {
    id: 1,
    name: 'Adición de valores',
    description: 'Contract values creation status',
  };

  beforeEach(() => {
    authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);
    contractAuditHistoryServiceSpy = jasmine.createSpyObj(
      'ContractAuditHistoryService',
      ['create'],
    );
    contractAuditStatusServiceSpy = jasmine.createSpyObj(
      'ContractAuditStatusService',
      ['getByName'],
    );

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        ContractValuesService,
        { provide: AuthService, useValue: authServiceSpy },
        {
          provide: ContractAuditHistoryService,
          useValue: contractAuditHistoryServiceSpy,
        },
        {
          provide: ContractAuditStatusService,
          useValue: contractAuditStatusServiceSpy,
        },
      ],
    });
    service = TestBed.inject(ContractValuesService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all contract values', () => {
      const mockValues: ContractValues[] = [mockContractValues];

      service.getAll().subscribe((values) => {
        expect(values).toEqual(mockValues);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockValues);
    });
  });

  describe('getById', () => {
    it('should return a single contract values by id', () => {
      service.getById(1).subscribe((values) => {
        expect(values).toEqual(mockContractValues);
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('GET');
      req.flush(mockContractValues);
    });
  });

  describe('getAllByContractId', () => {
    it('should return contract values by contract id', () => {
      const contractId = 1;
      const mockValues: ContractValues[] = [mockContractValues];

      service.getAllByContractId(contractId).subscribe((values) => {
        expect(values).toEqual(mockValues);
      });

      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockValues);
    });
  });

  describe('getStartDateByContractId', () => {
    it('should return start date by contract id', () => {
      const contractId = 1;
      const startDate = new Date('2023-01-01');

      service.getStartDateByContractId(contractId).subscribe((date) => {
        expect(date).toEqual(startDate);
      });

      const req = httpMock.expectOne(
        `${apiUrl}/contract/${contractId}/start-date`,
      );
      expect(req.request.method).toBe('GET');
      req.flush(startDate);
    });

    it('should handle null date', () => {
      const contractId = 1;

      service.getStartDateByContractId(contractId).subscribe((date) => {
        expect(date).toBeNull();
      });

      const req = httpMock.expectOne(
        `${apiUrl}/contract/${contractId}/start-date`,
      );
      expect(req.request.method).toBe('GET');
      req.flush(null);
    });
  });

  describe('getLatestEndDateByContractId', () => {
    it('should return latest end date by contract id', () => {
      const contractId = 1;
      const endDate = new Date('2023-12-31');

      service.getLatestEndDateByContractId(contractId).subscribe((date) => {
        expect(date).toEqual(endDate);
      });

      const req = httpMock.expectOne(
        `${apiUrl}/contract/${contractId}/latest-end-date`,
      );
      expect(req.request.method).toBe('GET');
      req.flush(endDate);
    });

    it('should handle null date', () => {
      const contractId = 1;

      service.getLatestEndDateByContractId(contractId).subscribe((date) => {
        expect(date).toBeNull();
      });

      const req = httpMock.expectOne(
        `${apiUrl}/contract/${contractId}/latest-end-date`,
      );
      expect(req.request.method).toBe('GET');
      req.flush(null);
    });
  });

  describe('create', () => {
    it('should create new contract values with audit record when user is logged in', () => {
      const newContractValues: Omit<ContractValues, 'id'> = {
        contractId: 1,
        numericValue: 1000000,
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        subscriptionDate: '2023-01-01',
        cdp: 12345,
        cdpEntityId: 1,
        madsValue: 0,
        isOtherEntity: false,
        cdpEntity: { id: 1, name: 'Test CDP Entity' },
      };

      const mockResponse: ContractValues = {
        id: 1,
        ...newContractValues,
      };

      authServiceSpy.getCurrentUser.and.returnValue(mockUser);
      contractAuditStatusServiceSpy.getByName.and.returnValue(
        of(mockAuditStatus),
      );

      const mockAuditHistory: ContractAuditHistory = {
        id: 1,
        contractId: 1,
        auditStatusId: 1,
        auditDate: new Date(),
        comment: 'Test comment',
        auditorId: 1,
      };

      contractAuditHistoryServiceSpy.create.and.returnValue(
        of(mockAuditHistory),
      );

      service.create(newContractValues).subscribe((values) => {
        expect(values).toEqual(mockResponse);
        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();
        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith(
          'Adición de valores',
        );
        expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newContractValues);
      req.flush(mockResponse);
    });

    it('should create new contract values without audit record when user is not logged in', () => {
      const newContractValues: Omit<ContractValues, 'id'> = {
        contractId: 1,
        numericValue: 1000000,
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        subscriptionDate: '2023-01-01',
        cdp: 12345,
        cdpEntityId: 1,
        madsValue: 0,
        isOtherEntity: false,
        cdpEntity: { id: 1, name: 'Test CDP Entity' },
      };

      const mockResponse: ContractValues = {
        id: 1,
        ...newContractValues,
      };

      authServiceSpy.getCurrentUser.and.returnValue(null);

      service.create(newContractValues).subscribe((values) => {
        expect(values).toEqual(mockResponse);
        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();
        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();
        expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newContractValues);
      req.flush(mockResponse);
    });
  });

  describe('update', () => {
    it('should update existing contract values with audit record when there are changes and user is logged in', () => {
      const id = 1;
      const originalValues: ContractValues = {
        id: 1,
        contractId: 1,
        numericValue: 1000000,
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        subscriptionDate: '2023-01-01',
        cdp: 12345,
        cdpEntityId: 1,
        madsValue: 0,
        isOtherEntity: false,
        cdpEntity: { id: 1, name: 'Test CDP Entity' },
      };

      const updateValues: Partial<ContractValues> = {
        numericValue: 1500000,
        endDate: '2024-06-30',
      };

      const mockResponse: ContractValues = {
        ...originalValues,
        ...updateValues,
      };

      authServiceSpy.getCurrentUser.and.returnValue(mockUser);
      contractAuditStatusServiceSpy.getByName.and.returnValue(
        of(mockAuditStatus),
      );

      const mockAuditHistory: ContractAuditHistory = {
        id: 1,
        contractId: 1,
        auditorId: mockUser.id,
        auditStatusId: mockAuditStatus.id,
        contractAuditStatus: mockAuditStatus,
        auditDate: new Date().toISOString(),
        comment: 'Test comment',
      };

      contractAuditHistoryServiceSpy.create.and.returnValue(
        of(mockAuditHistory),
      );

      let updateResult: ContractValues | undefined;
      service.update(id, updateValues).subscribe((result) => {
        updateResult = result;
      });

      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(getReq.request.method).toBe('GET');
      getReq.flush(originalValues);

      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(putReq.request.method).toBe('PUT');
      expect(putReq.request.body).toEqual(updateValues);
      putReq.flush(mockResponse);

      expect(updateResult).toEqual(mockResponse);
      expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith(
        'Edición de valores',
      );
      expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();
    });

    it('should update existing contract values without audit record when there are no changes', () => {
      const id = 1;
      const originalValues: ContractValues = {
        id: 1,
        contractId: 1,
        numericValue: 1000000,
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        subscriptionDate: '2023-01-01',
        cdp: 12345,
        cdpEntityId: 1,
        madsValue: 0,
        isOtherEntity: false,
        cdpEntity: { id: 1, name: 'Test CDP Entity' },
      };

      const updateValues: Partial<ContractValues> = {
        numericValue: 1000000,
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        subscriptionDate: '2023-01-01',
      };

      const mockResponse: ContractValues = {
        ...originalValues,
      };

      authServiceSpy.getCurrentUser.and.returnValue(mockUser);

      let updateResult: ContractValues | undefined;
      service.update(id, updateValues).subscribe((result) => {
        updateResult = result;
      });

      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(getReq.request.method).toBe('GET');
      getReq.flush(originalValues);

      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(putReq.request.method).toBe('PUT');
      expect(putReq.request.body).toEqual(updateValues);
      putReq.flush(mockResponse);

      expect(updateResult).toEqual(mockResponse);
      expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();
      expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();
    });

    it('should update existing contract values without audit record when user is not logged in', () => {
      const id = 1;
      const originalValues: ContractValues = {
        id: 1,
        contractId: 1,
        numericValue: 1000000,
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        subscriptionDate: '2023-01-01',
        cdp: 12345,
        cdpEntityId: 1,
        madsValue: 0,
        isOtherEntity: false,
        cdpEntity: { id: 1, name: 'Test CDP Entity' },
      };

      const updateValues: Partial<ContractValues> = {
        numericValue: 1500000,
        endDate: '2024-06-30',
      };

      const mockResponse: ContractValues = {
        ...originalValues,
        ...updateValues,
      };

      authServiceSpy.getCurrentUser.and.returnValue(null);

      let updateResult: ContractValues | undefined;
      service.update(id, updateValues).subscribe((result) => {
        updateResult = result;
      });

      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(getReq.request.method).toBe('GET');
      getReq.flush(originalValues);

      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(putReq.request.method).toBe('PUT');
      expect(putReq.request.body).toEqual(updateValues);
      putReq.flush(mockResponse);

      expect(updateResult).toEqual(mockResponse);
      expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();
      expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();
    });
  });

  describe('delete', () => {
    it('should delete contract values', () => {
      const id = 1;

      service.delete(id).subscribe((response) => {
        expect(response).toBeNull();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });
});