import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';

import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AlertService } from '@shared/services/alert.service';
import { SupervisorDialogComponent } from '@supervisor-management/components/supervisor-dialog/supervisor-dialog.component';
import { Supervisor } from '@supervisor-management/models/supervisor.model';
import { SupervisorService } from '@supervisor-management/services/supervisor.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { finalize } from 'rxjs';

@Component({
  selector: 'app-supervisors-list-page',
  templateUrl: './supervisors-list-page.component.html',
  styleUrl: './supervisors-list-page.component.scss',
  standalone: true,
  imports: [
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    MatTooltipModule,
  ],
})
export class SupervisorsListPageComponent implements OnInit, AfterViewInit {
  displayedColumns = [
    'fullName',
    'idType',
    'idNumber',
    'email',
    'position',
    'actions',
  ];
  dataSource = new MatTableDataSource<Supervisor>();

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private readonly supervisorService: SupervisorService,
    private readonly dialog: MatDialog,
    private readonly spinner: NgxSpinnerService,
    private readonly alert: AlertService,
  ) {}

  ngOnInit(): void {
    this.spinner.show();
    this.supervisorService
      .getAll()
      .pipe(finalize(() => this.spinner.hide()))
      .subscribe({
        next: (supervisors) => (this.dataSource.data = supervisors),
        error: (error) => {
          this.alert.error(error.error?.detail ?? 'Error al cargar supervisores');
        },
      });
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  openSupervisorDialog(supervisor?: Supervisor): void {
    this.dialog
      .open(SupervisorDialogComponent, {
        width: '95%',
        maxWidth: '800px',
        height: 'auto',
        maxHeight: '90vh',
        data: supervisor,
      })
      .afterClosed()
      .subscribe((result?: Supervisor) => {
        if (!result) return;

        this.dataSource.data = supervisor
          ? this.dataSource.data.map((s) => (s.id === result.id ? result : s))
          : [...this.dataSource.data, result];
      });
  }

  applyFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    this.dataSource.paginator?.firstPage();
  }
}
