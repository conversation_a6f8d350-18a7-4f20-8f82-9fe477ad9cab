import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { LegalNature } from '@contractor-management/models/legal-nature.model';
import { environment } from '@env';
import { LegalNatureService } from './legal-nature.service';

describe('LegalNatureService', () => {
  let service: LegalNatureService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/legal-nature`;

  const mockLegalNature: LegalNature = {
    id: 1,
    name: 'Test Legal Nature',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [LegalNatureService],
    });
    service = TestBed.inject(LegalNatureService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all legal natures', () => {
      const mockLegalNatures = [mockLegalNature];

      service.getAll().subscribe((legalNatures) => {
        expect(legalNatures).toEqual(mockLegalNatures);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockLegalNatures);
    });
  });

  describe('getById', () => {
    it('should return a legal nature by id', () => {
      const id = 1;

      service.getById(id).subscribe((legalNature) => {
        expect(legalNature).toEqual(mockLegalNature);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockLegalNature);
    });
  });

  describe('create', () => {
    it('should create a new legal nature', () => {
      const newLegalNature: Omit<LegalNature, 'id'> = {
        name: 'New Legal Nature',
      };

      service.create(newLegalNature).subscribe((legalNature) => {
        expect(legalNature).toEqual(mockLegalNature);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newLegalNature);
      req.flush(mockLegalNature);
    });
  });

  describe('update', () => {
    it('should update a legal nature', () => {
      const id = 1;
      const updateData: Partial<LegalNature> = {
        name: 'Updated Legal Nature',
      };

      service.update(id, updateData).subscribe((legalNature) => {
        expect(legalNature).toEqual(mockLegalNature);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockLegalNature);
    });
  });

  describe('delete', () => {
    it('should delete a legal nature', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });
});