import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { ReportObligation } from '@contractor-dashboard/models/report-obligation.model';
import { environment } from '@env';
import { ReportObligationService } from './report-obligation.service';

describe('ReportObligationService', () => {
  let service: ReportObligationService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/report-obligations`;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ReportObligationService],
    });
    service = TestBed.inject(ReportObligationService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  const mockReportObligation: ReportObligation = {
    id: 1,
    description: 'Test obligation',
    evidence: 'Test evidence',
    filePath: '/path/to/file',
    monthlyReportId: 1,
    obligationId: 1,
  };

  describe('getAll', () => {
    it('should return all report obligations', () => {
      const mockReportObligations = [mockReportObligation];

      service.getAll().subscribe((reportObligations) => {
        expect(reportObligations).toEqual(mockReportObligations);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockReportObligations);
    });

    it('should handle error when getting all report obligations', () => {
      service.getAll().subscribe({
        error: (error) => {
          expect(error.status).toBe(500);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Server error'), { status: 500 });
    });
  });

  describe('getById', () => {
    it('should return a report obligation by id', () => {
      service.getById(1).subscribe((reportObligation) => {
        expect(reportObligation).toEqual(mockReportObligation);
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('GET');
      req.flush(mockReportObligation);
    });

    it('should handle error when getting report obligation by id', () => {
      service.getById(999).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/999`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('create', () => {
    const newReportObligation: Omit<ReportObligation, 'id'> = {
      description: 'New obligation',
      evidence: 'New evidence',
      filePath: '/path/to/new/file',
      monthlyReportId: 1,
      obligationId: 1,
    };

    it('should create a new report obligation', () => {
      service.create(newReportObligation).subscribe((reportObligation) => {
        expect(reportObligation).toEqual(mockReportObligation);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newReportObligation);
      req.flush(mockReportObligation);
    });

    it('should handle error when creating report obligation', () => {
      service.create(newReportObligation).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('update', () => {
    const updateData: Partial<ReportObligation> = {
      description: 'Updated description',
      evidence: 'Updated evidence',
    };

    it('should update a report obligation', () => {
      service.update(1, updateData).subscribe((reportObligation) => {
        expect(reportObligation).toEqual({
          ...mockReportObligation,
          ...updateData,
        });
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush({ ...mockReportObligation, ...updateData });
    });

    it('should handle error when updating report obligation', () => {
      service.update(1, updateData).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('delete', () => {
    it('should delete a report obligation', () => {
      service.delete(1).subscribe((response) => {
        expect(response).toBeNull();
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });

    it('should handle error when deleting report obligation', () => {
      service.delete(1).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('getByMonthlyReportId', () => {
    it('should return report obligations by monthly report id', () => {
      const monthlyReportId = 1;
      const mockReportObligations = [mockReportObligation];

      service
        .getByMonthlyReportId(monthlyReportId)
        .subscribe((reportObligations) => {
          expect(reportObligations).toEqual(mockReportObligations);
        });

      const req = httpMock.expectOne(
        `${apiUrl}/monthly-report/${monthlyReportId}`,
      );
      expect(req.request.method).toBe('GET');
      req.flush(mockReportObligations);
    });

    it('should handle error when getting report obligations by monthly report id', () => {
      const monthlyReportId = 999;

      service.getByMonthlyReportId(monthlyReportId).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(
        `${apiUrl}/monthly-report/${monthlyReportId}`,
      );
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });
});