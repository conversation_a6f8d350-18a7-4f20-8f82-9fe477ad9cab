import { AsyncPipe } from '@angular/common';
import {
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  MatAutocompleteModule,
  MatAutocompleteSelectedEvent,
} from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { Observable } from 'rxjs';
import { map, startWith, switchMap } from 'rxjs/operators';

import { Contractor } from '@contractor-management/models/contractor.model';
import { ContractorService } from '@contractor-management/services/contractor.service';
import { AlertService } from '@shared/services/alert.service';

@Component({
  selector: 'app-contractor-detail-form',
  templateUrl: './contractor-detail-form.component.html',
  styleUrl: './contractor-detail-form.component.scss',
  standalone: true,
  imports: [
    AsyncPipe,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatButtonModule,
    MatAutocompleteModule,
  ],
})
export class ContractorDetailFormComponent implements OnChanges, OnInit {
  @Input() isContractFinished = false;
  @Input() contractorId?: number;

  private hasExplicitContractorId = false;

  contractor: Contractor | null = null;

  contractorForm = this.formBuilder.group({
    fullName: ['', Validators.required],
    idNumber: ['', Validators.required],
    personalEmail: [{ value: '', disabled: true }],
    corporateEmail: [{ value: '', disabled: true }],
  });

  get fullNameControl(): FormControl {
    return this.contractorForm.get('fullName') as FormControl;
  }

  get idNumberControl(): FormControl {
    return this.contractorForm.get('idNumber') as FormControl;
  }

  filteredContractorsByFullName: Observable<Contractor[]> =
    this.fullNameControl.valueChanges.pipe(
      startWith(''),
      switchMap((value) => {
        const filterValue =
          typeof value === 'string'
            ? value.toLowerCase()
            : value?.fullName?.toLowerCase() || '';

        return this.contractorService
          .getAll()
          .pipe(
            map((contractors) =>
              contractors.filter((contractor) =>
                contractor.fullName.toLowerCase().includes(filterValue),
              ),
            ),
          );
      }),
    );

  filteredContractorsByIdNumber: Observable<Contractor[]> =
    this.idNumberControl.valueChanges.pipe(
      startWith(''),
      switchMap((value) => {
        const filterValue =
          typeof value === 'string' ? value : value?.idNumber?.toString() || '';

        return this.contractorService
          .getAll()
          .pipe(
            map((contractors) =>
              contractors.filter((contractor) =>
                contractor.idNumber.toString().includes(filterValue),
              ),
            ),
          );
      }),
    );

  constructor(
    private readonly contractorService: ContractorService,
    private readonly formBuilder: FormBuilder,
    private readonly alert: AlertService,
  ) {}

  ngOnInit(): void {
    if (this.contractorId) {
      this.hasExplicitContractorId = true;
      this.loadContractorIfProvided();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.isContractFinished) {
      this.fullNameControl.disable();
      this.idNumberControl.disable();
    } else if (!this.hasExplicitContractorId) {
      this.fullNameControl.enable();
      this.idNumberControl.enable();
    }

    if (changes['contractorId'] && this.contractorId) {
      this.hasExplicitContractorId = true;
      this.loadContractorIfProvided();
    }
  }

  private loadContractorIfProvided(): void {
    if (this.contractorId) {
      this.contractorService.getById(this.contractorId).subscribe({
        next: (contractor) => {
          if (contractor) {
            this.contractor = contractor;
            this.contractorForm.patchValue({
              fullName: contractor.fullName,
              idNumber: contractor.idNumber.toString(),
              personalEmail: contractor.personalEmail || '',
              corporateEmail: contractor.corporateEmail || '',
            });

            if (this.hasExplicitContractorId) {
              this.fullNameControl.disable();
              this.idNumberControl.disable();
            }
          }
        },
        error: (error) => {
          this.alert.error(error.error?.detail ?? 'Error al cargar los datos del contratista');
        },
      });
    }
  }

  onContractorSelected(event: MatAutocompleteSelectedEvent): void {
    const selectedContractor = event.option.value as Contractor;
    this.contractorForm.patchValue({
      fullName: selectedContractor.fullName,
      idNumber: selectedContractor.idNumber.toString(),
      personalEmail: selectedContractor.personalEmail || '',
      corporateEmail: selectedContractor.corporateEmail || '',
    });
    this.contractor = selectedContractor;

    if (this.hasExplicitContractorId) {
      this.fullNameControl.disable();
      this.idNumberControl.disable();
    }
  }

  clearFields(): void {
    this.contractorForm.patchValue({
      fullName: '',
      idNumber: '',
      personalEmail: '',
      corporateEmail: '',
    });
    this.contractor = null;

    if (!this.isContractFinished && !this.hasExplicitContractorId) {
      this.fullNameControl.enable();
      this.idNumberControl.enable();
    }
  }

  isValid(): boolean {
    return this.contractorForm.valid;
  }

  getValue(): Contractor | null {
    Object.keys(this.contractorForm.controls).forEach(key => {
      const control = this.contractorForm.get(key);
      if (control) {
        control.markAsTouched();
      }
    });

    return this.contractor;
  }
}
