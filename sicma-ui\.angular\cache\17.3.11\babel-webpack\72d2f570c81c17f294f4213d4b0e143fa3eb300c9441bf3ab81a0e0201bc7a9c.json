{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { InsuredRisksService } from './insured_risks.service';\ndescribe('InsuredRisksService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/insured_risks`;\n  const mockInsuredRisk = {\n    id: 1,\n    name: 'Test Insured Risk'\n  };\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [InsuredRisksService]\n    });\n    service = TestBed.inject(InsuredRisksService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all insured risks', () => {\n      const mockInsuredRisks = [mockInsuredRisk];\n      service.getAll().subscribe(insuredRisks => {\n        expect(insuredRisks).toEqual(mockInsuredRisks);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockInsuredRisks);\n    });\n  });\n  describe('getById', () => {\n    it('should return an insured risk by id', () => {\n      const id = 1;\n      service.getById(id).subscribe(insuredRisk => {\n        expect(insuredRisk).toEqual(mockInsuredRisk);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockInsuredRisk);\n    });\n  });\n  describe('create', () => {\n    it('should create a new insured risk', () => {\n      const newInsuredRisk = {\n        name: 'New Insured Risk'\n      };\n      service.create(newInsuredRisk).subscribe(insuredRisk => {\n        expect(insuredRisk).toEqual(mockInsuredRisk);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newInsuredRisk);\n      req.flush(mockInsuredRisk);\n    });\n  });\n  describe('update', () => {\n    it('should update an insured risk', () => {\n      const id = 1;\n      const updateData = {\n        name: 'Updated Insured Risk'\n      };\n      service.update(id, updateData).subscribe(insuredRisk => {\n        expect(insuredRisk).toEqual(mockInsuredRisk);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockInsuredRisk);\n    });\n  });\n  describe('delete', () => {\n    it('should delete an insured risk', () => {\n      const id = 1;\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n  describe('getByName', () => {\n    it('should return an insured risk by name', () => {\n      const name = 'Test Insured Risk';\n      service.getByName(name).subscribe(insuredRisk => {\n        expect(insuredRisk).toEqual(mockInsuredRisk);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockInsuredRisk);\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "InsuredRisksService", "describe", "service", "httpMock", "apiUrl", "mockInsuredRisk", "id", "name", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockInsuredRisks", "getAll", "subscribe", "insuredRisks", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "getById", "insuredRisk", "newInsuredRisk", "create", "body", "updateData", "update", "delete", "nothing", "getByName"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\services\\insured_risks.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { InsuredRisks } from '@contract-management/models/insured_risks.model';\nimport { environment } from '@env';\nimport { InsuredRisksService } from './insured_risks.service';\n\ndescribe('InsuredRisksService', () => {\n  let service: InsuredRisksService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/insured_risks`;\n\n  const mockInsuredRisk: InsuredRisks = {\n    id: 1,\n    name: 'Test Insured Risk',\n  };\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [InsuredRisksService],\n    });\n    service = TestBed.inject(InsuredRisksService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all insured risks', () => {\n      const mockInsuredRisks = [mockInsuredRisk];\n\n      service.getAll().subscribe((insuredRisks) => {\n        expect(insuredRisks).toEqual(mockInsuredRisks);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockInsuredRisks);\n    });\n  });\n\n  describe('getById', () => {\n    it('should return an insured risk by id', () => {\n      const id = 1;\n\n      service.getById(id).subscribe((insuredRisk) => {\n        expect(insuredRisk).toEqual(mockInsuredRisk);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockInsuredRisk);\n    });\n  });\n\n  describe('create', () => {\n    it('should create a new insured risk', () => {\n      const newInsuredRisk: Omit<InsuredRisks, 'id'> = {\n        name: 'New Insured Risk',\n      };\n\n      service.create(newInsuredRisk).subscribe((insuredRisk) => {\n        expect(insuredRisk).toEqual(mockInsuredRisk);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newInsuredRisk);\n      req.flush(mockInsuredRisk);\n    });\n  });\n\n  describe('update', () => {\n    it('should update an insured risk', () => {\n      const id = 1;\n      const updateData: Partial<InsuredRisks> = {\n        name: 'Updated Insured Risk',\n      };\n\n      service.update(id, updateData).subscribe((insuredRisk) => {\n        expect(insuredRisk).toEqual(mockInsuredRisk);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockInsuredRisk);\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete an insured risk', () => {\n      const id = 1;\n\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n\n  describe('getByName', () => {\n    it('should return an insured risk by name', () => {\n      const name = 'Test Insured Risk';\n\n      service.getByName(name).subscribe((insuredRisk) => {\n        expect(insuredRisk).toEqual(mockInsuredRisk);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockInsuredRisk);\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,mBAAmB,QAAQ,yBAAyB;AAE7DC,QAAQ,CAAC,qBAAqB,EAAE,MAAK;EACnC,IAAIC,OAA4B;EAChC,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,gBAAgB;EAEpD,MAAMC,eAAe,GAAiB;IACpCC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE;GACP;EAEDC,UAAU,CAAC,MAAK;IACdV,OAAO,CAACW,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACd,uBAAuB,CAAC;MAClCe,SAAS,EAAE,CAACX,mBAAmB;KAChC,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACc,MAAM,CAACZ,mBAAmB,CAAC;IAC7CG,QAAQ,GAAGL,OAAO,CAACc,MAAM,CAACf,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFgB,SAAS,CAAC,MAAK;IACbV,QAAQ,CAACW,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACd,OAAO,CAAC,CAACe,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFhB,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,iCAAiC,EAAE,MAAK;MACzC,MAAMG,gBAAgB,GAAG,CAACb,eAAe,CAAC;MAE1CH,OAAO,CAACiB,MAAM,EAAE,CAACC,SAAS,CAAEC,YAAY,IAAI;QAC1CL,MAAM,CAACK,YAAY,CAAC,CAACC,OAAO,CAACJ,gBAAgB,CAAC;MAChD,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,gBAAgB,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjB,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBc,EAAE,CAAC,qCAAqC,EAAE,MAAK;MAC7C,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAAC2B,OAAO,CAACvB,EAAE,CAAC,CAACc,SAAS,CAAEU,WAAW,IAAI;QAC5Cd,MAAM,CAACc,WAAW,CAAC,CAACR,OAAO,CAACjB,eAAe,CAAC;MAC9C,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvB,eAAe,CAAC;IAC5B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,kCAAkC,EAAE,MAAK;MAC1C,MAAMgB,cAAc,GAA6B;QAC/CxB,IAAI,EAAE;OACP;MAEDL,OAAO,CAAC8B,MAAM,CAACD,cAAc,CAAC,CAACX,SAAS,CAAEU,WAAW,IAAI;QACvDd,MAAM,CAACc,WAAW,CAAC,CAACR,OAAO,CAACjB,eAAe,CAAC;MAC9C,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACS,cAAc,CAAC;MAChDR,GAAG,CAACK,KAAK,CAACvB,eAAe,CAAC;IAC5B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,+BAA+B,EAAE,MAAK;MACvC,MAAMT,EAAE,GAAG,CAAC;MACZ,MAAM4B,UAAU,GAA0B;QACxC3B,IAAI,EAAE;OACP;MAEDL,OAAO,CAACiC,MAAM,CAAC7B,EAAE,EAAE4B,UAAU,CAAC,CAACd,SAAS,CAAEU,WAAW,IAAI;QACvDd,MAAM,CAACc,WAAW,CAAC,CAACR,OAAO,CAACjB,eAAe,CAAC;MAC9C,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACY,UAAU,CAAC;MAC5CX,GAAG,CAACK,KAAK,CAACvB,eAAe,CAAC;IAC5B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,+BAA+B,EAAE,MAAK;MACvC,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAACkC,MAAM,CAAC9B,EAAE,CAAC,CAACc,SAAS,CAAC,MAAK;QAChCJ,MAAM,EAAE,CAACqB,OAAO,EAAE;MACpB,CAAC,CAAC;MAEF,MAAMd,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF3B,QAAQ,CAAC,WAAW,EAAE,MAAK;IACzBc,EAAE,CAAC,uCAAuC,EAAE,MAAK;MAC/C,MAAMR,IAAI,GAAG,mBAAmB;MAEhCL,OAAO,CAACoC,SAAS,CAAC/B,IAAI,CAAC,CAACa,SAAS,CAAEU,WAAW,IAAI;QAChDd,MAAM,CAACc,WAAW,CAAC,CAACR,OAAO,CAACjB,eAAe,CAAC;MAC9C,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,SAASG,IAAI,EAAE,CAAC;MACxDS,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvB,eAAe,CAAC;IAC5B,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}