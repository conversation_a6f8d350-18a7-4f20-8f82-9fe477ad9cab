import { HttpErrorResponse } from '@angular/common/http';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Contract } from '@contract-management/models/contract.model';
import { Obligation } from '@contract-management/models/obligation.model';
import { ObligationService } from '@contract-management/services/obligation.service';
import { AlertService } from '@shared/services/alert.service';

type TableObligation = Obligation & { editing: boolean };

@Component({
  selector: 'app-obligations-list',
  templateUrl: './obligations-list.component.html',
  styleUrl: './obligations-list.component.scss',
  standalone: true,
  imports: [
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    MatTooltipModule,
  ],
})
export class ObligationsListComponent implements OnInit {
  @Input() contract!: Contract;
  @Input() isContractFinished = false;
  @Output() obligationChanged = new EventEmitter<void>();

  obligationsList: TableObligation[] = [];
  obligationsColumns: string[] = ['number', 'name', 'actions'];
  private originalStates: Record<number, { name: string; number?: number }> =
    {};

  constructor(
    private readonly obligationService: ObligationService,
    private readonly alert: AlertService,
  ) {}

  ngOnInit(): void {
    if (this.contract?.obligations) {
      this.obligationsList = this.contract.obligations
        .map((obligation) => ({
          ...obligation,
          editing: false,
        }))
        .sort((a, b) => (a.number ?? Infinity) - (b.number ?? Infinity));
    }
  }

  addNewObligation(): void {
    const newObligation: TableObligation = {
      name: '',
      contractId: this.contract.id,
      editing: true,
    };
    this.obligationsList = [...this.obligationsList, newObligation];
  }

  editObligation(obligation: TableObligation): void {
    if (obligation.id) {
      this.originalStates[obligation.id] = {
        name: obligation.name,
        number: obligation.number,
      };
    }
    obligation.editing = true;
  }

  saveObligation(obligation: TableObligation): void {
    const { ...obligationData } = obligation;
    delete (obligationData as Partial<TableObligation>).editing;

    const operation = obligationData.id
      ? this.obligationService.update(obligationData.id, obligationData)
      : this.obligationService.create(obligationData);

    operation.subscribe({
      next: (savedObligation: Obligation) => {
        let updatedList: TableObligation[];
        if (obligationData.id) {
          updatedList = this.obligationsList.map((ob) =>
            ob.id === savedObligation.id
              ? { ...savedObligation, editing: false }
              : ob,
          );
          delete this.originalStates[obligationData.id];
        } else {
          updatedList = [
            ...this.obligationsList.filter((ob) => ob.id),
            { ...savedObligation, editing: false },
          ];
        }
        this.obligationsList = updatedList.sort(
          (a, b) => (a.number ?? Infinity) - (b.number ?? Infinity),
        );
        obligation.editing = false;

        this.alert.success(
          `Obligación ${
            obligationData.id ? 'actualizada' : 'agregada'
          } correctamente`,
        );
        this.obligationChanged.emit();
      },
      error: ({ status }: HttpErrorResponse) => {
        switch (status) {
          case 400:
            if (obligation.name.length > 1000) {
              this.alert.warning(
                'Validación Fallida',
                'La obligación no puede tener más de 1000 caracteres',
              );
            } else {
              this.alert.warning(
                'Validación Fallida',
                'La obligación no puede estar vacía o contener solo espacios en blanco',
              );
            }
            break;
          case 409:
            this.alert.warning(
              'Obligación Duplicada',
              'Ya existe una obligación con esta descripción en el contrato',
            );
            break;
          default:
            this.alert.error(
              'Error en la Operación',
              `Error al ${obligation.id ? 'actualizar' : 'agregar'} la obligación`,
            );
        }
        obligation.editing = true;
      },
    });
  }

  cancelEdit(obligation: TableObligation): void {
    if (!obligation.id) {
      this.obligationsList = this.obligationsList.filter(
        (ob) => ob !== obligation,
      );
      this.obligationChanged.emit();
    } else if (obligation.id && this.originalStates[obligation.id]) {
      const originalState = this.originalStates[obligation.id];
      obligation.name = originalState.name;
      obligation.number = originalState.number;
      delete this.originalStates[obligation.id];
      obligation.editing = false;
      this.obligationChanged.emit();
    } else {
      obligation.editing = false;
    }
  }

  async deleteObligation(obligation: TableObligation): Promise<void> {
    if (!obligation.id) return;

    const confirmed = await this.alert.confirm(
      '¿Está seguro que desea eliminar esta obligación?',
    );

    if (confirmed) {
      this.obligationService.delete(obligation.id).subscribe({
        next: () => {
          this.obligationsList = this.obligationsList
            .filter((ob) => ob.id !== obligation.id)
            .sort((a, b) => (a.number ?? Infinity) - (b.number ?? Infinity));
          this.alert.success('Obligación eliminada correctamente');
          this.obligationChanged.emit();
        },
        error: (error: HttpErrorResponse) => {
          if (error.status === 409) {
            this.alert.warning(
              'No se puede eliminar',
              'Esta obligación está siendo utilizada en informes mensuales y no puede ser eliminada.',
            );
          } else {
            this.alert.error(error.error?.detail ?? 'No se pudo eliminar la obligación.');
          }
        },
      });
    }
  }
}
