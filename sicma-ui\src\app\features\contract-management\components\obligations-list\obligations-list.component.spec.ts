import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { Contract } from '@contract-management/models/contract.model';
import { Obligation } from '@contract-management/models/obligation.model';
import { ObligationService } from '@contract-management/services/obligation.service';
import { AlertService } from '@shared/services/alert.service';
import { of, throwError } from 'rxjs';
import { ObligationsListComponent } from './obligations-list.component';

type TableObligation = Obligation & { editing: boolean };

describe('ObligationsListComponent', () => {
  let component: ObligationsListComponent;
  let fixture: ComponentFixture<ObligationsListComponent>;
  let obligationService: jasmine.SpyObj<ObligationService>;
  let alertService: jasmine.SpyObj<AlertService>;

  const mockObligations: TableObligation[] = [
    { id: 1, name: 'Obligation 1', contractId: 1, number: 1, editing: false },
    { id: 2, name: 'Obligation 2', contractId: 1, number: 2, editing: false },
  ];

  const mockContract: Contract = {
    id: 1,
    contractNumber: 123,
    monthlyPayment: 1000000,
    object: 'Test Contract',
    rup: true,
    secopCode: 123456,
    addition: false,
    cession: false,
    settled: false,
    contractTypeId: 1,
    statusId: 1,
    causesSelectionId: 1,
    managementSupportId: 1,
    contractClassId: 1,
    obligations: mockObligations,
  };

  beforeEach(() => {
    const obligationServiceSpy = jasmine.createSpyObj('ObligationService', [
      'create',
      'update',
      'delete',
    ]);
    const alertServiceSpy = jasmine.createSpyObj('AlertService', [
      'success',
      'error',
      'warning',
      'confirm',
    ]);

    TestBed.configureTestingModule({
      imports: [
        ObligationsListComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
      ],
      providers: [
        { provide: ObligationService, useValue: obligationServiceSpy },
        { provide: AlertService, useValue: alertServiceSpy },
      ],
    });

    fixture = TestBed.createComponent(ObligationsListComponent);
    component = fixture.componentInstance;
    obligationService = TestBed.inject(
      ObligationService,
    ) as jasmine.SpyObj<ObligationService>;
    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;

    obligationService.create.and.returnValue(
      of({ id: 3, name: 'New Obligation', contractId: 1, number: 3 }),
    );
    obligationService.update.and.returnValue(
      of({ id: 1, name: 'Updated Obligation', contractId: 1, number: 1 }),
    );
    obligationService.delete.and.returnValue(of(void 0));
    alertService.confirm.and.returnValue(Promise.resolve(true));

    component.contract = mockContract;
  });

  it('should create', () => {
    fixture.detectChanges();
    expect(component).toBeTruthy();
  });

  describe('Initialization', () => {
    it('should initialize obligations list from contract', () => {
      fixture.detectChanges();
      expect(component.obligationsList).toEqual(mockObligations);
    });

    it('should handle empty obligations list', () => {
      component.contract = { ...mockContract, obligations: undefined };
      fixture.detectChanges();
      expect(component.obligationsList).toEqual([]);
    });
  });

  describe('CRUD Operations', () => {
    it('should add new obligation', () => {
      const initialLength = component.obligationsList.length;
      component.addNewObligation();
      expect(component.obligationsList.length).toBe(initialLength + 1);
      expect(component.obligationsList[initialLength]).toEqual({
        name: '',
        contractId: mockContract.id,
        editing: true,
      });
    });

    it('should edit obligation', () => {
      fixture.detectChanges();
      const obligation = component.obligationsList[0];
      component.editObligation(obligation);
      expect(obligation.editing).toBeTrue();
    });

    it('should save new obligation successfully', () => {
      const newObligation: TableObligation = {
        name: 'New Obligation',
        contractId: mockContract.id,
        editing: true,
      };

      component.saveObligation(newObligation);

      expect(obligationService.create).toHaveBeenCalledWith({
        name: 'New Obligation',
        contractId: 1,
      });
      expect(alertService.success).toHaveBeenCalledWith(
        'Obligación agregada correctamente',
      );
    });

    it('should update existing obligation successfully', () => {
      const obligation = {
        ...mockObligations[0],
        name: 'Updated Name',
        number: 1,
      };

      component.saveObligation(obligation);

      expect(obligationService.update).toHaveBeenCalledWith(
        1,
        jasmine.objectContaining({ name: 'Updated Name', number: 1 }),
      );
      expect(alertService.success).toHaveBeenCalledWith(
        'Obligación actualizada correctamente',
      );
    });

    it('should handle validation error when saving obligation', () => {
      const obligation = { ...mockObligations[0], name: '' };
      obligationService.update.and.returnValue(
        throwError(() => ({ status: 400 })),
      );

      component.saveObligation(obligation);

      expect(alertService.warning).toHaveBeenCalledWith(
        'Validación Fallida',
        'La obligación no puede estar vacía o contener solo espacios en blanco',
      );
    });

    it('should handle duplicate error when saving obligation', () => {
      const obligation = { ...mockObligations[0], name: 'Duplicate' };
      obligationService.update.and.returnValue(
        throwError(() => ({ status: 409 })),
      );

      component.saveObligation(obligation);

      expect(alertService.warning).toHaveBeenCalledWith(
        'Obligación Duplicada',
        'Ya existe una obligación con esta descripción en el contrato',
      );
    });

    it('should delete obligation after confirmation', async () => {
      const obligationToDelete = mockObligations[0];
      const emitSpy = spyOn(component.obligationChanged, 'emit');

      await component.deleteObligation(obligationToDelete);

      expect(obligationService.delete).toHaveBeenCalledWith(1);
      expect(alertService.success).toHaveBeenCalledWith(
        'Obligación eliminada correctamente',
      );
      expect(emitSpy).toHaveBeenCalled();
    });

    it('should not delete obligation when user cancels', async () => {
      alertService.confirm.and.returnValue(Promise.resolve(false));
      const obligationToDelete = mockObligations[0];

      await component.deleteObligation(obligationToDelete);

      expect(obligationService.delete).not.toHaveBeenCalled();
    });

    it('should handle error when deleting obligation', async () => {
      obligationService.delete.and.returnValue(
        throwError(() => ({ error: 'Error' })),
      );
      const obligationToDelete = mockObligations[0];

      await component.deleteObligation(obligationToDelete);

      expect(alertService.error).toHaveBeenCalledWith(
        'No se pudo eliminar la obligación.',
      );
    });
  });

  describe('Cancel Operations', () => {
    it('should remove new obligation on cancel', () => {
      const newObligation: TableObligation = {
        name: '',
        contractId: mockContract.id,
        editing: true,
      };
      component.obligationsList = [...mockObligations, newObligation];

      component.cancelEdit(newObligation);

      expect(component.obligationsList).toEqual(mockObligations);
    });

    it('should restore original state on cancel edit', () => {
      const obligation = { ...mockObligations[0], editing: true };
      component.editObligation(obligation);
      obligation.name = 'Changed Name';

      component.cancelEdit(obligation);

      expect(obligation.name).toBe(mockObligations[0].name);
      expect(obligation.number).toBe(mockObligations[0].number);
      expect(obligation.editing).toBeFalse();
    });
  });
});
