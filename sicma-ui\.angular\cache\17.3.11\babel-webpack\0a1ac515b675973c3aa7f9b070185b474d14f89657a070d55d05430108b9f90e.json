{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { AppComponent } from './app.component';\ndescribe('AppComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [AppComponent]\n    });\n    fixture = TestBed.createComponent(AppComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "AppComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "imports", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\app.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\n\nimport { AppComponent } from './app.component';\n\ndescribe('AppComponent', () => {\n  let component: AppComponent;\n  let fixture: ComponentFixture<AppComponent>;\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [AppComponent],\n    });\n    fixture = TestBed.createComponent(AppComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});"], "mappings": "AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AAEjE,SAASC,YAAY,QAAQ,iBAAiB;AAE9CC,QAAQ,CAAC,cAAc,EAAE,MAAK;EAC5B,IAAIC,SAAuB;EAC3B,IAAIC,OAAuC;EAE3CC,UAAU,CAAC,MAAK;IACdL,OAAO,CAACM,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACN,YAAY;KACvB,CAAC;IACFG,OAAO,GAAGJ,OAAO,CAACQ,eAAe,CAACP,YAAY,CAAC;IAC/CE,SAAS,GAAGC,OAAO,CAACK,iBAAiB;IACrCL,OAAO,CAACM,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACT,SAAS,CAAC,CAACU,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}