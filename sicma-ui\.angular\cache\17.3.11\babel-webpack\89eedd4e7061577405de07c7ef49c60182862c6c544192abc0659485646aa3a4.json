{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { CcpService } from './ccp.service';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { ContractAuditHistoryService } from './contract-audit-history.service';\nimport { ContractAuditStatusService } from './contract-audit-status.service';\nimport { of } from 'rxjs';\ndescribe('CcpService', () => {\n  let service;\n  let httpTestingController;\n  let authServiceSpy;\n  let contractAuditHistoryServiceSpy;\n  let contractAuditStatusServiceSpy;\n  const API_URL = `${environment.apiUrl}/ccps`;\n  const mockCCP = {\n    id: 1,\n    contractId: 1,\n    expenseObjectUseCcp: '123456',\n    expenseObjectDescription: 'Descripción del objeto de gasto',\n    value: 1000000\n  };\n  const mockUser = {\n    id: 1,\n    username: 'testuser',\n    profiles: []\n  };\n  const mockAuditStatus = {\n    id: 1,\n    name: 'Creación de CCP',\n    description: 'CCP creation status'\n  };\n  beforeEach(() => {\n    authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);\n    contractAuditHistoryServiceSpy = jasmine.createSpyObj('ContractAuditHistoryService', ['create']);\n    contractAuditStatusServiceSpy = jasmine.createSpyObj('ContractAuditStatusService', ['getByName']);\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [CcpService, {\n        provide: AuthService,\n        useValue: authServiceSpy\n      }, {\n        provide: ContractAuditHistoryService,\n        useValue: contractAuditHistoryServiceSpy\n      }, {\n        provide: ContractAuditStatusService,\n        useValue: contractAuditStatusServiceSpy\n      }]\n    });\n    service = TestBed.inject(CcpService);\n    httpTestingController = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpTestingController.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all CCPs', () => {\n      const mockCCPs = [mockCCP];\n      service.getAll().subscribe(ccps => {\n        expect(ccps).toEqual(mockCCPs);\n      });\n      const req = httpTestingController.expectOne(API_URL);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockCCPs);\n    });\n  });\n  describe('getById', () => {\n    it('should return a single CCP by id', () => {\n      service.getById(1).subscribe(ccp => {\n        expect(ccp).toEqual(mockCCP);\n      });\n      const req = httpTestingController.expectOne(`${API_URL}/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockCCP);\n    });\n  });\n  describe('getAllByContractId', () => {\n    it('should return CCPs by contract id', () => {\n      const contractId = 1;\n      const mockCCPs = [mockCCP];\n      service.getAllByContractId(contractId).subscribe(ccps => {\n        expect(ccps).toEqual(mockCCPs);\n      });\n      const req = httpTestingController.expectOne(`${API_URL}/contract/${contractId}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockCCPs);\n    });\n  });\n  describe('create', () => {\n    it('should create a new CCP with audit record when user is logged in', () => {\n      const newCCP = {\n        contractId: 1,\n        expenseObjectUseCcp: '123456',\n        expenseObjectDescription: 'Descripción del objeto de gasto',\n        value: 1000000\n      };\n      const mockResponse = {\n        id: 1,\n        ...newCCP\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n      contractAuditStatusServiceSpy.getByName.and.returnValue(of(mockAuditStatus));\n      const mockAuditHistory = {\n        id: 1,\n        contractId: 1,\n        auditStatusId: 1,\n        auditDate: new Date(),\n        comment: 'Test comment',\n        auditorId: 1\n      };\n      contractAuditHistoryServiceSpy.create.and.returnValue(of(mockAuditHistory));\n      service.create(newCCP).subscribe(ccp => {\n        expect(ccp).toEqual(mockResponse);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith('Creación de CCP');\n        expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();\n      });\n      const req = httpTestingController.expectOne(API_URL);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newCCP);\n      req.flush(mockResponse);\n    });\n    it('should create a new CCP without audit record when user is not logged in', () => {\n      const newCCP = {\n        contractId: 1,\n        expenseObjectUseCcp: '123456',\n        expenseObjectDescription: 'Descripción del objeto de gasto',\n        value: 1000000\n      };\n      const mockResponse = {\n        id: 1,\n        ...newCCP\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n      service.create(newCCP).subscribe(ccp => {\n        expect(ccp).toEqual(mockResponse);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n        expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();\n      });\n      const req = httpTestingController.expectOne(API_URL);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newCCP);\n      req.flush(mockResponse);\n    });\n  });\n  describe('update', () => {\n    it('should update an existing CCP with audit record when there are changes and user is logged in', () => {\n      const id = 1;\n      const originalCCP = {\n        id: 1,\n        contractId: 1,\n        expenseObjectUseCcp: '123456',\n        expenseObjectDescription: 'Descripción original',\n        value: 1000000\n      };\n      const updateCCP = {\n        contractId: 1,\n        expenseObjectDescription: 'Descripción actualizada',\n        value: 1500000\n      };\n      const mockResponse = {\n        ...originalCCP,\n        ...updateCCP\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n      contractAuditStatusServiceSpy.getByName.and.returnValue(of(mockAuditStatus));\n      const mockAuditHistory = {\n        id: 1,\n        contractId: 1,\n        auditStatusId: mockAuditStatus.id,\n        auditDate: new Date(),\n        comment: 'Test comment',\n        auditorId: mockUser.id\n      };\n      contractAuditHistoryServiceSpy.create.and.returnValue(of(mockAuditHistory));\n      let updateResult;\n      service.update(id, updateCCP).subscribe(result => {\n        updateResult = result;\n      });\n      const getReq = httpTestingController.expectOne(`${API_URL}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n      getReq.flush(originalCCP);\n      const putReq = httpTestingController.expectOne(`${API_URL}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateCCP);\n      putReq.flush(mockResponse);\n      expect(updateResult).toEqual(mockResponse);\n      expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith('Edición de CCP');\n      expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();\n    });\n    it('should update an existing CCP without audit record when there are no changes', () => {\n      const id = 1;\n      const originalCCP = {\n        id: 1,\n        contractId: 1,\n        expenseObjectUseCcp: '123456',\n        expenseObjectDescription: 'Descripción original',\n        value: 1000000\n      };\n      const updateCCP = {\n        contractId: 1,\n        expenseObjectUseCcp: '123456',\n        expenseObjectDescription: 'Descripción original',\n        value: 1000000\n      };\n      const mockResponse = {\n        ...originalCCP\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n      let done = false;\n      let updateResult;\n      service.update(id, updateCCP).subscribe({\n        next: result => {\n          updateResult = result;\n          done = true;\n        },\n        error: err => {\n          fail('Should not have errored: ' + err);\n        }\n      });\n      const getReq = httpTestingController.expectOne(`${API_URL}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n      getReq.flush(originalCCP);\n      const putReq = httpTestingController.expectOne(`${API_URL}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateCCP);\n      putReq.flush(mockResponse);\n      expect(done).toBeTrue();\n      expect(updateResult).toEqual(mockResponse);\n      expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n      expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();\n    });\n    it('should update an existing CCP without audit record when user not logged in', () => {\n      const id = 1;\n      const originalCCP = {\n        id: 1,\n        contractId: 1,\n        expenseObjectUseCcp: '123456',\n        expenseObjectDescription: 'Original description',\n        value: 1000000\n      };\n      const updateCCP = {\n        contractId: 1,\n        expenseObjectDescription: 'Updated description',\n        value: 2000000\n      };\n      const mockResponse = {\n        ...originalCCP,\n        ...updateCCP\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n      let updateResult;\n      service.update(id, updateCCP).subscribe(result => {\n        updateResult = result;\n      });\n      const getReq = httpTestingController.expectOne(`${API_URL}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n      getReq.flush(originalCCP);\n      const putReq = httpTestingController.expectOne(`${API_URL}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateCCP);\n      putReq.flush(mockResponse);\n      expect(updateResult).toEqual(mockResponse);\n      expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n      expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();\n    });\n  });\n  describe('delete', () => {\n    it('should delete a CCP', () => {\n      const id = 1;\n      service.delete(id).subscribe(response => {\n        expect(response).toBeNull();\n      });\n      const req = httpTestingController.expectOne(`${API_URL}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "CcpService", "AuthService", "ContractAuditHistoryService", "ContractAuditStatusService", "of", "describe", "service", "httpTestingController", "authServiceSpy", "contractAuditHistoryServiceSpy", "contractAuditStatusServiceSpy", "API_URL", "apiUrl", "mockCCP", "id", "contractId", "expenseObjectUseCcp", "expenseObjectDescription", "value", "mockUser", "username", "profiles", "mockAuditStatus", "name", "description", "beforeEach", "jasmine", "createSpyObj", "configureTestingModule", "imports", "providers", "provide", "useValue", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockCCPs", "getAll", "subscribe", "ccps", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "getById", "ccp", "getAllByContractId", "newCCP", "mockResponse", "getCurrentUser", "and", "returnValue", "getByName", "mockAuditHistory", "auditStatusId", "auditDate", "Date", "comment", "auditorId", "create", "toHaveBeenCalled", "toHaveBeenCalledWith", "body", "not", "originalCCP", "updateCCP", "updateResult", "update", "result", "getReq", "putReq", "done", "next", "error", "err", "fail", "toBeTrue", "delete", "response", "toBeNull"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\services\\ccp.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { CCP } from '@contract-management/models/ccp.model';\nimport { environment } from '@env';\nimport { CcpService } from './ccp.service';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { ContractAuditHistoryService } from './contract-audit-history.service';\nimport { ContractAuditStatusService } from './contract-audit-status.service';\nimport { of } from 'rxjs';\nimport { User } from '@core/auth/models/user.model';\nimport { ContractAuditStatus } from '../models/contract-audit-status.model';\nimport { ContractAuditHistory } from '../models/contract-audit-history.model';\n\ndescribe('CcpService', () => {\n  let service: CcpService;\n  let httpTestingController: HttpTestingController;\n  let authServiceSpy: jasmine.SpyObj<AuthService>;\n  let contractAuditHistoryServiceSpy: jasmine.SpyObj<ContractAuditHistoryService>;\n  let contractAuditStatusServiceSpy: jasmine.SpyObj<ContractAuditStatusService>;\n  const API_URL = `${environment.apiUrl}/ccps`;\n\n  const mockCCP: CCP = {\n    id: 1,\n    contractId: 1,\n    expenseObjectUseCcp: '123456',\n    expenseObjectDescription: 'Descripción del objeto de gasto',\n    value: 1000000,\n  };\n\n  const mockUser: User = {\n    id: 1,\n    username: 'testuser',\n    profiles: [],\n  };\n\n  const mockAuditStatus: ContractAuditStatus = {\n    id: 1,\n    name: 'Creación de CCP',\n    description: 'CCP creation status',\n  };\n\n  beforeEach(() => {\n    authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);\n    contractAuditHistoryServiceSpy = jasmine.createSpyObj(\n      'ContractAuditHistoryService',\n      ['create'],\n    );\n    contractAuditStatusServiceSpy = jasmine.createSpyObj(\n      'ContractAuditStatusService',\n      ['getByName'],\n    );\n\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [\n        CcpService,\n        { provide: AuthService, useValue: authServiceSpy },\n        {\n          provide: ContractAuditHistoryService,\n          useValue: contractAuditHistoryServiceSpy,\n        },\n        {\n          provide: ContractAuditStatusService,\n          useValue: contractAuditStatusServiceSpy,\n        },\n      ],\n    });\n    service = TestBed.inject(CcpService);\n    httpTestingController = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpTestingController.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all CCPs', () => {\n      const mockCCPs: CCP[] = [mockCCP];\n\n      service.getAll().subscribe((ccps) => {\n        expect(ccps).toEqual(mockCCPs);\n      });\n\n      const req = httpTestingController.expectOne(API_URL);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockCCPs);\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a single CCP by id', () => {\n      service.getById(1).subscribe((ccp) => {\n        expect(ccp).toEqual(mockCCP);\n      });\n\n      const req = httpTestingController.expectOne(`${API_URL}/1`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockCCP);\n    });\n  });\n\n  describe('getAllByContractId', () => {\n    it('should return CCPs by contract id', () => {\n      const contractId = 1;\n      const mockCCPs: CCP[] = [mockCCP];\n\n      service.getAllByContractId(contractId).subscribe((ccps) => {\n        expect(ccps).toEqual(mockCCPs);\n      });\n\n      const req = httpTestingController.expectOne(\n        `${API_URL}/contract/${contractId}`,\n      );\n      expect(req.request.method).toBe('GET');\n      req.flush(mockCCPs);\n    });\n  });\n\n  describe('create', () => {\n    it('should create a new CCP with audit record when user is logged in', () => {\n      const newCCP: Omit<CCP, 'id'> = {\n        contractId: 1,\n        expenseObjectUseCcp: '123456',\n        expenseObjectDescription: 'Descripción del objeto de gasto',\n        value: 1000000,\n      };\n\n      const mockResponse: CCP = {\n        id: 1,\n        ...newCCP,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n      contractAuditStatusServiceSpy.getByName.and.returnValue(\n        of(mockAuditStatus),\n      );\n\n      const mockAuditHistory: ContractAuditHistory = {\n        id: 1,\n        contractId: 1,\n        auditStatusId: 1,\n        auditDate: new Date(),\n        comment: 'Test comment',\n        auditorId: 1,\n      };\n\n      contractAuditHistoryServiceSpy.create.and.returnValue(\n        of(mockAuditHistory),\n      );\n\n      service.create(newCCP).subscribe((ccp) => {\n        expect(ccp).toEqual(mockResponse);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith(\n          'Creación de CCP',\n        );\n        expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();\n      });\n\n      const req = httpTestingController.expectOne(API_URL);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newCCP);\n      req.flush(mockResponse);\n    });\n\n    it('should create a new CCP without audit record when user is not logged in', () => {\n      const newCCP: Omit<CCP, 'id'> = {\n        contractId: 1,\n        expenseObjectUseCcp: '123456',\n        expenseObjectDescription: 'Descripción del objeto de gasto',\n        value: 1000000,\n      };\n\n      const mockResponse: CCP = {\n        id: 1,\n        ...newCCP,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n\n      service.create(newCCP).subscribe((ccp) => {\n        expect(ccp).toEqual(mockResponse);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n        expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();\n      });\n\n      const req = httpTestingController.expectOne(API_URL);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newCCP);\n      req.flush(mockResponse);\n    });\n  });\n\n  describe('update', () => {\n    it('should update an existing CCP with audit record when there are changes and user is logged in', () => {\n      const id = 1;\n      const originalCCP: CCP = {\n        id: 1,\n        contractId: 1,\n        expenseObjectUseCcp: '123456',\n        expenseObjectDescription: 'Descripción original',\n        value: 1000000,\n      };\n\n      const updateCCP: Partial<CCP> = {\n        contractId: 1,\n        expenseObjectDescription: 'Descripción actualizada',\n        value: 1500000,\n      };\n\n      const mockResponse: CCP = {\n        ...originalCCP,\n        ...updateCCP,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n      contractAuditStatusServiceSpy.getByName.and.returnValue(\n        of(mockAuditStatus),\n      );\n\n      const mockAuditHistory: ContractAuditHistory = {\n        id: 1,\n        contractId: 1,\n        auditStatusId: mockAuditStatus.id,\n        auditDate: new Date(),\n        comment: 'Test comment',\n        auditorId: mockUser.id,\n      };\n\n      contractAuditHistoryServiceSpy.create.and.returnValue(\n        of(mockAuditHistory),\n      );\n\n      let updateResult: CCP | undefined;\n      service.update(id, updateCCP).subscribe((result) => {\n        updateResult = result;\n      });\n\n      const getReq = httpTestingController.expectOne(`${API_URL}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n      getReq.flush(originalCCP);\n\n      const putReq = httpTestingController.expectOne(`${API_URL}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateCCP);\n      putReq.flush(mockResponse);\n\n      expect(updateResult).toEqual(mockResponse);\n      expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith(\n        'Edición de CCP',\n      );\n      expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();\n    });\n\n    it('should update an existing CCP without audit record when there are no changes', () => {\n      const id = 1;\n      const originalCCP: CCP = {\n        id: 1,\n        contractId: 1,\n        expenseObjectUseCcp: '123456',\n        expenseObjectDescription: 'Descripción original',\n        value: 1000000,\n      };\n\n      const updateCCP: Partial<CCP> = {\n        contractId: 1,\n        expenseObjectUseCcp: '123456',\n        expenseObjectDescription: 'Descripción original',\n        value: 1000000,\n      };\n\n      const mockResponse: CCP = { ...originalCCP };\n\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n\n      let done = false;\n      let updateResult: CCP | undefined;\n\n      service.update(id, updateCCP).subscribe({\n        next: (result) => {\n          updateResult = result;\n          done = true;\n        },\n        error: (err) => {\n          fail('Should not have errored: ' + err);\n        },\n      });\n\n      const getReq = httpTestingController.expectOne(`${API_URL}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n      getReq.flush(originalCCP);\n\n      const putReq = httpTestingController.expectOne(`${API_URL}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateCCP);\n      putReq.flush(mockResponse);\n\n      expect(done).toBeTrue();\n      expect(updateResult).toEqual(mockResponse);\n      expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n      expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();\n    });\n\n    it('should update an existing CCP without audit record when user not logged in', () => {\n      const id = 1;\n      const originalCCP: CCP = {\n        id: 1,\n        contractId: 1,\n        expenseObjectUseCcp: '123456',\n        expenseObjectDescription: 'Original description',\n        value: 1000000,\n      };\n\n      const updateCCP: Partial<CCP> = {\n        contractId: 1,\n        expenseObjectDescription: 'Updated description',\n        value: 2000000,\n      };\n\n      const mockResponse: CCP = {\n        ...originalCCP,\n        ...updateCCP,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n\n      let updateResult: CCP | undefined;\n      service.update(id, updateCCP).subscribe((result) => {\n        updateResult = result;\n      });\n\n      const getReq = httpTestingController.expectOne(`${API_URL}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n      getReq.flush(originalCCP);\n\n      const putReq = httpTestingController.expectOne(`${API_URL}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateCCP);\n      putReq.flush(mockResponse);\n\n      expect(updateResult).toEqual(mockResponse);\n      expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n      expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete a CCP', () => {\n      const id = 1;\n\n      service.delete(id).subscribe((response) => {\n        expect(response).toBeNull();\n      });\n\n      const req = httpTestingController.expectOne(`${API_URL}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,0BAA0B,QAAQ,iCAAiC;AAC5E,SAASC,EAAE,QAAQ,MAAM;AAKzBC,QAAQ,CAAC,YAAY,EAAE,MAAK;EAC1B,IAAIC,OAAmB;EACvB,IAAIC,qBAA4C;EAChD,IAAIC,cAA2C;EAC/C,IAAIC,8BAA2E;EAC/E,IAAIC,6BAAyE;EAC7E,MAAMC,OAAO,GAAG,GAAGZ,WAAW,CAACa,MAAM,OAAO;EAE5C,MAAMC,OAAO,GAAQ;IACnBC,EAAE,EAAE,CAAC;IACLC,UAAU,EAAE,CAAC;IACbC,mBAAmB,EAAE,QAAQ;IAC7BC,wBAAwB,EAAE,iCAAiC;IAC3DC,KAAK,EAAE;GACR;EAED,MAAMC,QAAQ,GAAS;IACrBL,EAAE,EAAE,CAAC;IACLM,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE;GACX;EAED,MAAMC,eAAe,GAAwB;IAC3CR,EAAE,EAAE,CAAC;IACLS,IAAI,EAAE,iBAAiB;IACvBC,WAAW,EAAE;GACd;EAEDC,UAAU,CAAC,MAAK;IACdjB,cAAc,GAAGkB,OAAO,CAACC,YAAY,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,CAAC;IACxElB,8BAA8B,GAAGiB,OAAO,CAACC,YAAY,CACnD,6BAA6B,EAC7B,CAAC,QAAQ,CAAC,CACX;IACDjB,6BAA6B,GAAGgB,OAAO,CAACC,YAAY,CAClD,4BAA4B,EAC5B,CAAC,WAAW,CAAC,CACd;IAED7B,OAAO,CAAC8B,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACjC,uBAAuB,CAAC;MAClCkC,SAAS,EAAE,CACT9B,UAAU,EACV;QAAE+B,OAAO,EAAE9B,WAAW;QAAE+B,QAAQ,EAAExB;MAAc,CAAE,EAClD;QACEuB,OAAO,EAAE7B,2BAA2B;QACpC8B,QAAQ,EAAEvB;OACX,EACD;QACEsB,OAAO,EAAE5B,0BAA0B;QACnC6B,QAAQ,EAAEtB;OACX;KAEJ,CAAC;IACFJ,OAAO,GAAGR,OAAO,CAACmC,MAAM,CAACjC,UAAU,CAAC;IACpCO,qBAAqB,GAAGT,OAAO,CAACmC,MAAM,CAACpC,qBAAqB,CAAC;EAC/D,CAAC,CAAC;EAEFqC,SAAS,CAAC,MAAK;IACb3B,qBAAqB,CAAC4B,MAAM,EAAE;EAChC,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAAC/B,OAAO,CAAC,CAACgC,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFjC,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB+B,EAAE,CAAC,wBAAwB,EAAE,MAAK;MAChC,MAAMG,QAAQ,GAAU,CAAC1B,OAAO,CAAC;MAEjCP,OAAO,CAACkC,MAAM,EAAE,CAACC,SAAS,CAAEC,IAAI,IAAI;QAClCL,MAAM,CAACK,IAAI,CAAC,CAACC,OAAO,CAACJ,QAAQ,CAAC;MAChC,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGrC,qBAAqB,CAACsC,SAAS,CAAClC,OAAO,CAAC;MACpD0B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,QAAQ,CAAC;IACrB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFlC,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvB+B,EAAE,CAAC,kCAAkC,EAAE,MAAK;MAC1C9B,OAAO,CAAC4C,OAAO,CAAC,CAAC,CAAC,CAACT,SAAS,CAAEU,GAAG,IAAI;QACnCd,MAAM,CAACc,GAAG,CAAC,CAACR,OAAO,CAAC9B,OAAO,CAAC;MAC9B,CAAC,CAAC;MAEF,MAAM+B,GAAG,GAAGrC,qBAAqB,CAACsC,SAAS,CAAC,GAAGlC,OAAO,IAAI,CAAC;MAC3D0B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACpC,OAAO,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFR,QAAQ,CAAC,oBAAoB,EAAE,MAAK;IAClC+B,EAAE,CAAC,mCAAmC,EAAE,MAAK;MAC3C,MAAMrB,UAAU,GAAG,CAAC;MACpB,MAAMwB,QAAQ,GAAU,CAAC1B,OAAO,CAAC;MAEjCP,OAAO,CAAC8C,kBAAkB,CAACrC,UAAU,CAAC,CAAC0B,SAAS,CAAEC,IAAI,IAAI;QACxDL,MAAM,CAACK,IAAI,CAAC,CAACC,OAAO,CAACJ,QAAQ,CAAC;MAChC,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGrC,qBAAqB,CAACsC,SAAS,CACzC,GAAGlC,OAAO,aAAaI,UAAU,EAAE,CACpC;MACDsB,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,QAAQ,CAAC;IACrB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFlC,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB+B,EAAE,CAAC,kEAAkE,EAAE,MAAK;MAC1E,MAAMiB,MAAM,GAAoB;QAC9BtC,UAAU,EAAE,CAAC;QACbC,mBAAmB,EAAE,QAAQ;QAC7BC,wBAAwB,EAAE,iCAAiC;QAC3DC,KAAK,EAAE;OACR;MAED,MAAMoC,YAAY,GAAQ;QACxBxC,EAAE,EAAE,CAAC;QACL,GAAGuC;OACJ;MAED7C,cAAc,CAAC+C,cAAc,CAACC,GAAG,CAACC,WAAW,CAACtC,QAAQ,CAAC;MACvDT,6BAA6B,CAACgD,SAAS,CAACF,GAAG,CAACC,WAAW,CACrDrD,EAAE,CAACkB,eAAe,CAAC,CACpB;MAED,MAAMqC,gBAAgB,GAAyB;QAC7C7C,EAAE,EAAE,CAAC;QACLC,UAAU,EAAE,CAAC;QACb6C,aAAa,EAAE,CAAC;QAChBC,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBC,OAAO,EAAE,cAAc;QACvBC,SAAS,EAAE;OACZ;MAEDvD,8BAA8B,CAACwD,MAAM,CAACT,GAAG,CAACC,WAAW,CACnDrD,EAAE,CAACuD,gBAAgB,CAAC,CACrB;MAEDrD,OAAO,CAAC2D,MAAM,CAACZ,MAAM,CAAC,CAACZ,SAAS,CAAEU,GAAG,IAAI;QACvCd,MAAM,CAACc,GAAG,CAAC,CAACR,OAAO,CAACW,YAAY,CAAC;QACjCjB,MAAM,CAAC7B,cAAc,CAAC+C,cAAc,CAAC,CAACW,gBAAgB,EAAE;QACxD7B,MAAM,CAAC3B,6BAA6B,CAACgD,SAAS,CAAC,CAACS,oBAAoB,CAClE,iBAAiB,CAClB;QACD9B,MAAM,CAAC5B,8BAA8B,CAACwD,MAAM,CAAC,CAACC,gBAAgB,EAAE;MAClE,CAAC,CAAC;MAEF,MAAMtB,GAAG,GAAGrC,qBAAqB,CAACsC,SAAS,CAAClC,OAAO,CAAC;MACpD0B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACsB,IAAI,CAAC,CAACzB,OAAO,CAACU,MAAM,CAAC;MACxCT,GAAG,CAACK,KAAK,CAACK,YAAY,CAAC;IACzB,CAAC,CAAC;IAEFlB,EAAE,CAAC,yEAAyE,EAAE,MAAK;MACjF,MAAMiB,MAAM,GAAoB;QAC9BtC,UAAU,EAAE,CAAC;QACbC,mBAAmB,EAAE,QAAQ;QAC7BC,wBAAwB,EAAE,iCAAiC;QAC3DC,KAAK,EAAE;OACR;MAED,MAAMoC,YAAY,GAAQ;QACxBxC,EAAE,EAAE,CAAC;QACL,GAAGuC;OACJ;MAED7C,cAAc,CAAC+C,cAAc,CAACC,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;MAEnDnD,OAAO,CAAC2D,MAAM,CAACZ,MAAM,CAAC,CAACZ,SAAS,CAAEU,GAAG,IAAI;QACvCd,MAAM,CAACc,GAAG,CAAC,CAACR,OAAO,CAACW,YAAY,CAAC;QACjCjB,MAAM,CAAC7B,cAAc,CAAC+C,cAAc,CAAC,CAACW,gBAAgB,EAAE;QACxD7B,MAAM,CAAC3B,6BAA6B,CAACgD,SAAS,CAAC,CAACW,GAAG,CAACH,gBAAgB,EAAE;QACtE7B,MAAM,CAAC5B,8BAA8B,CAACwD,MAAM,CAAC,CAACI,GAAG,CAACH,gBAAgB,EAAE;MACtE,CAAC,CAAC;MAEF,MAAMtB,GAAG,GAAGrC,qBAAqB,CAACsC,SAAS,CAAClC,OAAO,CAAC;MACpD0B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACsB,IAAI,CAAC,CAACzB,OAAO,CAACU,MAAM,CAAC;MACxCT,GAAG,CAACK,KAAK,CAACK,YAAY,CAAC;IACzB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjD,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB+B,EAAE,CAAC,8FAA8F,EAAE,MAAK;MACtG,MAAMtB,EAAE,GAAG,CAAC;MACZ,MAAMwD,WAAW,GAAQ;QACvBxD,EAAE,EAAE,CAAC;QACLC,UAAU,EAAE,CAAC;QACbC,mBAAmB,EAAE,QAAQ;QAC7BC,wBAAwB,EAAE,sBAAsB;QAChDC,KAAK,EAAE;OACR;MAED,MAAMqD,SAAS,GAAiB;QAC9BxD,UAAU,EAAE,CAAC;QACbE,wBAAwB,EAAE,yBAAyB;QACnDC,KAAK,EAAE;OACR;MAED,MAAMoC,YAAY,GAAQ;QACxB,GAAGgB,WAAW;QACd,GAAGC;OACJ;MAED/D,cAAc,CAAC+C,cAAc,CAACC,GAAG,CAACC,WAAW,CAACtC,QAAQ,CAAC;MACvDT,6BAA6B,CAACgD,SAAS,CAACF,GAAG,CAACC,WAAW,CACrDrD,EAAE,CAACkB,eAAe,CAAC,CACpB;MAED,MAAMqC,gBAAgB,GAAyB;QAC7C7C,EAAE,EAAE,CAAC;QACLC,UAAU,EAAE,CAAC;QACb6C,aAAa,EAAEtC,eAAe,CAACR,EAAE;QACjC+C,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBC,OAAO,EAAE,cAAc;QACvBC,SAAS,EAAE7C,QAAQ,CAACL;OACrB;MAEDL,8BAA8B,CAACwD,MAAM,CAACT,GAAG,CAACC,WAAW,CACnDrD,EAAE,CAACuD,gBAAgB,CAAC,CACrB;MAED,IAAIa,YAA6B;MACjClE,OAAO,CAACmE,MAAM,CAAC3D,EAAE,EAAEyD,SAAS,CAAC,CAAC9B,SAAS,CAAEiC,MAAM,IAAI;QACjDF,YAAY,GAAGE,MAAM;MACvB,CAAC,CAAC;MAEF,MAAMC,MAAM,GAAGpE,qBAAqB,CAACsC,SAAS,CAAC,GAAGlC,OAAO,IAAIG,EAAE,EAAE,CAAC;MAClEuB,MAAM,CAACsC,MAAM,CAAC7B,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACzC2B,MAAM,CAAC1B,KAAK,CAACqB,WAAW,CAAC;MAEzB,MAAMM,MAAM,GAAGrE,qBAAqB,CAACsC,SAAS,CAAC,GAAGlC,OAAO,IAAIG,EAAE,EAAE,CAAC;MAClEuB,MAAM,CAACuC,MAAM,CAAC9B,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACzCX,MAAM,CAACuC,MAAM,CAAC9B,OAAO,CAACsB,IAAI,CAAC,CAACzB,OAAO,CAAC4B,SAAS,CAAC;MAC9CK,MAAM,CAAC3B,KAAK,CAACK,YAAY,CAAC;MAE1BjB,MAAM,CAACmC,YAAY,CAAC,CAAC7B,OAAO,CAACW,YAAY,CAAC;MAC1CjB,MAAM,CAAC3B,6BAA6B,CAACgD,SAAS,CAAC,CAACS,oBAAoB,CAClE,gBAAgB,CACjB;MACD9B,MAAM,CAAC5B,8BAA8B,CAACwD,MAAM,CAAC,CAACC,gBAAgB,EAAE;IAClE,CAAC,CAAC;IAEF9B,EAAE,CAAC,8EAA8E,EAAE,MAAK;MACtF,MAAMtB,EAAE,GAAG,CAAC;MACZ,MAAMwD,WAAW,GAAQ;QACvBxD,EAAE,EAAE,CAAC;QACLC,UAAU,EAAE,CAAC;QACbC,mBAAmB,EAAE,QAAQ;QAC7BC,wBAAwB,EAAE,sBAAsB;QAChDC,KAAK,EAAE;OACR;MAED,MAAMqD,SAAS,GAAiB;QAC9BxD,UAAU,EAAE,CAAC;QACbC,mBAAmB,EAAE,QAAQ;QAC7BC,wBAAwB,EAAE,sBAAsB;QAChDC,KAAK,EAAE;OACR;MAED,MAAMoC,YAAY,GAAQ;QAAE,GAAGgB;MAAW,CAAE;MAE5C9D,cAAc,CAAC+C,cAAc,CAACC,GAAG,CAACC,WAAW,CAACtC,QAAQ,CAAC;MAEvD,IAAI0D,IAAI,GAAG,KAAK;MAChB,IAAIL,YAA6B;MAEjClE,OAAO,CAACmE,MAAM,CAAC3D,EAAE,EAAEyD,SAAS,CAAC,CAAC9B,SAAS,CAAC;QACtCqC,IAAI,EAAGJ,MAAM,IAAI;UACfF,YAAY,GAAGE,MAAM;UACrBG,IAAI,GAAG,IAAI;QACb,CAAC;QACDE,KAAK,EAAGC,GAAG,IAAI;UACbC,IAAI,CAAC,2BAA2B,GAAGD,GAAG,CAAC;QACzC;OACD,CAAC;MAEF,MAAML,MAAM,GAAGpE,qBAAqB,CAACsC,SAAS,CAAC,GAAGlC,OAAO,IAAIG,EAAE,EAAE,CAAC;MAClEuB,MAAM,CAACsC,MAAM,CAAC7B,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACzC2B,MAAM,CAAC1B,KAAK,CAACqB,WAAW,CAAC;MAEzB,MAAMM,MAAM,GAAGrE,qBAAqB,CAACsC,SAAS,CAAC,GAAGlC,OAAO,IAAIG,EAAE,EAAE,CAAC;MAClEuB,MAAM,CAACuC,MAAM,CAAC9B,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACzCX,MAAM,CAACuC,MAAM,CAAC9B,OAAO,CAACsB,IAAI,CAAC,CAACzB,OAAO,CAAC4B,SAAS,CAAC;MAC9CK,MAAM,CAAC3B,KAAK,CAACK,YAAY,CAAC;MAE1BjB,MAAM,CAACwC,IAAI,CAAC,CAACK,QAAQ,EAAE;MACvB7C,MAAM,CAACmC,YAAY,CAAC,CAAC7B,OAAO,CAACW,YAAY,CAAC;MAC1CjB,MAAM,CAAC3B,6BAA6B,CAACgD,SAAS,CAAC,CAACW,GAAG,CAACH,gBAAgB,EAAE;MACtE7B,MAAM,CAAC5B,8BAA8B,CAACwD,MAAM,CAAC,CAACI,GAAG,CAACH,gBAAgB,EAAE;IACtE,CAAC,CAAC;IAEF9B,EAAE,CAAC,4EAA4E,EAAE,MAAK;MACpF,MAAMtB,EAAE,GAAG,CAAC;MACZ,MAAMwD,WAAW,GAAQ;QACvBxD,EAAE,EAAE,CAAC;QACLC,UAAU,EAAE,CAAC;QACbC,mBAAmB,EAAE,QAAQ;QAC7BC,wBAAwB,EAAE,sBAAsB;QAChDC,KAAK,EAAE;OACR;MAED,MAAMqD,SAAS,GAAiB;QAC9BxD,UAAU,EAAE,CAAC;QACbE,wBAAwB,EAAE,qBAAqB;QAC/CC,KAAK,EAAE;OACR;MAED,MAAMoC,YAAY,GAAQ;QACxB,GAAGgB,WAAW;QACd,GAAGC;OACJ;MAED/D,cAAc,CAAC+C,cAAc,CAACC,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;MAEnD,IAAIe,YAA6B;MACjClE,OAAO,CAACmE,MAAM,CAAC3D,EAAE,EAAEyD,SAAS,CAAC,CAAC9B,SAAS,CAAEiC,MAAM,IAAI;QACjDF,YAAY,GAAGE,MAAM;MACvB,CAAC,CAAC;MAEF,MAAMC,MAAM,GAAGpE,qBAAqB,CAACsC,SAAS,CAAC,GAAGlC,OAAO,IAAIG,EAAE,EAAE,CAAC;MAClEuB,MAAM,CAACsC,MAAM,CAAC7B,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACzC2B,MAAM,CAAC1B,KAAK,CAACqB,WAAW,CAAC;MAEzB,MAAMM,MAAM,GAAGrE,qBAAqB,CAACsC,SAAS,CAAC,GAAGlC,OAAO,IAAIG,EAAE,EAAE,CAAC;MAClEuB,MAAM,CAACuC,MAAM,CAAC9B,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACzCX,MAAM,CAACuC,MAAM,CAAC9B,OAAO,CAACsB,IAAI,CAAC,CAACzB,OAAO,CAAC4B,SAAS,CAAC;MAC9CK,MAAM,CAAC3B,KAAK,CAACK,YAAY,CAAC;MAE1BjB,MAAM,CAACmC,YAAY,CAAC,CAAC7B,OAAO,CAACW,YAAY,CAAC;MAC1CjB,MAAM,CAAC3B,6BAA6B,CAACgD,SAAS,CAAC,CAACW,GAAG,CAACH,gBAAgB,EAAE;MACtE7B,MAAM,CAAC5B,8BAA8B,CAACwD,MAAM,CAAC,CAACI,GAAG,CAACH,gBAAgB,EAAE;IACtE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7D,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB+B,EAAE,CAAC,qBAAqB,EAAE,MAAK;MAC7B,MAAMtB,EAAE,GAAG,CAAC;MAEZR,OAAO,CAAC6E,MAAM,CAACrE,EAAE,CAAC,CAAC2B,SAAS,CAAE2C,QAAQ,IAAI;QACxC/C,MAAM,CAAC+C,QAAQ,CAAC,CAACC,QAAQ,EAAE;MAC7B,CAAC,CAAC;MAEF,MAAMzC,GAAG,GAAGrC,qBAAqB,CAACsC,SAAS,CAAC,GAAGlC,OAAO,IAAIG,EAAE,EAAE,CAAC;MAC/DuB,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}