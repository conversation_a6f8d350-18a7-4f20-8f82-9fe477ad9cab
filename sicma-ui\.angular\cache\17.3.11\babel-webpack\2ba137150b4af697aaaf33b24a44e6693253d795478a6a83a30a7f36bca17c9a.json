{"ast": null, "code": "function cov_2456r4fjl6() {\n  var path = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-dashboard\\\\components\\\\monthly-reports-tab\\\\monthly-reports-tab.component.ts\";\n  var hash = \"3294b6d67a0630b3fa7a0955d7dc403d2cb4a052\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-dashboard\\\\components\\\\monthly-reports-tab\\\\monthly-reports-tab.component.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 28,\n          column: 33\n        },\n        end: {\n          line: 302,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 30,\n          column: 8\n        },\n        end: {\n          line: 30,\n          column: 29\n        }\n      },\n      \"2\": {\n        start: {\n          line: 31,\n          column: 8\n        },\n        end: {\n          line: 31,\n          column: 27\n        }\n      },\n      \"3\": {\n        start: {\n          line: 32,\n          column: 8\n        },\n        end: {\n          line: 32,\n          column: 57\n        }\n      },\n      \"4\": {\n        start: {\n          line: 33,\n          column: 8\n        },\n        end: {\n          line: 33,\n          column: 67\n        }\n      },\n      \"5\": {\n        start: {\n          line: 34,\n          column: 8\n        },\n        end: {\n          line: 34,\n          column: 67\n        }\n      },\n      \"6\": {\n        start: {\n          line: 35,\n          column: 8\n        },\n        end: {\n          line: 35,\n          column: 43\n        }\n      },\n      \"7\": {\n        start: {\n          line: 36,\n          column: 8\n        },\n        end: {\n          line: 36,\n          column: 31\n        }\n      },\n      \"8\": {\n        start: {\n          line: 37,\n          column: 8\n        },\n        end: {\n          line: 37,\n          column: 69\n        }\n      },\n      \"9\": {\n        start: {\n          line: 38,\n          column: 8\n        },\n        end: {\n          line: 38,\n          column: 39\n        }\n      },\n      \"10\": {\n        start: {\n          line: 39,\n          column: 8\n        },\n        end: {\n          line: 39,\n          column: 33\n        }\n      },\n      \"11\": {\n        start: {\n          line: 40,\n          column: 8\n        },\n        end: {\n          line: 47,\n          column: 10\n        }\n      },\n      \"12\": {\n        start: {\n          line: 48,\n          column: 8\n        },\n        end: {\n          line: 48,\n          column: 51\n        }\n      },\n      \"13\": {\n        start: {\n          line: 51,\n          column: 8\n        },\n        end: {\n          line: 56,\n          column: 9\n        }\n      },\n      \"14\": {\n        start: {\n          line: 52,\n          column: 12\n        },\n        end: {\n          line: 52,\n          column: 55\n        }\n      },\n      \"15\": {\n        start: {\n          line: 53,\n          column: 12\n        },\n        end: {\n          line: 55,\n          column: 13\n        }\n      },\n      \"16\": {\n        start: {\n          line: 54,\n          column: 16\n        },\n        end: {\n          line: 54,\n          column: 54\n        }\n      },\n      \"17\": {\n        start: {\n          line: 59,\n          column: 8\n        },\n        end: {\n          line: 59,\n          column: 51\n        }\n      },\n      \"18\": {\n        start: {\n          line: 60,\n          column: 8\n        },\n        end: {\n          line: 60,\n          column: 41\n        }\n      },\n      \"19\": {\n        start: {\n          line: 63,\n          column: 28\n        },\n        end: {\n          line: 63,\n          column: 46\n        }\n      },\n      \"20\": {\n        start: {\n          line: 64,\n          column: 8\n        },\n        end: {\n          line: 64,\n          column: 66\n        }\n      },\n      \"21\": {\n        start: {\n          line: 65,\n          column: 8\n        },\n        end: {\n          line: 67,\n          column: 9\n        }\n      },\n      \"22\": {\n        start: {\n          line: 66,\n          column: 12\n        },\n        end: {\n          line: 66,\n          column: 50\n        }\n      },\n      \"23\": {\n        start: {\n          line: 70,\n          column: 26\n        },\n        end: {\n          line: 76,\n          column: 10\n        }\n      },\n      \"24\": {\n        start: {\n          line: 77,\n          column: 8\n        },\n        end: {\n          line: 91,\n          column: 11\n        }\n      },\n      \"25\": {\n        start: {\n          line: 78,\n          column: 12\n        },\n        end: {\n          line: 90,\n          column: 13\n        }\n      },\n      \"26\": {\n        start: {\n          line: 79,\n          column: 16\n        },\n        end: {\n          line: 89,\n          column: 19\n        }\n      },\n      \"27\": {\n        start: {\n          line: 83,\n          column: 24\n        },\n        end: {\n          line: 83,\n          column: 61\n        }\n      },\n      \"28\": {\n        start: {\n          line: 84,\n          column: 24\n        },\n        end: {\n          line: 84,\n          column: 62\n        }\n      },\n      \"29\": {\n        start: {\n          line: 87,\n          column: 24\n        },\n        end: {\n          line: 87,\n          column: 108\n        }\n      },\n      \"30\": {\n        start: {\n          line: 94,\n          column: 8\n        },\n        end: {\n          line: 94,\n          column: 28\n        }\n      },\n      \"31\": {\n        start: {\n          line: 95,\n          column: 8\n        },\n        end: {\n          line: 99,\n          column: 9\n        }\n      },\n      \"32\": {\n        start: {\n          line: 96,\n          column: 12\n        },\n        end: {\n          line: 96,\n          column: 88\n        }\n      },\n      \"33\": {\n        start: {\n          line: 97,\n          column: 12\n        },\n        end: {\n          line: 97,\n          column: 32\n        }\n      },\n      \"34\": {\n        start: {\n          line: 98,\n          column: 12\n        },\n        end: {\n          line: 98,\n          column: 19\n        }\n      },\n      \"35\": {\n        start: {\n          line: 100,\n          column: 8\n        },\n        end: {\n          line: 247,\n          column: 11\n        }\n      },\n      \"36\": {\n        start: {\n          line: 103,\n          column: 12\n        },\n        end: {\n          line: 105,\n          column: 13\n        }\n      },\n      \"37\": {\n        start: {\n          line: 104,\n          column: 16\n        },\n        end: {\n          line: 104,\n          column: 76\n        }\n      },\n      \"38\": {\n        start: {\n          line: 106,\n          column: 12\n        },\n        end: {\n          line: 152,\n          column: 13\n        }\n      },\n      \"39\": {\n        start: {\n          line: 107,\n          column: 16\n        },\n        end: {\n          line: 107,\n          column: 36\n        }\n      },\n      \"40\": {\n        start: {\n          line: 108,\n          column: 34\n        },\n        end: {\n          line: 116,\n          column: 18\n        }\n      },\n      \"41\": {\n        start: {\n          line: 117,\n          column: 16\n        },\n        end: {\n          line: 130,\n          column: 20\n        }\n      },\n      \"42\": {\n        start: {\n          line: 118,\n          column: 20\n        },\n        end: {\n          line: 129,\n          column: 21\n        }\n      },\n      \"43\": {\n        start: {\n          line: 119,\n          column: 24\n        },\n        end: {\n          line: 119,\n          column: 44\n        }\n      },\n      \"44\": {\n        start: {\n          line: 120,\n          column: 24\n        },\n        end: {\n          line: 125,\n          column: 29\n        }\n      },\n      \"45\": {\n        start: {\n          line: 122,\n          column: 59\n        },\n        end: {\n          line: 125,\n          column: 25\n        }\n      },\n      \"46\": {\n        start: {\n          line: 128,\n          column: 24\n        },\n        end: {\n          line: 128,\n          column: 34\n        }\n      },\n      \"47\": {\n        start: {\n          line: 133,\n          column: 16\n        },\n        end: {\n          line: 133,\n          column: 64\n        }\n      },\n      \"48\": {\n        start: {\n          line: 133,\n          column: 51\n        },\n        end: {\n          line: 133,\n          column: 62\n        }\n      },\n      \"49\": {\n        start: {\n          line: 134,\n          column: 35\n        },\n        end: {\n          line: 134,\n          column: 86\n        }\n      },\n      \"50\": {\n        start: {\n          line: 135,\n          column: 40\n        },\n        end: {\n          line: 137,\n          column: 23\n        }\n      },\n      \"51\": {\n        start: {\n          line: 138,\n          column: 34\n        },\n        end: {\n          line: 141,\n          column: 17\n        }\n      },\n      \"52\": {\n        start: {\n          line: 142,\n          column: 16\n        },\n        end: {\n          line: 151,\n          column: 20\n        }\n      },\n      \"53\": {\n        start: {\n          line: 143,\n          column: 20\n        },\n        end: {\n          line: 150,\n          column: 25\n        }\n      },\n      \"54\": {\n        start: {\n          line: 145,\n          column: 59\n        },\n        end: {\n          line: 150,\n          column: 21\n        }\n      },\n      \"55\": {\n        start: {\n          line: 153,\n          column: 27\n        },\n        end: {\n          line: 153,\n          column: 46\n        }\n      },\n      \"56\": {\n        start: {\n          line: 156,\n          column: 16\n        },\n        end: {\n          line: 242,\n          column: 17\n        }\n      },\n      \"57\": {\n        start: {\n          line: 157,\n          column: 20\n        },\n        end: {\n          line: 157,\n          column: 57\n        }\n      },\n      \"58\": {\n        start: {\n          line: 158,\n          column: 20\n        },\n        end: {\n          line: 158,\n          column: 63\n        }\n      },\n      \"59\": {\n        start: {\n          line: 159,\n          column: 20\n        },\n        end: {\n          line: 170,\n          column: 21\n        }\n      },\n      \"60\": {\n        start: {\n          line: 160,\n          column: 45\n        },\n        end: {\n          line: 160,\n          column: 123\n        }\n      },\n      \"61\": {\n        start: {\n          line: 160,\n          column: 77\n        },\n        end: {\n          line: 160,\n          column: 122\n        }\n      },\n      \"62\": {\n        start: {\n          line: 161,\n          column: 24\n        },\n        end: {\n          line: 169,\n          column: 25\n        }\n      },\n      \"63\": {\n        start: {\n          line: 162,\n          column: 28\n        },\n        end: {\n          line: 167,\n          column: 29\n        }\n      },\n      \"64\": {\n        start: {\n          line: 163,\n          column: 32\n        },\n        end: {\n          line: 163,\n          column: 91\n        }\n      },\n      \"65\": {\n        start: {\n          line: 166,\n          column: 32\n        },\n        end: {\n          line: 166,\n          column: 95\n        }\n      },\n      \"66\": {\n        start: {\n          line: 168,\n          column: 28\n        },\n        end: {\n          line: 168,\n          column: 69\n        }\n      },\n      \"67\": {\n        start: {\n          line: 172,\n          column: 21\n        },\n        end: {\n          line: 242,\n          column: 17\n        }\n      },\n      \"68\": {\n        start: {\n          line: 176,\n          column: 20\n        },\n        end: {\n          line: 241,\n          column: 21\n        }\n      },\n      \"69\": {\n        start: {\n          line: 177,\n          column: 24\n        },\n        end: {\n          line: 177,\n          column: 45\n        }\n      },\n      \"70\": {\n        start: {\n          line: 178,\n          column: 24\n        },\n        end: {\n          line: 240,\n          column: 27\n        }\n      },\n      \"71\": {\n        start: {\n          line: 190,\n          column: 52\n        },\n        end: {\n          line: 190,\n          column: 85\n        }\n      },\n      \"72\": {\n        start: {\n          line: 191,\n          column: 32\n        },\n        end: {\n          line: 194,\n          column: 33\n        }\n      },\n      \"73\": {\n        start: {\n          line: 192,\n          column: 36\n        },\n        end: {\n          line: 192,\n          column: 107\n        }\n      },\n      \"74\": {\n        start: {\n          line: 193,\n          column: 36\n        },\n        end: {\n          line: 193,\n          column: 43\n        }\n      },\n      \"75\": {\n        start: {\n          line: 195,\n          column: 32\n        },\n        end: {\n          line: 235,\n          column: 35\n        }\n      },\n      \"76\": {\n        start: {\n          line: 205,\n          column: 40\n        },\n        end: {\n          line: 217,\n          column: 43\n        }\n      },\n      \"77\": {\n        start: {\n          line: 209,\n          column: 48\n        },\n        end: {\n          line: 209,\n          column: 85\n        }\n      },\n      \"78\": {\n        start: {\n          line: 210,\n          column: 48\n        },\n        end: {\n          line: 210,\n          column: 91\n        }\n      },\n      \"79\": {\n        start: {\n          line: 211,\n          column: 48\n        },\n        end: {\n          line: 211,\n          column: 107\n        }\n      },\n      \"80\": {\n        start: {\n          line: 212,\n          column: 48\n        },\n        end: {\n          line: 212,\n          column: 90\n        }\n      },\n      \"81\": {\n        start: {\n          line: 215,\n          column: 48\n        },\n        end: {\n          line: 215,\n          column: 132\n        }\n      },\n      \"82\": {\n        start: {\n          line: 220,\n          column: 40\n        },\n        end: {\n          line: 220,\n          column: 125\n        }\n      },\n      \"83\": {\n        start: {\n          line: 221,\n          column: 40\n        },\n        end: {\n          line: 233,\n          column: 43\n        }\n      },\n      \"84\": {\n        start: {\n          line: 225,\n          column: 48\n        },\n        end: {\n          line: 225,\n          column: 85\n        }\n      },\n      \"85\": {\n        start: {\n          line: 226,\n          column: 48\n        },\n        end: {\n          line: 226,\n          column: 91\n        }\n      },\n      \"86\": {\n        start: {\n          line: 227,\n          column: 48\n        },\n        end: {\n          line: 227,\n          column: 107\n        }\n      },\n      \"87\": {\n        start: {\n          line: 228,\n          column: 48\n        },\n        end: {\n          line: 228,\n          column: 90\n        }\n      },\n      \"88\": {\n        start: {\n          line: 231,\n          column: 48\n        },\n        end: {\n          line: 231,\n          column: 132\n        }\n      },\n      \"89\": {\n        start: {\n          line: 238,\n          column: 32\n        },\n        end: {\n          line: 238,\n          column: 108\n        }\n      },\n      \"90\": {\n        start: {\n          line: 245,\n          column: 16\n        },\n        end: {\n          line: 245,\n          column: 92\n        }\n      },\n      \"91\": {\n        start: {\n          line: 250,\n          column: 8\n        },\n        end: {\n          line: 251,\n          column: 24\n        }\n      },\n      \"92\": {\n        start: {\n          line: 251,\n          column: 12\n        },\n        end: {\n          line: 251,\n          column: 24\n        }\n      },\n      \"93\": {\n        start: {\n          line: 252,\n          column: 30\n        },\n        end: {\n          line: 252,\n          column: 102\n        }\n      },\n      \"94\": {\n        start: {\n          line: 252,\n          column: 70\n        },\n        end: {\n          line: 252,\n          column: 101\n        }\n      },\n      \"95\": {\n        start: {\n          line: 253,\n          column: 27\n        },\n        end: {\n          line: 253,\n          column: 43\n        }\n      },\n      \"96\": {\n        start: {\n          line: 254,\n          column: 8\n        },\n        end: {\n          line: 254,\n          column: 67\n        }\n      },\n      \"97\": {\n        start: {\n          line: 257,\n          column: 8\n        },\n        end: {\n          line: 258,\n          column: 19\n        }\n      },\n      \"98\": {\n        start: {\n          line: 258,\n          column: 12\n        },\n        end: {\n          line: 258,\n          column: 19\n        }\n      },\n      \"99\": {\n        start: {\n          line: 259,\n          column: 8\n        },\n        end: {\n          line: 259,\n          column: 28\n        }\n      },\n      \"100\": {\n        start: {\n          line: 260,\n          column: 8\n        },\n        end: {\n          line: 276,\n          column: 11\n        }\n      },\n      \"101\": {\n        start: {\n          line: 262,\n          column: 33\n        },\n        end: {\n          line: 262,\n          column: 52\n        }\n      },\n      \"102\": {\n        start: {\n          line: 265,\n          column: 28\n        },\n        end: {\n          line: 265,\n          column: 60\n        }\n      },\n      \"103\": {\n        start: {\n          line: 266,\n          column: 29\n        },\n        end: {\n          line: 266,\n          column: 56\n        }\n      },\n      \"104\": {\n        start: {\n          line: 267,\n          column: 16\n        },\n        end: {\n          line: 267,\n          column: 32\n        }\n      },\n      \"105\": {\n        start: {\n          line: 268,\n          column: 16\n        },\n        end: {\n          line: 268,\n          column: 77\n        }\n      },\n      \"106\": {\n        start: {\n          line: 269,\n          column: 16\n        },\n        end: {\n          line: 269,\n          column: 29\n        }\n      },\n      \"107\": {\n        start: {\n          line: 270,\n          column: 16\n        },\n        end: {\n          line: 270,\n          column: 48\n        }\n      },\n      \"108\": {\n        start: {\n          line: 271,\n          column: 16\n        },\n        end: {\n          line: 271,\n          column: 66\n        }\n      },\n      \"109\": {\n        start: {\n          line: 274,\n          column: 16\n        },\n        end: {\n          line: 274,\n          column: 85\n        }\n      },\n      \"110\": {\n        start: {\n          line: 279,\n          column: 8\n        },\n        end: {\n          line: 282,\n          column: 11\n        }\n      },\n      \"111\": {\n        start: {\n          line: 284,\n          column: 13\n        },\n        end: {\n          line: 294,\n          column: 6\n        }\n      },\n      \"112\": {\n        start: {\n          line: 284,\n          column: 41\n        },\n        end: {\n          line: 294,\n          column: 5\n        }\n      },\n      \"113\": {\n        start: {\n          line: 295,\n          column: 13\n        },\n        end: {\n          line: 301,\n          column: 6\n        }\n      },\n      \"114\": {\n        start: {\n          line: 303,\n          column: 0\n        },\n        end: {\n          line: 323,\n          column: 31\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 29,\n            column: 4\n          },\n          end: {\n            line: 29,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 29,\n            column: 172\n          },\n          end: {\n            line: 49,\n            column: 5\n          }\n        },\n        line: 29\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 50,\n            column: 4\n          },\n          end: {\n            line: 50,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 50,\n            column: 25\n          },\n          end: {\n            line: 57,\n            column: 5\n          }\n        },\n        line: 50\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 58,\n            column: 4\n          },\n          end: {\n            line: 58,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 58,\n            column: 22\n          },\n          end: {\n            line: 61,\n            column: 5\n          }\n        },\n        line: 58\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 62,\n            column: 4\n          },\n          end: {\n            line: 62,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 62,\n            column: 23\n          },\n          end: {\n            line: 68,\n            column: 5\n          }\n        },\n        line: 62\n      },\n      \"4\": {\n        name: \"(anonymous_4)\",\n        decl: {\n          start: {\n            line: 69,\n            column: 4\n          },\n          end: {\n            line: 69,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 69,\n            column: 34\n          },\n          end: {\n            line: 92,\n            column: 5\n          }\n        },\n        line: 69\n      },\n      \"5\": {\n        name: \"(anonymous_5)\",\n        decl: {\n          start: {\n            line: 77,\n            column: 42\n          },\n          end: {\n            line: 77,\n            column: 43\n          }\n        },\n        loc: {\n          start: {\n            line: 77,\n            column: 54\n          },\n          end: {\n            line: 91,\n            column: 9\n          }\n        },\n        line: 77\n      },\n      \"6\": {\n        name: \"(anonymous_6)\",\n        decl: {\n          start: {\n            line: 82,\n            column: 26\n          },\n          end: {\n            line: 82,\n            column: 27\n          }\n        },\n        loc: {\n          start: {\n            line: 82,\n            column: 46\n          },\n          end: {\n            line: 85,\n            column: 21\n          }\n        },\n        line: 82\n      },\n      \"7\": {\n        name: \"(anonymous_7)\",\n        decl: {\n          start: {\n            line: 86,\n            column: 27\n          },\n          end: {\n            line: 86,\n            column: 28\n          }\n        },\n        loc: {\n          start: {\n            line: 86,\n            column: 38\n          },\n          end: {\n            line: 88,\n            column: 21\n          }\n        },\n        line: 86\n      },\n      \"8\": {\n        name: \"(anonymous_8)\",\n        decl: {\n          start: {\n            line: 93,\n            column: 4\n          },\n          end: {\n            line: 93,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 93,\n            column: 22\n          },\n          end: {\n            line: 248,\n            column: 5\n          }\n        },\n        line: 93\n      },\n      \"9\": {\n        name: \"(anonymous_9)\",\n        decl: {\n          start: {\n            line: 102,\n            column: 28\n          },\n          end: {\n            line: 102,\n            column: 29\n          }\n        },\n        loc: {\n          start: {\n            line: 102,\n            column: 52\n          },\n          end: {\n            line: 153,\n            column: 9\n          }\n        },\n        line: 102\n      },\n      \"10\": {\n        name: \"(anonymous_10)\",\n        decl: {\n          start: {\n            line: 117,\n            column: 62\n          },\n          end: {\n            line: 117,\n            column: 63\n          }\n        },\n        loc: {\n          start: {\n            line: 117,\n            column: 82\n          },\n          end: {\n            line: 130,\n            column: 17\n          }\n        },\n        line: 117\n      },\n      \"11\": {\n        name: \"(anonymous_11)\",\n        decl: {\n          start: {\n            line: 122,\n            column: 38\n          },\n          end: {\n            line: 122,\n            column: 39\n          }\n        },\n        loc: {\n          start: {\n            line: 122,\n            column: 59\n          },\n          end: {\n            line: 125,\n            column: 25\n          }\n        },\n        line: 122\n      },\n      \"12\": {\n        name: \"(anonymous_12)\",\n        decl: {\n          start: {\n            line: 133,\n            column: 41\n          },\n          end: {\n            line: 133,\n            column: 42\n          }\n        },\n        loc: {\n          start: {\n            line: 133,\n            column: 51\n          },\n          end: {\n            line: 133,\n            column: 62\n          }\n        },\n        line: 133\n      },\n      \"13\": {\n        name: \"(anonymous_13)\",\n        decl: {\n          start: {\n            line: 142,\n            column: 75\n          },\n          end: {\n            line: 142,\n            column: 76\n          }\n        },\n        loc: {\n          start: {\n            line: 142,\n            column: 87\n          },\n          end: {\n            line: 151,\n            column: 17\n          }\n        },\n        line: 142\n      },\n      \"14\": {\n        name: \"(anonymous_14)\",\n        decl: {\n          start: {\n            line: 145,\n            column: 34\n          },\n          end: {\n            line: 145,\n            column: 35\n          }\n        },\n        loc: {\n          start: {\n            line: 145,\n            column: 59\n          },\n          end: {\n            line: 150,\n            column: 21\n          }\n        },\n        line: 145\n      },\n      \"15\": {\n        name: \"(anonymous_15)\",\n        decl: {\n          start: {\n            line: 153,\n            column: 21\n          },\n          end: {\n            line: 153,\n            column: 22\n          }\n        },\n        loc: {\n          start: {\n            line: 153,\n            column: 27\n          },\n          end: {\n            line: 153,\n            column: 46\n          }\n        },\n        line: 153\n      },\n      \"16\": {\n        name: \"(anonymous_16)\",\n        decl: {\n          start: {\n            line: 155,\n            column: 18\n          },\n          end: {\n            line: 155,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 155,\n            column: 124\n          },\n          end: {\n            line: 243,\n            column: 13\n          }\n        },\n        line: 155\n      },\n      \"17\": {\n        name: \"(anonymous_17)\",\n        decl: {\n          start: {\n            line: 160,\n            column: 70\n          },\n          end: {\n            line: 160,\n            column: 71\n          }\n        },\n        loc: {\n          start: {\n            line: 160,\n            column: 77\n          },\n          end: {\n            line: 160,\n            column: 122\n          }\n        },\n        line: 160\n      },\n      \"18\": {\n        name: \"(anonymous_18)\",\n        decl: {\n          start: {\n            line: 189,\n            column: 34\n          },\n          end: {\n            line: 189,\n            column: 35\n          }\n        },\n        loc: {\n          start: {\n            line: 189,\n            column: 53\n          },\n          end: {\n            line: 236,\n            column: 29\n          }\n        },\n        line: 189\n      },\n      \"19\": {\n        name: \"(anonymous_19)\",\n        decl: {\n          start: {\n            line: 204,\n            column: 42\n          },\n          end: {\n            line: 204,\n            column: 43\n          }\n        },\n        loc: {\n          start: {\n            line: 204,\n            column: 48\n          },\n          end: {\n            line: 218,\n            column: 37\n          }\n        },\n        line: 204\n      },\n      \"20\": {\n        name: \"(anonymous_20)\",\n        decl: {\n          start: {\n            line: 208,\n            column: 50\n          },\n          end: {\n            line: 208,\n            column: 51\n          }\n        },\n        loc: {\n          start: {\n            line: 208,\n            column: 70\n          },\n          end: {\n            line: 213,\n            column: 45\n          }\n        },\n        line: 208\n      },\n      \"21\": {\n        name: \"(anonymous_21)\",\n        decl: {\n          start: {\n            line: 214,\n            column: 51\n          },\n          end: {\n            line: 214,\n            column: 52\n          }\n        },\n        loc: {\n          start: {\n            line: 214,\n            column: 62\n          },\n          end: {\n            line: 216,\n            column: 45\n          }\n        },\n        line: 214\n      },\n      \"22\": {\n        name: \"(anonymous_22)\",\n        decl: {\n          start: {\n            line: 219,\n            column: 43\n          },\n          end: {\n            line: 219,\n            column: 44\n          }\n        },\n        loc: {\n          start: {\n            line: 219,\n            column: 54\n          },\n          end: {\n            line: 234,\n            column: 37\n          }\n        },\n        line: 219\n      },\n      \"23\": {\n        name: \"(anonymous_23)\",\n        decl: {\n          start: {\n            line: 224,\n            column: 50\n          },\n          end: {\n            line: 224,\n            column: 51\n          }\n        },\n        loc: {\n          start: {\n            line: 224,\n            column: 70\n          },\n          end: {\n            line: 229,\n            column: 45\n          }\n        },\n        line: 224\n      },\n      \"24\": {\n        name: \"(anonymous_24)\",\n        decl: {\n          start: {\n            line: 230,\n            column: 51\n          },\n          end: {\n            line: 230,\n            column: 52\n          }\n        },\n        loc: {\n          start: {\n            line: 230,\n            column: 62\n          },\n          end: {\n            line: 232,\n            column: 45\n          }\n        },\n        line: 230\n      },\n      \"25\": {\n        name: \"(anonymous_25)\",\n        decl: {\n          start: {\n            line: 237,\n            column: 35\n          },\n          end: {\n            line: 237,\n            column: 36\n          }\n        },\n        loc: {\n          start: {\n            line: 237,\n            column: 46\n          },\n          end: {\n            line: 239,\n            column: 29\n          }\n        },\n        line: 237\n      },\n      \"26\": {\n        name: \"(anonymous_26)\",\n        decl: {\n          start: {\n            line: 244,\n            column: 19\n          },\n          end: {\n            line: 244,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 244,\n            column: 30\n          },\n          end: {\n            line: 246,\n            column: 13\n          }\n        },\n        line: 244\n      },\n      \"27\": {\n        name: \"(anonymous_27)\",\n        decl: {\n          start: {\n            line: 249,\n            column: 4\n          },\n          end: {\n            line: 249,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 249,\n            column: 29\n          },\n          end: {\n            line: 255,\n            column: 5\n          }\n        },\n        line: 249\n      },\n      \"28\": {\n        name: \"(anonymous_28)\",\n        decl: {\n          start: {\n            line: 252,\n            column: 60\n          },\n          end: {\n            line: 252,\n            column: 61\n          }\n        },\n        loc: {\n          start: {\n            line: 252,\n            column: 70\n          },\n          end: {\n            line: 252,\n            column: 101\n          }\n        },\n        line: 252\n      },\n      \"29\": {\n        name: \"(anonymous_29)\",\n        decl: {\n          start: {\n            line: 256,\n            column: 4\n          },\n          end: {\n            line: 256,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 256,\n            column: 24\n          },\n          end: {\n            line: 277,\n            column: 5\n          }\n        },\n        line: 256\n      },\n      \"30\": {\n        name: \"(anonymous_30)\",\n        decl: {\n          start: {\n            line: 262,\n            column: 27\n          },\n          end: {\n            line: 262,\n            column: 28\n          }\n        },\n        loc: {\n          start: {\n            line: 262,\n            column: 33\n          },\n          end: {\n            line: 262,\n            column: 52\n          }\n        },\n        line: 262\n      },\n      \"31\": {\n        name: \"(anonymous_31)\",\n        decl: {\n          start: {\n            line: 264,\n            column: 18\n          },\n          end: {\n            line: 264,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 264,\n            column: 28\n          },\n          end: {\n            line: 272,\n            column: 13\n          }\n        },\n        line: 264\n      },\n      \"32\": {\n        name: \"(anonymous_32)\",\n        decl: {\n          start: {\n            line: 273,\n            column: 19\n          },\n          end: {\n            line: 273,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 273,\n            column: 30\n          },\n          end: {\n            line: 275,\n            column: 13\n          }\n        },\n        line: 273\n      },\n      \"33\": {\n        name: \"(anonymous_33)\",\n        decl: {\n          start: {\n            line: 278,\n            column: 4\n          },\n          end: {\n            line: 278,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 278,\n            column: 36\n          },\n          end: {\n            line: 283,\n            column: 5\n          }\n        },\n        line: 278\n      },\n      \"34\": {\n        name: \"(anonymous_34)\",\n        decl: {\n          start: {\n            line: 284,\n            column: 35\n          },\n          end: {\n            line: 284,\n            column: 36\n          }\n        },\n        loc: {\n          start: {\n            line: 284,\n            column: 41\n          },\n          end: {\n            line: 294,\n            column: 5\n          }\n        },\n        line: 284\n      }\n    },\n    branchMap: {\n      \"0\": {\n        loc: {\n          start: {\n            line: 51,\n            column: 8\n          },\n          end: {\n            line: 56,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 51,\n            column: 8\n          },\n          end: {\n            line: 56,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 51\n      },\n      \"1\": {\n        loc: {\n          start: {\n            line: 53,\n            column: 12\n          },\n          end: {\n            line: 55,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 53,\n            column: 12\n          },\n          end: {\n            line: 55,\n            column: 13\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 53\n      },\n      \"2\": {\n        loc: {\n          start: {\n            line: 65,\n            column: 8\n          },\n          end: {\n            line: 67,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 65,\n            column: 8\n          },\n          end: {\n            line: 67,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 65\n      },\n      \"3\": {\n        loc: {\n          start: {\n            line: 78,\n            column: 12\n          },\n          end: {\n            line: 90,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 78,\n            column: 12\n          },\n          end: {\n            line: 90,\n            column: 13\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 78\n      },\n      \"4\": {\n        loc: {\n          start: {\n            line: 78,\n            column: 16\n          },\n          end: {\n            line: 78,\n            column: 51\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 78,\n            column: 16\n          },\n          end: {\n            line: 78,\n            column: 22\n          }\n        }, {\n          start: {\n            line: 78,\n            column: 26\n          },\n          end: {\n            line: 78,\n            column: 51\n          }\n        }],\n        line: 78\n      },\n      \"5\": {\n        loc: {\n          start: {\n            line: 87,\n            column: 41\n          },\n          end: {\n            line: 87,\n            column: 106\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 87,\n            column: 41\n          },\n          end: {\n            line: 87,\n            column: 60\n          }\n        }, {\n          start: {\n            line: 87,\n            column: 64\n          },\n          end: {\n            line: 87,\n            column: 106\n          }\n        }],\n        line: 87\n      },\n      \"6\": {\n        loc: {\n          start: {\n            line: 95,\n            column: 8\n          },\n          end: {\n            line: 99,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 95,\n            column: 8\n          },\n          end: {\n            line: 99,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 95\n      },\n      \"7\": {\n        loc: {\n          start: {\n            line: 95,\n            column: 12\n          },\n          end: {\n            line: 95,\n            column: 65\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 95,\n            column: 12\n          },\n          end: {\n            line: 95,\n            column: 38\n          }\n        }, {\n          start: {\n            line: 95,\n            column: 42\n          },\n          end: {\n            line: 95,\n            column: 65\n          }\n        }],\n        line: 95\n      },\n      \"8\": {\n        loc: {\n          start: {\n            line: 103,\n            column: 12\n          },\n          end: {\n            line: 105,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 103,\n            column: 12\n          },\n          end: {\n            line: 105,\n            column: 13\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 103\n      },\n      \"9\": {\n        loc: {\n          start: {\n            line: 106,\n            column: 12\n          },\n          end: {\n            line: 152,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 106,\n            column: 12\n          },\n          end: {\n            line: 152,\n            column: 13\n          }\n        }, {\n          start: {\n            line: 132,\n            column: 17\n          },\n          end: {\n            line: 152,\n            column: 13\n          }\n        }],\n        line: 106\n      },\n      \"10\": {\n        loc: {\n          start: {\n            line: 118,\n            column: 20\n          },\n          end: {\n            line: 129,\n            column: 21\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 118,\n            column: 20\n          },\n          end: {\n            line: 129,\n            column: 21\n          }\n        }, {\n          start: {\n            line: 127,\n            column: 25\n          },\n          end: {\n            line: 129,\n            column: 21\n          }\n        }],\n        line: 118\n      },\n      \"11\": {\n        loc: {\n          start: {\n            line: 135,\n            column: 40\n          },\n          end: {\n            line: 137,\n            column: 23\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 136,\n            column: 22\n          },\n          end: {\n            line: 136,\n            column: 49\n          }\n        }, {\n          start: {\n            line: 137,\n            column: 22\n          },\n          end: {\n            line: 137,\n            column: 23\n          }\n        }],\n        line: 135\n      },\n      \"12\": {\n        loc: {\n          start: {\n            line: 156,\n            column: 16\n          },\n          end: {\n            line: 242,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 156,\n            column: 16\n          },\n          end: {\n            line: 242,\n            column: 17\n          }\n        }, {\n          start: {\n            line: 172,\n            column: 21\n          },\n          end: {\n            line: 242,\n            column: 17\n          }\n        }],\n        line: 156\n      },\n      \"13\": {\n        loc: {\n          start: {\n            line: 159,\n            column: 20\n          },\n          end: {\n            line: 170,\n            column: 21\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 159,\n            column: 20\n          },\n          end: {\n            line: 170,\n            column: 21\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 159\n      },\n      \"14\": {\n        loc: {\n          start: {\n            line: 161,\n            column: 24\n          },\n          end: {\n            line: 169,\n            column: 25\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 161,\n            column: 24\n          },\n          end: {\n            line: 169,\n            column: 25\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 161\n      },\n      \"15\": {\n        loc: {\n          start: {\n            line: 162,\n            column: 28\n          },\n          end: {\n            line: 167,\n            column: 29\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 162,\n            column: 28\n          },\n          end: {\n            line: 167,\n            column: 29\n          }\n        }, {\n          start: {\n            line: 165,\n            column: 33\n          },\n          end: {\n            line: 167,\n            column: 29\n          }\n        }],\n        line: 162\n      },\n      \"16\": {\n        loc: {\n          start: {\n            line: 172,\n            column: 21\n          },\n          end: {\n            line: 242,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 172,\n            column: 21\n          },\n          end: {\n            line: 242,\n            column: 17\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 172\n      },\n      \"17\": {\n        loc: {\n          start: {\n            line: 172,\n            column: 25\n          },\n          end: {\n            line: 175,\n            column: 35\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 172,\n            column: 25\n          },\n          end: {\n            line: 172,\n            column: 31\n          }\n        }, {\n          start: {\n            line: 173,\n            column: 20\n          },\n          end: {\n            line: 173,\n            column: 38\n          }\n        }, {\n          start: {\n            line: 174,\n            column: 20\n          },\n          end: {\n            line: 174,\n            column: 38\n          }\n        }, {\n          start: {\n            line: 175,\n            column: 20\n          },\n          end: {\n            line: 175,\n            column: 35\n          }\n        }],\n        line: 172\n      },\n      \"18\": {\n        loc: {\n          start: {\n            line: 176,\n            column: 20\n          },\n          end: {\n            line: 241,\n            column: 21\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 176,\n            column: 20\n          },\n          end: {\n            line: 241,\n            column: 21\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 176\n      },\n      \"19\": {\n        loc: {\n          start: {\n            line: 191,\n            column: 32\n          },\n          end: {\n            line: 194,\n            column: 33\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 191,\n            column: 32\n          },\n          end: {\n            line: 194,\n            column: 33\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 191\n      },\n      \"20\": {\n        loc: {\n          start: {\n            line: 215,\n            column: 65\n          },\n          end: {\n            line: 215,\n            column: 130\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 215,\n            column: 65\n          },\n          end: {\n            line: 215,\n            column: 84\n          }\n        }, {\n          start: {\n            line: 215,\n            column: 88\n          },\n          end: {\n            line: 215,\n            column: 130\n          }\n        }],\n        line: 215\n      },\n      \"21\": {\n        loc: {\n          start: {\n            line: 220,\n            column: 57\n          },\n          end: {\n            line: 220,\n            column: 123\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 220,\n            column: 57\n          },\n          end: {\n            line: 220,\n            column: 76\n          }\n        }, {\n          start: {\n            line: 220,\n            column: 80\n          },\n          end: {\n            line: 220,\n            column: 123\n          }\n        }],\n        line: 220\n      },\n      \"22\": {\n        loc: {\n          start: {\n            line: 231,\n            column: 65\n          },\n          end: {\n            line: 231,\n            column: 130\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 231,\n            column: 65\n          },\n          end: {\n            line: 231,\n            column: 84\n          }\n        }, {\n          start: {\n            line: 231,\n            column: 88\n          },\n          end: {\n            line: 231,\n            column: 130\n          }\n        }],\n        line: 231\n      },\n      \"23\": {\n        loc: {\n          start: {\n            line: 238,\n            column: 49\n          },\n          end: {\n            line: 238,\n            column: 106\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 238,\n            column: 49\n          },\n          end: {\n            line: 238,\n            column: 68\n          }\n        }, {\n          start: {\n            line: 238,\n            column: 72\n          },\n          end: {\n            line: 238,\n            column: 106\n          }\n        }],\n        line: 238\n      },\n      \"24\": {\n        loc: {\n          start: {\n            line: 245,\n            column: 33\n          },\n          end: {\n            line: 245,\n            column: 90\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 245,\n            column: 33\n          },\n          end: {\n            line: 245,\n            column: 52\n          }\n        }, {\n          start: {\n            line: 245,\n            column: 56\n          },\n          end: {\n            line: 245,\n            column: 90\n          }\n        }],\n        line: 245\n      },\n      \"25\": {\n        loc: {\n          start: {\n            line: 250,\n            column: 8\n          },\n          end: {\n            line: 251,\n            column: 24\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 250,\n            column: 8\n          },\n          end: {\n            line: 251,\n            column: 24\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 250\n      },\n      \"26\": {\n        loc: {\n          start: {\n            line: 257,\n            column: 8\n          },\n          end: {\n            line: 258,\n            column: 19\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 257,\n            column: 8\n          },\n          end: {\n            line: 258,\n            column: 19\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 257\n      },\n      \"27\": {\n        loc: {\n          start: {\n            line: 274,\n            column: 33\n          },\n          end: {\n            line: 274,\n            column: 83\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 274,\n            column: 33\n          },\n          end: {\n            line: 274,\n            column: 52\n          }\n        }, {\n          start: {\n            line: 274,\n            column: 56\n          },\n          end: {\n            line: 274,\n            column: 83\n          }\n        }],\n        line: 274\n      }\n    },\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0,\n      \"36\": 0,\n      \"37\": 0,\n      \"38\": 0,\n      \"39\": 0,\n      \"40\": 0,\n      \"41\": 0,\n      \"42\": 0,\n      \"43\": 0,\n      \"44\": 0,\n      \"45\": 0,\n      \"46\": 0,\n      \"47\": 0,\n      \"48\": 0,\n      \"49\": 0,\n      \"50\": 0,\n      \"51\": 0,\n      \"52\": 0,\n      \"53\": 0,\n      \"54\": 0,\n      \"55\": 0,\n      \"56\": 0,\n      \"57\": 0,\n      \"58\": 0,\n      \"59\": 0,\n      \"60\": 0,\n      \"61\": 0,\n      \"62\": 0,\n      \"63\": 0,\n      \"64\": 0,\n      \"65\": 0,\n      \"66\": 0,\n      \"67\": 0,\n      \"68\": 0,\n      \"69\": 0,\n      \"70\": 0,\n      \"71\": 0,\n      \"72\": 0,\n      \"73\": 0,\n      \"74\": 0,\n      \"75\": 0,\n      \"76\": 0,\n      \"77\": 0,\n      \"78\": 0,\n      \"79\": 0,\n      \"80\": 0,\n      \"81\": 0,\n      \"82\": 0,\n      \"83\": 0,\n      \"84\": 0,\n      \"85\": 0,\n      \"86\": 0,\n      \"87\": 0,\n      \"88\": 0,\n      \"89\": 0,\n      \"90\": 0,\n      \"91\": 0,\n      \"92\": 0,\n      \"93\": 0,\n      \"94\": 0,\n      \"95\": 0,\n      \"96\": 0,\n      \"97\": 0,\n      \"98\": 0,\n      \"99\": 0,\n      \"100\": 0,\n      \"101\": 0,\n      \"102\": 0,\n      \"103\": 0,\n      \"104\": 0,\n      \"105\": 0,\n      \"106\": 0,\n      \"107\": 0,\n      \"108\": 0,\n      \"109\": 0,\n      \"110\": 0,\n      \"111\": 0,\n      \"112\": 0,\n      \"113\": 0,\n      \"114\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0\n    },\n    b: {\n      \"0\": [0, 0],\n      \"1\": [0, 0],\n      \"2\": [0, 0],\n      \"3\": [0, 0],\n      \"4\": [0, 0],\n      \"5\": [0, 0],\n      \"6\": [0, 0],\n      \"7\": [0, 0],\n      \"8\": [0, 0],\n      \"9\": [0, 0],\n      \"10\": [0, 0],\n      \"11\": [0, 0],\n      \"12\": [0, 0],\n      \"13\": [0, 0],\n      \"14\": [0, 0],\n      \"15\": [0, 0],\n      \"16\": [0, 0],\n      \"17\": [0, 0, 0, 0],\n      \"18\": [0, 0],\n      \"19\": [0, 0],\n      \"20\": [0, 0],\n      \"21\": [0, 0],\n      \"22\": [0, 0],\n      \"23\": [0, 0],\n      \"24\": [0, 0],\n      \"25\": [0, 0],\n      \"26\": [0, 0],\n      \"27\": [0, 0]\n    },\n    inputSourceMap: {\n      version: 3,\n      file: \"monthly-reports-tab.component.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-dashboard\\\\components\\\\monthly-reports-tab\\\\monthly-reports-tab.component.ts\"],\n      names: [],\n      mappings: \";;;AAAA,OAAO,EAEL,SAAS,EACT,KAAK,EAGL,SAAS,GACV,MAAM,eAAe,CAAC;AACvB,OAAO,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAC3D,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AACrD,OAAO,EAAE,kBAAkB,EAAE,MAAM,8BAA8B,CAAC;AAClE,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACzD,OAAO,EAAE,YAAY,EAAE,kBAAkB,EAAE,MAAM,6BAA6B,CAAC;AAC/E,OAAO,EAAE,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AAChE,OAAO,EAAE,kBAAkB,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AAC7E,OAAO,EAAE,gBAAgB,EAAE,MAAM,2BAA2B,CAAC;AAC7D,OAAO,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AAChD,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE,SAAS,EAAE,MAAM,MAAM,CAAC;AAEhD,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,iBAAiB,CAAC;AACzD,OAAO,EAAE,yBAAyB,EAAE,MAAM,2DAA2D,CAAC;AAItG,OAAO,EAAE,oBAAoB,EAAE,MAAM,uDAAuD,CAAC;AAC7F,OAAO,EAAE,aAAa,EAAE,MAAM,+CAA+C,CAAC;AAC9E,OAAO,EAAE,0BAA0B,EAAE,MAAM,8DAA8D,CAAC;AAC1G,OAAO,EAAE,yBAAyB,EAAE,MAAM,6DAA6D,CAAC;AACxG,OAAO,EAAE,WAAW,EAAE,MAAM,kCAAkC,CAAC;AAC/D,OAAO,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAC9D,OAAO,EAAE,4BAA4B,EAAE,MAAM,yDAAyD,CAAC;AACvG,OAAO,EAAE,yCAAyC,EAAE,MAAM,uFAAuF,CAAC;AAClJ,OAAO,EAAE,2BAA2B,EAAE,MAAM,uDAAuD,CAAC;AAuB7F,IAAM,0BAA0B,GAAhC,MAAM,0BAA0B;IAmBrC,YACmB,MAAiB,EACjB,KAAmB,EACnB,oBAA0C,EAC1C,yBAAoD,EACpD,yBAAoD,EACpD,aAA4B,EAC5B,OAA0B,EAC1B,0BAAsD,EACtD,WAAwB;QARxB,WAAM,GAAN,MAAM,CAAW;QACjB,UAAK,GAAL,KAAK,CAAc;QACnB,yBAAoB,GAApB,oBAAoB,CAAsB;QAC1C,8BAAyB,GAAzB,yBAAyB,CAA2B;QACpD,8BAAyB,GAAzB,yBAAyB,CAA2B;QACpD,kBAAa,GAAb,aAAa,CAAe;QAC5B,YAAO,GAAP,OAAO,CAAmB;QAC1B,+BAA0B,GAA1B,0BAA0B,CAA4B;QACtD,gBAAW,GAAX,WAAW,CAAa;QA3BlC,mBAAc,GAAoB,EAAE;QAQ7C,qBAAgB,GAAa;YAC3B,cAAc;YACd,WAAW;YACX,SAAS;YACT,qBAAqB;YACrB,YAAY;YACZ,SAAS;SACV,CAAC;QACF,eAAU,GAAG,IAAI,kBAAkB,EAAiB,CAAC;IAYlD,CAAC;IAEJ,WAAW,CAAC,OAAsB;QAChC,IAAI,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC;YAC3C,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;gBAC9B,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;YACxC,CAAC;QACH,CAAC;IACH,CAAC;IAED,eAAe;QACb,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC3C,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;IACnC,CAAC;IAED,WAAW,CAAC,KAAY;QACtB,MAAM,WAAW,GAAI,KAAK,CAAC,MAA2B,CAAC,KAAK,CAAC;QAC7D,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAE1D,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;YAC9B,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;QACxC,CAAC;IACH,CAAC;IAED,qBAAqB,CAAC,MAAqB;QACzC,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE;YAC/D,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,MAAM;YACd,QAAQ,EAAE,OAAO;YACjB,SAAS,EAAE,OAAO;YAClB,IAAI,EAAE,EAAE,MAAM,EAAE;SACjB,CAAC,CAAC;QAEH,SAAS,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,MAAe,EAAE,EAAE;YACpD,IAAI,MAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBACxC,IAAI,CAAC,oBAAoB;qBACtB,yBAAyB,CAAC,IAAI,CAAC,oBAAoB,CAAC;qBACpD,SAAS,CAAC;oBACT,IAAI,EAAE,CAAC,cAAc,EAAE,EAAE;wBACvB,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;wBACrC,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,cAAc,CAAC;oBACxC,CAAC;oBACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;wBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,0CAA0C,CAAC,CAAC;oBACtF,CAAC;iBACF,CAAC,CAAC;YACP,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,eAAe;QACb,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QAEpB,IAAI,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC1D,IAAI,CAAC,KAAK,CAAC,OAAO,CAChB,uDAAuD,CACxD,CAAC;YACF,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YACpB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,yBAAyB;aAC3B,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC;aAClC,IAAI,CACH,SAAS,CAAC,CAAC,kBAAkB,EAAE,EAAE;YAC/B,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC;gBACrC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;YAC9D,CAAC;YAED,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACrC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;gBACpB,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;oBAC9D,QAAQ,EAAE,OAAO;oBACjB,KAAK,EAAE,MAAM;oBACb,IAAI,EAAE;wBACJ,UAAU,EAAE,kBAAkB,CAAC,QAAQ,CAAC,EAAE;wBAC1C,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;qBAChD;oBACD,SAAS,EAAE,QAAQ;iBACpB,CAAC,CAAC;gBAEH,OAAO,SAAS,CAAC,WAAW,EAAE,CAAC,IAAI,CACjC,SAAS,CAAC,CAAC,cAAkC,EAAE,EAAE;oBAC/C,IAAI,cAAc,EAAE,CAAC;wBACnB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;wBACpB,OAAO,IAAI,CAAC,oBAAoB;6BAC7B,yBAAyB,CAAC,IAAI,CAAC,oBAAqB,CAAC;6BACrD,IAAI,CACH,GAAG,CAAC,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;4BACvB,cAAc;4BACd,cAAc;yBACf,CAAC,CAAC,CACJ,CAAC;oBACN,CAAC;yBAAM,CAAC;wBACN,OAAO,EAAE,CAAC;oBACZ,CAAC;gBACH,CAAC,CAAC,CACH,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;gBAChD,MAAM,UAAU,GACd,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACtD,MAAM,eAAe,GAAG,UAAU;oBAChC,CAAC,CAAC,UAAU,CAAC,YAAY,GAAG,CAAC;oBAC7B,CAAC,CAAC,CAAC,CAAC;gBAEN,MAAM,SAAS,GAA8B;oBAC3C,UAAU,EAAE,kBAAkB,CAAC,QAAQ,CAAC,EAAE;oBAC1C,WAAW,EAAE,eAAe;iBAC7B,CAAC;gBAEF,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAC9C,SAAS,CAAC,CAAC,MAAM,EAAE,EAAE;oBACnB,OAAO,IAAI,CAAC,yBAAyB;yBAClC,SAAS,CAAC,UAAU,CAAC;yBACrB,IAAI,CACH,GAAG,CAAC,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;wBAC3B,MAAM;wBACN,kBAAkB;wBAClB,kBAAkB;wBAClB,eAAe;qBAChB,CAAC,CAAC,CACJ,CAAC;gBACN,CAAC,CAAC,CACH,CAAC;YACJ,CAAC;QACH,CAAC,CAAC,EACF,QAAQ,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CACpC;aACA,SAAS,CAAC;YACT,IAAI,EAAE,CAAC,EACL,cAAc,EACd,MAAM,EACN,kBAAkB,EAClB,kBAAkB,EAClB,eAAe,EACf,cAAc,GAQf,EAAE,EAAE;gBACH,IAAI,cAAc,EAAE,CAAC;oBACnB,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;oBACrC,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC;oBAC3C,IAAI,cAAc,EAAE,CAAC;wBACnB,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAC3C,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,YAAY,KAAK,cAAc,CAAC,WAAW,CACrD,CAAC;wBACF,IAAI,YAAY,EAAE,CAAC;4BACjB,IAAI,YAAY,CAAC,YAAY,KAAK,CAAC,EAAE,CAAC;gCACpC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC;4BAC7D,CAAC;iCAAM,CAAC;gCACN,IAAI,CAAC,KAAK,CAAC,OAAO,CAChB,0CAA0C,CAC3C,CAAC;4BACJ,CAAC;4BACD,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;wBAC3C,CAAC;oBACH,CAAC;gBACH,CAAC;qBAAM,IACL,MAAM;oBACN,kBAAkB;oBAClB,kBAAkB;oBAClB,eAAe,EACf,CAAC;oBACD,IAAI,kBAAkB,CAAC,EAAE,EAAE,CAAC;wBAC1B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;wBACrB,IAAI,CAAC,oBAAoB;6BACtB,MAAM,CAAC;4BACN,oBAAoB,EAAE,kBAAkB,CAAC,EAAE;4BAC3C,YAAY,EAAE,eAAe;4BAC7B,SAAS,EAAE,MAAM,CAAC,UAAU;4BAC5B,OAAO,EAAE,MAAM,CAAC,QAAQ;4BACxB,UAAU,EAAE,MAAM,CAAC,OAAO;4BAC1B,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;4BACnD,YAAY,EAAE,kBAAkB,CAAC,QAAQ,EAAE,YAAY,EAAE,IAAI;yBAC9D,CAAC;6BACD,SAAS,CAAC;4BACT,IAAI,EAAE,CAAC,aAA4B,EAAE,EAAE;gCACrC,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;gCACtD,IAAI,CAAC,WAAW,EAAE,EAAE,EAAE,CAAC;oCACrB,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,oDAAoD,CACrD,CAAC;oCACF,OAAO;gCACT,CAAC;gCAED,IAAI,CAAC,0BAA0B;qCAC5B,MAAM,CAAC;oCACN,eAAe,EAAE,aAAa,CAAC,EAAE;oCACjC,cAAc,EAAE,kBAAkB,CAAC,EAAE;oCACrC,UAAU,EAAE,IAAI,IAAI,EAAE;oCACtB,OAAO,EAAE,gBAAgB;oCACzB,UAAU,EAAE,WAAW,CAAC,EAAE;iCAC3B,CAAC;qCACD,SAAS,CAAC;oCACT,IAAI,EAAE,GAAG,EAAE;wCACT,IAAI,CAAC,oBAAoB;6CACtB,yBAAyB,CAAC,kBAAkB,CAAC,EAAE,CAAC;6CAChD,SAAS,CAAC;4CACT,IAAI,EAAE,CAAC,cAAc,EAAE,EAAE;gDACvB,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;gDACrC,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC;gDAC3C,IAAI,CAAC,KAAK,CAAC,OAAO,CAChB,sCAAsC,CACvC,CAAC;gDACF,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;4CAC5C,CAAC;4CACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gDACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,0CAA0C,CAAC,CAAC;4CACtF,CAAC;yCACF,CAAC,CAAC;oCACP,CAAC;oCACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;wCACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,2CAA2C,CAAC,CAAC;wCACrF,IAAI,CAAC,oBAAoB;6CACtB,yBAAyB,CAAC,kBAAkB,CAAC,EAAE,CAAC;6CAChD,SAAS,CAAC;4CACT,IAAI,EAAE,CAAC,cAAc,EAAE,EAAE;gDACvB,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;gDACrC,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC;gDAC3C,IAAI,CAAC,KAAK,CAAC,OAAO,CAChB,sCAAsC,CACvC,CAAC;gDACF,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;4CAC5C,CAAC;4CACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gDACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,0CAA0C,CAAC,CAAC;4CACtF,CAAC;yCACF,CAAC,CAAC;oCACP,CAAC;iCACF,CAAC,CAAC;4BACP,CAAC;4BACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gCACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,kCAAkC,CAAC,CAAC;4BAC9E,CAAC;yBACF,CAAC,CAAC;oBACP,CAAC;gBACH,CAAC;YACH,CAAC;YACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,kCAAkC,CAAC,CAAC;YAC9E,CAAC;SACF,CAAC,CAAC;IACP,CAAC;IAED,IAAI,kBAAkB;QACpB,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAElD,MAAM,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CACjD,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC,YAAY,CAC1C,CAAC;QACF,MAAM,UAAU,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;QAEpC,OAAO,UAAU,CAAC,mBAAmB,EAAE,IAAI,KAAK,UAAU,CAAC;IAC7D,CAAC;IAED,WAAW,CAAC,MAAqB;QAC/B,IAAI,CAAC,MAAM,CAAC,EAAE;YAAE,OAAO;QAEvB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACpB,IAAI,CAAC,oBAAoB;aACtB,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC;aACtB,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;aACzC,SAAS,CAAC;YACT,IAAI,EAAE,CAAC,IAAI,EAAE,EAAE;gBACb,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAC7C,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;gBACzC,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;gBAChB,IAAI,CAAC,QAAQ,GAAG,mBAAmB,MAAM,CAAC,YAAY,MAAM,CAAC;gBAC7D,IAAI,CAAC,KAAK,EAAE,CAAC;gBACb,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;gBAChC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;YACpD,CAAC;YACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,2BAA2B,CAAC,CAAC;YACvE,CAAC;SACF,CAAC,CAAC;IACP,CAAC;IAED,uBAAuB,CAAC,MAAqB;QAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yCAAyC,EAAE;YAC1D,KAAK,EAAE,QAAQ;YACf,IAAI,EAAE,EAAE,eAAe,EAAE,MAAM,CAAC,EAAE,EAAE;SACrC,CAAC,CAAC;IACL,CAAC;;;;;;;;;;;;;iCA9TA,KAAK;uCACL,KAAK;oCACL,KAAK;4BAEL,SAAS,SAAC,YAAY;uBACtB,SAAS,SAAC,OAAO;;;AANP,0BAA0B;IAnBtC,SAAS,CAAC;QACT,QAAQ,EAAE,yBAAyB;QACnC,8BAAmD;QAEnD,UAAU,EAAE,IAAI;QAChB,OAAO,EAAE;YACP,cAAc;YACd,kBAAkB;YAClB,aAAa;YACb,kBAAkB;YAClB,cAAc;YACd,eAAe;YACf,aAAa;YACb,gBAAgB;YAChB,aAAa;YACb,QAAQ;YACR,YAAY;SACb;;KACF,CAAC;GACW,0BAA0B,CAgUtC\",\n      sourcesContent: [\"import {\\n  AfterViewInit,\\n  Component,\\n  Input,\\n  OnChanges,\\n  SimpleChanges,\\n  ViewChild,\\n} from '@angular/core';\\nimport { MatButtonModule } from '@angular/material/button';\\nimport { MatCardModule } from '@angular/material/card';\\nimport { MatDialog } from '@angular/material/dialog';\\nimport { MatFormFieldModule } from '@angular/material/form-field';\\nimport { MatIconModule } from '@angular/material/icon';\\nimport { MatInputModule } from '@angular/material/input';\\nimport { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';\\nimport { MatSort, MatSortModule } from '@angular/material/sort';\\nimport { MatTableDataSource, MatTableModule } from '@angular/material/table';\\nimport { MatTooltipModule } from '@angular/material/tooltip';\\nimport { NgxSpinnerService } from 'ngx-spinner';\\nimport { finalize, map, switchMap } from 'rxjs';\\n\\nimport { CurrencyPipe, DatePipe } from '@angular/common';\\nimport { ContractorContractService } from '@contract-management/services/contractor-contract.service';\\nimport { ConsultPeriod } from '@contractor-dashboard/models/ConsultPeriod.model';\\nimport { MonthlyReport } from '@contractor-dashboard/models/monthly-report.model';\\nimport { Period } from '@contractor-dashboard/models/Period.model';\\nimport { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';\\nimport { PeriodService } from '@contractor-dashboard/services/period.service';\\nimport { ReportReviewHistoryService } from '@contractor-dashboard/services/report-review-history.service';\\nimport { ReportReviewStatusService } from '@contractor-dashboard/services/report-review-status.service';\\nimport { AuthService } from '@core/auth/services/auth.service';\\nimport { AlertService } from '@shared/services/alert.service';\\nimport { MonthlyReportDialogComponent } from './monthly-report-dialog/monthly-report-dialog.component';\\nimport { MonthlyReportReviewHistoryDialogComponent } from './monthly-report-review-history-dialog/monthly-report-review-history-dialog.component';\\nimport { SelectPeriodDialogComponent } from './select-period-dialog/select-period-dialog.component';\\nimport { ReportReviewStatus } from '@contractor-dashboard/models/report-review-status.model';\\nimport { ContractorContract } from '@contract-management/models/contractor-contract.model';\\n\\n@Component({\\n  selector: 'app-monthly-reports-tab',\\n  templateUrl: './monthly-reports-tab.component.html',\\n  styleUrl: './monthly-reports-tab.component.scss',\\n  standalone: true,\\n  imports: [\\n    MatTableModule,\\n    MatPaginatorModule,\\n    MatSortModule,\\n    MatFormFieldModule,\\n    MatInputModule,\\n    MatButtonModule,\\n    MatIconModule,\\n    MatTooltipModule,\\n    MatCardModule,\\n    DatePipe,\\n    CurrencyPipe,\\n  ],\\n})\\nexport class MonthlyReportsTabComponent implements OnChanges, AfterViewInit {\\n  @Input() monthlyReports: MonthlyReport[] = [];\\n  @Input() contractorContractId: number | undefined;\\n  @Input() contractStartDate: Date | undefined;\\n\\n  @ViewChild(MatPaginator) paginator!: MatPaginator;\\n  @ViewChild(MatSort) sort!: MatSort;\\n\\n  Period!: Period;\\n  displayedColumns: string[] = [\\n    'reportNumber',\\n    'startDate',\\n    'endDate',\\n    'currentReviewStatus',\\n    'totalValue',\\n    'actions',\\n  ];\\n  dataSource = new MatTableDataSource<MonthlyReport>();\\n\\n  constructor(\\n    private readonly dialog: MatDialog,\\n    private readonly alert: AlertService,\\n    private readonly monthlyReportService: MonthlyReportService,\\n    private readonly contractorContractService: ContractorContractService,\\n    private readonly reportReviewStatusService: ReportReviewStatusService,\\n    private readonly periodService: PeriodService,\\n    private readonly spinner: NgxSpinnerService,\\n    private readonly reportReviewHistoryService: ReportReviewHistoryService,\\n    private readonly authService: AuthService,\\n  ) {}\\n\\n  ngOnChanges(changes: SimpleChanges): void {\\n    if (changes['monthlyReports']) {\\n      this.dataSource.data = this.monthlyReports;\\n      if (this.dataSource.paginator) {\\n        this.dataSource.paginator.firstPage();\\n      }\\n    }\\n  }\\n\\n  ngAfterViewInit(): void {\\n    this.dataSource.paginator = this.paginator;\\n    this.dataSource.sort = this.sort;\\n  }\\n\\n  applyFilter(event: Event): void {\\n    const filterValue = (event.target as HTMLInputElement).value;\\n    this.dataSource.filter = filterValue.trim().toLowerCase();\\n\\n    if (this.dataSource.paginator) {\\n      this.dataSource.paginator.firstPage();\\n    }\\n  }\\n\\n  openReportDetailsForm(report: MonthlyReport): void {\\n    const dialogRef = this.dialog.open(MonthlyReportDialogComponent, {\\n      width: '90vw',\\n      height: '90vh',\\n      maxWidth: '100vw',\\n      maxHeight: '100vh',\\n      data: { report },\\n    });\\n\\n    dialogRef.afterClosed().subscribe((result: boolean) => {\\n      if (result && this.contractorContractId) {\\n        this.monthlyReportService\\n          .getByContractorContractId(this.contractorContractId)\\n          .subscribe({\\n            next: (monthlyReports) => {\\n              this.monthlyReports = monthlyReports;\\n              this.dataSource.data = monthlyReports;\\n            },\\n            error: (error) => {\\n              this.alert.error(error.error?.detail ?? 'Error al actualizar la tabla de informes');\\n            },\\n          });\\n      }\\n    });\\n  }\\n\\n  createNewReport(): void {\\n    this.spinner.show();\\n\\n    if (!this.contractorContractId || !this.contractStartDate) {\\n      this.alert.warning(\\n        'No se puede crear un informe sin un contrato asociado',\\n      );\\n      this.spinner.hide();\\n      return;\\n    }\\n\\n    this.contractorContractService\\n      .getById(this.contractorContractId)\\n      .pipe(\\n        switchMap((contractorContract) => {\\n          if (!contractorContract.contract?.id) {\\n            throw new Error('Error: No se encontr\\xF3 el ID del contrato');\\n          }\\n\\n          if (this.monthlyReports.length === 0) {\\n            this.spinner.hide();\\n            const dialogRef = this.dialog.open(SelectPeriodDialogComponent, {\\n              maxWidth: '600px',\\n              width: '100%',\\n              data: {\\n                contractId: contractorContract.contract.id,\\n                contractorContractId: this.contractorContractId,\\n              },\\n              autoFocus: 'dialog',\\n            });\\n\\n            return dialogRef.afterClosed().pipe(\\n              switchMap((selectedPeriod: Period | undefined) => {\\n                if (selectedPeriod) {\\n                  this.spinner.show();\\n                  return this.monthlyReportService\\n                    .getByContractorContractId(this.contractorContractId!)\\n                    .pipe(\\n                      map((monthlyReports) => ({\\n                        monthlyReports,\\n                        selectedPeriod,\\n                      })),\\n                    );\\n                } else {\\n                  return [];\\n                }\\n              }),\\n            );\\n          } else {\\n            this.monthlyReports.sort((a, b) => a.id - b.id);\\n            const lastReport =\\n              this.monthlyReports[this.monthlyReports.length - 1];\\n            const newReportNumber = lastReport\\n              ? lastReport.reportNumber + 1\\n              : 1;\\n\\n            const newPeriod: Omit<ConsultPeriod, 'id'> = {\\n              id_contrat: contractorContract.contract.id,\\n              num_payment: newReportNumber,\\n            };\\n\\n            return this.periodService.create(newPeriod).pipe(\\n              switchMap((period) => {\\n                return this.reportReviewStatusService\\n                  .getByName('Borrador')\\n                  .pipe(\\n                    map((reportReviewStatus) => ({\\n                      period,\\n                      reportReviewStatus,\\n                      contractorContract,\\n                      newReportNumber,\\n                    })),\\n                  );\\n              }),\\n            );\\n          }\\n        }),\\n        finalize(() => this.spinner.hide()),\\n      )\\n      .subscribe({\\n        next: ({\\n          monthlyReports,\\n          period,\\n          reportReviewStatus,\\n          contractorContract,\\n          newReportNumber,\\n          selectedPeriod,\\n        }: {\\n          monthlyReports?: MonthlyReport[];\\n          period?: Period;\\n          reportReviewStatus?: ReportReviewStatus;\\n          contractorContract?: ContractorContract;\\n          newReportNumber?: number;\\n          selectedPeriod?: Period;\\n        }) => {\\n          if (monthlyReports) {\\n            this.monthlyReports = monthlyReports;\\n            this.dataSource.data = this.monthlyReports;\\n            if (selectedPeriod) {\\n              const reportToOpen = this.monthlyReports.find(\\n                (r) => r.reportNumber === selectedPeriod.num_payment,\\n              );\\n              if (reportToOpen) {\\n                if (reportToOpen.reportNumber === 1) {\\n                  this.alert.success('Informe mensual creado correctamente');\\n                } else {\\n                  this.alert.success(\\n                    'Informes mensuales creados correctamente',\\n                  );\\n                }\\n                this.openReportDetailsForm(reportToOpen);\\n              }\\n            }\\n          } else if (\\n            period &&\\n            reportReviewStatus &&\\n            contractorContract &&\\n            newReportNumber\\n          ) {\\n            if (reportReviewStatus.id) {\\n              this.Period = period;\\n              this.monthlyReportService\\n                .create({\\n                  contractorContractId: contractorContract.id,\\n                  reportNumber: newReportNumber,\\n                  startDate: period.start_date,\\n                  endDate: period.end_date,\\n                  totalValue: period.payment,\\n                  creationDate: new Date().toISOString().slice(0, 10),\\n                  contractYear: contractorContract.contract?.contractYear?.year,\\n                })\\n                .subscribe({\\n                  next: (createdReport: MonthlyReport) => {\\n                    const currentUser = this.authService.getCurrentUser();\\n                    if (!currentUser?.id) {\\n                      this.alert.error(\\n                        'Error: No se pudo obtener el ID del usuario actual',\\n                      );\\n                      return;\\n                    }\\n\\n                    this.reportReviewHistoryService\\n                      .create({\\n                        monthlyReportId: createdReport.id,\\n                        reviewStatusId: reportReviewStatus.id,\\n                        reviewDate: new Date(),\\n                        comment: 'Informe creado',\\n                        reviewerId: currentUser.id,\\n                      })\\n                      .subscribe({\\n                        next: () => {\\n                          this.monthlyReportService\\n                            .getByContractorContractId(contractorContract.id)\\n                            .subscribe({\\n                              next: (monthlyReports) => {\\n                                this.monthlyReports = monthlyReports;\\n                                this.dataSource.data = this.monthlyReports;\\n                                this.alert.success(\\n                                  'Informe mensual creado correctamente',\\n                                );\\n                                this.openReportDetailsForm(createdReport);\\n                              },\\n                              error: (error) => {\\n                                this.alert.error(error.error?.detail ?? 'Error al actualizar la lista de informes');\\n                              },\\n                            });\\n                        },\\n                        error: (error) => {\\n                          this.alert.error(error.error?.detail ?? 'Error al crear el historial de revisiones');\\n                          this.monthlyReportService\\n                            .getByContractorContractId(contractorContract.id)\\n                            .subscribe({\\n                              next: (monthlyReports) => {\\n                                this.monthlyReports = monthlyReports;\\n                                this.dataSource.data = this.monthlyReports;\\n                                this.alert.success(\\n                                  'Informe mensual creado correctamente',\\n                                );\\n                                this.openReportDetailsForm(createdReport);\\n                              },\\n                              error: (error) => {\\n                                this.alert.error(error.error?.detail ?? 'Error al actualizar la lista de informes');\\n                              },\\n                            });\\n                        },\\n                      });\\n                  },\\n                  error: (error) => {\\n                    this.alert.error(error.error?.detail ?? 'Error al crear el nuevo informe.');\\n                  },\\n                });\\n            }\\n          }\\n        },\\n        error: (error) => {\\n          this.alert.error(error.error?.detail ?? 'Error al crear el nuevo informe.');\\n        },\\n      });\\n  }\\n\\n  get canCreateNewReport(): boolean {\\n    if (this.monthlyReports.length === 0) return true;\\n\\n    const sortedReports = [...this.monthlyReports].sort(\\n      (a, b) => b.reportNumber - a.reportNumber,\\n    );\\n    const lastReport = sortedReports[0];\\n\\n    return lastReport.currentReviewStatus?.name === 'Aprobado';\\n  }\\n\\n  downloadPdf(report: MonthlyReport): void {\\n    if (!report.id) return;\\n\\n    this.spinner.show();\\n    this.monthlyReportService\\n      .downloadPdf(report.id)\\n      .pipe(finalize(() => this.spinner.hide()))\\n      .subscribe({\\n        next: (blob) => {\\n          const url = window.URL.createObjectURL(blob);\\n          const link = document.createElement('a');\\n          link.href = url;\\n          link.download = `Informe_Mensual_${report.reportNumber}.pdf`;\\n          link.click();\\n          window.URL.revokeObjectURL(url);\\n          this.alert.success('PDF descargado exitosamente');\\n        },\\n        error: (error) => {\\n          this.alert.error(error.error?.detail ?? 'Error al descargar el PDF');\\n        },\\n      });\\n  }\\n\\n  openReviewHistoryDialog(report: MonthlyReport): void {\\n    this.dialog.open(MonthlyReportReviewHistoryDialogComponent, {\\n      width: '1000px',\\n      data: { monthlyReportId: report.id },\\n    });\\n  }\\n}\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"3294b6d67a0630b3fa7a0955d7dc403d2cb4a052\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_2456r4fjl6 = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_2456r4fjl6();\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./monthly-reports-tab.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./monthly-reports-tab.component.scss?ngResource\";\nimport { Component, Input, ViewChild } from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatDialog } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSort, MatSortModule } from '@angular/material/sort';\nimport { MatTableDataSource, MatTableModule } from '@angular/material/table';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { finalize, map, switchMap } from 'rxjs';\nimport { CurrencyPipe, DatePipe } from '@angular/common';\nimport { ContractorContractService } from '@contract-management/services/contractor-contract.service';\nimport { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';\nimport { PeriodService } from '@contractor-dashboard/services/period.service';\nimport { ReportReviewHistoryService } from '@contractor-dashboard/services/report-review-history.service';\nimport { ReportReviewStatusService } from '@contractor-dashboard/services/report-review-status.service';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { MonthlyReportDialogComponent } from './monthly-report-dialog/monthly-report-dialog.component';\nimport { MonthlyReportReviewHistoryDialogComponent } from './monthly-report-review-history-dialog/monthly-report-review-history-dialog.component';\nimport { SelectPeriodDialogComponent } from './select-period-dialog/select-period-dialog.component';\ncov_2456r4fjl6().s[0]++;\nlet MonthlyReportsTabComponent = class MonthlyReportsTabComponent {\n  constructor(dialog, alert, monthlyReportService, contractorContractService, reportReviewStatusService, periodService, spinner, reportReviewHistoryService, authService) {\n    cov_2456r4fjl6().f[0]++;\n    cov_2456r4fjl6().s[1]++;\n    this.dialog = dialog;\n    cov_2456r4fjl6().s[2]++;\n    this.alert = alert;\n    cov_2456r4fjl6().s[3]++;\n    this.monthlyReportService = monthlyReportService;\n    cov_2456r4fjl6().s[4]++;\n    this.contractorContractService = contractorContractService;\n    cov_2456r4fjl6().s[5]++;\n    this.reportReviewStatusService = reportReviewStatusService;\n    cov_2456r4fjl6().s[6]++;\n    this.periodService = periodService;\n    cov_2456r4fjl6().s[7]++;\n    this.spinner = spinner;\n    cov_2456r4fjl6().s[8]++;\n    this.reportReviewHistoryService = reportReviewHistoryService;\n    cov_2456r4fjl6().s[9]++;\n    this.authService = authService;\n    cov_2456r4fjl6().s[10]++;\n    this.monthlyReports = [];\n    cov_2456r4fjl6().s[11]++;\n    this.displayedColumns = ['reportNumber', 'startDate', 'endDate', 'currentReviewStatus', 'totalValue', 'actions'];\n    cov_2456r4fjl6().s[12]++;\n    this.dataSource = new MatTableDataSource();\n  }\n  ngOnChanges(changes) {\n    cov_2456r4fjl6().f[1]++;\n    cov_2456r4fjl6().s[13]++;\n    if (changes['monthlyReports']) {\n      cov_2456r4fjl6().b[0][0]++;\n      cov_2456r4fjl6().s[14]++;\n      this.dataSource.data = this.monthlyReports;\n      cov_2456r4fjl6().s[15]++;\n      if (this.dataSource.paginator) {\n        cov_2456r4fjl6().b[1][0]++;\n        cov_2456r4fjl6().s[16]++;\n        this.dataSource.paginator.firstPage();\n      } else {\n        cov_2456r4fjl6().b[1][1]++;\n      }\n    } else {\n      cov_2456r4fjl6().b[0][1]++;\n    }\n  }\n  ngAfterViewInit() {\n    cov_2456r4fjl6().f[2]++;\n    cov_2456r4fjl6().s[17]++;\n    this.dataSource.paginator = this.paginator;\n    cov_2456r4fjl6().s[18]++;\n    this.dataSource.sort = this.sort;\n  }\n  applyFilter(event) {\n    cov_2456r4fjl6().f[3]++;\n    const filterValue = (cov_2456r4fjl6().s[19]++, event.target.value);\n    cov_2456r4fjl6().s[20]++;\n    this.dataSource.filter = filterValue.trim().toLowerCase();\n    cov_2456r4fjl6().s[21]++;\n    if (this.dataSource.paginator) {\n      cov_2456r4fjl6().b[2][0]++;\n      cov_2456r4fjl6().s[22]++;\n      this.dataSource.paginator.firstPage();\n    } else {\n      cov_2456r4fjl6().b[2][1]++;\n    }\n  }\n  openReportDetailsForm(report) {\n    cov_2456r4fjl6().f[4]++;\n    const dialogRef = (cov_2456r4fjl6().s[23]++, this.dialog.open(MonthlyReportDialogComponent, {\n      width: '90vw',\n      height: '90vh',\n      maxWidth: '100vw',\n      maxHeight: '100vh',\n      data: {\n        report\n      }\n    }));\n    cov_2456r4fjl6().s[24]++;\n    dialogRef.afterClosed().subscribe(result => {\n      cov_2456r4fjl6().f[5]++;\n      cov_2456r4fjl6().s[25]++;\n      if ((cov_2456r4fjl6().b[4][0]++, result) && (cov_2456r4fjl6().b[4][1]++, this.contractorContractId)) {\n        cov_2456r4fjl6().b[3][0]++;\n        cov_2456r4fjl6().s[26]++;\n        this.monthlyReportService.getByContractorContractId(this.contractorContractId).subscribe({\n          next: monthlyReports => {\n            cov_2456r4fjl6().f[6]++;\n            cov_2456r4fjl6().s[27]++;\n            this.monthlyReports = monthlyReports;\n            cov_2456r4fjl6().s[28]++;\n            this.dataSource.data = monthlyReports;\n          },\n          error: error => {\n            cov_2456r4fjl6().f[7]++;\n            cov_2456r4fjl6().s[29]++;\n            this.alert.error((cov_2456r4fjl6().b[5][0]++, error.error?.detail) ?? (cov_2456r4fjl6().b[5][1]++, 'Error al actualizar la tabla de informes'));\n          }\n        });\n      } else {\n        cov_2456r4fjl6().b[3][1]++;\n      }\n    });\n  }\n  createNewReport() {\n    cov_2456r4fjl6().f[8]++;\n    cov_2456r4fjl6().s[30]++;\n    this.spinner.show();\n    cov_2456r4fjl6().s[31]++;\n    if ((cov_2456r4fjl6().b[7][0]++, !this.contractorContractId) || (cov_2456r4fjl6().b[7][1]++, !this.contractStartDate)) {\n      cov_2456r4fjl6().b[6][0]++;\n      cov_2456r4fjl6().s[32]++;\n      this.alert.warning('No se puede crear un informe sin un contrato asociado');\n      cov_2456r4fjl6().s[33]++;\n      this.spinner.hide();\n      cov_2456r4fjl6().s[34]++;\n      return;\n    } else {\n      cov_2456r4fjl6().b[6][1]++;\n    }\n    cov_2456r4fjl6().s[35]++;\n    this.contractorContractService.getById(this.contractorContractId).pipe(switchMap(contractorContract => {\n      cov_2456r4fjl6().f[9]++;\n      cov_2456r4fjl6().s[36]++;\n      if (!contractorContract.contract?.id) {\n        cov_2456r4fjl6().b[8][0]++;\n        cov_2456r4fjl6().s[37]++;\n        throw new Error('Error: No se encontró el ID del contrato');\n      } else {\n        cov_2456r4fjl6().b[8][1]++;\n      }\n      cov_2456r4fjl6().s[38]++;\n      if (this.monthlyReports.length === 0) {\n        cov_2456r4fjl6().b[9][0]++;\n        cov_2456r4fjl6().s[39]++;\n        this.spinner.hide();\n        const dialogRef = (cov_2456r4fjl6().s[40]++, this.dialog.open(SelectPeriodDialogComponent, {\n          maxWidth: '600px',\n          width: '100%',\n          data: {\n            contractId: contractorContract.contract.id,\n            contractorContractId: this.contractorContractId\n          },\n          autoFocus: 'dialog'\n        }));\n        cov_2456r4fjl6().s[41]++;\n        return dialogRef.afterClosed().pipe(switchMap(selectedPeriod => {\n          cov_2456r4fjl6().f[10]++;\n          cov_2456r4fjl6().s[42]++;\n          if (selectedPeriod) {\n            cov_2456r4fjl6().b[10][0]++;\n            cov_2456r4fjl6().s[43]++;\n            this.spinner.show();\n            cov_2456r4fjl6().s[44]++;\n            return this.monthlyReportService.getByContractorContractId(this.contractorContractId).pipe(map(monthlyReports => {\n              cov_2456r4fjl6().f[11]++;\n              cov_2456r4fjl6().s[45]++;\n              return {\n                monthlyReports,\n                selectedPeriod\n              };\n            }));\n          } else {\n            cov_2456r4fjl6().b[10][1]++;\n            cov_2456r4fjl6().s[46]++;\n            return [];\n          }\n        }));\n      } else {\n        cov_2456r4fjl6().b[9][1]++;\n        cov_2456r4fjl6().s[47]++;\n        this.monthlyReports.sort((a, b) => {\n          cov_2456r4fjl6().f[12]++;\n          cov_2456r4fjl6().s[48]++;\n          return a.id - b.id;\n        });\n        const lastReport = (cov_2456r4fjl6().s[49]++, this.monthlyReports[this.monthlyReports.length - 1]);\n        const newReportNumber = (cov_2456r4fjl6().s[50]++, lastReport ? (cov_2456r4fjl6().b[11][0]++, lastReport.reportNumber + 1) : (cov_2456r4fjl6().b[11][1]++, 1));\n        const newPeriod = (cov_2456r4fjl6().s[51]++, {\n          id_contrat: contractorContract.contract.id,\n          num_payment: newReportNumber\n        });\n        cov_2456r4fjl6().s[52]++;\n        return this.periodService.create(newPeriod).pipe(switchMap(period => {\n          cov_2456r4fjl6().f[13]++;\n          cov_2456r4fjl6().s[53]++;\n          return this.reportReviewStatusService.getByName('Borrador').pipe(map(reportReviewStatus => {\n            cov_2456r4fjl6().f[14]++;\n            cov_2456r4fjl6().s[54]++;\n            return {\n              period,\n              reportReviewStatus,\n              contractorContract,\n              newReportNumber\n            };\n          }));\n        }));\n      }\n    }), finalize(() => {\n      cov_2456r4fjl6().f[15]++;\n      cov_2456r4fjl6().s[55]++;\n      return this.spinner.hide();\n    })).subscribe({\n      next: ({\n        monthlyReports,\n        period,\n        reportReviewStatus,\n        contractorContract,\n        newReportNumber,\n        selectedPeriod\n      }) => {\n        cov_2456r4fjl6().f[16]++;\n        cov_2456r4fjl6().s[56]++;\n        if (monthlyReports) {\n          cov_2456r4fjl6().b[12][0]++;\n          cov_2456r4fjl6().s[57]++;\n          this.monthlyReports = monthlyReports;\n          cov_2456r4fjl6().s[58]++;\n          this.dataSource.data = this.monthlyReports;\n          cov_2456r4fjl6().s[59]++;\n          if (selectedPeriod) {\n            cov_2456r4fjl6().b[13][0]++;\n            const reportToOpen = (cov_2456r4fjl6().s[60]++, this.monthlyReports.find(r => {\n              cov_2456r4fjl6().f[17]++;\n              cov_2456r4fjl6().s[61]++;\n              return r.reportNumber === selectedPeriod.num_payment;\n            }));\n            cov_2456r4fjl6().s[62]++;\n            if (reportToOpen) {\n              cov_2456r4fjl6().b[14][0]++;\n              cov_2456r4fjl6().s[63]++;\n              if (reportToOpen.reportNumber === 1) {\n                cov_2456r4fjl6().b[15][0]++;\n                cov_2456r4fjl6().s[64]++;\n                this.alert.success('Informe mensual creado correctamente');\n              } else {\n                cov_2456r4fjl6().b[15][1]++;\n                cov_2456r4fjl6().s[65]++;\n                this.alert.success('Informes mensuales creados correctamente');\n              }\n              cov_2456r4fjl6().s[66]++;\n              this.openReportDetailsForm(reportToOpen);\n            } else {\n              cov_2456r4fjl6().b[14][1]++;\n            }\n          } else {\n            cov_2456r4fjl6().b[13][1]++;\n          }\n        } else {\n          cov_2456r4fjl6().b[12][1]++;\n          cov_2456r4fjl6().s[67]++;\n          if ((cov_2456r4fjl6().b[17][0]++, period) && (cov_2456r4fjl6().b[17][1]++, reportReviewStatus) && (cov_2456r4fjl6().b[17][2]++, contractorContract) && (cov_2456r4fjl6().b[17][3]++, newReportNumber)) {\n            cov_2456r4fjl6().b[16][0]++;\n            cov_2456r4fjl6().s[68]++;\n            if (reportReviewStatus.id) {\n              cov_2456r4fjl6().b[18][0]++;\n              cov_2456r4fjl6().s[69]++;\n              this.Period = period;\n              cov_2456r4fjl6().s[70]++;\n              this.monthlyReportService.create({\n                contractorContractId: contractorContract.id,\n                reportNumber: newReportNumber,\n                startDate: period.start_date,\n                endDate: period.end_date,\n                totalValue: period.payment,\n                creationDate: new Date().toISOString().slice(0, 10),\n                contractYear: contractorContract.contract?.contractYear?.year\n              }).subscribe({\n                next: createdReport => {\n                  cov_2456r4fjl6().f[18]++;\n                  const currentUser = (cov_2456r4fjl6().s[71]++, this.authService.getCurrentUser());\n                  cov_2456r4fjl6().s[72]++;\n                  if (!currentUser?.id) {\n                    cov_2456r4fjl6().b[19][0]++;\n                    cov_2456r4fjl6().s[73]++;\n                    this.alert.error('Error: No se pudo obtener el ID del usuario actual');\n                    cov_2456r4fjl6().s[74]++;\n                    return;\n                  } else {\n                    cov_2456r4fjl6().b[19][1]++;\n                  }\n                  cov_2456r4fjl6().s[75]++;\n                  this.reportReviewHistoryService.create({\n                    monthlyReportId: createdReport.id,\n                    reviewStatusId: reportReviewStatus.id,\n                    reviewDate: new Date(),\n                    comment: 'Informe creado',\n                    reviewerId: currentUser.id\n                  }).subscribe({\n                    next: () => {\n                      cov_2456r4fjl6().f[19]++;\n                      cov_2456r4fjl6().s[76]++;\n                      this.monthlyReportService.getByContractorContractId(contractorContract.id).subscribe({\n                        next: monthlyReports => {\n                          cov_2456r4fjl6().f[20]++;\n                          cov_2456r4fjl6().s[77]++;\n                          this.monthlyReports = monthlyReports;\n                          cov_2456r4fjl6().s[78]++;\n                          this.dataSource.data = this.monthlyReports;\n                          cov_2456r4fjl6().s[79]++;\n                          this.alert.success('Informe mensual creado correctamente');\n                          cov_2456r4fjl6().s[80]++;\n                          this.openReportDetailsForm(createdReport);\n                        },\n                        error: error => {\n                          cov_2456r4fjl6().f[21]++;\n                          cov_2456r4fjl6().s[81]++;\n                          this.alert.error((cov_2456r4fjl6().b[20][0]++, error.error?.detail) ?? (cov_2456r4fjl6().b[20][1]++, 'Error al actualizar la lista de informes'));\n                        }\n                      });\n                    },\n                    error: error => {\n                      cov_2456r4fjl6().f[22]++;\n                      cov_2456r4fjl6().s[82]++;\n                      this.alert.error((cov_2456r4fjl6().b[21][0]++, error.error?.detail) ?? (cov_2456r4fjl6().b[21][1]++, 'Error al crear el historial de revisiones'));\n                      cov_2456r4fjl6().s[83]++;\n                      this.monthlyReportService.getByContractorContractId(contractorContract.id).subscribe({\n                        next: monthlyReports => {\n                          cov_2456r4fjl6().f[23]++;\n                          cov_2456r4fjl6().s[84]++;\n                          this.monthlyReports = monthlyReports;\n                          cov_2456r4fjl6().s[85]++;\n                          this.dataSource.data = this.monthlyReports;\n                          cov_2456r4fjl6().s[86]++;\n                          this.alert.success('Informe mensual creado correctamente');\n                          cov_2456r4fjl6().s[87]++;\n                          this.openReportDetailsForm(createdReport);\n                        },\n                        error: error => {\n                          cov_2456r4fjl6().f[24]++;\n                          cov_2456r4fjl6().s[88]++;\n                          this.alert.error((cov_2456r4fjl6().b[22][0]++, error.error?.detail) ?? (cov_2456r4fjl6().b[22][1]++, 'Error al actualizar la lista de informes'));\n                        }\n                      });\n                    }\n                  });\n                },\n                error: error => {\n                  cov_2456r4fjl6().f[25]++;\n                  cov_2456r4fjl6().s[89]++;\n                  this.alert.error((cov_2456r4fjl6().b[23][0]++, error.error?.detail) ?? (cov_2456r4fjl6().b[23][1]++, 'Error al crear el nuevo informe.'));\n                }\n              });\n            } else {\n              cov_2456r4fjl6().b[18][1]++;\n            }\n          } else {\n            cov_2456r4fjl6().b[16][1]++;\n          }\n        }\n      },\n      error: error => {\n        cov_2456r4fjl6().f[26]++;\n        cov_2456r4fjl6().s[90]++;\n        this.alert.error((cov_2456r4fjl6().b[24][0]++, error.error?.detail) ?? (cov_2456r4fjl6().b[24][1]++, 'Error al crear el nuevo informe.'));\n      }\n    });\n  }\n  get canCreateNewReport() {\n    cov_2456r4fjl6().f[27]++;\n    cov_2456r4fjl6().s[91]++;\n    if (this.monthlyReports.length === 0) {\n      cov_2456r4fjl6().b[25][0]++;\n      cov_2456r4fjl6().s[92]++;\n      return true;\n    } else {\n      cov_2456r4fjl6().b[25][1]++;\n    }\n    const sortedReports = (cov_2456r4fjl6().s[93]++, [...this.monthlyReports].sort((a, b) => {\n      cov_2456r4fjl6().f[28]++;\n      cov_2456r4fjl6().s[94]++;\n      return b.reportNumber - a.reportNumber;\n    }));\n    const lastReport = (cov_2456r4fjl6().s[95]++, sortedReports[0]);\n    cov_2456r4fjl6().s[96]++;\n    return lastReport.currentReviewStatus?.name === 'Aprobado';\n  }\n  downloadPdf(report) {\n    cov_2456r4fjl6().f[29]++;\n    cov_2456r4fjl6().s[97]++;\n    if (!report.id) {\n      cov_2456r4fjl6().b[26][0]++;\n      cov_2456r4fjl6().s[98]++;\n      return;\n    } else {\n      cov_2456r4fjl6().b[26][1]++;\n    }\n    cov_2456r4fjl6().s[99]++;\n    this.spinner.show();\n    cov_2456r4fjl6().s[100]++;\n    this.monthlyReportService.downloadPdf(report.id).pipe(finalize(() => {\n      cov_2456r4fjl6().f[30]++;\n      cov_2456r4fjl6().s[101]++;\n      return this.spinner.hide();\n    })).subscribe({\n      next: blob => {\n        cov_2456r4fjl6().f[31]++;\n        const url = (cov_2456r4fjl6().s[102]++, window.URL.createObjectURL(blob));\n        const link = (cov_2456r4fjl6().s[103]++, document.createElement('a'));\n        cov_2456r4fjl6().s[104]++;\n        link.href = url;\n        cov_2456r4fjl6().s[105]++;\n        link.download = `Informe_Mensual_${report.reportNumber}.pdf`;\n        cov_2456r4fjl6().s[106]++;\n        link.click();\n        cov_2456r4fjl6().s[107]++;\n        window.URL.revokeObjectURL(url);\n        cov_2456r4fjl6().s[108]++;\n        this.alert.success('PDF descargado exitosamente');\n      },\n      error: error => {\n        cov_2456r4fjl6().f[32]++;\n        cov_2456r4fjl6().s[109]++;\n        this.alert.error((cov_2456r4fjl6().b[27][0]++, error.error?.detail) ?? (cov_2456r4fjl6().b[27][1]++, 'Error al descargar el PDF'));\n      }\n    });\n  }\n  openReviewHistoryDialog(report) {\n    cov_2456r4fjl6().f[33]++;\n    cov_2456r4fjl6().s[110]++;\n    this.dialog.open(MonthlyReportReviewHistoryDialogComponent, {\n      width: '1000px',\n      data: {\n        monthlyReportId: report.id\n      }\n    });\n  }\n  static {\n    cov_2456r4fjl6().s[111]++;\n    this.ctorParameters = () => {\n      cov_2456r4fjl6().f[34]++;\n      cov_2456r4fjl6().s[112]++;\n      return [{\n        type: MatDialog\n      }, {\n        type: AlertService\n      }, {\n        type: MonthlyReportService\n      }, {\n        type: ContractorContractService\n      }, {\n        type: ReportReviewStatusService\n      }, {\n        type: PeriodService\n      }, {\n        type: NgxSpinnerService\n      }, {\n        type: ReportReviewHistoryService\n      }, {\n        type: AuthService\n      }];\n    };\n  }\n  static {\n    cov_2456r4fjl6().s[113]++;\n    this.propDecorators = {\n      monthlyReports: [{\n        type: Input\n      }],\n      contractorContractId: [{\n        type: Input\n      }],\n      contractStartDate: [{\n        type: Input\n      }],\n      paginator: [{\n        type: ViewChild,\n        args: [MatPaginator]\n      }],\n      sort: [{\n        type: ViewChild,\n        args: [MatSort]\n      }]\n    };\n  }\n};\ncov_2456r4fjl6().s[114]++;\nMonthlyReportsTabComponent = __decorate([Component({\n  selector: 'app-monthly-reports-tab',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: true,\n  imports: [MatTableModule, MatPaginatorModule, MatSortModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatIconModule, MatTooltipModule, MatCardModule, DatePipe, CurrencyPipe],\n  styles: [__NG_CLI_RESOURCE__1]\n})], MonthlyReportsTabComponent);\nexport { MonthlyReportsTabComponent };", "map": {"version": 3, "names": ["cov_2456r4fjl6", "actualCoverage", "Component", "Input", "ViewChild", "MatButtonModule", "MatCardModule", "MatDialog", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatPaginator", "MatPaginatorModule", "MatSort", "MatSortModule", "MatTableDataSource", "MatTableModule", "MatTooltipModule", "NgxSpinnerService", "finalize", "map", "switchMap", "C<PERSON><PERSON>cyPipe", "DatePipe", "ContractorContractService", "MonthlyReportService", "PeriodService", "ReportReviewHistoryService", "ReportReviewStatusService", "AuthService", "AlertService", "MonthlyReportDialogComponent", "MonthlyReportReviewHistoryDialogComponent", "SelectPeriodDialogComponent", "s", "MonthlyReportsTabComponent", "constructor", "dialog", "alert", "monthlyReportService", "contractorContractService", "reportReviewStatusService", "periodService", "spinner", "reportReviewHistoryService", "authService", "f", "monthlyReports", "displayedColumns", "dataSource", "ngOnChanges", "changes", "b", "data", "paginator", "firstPage", "ngAfterViewInit", "sort", "applyFilter", "event", "filterValue", "target", "value", "filter", "trim", "toLowerCase", "openReportDetailsForm", "report", "dialogRef", "open", "width", "height", "max<PERSON><PERSON><PERSON>", "maxHeight", "afterClosed", "subscribe", "result", "contractorContractId", "getByContractorContractId", "next", "error", "detail", "createNewReport", "show", "contractStartDate", "warning", "hide", "getById", "pipe", "contractorContract", "contract", "id", "Error", "length", "contractId", "autoFocus", "<PERSON><PERSON><PERSON><PERSON>", "a", "lastReport", "newReportNumber", "reportNumber", "newPeriod", "id_contrat", "num_payment", "create", "period", "getByName", "reportReviewStatus", "reportToOpen", "find", "r", "success", "Period", "startDate", "start_date", "endDate", "end_date", "totalValue", "payment", "creationDate", "Date", "toISOString", "slice", "contractYear", "year", "createdReport", "currentUser", "getCurrentUser", "monthlyReportId", "reviewStatusId", "reviewDate", "comment", "reviewerId", "canCreateNewReport", "sortedReports", "currentReviewStatus", "name", "downloadPdf", "blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "click", "revokeObjectURL", "openReviewHistoryDialog", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\components\\monthly-reports-tab\\monthly-reports-tab.component.ts"], "sourcesContent": ["import {\n  AfterViewInit,\n  Component,\n  Input,\n  OnChanges,\n  SimpleChanges,\n  ViewChild,\n} from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatDialog } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSort, MatSortModule } from '@angular/material/sort';\nimport { MatTableDataSource, MatTableModule } from '@angular/material/table';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { finalize, map, switchMap } from 'rxjs';\n\nimport { CurrencyPipe, DatePipe } from '@angular/common';\nimport { ContractorContractService } from '@contract-management/services/contractor-contract.service';\nimport { ConsultPeriod } from '@contractor-dashboard/models/ConsultPeriod.model';\nimport { MonthlyReport } from '@contractor-dashboard/models/monthly-report.model';\nimport { Period } from '@contractor-dashboard/models/Period.model';\nimport { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';\nimport { PeriodService } from '@contractor-dashboard/services/period.service';\nimport { ReportReviewHistoryService } from '@contractor-dashboard/services/report-review-history.service';\nimport { ReportReviewStatusService } from '@contractor-dashboard/services/report-review-status.service';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { MonthlyReportDialogComponent } from './monthly-report-dialog/monthly-report-dialog.component';\nimport { MonthlyReportReviewHistoryDialogComponent } from './monthly-report-review-history-dialog/monthly-report-review-history-dialog.component';\nimport { SelectPeriodDialogComponent } from './select-period-dialog/select-period-dialog.component';\nimport { ReportReviewStatus } from '@contractor-dashboard/models/report-review-status.model';\nimport { ContractorContract } from '@contract-management/models/contractor-contract.model';\n\n@Component({\n  selector: 'app-monthly-reports-tab',\n  templateUrl: './monthly-reports-tab.component.html',\n  styleUrl: './monthly-reports-tab.component.scss',\n  standalone: true,\n  imports: [\n    MatTableModule,\n    MatPaginatorModule,\n    MatSortModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatIconModule,\n    MatTooltipModule,\n    MatCardModule,\n    DatePipe,\n    CurrencyPipe,\n  ],\n})\nexport class MonthlyReportsTabComponent implements OnChanges, AfterViewInit {\n  @Input() monthlyReports: MonthlyReport[] = [];\n  @Input() contractorContractId: number | undefined;\n  @Input() contractStartDate: Date | undefined;\n\n  @ViewChild(MatPaginator) paginator!: MatPaginator;\n  @ViewChild(MatSort) sort!: MatSort;\n\n  Period!: Period;\n  displayedColumns: string[] = [\n    'reportNumber',\n    'startDate',\n    'endDate',\n    'currentReviewStatus',\n    'totalValue',\n    'actions',\n  ];\n  dataSource = new MatTableDataSource<MonthlyReport>();\n\n  constructor(\n    private readonly dialog: MatDialog,\n    private readonly alert: AlertService,\n    private readonly monthlyReportService: MonthlyReportService,\n    private readonly contractorContractService: ContractorContractService,\n    private readonly reportReviewStatusService: ReportReviewStatusService,\n    private readonly periodService: PeriodService,\n    private readonly spinner: NgxSpinnerService,\n    private readonly reportReviewHistoryService: ReportReviewHistoryService,\n    private readonly authService: AuthService,\n  ) {}\n\n  ngOnChanges(changes: SimpleChanges): void {\n    if (changes['monthlyReports']) {\n      this.dataSource.data = this.monthlyReports;\n      if (this.dataSource.paginator) {\n        this.dataSource.paginator.firstPage();\n      }\n    }\n  }\n\n  ngAfterViewInit(): void {\n    this.dataSource.paginator = this.paginator;\n    this.dataSource.sort = this.sort;\n  }\n\n  applyFilter(event: Event): void {\n    const filterValue = (event.target as HTMLInputElement).value;\n    this.dataSource.filter = filterValue.trim().toLowerCase();\n\n    if (this.dataSource.paginator) {\n      this.dataSource.paginator.firstPage();\n    }\n  }\n\n  openReportDetailsForm(report: MonthlyReport): void {\n    const dialogRef = this.dialog.open(MonthlyReportDialogComponent, {\n      width: '90vw',\n      height: '90vh',\n      maxWidth: '100vw',\n      maxHeight: '100vh',\n      data: { report },\n    });\n\n    dialogRef.afterClosed().subscribe((result: boolean) => {\n      if (result && this.contractorContractId) {\n        this.monthlyReportService\n          .getByContractorContractId(this.contractorContractId)\n          .subscribe({\n            next: (monthlyReports) => {\n              this.monthlyReports = monthlyReports;\n              this.dataSource.data = monthlyReports;\n            },\n            error: (error) => {\n              this.alert.error(error.error?.detail ?? 'Error al actualizar la tabla de informes');\n            },\n          });\n      }\n    });\n  }\n\n  createNewReport(): void {\n    this.spinner.show();\n\n    if (!this.contractorContractId || !this.contractStartDate) {\n      this.alert.warning(\n        'No se puede crear un informe sin un contrato asociado',\n      );\n      this.spinner.hide();\n      return;\n    }\n\n    this.contractorContractService\n      .getById(this.contractorContractId)\n      .pipe(\n        switchMap((contractorContract) => {\n          if (!contractorContract.contract?.id) {\n            throw new Error('Error: No se encontró el ID del contrato');\n          }\n\n          if (this.monthlyReports.length === 0) {\n            this.spinner.hide();\n            const dialogRef = this.dialog.open(SelectPeriodDialogComponent, {\n              maxWidth: '600px',\n              width: '100%',\n              data: {\n                contractId: contractorContract.contract.id,\n                contractorContractId: this.contractorContractId,\n              },\n              autoFocus: 'dialog',\n            });\n\n            return dialogRef.afterClosed().pipe(\n              switchMap((selectedPeriod: Period | undefined) => {\n                if (selectedPeriod) {\n                  this.spinner.show();\n                  return this.monthlyReportService\n                    .getByContractorContractId(this.contractorContractId!)\n                    .pipe(\n                      map((monthlyReports) => ({\n                        monthlyReports,\n                        selectedPeriod,\n                      })),\n                    );\n                } else {\n                  return [];\n                }\n              }),\n            );\n          } else {\n            this.monthlyReports.sort((a, b) => a.id - b.id);\n            const lastReport =\n              this.monthlyReports[this.monthlyReports.length - 1];\n            const newReportNumber = lastReport\n              ? lastReport.reportNumber + 1\n              : 1;\n\n            const newPeriod: Omit<ConsultPeriod, 'id'> = {\n              id_contrat: contractorContract.contract.id,\n              num_payment: newReportNumber,\n            };\n\n            return this.periodService.create(newPeriod).pipe(\n              switchMap((period) => {\n                return this.reportReviewStatusService\n                  .getByName('Borrador')\n                  .pipe(\n                    map((reportReviewStatus) => ({\n                      period,\n                      reportReviewStatus,\n                      contractorContract,\n                      newReportNumber,\n                    })),\n                  );\n              }),\n            );\n          }\n        }),\n        finalize(() => this.spinner.hide()),\n      )\n      .subscribe({\n        next: ({\n          monthlyReports,\n          period,\n          reportReviewStatus,\n          contractorContract,\n          newReportNumber,\n          selectedPeriod,\n        }: {\n          monthlyReports?: MonthlyReport[];\n          period?: Period;\n          reportReviewStatus?: ReportReviewStatus;\n          contractorContract?: ContractorContract;\n          newReportNumber?: number;\n          selectedPeriod?: Period;\n        }) => {\n          if (monthlyReports) {\n            this.monthlyReports = monthlyReports;\n            this.dataSource.data = this.monthlyReports;\n            if (selectedPeriod) {\n              const reportToOpen = this.monthlyReports.find(\n                (r) => r.reportNumber === selectedPeriod.num_payment,\n              );\n              if (reportToOpen) {\n                if (reportToOpen.reportNumber === 1) {\n                  this.alert.success('Informe mensual creado correctamente');\n                } else {\n                  this.alert.success(\n                    'Informes mensuales creados correctamente',\n                  );\n                }\n                this.openReportDetailsForm(reportToOpen);\n              }\n            }\n          } else if (\n            period &&\n            reportReviewStatus &&\n            contractorContract &&\n            newReportNumber\n          ) {\n            if (reportReviewStatus.id) {\n              this.Period = period;\n              this.monthlyReportService\n                .create({\n                  contractorContractId: contractorContract.id,\n                  reportNumber: newReportNumber,\n                  startDate: period.start_date,\n                  endDate: period.end_date,\n                  totalValue: period.payment,\n                  creationDate: new Date().toISOString().slice(0, 10),\n                  contractYear: contractorContract.contract?.contractYear?.year,\n                })\n                .subscribe({\n                  next: (createdReport: MonthlyReport) => {\n                    const currentUser = this.authService.getCurrentUser();\n                    if (!currentUser?.id) {\n                      this.alert.error(\n                        'Error: No se pudo obtener el ID del usuario actual',\n                      );\n                      return;\n                    }\n\n                    this.reportReviewHistoryService\n                      .create({\n                        monthlyReportId: createdReport.id,\n                        reviewStatusId: reportReviewStatus.id,\n                        reviewDate: new Date(),\n                        comment: 'Informe creado',\n                        reviewerId: currentUser.id,\n                      })\n                      .subscribe({\n                        next: () => {\n                          this.monthlyReportService\n                            .getByContractorContractId(contractorContract.id)\n                            .subscribe({\n                              next: (monthlyReports) => {\n                                this.monthlyReports = monthlyReports;\n                                this.dataSource.data = this.monthlyReports;\n                                this.alert.success(\n                                  'Informe mensual creado correctamente',\n                                );\n                                this.openReportDetailsForm(createdReport);\n                              },\n                              error: (error) => {\n                                this.alert.error(error.error?.detail ?? 'Error al actualizar la lista de informes');\n                              },\n                            });\n                        },\n                        error: (error) => {\n                          this.alert.error(error.error?.detail ?? 'Error al crear el historial de revisiones');\n                          this.monthlyReportService\n                            .getByContractorContractId(contractorContract.id)\n                            .subscribe({\n                              next: (monthlyReports) => {\n                                this.monthlyReports = monthlyReports;\n                                this.dataSource.data = this.monthlyReports;\n                                this.alert.success(\n                                  'Informe mensual creado correctamente',\n                                );\n                                this.openReportDetailsForm(createdReport);\n                              },\n                              error: (error) => {\n                                this.alert.error(error.error?.detail ?? 'Error al actualizar la lista de informes');\n                              },\n                            });\n                        },\n                      });\n                  },\n                  error: (error) => {\n                    this.alert.error(error.error?.detail ?? 'Error al crear el nuevo informe.');\n                  },\n                });\n            }\n          }\n        },\n        error: (error) => {\n          this.alert.error(error.error?.detail ?? 'Error al crear el nuevo informe.');\n        },\n      });\n  }\n\n  get canCreateNewReport(): boolean {\n    if (this.monthlyReports.length === 0) return true;\n\n    const sortedReports = [...this.monthlyReports].sort(\n      (a, b) => b.reportNumber - a.reportNumber,\n    );\n    const lastReport = sortedReports[0];\n\n    return lastReport.currentReviewStatus?.name === 'Aprobado';\n  }\n\n  downloadPdf(report: MonthlyReport): void {\n    if (!report.id) return;\n\n    this.spinner.show();\n    this.monthlyReportService\n      .downloadPdf(report.id)\n      .pipe(finalize(() => this.spinner.hide()))\n      .subscribe({\n        next: (blob) => {\n          const url = window.URL.createObjectURL(blob);\n          const link = document.createElement('a');\n          link.href = url;\n          link.download = `Informe_Mensual_${report.reportNumber}.pdf`;\n          link.click();\n          window.URL.revokeObjectURL(url);\n          this.alert.success('PDF descargado exitosamente');\n        },\n        error: (error) => {\n          this.alert.error(error.error?.detail ?? 'Error al descargar el PDF');\n        },\n      });\n  }\n\n  openReviewHistoryDialog(report: MonthlyReport): void {\n    this.dialog.open(MonthlyReportReviewHistoryDialogComponent, {\n      width: '1000px',\n      data: { monthlyReportId: report.id },\n    });\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmBS;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;AAnBT,SAEEE,SAAS,EACTC,KAAK,EAGLC,SAAS,QACJ,eAAe;AACtB,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,6BAA6B;AAC9E,SAASC,OAAO,EAAEC,aAAa,QAAQ,wBAAwB;AAC/D,SAASC,kBAAkB,EAAEC,cAAc,QAAQ,yBAAyB;AAC5E,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,iBAAiB,QAAQ,aAAa;AAC/C,SAASC,QAAQ,EAAEC,GAAG,EAAEC,SAAS,QAAQ,MAAM;AAE/C,SAASC,YAAY,EAAEC,QAAQ,QAAQ,iBAAiB;AACxD,SAASC,yBAAyB,QAAQ,2DAA2D;AAIrG,SAASC,oBAAoB,QAAQ,uDAAuD;AAC5F,SAASC,aAAa,QAAQ,+CAA+C;AAC7E,SAASC,0BAA0B,QAAQ,8DAA8D;AACzG,SAASC,yBAAyB,QAAQ,6DAA6D;AACvG,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,4BAA4B,QAAQ,yDAAyD;AACtG,SAASC,yCAAyC,QAAQ,uFAAuF;AACjJ,SAASC,2BAA2B,QAAQ,uDAAuD;AAACjC,cAAA,GAAAkC,CAAA;AAuB7F,IAAMC,0BAA0B,GAAhC,MAAMA,0BAA0B;EAmBrCC,YACmBC,MAAiB,EACjBC,KAAmB,EACnBC,oBAA0C,EAC1CC,yBAAoD,EACpDC,yBAAoD,EACpDC,aAA4B,EAC5BC,OAA0B,EAC1BC,0BAAsD,EACtDC,WAAwB;IAAA7C,cAAA,GAAA8C,CAAA;IAAA9C,cAAA,GAAAkC,CAAA;IARxB,KAAAG,MAAM,GAANA,MAAM;IAAWrC,cAAA,GAAAkC,CAAA;IACjB,KAAAI,KAAK,GAALA,KAAK;IAActC,cAAA,GAAAkC,CAAA;IACnB,KAAAK,oBAAoB,GAApBA,oBAAoB;IAAsBvC,cAAA,GAAAkC,CAAA;IAC1C,KAAAM,yBAAyB,GAAzBA,yBAAyB;IAA2BxC,cAAA,GAAAkC,CAAA;IACpD,KAAAO,yBAAyB,GAAzBA,yBAAyB;IAA2BzC,cAAA,GAAAkC,CAAA;IACpD,KAAAQ,aAAa,GAAbA,aAAa;IAAe1C,cAAA,GAAAkC,CAAA;IAC5B,KAAAS,OAAO,GAAPA,OAAO;IAAmB3C,cAAA,GAAAkC,CAAA;IAC1B,KAAAU,0BAA0B,GAA1BA,0BAA0B;IAA4B5C,cAAA,GAAAkC,CAAA;IACtD,KAAAW,WAAW,GAAXA,WAAW;IAAa7C,cAAA,GAAAkC,CAAA;IA3BlC,KAAAa,cAAc,GAAoB,EAAE;IAAA/C,cAAA,GAAAkC,CAAA;IAQ7C,KAAAc,gBAAgB,GAAa,CAC3B,cAAc,EACd,WAAW,EACX,SAAS,EACT,qBAAqB,EACrB,YAAY,EACZ,SAAS,CACV;IAAChD,cAAA,GAAAkC,CAAA;IACF,KAAAe,UAAU,GAAG,IAAIlC,kBAAkB,EAAiB;EAYjD;EAEHmC,WAAWA,CAACC,OAAsB;IAAAnD,cAAA,GAAA8C,CAAA;IAAA9C,cAAA,GAAAkC,CAAA;IAChC,IAAIiB,OAAO,CAAC,gBAAgB,CAAC,EAAE;MAAAnD,cAAA,GAAAoD,CAAA;MAAApD,cAAA,GAAAkC,CAAA;MAC7B,IAAI,CAACe,UAAU,CAACI,IAAI,GAAG,IAAI,CAACN,cAAc;MAAC/C,cAAA,GAAAkC,CAAA;MAC3C,IAAI,IAAI,CAACe,UAAU,CAACK,SAAS,EAAE;QAAAtD,cAAA,GAAAoD,CAAA;QAAApD,cAAA,GAAAkC,CAAA;QAC7B,IAAI,CAACe,UAAU,CAACK,SAAS,CAACC,SAAS,EAAE;MACvC,CAAC;QAAAvD,cAAA,GAAAoD,CAAA;MAAA;IACH,CAAC;MAAApD,cAAA,GAAAoD,CAAA;IAAA;EACH;EAEAI,eAAeA,CAAA;IAAAxD,cAAA,GAAA8C,CAAA;IAAA9C,cAAA,GAAAkC,CAAA;IACb,IAAI,CAACe,UAAU,CAACK,SAAS,GAAG,IAAI,CAACA,SAAS;IAACtD,cAAA,GAAAkC,CAAA;IAC3C,IAAI,CAACe,UAAU,CAACQ,IAAI,GAAG,IAAI,CAACA,IAAI;EAClC;EAEAC,WAAWA,CAACC,KAAY;IAAA3D,cAAA,GAAA8C,CAAA;IACtB,MAAMc,WAAW,IAAA5D,cAAA,GAAAkC,CAAA,QAAIyB,KAAK,CAACE,MAA2B,CAACC,KAAK;IAAC9D,cAAA,GAAAkC,CAAA;IAC7D,IAAI,CAACe,UAAU,CAACc,MAAM,GAAGH,WAAW,CAACI,IAAI,EAAE,CAACC,WAAW,EAAE;IAACjE,cAAA,GAAAkC,CAAA;IAE1D,IAAI,IAAI,CAACe,UAAU,CAACK,SAAS,EAAE;MAAAtD,cAAA,GAAAoD,CAAA;MAAApD,cAAA,GAAAkC,CAAA;MAC7B,IAAI,CAACe,UAAU,CAACK,SAAS,CAACC,SAAS,EAAE;IACvC,CAAC;MAAAvD,cAAA,GAAAoD,CAAA;IAAA;EACH;EAEAc,qBAAqBA,CAACC,MAAqB;IAAAnE,cAAA,GAAA8C,CAAA;IACzC,MAAMsB,SAAS,IAAApE,cAAA,GAAAkC,CAAA,QAAG,IAAI,CAACG,MAAM,CAACgC,IAAI,CAACtC,4BAA4B,EAAE;MAC/DuC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,OAAO;MACjBC,SAAS,EAAE,OAAO;MAClBpB,IAAI,EAAE;QAAEc;MAAM;KACf,CAAC;IAACnE,cAAA,GAAAkC,CAAA;IAEHkC,SAAS,CAACM,WAAW,EAAE,CAACC,SAAS,CAAEC,MAAe,IAAI;MAAA5E,cAAA,GAAA8C,CAAA;MAAA9C,cAAA,GAAAkC,CAAA;MACpD,IAAI,CAAAlC,cAAA,GAAAoD,CAAA,UAAAwB,MAAM,MAAA5E,cAAA,GAAAoD,CAAA,UAAI,IAAI,CAACyB,oBAAoB,GAAE;QAAA7E,cAAA,GAAAoD,CAAA;QAAApD,cAAA,GAAAkC,CAAA;QACvC,IAAI,CAACK,oBAAoB,CACtBuC,yBAAyB,CAAC,IAAI,CAACD,oBAAoB,CAAC,CACpDF,SAAS,CAAC;UACTI,IAAI,EAAGhC,cAAc,IAAI;YAAA/C,cAAA,GAAA8C,CAAA;YAAA9C,cAAA,GAAAkC,CAAA;YACvB,IAAI,CAACa,cAAc,GAAGA,cAAc;YAAC/C,cAAA,GAAAkC,CAAA;YACrC,IAAI,CAACe,UAAU,CAACI,IAAI,GAAGN,cAAc;UACvC,CAAC;UACDiC,KAAK,EAAGA,KAAK,IAAI;YAAAhF,cAAA,GAAA8C,CAAA;YAAA9C,cAAA,GAAAkC,CAAA;YACf,IAAI,CAACI,KAAK,CAAC0C,KAAK,CAAC,CAAAhF,cAAA,GAAAoD,CAAA,UAAA4B,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAAjF,cAAA,GAAAoD,CAAA,UAAI,0CAA0C,EAAC;UACrF;SACD,CAAC;MACN,CAAC;QAAApD,cAAA,GAAAoD,CAAA;MAAA;IACH,CAAC,CAAC;EACJ;EAEA8B,eAAeA,CAAA;IAAAlF,cAAA,GAAA8C,CAAA;IAAA9C,cAAA,GAAAkC,CAAA;IACb,IAAI,CAACS,OAAO,CAACwC,IAAI,EAAE;IAACnF,cAAA,GAAAkC,CAAA;IAEpB,IAAI,CAAAlC,cAAA,GAAAoD,CAAA,WAAC,IAAI,CAACyB,oBAAoB,MAAA7E,cAAA,GAAAoD,CAAA,UAAI,CAAC,IAAI,CAACgC,iBAAiB,GAAE;MAAApF,cAAA,GAAAoD,CAAA;MAAApD,cAAA,GAAAkC,CAAA;MACzD,IAAI,CAACI,KAAK,CAAC+C,OAAO,CAChB,uDAAuD,CACxD;MAACrF,cAAA,GAAAkC,CAAA;MACF,IAAI,CAACS,OAAO,CAAC2C,IAAI,EAAE;MAACtF,cAAA,GAAAkC,CAAA;MACpB;IACF,CAAC;MAAAlC,cAAA,GAAAoD,CAAA;IAAA;IAAApD,cAAA,GAAAkC,CAAA;IAED,IAAI,CAACM,yBAAyB,CAC3B+C,OAAO,CAAC,IAAI,CAACV,oBAAoB,CAAC,CAClCW,IAAI,CACHnE,SAAS,CAAEoE,kBAAkB,IAAI;MAAAzF,cAAA,GAAA8C,CAAA;MAAA9C,cAAA,GAAAkC,CAAA;MAC/B,IAAI,CAACuD,kBAAkB,CAACC,QAAQ,EAAEC,EAAE,EAAE;QAAA3F,cAAA,GAAAoD,CAAA;QAAApD,cAAA,GAAAkC,CAAA;QACpC,MAAM,IAAI0D,KAAK,CAAC,0CAA0C,CAAC;MAC7D,CAAC;QAAA5F,cAAA,GAAAoD,CAAA;MAAA;MAAApD,cAAA,GAAAkC,CAAA;MAED,IAAI,IAAI,CAACa,cAAc,CAAC8C,MAAM,KAAK,CAAC,EAAE;QAAA7F,cAAA,GAAAoD,CAAA;QAAApD,cAAA,GAAAkC,CAAA;QACpC,IAAI,CAACS,OAAO,CAAC2C,IAAI,EAAE;QACnB,MAAMlB,SAAS,IAAApE,cAAA,GAAAkC,CAAA,QAAG,IAAI,CAACG,MAAM,CAACgC,IAAI,CAACpC,2BAA2B,EAAE;UAC9DuC,QAAQ,EAAE,OAAO;UACjBF,KAAK,EAAE,MAAM;UACbjB,IAAI,EAAE;YACJyC,UAAU,EAAEL,kBAAkB,CAACC,QAAQ,CAACC,EAAE;YAC1Cd,oBAAoB,EAAE,IAAI,CAACA;WAC5B;UACDkB,SAAS,EAAE;SACZ,CAAC;QAAC/F,cAAA,GAAAkC,CAAA;QAEH,OAAOkC,SAAS,CAACM,WAAW,EAAE,CAACc,IAAI,CACjCnE,SAAS,CAAE2E,cAAkC,IAAI;UAAAhG,cAAA,GAAA8C,CAAA;UAAA9C,cAAA,GAAAkC,CAAA;UAC/C,IAAI8D,cAAc,EAAE;YAAAhG,cAAA,GAAAoD,CAAA;YAAApD,cAAA,GAAAkC,CAAA;YAClB,IAAI,CAACS,OAAO,CAACwC,IAAI,EAAE;YAACnF,cAAA,GAAAkC,CAAA;YACpB,OAAO,IAAI,CAACK,oBAAoB,CAC7BuC,yBAAyB,CAAC,IAAI,CAACD,oBAAqB,CAAC,CACrDW,IAAI,CACHpE,GAAG,CAAE2B,cAAc,IAAM;cAAA/C,cAAA,GAAA8C,CAAA;cAAA9C,cAAA,GAAAkC,CAAA;cAAA;gBACvBa,cAAc;gBACdiD;eACD;aAAC,CAAC,CACJ;UACL,CAAC,MAAM;YAAAhG,cAAA,GAAAoD,CAAA;YAAApD,cAAA,GAAAkC,CAAA;YACL,OAAO,EAAE;UACX;QACF,CAAC,CAAC,CACH;MACH,CAAC,MAAM;QAAAlC,cAAA,GAAAoD,CAAA;QAAApD,cAAA,GAAAkC,CAAA;QACL,IAAI,CAACa,cAAc,CAACU,IAAI,CAAC,CAACwC,CAAC,EAAE7C,CAAC,KAAK;UAAApD,cAAA,GAAA8C,CAAA;UAAA9C,cAAA,GAAAkC,CAAA;UAAA,OAAA+D,CAAC,CAACN,EAAE,GAAGvC,CAAC,CAACuC,EAAE;QAAF,CAAE,CAAC;QAC/C,MAAMO,UAAU,IAAAlG,cAAA,GAAAkC,CAAA,QACd,IAAI,CAACa,cAAc,CAAC,IAAI,CAACA,cAAc,CAAC8C,MAAM,GAAG,CAAC,CAAC;QACrD,MAAMM,eAAe,IAAAnG,cAAA,GAAAkC,CAAA,QAAGgE,UAAU,IAAAlG,cAAA,GAAAoD,CAAA,WAC9B8C,UAAU,CAACE,YAAY,GAAG,CAAC,KAAApG,cAAA,GAAAoD,CAAA,WAC3B,CAAC;QAEL,MAAMiD,SAAS,IAAArG,cAAA,GAAAkC,CAAA,QAA8B;UAC3CoE,UAAU,EAAEb,kBAAkB,CAACC,QAAQ,CAACC,EAAE;UAC1CY,WAAW,EAAEJ;SACd;QAACnG,cAAA,GAAAkC,CAAA;QAEF,OAAO,IAAI,CAACQ,aAAa,CAAC8D,MAAM,CAACH,SAAS,CAAC,CAACb,IAAI,CAC9CnE,SAAS,CAAEoF,MAAM,IAAI;UAAAzG,cAAA,GAAA8C,CAAA;UAAA9C,cAAA,GAAAkC,CAAA;UACnB,OAAO,IAAI,CAACO,yBAAyB,CAClCiE,SAAS,CAAC,UAAU,CAAC,CACrBlB,IAAI,CACHpE,GAAG,CAAEuF,kBAAkB,IAAM;YAAA3G,cAAA,GAAA8C,CAAA;YAAA9C,cAAA,GAAAkC,CAAA;YAAA;cAC3BuE,MAAM;cACNE,kBAAkB;cAClBlB,kBAAkB;cAClBU;aACD;WAAC,CAAC,CACJ;QACL,CAAC,CAAC,CACH;MACH;IACF,CAAC,CAAC,EACFhF,QAAQ,CAAC,MAAM;MAAAnB,cAAA,GAAA8C,CAAA;MAAA9C,cAAA,GAAAkC,CAAA;MAAA,WAAI,CAACS,OAAO,CAAC2C,IAAI,EAAE;IAAF,CAAE,CAAC,CACpC,CACAX,SAAS,CAAC;MACTI,IAAI,EAAEA,CAAC;QACLhC,cAAc;QACd0D,MAAM;QACNE,kBAAkB;QAClBlB,kBAAkB;QAClBU,eAAe;QACfH;MAAc,CAQf,KAAI;QAAAhG,cAAA,GAAA8C,CAAA;QAAA9C,cAAA,GAAAkC,CAAA;QACH,IAAIa,cAAc,EAAE;UAAA/C,cAAA,GAAAoD,CAAA;UAAApD,cAAA,GAAAkC,CAAA;UAClB,IAAI,CAACa,cAAc,GAAGA,cAAc;UAAC/C,cAAA,GAAAkC,CAAA;UACrC,IAAI,CAACe,UAAU,CAACI,IAAI,GAAG,IAAI,CAACN,cAAc;UAAC/C,cAAA,GAAAkC,CAAA;UAC3C,IAAI8D,cAAc,EAAE;YAAAhG,cAAA,GAAAoD,CAAA;YAClB,MAAMwD,YAAY,IAAA5G,cAAA,GAAAkC,CAAA,QAAG,IAAI,CAACa,cAAc,CAAC8D,IAAI,CAC1CC,CAAC,IAAK;cAAA9G,cAAA,GAAA8C,CAAA;cAAA9C,cAAA,GAAAkC,CAAA;cAAA,OAAA4E,CAAC,CAACV,YAAY,KAAKJ,cAAc,CAACO,WAAW;YAAX,CAAW,CACrD;YAACvG,cAAA,GAAAkC,CAAA;YACF,IAAI0E,YAAY,EAAE;cAAA5G,cAAA,GAAAoD,CAAA;cAAApD,cAAA,GAAAkC,CAAA;cAChB,IAAI0E,YAAY,CAACR,YAAY,KAAK,CAAC,EAAE;gBAAApG,cAAA,GAAAoD,CAAA;gBAAApD,cAAA,GAAAkC,CAAA;gBACnC,IAAI,CAACI,KAAK,CAACyE,OAAO,CAAC,sCAAsC,CAAC;cAC5D,CAAC,MAAM;gBAAA/G,cAAA,GAAAoD,CAAA;gBAAApD,cAAA,GAAAkC,CAAA;gBACL,IAAI,CAACI,KAAK,CAACyE,OAAO,CAChB,0CAA0C,CAC3C;cACH;cAAC/G,cAAA,GAAAkC,CAAA;cACD,IAAI,CAACgC,qBAAqB,CAAC0C,YAAY,CAAC;YAC1C,CAAC;cAAA5G,cAAA,GAAAoD,CAAA;YAAA;UACH,CAAC;YAAApD,cAAA,GAAAoD,CAAA;UAAA;QACH,CAAC,MAAM;UAAApD,cAAA,GAAAoD,CAAA;UAAApD,cAAA,GAAAkC,CAAA;UAAA,IACL,CAAAlC,cAAA,GAAAoD,CAAA,WAAAqD,MAAM,MAAAzG,cAAA,GAAAoD,CAAA,WACNuD,kBAAkB,MAAA3G,cAAA,GAAAoD,CAAA,WAClBqC,kBAAkB,MAAAzF,cAAA,GAAAoD,CAAA,WAClB+C,eAAe,GACf;YAAAnG,cAAA,GAAAoD,CAAA;YAAApD,cAAA,GAAAkC,CAAA;YACA,IAAIyE,kBAAkB,CAAChB,EAAE,EAAE;cAAA3F,cAAA,GAAAoD,CAAA;cAAApD,cAAA,GAAAkC,CAAA;cACzB,IAAI,CAAC8E,MAAM,GAAGP,MAAM;cAACzG,cAAA,GAAAkC,CAAA;cACrB,IAAI,CAACK,oBAAoB,CACtBiE,MAAM,CAAC;gBACN3B,oBAAoB,EAAEY,kBAAkB,CAACE,EAAE;gBAC3CS,YAAY,EAAED,eAAe;gBAC7Bc,SAAS,EAAER,MAAM,CAACS,UAAU;gBAC5BC,OAAO,EAAEV,MAAM,CAACW,QAAQ;gBACxBC,UAAU,EAAEZ,MAAM,CAACa,OAAO;gBAC1BC,YAAY,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;gBACnDC,YAAY,EAAElC,kBAAkB,CAACC,QAAQ,EAAEiC,YAAY,EAAEC;eAC1D,CAAC,CACDjD,SAAS,CAAC;gBACTI,IAAI,EAAG8C,aAA4B,IAAI;kBAAA7H,cAAA,GAAA8C,CAAA;kBACrC,MAAMgF,WAAW,IAAA9H,cAAA,GAAAkC,CAAA,QAAG,IAAI,CAACW,WAAW,CAACkF,cAAc,EAAE;kBAAC/H,cAAA,GAAAkC,CAAA;kBACtD,IAAI,CAAC4F,WAAW,EAAEnC,EAAE,EAAE;oBAAA3F,cAAA,GAAAoD,CAAA;oBAAApD,cAAA,GAAAkC,CAAA;oBACpB,IAAI,CAACI,KAAK,CAAC0C,KAAK,CACd,oDAAoD,CACrD;oBAAChF,cAAA,GAAAkC,CAAA;oBACF;kBACF,CAAC;oBAAAlC,cAAA,GAAAoD,CAAA;kBAAA;kBAAApD,cAAA,GAAAkC,CAAA;kBAED,IAAI,CAACU,0BAA0B,CAC5B4D,MAAM,CAAC;oBACNwB,eAAe,EAAEH,aAAa,CAAClC,EAAE;oBACjCsC,cAAc,EAAEtB,kBAAkB,CAAChB,EAAE;oBACrCuC,UAAU,EAAE,IAAIV,IAAI,EAAE;oBACtBW,OAAO,EAAE,gBAAgB;oBACzBC,UAAU,EAAEN,WAAW,CAACnC;mBACzB,CAAC,CACDhB,SAAS,CAAC;oBACTI,IAAI,EAAEA,CAAA,KAAK;sBAAA/E,cAAA,GAAA8C,CAAA;sBAAA9C,cAAA,GAAAkC,CAAA;sBACT,IAAI,CAACK,oBAAoB,CACtBuC,yBAAyB,CAACW,kBAAkB,CAACE,EAAE,CAAC,CAChDhB,SAAS,CAAC;wBACTI,IAAI,EAAGhC,cAAc,IAAI;0BAAA/C,cAAA,GAAA8C,CAAA;0BAAA9C,cAAA,GAAAkC,CAAA;0BACvB,IAAI,CAACa,cAAc,GAAGA,cAAc;0BAAC/C,cAAA,GAAAkC,CAAA;0BACrC,IAAI,CAACe,UAAU,CAACI,IAAI,GAAG,IAAI,CAACN,cAAc;0BAAC/C,cAAA,GAAAkC,CAAA;0BAC3C,IAAI,CAACI,KAAK,CAACyE,OAAO,CAChB,sCAAsC,CACvC;0BAAC/G,cAAA,GAAAkC,CAAA;0BACF,IAAI,CAACgC,qBAAqB,CAAC2D,aAAa,CAAC;wBAC3C,CAAC;wBACD7C,KAAK,EAAGA,KAAK,IAAI;0BAAAhF,cAAA,GAAA8C,CAAA;0BAAA9C,cAAA,GAAAkC,CAAA;0BACf,IAAI,CAACI,KAAK,CAAC0C,KAAK,CAAC,CAAAhF,cAAA,GAAAoD,CAAA,WAAA4B,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAAjF,cAAA,GAAAoD,CAAA,WAAI,0CAA0C,EAAC;wBACrF;uBACD,CAAC;oBACN,CAAC;oBACD4B,KAAK,EAAGA,KAAK,IAAI;sBAAAhF,cAAA,GAAA8C,CAAA;sBAAA9C,cAAA,GAAAkC,CAAA;sBACf,IAAI,CAACI,KAAK,CAAC0C,KAAK,CAAC,CAAAhF,cAAA,GAAAoD,CAAA,WAAA4B,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAAjF,cAAA,GAAAoD,CAAA,WAAI,2CAA2C,EAAC;sBAACpD,cAAA,GAAAkC,CAAA;sBACrF,IAAI,CAACK,oBAAoB,CACtBuC,yBAAyB,CAACW,kBAAkB,CAACE,EAAE,CAAC,CAChDhB,SAAS,CAAC;wBACTI,IAAI,EAAGhC,cAAc,IAAI;0BAAA/C,cAAA,GAAA8C,CAAA;0BAAA9C,cAAA,GAAAkC,CAAA;0BACvB,IAAI,CAACa,cAAc,GAAGA,cAAc;0BAAC/C,cAAA,GAAAkC,CAAA;0BACrC,IAAI,CAACe,UAAU,CAACI,IAAI,GAAG,IAAI,CAACN,cAAc;0BAAC/C,cAAA,GAAAkC,CAAA;0BAC3C,IAAI,CAACI,KAAK,CAACyE,OAAO,CAChB,sCAAsC,CACvC;0BAAC/G,cAAA,GAAAkC,CAAA;0BACF,IAAI,CAACgC,qBAAqB,CAAC2D,aAAa,CAAC;wBAC3C,CAAC;wBACD7C,KAAK,EAAGA,KAAK,IAAI;0BAAAhF,cAAA,GAAA8C,CAAA;0BAAA9C,cAAA,GAAAkC,CAAA;0BACf,IAAI,CAACI,KAAK,CAAC0C,KAAK,CAAC,CAAAhF,cAAA,GAAAoD,CAAA,WAAA4B,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAAjF,cAAA,GAAAoD,CAAA,WAAI,0CAA0C,EAAC;wBACrF;uBACD,CAAC;oBACN;mBACD,CAAC;gBACN,CAAC;gBACD4B,KAAK,EAAGA,KAAK,IAAI;kBAAAhF,cAAA,GAAA8C,CAAA;kBAAA9C,cAAA,GAAAkC,CAAA;kBACf,IAAI,CAACI,KAAK,CAAC0C,KAAK,CAAC,CAAAhF,cAAA,GAAAoD,CAAA,WAAA4B,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAAjF,cAAA,GAAAoD,CAAA,WAAI,kCAAkC,EAAC;gBAC7E;eACD,CAAC;YACN,CAAC;cAAApD,cAAA,GAAAoD,CAAA;YAAA;UACH,CAAC;YAAApD,cAAA,GAAAoD,CAAA;UAAA;QAAD;MACF,CAAC;MACD4B,KAAK,EAAGA,KAAK,IAAI;QAAAhF,cAAA,GAAA8C,CAAA;QAAA9C,cAAA,GAAAkC,CAAA;QACf,IAAI,CAACI,KAAK,CAAC0C,KAAK,CAAC,CAAAhF,cAAA,GAAAoD,CAAA,WAAA4B,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAAjF,cAAA,GAAAoD,CAAA,WAAI,kCAAkC,EAAC;MAC7E;KACD,CAAC;EACN;EAEA,IAAIiF,kBAAkBA,CAAA;IAAArI,cAAA,GAAA8C,CAAA;IAAA9C,cAAA,GAAAkC,CAAA;IACpB,IAAI,IAAI,CAACa,cAAc,CAAC8C,MAAM,KAAK,CAAC,EAAE;MAAA7F,cAAA,GAAAoD,CAAA;MAAApD,cAAA,GAAAkC,CAAA;MAAA,OAAO,IAAI;IAAA,CAAC;MAAAlC,cAAA,GAAAoD,CAAA;IAAA;IAElD,MAAMkF,aAAa,IAAAtI,cAAA,GAAAkC,CAAA,QAAG,CAAC,GAAG,IAAI,CAACa,cAAc,CAAC,CAACU,IAAI,CACjD,CAACwC,CAAC,EAAE7C,CAAC,KAAK;MAAApD,cAAA,GAAA8C,CAAA;MAAA9C,cAAA,GAAAkC,CAAA;MAAA,OAAAkB,CAAC,CAACgD,YAAY,GAAGH,CAAC,CAACG,YAAY;IAAZ,CAAY,CAC1C;IACD,MAAMF,UAAU,IAAAlG,cAAA,GAAAkC,CAAA,QAAGoG,aAAa,CAAC,CAAC,CAAC;IAACtI,cAAA,GAAAkC,CAAA;IAEpC,OAAOgE,UAAU,CAACqC,mBAAmB,EAAEC,IAAI,KAAK,UAAU;EAC5D;EAEAC,WAAWA,CAACtE,MAAqB;IAAAnE,cAAA,GAAA8C,CAAA;IAAA9C,cAAA,GAAAkC,CAAA;IAC/B,IAAI,CAACiC,MAAM,CAACwB,EAAE,EAAE;MAAA3F,cAAA,GAAAoD,CAAA;MAAApD,cAAA,GAAAkC,CAAA;MAAA;IAAA,CAAO;MAAAlC,cAAA,GAAAoD,CAAA;IAAA;IAAApD,cAAA,GAAAkC,CAAA;IAEvB,IAAI,CAACS,OAAO,CAACwC,IAAI,EAAE;IAACnF,cAAA,GAAAkC,CAAA;IACpB,IAAI,CAACK,oBAAoB,CACtBkG,WAAW,CAACtE,MAAM,CAACwB,EAAE,CAAC,CACtBH,IAAI,CAACrE,QAAQ,CAAC,MAAM;MAAAnB,cAAA,GAAA8C,CAAA;MAAA9C,cAAA,GAAAkC,CAAA;MAAA,WAAI,CAACS,OAAO,CAAC2C,IAAI,EAAE;IAAF,CAAE,CAAC,CAAC,CACzCX,SAAS,CAAC;MACTI,IAAI,EAAG2D,IAAI,IAAI;QAAA1I,cAAA,GAAA8C,CAAA;QACb,MAAM6F,GAAG,IAAA3I,cAAA,GAAAkC,CAAA,SAAG0G,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;QAC5C,MAAMK,IAAI,IAAA/I,cAAA,GAAAkC,CAAA,SAAG8G,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QAACjJ,cAAA,GAAAkC,CAAA;QACzC6G,IAAI,CAACG,IAAI,GAAGP,GAAG;QAAC3I,cAAA,GAAAkC,CAAA;QAChB6G,IAAI,CAACI,QAAQ,GAAG,mBAAmBhF,MAAM,CAACiC,YAAY,MAAM;QAACpG,cAAA,GAAAkC,CAAA;QAC7D6G,IAAI,CAACK,KAAK,EAAE;QAACpJ,cAAA,GAAAkC,CAAA;QACb0G,MAAM,CAACC,GAAG,CAACQ,eAAe,CAACV,GAAG,CAAC;QAAC3I,cAAA,GAAAkC,CAAA;QAChC,IAAI,CAACI,KAAK,CAACyE,OAAO,CAAC,6BAA6B,CAAC;MACnD,CAAC;MACD/B,KAAK,EAAGA,KAAK,IAAI;QAAAhF,cAAA,GAAA8C,CAAA;QAAA9C,cAAA,GAAAkC,CAAA;QACf,IAAI,CAACI,KAAK,CAAC0C,KAAK,CAAC,CAAAhF,cAAA,GAAAoD,CAAA,WAAA4B,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAAjF,cAAA,GAAAoD,CAAA,WAAI,2BAA2B,EAAC;MACtE;KACD,CAAC;EACN;EAEAkG,uBAAuBA,CAACnF,MAAqB;IAAAnE,cAAA,GAAA8C,CAAA;IAAA9C,cAAA,GAAAkC,CAAA;IAC3C,IAAI,CAACG,MAAM,CAACgC,IAAI,CAACrC,yCAAyC,EAAE;MAC1DsC,KAAK,EAAE,QAAQ;MACfjB,IAAI,EAAE;QAAE2E,eAAe,EAAE7D,MAAM,CAACwB;MAAE;KACnC,CAAC;EACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;cA9TCxF;MAAK;;cACLA;MAAK;;cACLA;MAAK;;cAELC,SAAS;QAAAmJ,IAAA,GAAC5I,YAAY;MAAA;;cACtBP,SAAS;QAAAmJ,IAAA,GAAC1I,OAAO;MAAA;;;;;AANPsB,0BAA0B,GAAAqH,UAAA,EAnBtCtJ,SAAS,CAAC;EACTuJ,QAAQ,EAAE,yBAAyB;EACnCC,QAAA,EAAAC,oBAAmD;EAEnDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP7I,cAAc,EACdJ,kBAAkB,EAClBE,aAAa,EACbN,kBAAkB,EAClBE,cAAc,EACdL,eAAe,EACfI,aAAa,EACbQ,gBAAgB,EAChBX,aAAa,EACbiB,QAAQ,EACRD,YAAY,CACb;;CACF,CAAC,C,EACWa,0BAA0B,CAgUtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}