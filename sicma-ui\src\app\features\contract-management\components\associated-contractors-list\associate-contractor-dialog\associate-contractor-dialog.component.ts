import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { ContractorDetailFormComponent } from '@contract-management/components/contractor-detail-form/contractor-detail-form.component';
import { InsuredRisks } from '@contract-management/models/insured_risks.model';
import { TypeWarranty } from '@contract-management/models/type_warranty.model';
import { ContractValuesService } from '@contract-management/services/contract-values.service';
import { ContractService } from '@contract-management/services/contract.service';
import { ContractorContractService } from '@contract-management/services/contractor-contract.service';
import { InsuredRisksService } from '@contract-management/services/insured_risks.service';
import { TypeWarrantyService } from '@contract-management/services/type_warranty.service';
import { AlertService } from '@shared/services/alert.service';
import { forkJoin } from 'rxjs';
import { createDateComparisonValidator } from './validators/date-comparison.validator';

@Component({
  selector: 'app-associate-contractor-dialog',
  templateUrl: './associate-contractor-dialog.component.html',
  styleUrl: './associate-contractor-dialog.component.scss',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatDatepickerModule,
    MatSelectModule,
    MatSlideToggleModule,
    ContractorDetailFormComponent,
  ],
})
export class AssociateContractorDialogComponent implements OnInit {
  @ViewChild(ContractorDetailFormComponent)
  contractorDetailForm!: ContractorDetailFormComponent;

  cessionForm: FormGroup = this.formBuilder.group({
    subscriptionDate: [null, [Validators.required]],
    startDate: [{ value: null, disabled: false }, [Validators.required]],
    warranty: [false],
    dateExpeditionWarranty: [''],
    typeWarrantyId: [''],
    insuredRisksId: [''],
  });

  minStartDate: Date | null = null;
  latestContractEndDate: Date | null = null;
  typeWarranty: TypeWarranty[] = [];
  insuredRisks: InsuredRisks[] = [];

  constructor(
    private readonly dialogRef: MatDialogRef<AssociateContractorDialogComponent>,
    private readonly formBuilder: FormBuilder,
    private readonly contractorContractService: ContractorContractService,
    private readonly contractService: ContractService,
    private readonly contractValuesService: ContractValuesService,
    private readonly typeWarrantyService: TypeWarrantyService,
    private readonly insuredRisksService: InsuredRisksService,
    private readonly alert: AlertService,
    @Inject(MAT_DIALOG_DATA) public data: { contractId: number },
  ) {}

  ngOnInit(): void {
    this.loadInitialData();
    this.setupWarrantyValidation();
  }

  private loadInitialData(): void {
    forkJoin({
      typeWarranty: this.typeWarrantyService.getAll(),
      insuredRisks: this.insuredRisksService.getAll(),
    }).subscribe({
      next: ({ typeWarranty, insuredRisks }) => {
        this.typeWarranty = typeWarranty;
        this.insuredRisks = insuredRisks;
        this.loadLatestContractorsTerminationDate();
        this.loadLatestContractEndDate();
      },
      error: (_) => {
        this.alert.error(error.error?.detail ?? 'Error al cargar los datos iniciales');
      },
    });
  }

  private setupValidators(): void {
    const dateComparisonValidator = createDateComparisonValidator({
      minStartDate: this.minStartDate,
      latestContractEndDate: this.latestContractEndDate,
    });

    const startDateControl = this.cessionForm.get('startDate');
    const subscriptionDateControl = this.cessionForm.get('subscriptionDate');

    if (startDateControl && subscriptionDateControl) {
      startDateControl.setValidators([
        Validators.required,
        dateComparisonValidator,
      ]);
      subscriptionDateControl.setValidators([
        Validators.required,
        dateComparisonValidator,
      ]);

      startDateControl.updateValueAndValidity({ emitEvent: false });
      subscriptionDateControl.updateValueAndValidity({ emitEvent: false });

      subscriptionDateControl.valueChanges.subscribe(() => {
        this.setupValidators();
      });

      startDateControl.valueChanges.subscribe(() => {
        this.setupValidators();
      });
    }
  }

  private setupWarrantyValidation(): void {
    const dateExpeditionWarranty = this.cessionForm.get(
      'dateExpeditionWarranty',
    );
    const typeWarrantyId = this.cessionForm.get('typeWarrantyId');
    const insuredRisksId = this.cessionForm.get('insuredRisksId');

    const updateValidation = (hasWarranty: boolean | null) => {
      if (!dateExpeditionWarranty || !typeWarrantyId || !insuredRisksId) return;

      if (hasWarranty) {
        dateExpeditionWarranty.setValidators([Validators.required]);
        typeWarrantyId.setValidators([Validators.required]);
        insuredRisksId.setValidators([Validators.required]);
      } else {
        dateExpeditionWarranty.setValidators(null);
        typeWarrantyId.setValidators(null);
        insuredRisksId.setValidators(null);

        dateExpeditionWarranty.patchValue(null, { emitEvent: false });
        typeWarrantyId.patchValue(null, { emitEvent: false });
        insuredRisksId.patchValue(null, { emitEvent: false });
      }

      dateExpeditionWarranty.markAsUntouched();
      typeWarrantyId.markAsUntouched();
      insuredRisksId.markAsUntouched();

      dateExpeditionWarranty.updateValueAndValidity({ emitEvent: false });
      typeWarrantyId.updateValueAndValidity({ emitEvent: false });
      insuredRisksId.updateValueAndValidity({ emitEvent: false });
    };

    const initialWarranty = this.cessionForm.get('warranty')?.value || false;
    updateValidation(initialWarranty);

    this.cessionForm.get('warranty')?.valueChanges.subscribe(updateValidation);
  }

  loadLatestContractorsTerminationDate(): void {
    this.contractorContractService
      .getLatestTerminationDate(this.data.contractId)
      .subscribe((date: string) => {
        this.minStartDate = new Date(date);
        this.minStartDate.setDate(this.minStartDate.getDate() + 2);
        if (this.latestContractEndDate !== null) {
          this.setupValidators();
        }
      });
  }

  loadLatestContractEndDate(): void {
    this.contractValuesService
      .getLatestEndDateByContractId(this.data.contractId)
      .subscribe((date: Date | null) => {
        if (date) {
          this.latestContractEndDate = new Date(date);
          if (this.minStartDate !== null) {
            this.setupValidators();
          }
        }
      });
  }

  isFormValid(): boolean {
    return this.cessionForm.valid && this.contractorDetailForm?.isValid();
  }

  onSubmit(): void {
    if (this.isFormValid()) {
      const contractor = this.contractorDetailForm.getValue();
      if (contractor) {
        const warranty = this.cessionForm.get('warranty')?.value || false;
        const dateExpeditionWarranty = this.cessionForm.get(
          'dateExpeditionWarranty',
        )?.value;

        this.contractorContractService
          .create({
            contractorId: contractor.id,
            contractId: this.data.contractId,
            subscriptionDate: this.cessionForm
              .get('subscriptionDate')
              ?.value.toISOString()
              .slice(0, 10),
            contractStartDate: this.cessionForm
              .get('startDate')
              ?.value.toISOString()
              .slice(0, 10),
            warranty: warranty,
            dateExpeditionWarranty:
              warranty && dateExpeditionWarranty
                ? dateExpeditionWarranty.toISOString().slice(0, 10)
                : undefined,
            typeWarrantyId: warranty
              ? this.cessionForm.get('typeWarrantyId')?.value
              : undefined,
            insuredRisksId: warranty
              ? this.cessionForm.get('insuredRisksId')?.value
              : undefined,
          })
          .subscribe({
            next: () => {
              this.contractService
                .update(this.data.contractId, {
                  earlyTermination: false,
                  cession: true,
                })
                .subscribe({
                  next: () => {
                    this.alert.success(
                      'Contratista asociado exitosamente y contrato actualizado',
                    );
                    this.dialogRef.close({
                      success: true,
                      contractorAdded: true,
                    });
                  },
                  error: (_) => {
                    this.alert.warning(
                      'Contratista asociado, pero hubo un error al actualizar el contrato',
                    );
                    this.dialogRef.close({
                      success: true,
                      contractorAdded: true,
                    });
                  },
                });
            },
            error: (_) => {
              this.alert.error(error.error?.detail ?? 'Error al asociar el contratista.');
            },
          });
      }
    }
  }
}
