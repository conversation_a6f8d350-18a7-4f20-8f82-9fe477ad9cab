import { Component, Inject } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { ContractValues } from '@contract-management/models/contract-values.model';
import { Contract } from '@contract-management/models/contract.model';
import { ContractValuesService } from '@contract-management/services/contract-values.service';
import { AlertService } from '@shared/services/alert.service';
import { ContractValuesFormComponent } from '@contract-management/components/contract-values-form/contract-values-form.component';

@Component({
  selector: 'app-contract-values-dialog',
  templateUrl: './contract-values-dialog.component.html',
  styleUrl: './contract-values-dialog.component.scss',
  standalone: true,
  imports: [
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    ContractValuesFormComponent,
  ],
})
export class ContractValuesDialogComponent {
  isInitialValue = false;

  constructor(
    private readonly dialogRef: MatDialogRef<ContractValuesDialogComponent>,
    private readonly contractValuesService: ContractValuesService,
    private readonly alert: AlertService,
    @Inject(MAT_DIALOG_DATA)
    public data: {
      contractValue?: ContractValues;
      contract: Contract;
      isInitialValue?: boolean;
    },
  ) {
    this.isInitialValue = this.isInitialContractValue();
  }

  private isInitialContractValue(): boolean {
    if (typeof this.data.isInitialValue === 'boolean') {
      return this.data.isInitialValue;
    }

    if (
      !this.data.contract?.contractValues ||
      this.data.contract.contractValues.length === 0
    ) {
      return true;
    }

    if (this.data.contractValue?.id) {
      const lowestIdValue = this.data.contract.contractValues.reduce(
        (min, current) =>
          current.id !== undefined &&
          min?.id !== undefined &&
          current.id < min.id
            ? current
            : min,
        this.data.contract.contractValues[0],
      );

      return this.data.contractValue.id === lowestIdValue?.id;
    }

    return false;
  }

  async onSubmit(
    contractValues: Omit<ContractValues, 'id'> | null,
  ): Promise<void> {
    if (!contractValues) return;

    if (
      !this.data.contractValue &&
      contractValues.futureValidityValue &&
      contractValues.futureValidityValue > 0
    ) {
      const confirmed = await this.alert.confirm(
        '¿Está seguro de agregar este valor de vigencia futura?',
        `El valor de vigencia futura es de ${new Intl.NumberFormat('es-CO', { style: 'currency', currency: 'COP' }).format(contractValues.futureValidityValue)}`,
      );

      if (!confirmed) {
        return;
      }
    }

    const operation = this.data.contractValue?.id
      ? this.contractValuesService.update(
          this.data.contractValue.id,
          contractValues,
        )
      : this.contractValuesService.create(contractValues);

    operation.subscribe({
      next: (contractValues) => {
        this.alert.success(
          `Valores del Contrato ${
            this.data.contractValue ? 'actualizados' : 'guardados'
          } correctamente`,
        );
        this.dialogRef.close(contractValues);
      },
      error: (error) => {
        this.alert.error(
          error.error?.detail ??
            `Error al ${
              this.data.contractValue ? 'actualizar' : 'guardar'
            } los valores del contrato`,
        );
      },
    });
  }
}
