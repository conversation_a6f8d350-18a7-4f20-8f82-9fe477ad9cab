import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { Contract } from '@contract-management/models/contract.model';
import { Suspension } from '@contract-management/models/suspension.model';
import { of } from 'rxjs';
import { SuspensionDialogComponent } from './suspension-dialog/suspension-dialog.component';
import { SuspensionsListComponent } from './suspensions-list.component';

describe('SuspensionsListComponent', () => {
  let component: SuspensionsListComponent;
  let fixture: ComponentFixture<SuspensionsListComponent>;
  let dialog: jasmine.SpyObj<MatDialog>;

  const mockSuspension: Suspension = {
    id: 1,
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-01-31'),
    reason: 'Test Suspension',
    contractId: 1,
    suspensionDays: 30,
  };

  const mockContract: Contract = {
    id: 1,
    contractNumber: 123,
    contractYearId: 2024,
    object: 'Test Contract',
    rup: false,
    sigepLink: '',
    secopLink: '',
    addition: false,
    cession: false,
    settled: false,
    selectionModalityId: 1,
    trackingTypeId: 1,
    contractTypeId: 1,
    statusId: 1,
    dependencyId: 1,
    groupId: 1,
    monthlyPayment: 1000,
    municipalityId: 1,
    departmentId: 1,
    secopCode: 123,
    causesSelectionId: 1,
    managementSupportId: 1,
    contractClassId: 1,
    suspensions: [mockSuspension],
  };

  beforeEach(() => {
    const dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);
    dialogSpy.open.and.returnValue({
      afterClosed: () => of(undefined),
    } as MatDialogRef<unknown>);

    TestBed.configureTestingModule({
      imports: [
        SuspensionsListComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
      ],
      providers: [{ provide: MatDialog, useValue: dialogSpy }],
    });

    fixture = TestBed.createComponent(SuspensionsListComponent);
    component = fixture.componentInstance;
    dialog = TestBed.inject(MatDialog) as jasmine.SpyObj<MatDialog>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with contract suspensions', () => {
    component.contract = mockContract;
    component.ngOnInit();
    expect(component.dataSource.data).toEqual(mockContract.suspensions || []);
  });

  it('should open suspension form for new suspension', () => {
    component.contract = mockContract;
    component.ngOnInit();

    const newSuspension: Suspension = {
      id: 2,
      startDate: new Date('2024-02-01'),
      endDate: new Date('2024-02-28'),
      reason: 'New Suspension',
      contractId: 1,
      suspensionDays: 27,
    };

    const mockDialogRef = {
      afterClosed: () => of(newSuspension),
    } as MatDialogRef<unknown>;

    dialog.open.and.returnValue(mockDialogRef);

    component.openSuspensionForm();

    expect(dialog.open).toHaveBeenCalledWith(SuspensionDialogComponent, {
      width: '600px',
      data: {
        suspension: undefined,
        contractId: mockContract.id,
        lastSuspensionEndDate: new Date('2024-01-31'),
      },
    });

    expect(component.dataSource.data.length).toBe(2);
  });

  it('should open suspension form for existing suspension', () => {
    component.contract = mockContract;
    component.ngOnInit();

    const updatedSuspension: Suspension = {
      ...mockSuspension,
      reason: 'Updated Suspension',
    };

    const mockDialogRef = {
      afterClosed: () => of(updatedSuspension),
    } as MatDialogRef<unknown>;

    dialog.open.and.returnValue(mockDialogRef);

    component.openSuspensionForm(mockSuspension);

    expect(dialog.open).toHaveBeenCalledWith(SuspensionDialogComponent, {
      width: '600px',
      data: {
        suspension: mockSuspension,
        contractId: mockContract.id,
        lastSuspensionEndDate: null,
      },
    });

    expect(component.dataSource.data[0].reason).toBe('Updated Suspension');
  });

  it('should not update data when dialog is closed without result', () => {
    component.contract = mockContract;
    component.ngOnInit();
    const initialData = [...component.dataSource.data];

    const mockDialogRef = {
      afterClosed: () => of(undefined),
    } as MatDialogRef<unknown>;

    dialog.open.and.returnValue(mockDialogRef);

    component.openSuspensionForm();

    expect(component.dataSource.data).toEqual(initialData);
  });

  it('should get last suspension end date', () => {
    const secondSuspension: Suspension = {
      id: 2,
      startDate: new Date('2024-02-01'),
      endDate: new Date('2024-02-28'),
      reason: 'Second Suspension',
      contractId: 1,
      suspensionDays: 27,
    };

    component.contract = {
      ...mockContract,
      suspensions: [mockSuspension, secondSuspension],
    };
    component.ngOnInit();

    const lastEndDate = component['getLastSuspensionEndDate']();
    expect(lastEndDate).toEqual(new Date('2024-02-28'));
  });

  it('should return null when no suspensions exist', () => {
    component.contract = {
      ...mockContract,
      suspensions: [],
    };
    component.ngOnInit();

    const lastEndDate = component['getLastSuspensionEndDate']();
    expect(lastEndDate).toBeNull();
  });
});