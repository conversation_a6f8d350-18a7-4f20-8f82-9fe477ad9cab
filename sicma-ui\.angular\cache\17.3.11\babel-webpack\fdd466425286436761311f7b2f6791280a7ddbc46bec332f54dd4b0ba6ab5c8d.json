{"ast": null, "code": "function cov_2lymkq89t6() {\n  var path = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-dashboard\\\\components\\\\monthly-reports-tab\\\\monthly-report-dialog\\\\monthly-report-initial-documentation\\\\social-security-info\\\\social-security-info.component.ts\";\n  var hash = \"57dff35193b7406c2d861e8078225d03ecfb0b98\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-dashboard\\\\components\\\\monthly-reports-tab\\\\monthly-report-dialog\\\\monthly-report-initial-documentation\\\\social-security-info\\\\social-security-info.component.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 19,\n          column: 34\n        },\n        end: {\n          line: 173,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 21,\n          column: 8\n        },\n        end: {\n          line: 21,\n          column: 21\n        }\n      },\n      \"2\": {\n        start: {\n          line: 22,\n          column: 8\n        },\n        end: {\n          line: 22,\n          column: 37\n        }\n      },\n      \"3\": {\n        start: {\n          line: 23,\n          column: 8\n        },\n        end: {\n          line: 23,\n          column: 37\n        }\n      },\n      \"4\": {\n        start: {\n          line: 24,\n          column: 8\n        },\n        end: {\n          line: 24,\n          column: 53\n        }\n      },\n      \"5\": {\n        start: {\n          line: 25,\n          column: 8\n        },\n        end: {\n          line: 25,\n          column: 27\n        }\n      },\n      \"6\": {\n        start: {\n          line: 26,\n          column: 8\n        },\n        end: {\n          line: 26,\n          column: 34\n        }\n      },\n      \"7\": {\n        start: {\n          line: 27,\n          column: 8\n        },\n        end: {\n          line: 27,\n          column: 45\n        }\n      },\n      \"8\": {\n        start: {\n          line: 28,\n          column: 8\n        },\n        end: {\n          line: 35,\n          column: 11\n        }\n      },\n      \"9\": {\n        start: {\n          line: 36,\n          column: 8\n        },\n        end: {\n          line: 36,\n          column: 26\n        }\n      },\n      \"10\": {\n        start: {\n          line: 37,\n          column: 8\n        },\n        end: {\n          line: 37,\n          column: 26\n        }\n      },\n      \"11\": {\n        start: {\n          line: 38,\n          column: 8\n        },\n        end: {\n          line: 38,\n          column: 34\n        }\n      },\n      \"12\": {\n        start: {\n          line: 39,\n          column: 8\n        },\n        end: {\n          line: 39,\n          column: 39\n        }\n      },\n      \"13\": {\n        start: {\n          line: 40,\n          column: 8\n        },\n        end: {\n          line: 40,\n          column: 39\n        }\n      },\n      \"14\": {\n        start: {\n          line: 41,\n          column: 8\n        },\n        end: {\n          line: 41,\n          column: 43\n        }\n      },\n      \"15\": {\n        start: {\n          line: 42,\n          column: 8\n        },\n        end: {\n          line: 42,\n          column: 43\n        }\n      },\n      \"16\": {\n        start: {\n          line: 43,\n          column: 8\n        },\n        end: {\n          line: 43,\n          column: 43\n        }\n      },\n      \"17\": {\n        start: {\n          line: 44,\n          column: 8\n        },\n        end: {\n          line: 44,\n          column: 47\n        }\n      },\n      \"18\": {\n        start: {\n          line: 47,\n          column: 8\n        },\n        end: {\n          line: 60,\n          column: 11\n        }\n      },\n      \"19\": {\n        start: {\n          line: 53,\n          column: 16\n        },\n        end: {\n          line: 53,\n          column: 35\n        }\n      },\n      \"20\": {\n        start: {\n          line: 54,\n          column: 16\n        },\n        end: {\n          line: 54,\n          column: 35\n        }\n      },\n      \"21\": {\n        start: {\n          line: 55,\n          column: 16\n        },\n        end: {\n          line: 55,\n          column: 51\n        }\n      },\n      \"22\": {\n        start: {\n          line: 58,\n          column: 16\n        },\n        end: {\n          line: 58,\n          column: 100\n        }\n      },\n      \"23\": {\n        start: {\n          line: 61,\n          column: 8\n        },\n        end: {\n          line: 63,\n          column: 11\n        }\n      },\n      \"24\": {\n        start: {\n          line: 62,\n          column: 12\n        },\n        end: {\n          line: 62,\n          column: 35\n        }\n      },\n      \"25\": {\n        start: {\n          line: 66,\n          column: 8\n        },\n        end: {\n          line: 73,\n          column: 9\n        }\n      },\n      \"26\": {\n        start: {\n          line: 67,\n          column: 25\n        },\n        end: {\n          line: 67,\n          column: 60\n        }\n      },\n      \"27\": {\n        start: {\n          line: 68,\n          column: 12\n        },\n        end: {\n          line: 72,\n          column: 15\n        }\n      },\n      \"28\": {\n        start: {\n          line: 76,\n          column: 27\n        },\n        end: {\n          line: 76,\n          column: 69\n        }\n      },\n      \"29\": {\n        start: {\n          line: 77,\n          column: 32\n        },\n        end: {\n          line: 83,\n          column: 64\n        }\n      },\n      \"30\": {\n        start: {\n          line: 84,\n          column: 8\n        },\n        end: {\n          line: 84,\n          column: 54\n        }\n      },\n      \"31\": {\n        start: {\n          line: 87,\n          column: 33\n        },\n        end: {\n          line: 89,\n          column: 48\n        }\n      },\n      \"32\": {\n        start: {\n          line: 90,\n          column: 29\n        },\n        end: {\n          line: 91,\n          column: 52\n        }\n      },\n      \"33\": {\n        start: {\n          line: 92,\n          column: 29\n        },\n        end: {\n          line: 93,\n          column: 52\n        }\n      },\n      \"34\": {\n        start: {\n          line: 94,\n          column: 33\n        },\n        end: {\n          line: 95,\n          column: 56\n        }\n      },\n      \"35\": {\n        start: {\n          line: 96,\n          column: 24\n        },\n        end: {\n          line: 96,\n          column: 92\n        }\n      },\n      \"36\": {\n        start: {\n          line: 97,\n          column: 8\n        },\n        end: {\n          line: 97,\n          column: 23\n        }\n      },\n      \"37\": {\n        start: {\n          line: 100,\n          column: 21\n        },\n        end: {\n          line: 100,\n          column: 44\n        }\n      },\n      \"38\": {\n        start: {\n          line: 101,\n          column: 8\n        },\n        end: {\n          line: 128,\n          column: 9\n        }\n      },\n      \"39\": {\n        start: {\n          line: 102,\n          column: 12\n        },\n        end: {\n          line: 126,\n          column: 13\n        }\n      },\n      \"40\": {\n        start: {\n          line: 103,\n          column: 16\n        },\n        end: {\n          line: 122,\n          column: 17\n        }\n      },\n      \"41\": {\n        start: {\n          line: 104,\n          column: 20\n        },\n        end: {\n          line: 104,\n          column: 66\n        }\n      },\n      \"42\": {\n        start: {\n          line: 105,\n          column: 20\n        },\n        end: {\n          line: 118,\n          column: 21\n        }\n      },\n      \"43\": {\n        start: {\n          line: 107,\n          column: 28\n        },\n        end: {\n          line: 107,\n          column: 59\n        }\n      },\n      \"44\": {\n        start: {\n          line: 108,\n          column: 28\n        },\n        end: {\n          line: 108,\n          column: 68\n        }\n      },\n      \"45\": {\n        start: {\n          line: 109,\n          column: 28\n        },\n        end: {\n          line: 109,\n          column: 34\n        }\n      },\n      \"46\": {\n        start: {\n          line: 111,\n          column: 28\n        },\n        end: {\n          line: 111,\n          column: 59\n        }\n      },\n      \"47\": {\n        start: {\n          line: 112,\n          column: 28\n        },\n        end: {\n          line: 112,\n          column: 68\n        }\n      },\n      \"48\": {\n        start: {\n          line: 113,\n          column: 28\n        },\n        end: {\n          line: 113,\n          column: 34\n        }\n      },\n      \"49\": {\n        start: {\n          line: 115,\n          column: 28\n        },\n        end: {\n          line: 115,\n          column: 63\n        }\n      },\n      \"50\": {\n        start: {\n          line: 116,\n          column: 28\n        },\n        end: {\n          line: 116,\n          column: 72\n        }\n      },\n      \"51\": {\n        start: {\n          line: 117,\n          column: 28\n        },\n        end: {\n          line: 117,\n          column: 34\n        }\n      },\n      \"52\": {\n        start: {\n          line: 121,\n          column: 20\n        },\n        end: {\n          line: 121,\n          column: 71\n        }\n      },\n      \"53\": {\n        start: {\n          line: 125,\n          column: 16\n        },\n        end: {\n          line: 125,\n          column: 66\n        }\n      },\n      \"54\": {\n        start: {\n          line: 127,\n          column: 12\n        },\n        end: {\n          line: 127,\n          column: 65\n        }\n      },\n      \"55\": {\n        start: {\n          line: 131,\n          column: 8\n        },\n        end: {\n          line: 147,\n          column: 9\n        }\n      },\n      \"56\": {\n        start: {\n          line: 133,\n          column: 16\n        },\n        end: {\n          line: 135,\n          column: 17\n        }\n      },\n      \"57\": {\n        start: {\n          line: 134,\n          column: 20\n        },\n        end: {\n          line: 134,\n          column: 82\n        }\n      },\n      \"58\": {\n        start: {\n          line: 136,\n          column: 16\n        },\n        end: {\n          line: 136,\n          column: 22\n        }\n      },\n      \"59\": {\n        start: {\n          line: 138,\n          column: 16\n        },\n        end: {\n          line: 140,\n          column: 17\n        }\n      },\n      \"60\": {\n        start: {\n          line: 139,\n          column: 20\n        },\n        end: {\n          line: 139,\n          column: 82\n        }\n      },\n      \"61\": {\n        start: {\n          line: 141,\n          column: 16\n        },\n        end: {\n          line: 141,\n          column: 22\n        }\n      },\n      \"62\": {\n        start: {\n          line: 143,\n          column: 16\n        },\n        end: {\n          line: 145,\n          column: 17\n        }\n      },\n      \"63\": {\n        start: {\n          line: 144,\n          column: 20\n        },\n        end: {\n          line: 144,\n          column: 86\n        }\n      },\n      \"64\": {\n        start: {\n          line: 146,\n          column: 16\n        },\n        end: {\n          line: 146,\n          column: 22\n        }\n      },\n      \"65\": {\n        start: {\n          line: 150,\n          column: 8\n        },\n        end: {\n          line: 150,\n          column: 47\n        }\n      },\n      \"66\": {\n        start: {\n          line: 153,\n          column: 8\n        },\n        end: {\n          line: 153,\n          column: 69\n        }\n      },\n      \"67\": {\n        start: {\n          line: 153,\n          column: 42\n        },\n        end: {\n          line: 153,\n          column: 55\n        }\n      },\n      \"68\": {\n        start: {\n          line: 156,\n          column: 8\n        },\n        end: {\n          line: 156,\n          column: 69\n        }\n      },\n      \"69\": {\n        start: {\n          line: 156,\n          column: 42\n        },\n        end: {\n          line: 156,\n          column: 55\n        }\n      },\n      \"70\": {\n        start: {\n          line: 159,\n          column: 8\n        },\n        end: {\n          line: 159,\n          column: 79\n        }\n      },\n      \"71\": {\n        start: {\n          line: 159,\n          column: 51\n        },\n        end: {\n          line: 159,\n          column: 65\n        }\n      },\n      \"72\": {\n        start: {\n          line: 161,\n          column: 13\n        },\n        end: {\n          line: 167,\n          column: 6\n        }\n      },\n      \"73\": {\n        start: {\n          line: 161,\n          column: 41\n        },\n        end: {\n          line: 167,\n          column: 5\n        }\n      },\n      \"74\": {\n        start: {\n          line: 168,\n          column: 13\n        },\n        end: {\n          line: 172,\n          column: 6\n        }\n      },\n      \"75\": {\n        start: {\n          line: 174,\n          column: 0\n        },\n        end: {\n          line: 194,\n          column: 32\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 20,\n            column: 4\n          },\n          end: {\n            line: 20,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 20,\n            column: 71\n          },\n          end: {\n            line: 45,\n            column: 5\n          }\n        },\n        line: 20\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 46,\n            column: 4\n          },\n          end: {\n            line: 46,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 46,\n            column: 15\n          },\n          end: {\n            line: 64,\n            column: 5\n          }\n        },\n        line: 46\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 52,\n            column: 18\n          },\n          end: {\n            line: 52,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 52,\n            column: 49\n          },\n          end: {\n            line: 56,\n            column: 13\n          }\n        },\n        line: 52\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 57,\n            column: 19\n          },\n          end: {\n            line: 57,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 57,\n            column: 30\n          },\n          end: {\n            line: 59,\n            column: 13\n          }\n        },\n        line: 57\n      },\n      \"4\": {\n        name: \"(anonymous_4)\",\n        decl: {\n          start: {\n            line: 61,\n            column: 41\n          },\n          end: {\n            line: 61,\n            column: 42\n          }\n        },\n        loc: {\n          start: {\n            line: 61,\n            column: 47\n          },\n          end: {\n            line: 63,\n            column: 9\n          }\n        },\n        line: 61\n      },\n      \"5\": {\n        name: \"(anonymous_5)\",\n        decl: {\n          start: {\n            line: 65,\n            column: 4\n          },\n          end: {\n            line: 65,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 65,\n            column: 25\n          },\n          end: {\n            line: 74,\n            column: 5\n          }\n        },\n        line: 65\n      },\n      \"6\": {\n        name: \"(anonymous_6)\",\n        decl: {\n          start: {\n            line: 75,\n            column: 4\n          },\n          end: {\n            line: 75,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 75,\n            column: 29\n          },\n          end: {\n            line: 85,\n            column: 5\n          }\n        },\n        line: 75\n      },\n      \"7\": {\n        name: \"(anonymous_7)\",\n        decl: {\n          start: {\n            line: 86,\n            column: 4\n          },\n          end: {\n            line: 86,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 86,\n            column: 18\n          },\n          end: {\n            line: 98,\n            column: 5\n          }\n        },\n        line: 86\n      },\n      \"8\": {\n        name: \"(anonymous_8)\",\n        decl: {\n          start: {\n            line: 99,\n            column: 4\n          },\n          end: {\n            line: 99,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 99,\n            column: 39\n          },\n          end: {\n            line: 129,\n            column: 5\n          }\n        },\n        line: 99\n      },\n      \"9\": {\n        name: \"(anonymous_9)\",\n        decl: {\n          start: {\n            line: 130,\n            column: 4\n          },\n          end: {\n            line: 130,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 130,\n            column: 27\n          },\n          end: {\n            line: 148,\n            column: 5\n          }\n        },\n        line: 130\n      },\n      \"10\": {\n        name: \"(anonymous_10)\",\n        decl: {\n          start: {\n            line: 149,\n            column: 4\n          },\n          end: {\n            line: 149,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 149,\n            column: 20\n          },\n          end: {\n            line: 151,\n            column: 5\n          }\n        },\n        line: 149\n      },\n      \"11\": {\n        name: \"(anonymous_11)\",\n        decl: {\n          start: {\n            line: 152,\n            column: 4\n          },\n          end: {\n            line: 152,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 152,\n            column: 19\n          },\n          end: {\n            line: 154,\n            column: 5\n          }\n        },\n        line: 152\n      },\n      \"12\": {\n        name: \"(anonymous_12)\",\n        decl: {\n          start: {\n            line: 153,\n            column: 33\n          },\n          end: {\n            line: 153,\n            column: 34\n          }\n        },\n        loc: {\n          start: {\n            line: 153,\n            column: 42\n          },\n          end: {\n            line: 153,\n            column: 55\n          }\n        },\n        line: 153\n      },\n      \"13\": {\n        name: \"(anonymous_13)\",\n        decl: {\n          start: {\n            line: 155,\n            column: 4\n          },\n          end: {\n            line: 155,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 155,\n            column: 19\n          },\n          end: {\n            line: 157,\n            column: 5\n          }\n        },\n        line: 155\n      },\n      \"14\": {\n        name: \"(anonymous_14)\",\n        decl: {\n          start: {\n            line: 156,\n            column: 33\n          },\n          end: {\n            line: 156,\n            column: 34\n          }\n        },\n        loc: {\n          start: {\n            line: 156,\n            column: 42\n          },\n          end: {\n            line: 156,\n            column: 55\n          }\n        },\n        line: 156\n      },\n      \"15\": {\n        name: \"(anonymous_15)\",\n        decl: {\n          start: {\n            line: 158,\n            column: 4\n          },\n          end: {\n            line: 158,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 158,\n            column: 27\n          },\n          end: {\n            line: 160,\n            column: 5\n          }\n        },\n        line: 158\n      },\n      \"16\": {\n        name: \"(anonymous_16)\",\n        decl: {\n          start: {\n            line: 159,\n            column: 41\n          },\n          end: {\n            line: 159,\n            column: 42\n          }\n        },\n        loc: {\n          start: {\n            line: 159,\n            column: 51\n          },\n          end: {\n            line: 159,\n            column: 65\n          }\n        },\n        line: 159\n      },\n      \"17\": {\n        name: \"(anonymous_17)\",\n        decl: {\n          start: {\n            line: 161,\n            column: 35\n          },\n          end: {\n            line: 161,\n            column: 36\n          }\n        },\n        loc: {\n          start: {\n            line: 161,\n            column: 41\n          },\n          end: {\n            line: 167,\n            column: 5\n          }\n        },\n        line: 161\n      }\n    },\n    branchMap: {\n      \"0\": {\n        loc: {\n          start: {\n            line: 58,\n            column: 33\n          },\n          end: {\n            line: 58,\n            column: 98\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 58,\n            column: 33\n          },\n          end: {\n            line: 58,\n            column: 52\n          }\n        }, {\n          start: {\n            line: 58,\n            column: 56\n          },\n          end: {\n            line: 58,\n            column: 98\n          }\n        }],\n        line: 58\n      },\n      \"1\": {\n        loc: {\n          start: {\n            line: 66,\n            column: 8\n          },\n          end: {\n            line: 73,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 66,\n            column: 8\n          },\n          end: {\n            line: 73,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 66\n      },\n      \"2\": {\n        loc: {\n          start: {\n            line: 77,\n            column: 32\n          },\n          end: {\n            line: 83,\n            column: 64\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 77,\n            column: 32\n          },\n          end: {\n            line: 77,\n            column: 48\n          }\n        }, {\n          start: {\n            line: 78,\n            column: 14\n          },\n          end: {\n            line: 78,\n            column: 46\n          }\n        }, {\n          start: {\n            line: 79,\n            column: 16\n          },\n          end: {\n            line: 79,\n            column: 54\n          }\n        }, {\n          start: {\n            line: 80,\n            column: 17\n          },\n          end: {\n            line: 80,\n            column: 49\n          }\n        }, {\n          start: {\n            line: 81,\n            column: 20\n          },\n          end: {\n            line: 81,\n            column: 58\n          }\n        }, {\n          start: {\n            line: 82,\n            column: 17\n          },\n          end: {\n            line: 82,\n            column: 57\n          }\n        }, {\n          start: {\n            line: 83,\n            column: 20\n          },\n          end: {\n            line: 83,\n            column: 62\n          }\n        }],\n        line: 77\n      },\n      \"3\": {\n        loc: {\n          start: {\n            line: 84,\n            column: 15\n          },\n          end: {\n            line: 84,\n            column: 53\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 84,\n            column: 15\n          },\n          end: {\n            line: 84,\n            column: 25\n          }\n        }, {\n          start: {\n            line: 84,\n            column: 29\n          },\n          end: {\n            line: 84,\n            column: 53\n          }\n        }],\n        line: 84\n      },\n      \"4\": {\n        loc: {\n          start: {\n            line: 87,\n            column: 41\n          },\n          end: {\n            line: 89,\n            column: 47\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 87,\n            column: 41\n          },\n          end: {\n            line: 87,\n            column: 68\n          }\n        }, {\n          start: {\n            line: 88,\n            column: 12\n          },\n          end: {\n            line: 88,\n            column: 39\n          }\n        }, {\n          start: {\n            line: 89,\n            column: 12\n          },\n          end: {\n            line: 89,\n            column: 47\n          }\n        }],\n        line: 87\n      },\n      \"5\": {\n        loc: {\n          start: {\n            line: 90,\n            column: 37\n          },\n          end: {\n            line: 91,\n            column: 51\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 90,\n            column: 37\n          },\n          end: {\n            line: 90,\n            column: 75\n          }\n        }, {\n          start: {\n            line: 91,\n            column: 12\n          },\n          end: {\n            line: 91,\n            column: 51\n          }\n        }],\n        line: 90\n      },\n      \"6\": {\n        loc: {\n          start: {\n            line: 92,\n            column: 37\n          },\n          end: {\n            line: 93,\n            column: 51\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 92,\n            column: 37\n          },\n          end: {\n            line: 92,\n            column: 75\n          }\n        }, {\n          start: {\n            line: 93,\n            column: 12\n          },\n          end: {\n            line: 93,\n            column: 51\n          }\n        }],\n        line: 92\n      },\n      \"7\": {\n        loc: {\n          start: {\n            line: 94,\n            column: 41\n          },\n          end: {\n            line: 95,\n            column: 55\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 94,\n            column: 41\n          },\n          end: {\n            line: 94,\n            column: 87\n          }\n        }, {\n          start: {\n            line: 95,\n            column: 12\n          },\n          end: {\n            line: 95,\n            column: 55\n          }\n        }],\n        line: 94\n      },\n      \"8\": {\n        loc: {\n          start: {\n            line: 96,\n            column: 24\n          },\n          end: {\n            line: 96,\n            column: 92\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 96,\n            column: 24\n          },\n          end: {\n            line: 96,\n            column: 40\n          }\n        }, {\n          start: {\n            line: 96,\n            column: 44\n          },\n          end: {\n            line: 96,\n            column: 56\n          }\n        }, {\n          start: {\n            line: 96,\n            column: 60\n          },\n          end: {\n            line: 96,\n            column: 72\n          }\n        }, {\n          start: {\n            line: 96,\n            column: 76\n          },\n          end: {\n            line: 96,\n            column: 92\n          }\n        }],\n        line: 96\n      },\n      \"9\": {\n        loc: {\n          start: {\n            line: 101,\n            column: 8\n          },\n          end: {\n            line: 128,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 101,\n            column: 8\n          },\n          end: {\n            line: 128,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 101\n      },\n      \"10\": {\n        loc: {\n          start: {\n            line: 102,\n            column: 12\n          },\n          end: {\n            line: 126,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 102,\n            column: 12\n          },\n          end: {\n            line: 126,\n            column: 13\n          }\n        }, {\n          start: {\n            line: 124,\n            column: 17\n          },\n          end: {\n            line: 126,\n            column: 13\n          }\n        }],\n        line: 102\n      },\n      \"11\": {\n        loc: {\n          start: {\n            line: 103,\n            column: 16\n          },\n          end: {\n            line: 122,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 103,\n            column: 16\n          },\n          end: {\n            line: 122,\n            column: 17\n          }\n        }, {\n          start: {\n            line: 120,\n            column: 21\n          },\n          end: {\n            line: 122,\n            column: 17\n          }\n        }],\n        line: 103\n      },\n      \"12\": {\n        loc: {\n          start: {\n            line: 105,\n            column: 20\n          },\n          end: {\n            line: 118,\n            column: 21\n          }\n        },\n        type: \"switch\",\n        locations: [{\n          start: {\n            line: 106,\n            column: 24\n          },\n          end: {\n            line: 109,\n            column: 34\n          }\n        }, {\n          start: {\n            line: 110,\n            column: 24\n          },\n          end: {\n            line: 113,\n            column: 34\n          }\n        }, {\n          start: {\n            line: 114,\n            column: 24\n          },\n          end: {\n            line: 117,\n            column: 34\n          }\n        }],\n        line: 105\n      },\n      \"13\": {\n        loc: {\n          start: {\n            line: 131,\n            column: 8\n          },\n          end: {\n            line: 147,\n            column: 9\n          }\n        },\n        type: \"switch\",\n        locations: [{\n          start: {\n            line: 132,\n            column: 12\n          },\n          end: {\n            line: 136,\n            column: 22\n          }\n        }, {\n          start: {\n            line: 137,\n            column: 12\n          },\n          end: {\n            line: 141,\n            column: 22\n          }\n        }, {\n          start: {\n            line: 142,\n            column: 12\n          },\n          end: {\n            line: 146,\n            column: 22\n          }\n        }],\n        line: 131\n      },\n      \"14\": {\n        loc: {\n          start: {\n            line: 133,\n            column: 16\n          },\n          end: {\n            line: 135,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 133,\n            column: 16\n          },\n          end: {\n            line: 135,\n            column: 17\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 133\n      },\n      \"15\": {\n        loc: {\n          start: {\n            line: 138,\n            column: 16\n          },\n          end: {\n            line: 140,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 138,\n            column: 16\n          },\n          end: {\n            line: 140,\n            column: 17\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 138\n      },\n      \"16\": {\n        loc: {\n          start: {\n            line: 143,\n            column: 16\n          },\n          end: {\n            line: 145,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 143,\n            column: 16\n          },\n          end: {\n            line: 145,\n            column: 17\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 143\n      },\n      \"17\": {\n        loc: {\n          start: {\n            line: 153,\n            column: 15\n          },\n          end: {\n            line: 153,\n            column: 68\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 153,\n            column: 15\n          },\n          end: {\n            line: 153,\n            column: 62\n          }\n        }, {\n          start: {\n            line: 153,\n            column: 66\n          },\n          end: {\n            line: 153,\n            column: 68\n          }\n        }],\n        line: 153\n      },\n      \"18\": {\n        loc: {\n          start: {\n            line: 156,\n            column: 15\n          },\n          end: {\n            line: 156,\n            column: 68\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 156,\n            column: 15\n          },\n          end: {\n            line: 156,\n            column: 62\n          }\n        }, {\n          start: {\n            line: 156,\n            column: 66\n          },\n          end: {\n            line: 156,\n            column: 68\n          }\n        }],\n        line: 156\n      },\n      \"19\": {\n        loc: {\n          start: {\n            line: 159,\n            column: 15\n          },\n          end: {\n            line: 159,\n            column: 78\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 159,\n            column: 15\n          },\n          end: {\n            line: 159,\n            column: 72\n          }\n        }, {\n          start: {\n            line: 159,\n            column: 76\n          },\n          end: {\n            line: 159,\n            column: 78\n          }\n        }],\n        line: 159\n      }\n    },\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0,\n      \"36\": 0,\n      \"37\": 0,\n      \"38\": 0,\n      \"39\": 0,\n      \"40\": 0,\n      \"41\": 0,\n      \"42\": 0,\n      \"43\": 0,\n      \"44\": 0,\n      \"45\": 0,\n      \"46\": 0,\n      \"47\": 0,\n      \"48\": 0,\n      \"49\": 0,\n      \"50\": 0,\n      \"51\": 0,\n      \"52\": 0,\n      \"53\": 0,\n      \"54\": 0,\n      \"55\": 0,\n      \"56\": 0,\n      \"57\": 0,\n      \"58\": 0,\n      \"59\": 0,\n      \"60\": 0,\n      \"61\": 0,\n      \"62\": 0,\n      \"63\": 0,\n      \"64\": 0,\n      \"65\": 0,\n      \"66\": 0,\n      \"67\": 0,\n      \"68\": 0,\n      \"69\": 0,\n      \"70\": 0,\n      \"71\": 0,\n      \"72\": 0,\n      \"73\": 0,\n      \"74\": 0,\n      \"75\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0\n    },\n    b: {\n      \"0\": [0, 0],\n      \"1\": [0, 0],\n      \"2\": [0, 0, 0, 0, 0, 0, 0],\n      \"3\": [0, 0],\n      \"4\": [0, 0, 0],\n      \"5\": [0, 0],\n      \"6\": [0, 0],\n      \"7\": [0, 0],\n      \"8\": [0, 0, 0, 0],\n      \"9\": [0, 0],\n      \"10\": [0, 0],\n      \"11\": [0, 0],\n      \"12\": [0, 0, 0],\n      \"13\": [0, 0, 0],\n      \"14\": [0, 0],\n      \"15\": [0, 0],\n      \"16\": [0, 0],\n      \"17\": [0, 0],\n      \"18\": [0, 0],\n      \"19\": [0, 0]\n    },\n    inputSourceMap: {\n      version: 3,\n      file: \"social-security-info.component.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contractor-dashboard\\\\components\\\\monthly-reports-tab\\\\monthly-report-dialog\\\\monthly-report-initial-documentation\\\\social-security-info\\\\social-security-info.component.ts\"],\n      names: [],\n      mappings: \";;;AAEA,OAAO,EAAE,UAAU,EAAE,MAAM,4CAA4C,CAAC;AACxE,OAAO,EAAE,kBAAkB,EAAE,MAAM,qDAAqD,CAAC;AAEzF,OAAO,EAAE,UAAU,EAAE,MAAM,6CAA6C,CAAC;AACzE,OAAO,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAE9D,OAAO,EACL,SAAS,EACT,YAAY,EACZ,KAAK,EAGL,MAAM,GAEP,MAAM,eAAe,CAAC;AACvB,OAAO,EACL,WAAW,EAEX,mBAAmB,EACnB,UAAU,GACX,MAAM,gBAAgB,CAAC;AACxB,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AACpE,OAAO,EAAE,SAAS,EAAE,MAAM,wBAAwB,CAAC;AACnD,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,8BAA8B,CAAC;AAChF,OAAO,EAAE,OAAO,EAAE,MAAM,wBAAwB,CAAC;AACjD,OAAO,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AACnD,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AACrD,OAAO,EAAE,UAAU,EAAE,MAAM,2BAA2B,CAAC;AAEvD,OAAO,EACL,iBAAiB,EACjB,gBAAgB,GACjB,MAAM,oCAAoC,CAAC;AAC5C,OAAO,EAAE,QAAQ,EAAE,MAAM,MAAM,CAAC;AAqBzB,IAAM,2BAA2B,GAAjC,MAAM,2BAA2B;IA0BtC,YACmB,EAAe,EACf,UAAsB,EACtB,UAAsB,EACtB,kBAAsC,EACtC,KAAmB;QAJnB,OAAE,GAAF,EAAE,CAAa;QACf,eAAU,GAAV,UAAU,CAAY;QACtB,eAAU,GAAV,UAAU,CAAY;QACtB,uBAAkB,GAAlB,kBAAkB,CAAoB;QACtC,UAAK,GAAL,KAAK,CAAc;QA7B7B,iBAAY,GAAG,KAAK;QACnB,eAAU,GAAG,IAAI,YAAY,EAAQ;QAE/C,SAAI,GAAc,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;YAC9B,GAAG,EAAE,CAAC,EAAE,EAAE,UAAU,CAAC,QAAQ,CAAC;YAC9B,cAAc,EAAE,CAAC,IAAI,EAAE,CAAC,iBAAiB,EAAE,EAAE,gBAAgB,EAAE,CAAC,CAAC;YACjE,GAAG,EAAE,CAAC,EAAE,EAAE,UAAU,CAAC,QAAQ,CAAC;YAC9B,cAAc,EAAE,CAAC,IAAI,EAAE,CAAC,iBAAiB,EAAE,EAAE,gBAAgB,EAAE,CAAC,CAAC;YACjE,WAAW,EAAE,CAAC,EAAE,EAAE,UAAU,CAAC,QAAQ,CAAC;YACtC,sBAAsB,EAAE,CAAC,IAAI,EAAE,CAAC,iBAAiB,EAAE,EAAE,gBAAgB,EAAE,CAAC,CAAC;SAC1E,CAAC,CAAC;QAEH,YAAO,GAAU,EAAE,CAAC;QACpB,YAAO,GAAU,EAAE,CAAC;QACpB,oBAAe,GAAkB,EAAE,CAAC;QAEpC,uBAAkB,GAAgB,IAAI,CAAC;QACvC,uBAAkB,GAAgB,IAAI,CAAC;QACvC,2BAAsB,GAAgB,IAAI,CAAC;QAE3C,2BAAsB,GAAkB,IAAI,CAAC;QAC7C,2BAAsB,GAAkB,IAAI,CAAC;QAC7C,+BAA0B,GAAkB,IAAI,CAAC;IAQ9C,CAAC;IAEJ,QAAQ;QACN,QAAQ,CAAC;YACP,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;YAC7B,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;YAC7B,WAAW,EAAE,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE;SAC9C,CAAC,CAAC,SAAS,CAAC;YACX,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,WAAW,EAAE,EAAE,EAAE;gBAClC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;gBACnB,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;gBACnB,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC;YACrC,CAAC;YACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,0CAA0C,CAAC,CAAC;YACtF,CAAC;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,EAAE;YACpC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,WAAW,CAAC,OAAsB;QAChC,IAAI,OAAO,CAAC,aAAa,CAAC,EAAE,YAAY,EAAE,CAAC;YACzC,MAAM,IAAI,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC;YACjD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;gBACnB,GAAG,EAAE,IAAI,CAAC,KAAK;gBACf,GAAG,EAAE,IAAI,CAAC,KAAK;gBACf,WAAW,EAAE,IAAI,CAAC,aAAa;aAChC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,WAAW,CAAC,WAAmB;QAC7B,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,KAAK,CAAC,CAAC;QAC9D,MAAM,eAAe,GACnB,IAAI,CAAC,WAAW;YAChB,CAAC,CAAC,WAAW,KAAK,gBAAgB;gBAChC,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC;gBACvC,CAAC,WAAW,KAAK,gBAAgB;oBAC/B,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC;gBACzC,CAAC,WAAW,KAAK,wBAAwB;oBACvC,IAAI,CAAC,WAAW,CAAC,yBAAyB,CAAC,CAAC,CAAC;QACnD,OAAO,UAAU,IAAI,OAAO,CAAC,eAAe,CAAC,CAAC;IAChD,CAAC;IAED,IAAI,OAAO;QACT,MAAM,gBAAgB,GAAG,OAAO,CAC9B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,KAAK;YACzB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,KAAK;YAC3B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,KAAK,CACtC,CAAC;QAEF,MAAM,YAAY,GAAG,OAAO,CAC1B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,KAAK;YACpC,IAAI,CAAC,WAAW,EAAE,qBAAqB,CAC1C,CAAC;QAEF,MAAM,YAAY,GAAG,OAAO,CAC1B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,KAAK;YACpC,IAAI,CAAC,WAAW,EAAE,qBAAqB,CAC1C,CAAC;QAEF,MAAM,gBAAgB,GAAG,OAAO,CAC9B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,wBAAwB,CAAC,EAAE,KAAK;YAC5C,IAAI,CAAC,WAAW,EAAE,yBAAyB,CAC9C,CAAC;QAEF,MAAM,OAAO,GACX,gBAAgB,IAAI,YAAY,IAAI,YAAY,IAAI,gBAAgB,CAAC;QAEvE,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,cAAc,CAAC,KAAY,EAAE,WAAmB;QAC9C,MAAM,IAAI,GAAI,KAAK,CAAC,MAA2B,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;QAC3D,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;gBACzB,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC;oBAC7B,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBAC9C,QAAQ,WAAW,EAAE,CAAC;wBACpB,KAAK,gBAAgB;4BACnB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;4BAC/B,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,IAAI,CAAC;4BACxC,MAAM;wBACR,KAAK,gBAAgB;4BACnB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;4BAC/B,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,IAAI,CAAC;4BACxC,MAAM;wBACR,KAAK,wBAAwB;4BAC3B,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;4BACnC,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,IAAI,CAAC;4BAC5C,MAAM;oBACV,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;gBACrD,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;YACpD,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,sBAAsB,EAAE,CAAC;QACvD,CAAC;IACH,CAAC;IAED,YAAY,CAAC,QAAmC;QAC9C,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,KAAK;gBACR,IAAI,IAAI,CAAC,WAAW,EAAE,qBAAqB,EAAE,CAAC;oBAC5C,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC;gBAChE,CAAC;gBACD,MAAM;YACR,KAAK,KAAK;gBACR,IAAI,IAAI,CAAC,WAAW,EAAE,qBAAqB,EAAE,CAAC;oBAC5C,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC;gBAChE,CAAC;gBACD,MAAM;YACR,KAAK,SAAS;gBACZ,IAAI,IAAI,CAAC,WAAW,EAAE,yBAAyB,EAAE,CAAC;oBAChD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,yBAAyB,EAAE,QAAQ,CAAC,CAAC;gBACpE,CAAC;gBACD,MAAM;QACV,CAAC;IACH,CAAC;IAEO,SAAS,CAAC,IAAU;QAC1B,OAAO,IAAI,CAAC,IAAI,KAAK,iBAAiB,CAAC;IACzC,CAAC;IAED,UAAU,CAAC,EAAU;QACnB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC;IAC/D,CAAC;IAED,UAAU,CAAC,EAAU;QACnB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC;IAC/D,CAAC;IAED,kBAAkB,CAAC,EAAU;QAC3B,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC;IACzE,CAAC;;;;;;;;;8BA1KA,KAAK;+BACL,KAAK;6BACL,MAAM;;;AAHI,2BAA2B;IAnBvC,SAAS,CAAC;QACT,QAAQ,EAAE,0BAA0B;QACpC,8BAAoD;QAEpD,UAAU,EAAE,IAAI;QAChB,OAAO,EAAE;YACP,mBAAmB;YACnB,OAAO;YACP,YAAY;YACZ,QAAQ;YACR,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,SAAS;YACT,aAAa;YACb,UAAU;SACX;;KACF,CAAC;GACW,2BAA2B,CA4KvC\",\n      sourcesContent: [\"import { ARL } from '@contractor-dashboard/models/arl.model';\\nimport { PensionFund } from '@contractor-dashboard/models/pension-fund.model';\\nimport { ArlService } from '@contractor-dashboard/services/arl.service';\\nimport { PensionFundService } from '@contractor-dashboard/services/pension-fund.service';\\nimport { Eps } from '@contractor-management/models/eps.model';\\nimport { EpsService } from '@contractor-management/services/eps.service';\\nimport { AlertService } from '@shared/services/alert.service';\\n\\nimport {\\n  Component,\\n  EventEmitter,\\n  Input,\\n  OnChanges,\\n  OnInit,\\n  Output,\\n  SimpleChanges,\\n} from '@angular/core';\\nimport {\\n  FormBuilder,\\n  FormGroup,\\n  ReactiveFormsModule,\\n  Validators,\\n} from '@angular/forms';\\nimport { MatButton, MatIconButton } from '@angular/material/button';\\nimport { MatOption } from '@angular/material/core';\\nimport { MatError, MatFormField, MatLabel } from '@angular/material/form-field';\\nimport { MatIcon } from '@angular/material/icon';\\nimport { MatInput } from '@angular/material/input';\\nimport { MatSelect } from '@angular/material/select';\\nimport { MatTooltip } from '@angular/material/tooltip';\\nimport { InitialReportDocumentation } from '@contractor-dashboard/models/initial-report-documentation.model';\\nimport {\\n  fileSizeValidator,\\n  pdfFileValidator,\\n} from '@shared/validators/file.validators';\\nimport { forkJoin } from 'rxjs';\\n\\n@Component({\\n  selector: 'app-social-security-info',\\n  templateUrl: './social-security-info.component.html',\\n  styleUrl: './social-security-info.component.scss',\\n  standalone: true,\\n  imports: [\\n    ReactiveFormsModule,\\n    MatIcon,\\n    MatFormField,\\n    MatLabel,\\n    MatSelect,\\n    MatOption,\\n    MatInput,\\n    MatError,\\n    MatButton,\\n    MatIconButton,\\n    MatTooltip,\\n  ],\\n})\\nexport class SocialSecurityInfoComponent implements OnInit, OnChanges {\\n  @Input() initialData?: InitialReportDocumentation;\\n  @Input() isSupervisor = false;\\n  @Output() formChange = new EventEmitter<void>();\\n\\n  form: FormGroup = this.fb.group({\\n    eps: ['', Validators.required],\\n    epsSupportFile: [null, [fileSizeValidator(), pdfFileValidator()]],\\n    arl: ['', Validators.required],\\n    arlSupportFile: [null, [fileSizeValidator(), pdfFileValidator()]],\\n    pensionFund: ['', Validators.required],\\n    pensionFundSupportFile: [null, [fileSizeValidator(), pdfFileValidator()]],\\n  });\\n\\n  epsList: Eps[] = [];\\n  arlList: ARL[] = [];\\n  pensionFundList: PensionFund[] = [];\\n\\n  epsCertificateFile: File | null = null;\\n  arlCertificateFile: File | null = null;\\n  pensionCertificateFile: File | null = null;\\n\\n  epsCertificateFileName: string | null = null;\\n  arlCertificateFileName: string | null = null;\\n  pensionCertificateFileName: string | null = null;\\n\\n  constructor(\\n    private readonly fb: FormBuilder,\\n    private readonly epsService: EpsService,\\n    private readonly arlService: ArlService,\\n    private readonly pensionFundService: PensionFundService,\\n    private readonly alert: AlertService,\\n  ) {}\\n\\n  ngOnInit(): void {\\n    forkJoin({\\n      eps: this.epsService.getAll(),\\n      arl: this.arlService.getAll(),\\n      pensionFund: this.pensionFundService.getAll(),\\n    }).subscribe({\\n      next: ({ eps, arl, pensionFund }) => {\\n        this.epsList = eps;\\n        this.arlList = arl;\\n        this.pensionFundList = pensionFund;\\n      },\\n      error: (error) => {\\n        this.alert.error(error.error?.detail ?? 'Error al cargar los datos del formulario');\\n      },\\n    });\\n\\n    this.form.valueChanges.subscribe(() => {\\n      this.formChange.emit();\\n    });\\n  }\\n\\n  ngOnChanges(changes: SimpleChanges): void {\\n    if (changes['initialData']?.currentValue) {\\n      const data = changes['initialData'].currentValue;\\n      this.form.patchValue({\\n        eps: data.epsId,\\n        arl: data.arlId,\\n        pensionFund: data.pensionFundId,\\n      });\\n    }\\n  }\\n\\n  isFileValid(controlName: string): boolean {\\n    const hasNewFile = Boolean(this.form.get(controlName)?.value);\\n    const hasExistingFile =\\n      this.initialData &&\\n      ((controlName === 'epsSupportFile' &&\\n        this.initialData.epsCertificateFileUrl) ||\\n        (controlName === 'arlSupportFile' &&\\n          this.initialData.arlCertificateFileUrl) ||\\n        (controlName === 'pensionFundSupportFile' &&\\n          this.initialData.pensionCertificateFileUrl));\\n    return hasNewFile || Boolean(hasExistingFile);\\n  }\\n\\n  get isValid(): boolean {\\n    const basicFieldsValid = Boolean(\\n      this.form.get('eps')?.valid &&\\n        this.form.get('arl')?.valid &&\\n        this.form.get('pensionFund')?.valid,\\n    );\\n\\n    const epsFileValid = Boolean(\\n      this.form.get('epsSupportFile')?.value ||\\n        this.initialData?.epsCertificateFileUrl,\\n    );\\n\\n    const arlFileValid = Boolean(\\n      this.form.get('arlSupportFile')?.value ||\\n        this.initialData?.arlCertificateFileUrl,\\n    );\\n\\n    const pensionFileValid = Boolean(\\n      this.form.get('pensionFundSupportFile')?.value ||\\n        this.initialData?.pensionCertificateFileUrl,\\n    );\\n\\n    const isValid =\\n      basicFieldsValid && epsFileValid && arlFileValid && pensionFileValid;\\n\\n    return isValid;\\n  }\\n\\n  onFileSelected(event: Event, controlName: string): void {\\n    const file = (event.target as HTMLInputElement).files?.[0];\\n    if (file) {\\n      if (this.isPdfFile(file)) {\\n        if (file.size <= 1024 * 1024) {\\n          this.form.patchValue({ [controlName]: file });\\n          switch (controlName) {\\n            case 'epsSupportFile':\\n              this.epsCertificateFile = file;\\n              this.epsCertificateFileName = file.name;\\n              break;\\n            case 'arlSupportFile':\\n              this.arlCertificateFile = file;\\n              this.arlCertificateFileName = file.name;\\n              break;\\n            case 'pensionFundSupportFile':\\n              this.pensionCertificateFile = file;\\n              this.pensionCertificateFileName = file.name;\\n              break;\\n          }\\n        } else {\\n          this.alert.error('El archivo no debe superar 1MB');\\n        }\\n      } else {\\n        this.alert.error('Solo se permiten archivos PDF');\\n      }\\n      this.form.get(controlName)?.updateValueAndValidity();\\n    }\\n  }\\n\\n  downloadFile(fileType: 'eps' | 'arl' | 'pension'): void {\\n    switch (fileType) {\\n      case 'eps':\\n        if (this.initialData?.epsCertificateFileUrl) {\\n          window.open(this.initialData.epsCertificateFileUrl, '_blank');\\n        }\\n        break;\\n      case 'arl':\\n        if (this.initialData?.arlCertificateFileUrl) {\\n          window.open(this.initialData.arlCertificateFileUrl, '_blank');\\n        }\\n        break;\\n      case 'pension':\\n        if (this.initialData?.pensionCertificateFileUrl) {\\n          window.open(this.initialData.pensionCertificateFileUrl, '_blank');\\n        }\\n        break;\\n    }\\n  }\\n\\n  private isPdfFile(file: File): boolean {\\n    return file.type === 'application/pdf';\\n  }\\n\\n  getEpsName(id: number): string {\\n    return this.epsList.find((eps) => eps.id === id)?.name || '';\\n  }\\n\\n  getArlName(id: number): string {\\n    return this.arlList.find((arl) => arl.id === id)?.name || '';\\n  }\\n\\n  getPensionFundName(id: number): string {\\n    return this.pensionFundList.find((fund) => fund.id === id)?.name || '';\\n  }\\n}\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"57dff35193b7406c2d861e8078225d03ecfb0b98\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_2lymkq89t6 = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_2lymkq89t6();\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./social-security-info.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./social-security-info.component.scss?ngResource\";\nimport { ArlService } from '@contractor-dashboard/services/arl.service';\nimport { PensionFundService } from '@contractor-dashboard/services/pension-fund.service';\nimport { EpsService } from '@contractor-management/services/eps.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatButton, MatIconButton } from '@angular/material/button';\nimport { MatOption } from '@angular/material/core';\nimport { MatError, MatFormField, MatLabel } from '@angular/material/form-field';\nimport { MatIcon } from '@angular/material/icon';\nimport { MatInput } from '@angular/material/input';\nimport { MatSelect } from '@angular/material/select';\nimport { MatTooltip } from '@angular/material/tooltip';\nimport { fileSizeValidator, pdfFileValidator } from '@shared/validators/file.validators';\nimport { forkJoin } from 'rxjs';\ncov_2lymkq89t6().s[0]++;\nlet SocialSecurityInfoComponent = class SocialSecurityInfoComponent {\n  constructor(fb, epsService, arlService, pensionFundService, alert) {\n    cov_2lymkq89t6().f[0]++;\n    cov_2lymkq89t6().s[1]++;\n    this.fb = fb;\n    cov_2lymkq89t6().s[2]++;\n    this.epsService = epsService;\n    cov_2lymkq89t6().s[3]++;\n    this.arlService = arlService;\n    cov_2lymkq89t6().s[4]++;\n    this.pensionFundService = pensionFundService;\n    cov_2lymkq89t6().s[5]++;\n    this.alert = alert;\n    cov_2lymkq89t6().s[6]++;\n    this.isSupervisor = false;\n    cov_2lymkq89t6().s[7]++;\n    this.formChange = new EventEmitter();\n    cov_2lymkq89t6().s[8]++;\n    this.form = this.fb.group({\n      eps: ['', Validators.required],\n      epsSupportFile: [null, [fileSizeValidator(), pdfFileValidator()]],\n      arl: ['', Validators.required],\n      arlSupportFile: [null, [fileSizeValidator(), pdfFileValidator()]],\n      pensionFund: ['', Validators.required],\n      pensionFundSupportFile: [null, [fileSizeValidator(), pdfFileValidator()]]\n    });\n    cov_2lymkq89t6().s[9]++;\n    this.epsList = [];\n    cov_2lymkq89t6().s[10]++;\n    this.arlList = [];\n    cov_2lymkq89t6().s[11]++;\n    this.pensionFundList = [];\n    cov_2lymkq89t6().s[12]++;\n    this.epsCertificateFile = null;\n    cov_2lymkq89t6().s[13]++;\n    this.arlCertificateFile = null;\n    cov_2lymkq89t6().s[14]++;\n    this.pensionCertificateFile = null;\n    cov_2lymkq89t6().s[15]++;\n    this.epsCertificateFileName = null;\n    cov_2lymkq89t6().s[16]++;\n    this.arlCertificateFileName = null;\n    cov_2lymkq89t6().s[17]++;\n    this.pensionCertificateFileName = null;\n  }\n  ngOnInit() {\n    cov_2lymkq89t6().f[1]++;\n    cov_2lymkq89t6().s[18]++;\n    forkJoin({\n      eps: this.epsService.getAll(),\n      arl: this.arlService.getAll(),\n      pensionFund: this.pensionFundService.getAll()\n    }).subscribe({\n      next: ({\n        eps,\n        arl,\n        pensionFund\n      }) => {\n        cov_2lymkq89t6().f[2]++;\n        cov_2lymkq89t6().s[19]++;\n        this.epsList = eps;\n        cov_2lymkq89t6().s[20]++;\n        this.arlList = arl;\n        cov_2lymkq89t6().s[21]++;\n        this.pensionFundList = pensionFund;\n      },\n      error: error => {\n        cov_2lymkq89t6().f[3]++;\n        cov_2lymkq89t6().s[22]++;\n        this.alert.error((cov_2lymkq89t6().b[0][0]++, error.error?.detail) ?? (cov_2lymkq89t6().b[0][1]++, 'Error al cargar los datos del formulario'));\n      }\n    });\n    cov_2lymkq89t6().s[23]++;\n    this.form.valueChanges.subscribe(() => {\n      cov_2lymkq89t6().f[4]++;\n      cov_2lymkq89t6().s[24]++;\n      this.formChange.emit();\n    });\n  }\n  ngOnChanges(changes) {\n    cov_2lymkq89t6().f[5]++;\n    cov_2lymkq89t6().s[25]++;\n    if (changes['initialData']?.currentValue) {\n      cov_2lymkq89t6().b[1][0]++;\n      const data = (cov_2lymkq89t6().s[26]++, changes['initialData'].currentValue);\n      cov_2lymkq89t6().s[27]++;\n      this.form.patchValue({\n        eps: data.epsId,\n        arl: data.arlId,\n        pensionFund: data.pensionFundId\n      });\n    } else {\n      cov_2lymkq89t6().b[1][1]++;\n    }\n  }\n  isFileValid(controlName) {\n    cov_2lymkq89t6().f[6]++;\n    const hasNewFile = (cov_2lymkq89t6().s[28]++, Boolean(this.form.get(controlName)?.value));\n    const hasExistingFile = (cov_2lymkq89t6().s[29]++, (cov_2lymkq89t6().b[2][0]++, this.initialData) && ((cov_2lymkq89t6().b[2][1]++, controlName === 'epsSupportFile') && (cov_2lymkq89t6().b[2][2]++, this.initialData.epsCertificateFileUrl) || (cov_2lymkq89t6().b[2][3]++, controlName === 'arlSupportFile') && (cov_2lymkq89t6().b[2][4]++, this.initialData.arlCertificateFileUrl) || (cov_2lymkq89t6().b[2][5]++, controlName === 'pensionFundSupportFile') && (cov_2lymkq89t6().b[2][6]++, this.initialData.pensionCertificateFileUrl)));\n    cov_2lymkq89t6().s[30]++;\n    return (cov_2lymkq89t6().b[3][0]++, hasNewFile) || (cov_2lymkq89t6().b[3][1]++, Boolean(hasExistingFile));\n  }\n  get isValid() {\n    cov_2lymkq89t6().f[7]++;\n    const basicFieldsValid = (cov_2lymkq89t6().s[31]++, Boolean((cov_2lymkq89t6().b[4][0]++, this.form.get('eps')?.valid) && (cov_2lymkq89t6().b[4][1]++, this.form.get('arl')?.valid) && (cov_2lymkq89t6().b[4][2]++, this.form.get('pensionFund')?.valid)));\n    const epsFileValid = (cov_2lymkq89t6().s[32]++, Boolean((cov_2lymkq89t6().b[5][0]++, this.form.get('epsSupportFile')?.value) || (cov_2lymkq89t6().b[5][1]++, this.initialData?.epsCertificateFileUrl)));\n    const arlFileValid = (cov_2lymkq89t6().s[33]++, Boolean((cov_2lymkq89t6().b[6][0]++, this.form.get('arlSupportFile')?.value) || (cov_2lymkq89t6().b[6][1]++, this.initialData?.arlCertificateFileUrl)));\n    const pensionFileValid = (cov_2lymkq89t6().s[34]++, Boolean((cov_2lymkq89t6().b[7][0]++, this.form.get('pensionFundSupportFile')?.value) || (cov_2lymkq89t6().b[7][1]++, this.initialData?.pensionCertificateFileUrl)));\n    const isValid = (cov_2lymkq89t6().s[35]++, (cov_2lymkq89t6().b[8][0]++, basicFieldsValid) && (cov_2lymkq89t6().b[8][1]++, epsFileValid) && (cov_2lymkq89t6().b[8][2]++, arlFileValid) && (cov_2lymkq89t6().b[8][3]++, pensionFileValid));\n    cov_2lymkq89t6().s[36]++;\n    return isValid;\n  }\n  onFileSelected(event, controlName) {\n    cov_2lymkq89t6().f[8]++;\n    const file = (cov_2lymkq89t6().s[37]++, event.target.files?.[0]);\n    cov_2lymkq89t6().s[38]++;\n    if (file) {\n      cov_2lymkq89t6().b[9][0]++;\n      cov_2lymkq89t6().s[39]++;\n      if (this.isPdfFile(file)) {\n        cov_2lymkq89t6().b[10][0]++;\n        cov_2lymkq89t6().s[40]++;\n        if (file.size <= 1024 * 1024) {\n          cov_2lymkq89t6().b[11][0]++;\n          cov_2lymkq89t6().s[41]++;\n          this.form.patchValue({\n            [controlName]: file\n          });\n          cov_2lymkq89t6().s[42]++;\n          switch (controlName) {\n            case 'epsSupportFile':\n              cov_2lymkq89t6().b[12][0]++;\n              cov_2lymkq89t6().s[43]++;\n              this.epsCertificateFile = file;\n              cov_2lymkq89t6().s[44]++;\n              this.epsCertificateFileName = file.name;\n              cov_2lymkq89t6().s[45]++;\n              break;\n            case 'arlSupportFile':\n              cov_2lymkq89t6().b[12][1]++;\n              cov_2lymkq89t6().s[46]++;\n              this.arlCertificateFile = file;\n              cov_2lymkq89t6().s[47]++;\n              this.arlCertificateFileName = file.name;\n              cov_2lymkq89t6().s[48]++;\n              break;\n            case 'pensionFundSupportFile':\n              cov_2lymkq89t6().b[12][2]++;\n              cov_2lymkq89t6().s[49]++;\n              this.pensionCertificateFile = file;\n              cov_2lymkq89t6().s[50]++;\n              this.pensionCertificateFileName = file.name;\n              cov_2lymkq89t6().s[51]++;\n              break;\n          }\n        } else {\n          cov_2lymkq89t6().b[11][1]++;\n          cov_2lymkq89t6().s[52]++;\n          this.alert.error('El archivo no debe superar 1MB');\n        }\n      } else {\n        cov_2lymkq89t6().b[10][1]++;\n        cov_2lymkq89t6().s[53]++;\n        this.alert.error('Solo se permiten archivos PDF');\n      }\n      cov_2lymkq89t6().s[54]++;\n      this.form.get(controlName)?.updateValueAndValidity();\n    } else {\n      cov_2lymkq89t6().b[9][1]++;\n    }\n  }\n  downloadFile(fileType) {\n    cov_2lymkq89t6().f[9]++;\n    cov_2lymkq89t6().s[55]++;\n    switch (fileType) {\n      case 'eps':\n        cov_2lymkq89t6().b[13][0]++;\n        cov_2lymkq89t6().s[56]++;\n        if (this.initialData?.epsCertificateFileUrl) {\n          cov_2lymkq89t6().b[14][0]++;\n          cov_2lymkq89t6().s[57]++;\n          window.open(this.initialData.epsCertificateFileUrl, '_blank');\n        } else {\n          cov_2lymkq89t6().b[14][1]++;\n        }\n        cov_2lymkq89t6().s[58]++;\n        break;\n      case 'arl':\n        cov_2lymkq89t6().b[13][1]++;\n        cov_2lymkq89t6().s[59]++;\n        if (this.initialData?.arlCertificateFileUrl) {\n          cov_2lymkq89t6().b[15][0]++;\n          cov_2lymkq89t6().s[60]++;\n          window.open(this.initialData.arlCertificateFileUrl, '_blank');\n        } else {\n          cov_2lymkq89t6().b[15][1]++;\n        }\n        cov_2lymkq89t6().s[61]++;\n        break;\n      case 'pension':\n        cov_2lymkq89t6().b[13][2]++;\n        cov_2lymkq89t6().s[62]++;\n        if (this.initialData?.pensionCertificateFileUrl) {\n          cov_2lymkq89t6().b[16][0]++;\n          cov_2lymkq89t6().s[63]++;\n          window.open(this.initialData.pensionCertificateFileUrl, '_blank');\n        } else {\n          cov_2lymkq89t6().b[16][1]++;\n        }\n        cov_2lymkq89t6().s[64]++;\n        break;\n    }\n  }\n  isPdfFile(file) {\n    cov_2lymkq89t6().f[10]++;\n    cov_2lymkq89t6().s[65]++;\n    return file.type === 'application/pdf';\n  }\n  getEpsName(id) {\n    cov_2lymkq89t6().f[11]++;\n    cov_2lymkq89t6().s[66]++;\n    return (cov_2lymkq89t6().b[17][0]++, this.epsList.find(eps => {\n      cov_2lymkq89t6().f[12]++;\n      cov_2lymkq89t6().s[67]++;\n      return eps.id === id;\n    })?.name) || (cov_2lymkq89t6().b[17][1]++, '');\n  }\n  getArlName(id) {\n    cov_2lymkq89t6().f[13]++;\n    cov_2lymkq89t6().s[68]++;\n    return (cov_2lymkq89t6().b[18][0]++, this.arlList.find(arl => {\n      cov_2lymkq89t6().f[14]++;\n      cov_2lymkq89t6().s[69]++;\n      return arl.id === id;\n    })?.name) || (cov_2lymkq89t6().b[18][1]++, '');\n  }\n  getPensionFundName(id) {\n    cov_2lymkq89t6().f[15]++;\n    cov_2lymkq89t6().s[70]++;\n    return (cov_2lymkq89t6().b[19][0]++, this.pensionFundList.find(fund => {\n      cov_2lymkq89t6().f[16]++;\n      cov_2lymkq89t6().s[71]++;\n      return fund.id === id;\n    })?.name) || (cov_2lymkq89t6().b[19][1]++, '');\n  }\n  static {\n    cov_2lymkq89t6().s[72]++;\n    this.ctorParameters = () => {\n      cov_2lymkq89t6().f[17]++;\n      cov_2lymkq89t6().s[73]++;\n      return [{\n        type: FormBuilder\n      }, {\n        type: EpsService\n      }, {\n        type: ArlService\n      }, {\n        type: PensionFundService\n      }, {\n        type: AlertService\n      }];\n    };\n  }\n  static {\n    cov_2lymkq89t6().s[74]++;\n    this.propDecorators = {\n      initialData: [{\n        type: Input\n      }],\n      isSupervisor: [{\n        type: Input\n      }],\n      formChange: [{\n        type: Output\n      }]\n    };\n  }\n};\ncov_2lymkq89t6().s[75]++;\nSocialSecurityInfoComponent = __decorate([Component({\n  selector: 'app-social-security-info',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: true,\n  imports: [ReactiveFormsModule, MatIcon, MatFormField, MatLabel, MatSelect, MatOption, MatInput, MatError, MatButton, MatIconButton, MatTooltip],\n  styles: [__NG_CLI_RESOURCE__1]\n})], SocialSecurityInfoComponent);\nexport { SocialSecurityInfoComponent };", "map": {"version": 3, "names": ["cov_2lymkq89t6", "actualCoverage", "ArlService", "PensionFundService", "EpsService", "AlertService", "Component", "EventEmitter", "Input", "Output", "FormBuilder", "ReactiveFormsModule", "Validators", "MatButton", "MatIconButton", "MatOption", "<PERSON><PERSON><PERSON><PERSON>", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatIcon", "MatInput", "MatSelect", "MatTooltip", "fileSizeValidator", "pdfFileValidator", "fork<PERSON><PERSON>n", "s", "SocialSecurityInfoComponent", "constructor", "fb", "epsService", "arlService", "pensionFundService", "alert", "f", "isSupervisor", "formChange", "form", "group", "eps", "required", "epsSupportFile", "arl", "arlSupportFile", "pensionFund", "pensionFundSupportFile", "epsList", "arlList", "pensionFundList", "epsCertificateFile", "arlCertificateFile", "pensionCertificateFile", "epsCertificateFileName", "arlCertificateFileName", "pensionCertificateFileName", "ngOnInit", "getAll", "subscribe", "next", "error", "b", "detail", "valueChanges", "emit", "ngOnChanges", "changes", "currentValue", "data", "patchValue", "epsId", "arlId", "pensionFundId", "isFileValid", "controlName", "hasNewFile", "Boolean", "get", "value", "hasExistingFile", "initialData", "epsCertificateFileUrl", "arlCertificateFileUrl", "pensionCertificateFileUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valid", "epsFileValid", "arlFile<PERSON><PERSON>", "pensionFileValid", "onFileSelected", "event", "file", "target", "files", "isPdfFile", "size", "name", "updateValueAndValidity", "downloadFile", "fileType", "window", "open", "type", "getEpsName", "id", "find", "getArlName", "getPensionFundName", "fund", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\components\\monthly-reports-tab\\monthly-report-dialog\\monthly-report-initial-documentation\\social-security-info\\social-security-info.component.ts"], "sourcesContent": ["import { ARL } from '@contractor-dashboard/models/arl.model';\nimport { PensionFund } from '@contractor-dashboard/models/pension-fund.model';\nimport { ArlService } from '@contractor-dashboard/services/arl.service';\nimport { PensionFundService } from '@contractor-dashboard/services/pension-fund.service';\nimport { Eps } from '@contractor-management/models/eps.model';\nimport { EpsService } from '@contractor-management/services/eps.service';\nimport { AlertService } from '@shared/services/alert.service';\n\nimport {\n  Component,\n  EventEmitter,\n  Input,\n  OnChanges,\n  OnInit,\n  Output,\n  SimpleChanges,\n} from '@angular/core';\nimport {\n  FormBuilder,\n  FormGroup,\n  ReactiveFormsModule,\n  Validators,\n} from '@angular/forms';\nimport { MatButton, MatIconButton } from '@angular/material/button';\nimport { MatOption } from '@angular/material/core';\nimport { MatError, MatFormField, MatLabel } from '@angular/material/form-field';\nimport { MatIcon } from '@angular/material/icon';\nimport { MatInput } from '@angular/material/input';\nimport { MatSelect } from '@angular/material/select';\nimport { MatTooltip } from '@angular/material/tooltip';\nimport { InitialReportDocumentation } from '@contractor-dashboard/models/initial-report-documentation.model';\nimport {\n  fileSizeValidator,\n  pdfFileValidator,\n} from '@shared/validators/file.validators';\nimport { forkJoin } from 'rxjs';\n\n@Component({\n  selector: 'app-social-security-info',\n  templateUrl: './social-security-info.component.html',\n  styleUrl: './social-security-info.component.scss',\n  standalone: true,\n  imports: [\n    ReactiveFormsModule,\n    MatIcon,\n    MatFormField,\n    MatLabel,\n    MatSelect,\n    MatOption,\n    MatInput,\n    MatError,\n    MatButton,\n    MatIconButton,\n    MatTooltip,\n  ],\n})\nexport class SocialSecurityInfoComponent implements OnInit, OnChanges {\n  @Input() initialData?: InitialReportDocumentation;\n  @Input() isSupervisor = false;\n  @Output() formChange = new EventEmitter<void>();\n\n  form: FormGroup = this.fb.group({\n    eps: ['', Validators.required],\n    epsSupportFile: [null, [fileSizeValidator(), pdfFileValidator()]],\n    arl: ['', Validators.required],\n    arlSupportFile: [null, [fileSizeValidator(), pdfFileValidator()]],\n    pensionFund: ['', Validators.required],\n    pensionFundSupportFile: [null, [fileSizeValidator(), pdfFileValidator()]],\n  });\n\n  epsList: Eps[] = [];\n  arlList: ARL[] = [];\n  pensionFundList: PensionFund[] = [];\n\n  epsCertificateFile: File | null = null;\n  arlCertificateFile: File | null = null;\n  pensionCertificateFile: File | null = null;\n\n  epsCertificateFileName: string | null = null;\n  arlCertificateFileName: string | null = null;\n  pensionCertificateFileName: string | null = null;\n\n  constructor(\n    private readonly fb: FormBuilder,\n    private readonly epsService: EpsService,\n    private readonly arlService: ArlService,\n    private readonly pensionFundService: PensionFundService,\n    private readonly alert: AlertService,\n  ) {}\n\n  ngOnInit(): void {\n    forkJoin({\n      eps: this.epsService.getAll(),\n      arl: this.arlService.getAll(),\n      pensionFund: this.pensionFundService.getAll(),\n    }).subscribe({\n      next: ({ eps, arl, pensionFund }) => {\n        this.epsList = eps;\n        this.arlList = arl;\n        this.pensionFundList = pensionFund;\n      },\n      error: (error) => {\n        this.alert.error(error.error?.detail ?? 'Error al cargar los datos del formulario');\n      },\n    });\n\n    this.form.valueChanges.subscribe(() => {\n      this.formChange.emit();\n    });\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    if (changes['initialData']?.currentValue) {\n      const data = changes['initialData'].currentValue;\n      this.form.patchValue({\n        eps: data.epsId,\n        arl: data.arlId,\n        pensionFund: data.pensionFundId,\n      });\n    }\n  }\n\n  isFileValid(controlName: string): boolean {\n    const hasNewFile = Boolean(this.form.get(controlName)?.value);\n    const hasExistingFile =\n      this.initialData &&\n      ((controlName === 'epsSupportFile' &&\n        this.initialData.epsCertificateFileUrl) ||\n        (controlName === 'arlSupportFile' &&\n          this.initialData.arlCertificateFileUrl) ||\n        (controlName === 'pensionFundSupportFile' &&\n          this.initialData.pensionCertificateFileUrl));\n    return hasNewFile || Boolean(hasExistingFile);\n  }\n\n  get isValid(): boolean {\n    const basicFieldsValid = Boolean(\n      this.form.get('eps')?.valid &&\n        this.form.get('arl')?.valid &&\n        this.form.get('pensionFund')?.valid,\n    );\n\n    const epsFileValid = Boolean(\n      this.form.get('epsSupportFile')?.value ||\n        this.initialData?.epsCertificateFileUrl,\n    );\n\n    const arlFileValid = Boolean(\n      this.form.get('arlSupportFile')?.value ||\n        this.initialData?.arlCertificateFileUrl,\n    );\n\n    const pensionFileValid = Boolean(\n      this.form.get('pensionFundSupportFile')?.value ||\n        this.initialData?.pensionCertificateFileUrl,\n    );\n\n    const isValid =\n      basicFieldsValid && epsFileValid && arlFileValid && pensionFileValid;\n\n    return isValid;\n  }\n\n  onFileSelected(event: Event, controlName: string): void {\n    const file = (event.target as HTMLInputElement).files?.[0];\n    if (file) {\n      if (this.isPdfFile(file)) {\n        if (file.size <= 1024 * 1024) {\n          this.form.patchValue({ [controlName]: file });\n          switch (controlName) {\n            case 'epsSupportFile':\n              this.epsCertificateFile = file;\n              this.epsCertificateFileName = file.name;\n              break;\n            case 'arlSupportFile':\n              this.arlCertificateFile = file;\n              this.arlCertificateFileName = file.name;\n              break;\n            case 'pensionFundSupportFile':\n              this.pensionCertificateFile = file;\n              this.pensionCertificateFileName = file.name;\n              break;\n          }\n        } else {\n          this.alert.error('El archivo no debe superar 1MB');\n        }\n      } else {\n        this.alert.error('Solo se permiten archivos PDF');\n      }\n      this.form.get(controlName)?.updateValueAndValidity();\n    }\n  }\n\n  downloadFile(fileType: 'eps' | 'arl' | 'pension'): void {\n    switch (fileType) {\n      case 'eps':\n        if (this.initialData?.epsCertificateFileUrl) {\n          window.open(this.initialData.epsCertificateFileUrl, '_blank');\n        }\n        break;\n      case 'arl':\n        if (this.initialData?.arlCertificateFileUrl) {\n          window.open(this.initialData.arlCertificateFileUrl, '_blank');\n        }\n        break;\n      case 'pension':\n        if (this.initialData?.pensionCertificateFileUrl) {\n          window.open(this.initialData.pensionCertificateFileUrl, '_blank');\n        }\n        break;\n    }\n  }\n\n  private isPdfFile(file: File): boolean {\n    return file.type === 'application/pdf';\n  }\n\n  getEpsName(id: number): string {\n    return this.epsList.find((eps) => eps.id === id)?.name || '';\n  }\n\n  getArlName(id: number): string {\n    return this.arlList.find((arl) => arl.id === id)?.name || '';\n  }\n\n  getPensionFundName(id: number): string {\n    return this.pensionFundList.find((fund) => fund.id === id)?.name || '';\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA6BS;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;AA3BT,SAASE,UAAU,QAAQ,4CAA4C;AACvE,SAASC,kBAAkB,QAAQ,qDAAqD;AAExF,SAASC,UAAU,QAAQ,6CAA6C;AACxE,SAASC,YAAY,QAAQ,gCAAgC;AAE7D,SACEC,SAAS,EACTC,YAAY,EACZC,KAAK,EAGLC,MAAM,QAED,eAAe;AACtB,SACEC,WAAW,EAEXC,mBAAmB,EACnBC,UAAU,QACL,gBAAgB;AACvB,SAASC,SAAS,EAAEC,aAAa,QAAQ,0BAA0B;AACnE,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,QAAQ,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,8BAA8B;AAC/E,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,UAAU,QAAQ,2BAA2B;AAEtD,SACEC,iBAAiB,EACjBC,gBAAgB,QACX,oCAAoC;AAC3C,SAASC,QAAQ,QAAQ,MAAM;AAACzB,cAAA,GAAA0B,CAAA;AAqBzB,IAAMC,2BAA2B,GAAjC,MAAMA,2BAA2B;EA0BtCC,YACmBC,EAAe,EACfC,UAAsB,EACtBC,UAAsB,EACtBC,kBAAsC,EACtCC,KAAmB;IAAAjC,cAAA,GAAAkC,CAAA;IAAAlC,cAAA,GAAA0B,CAAA;IAJnB,KAAAG,EAAE,GAAFA,EAAE;IAAa7B,cAAA,GAAA0B,CAAA;IACf,KAAAI,UAAU,GAAVA,UAAU;IAAY9B,cAAA,GAAA0B,CAAA;IACtB,KAAAK,UAAU,GAAVA,UAAU;IAAY/B,cAAA,GAAA0B,CAAA;IACtB,KAAAM,kBAAkB,GAAlBA,kBAAkB;IAAoBhC,cAAA,GAAA0B,CAAA;IACtC,KAAAO,KAAK,GAALA,KAAK;IAAcjC,cAAA,GAAA0B,CAAA;IA7B7B,KAAAS,YAAY,GAAG,KAAK;IAAAnC,cAAA,GAAA0B,CAAA;IACnB,KAAAU,UAAU,GAAG,IAAI7B,YAAY,EAAQ;IAAAP,cAAA,GAAA0B,CAAA;IAE/C,KAAAW,IAAI,GAAc,IAAI,CAACR,EAAE,CAACS,KAAK,CAAC;MAC9BC,GAAG,EAAE,CAAC,EAAE,EAAE3B,UAAU,CAAC4B,QAAQ,CAAC;MAC9BC,cAAc,EAAE,CAAC,IAAI,EAAE,CAAClB,iBAAiB,EAAE,EAAEC,gBAAgB,EAAE,CAAC,CAAC;MACjEkB,GAAG,EAAE,CAAC,EAAE,EAAE9B,UAAU,CAAC4B,QAAQ,CAAC;MAC9BG,cAAc,EAAE,CAAC,IAAI,EAAE,CAACpB,iBAAiB,EAAE,EAAEC,gBAAgB,EAAE,CAAC,CAAC;MACjEoB,WAAW,EAAE,CAAC,EAAE,EAAEhC,UAAU,CAAC4B,QAAQ,CAAC;MACtCK,sBAAsB,EAAE,CAAC,IAAI,EAAE,CAACtB,iBAAiB,EAAE,EAAEC,gBAAgB,EAAE,CAAC;KACzE,CAAC;IAACxB,cAAA,GAAA0B,CAAA;IAEH,KAAAoB,OAAO,GAAU,EAAE;IAAC9C,cAAA,GAAA0B,CAAA;IACpB,KAAAqB,OAAO,GAAU,EAAE;IAAC/C,cAAA,GAAA0B,CAAA;IACpB,KAAAsB,eAAe,GAAkB,EAAE;IAAChD,cAAA,GAAA0B,CAAA;IAEpC,KAAAuB,kBAAkB,GAAgB,IAAI;IAACjD,cAAA,GAAA0B,CAAA;IACvC,KAAAwB,kBAAkB,GAAgB,IAAI;IAAClD,cAAA,GAAA0B,CAAA;IACvC,KAAAyB,sBAAsB,GAAgB,IAAI;IAACnD,cAAA,GAAA0B,CAAA;IAE3C,KAAA0B,sBAAsB,GAAkB,IAAI;IAACpD,cAAA,GAAA0B,CAAA;IAC7C,KAAA2B,sBAAsB,GAAkB,IAAI;IAACrD,cAAA,GAAA0B,CAAA;IAC7C,KAAA4B,0BAA0B,GAAkB,IAAI;EAQ7C;EAEHC,QAAQA,CAAA;IAAAvD,cAAA,GAAAkC,CAAA;IAAAlC,cAAA,GAAA0B,CAAA;IACND,QAAQ,CAAC;MACPc,GAAG,EAAE,IAAI,CAACT,UAAU,CAAC0B,MAAM,EAAE;MAC7Bd,GAAG,EAAE,IAAI,CAACX,UAAU,CAACyB,MAAM,EAAE;MAC7BZ,WAAW,EAAE,IAAI,CAACZ,kBAAkB,CAACwB,MAAM;KAC5C,CAAC,CAACC,SAAS,CAAC;MACXC,IAAI,EAAEA,CAAC;QAAEnB,GAAG;QAAEG,GAAG;QAAEE;MAAW,CAAE,KAAI;QAAA5C,cAAA,GAAAkC,CAAA;QAAAlC,cAAA,GAAA0B,CAAA;QAClC,IAAI,CAACoB,OAAO,GAAGP,GAAG;QAACvC,cAAA,GAAA0B,CAAA;QACnB,IAAI,CAACqB,OAAO,GAAGL,GAAG;QAAC1C,cAAA,GAAA0B,CAAA;QACnB,IAAI,CAACsB,eAAe,GAAGJ,WAAW;MACpC,CAAC;MACDe,KAAK,EAAGA,KAAK,IAAI;QAAA3D,cAAA,GAAAkC,CAAA;QAAAlC,cAAA,GAAA0B,CAAA;QACf,IAAI,CAACO,KAAK,CAAC0B,KAAK,CAAC,CAAA3D,cAAA,GAAA4D,CAAA,UAAAD,KAAK,CAACA,KAAK,EAAEE,MAAM,MAAA7D,cAAA,GAAA4D,CAAA,UAAI,0CAA0C,EAAC;MACrF;KACD,CAAC;IAAC5D,cAAA,GAAA0B,CAAA;IAEH,IAAI,CAACW,IAAI,CAACyB,YAAY,CAACL,SAAS,CAAC,MAAK;MAAAzD,cAAA,GAAAkC,CAAA;MAAAlC,cAAA,GAAA0B,CAAA;MACpC,IAAI,CAACU,UAAU,CAAC2B,IAAI,EAAE;IACxB,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAACC,OAAsB;IAAAjE,cAAA,GAAAkC,CAAA;IAAAlC,cAAA,GAAA0B,CAAA;IAChC,IAAIuC,OAAO,CAAC,aAAa,CAAC,EAAEC,YAAY,EAAE;MAAAlE,cAAA,GAAA4D,CAAA;MACxC,MAAMO,IAAI,IAAAnE,cAAA,GAAA0B,CAAA,QAAGuC,OAAO,CAAC,aAAa,CAAC,CAACC,YAAY;MAAClE,cAAA,GAAA0B,CAAA;MACjD,IAAI,CAACW,IAAI,CAAC+B,UAAU,CAAC;QACnB7B,GAAG,EAAE4B,IAAI,CAACE,KAAK;QACf3B,GAAG,EAAEyB,IAAI,CAACG,KAAK;QACf1B,WAAW,EAAEuB,IAAI,CAACI;OACnB,CAAC;IACJ,CAAC;MAAAvE,cAAA,GAAA4D,CAAA;IAAA;EACH;EAEAY,WAAWA,CAACC,WAAmB;IAAAzE,cAAA,GAAAkC,CAAA;IAC7B,MAAMwC,UAAU,IAAA1E,cAAA,GAAA0B,CAAA,QAAGiD,OAAO,CAAC,IAAI,CAACtC,IAAI,CAACuC,GAAG,CAACH,WAAW,CAAC,EAAEI,KAAK,CAAC;IAC7D,MAAMC,eAAe,IAAA9E,cAAA,GAAA0B,CAAA,QACnB,CAAA1B,cAAA,GAAA4D,CAAA,cAAI,CAACmB,WAAW,MACd,CAAA/E,cAAA,GAAA4D,CAAA,UAAAa,WAAW,KAAK,gBAAgB,MAAAzE,cAAA,GAAA4D,CAAA,UAChC,IAAI,CAACmB,WAAW,CAACC,qBAAqB,KACrC,CAAAhF,cAAA,GAAA4D,CAAA,UAAAa,WAAW,KAAK,gBAAgB,MAAAzE,cAAA,GAAA4D,CAAA,UAC/B,IAAI,CAACmB,WAAW,CAACE,qBAAqB,CAAC,IACxC,CAAAjF,cAAA,GAAA4D,CAAA,UAAAa,WAAW,KAAK,wBAAwB,MAAAzE,cAAA,GAAA4D,CAAA,UACvC,IAAI,CAACmB,WAAW,CAACG,yBAAyB,CAAC,CAAC;IAAClF,cAAA,GAAA0B,CAAA;IACnD,OAAO,CAAA1B,cAAA,GAAA4D,CAAA,UAAAc,UAAU,MAAA1E,cAAA,GAAA4D,CAAA,UAAIe,OAAO,CAACG,eAAe,CAAC;EAC/C;EAEA,IAAIK,OAAOA,CAAA;IAAAnF,cAAA,GAAAkC,CAAA;IACT,MAAMkD,gBAAgB,IAAApF,cAAA,GAAA0B,CAAA,QAAGiD,OAAO,CAC9B,CAAA3E,cAAA,GAAA4D,CAAA,cAAI,CAACvB,IAAI,CAACuC,GAAG,CAAC,KAAK,CAAC,EAAES,KAAK,MAAArF,cAAA,GAAA4D,CAAA,UACzB,IAAI,CAACvB,IAAI,CAACuC,GAAG,CAAC,KAAK,CAAC,EAAES,KAAK,MAAArF,cAAA,GAAA4D,CAAA,UAC3B,IAAI,CAACvB,IAAI,CAACuC,GAAG,CAAC,aAAa,CAAC,EAAES,KAAK,EACtC;IAED,MAAMC,YAAY,IAAAtF,cAAA,GAAA0B,CAAA,QAAGiD,OAAO,CAC1B,CAAA3E,cAAA,GAAA4D,CAAA,cAAI,CAACvB,IAAI,CAACuC,GAAG,CAAC,gBAAgB,CAAC,EAAEC,KAAK,MAAA7E,cAAA,GAAA4D,CAAA,UACpC,IAAI,CAACmB,WAAW,EAAEC,qBAAqB,EAC1C;IAED,MAAMO,YAAY,IAAAvF,cAAA,GAAA0B,CAAA,QAAGiD,OAAO,CAC1B,CAAA3E,cAAA,GAAA4D,CAAA,cAAI,CAACvB,IAAI,CAACuC,GAAG,CAAC,gBAAgB,CAAC,EAAEC,KAAK,MAAA7E,cAAA,GAAA4D,CAAA,UACpC,IAAI,CAACmB,WAAW,EAAEE,qBAAqB,EAC1C;IAED,MAAMO,gBAAgB,IAAAxF,cAAA,GAAA0B,CAAA,QAAGiD,OAAO,CAC9B,CAAA3E,cAAA,GAAA4D,CAAA,cAAI,CAACvB,IAAI,CAACuC,GAAG,CAAC,wBAAwB,CAAC,EAAEC,KAAK,MAAA7E,cAAA,GAAA4D,CAAA,UAC5C,IAAI,CAACmB,WAAW,EAAEG,yBAAyB,EAC9C;IAED,MAAMC,OAAO,IAAAnF,cAAA,GAAA0B,CAAA,QACX,CAAA1B,cAAA,GAAA4D,CAAA,UAAAwB,gBAAgB,MAAApF,cAAA,GAAA4D,CAAA,UAAI0B,YAAY,MAAAtF,cAAA,GAAA4D,CAAA,UAAI2B,YAAY,MAAAvF,cAAA,GAAA4D,CAAA,UAAI4B,gBAAgB;IAACxF,cAAA,GAAA0B,CAAA;IAEvE,OAAOyD,OAAO;EAChB;EAEAM,cAAcA,CAACC,KAAY,EAAEjB,WAAmB;IAAAzE,cAAA,GAAAkC,CAAA;IAC9C,MAAMyD,IAAI,IAAA3F,cAAA,GAAA0B,CAAA,QAAIgE,KAAK,CAACE,MAA2B,CAACC,KAAK,GAAG,CAAC,CAAC;IAAC7F,cAAA,GAAA0B,CAAA;IAC3D,IAAIiE,IAAI,EAAE;MAAA3F,cAAA,GAAA4D,CAAA;MAAA5D,cAAA,GAAA0B,CAAA;MACR,IAAI,IAAI,CAACoE,SAAS,CAACH,IAAI,CAAC,EAAE;QAAA3F,cAAA,GAAA4D,CAAA;QAAA5D,cAAA,GAAA0B,CAAA;QACxB,IAAIiE,IAAI,CAACI,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE;UAAA/F,cAAA,GAAA4D,CAAA;UAAA5D,cAAA,GAAA0B,CAAA;UAC5B,IAAI,CAACW,IAAI,CAAC+B,UAAU,CAAC;YAAE,CAACK,WAAW,GAAGkB;UAAI,CAAE,CAAC;UAAC3F,cAAA,GAAA0B,CAAA;UAC9C,QAAQ+C,WAAW;YACjB,KAAK,gBAAgB;cAAAzE,cAAA,GAAA4D,CAAA;cAAA5D,cAAA,GAAA0B,CAAA;cACnB,IAAI,CAACuB,kBAAkB,GAAG0C,IAAI;cAAC3F,cAAA,GAAA0B,CAAA;cAC/B,IAAI,CAAC0B,sBAAsB,GAAGuC,IAAI,CAACK,IAAI;cAAChG,cAAA,GAAA0B,CAAA;cACxC;YACF,KAAK,gBAAgB;cAAA1B,cAAA,GAAA4D,CAAA;cAAA5D,cAAA,GAAA0B,CAAA;cACnB,IAAI,CAACwB,kBAAkB,GAAGyC,IAAI;cAAC3F,cAAA,GAAA0B,CAAA;cAC/B,IAAI,CAAC2B,sBAAsB,GAAGsC,IAAI,CAACK,IAAI;cAAChG,cAAA,GAAA0B,CAAA;cACxC;YACF,KAAK,wBAAwB;cAAA1B,cAAA,GAAA4D,CAAA;cAAA5D,cAAA,GAAA0B,CAAA;cAC3B,IAAI,CAACyB,sBAAsB,GAAGwC,IAAI;cAAC3F,cAAA,GAAA0B,CAAA;cACnC,IAAI,CAAC4B,0BAA0B,GAAGqC,IAAI,CAACK,IAAI;cAAChG,cAAA,GAAA0B,CAAA;cAC5C;UACJ;QACF,CAAC,MAAM;UAAA1B,cAAA,GAAA4D,CAAA;UAAA5D,cAAA,GAAA0B,CAAA;UACL,IAAI,CAACO,KAAK,CAAC0B,KAAK,CAAC,gCAAgC,CAAC;QACpD;MACF,CAAC,MAAM;QAAA3D,cAAA,GAAA4D,CAAA;QAAA5D,cAAA,GAAA0B,CAAA;QACL,IAAI,CAACO,KAAK,CAAC0B,KAAK,CAAC,+BAA+B,CAAC;MACnD;MAAC3D,cAAA,GAAA0B,CAAA;MACD,IAAI,CAACW,IAAI,CAACuC,GAAG,CAACH,WAAW,CAAC,EAAEwB,sBAAsB,EAAE;IACtD,CAAC;MAAAjG,cAAA,GAAA4D,CAAA;IAAA;EACH;EAEAsC,YAAYA,CAACC,QAAmC;IAAAnG,cAAA,GAAAkC,CAAA;IAAAlC,cAAA,GAAA0B,CAAA;IAC9C,QAAQyE,QAAQ;MACd,KAAK,KAAK;QAAAnG,cAAA,GAAA4D,CAAA;QAAA5D,cAAA,GAAA0B,CAAA;QACR,IAAI,IAAI,CAACqD,WAAW,EAAEC,qBAAqB,EAAE;UAAAhF,cAAA,GAAA4D,CAAA;UAAA5D,cAAA,GAAA0B,CAAA;UAC3C0E,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtB,WAAW,CAACC,qBAAqB,EAAE,QAAQ,CAAC;QAC/D,CAAC;UAAAhF,cAAA,GAAA4D,CAAA;QAAA;QAAA5D,cAAA,GAAA0B,CAAA;QACD;MACF,KAAK,KAAK;QAAA1B,cAAA,GAAA4D,CAAA;QAAA5D,cAAA,GAAA0B,CAAA;QACR,IAAI,IAAI,CAACqD,WAAW,EAAEE,qBAAqB,EAAE;UAAAjF,cAAA,GAAA4D,CAAA;UAAA5D,cAAA,GAAA0B,CAAA;UAC3C0E,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtB,WAAW,CAACE,qBAAqB,EAAE,QAAQ,CAAC;QAC/D,CAAC;UAAAjF,cAAA,GAAA4D,CAAA;QAAA;QAAA5D,cAAA,GAAA0B,CAAA;QACD;MACF,KAAK,SAAS;QAAA1B,cAAA,GAAA4D,CAAA;QAAA5D,cAAA,GAAA0B,CAAA;QACZ,IAAI,IAAI,CAACqD,WAAW,EAAEG,yBAAyB,EAAE;UAAAlF,cAAA,GAAA4D,CAAA;UAAA5D,cAAA,GAAA0B,CAAA;UAC/C0E,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtB,WAAW,CAACG,yBAAyB,EAAE,QAAQ,CAAC;QACnE,CAAC;UAAAlF,cAAA,GAAA4D,CAAA;QAAA;QAAA5D,cAAA,GAAA0B,CAAA;QACD;IACJ;EACF;EAEQoE,SAASA,CAACH,IAAU;IAAA3F,cAAA,GAAAkC,CAAA;IAAAlC,cAAA,GAAA0B,CAAA;IAC1B,OAAOiE,IAAI,CAACW,IAAI,KAAK,iBAAiB;EACxC;EAEAC,UAAUA,CAACC,EAAU;IAAAxG,cAAA,GAAAkC,CAAA;IAAAlC,cAAA,GAAA0B,CAAA;IACnB,OAAO,CAAA1B,cAAA,GAAA4D,CAAA,eAAI,CAACd,OAAO,CAAC2D,IAAI,CAAElE,GAAG,IAAK;MAAAvC,cAAA,GAAAkC,CAAA;MAAAlC,cAAA,GAAA0B,CAAA;MAAA,OAAAa,GAAG,CAACiE,EAAE,KAAKA,EAAE;IAAF,CAAE,CAAC,EAAER,IAAI,MAAAhG,cAAA,GAAA4D,CAAA,WAAI,EAAE;EAC9D;EAEA8C,UAAUA,CAACF,EAAU;IAAAxG,cAAA,GAAAkC,CAAA;IAAAlC,cAAA,GAAA0B,CAAA;IACnB,OAAO,CAAA1B,cAAA,GAAA4D,CAAA,eAAI,CAACb,OAAO,CAAC0D,IAAI,CAAE/D,GAAG,IAAK;MAAA1C,cAAA,GAAAkC,CAAA;MAAAlC,cAAA,GAAA0B,CAAA;MAAA,OAAAgB,GAAG,CAAC8D,EAAE,KAAKA,EAAE;IAAF,CAAE,CAAC,EAAER,IAAI,MAAAhG,cAAA,GAAA4D,CAAA,WAAI,EAAE;EAC9D;EAEA+C,kBAAkBA,CAACH,EAAU;IAAAxG,cAAA,GAAAkC,CAAA;IAAAlC,cAAA,GAAA0B,CAAA;IAC3B,OAAO,CAAA1B,cAAA,GAAA4D,CAAA,eAAI,CAACZ,eAAe,CAACyD,IAAI,CAAEG,IAAI,IAAK;MAAA5G,cAAA,GAAAkC,CAAA;MAAAlC,cAAA,GAAA0B,CAAA;MAAA,OAAAkF,IAAI,CAACJ,EAAE,KAAKA,EAAE;IAAF,CAAE,CAAC,EAAER,IAAI,MAAAhG,cAAA,GAAA4D,CAAA,WAAI,EAAE;EACxE;;;;;;;;;;;;;;;;;;;;;;;cA1KCpD;MAAK;;cACLA;MAAK;;cACLC;MAAM;;;;;AAHIkB,2BAA2B,GAAAkF,UAAA,EAnBvCvG,SAAS,CAAC;EACTwG,QAAQ,EAAE,0BAA0B;EACpCC,QAAA,EAAAC,oBAAoD;EAEpDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPvG,mBAAmB,EACnBQ,OAAO,EACPF,YAAY,EACZC,QAAQ,EACRG,SAAS,EACTN,SAAS,EACTK,QAAQ,EACRJ,QAAQ,EACRH,SAAS,EACTC,aAAa,EACbQ,UAAU,CACX;;CACF,CAAC,C,EACWK,2BAA2B,CA4KvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}