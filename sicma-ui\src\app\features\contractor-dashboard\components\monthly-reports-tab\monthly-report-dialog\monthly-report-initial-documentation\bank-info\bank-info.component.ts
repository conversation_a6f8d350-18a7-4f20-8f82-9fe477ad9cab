import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { forkJoin } from 'rxjs';

import { <PERSON><PERSON><PERSON>on, MatIconButton } from '@angular/material/button';
import { MatOption } from '@angular/material/core';
import { Mat<PERSON><PERSON>r, MatFormField, MatLabel } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput } from '@angular/material/input';
import { MatSelect } from '@angular/material/select';
import { MatTooltip } from '@angular/material/tooltip';
import { BankAccountType } from '@contractor-dashboard/models/bank-account-type.model';
import { Bank } from '@contractor-dashboard/models/bank.model';
import { InitialReportDocumentation } from '@contractor-dashboard/models/initial-report-documentation.model';
import { BankAccountTypeService } from '@contractor-dashboard/services/bank-account-type.service';
import { BankService } from '@contractor-dashboard/services/bank.service';
import { AlertService } from '@shared/services/alert.service';
import {
  fileSizeValidator,
  pdfFileValidator,
} from '@shared/validators/file.validators';

@Component({
  selector: 'app-bank-info',
  templateUrl: './bank-info.component.html',
  styleUrl: './bank-info.component.scss',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatIcon,
    MatFormField,
    MatLabel,
    MatSelect,
    MatOption,
    MatInput,
    MatError,
    MatButton,
    MatIconButton,
    MatTooltip,
  ],
})
export class BankInfoComponent implements OnInit, OnChanges {
  @Input() initialData?: InitialReportDocumentation;
  @Input() isSupervisor = false;
  @Output() formChange = new EventEmitter<void>();

  form: FormGroup = this.fb.group({
    bank: ['', Validators.required],
    accountType: ['', Validators.required],
    accountNumber: ['', Validators.required],
    bankCertificateFile: [null, [fileSizeValidator(), pdfFileValidator()]],
  });

  bankList: Bank[] = [];
  accountTypes: BankAccountType[] = [];
  bankCertificateFile: File | null = null;
  bankCertificateFileName: string | null = null;

  constructor(
    private fb: FormBuilder,
    private bankService: BankService,
    private bankAccountTypeService: BankAccountTypeService,
    private alert: AlertService,
  ) {}

  ngOnInit(): void {
    forkJoin({
      banks: this.bankService.getAll(),
      accountTypes: this.bankAccountTypeService.getAll(),
    }).subscribe({
      next: ({ banks, accountTypes }) => {
        this.bankList = banks;
        this.accountTypes = accountTypes;
      },
      error: (error) => {
        this.alert.error(error.error?.detail ?? 'Error al cargar los datos del formulario');
      },
    });

    this.form.valueChanges.subscribe(() => {
      this.formChange.emit();
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['initialData']?.currentValue) {
      const data = changes['initialData'].currentValue;
      this.form.patchValue({
        bank: data.bankId,
        accountNumber: data.accountNumber,
        accountType: data.bankAccountTypeId,
      });
    }
  }

  private isPdfFile(file: File): boolean {
    return file.type === 'application/pdf';
  }

  onFileSelected(event: Event): void {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (file) {
      if (this.isPdfFile(file)) {
        if (file.size <= 1024 * 1024) {
          // 1MB
          this.form.patchValue({ bankCertificateFile: file });
          this.bankCertificateFile = file;
          this.bankCertificateFileName = file.name;
        } else {
          this.alert.error('El archivo no debe superar 1MB');
        }
      } else {
        this.alert.error('Solo se permiten archivos PDF');
      }
      this.form.get('bankCertificateFile')?.updateValueAndValidity();
    }
  }

  downloadFile(): void {
    if (this.initialData?.bankCertificateFileUrl) {
      window.open(this.initialData.bankCertificateFileUrl, '_blank');
    }
  }

  get isValid(): boolean {
    const basicFieldsValid = Boolean(
      this.form.get('bank')?.valid &&
        this.form.get('accountType')?.valid &&
        this.form.get('accountNumber')?.valid,
    );

    const hasNewFile = Boolean(this.form.get('bankCertificateFile')?.value);
    const hasExistingFile = Boolean(this.initialData?.bankCertificateFileUrl);

    return basicFieldsValid && (hasNewFile || hasExistingFile);
  }

  getBankName(id: number): string {
    return this.bankList.find((bank) => bank.id === id)?.name || '';
  }

  getAccountTypeName(id: number): string {
    return this.accountTypes.find((type) => type.id === id)?.name || '';
  }
}
