import { CommonModule, registerLocaleData } from '@angular/common';
import { Component, Inject, OnInit, LOCALE_ID } from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { PeriodService } from '@contractor-dashboard/services/period.service';
import { Period } from '@contractor-dashboard/models/Period.model';
import { AlertService } from '@shared/services/alert.service';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { NgxSpinnerService } from 'ngx-spinner';
import localeEs from '@angular/common/locales/es';
import { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';
import { finalize } from 'rxjs';

registerLocaleData(localeEs);

@Component({
  selector: 'app-select-period-dialog',
  templateUrl: './select-period-dialog.component.html',
  styleUrls: ['./select-period-dialog.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
  ],
})
export class SelectPeriodDialogComponent implements OnInit {
  periods: Period[] = [];
  selectedPeriod: Period | null = null;
  contractId: number;
  contractorContractId: number;
  isLoading = false;

  constructor(
    public dialogRef: MatDialogRef<SelectPeriodDialogComponent>,
    @Inject(MAT_DIALOG_DATA)
    public data: { contractId: number; contractorContractId: number },
    private readonly periodService: PeriodService,
    private readonly monthlyReportService: MonthlyReportService,
    private readonly alertService: AlertService,
    private readonly spinner: NgxSpinnerService,
    @Inject(LOCALE_ID) public locale: string,
  ) {
    this.contractId = data.contractId;
    this.contractorContractId = data.contractorContractId;
  }

  ngOnInit(): void {
    this.loadPeriods();
  }

  loadPeriods(): void {
    this.isLoading = true;
    this.spinner.show();

    this.periodService.getPeriodsByContractId(this.contractId).subscribe({
      next: (periods) => {
        this.periods = periods;
        this.isLoading = false;
        this.spinner.hide();
      },
      error: () => {
        this.isLoading = false;
        this.spinner.hide();
        this.alertService.error('Error al cargar los períodos.');
        this.dialogRef.close();
      },
    });
  }

  selectPeriod(period: Period): void {
    this.selectedPeriod = period;
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onCreate(): void {
    if (this.selectedPeriod) {
      this.spinner.show();
      this.monthlyReportService
        .createMax({
          contractorContractId: this.contractorContractId,
          reportNumber: this.selectedPeriod.num_payment,
          startDate: this.selectedPeriod.start_date,
          endDate: this.selectedPeriod.end_date,
          totalValue: this.selectedPeriod.payment,
          creationDate: new Date().toISOString().slice(0, 10),
          isFirstReport: true,
        })
        .pipe(finalize(() => this.spinner.hide()))
        .subscribe({
          next: () => {
            this.dialogRef.close(this.selectedPeriod);
          },
          error: () => {
            this.alertService.error('Error al crear los informes mensuales');
          },
        });
    } else {
      this.alertService.warning('Debe seleccionar un período.');
    }
  }
}
