{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/Github/SICMA/sicma-ui/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { AuthComponent } from './auth.component';\ndescribe('AuthComponent', () => {\n  let component;\n  let fixture;\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    yield TestBed.configureTestingModule({\n      imports: [AuthComponent]\n    }).compileComponents();\n    fixture = TestBed.createComponent(AuthComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  }));\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "AuthComponent", "describe", "component", "fixture", "beforeEach", "_asyncToGenerator", "configureTestingModule", "imports", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\core\\auth\\auth.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\n\nimport { AuthComponent } from './auth.component';\n\ndescribe('AuthComponent', () => {\n  let component: AuthComponent;\n  let fixture: ComponentFixture<AuthComponent>;\n\n  beforeEach(async () => {\n    await TestBed.configureTestingModule({\n      imports: [AuthComponent],\n    }).compileComponents();\n\n    fixture = TestBed.createComponent(AuthComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});"], "mappings": ";AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AAEjE,SAASC,aAAa,QAAQ,kBAAkB;AAEhDC,QAAQ,CAAC,eAAe,EAAE,MAAK;EAC7B,IAAIC,SAAwB;EAC5B,IAAIC,OAAwC;EAE5CC,UAAU,eAAAC,iBAAA,CAAC,aAAW;IACpB,MAAMN,OAAO,CAACO,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CAACP,aAAa;KACxB,CAAC,CAACQ,iBAAiB,EAAE;IAEtBL,OAAO,GAAGJ,OAAO,CAACU,eAAe,CAACT,aAAa,CAAC;IAChDE,SAAS,GAAGC,OAAO,CAACO,iBAAiB;IACrCP,OAAO,CAACQ,aAAa,EAAE;EACzB,CAAC,EAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACX,SAAS,CAAC,CAACY,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}