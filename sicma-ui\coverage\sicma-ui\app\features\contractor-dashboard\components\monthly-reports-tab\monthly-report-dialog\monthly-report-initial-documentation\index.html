
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for app/features/contractor-dashboard/components/monthly-reports-tab/monthly-report-dialog/monthly-report-initial-documentation</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../../../../index.html">All files</a> app/features/contractor-dashboard/components/monthly-reports-tab/monthly-report-dialog/monthly-report-initial-documentation</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">80.35% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>45/56</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">35% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>7/20</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">92.85% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>13/14</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">81.81% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>45/55</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="monthly-report-initial-documentation.component.ts"><a href="monthly-report-initial-documentation.component.ts.html">monthly-report-initial-documentation.component.ts</a></td>
	<td data-value="80.35" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 80%"></div><div class="cover-empty" style="width: 20%"></div></div>
	</td>
	<td data-value="80.35" class="pct high">80.35%</td>
	<td data-value="56" class="abs high">45/56</td>
	<td data-value="35" class="pct low">35%</td>
	<td data-value="20" class="abs low">7/20</td>
	<td data-value="92.85" class="pct high">92.85%</td>
	<td data-value="14" class="abs high">13/14</td>
	<td data-value="81.81" class="pct high">81.81%</td>
	<td data-value="55" class="abs high">45/55</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-15T19:17:53.681Z
            </div>
        <script src="../../../../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../../../../sorter.js"></script>
        <script src="../../../../../../../block-navigation.js"></script>
    </body>
</html>
    