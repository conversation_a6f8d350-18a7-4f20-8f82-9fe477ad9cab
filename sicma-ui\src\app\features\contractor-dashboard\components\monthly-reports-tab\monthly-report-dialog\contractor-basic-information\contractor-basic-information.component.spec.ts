import { HttpClientTestingModule } from '@angular/common/http/testing';
import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatDialogRef } from '@angular/material/dialog';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { Contractor } from '@contractor-management/models/contractor.model';
import { EducationLevel } from '@contractor-management/models/education-level.model';
import { Eps } from '@contractor-management/models/eps.model';
import { ContractorService } from '@contractor-management/services/contractor.service';
import { EducationLevelService } from '@contractor-management/services/education-level.service';
import { EpsService } from '@contractor-management/services/eps.service';
import { GenderService } from '@contractor-management/services/gender.service';
import { ProfessionService } from '@contractor-management/services/profession.service';
import { Department } from '@shared/models/department.model';
import { IDType } from '@shared/models/id-type.model';
import { Municipality } from '@shared/models/municipality.model';
import { AlertService } from '@shared/services/alert.service';
import { DepartmentService } from '@shared/services/department.service';
import { IDTypeService } from '@shared/services/id-type.service';
import { MunicipalityService } from '@shared/services/municipality.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { of, throwError } from 'rxjs';
import { ContractorBasicInformationComponent } from './contractor-basic-information.component';

describe('ContractorBasicInformationComponent', () => {
  let component: ContractorBasicInformationComponent;
  let fixture: ComponentFixture<ContractorBasicInformationComponent>;
  let contractorService: jasmine.SpyObj<ContractorService>;
  let alertService: jasmine.SpyObj<AlertService>;
  let spinnerService: jasmine.SpyObj<NgxSpinnerService>;
  let municipalityService: jasmine.SpyObj<MunicipalityService>;
  let departmentService: jasmine.SpyObj<DepartmentService>;
  let educationLevelService: jasmine.SpyObj<EducationLevelService>;
  let epsService: jasmine.SpyObj<EpsService>;
  let idTypeService: jasmine.SpyObj<IDTypeService>;
  let genderService: jasmine.SpyObj<GenderService>;
  let professionService: jasmine.SpyObj<ProfessionService>;

  const mockDepartment: Department = {
    id: 1,
    name: 'Test Department',
  };

  const mockMunicipality: Municipality = {
    id: 1,
    name: 'Test Municipality',
    departmentId: 1,
    department: mockDepartment,
  };

  const mockEducationLevel: EducationLevel = {
    id: 1,
    name: 'Test Education Level',
  };

  const mockEps: Eps = {
    id: 1,
    name: 'Test EPS',
  };

  const mockIDType: IDType = {
    id: 1,
    name: 'Test ID Type',
  };

  const mockContractor: Contractor = {
    id: 1,
    fullName: 'John Doe',
    idType: mockIDType,
    idNumber: 123456789,
    idTypeId: 1,
    birthDate: '1990-01-01',
    departmentId: 1,
    department: mockDepartment,
    municipalityId: 1,
    municipality: mockMunicipality,
    phone: 1234567890,
    personalEmail: '<EMAIL>',
    corporateEmail: undefined,
    genderId: 1,
    professionId: 1,
    educationLevelId: 1,
    epsId: 1,
    lastObtainedDegree: undefined,
  };

  beforeEach(async () => {
    const contractorServiceSpy = jasmine.createSpyObj('ContractorService', [
      'update',
    ]);
    const alertServiceSpy = jasmine.createSpyObj('AlertService', [
      'success',
      'error',
      'warning',
    ]);
    const spinnerServiceSpy = jasmine.createSpyObj('NgxSpinnerService', [
      'show',
      'hide',
    ]);
    const municipalityServiceSpy = jasmine.createSpyObj('MunicipalityService', [
      'getAllByDepartmentId',
    ]);
    const departmentServiceSpy = jasmine.createSpyObj('DepartmentService', [
      'getAll',
    ]);
    const educationLevelServiceSpy = jasmine.createSpyObj(
      'EducationLevelService',
      ['getAll'],
    );
    const epsServiceSpy = jasmine.createSpyObj('EpsService', ['getAll']);
    const idTypeServiceSpy = jasmine.createSpyObj('IDTypeService', ['getAll']);
    const genderServiceSpy = jasmine.createSpyObj('GenderService', ['getAll']);
    const professionServiceSpy = jasmine.createSpyObj('ProfessionService', [
      'getAll',
    ]);

    await TestBed.configureTestingModule({
      imports: [
        ContractorBasicInformationComponent,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        ReactiveFormsModule,
      ],
      providers: [
        FormBuilder,
        { provide: ContractorService, useValue: contractorServiceSpy },
        { provide: AlertService, useValue: alertServiceSpy },
        { provide: NgxSpinnerService, useValue: spinnerServiceSpy },
        { provide: MunicipalityService, useValue: municipalityServiceSpy },
        { provide: DepartmentService, useValue: departmentServiceSpy },
        { provide: EducationLevelService, useValue: educationLevelServiceSpy },
        { provide: EpsService, useValue: epsServiceSpy },
        { provide: IDTypeService, useValue: idTypeServiceSpy },
        { provide: GenderService, useValue: genderServiceSpy },
        { provide: ProfessionService, useValue: professionServiceSpy },
        { provide: MatDialogRef, useValue: {} },
        provideNativeDateAdapter(),
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ContractorBasicInformationComponent);
    component = fixture.componentInstance;
    contractorService = TestBed.inject(
      ContractorService,
    ) as jasmine.SpyObj<ContractorService>;
    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;
    spinnerService = TestBed.inject(
      NgxSpinnerService,
    ) as jasmine.SpyObj<NgxSpinnerService>;
    municipalityService = TestBed.inject(
      MunicipalityService,
    ) as jasmine.SpyObj<MunicipalityService>;
    departmentService = TestBed.inject(
      DepartmentService,
    ) as jasmine.SpyObj<DepartmentService>;
    educationLevelService = TestBed.inject(
      EducationLevelService,
    ) as jasmine.SpyObj<EducationLevelService>;
    epsService = TestBed.inject(EpsService) as jasmine.SpyObj<EpsService>;
    idTypeService = TestBed.inject(
      IDTypeService,
    ) as jasmine.SpyObj<IDTypeService>;
    genderService = TestBed.inject(
      GenderService,
    ) as jasmine.SpyObj<GenderService>;
    professionService = TestBed.inject(
      ProfessionService,
    ) as jasmine.SpyObj<ProfessionService>;

    departmentService.getAll.and.returnValue(of([mockDepartment]));
    municipalityService.getAllByDepartmentId.and.returnValue(
      of([mockMunicipality]),
    );
    educationLevelService.getAll.and.returnValue(of([mockEducationLevel]));
    epsService.getAll.and.returnValue(of([mockEps]));
    idTypeService.getAll.and.returnValue(of([mockIDType]));
    genderService.getAll.and.returnValue(of([{ id: 1, name: 'Test Gender' }]));
    professionService.getAll.and.returnValue(
      of([{ id: 1, name: 'Test Profession' }]),
    );

    component.contractor = mockContractor;
    await component.ngOnInit();
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load form data when contractor is provided', () => {
    const formValue = component.contractorForm.value;
    expect(formValue.idNumber).toBe(String(mockContractor.idNumber));
    expect(formValue.phone).toBe(String(mockContractor.phone));
    expect(formValue.personalEmail).toBe(mockContractor.personalEmail);

    const expectedDate = new Date(mockContractor.birthDate + 'T00:00:00');
    expectedDate.setHours(0, 0, 0, 0);
    const actualDate = component.contractorForm.get('birthDate')?.value;
    expect(actualDate instanceof Date).toBe(true);
    if (actualDate instanceof Date) {
      actualDate.setHours(0, 0, 0, 0);
      expect(actualDate.toISOString()).toEqual(expectedDate.toISOString());
    }

    expect(formValue.departmentId).toBe(mockContractor.departmentId);
  });

  it('should update contractor successfully', fakeAsync(() => {
    contractorService.update.and.returnValue(of(mockContractor));
    component.contractorForm.patchValue({
      idNumber: '123456789',
      phone: '1234567890',
      personalEmail: '<EMAIL>',
      birthDate: new Date('1990-01-01'),
      departmentId: '1',
      municipalityId: '1',
      genderId: '1',
      professionId: '1',
      educationLevelId: '1',
      epsId: '1',
    });

    component.updateContractor();
    tick();

    expect(spinnerService.show).toHaveBeenCalled();
    expect(contractorService.update).toHaveBeenCalledWith(
      1,
      jasmine.any(Object),
    );
    expect(alertService.success).toHaveBeenCalledWith(
      'Los datos han sido actualizados correctamente.',
    );
    expect(spinnerService.hide).toHaveBeenCalled();
  }));

  it('should handle contractor update error', fakeAsync(() => {
    contractorService.update.and.returnValue(throwError(() => new Error()));
    component.contractorForm.patchValue({
      idNumber: '123456789',
      phone: '1234567890',
      personalEmail: '<EMAIL>',
      birthDate: new Date('1990-01-01'),
      departmentId: '1',
      municipalityId: '1',
      genderId: '1',
      professionId: '1',
      educationLevelId: '1',
      epsId: '1',
    });

    component.updateContractor();
    tick();

    expect(spinnerService.show).toHaveBeenCalled();
    expect(contractorService.update).toHaveBeenCalledWith(
      1,
      jasmine.any(Object),
    );
    expect(alertService.error).toHaveBeenCalledWith(
      'Error al actualizar los datos del contratista',
    );
    expect(spinnerService.hide).toHaveBeenCalled();
  }));

  it('should handle invalid form', () => {
    component.contractorForm.get('idNumber')?.setValue('');
    component.updateContractor();
    expect(alertService.warning).toHaveBeenCalledWith(
      'Por favor, complete todos los campos requeridos antes de actualizar.',
    );
  });

  it('should load municipalities when department changes', () => {
    component.contractorForm.get('departmentId')?.setValue(1);
    expect(municipalityService.getAllByDepartmentId).toHaveBeenCalledWith(1);
  });
});