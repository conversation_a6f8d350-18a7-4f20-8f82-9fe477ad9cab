import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { InsuredRisks } from '@contract-management/models/insured_risks.model';
import { environment } from '@env';
import { InsuredRisksService } from './insured_risks.service';

describe('InsuredRisksService', () => {
  let service: InsuredRisksService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/insured_risks`;

  const mockInsuredRisk: InsuredRisks = {
    id: 1,
    name: 'Test Insured Risk',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [InsuredRisksService],
    });
    service = TestBed.inject(InsuredRisksService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all insured risks', () => {
      const mockInsuredRisks = [mockInsuredRisk];

      service.getAll().subscribe((insuredRisks) => {
        expect(insuredRisks).toEqual(mockInsuredRisks);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockInsuredRisks);
    });
  });

  describe('getById', () => {
    it('should return an insured risk by id', () => {
      const id = 1;

      service.getById(id).subscribe((insuredRisk) => {
        expect(insuredRisk).toEqual(mockInsuredRisk);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockInsuredRisk);
    });
  });

  describe('create', () => {
    it('should create a new insured risk', () => {
      const newInsuredRisk: Omit<InsuredRisks, 'id'> = {
        name: 'New Insured Risk',
      };

      service.create(newInsuredRisk).subscribe((insuredRisk) => {
        expect(insuredRisk).toEqual(mockInsuredRisk);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newInsuredRisk);
      req.flush(mockInsuredRisk);
    });
  });

  describe('update', () => {
    it('should update an insured risk', () => {
      const id = 1;
      const updateData: Partial<InsuredRisks> = {
        name: 'Updated Insured Risk',
      };

      service.update(id, updateData).subscribe((insuredRisk) => {
        expect(insuredRisk).toEqual(mockInsuredRisk);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockInsuredRisk);
    });
  });

  describe('delete', () => {
    it('should delete an insured risk', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });

  describe('getByName', () => {
    it('should return an insured risk by name', () => {
      const name = 'Test Insured Risk';

      service.getByName(name).subscribe((insuredRisk) => {
        expect(insuredRisk).toEqual(mockInsuredRisk);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockInsuredRisk);
    });
  });
});