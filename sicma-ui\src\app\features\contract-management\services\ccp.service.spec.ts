import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { CCP } from '@contract-management/models/ccp.model';
import { environment } from '@env';
import { CcpService } from './ccp.service';
import { AuthService } from '@core/auth/services/auth.service';
import { ContractAuditHistoryService } from './contract-audit-history.service';
import { ContractAuditStatusService } from './contract-audit-status.service';
import { of } from 'rxjs';
import { User } from '@core/auth/models/user.model';
import { ContractAuditStatus } from '../models/contract-audit-status.model';
import { ContractAuditHistory } from '../models/contract-audit-history.model';

describe('CcpService', () => {
  let service: CcpService;
  let httpTestingController: HttpTestingController;
  let authServiceSpy: jasmine.SpyObj<AuthService>;
  let contractAuditHistoryServiceSpy: jasmine.SpyObj<ContractAuditHistoryService>;
  let contractAuditStatusServiceSpy: jasmine.SpyObj<ContractAuditStatusService>;
  const API_URL = `${environment.apiUrl}/ccps`;

  const mockCCP: CCP = {
    id: 1,
    contractId: 1,
    expenseObjectUseCcp: '123456',
    expenseObjectDescription: 'Descripción del objeto de gasto',
    value: 1000000,
  };

  const mockUser: User = {
    id: 1,
    username: 'testuser',
    profiles: [],
  };

  const mockAuditStatus: ContractAuditStatus = {
    id: 1,
    name: 'Creación de CCP',
    description: 'CCP creation status',
  };

  beforeEach(() => {
    authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);
    contractAuditHistoryServiceSpy = jasmine.createSpyObj(
      'ContractAuditHistoryService',
      ['create'],
    );
    contractAuditStatusServiceSpy = jasmine.createSpyObj(
      'ContractAuditStatusService',
      ['getByName'],
    );

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        CcpService,
        { provide: AuthService, useValue: authServiceSpy },
        {
          provide: ContractAuditHistoryService,
          useValue: contractAuditHistoryServiceSpy,
        },
        {
          provide: ContractAuditStatusService,
          useValue: contractAuditStatusServiceSpy,
        },
      ],
    });
    service = TestBed.inject(CcpService);
    httpTestingController = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all CCPs', () => {
      const mockCCPs: CCP[] = [mockCCP];

      service.getAll().subscribe((ccps) => {
        expect(ccps).toEqual(mockCCPs);
      });

      const req = httpTestingController.expectOne(API_URL);
      expect(req.request.method).toBe('GET');
      req.flush(mockCCPs);
    });
  });

  describe('getById', () => {
    it('should return a single CCP by id', () => {
      service.getById(1).subscribe((ccp) => {
        expect(ccp).toEqual(mockCCP);
      });

      const req = httpTestingController.expectOne(`${API_URL}/1`);
      expect(req.request.method).toBe('GET');
      req.flush(mockCCP);
    });
  });

  describe('getAllByContractId', () => {
    it('should return CCPs by contract id', () => {
      const contractId = 1;
      const mockCCPs: CCP[] = [mockCCP];

      service.getAllByContractId(contractId).subscribe((ccps) => {
        expect(ccps).toEqual(mockCCPs);
      });

      const req = httpTestingController.expectOne(
        `${API_URL}/contract/${contractId}`,
      );
      expect(req.request.method).toBe('GET');
      req.flush(mockCCPs);
    });
  });

  describe('create', () => {
    it('should create a new CCP with audit record when user is logged in', () => {
      const newCCP: Omit<CCP, 'id'> = {
        contractId: 1,
        expenseObjectUseCcp: '123456',
        expenseObjectDescription: 'Descripción del objeto de gasto',
        value: 1000000,
      };

      const mockResponse: CCP = {
        id: 1,
        ...newCCP,
      };

      authServiceSpy.getCurrentUser.and.returnValue(mockUser);
      contractAuditStatusServiceSpy.getByName.and.returnValue(
        of(mockAuditStatus),
      );

      const mockAuditHistory: ContractAuditHistory = {
        id: 1,
        contractId: 1,
        auditStatusId: 1,
        auditDate: new Date(),
        comment: 'Test comment',
        auditorId: 1,
      };

      contractAuditHistoryServiceSpy.create.and.returnValue(
        of(mockAuditHistory),
      );

      service.create(newCCP).subscribe((ccp) => {
        expect(ccp).toEqual(mockResponse);
        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();
        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith(
          'Creación de CCP',
        );
        expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();
      });

      const req = httpTestingController.expectOne(API_URL);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newCCP);
      req.flush(mockResponse);
    });

    it('should create a new CCP without audit record when user is not logged in', () => {
      const newCCP: Omit<CCP, 'id'> = {
        contractId: 1,
        expenseObjectUseCcp: '123456',
        expenseObjectDescription: 'Descripción del objeto de gasto',
        value: 1000000,
      };

      const mockResponse: CCP = {
        id: 1,
        ...newCCP,
      };

      authServiceSpy.getCurrentUser.and.returnValue(null);

      service.create(newCCP).subscribe((ccp) => {
        expect(ccp).toEqual(mockResponse);
        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();
        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();
        expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();
      });

      const req = httpTestingController.expectOne(API_URL);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newCCP);
      req.flush(mockResponse);
    });
  });

  describe('update', () => {
    it('should update an existing CCP with audit record when there are changes and user is logged in', () => {
      const id = 1;
      const originalCCP: CCP = {
        id: 1,
        contractId: 1,
        expenseObjectUseCcp: '123456',
        expenseObjectDescription: 'Descripción original',
        value: 1000000,
      };

      const updateCCP: Partial<CCP> = {
        contractId: 1,
        expenseObjectDescription: 'Descripción actualizada',
        value: 1500000,
      };

      const mockResponse: CCP = {
        ...originalCCP,
        ...updateCCP,
      };

      authServiceSpy.getCurrentUser.and.returnValue(mockUser);
      contractAuditStatusServiceSpy.getByName.and.returnValue(
        of(mockAuditStatus),
      );

      const mockAuditHistory: ContractAuditHistory = {
        id: 1,
        contractId: 1,
        auditStatusId: mockAuditStatus.id,
        auditDate: new Date(),
        comment: 'Test comment',
        auditorId: mockUser.id,
      };

      contractAuditHistoryServiceSpy.create.and.returnValue(
        of(mockAuditHistory),
      );

      let updateResult: CCP | undefined;
      service.update(id, updateCCP).subscribe((result) => {
        updateResult = result;
      });

      const getReq = httpTestingController.expectOne(`${API_URL}/${id}`);
      expect(getReq.request.method).toBe('GET');
      getReq.flush(originalCCP);

      const putReq = httpTestingController.expectOne(`${API_URL}/${id}`);
      expect(putReq.request.method).toBe('PUT');
      expect(putReq.request.body).toEqual(updateCCP);
      putReq.flush(mockResponse);

      expect(updateResult).toEqual(mockResponse);
      expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith(
        'Edición de CCP',
      );
      expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();
    });

    it('should update an existing CCP without audit record when there are no changes', () => {
      const id = 1;
      const originalCCP: CCP = {
        id: 1,
        contractId: 1,
        expenseObjectUseCcp: '123456',
        expenseObjectDescription: 'Descripción original',
        value: 1000000,
      };

      const updateCCP: Partial<CCP> = {
        contractId: 1,
        expenseObjectUseCcp: '123456',
        expenseObjectDescription: 'Descripción original',
        value: 1000000,
      };

      const mockResponse: CCP = { ...originalCCP };

      authServiceSpy.getCurrentUser.and.returnValue(mockUser);

      let done = false;
      let updateResult: CCP | undefined;

      service.update(id, updateCCP).subscribe({
        next: (result) => {
          updateResult = result;
          done = true;
        },
        error: (err) => {
          fail('Should not have errored: ' + err);
        },
      });

      const getReq = httpTestingController.expectOne(`${API_URL}/${id}`);
      expect(getReq.request.method).toBe('GET');
      getReq.flush(originalCCP);

      const putReq = httpTestingController.expectOne(`${API_URL}/${id}`);
      expect(putReq.request.method).toBe('PUT');
      expect(putReq.request.body).toEqual(updateCCP);
      putReq.flush(mockResponse);

      expect(done).toBeTrue();
      expect(updateResult).toEqual(mockResponse);
      expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();
      expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();
    });

    it('should update an existing CCP without audit record when user not logged in', () => {
      const id = 1;
      const originalCCP: CCP = {
        id: 1,
        contractId: 1,
        expenseObjectUseCcp: '123456',
        expenseObjectDescription: 'Original description',
        value: 1000000,
      };

      const updateCCP: Partial<CCP> = {
        contractId: 1,
        expenseObjectDescription: 'Updated description',
        value: 2000000,
      };

      const mockResponse: CCP = {
        ...originalCCP,
        ...updateCCP,
      };

      authServiceSpy.getCurrentUser.and.returnValue(null);

      let updateResult: CCP | undefined;
      service.update(id, updateCCP).subscribe((result) => {
        updateResult = result;
      });

      const getReq = httpTestingController.expectOne(`${API_URL}/${id}`);
      expect(getReq.request.method).toBe('GET');
      getReq.flush(originalCCP);

      const putReq = httpTestingController.expectOne(`${API_URL}/${id}`);
      expect(putReq.request.method).toBe('PUT');
      expect(putReq.request.body).toEqual(updateCCP);
      putReq.flush(mockResponse);

      expect(updateResult).toEqual(mockResponse);
      expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();
      expect(contractAuditHistoryServiceSpy.create).not.toHaveBeenCalled();
    });
  });

  describe('delete', () => {
    it('should delete a CCP', () => {
      const id = 1;

      service.delete(id).subscribe((response) => {
        expect(response).toBeNull();
      });

      const req = httpTestingController.expectOne(`${API_URL}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });
});