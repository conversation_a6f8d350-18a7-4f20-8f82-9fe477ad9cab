{"ast": null, "code": "function cov_2o4wmtzbzd() {\n  var path = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\shared\\\\services\\\\email-template.service.ts\";\n  var hash = \"c0d9499557d8966cdbac9a6752d7241faa324003\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\shared\\\\services\\\\email-template.service.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 5,\n          column: 27\n        },\n        end: {\n          line: 19,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 7,\n          column: 8\n        },\n        end: {\n          line: 7,\n          column: 25\n        }\n      },\n      \"2\": {\n        start: {\n          line: 8,\n          column: 8\n        },\n        end: {\n          line: 8,\n          column: 62\n        }\n      },\n      \"3\": {\n        start: {\n          line: 11,\n          column: 8\n        },\n        end: {\n          line: 11,\n          column: 63\n        }\n      },\n      \"4\": {\n        start: {\n          line: 14,\n          column: 8\n        },\n        end: {\n          line: 14,\n          column: 78\n        }\n      },\n      \"5\": {\n        start: {\n          line: 16,\n          column: 13\n        },\n        end: {\n          line: 18,\n          column: 6\n        }\n      },\n      \"6\": {\n        start: {\n          line: 16,\n          column: 41\n        },\n        end: {\n          line: 18,\n          column: 5\n        }\n      },\n      \"7\": {\n        start: {\n          line: 20,\n          column: 0\n        },\n        end: {\n          line: 24,\n          column: 25\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 6,\n            column: 4\n          },\n          end: {\n            line: 6,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 6,\n            column: 22\n          },\n          end: {\n            line: 9,\n            column: 5\n          }\n        },\n        line: 6\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 10,\n            column: 4\n          },\n          end: {\n            line: 10,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 10,\n            column: 36\n          },\n          end: {\n            line: 12,\n            column: 5\n          }\n        },\n        line: 10\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 13,\n            column: 4\n          },\n          end: {\n            line: 13,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 13,\n            column: 37\n          },\n          end: {\n            line: 15,\n            column: 5\n          }\n        },\n        line: 13\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 16,\n            column: 35\n          },\n          end: {\n            line: 16,\n            column: 36\n          }\n        },\n        loc: {\n          start: {\n            line: 16,\n            column: 41\n          },\n          end: {\n            line: 18,\n            column: 5\n          }\n        },\n        line: 16\n      }\n    },\n    branchMap: {},\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0\n    },\n    b: {},\n    inputSourceMap: {\n      version: 3,\n      file: \"email-template.service.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\shared\\\\services\\\\email-template.service.ts\"],\n      names: [],\n      mappings: \";AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAClD,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,WAAW,EAAE,MAAM,MAAM,CAAC;AAW5B,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IAG/B,YAA6B,IAAgB;QAAhB,SAAI,GAAJ,IAAI,CAAY;QAF5B,WAAM,GAAG,GAAG,WAAW,CAAC,MAAM,kBAAkB,CAAC;IAElB,CAAC;IAEjD,iBAAiB,CAAC,YAAoB;QACpC,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAgB,GAAG,IAAI,CAAC,MAAM,IAAI,YAAY,EAAE,CAAC,CAAC;IACxE,CAAC;IAED,SAAS,CACP,YAAoB,EACpB,OAAyB;QAEzB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CACnB,GAAG,IAAI,CAAC,MAAM,SAAS,YAAY,EAAE,EACrC,OAAO,CACR,CAAC;IACJ,CAAC;;;;;AAjBU,oBAAoB;IAHhC,UAAU,CAAC;QACV,UAAU,EAAE,MAAM;KACnB,CAAC;GACW,oBAAoB,CAkBhC\",\n      sourcesContent: [\"import { HttpClient } from '@angular/common/http';\\nimport { Injectable } from '@angular/core';\\nimport { environment } from '@env';\\nimport { Observable } from 'rxjs';\\n\\nimport {\\n  EmailTemplate,\\n  SendEmailRequest,\\n} from '@shared/models/email-template.model';\\n\\n@Injectable({\\n  providedIn: 'root',\\n})\\nexport class EmailTemplateService {\\n  private readonly apiUrl = `${environment.apiUrl}/email-templates`;\\n\\n  constructor(private readonly http: HttpClient) {}\\n\\n  getTemplateByName(templateName: string): Observable<EmailTemplate> {\\n    return this.http.get<EmailTemplate>(`${this.apiUrl}/${templateName}`);\\n  }\\n\\n  sendEmail(\\n    templateName: string,\\n    request: SendEmailRequest,\\n  ): Observable<{ message: string }> {\\n    return this.http.post<{ message: string }>(\\n      `${this.apiUrl}/send/${templateName}`,\\n      request,\\n    );\\n  }\\n}\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"c0d9499557d8966cdbac9a6752d7241faa324003\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_2o4wmtzbzd = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_2o4wmtzbzd();\nimport { __decorate } from \"tslib\";\nimport { HttpClient } from '@angular/common/http';\nimport { Injectable } from '@angular/core';\nimport { environment } from '@env';\ncov_2o4wmtzbzd().s[0]++;\nlet EmailTemplateService = class EmailTemplateService {\n  constructor(http) {\n    cov_2o4wmtzbzd().f[0]++;\n    cov_2o4wmtzbzd().s[1]++;\n    this.http = http;\n    cov_2o4wmtzbzd().s[2]++;\n    this.apiUrl = `${environment.apiUrl}/email-templates`;\n  }\n  getTemplateByName(templateName) {\n    cov_2o4wmtzbzd().f[1]++;\n    cov_2o4wmtzbzd().s[3]++;\n    return this.http.get(`${this.apiUrl}/${templateName}`);\n  }\n  sendEmail(templateName, request) {\n    cov_2o4wmtzbzd().f[2]++;\n    cov_2o4wmtzbzd().s[4]++;\n    return this.http.post(`${this.apiUrl}/send/${templateName}`, request);\n  }\n  static {\n    cov_2o4wmtzbzd().s[5]++;\n    this.ctorParameters = () => {\n      cov_2o4wmtzbzd().f[3]++;\n      cov_2o4wmtzbzd().s[6]++;\n      return [{\n        type: HttpClient\n      }];\n    };\n  }\n};\ncov_2o4wmtzbzd().s[7]++;\nEmailTemplateService = __decorate([Injectable({\n  providedIn: 'root'\n})], EmailTemplateService);\nexport { EmailTemplateService };", "map": {"version": 3, "names": ["HttpClient", "Injectable", "environment", "cov_2o4wmtzbzd", "s", "EmailTemplateService", "constructor", "http", "f", "apiUrl", "getTemplateByName", "templateName", "get", "sendEmail", "request", "post", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\shared\\services\\email-template.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\nimport { Injectable } from '@angular/core';\nimport { environment } from '@env';\nimport { Observable } from 'rxjs';\n\nimport {\n  EmailTemplate,\n  SendEmailRequest,\n} from '@shared/models/email-template.model';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class EmailTemplateService {\n  private readonly apiUrl = `${environment.apiUrl}/email-templates`;\n\n  constructor(private readonly http: HttpClient) {}\n\n  getTemplateByName(templateName: string): Observable<EmailTemplate> {\n    return this.http.get<EmailTemplate>(`${this.apiUrl}/${templateName}`);\n  }\n\n  sendEmail(\n    templateName: string,\n    request: SendEmailRequest,\n  ): Observable<{ message: string }> {\n    return this.http.post<{ message: string }>(\n      `${this.apiUrl}/send/${templateName}`,\n      request,\n    );\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,UAAU,QAAQ,sBAAsB;AACjD,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,WAAW,QAAQ,MAAM;AAACC,cAAA,GAAAC,CAAA;AAW5B,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAG/BC,YAA6BC,IAAgB;IAAAJ,cAAA,GAAAK,CAAA;IAAAL,cAAA,GAAAC,CAAA;IAAhB,KAAAG,IAAI,GAAJA,IAAI;IAAYJ,cAAA,GAAAC,CAAA;IAF5B,KAAAK,MAAM,GAAG,GAAGP,WAAW,CAACO,MAAM,kBAAkB;EAEjB;EAEhDC,iBAAiBA,CAACC,YAAoB;IAAAR,cAAA,GAAAK,CAAA;IAAAL,cAAA,GAAAC,CAAA;IACpC,OAAO,IAAI,CAACG,IAAI,CAACK,GAAG,CAAgB,GAAG,IAAI,CAACH,MAAM,IAAIE,YAAY,EAAE,CAAC;EACvE;EAEAE,SAASA,CACPF,YAAoB,EACpBG,OAAyB;IAAAX,cAAA,GAAAK,CAAA;IAAAL,cAAA,GAAAC,CAAA;IAEzB,OAAO,IAAI,CAACG,IAAI,CAACQ,IAAI,CACnB,GAAG,IAAI,CAACN,MAAM,SAASE,YAAY,EAAE,EACrCG,OAAO,CACR;EACH;;;;;;;;;;;;;AAjBWT,oBAAoB,GAAAW,UAAA,EAHhCf,UAAU,CAAC;EACVgB,UAAU,EAAE;CACb,CAAC,C,EACWZ,oBAAoB,CAkBhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}