import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { TaxRegime } from '@contractor-dashboard/models/tax-regime.model';
import { environment } from '@env';
import { TaxRegimeService } from './tax-regime.service';

describe('TaxRegimeService', () => {
  let service: TaxRegimeService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/tax-regimes`;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [TaxRegimeService],
    });
    service = TestBed.inject(TaxRegimeService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  const mockTaxRegime: TaxRegime = {
    id: 1,
    name: 'Simple',
  };

  describe('getAll', () => {
    it('should return all tax regimes', () => {
      const mockTaxRegimes = [mockTaxRegime];

      service.getAll().subscribe((taxRegimes) => {
        expect(taxRegimes).toEqual(mockTaxRegimes);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockTaxRegimes);
    });

    it('should handle error when getting all tax regimes', () => {
      service.getAll().subscribe({
        error: (error) => {
          expect(error.status).toBe(500);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Server error'), { status: 500 });
    });
  });

  describe('getById', () => {
    it('should return a tax regime by id', () => {
      service.getById(1).subscribe((taxRegime) => {
        expect(taxRegime).toEqual(mockTaxRegime);
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('GET');
      req.flush(mockTaxRegime);
    });

    it('should handle error when getting tax regime by id', () => {
      service.getById(999).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/999`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('create', () => {
    const newTaxRegime = {
      name: 'New Regime',
    };

    it('should create a new tax regime', () => {
      service.create(newTaxRegime).subscribe((taxRegime) => {
        expect(taxRegime).toEqual(mockTaxRegime);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newTaxRegime);
      req.flush(mockTaxRegime);
    });

    it('should handle error when creating tax regime', () => {
      service.create(newTaxRegime).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('update', () => {
    const updateData = {
      name: 'Updated Name',
    };

    it('should update a tax regime', () => {
      service.update(1, updateData).subscribe((taxRegime) => {
        expect(taxRegime).toEqual({ ...mockTaxRegime, ...updateData });
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush({ ...mockTaxRegime, ...updateData });
    });

    it('should handle error when updating tax regime', () => {
      service.update(1, updateData).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('delete', () => {
    it('should delete a tax regime', () => {
      service.delete(1).subscribe((response) => {
        expect(response).toBeNull();
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });

    it('should handle error when deleting tax regime', () => {
      service.delete(1).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('getByName', () => {
    it('should return a tax regime by name', () => {
      const name = 'Simple';

      service.getByName(name).subscribe((taxRegime) => {
        expect(taxRegime).toEqual(mockTaxRegime);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockTaxRegime);
    });

    it('should handle error when getting tax regime by name', () => {
      const name = 'NonExistent';

      service.getByName(name).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });
});