{"ast": null, "code": "function cov_1sm2ir9ycw() {\n  var path = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contract-management\\\\components\\\\contractor-detail-form\\\\contractor-detail-form.component.ts\";\n  var hash = \"f760f52b5346be88bed4f9f410b53cddf55c6c9f\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contract-management\\\\components\\\\contractor-detail-form\\\\contractor-detail-form.component.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 15,\n          column: 36\n        },\n        end: {\n          line: 142,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 17,\n          column: 8\n        },\n        end: {\n          line: 17,\n          column: 51\n        }\n      },\n      \"2\": {\n        start: {\n          line: 20,\n          column: 8\n        },\n        end: {\n          line: 20,\n          column: 51\n        }\n      },\n      \"3\": {\n        start: {\n          line: 23,\n          column: 8\n        },\n        end: {\n          line: 23,\n          column: 51\n        }\n      },\n      \"4\": {\n        start: {\n          line: 24,\n          column: 8\n        },\n        end: {\n          line: 24,\n          column: 39\n        }\n      },\n      \"5\": {\n        start: {\n          line: 25,\n          column: 8\n        },\n        end: {\n          line: 25,\n          column: 27\n        }\n      },\n      \"6\": {\n        start: {\n          line: 26,\n          column: 8\n        },\n        end: {\n          line: 26,\n          column: 40\n        }\n      },\n      \"7\": {\n        start: {\n          line: 27,\n          column: 8\n        },\n        end: {\n          line: 27,\n          column: 45\n        }\n      },\n      \"8\": {\n        start: {\n          line: 28,\n          column: 8\n        },\n        end: {\n          line: 28,\n          column: 31\n        }\n      },\n      \"9\": {\n        start: {\n          line: 29,\n          column: 8\n        },\n        end: {\n          line: 34,\n          column: 11\n        }\n      },\n      \"10\": {\n        start: {\n          line: 35,\n          column: 8\n        },\n        end: {\n          line: 42,\n          column: 12\n        }\n      },\n      \"11\": {\n        start: {\n          line: 36,\n          column: 32\n        },\n        end: {\n          line: 38,\n          column: 54\n        }\n      },\n      \"12\": {\n        start: {\n          line: 39,\n          column: 12\n        },\n        end: {\n          line: 41,\n          column: 137\n        }\n      },\n      \"13\": {\n        start: {\n          line: 41,\n          column: 43\n        },\n        end: {\n          line: 41,\n          column: 134\n        }\n      },\n      \"14\": {\n        start: {\n          line: 41,\n          column: 78\n        },\n        end: {\n          line: 41,\n          column: 133\n        }\n      },\n      \"15\": {\n        start: {\n          line: 43,\n          column: 8\n        },\n        end: {\n          line: 48,\n          column: 12\n        }\n      },\n      \"16\": {\n        start: {\n          line: 44,\n          column: 32\n        },\n        end: {\n          line: 44,\n          column: 101\n        }\n      },\n      \"17\": {\n        start: {\n          line: 45,\n          column: 12\n        },\n        end: {\n          line: 47,\n          column: 134\n        }\n      },\n      \"18\": {\n        start: {\n          line: 47,\n          column: 43\n        },\n        end: {\n          line: 47,\n          column: 131\n        }\n      },\n      \"19\": {\n        start: {\n          line: 47,\n          column: 78\n        },\n        end: {\n          line: 47,\n          column: 130\n        }\n      },\n      \"20\": {\n        start: {\n          line: 51,\n          column: 8\n        },\n        end: {\n          line: 54,\n          column: 9\n        }\n      },\n      \"21\": {\n        start: {\n          line: 52,\n          column: 12\n        },\n        end: {\n          line: 52,\n          column: 48\n        }\n      },\n      \"22\": {\n        start: {\n          line: 53,\n          column: 12\n        },\n        end: {\n          line: 53,\n          column: 44\n        }\n      },\n      \"23\": {\n        start: {\n          line: 57,\n          column: 8\n        },\n        end: {\n          line: 64,\n          column: 9\n        }\n      },\n      \"24\": {\n        start: {\n          line: 58,\n          column: 12\n        },\n        end: {\n          line: 58,\n          column: 43\n        }\n      },\n      \"25\": {\n        start: {\n          line: 59,\n          column: 12\n        },\n        end: {\n          line: 59,\n          column: 43\n        }\n      },\n      \"26\": {\n        start: {\n          line: 61,\n          column: 13\n        },\n        end: {\n          line: 64,\n          column: 9\n        }\n      },\n      \"27\": {\n        start: {\n          line: 62,\n          column: 12\n        },\n        end: {\n          line: 62,\n          column: 42\n        }\n      },\n      \"28\": {\n        start: {\n          line: 63,\n          column: 12\n        },\n        end: {\n          line: 63,\n          column: 42\n        }\n      },\n      \"29\": {\n        start: {\n          line: 65,\n          column: 8\n        },\n        end: {\n          line: 68,\n          column: 9\n        }\n      },\n      \"30\": {\n        start: {\n          line: 66,\n          column: 12\n        },\n        end: {\n          line: 66,\n          column: 48\n        }\n      },\n      \"31\": {\n        start: {\n          line: 67,\n          column: 12\n        },\n        end: {\n          line: 67,\n          column: 44\n        }\n      },\n      \"32\": {\n        start: {\n          line: 71,\n          column: 8\n        },\n        end: {\n          line: 92,\n          column: 9\n        }\n      },\n      \"33\": {\n        start: {\n          line: 72,\n          column: 12\n        },\n        end: {\n          line: 91,\n          column: 15\n        }\n      },\n      \"34\": {\n        start: {\n          line: 74,\n          column: 20\n        },\n        end: {\n          line: 86,\n          column: 21\n        }\n      },\n      \"35\": {\n        start: {\n          line: 75,\n          column: 24\n        },\n        end: {\n          line: 75,\n          column: 53\n        }\n      },\n      \"36\": {\n        start: {\n          line: 76,\n          column: 24\n        },\n        end: {\n          line: 81,\n          column: 27\n        }\n      },\n      \"37\": {\n        start: {\n          line: 82,\n          column: 24\n        },\n        end: {\n          line: 85,\n          column: 25\n        }\n      },\n      \"38\": {\n        start: {\n          line: 83,\n          column: 28\n        },\n        end: {\n          line: 83,\n          column: 59\n        }\n      },\n      \"39\": {\n        start: {\n          line: 84,\n          column: 28\n        },\n        end: {\n          line: 84,\n          column: 59\n        }\n      },\n      \"40\": {\n        start: {\n          line: 89,\n          column: 20\n        },\n        end: {\n          line: 89,\n          column: 105\n        }\n      },\n      \"41\": {\n        start: {\n          line: 95,\n          column: 35\n        },\n        end: {\n          line: 95,\n          column: 53\n        }\n      },\n      \"42\": {\n        start: {\n          line: 96,\n          column: 8\n        },\n        end: {\n          line: 101,\n          column: 11\n        }\n      },\n      \"43\": {\n        start: {\n          line: 102,\n          column: 8\n        },\n        end: {\n          line: 102,\n          column: 45\n        }\n      },\n      \"44\": {\n        start: {\n          line: 103,\n          column: 8\n        },\n        end: {\n          line: 106,\n          column: 9\n        }\n      },\n      \"45\": {\n        start: {\n          line: 104,\n          column: 12\n        },\n        end: {\n          line: 104,\n          column: 43\n        }\n      },\n      \"46\": {\n        start: {\n          line: 105,\n          column: 12\n        },\n        end: {\n          line: 105,\n          column: 43\n        }\n      },\n      \"47\": {\n        start: {\n          line: 109,\n          column: 8\n        },\n        end: {\n          line: 114,\n          column: 11\n        }\n      },\n      \"48\": {\n        start: {\n          line: 115,\n          column: 8\n        },\n        end: {\n          line: 115,\n          column: 31\n        }\n      },\n      \"49\": {\n        start: {\n          line: 116,\n          column: 8\n        },\n        end: {\n          line: 119,\n          column: 9\n        }\n      },\n      \"50\": {\n        start: {\n          line: 117,\n          column: 12\n        },\n        end: {\n          line: 117,\n          column: 42\n        }\n      },\n      \"51\": {\n        start: {\n          line: 118,\n          column: 12\n        },\n        end: {\n          line: 118,\n          column: 42\n        }\n      },\n      \"52\": {\n        start: {\n          line: 122,\n          column: 8\n        },\n        end: {\n          line: 122,\n          column: 41\n        }\n      },\n      \"53\": {\n        start: {\n          line: 125,\n          column: 8\n        },\n        end: {\n          line: 130,\n          column: 11\n        }\n      },\n      \"54\": {\n        start: {\n          line: 126,\n          column: 28\n        },\n        end: {\n          line: 126,\n          column: 56\n        }\n      },\n      \"55\": {\n        start: {\n          line: 127,\n          column: 12\n        },\n        end: {\n          line: 129,\n          column: 13\n        }\n      },\n      \"56\": {\n        start: {\n          line: 128,\n          column: 16\n        },\n        end: {\n          line: 128,\n          column: 40\n        }\n      },\n      \"57\": {\n        start: {\n          line: 131,\n          column: 8\n        },\n        end: {\n          line: 131,\n          column: 31\n        }\n      },\n      \"58\": {\n        start: {\n          line: 133,\n          column: 13\n        },\n        end: {\n          line: 137,\n          column: 6\n        }\n      },\n      \"59\": {\n        start: {\n          line: 133,\n          column: 41\n        },\n        end: {\n          line: 137,\n          column: 5\n        }\n      },\n      \"60\": {\n        start: {\n          line: 138,\n          column: 13\n        },\n        end: {\n          line: 141,\n          column: 6\n        }\n      },\n      \"61\": {\n        start: {\n          line: 143,\n          column: 0\n        },\n        end: {\n          line: 159,\n          column: 34\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 16,\n            column: 4\n          },\n          end: {\n            line: 16,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 16,\n            column: 26\n          },\n          end: {\n            line: 18,\n            column: 5\n          }\n        },\n        line: 16\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 19,\n            column: 4\n          },\n          end: {\n            line: 19,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 19,\n            column: 26\n          },\n          end: {\n            line: 21,\n            column: 5\n          }\n        },\n        line: 19\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 22,\n            column: 4\n          },\n          end: {\n            line: 22,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 22,\n            column: 55\n          },\n          end: {\n            line: 49,\n            column: 5\n          }\n        },\n        line: 22\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 35,\n            column: 109\n          },\n          end: {\n            line: 35,\n            column: 110\n          }\n        },\n        loc: {\n          start: {\n            line: 35,\n            column: 120\n          },\n          end: {\n            line: 42,\n            column: 9\n          }\n        },\n        line: 35\n      },\n      \"4\": {\n        name: \"(anonymous_4)\",\n        decl: {\n          start: {\n            line: 41,\n            column: 26\n          },\n          end: {\n            line: 41,\n            column: 27\n          }\n        },\n        loc: {\n          start: {\n            line: 41,\n            column: 43\n          },\n          end: {\n            line: 41,\n            column: 134\n          }\n        },\n        line: 41\n      },\n      \"5\": {\n        name: \"(anonymous_5)\",\n        decl: {\n          start: {\n            line: 41,\n            column: 62\n          },\n          end: {\n            line: 41,\n            column: 63\n          }\n        },\n        loc: {\n          start: {\n            line: 41,\n            column: 78\n          },\n          end: {\n            line: 41,\n            column: 133\n          }\n        },\n        line: 41\n      },\n      \"6\": {\n        name: \"(anonymous_6)\",\n        decl: {\n          start: {\n            line: 43,\n            column: 109\n          },\n          end: {\n            line: 43,\n            column: 110\n          }\n        },\n        loc: {\n          start: {\n            line: 43,\n            column: 120\n          },\n          end: {\n            line: 48,\n            column: 9\n          }\n        },\n        line: 43\n      },\n      \"7\": {\n        name: \"(anonymous_7)\",\n        decl: {\n          start: {\n            line: 47,\n            column: 26\n          },\n          end: {\n            line: 47,\n            column: 27\n          }\n        },\n        loc: {\n          start: {\n            line: 47,\n            column: 43\n          },\n          end: {\n            line: 47,\n            column: 131\n          }\n        },\n        line: 47\n      },\n      \"8\": {\n        name: \"(anonymous_8)\",\n        decl: {\n          start: {\n            line: 47,\n            column: 62\n          },\n          end: {\n            line: 47,\n            column: 63\n          }\n        },\n        loc: {\n          start: {\n            line: 47,\n            column: 78\n          },\n          end: {\n            line: 47,\n            column: 130\n          }\n        },\n        line: 47\n      },\n      \"9\": {\n        name: \"(anonymous_9)\",\n        decl: {\n          start: {\n            line: 50,\n            column: 4\n          },\n          end: {\n            line: 50,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 50,\n            column: 15\n          },\n          end: {\n            line: 55,\n            column: 5\n          }\n        },\n        line: 50\n      },\n      \"10\": {\n        name: \"(anonymous_10)\",\n        decl: {\n          start: {\n            line: 56,\n            column: 4\n          },\n          end: {\n            line: 56,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 56,\n            column: 25\n          },\n          end: {\n            line: 69,\n            column: 5\n          }\n        },\n        line: 56\n      },\n      \"11\": {\n        name: \"(anonymous_11)\",\n        decl: {\n          start: {\n            line: 70,\n            column: 4\n          },\n          end: {\n            line: 70,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 70,\n            column: 31\n          },\n          end: {\n            line: 93,\n            column: 5\n          }\n        },\n        line: 70\n      },\n      \"12\": {\n        name: \"(anonymous_12)\",\n        decl: {\n          start: {\n            line: 73,\n            column: 22\n          },\n          end: {\n            line: 73,\n            column: 23\n          }\n        },\n        loc: {\n          start: {\n            line: 73,\n            column: 38\n          },\n          end: {\n            line: 87,\n            column: 17\n          }\n        },\n        line: 73\n      },\n      \"13\": {\n        name: \"(anonymous_13)\",\n        decl: {\n          start: {\n            line: 88,\n            column: 23\n          },\n          end: {\n            line: 88,\n            column: 24\n          }\n        },\n        loc: {\n          start: {\n            line: 88,\n            column: 34\n          },\n          end: {\n            line: 90,\n            column: 17\n          }\n        },\n        line: 88\n      },\n      \"14\": {\n        name: \"(anonymous_14)\",\n        decl: {\n          start: {\n            line: 94,\n            column: 4\n          },\n          end: {\n            line: 94,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 94,\n            column: 32\n          },\n          end: {\n            line: 107,\n            column: 5\n          }\n        },\n        line: 94\n      },\n      \"15\": {\n        name: \"(anonymous_15)\",\n        decl: {\n          start: {\n            line: 108,\n            column: 4\n          },\n          end: {\n            line: 108,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 108,\n            column: 18\n          },\n          end: {\n            line: 120,\n            column: 5\n          }\n        },\n        line: 108\n      },\n      \"16\": {\n        name: \"(anonymous_16)\",\n        decl: {\n          start: {\n            line: 121,\n            column: 4\n          },\n          end: {\n            line: 121,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 121,\n            column: 14\n          },\n          end: {\n            line: 123,\n            column: 5\n          }\n        },\n        line: 121\n      },\n      \"17\": {\n        name: \"(anonymous_17)\",\n        decl: {\n          start: {\n            line: 124,\n            column: 4\n          },\n          end: {\n            line: 124,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 124,\n            column: 15\n          },\n          end: {\n            line: 132,\n            column: 5\n          }\n        },\n        line: 124\n      },\n      \"18\": {\n        name: \"(anonymous_18)\",\n        decl: {\n          start: {\n            line: 125,\n            column: 58\n          },\n          end: {\n            line: 125,\n            column: 59\n          }\n        },\n        loc: {\n          start: {\n            line: 125,\n            column: 65\n          },\n          end: {\n            line: 130,\n            column: 9\n          }\n        },\n        line: 125\n      },\n      \"19\": {\n        name: \"(anonymous_19)\",\n        decl: {\n          start: {\n            line: 133,\n            column: 35\n          },\n          end: {\n            line: 133,\n            column: 36\n          }\n        },\n        loc: {\n          start: {\n            line: 133,\n            column: 41\n          },\n          end: {\n            line: 137,\n            column: 5\n          }\n        },\n        line: 133\n      }\n    },\n    branchMap: {\n      \"0\": {\n        loc: {\n          start: {\n            line: 36,\n            column: 32\n          },\n          end: {\n            line: 38,\n            column: 54\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 37,\n            column: 18\n          },\n          end: {\n            line: 37,\n            column: 37\n          }\n        }, {\n          start: {\n            line: 38,\n            column: 18\n          },\n          end: {\n            line: 38,\n            column: 54\n          }\n        }],\n        line: 36\n      },\n      \"1\": {\n        loc: {\n          start: {\n            line: 38,\n            column: 18\n          },\n          end: {\n            line: 38,\n            column: 54\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 38,\n            column: 18\n          },\n          end: {\n            line: 38,\n            column: 48\n          }\n        }, {\n          start: {\n            line: 38,\n            column: 52\n          },\n          end: {\n            line: 38,\n            column: 54\n          }\n        }],\n        line: 38\n      },\n      \"2\": {\n        loc: {\n          start: {\n            line: 44,\n            column: 32\n          },\n          end: {\n            line: 44,\n            column: 101\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 44,\n            column: 60\n          },\n          end: {\n            line: 44,\n            column: 65\n          }\n        }, {\n          start: {\n            line: 44,\n            column: 68\n          },\n          end: {\n            line: 44,\n            column: 101\n          }\n        }],\n        line: 44\n      },\n      \"3\": {\n        loc: {\n          start: {\n            line: 44,\n            column: 68\n          },\n          end: {\n            line: 44,\n            column: 101\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 44,\n            column: 68\n          },\n          end: {\n            line: 44,\n            column: 95\n          }\n        }, {\n          start: {\n            line: 44,\n            column: 99\n          },\n          end: {\n            line: 44,\n            column: 101\n          }\n        }],\n        line: 44\n      },\n      \"4\": {\n        loc: {\n          start: {\n            line: 51,\n            column: 8\n          },\n          end: {\n            line: 54,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 51,\n            column: 8\n          },\n          end: {\n            line: 54,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 51\n      },\n      \"5\": {\n        loc: {\n          start: {\n            line: 57,\n            column: 8\n          },\n          end: {\n            line: 64,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 57,\n            column: 8\n          },\n          end: {\n            line: 64,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 61,\n            column: 13\n          },\n          end: {\n            line: 64,\n            column: 9\n          }\n        }],\n        line: 57\n      },\n      \"6\": {\n        loc: {\n          start: {\n            line: 61,\n            column: 13\n          },\n          end: {\n            line: 64,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 61,\n            column: 13\n          },\n          end: {\n            line: 64,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 61\n      },\n      \"7\": {\n        loc: {\n          start: {\n            line: 65,\n            column: 8\n          },\n          end: {\n            line: 68,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 65,\n            column: 8\n          },\n          end: {\n            line: 68,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 65\n      },\n      \"8\": {\n        loc: {\n          start: {\n            line: 65,\n            column: 12\n          },\n          end: {\n            line: 65,\n            column: 56\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 65,\n            column: 12\n          },\n          end: {\n            line: 65,\n            column: 35\n          }\n        }, {\n          start: {\n            line: 65,\n            column: 39\n          },\n          end: {\n            line: 65,\n            column: 56\n          }\n        }],\n        line: 65\n      },\n      \"9\": {\n        loc: {\n          start: {\n            line: 71,\n            column: 8\n          },\n          end: {\n            line: 92,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 71,\n            column: 8\n          },\n          end: {\n            line: 92,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 71\n      },\n      \"10\": {\n        loc: {\n          start: {\n            line: 74,\n            column: 20\n          },\n          end: {\n            line: 86,\n            column: 21\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 74,\n            column: 20\n          },\n          end: {\n            line: 86,\n            column: 21\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 74\n      },\n      \"11\": {\n        loc: {\n          start: {\n            line: 79,\n            column: 43\n          },\n          end: {\n            line: 79,\n            column: 73\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 79,\n            column: 43\n          },\n          end: {\n            line: 79,\n            column: 67\n          }\n        }, {\n          start: {\n            line: 79,\n            column: 71\n          },\n          end: {\n            line: 79,\n            column: 73\n          }\n        }],\n        line: 79\n      },\n      \"12\": {\n        loc: {\n          start: {\n            line: 80,\n            column: 44\n          },\n          end: {\n            line: 80,\n            column: 75\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 80,\n            column: 44\n          },\n          end: {\n            line: 80,\n            column: 69\n          }\n        }, {\n          start: {\n            line: 80,\n            column: 73\n          },\n          end: {\n            line: 80,\n            column: 75\n          }\n        }],\n        line: 80\n      },\n      \"13\": {\n        loc: {\n          start: {\n            line: 82,\n            column: 24\n          },\n          end: {\n            line: 85,\n            column: 25\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 82,\n            column: 24\n          },\n          end: {\n            line: 85,\n            column: 25\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 82\n      },\n      \"14\": {\n        loc: {\n          start: {\n            line: 89,\n            column: 37\n          },\n          end: {\n            line: 89,\n            column: 103\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 89,\n            column: 37\n          },\n          end: {\n            line: 89,\n            column: 56\n          }\n        }, {\n          start: {\n            line: 89,\n            column: 60\n          },\n          end: {\n            line: 89,\n            column: 103\n          }\n        }],\n        line: 89\n      },\n      \"15\": {\n        loc: {\n          start: {\n            line: 99,\n            column: 27\n          },\n          end: {\n            line: 99,\n            column: 65\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 99,\n            column: 27\n          },\n          end: {\n            line: 99,\n            column: 59\n          }\n        }, {\n          start: {\n            line: 99,\n            column: 63\n          },\n          end: {\n            line: 99,\n            column: 65\n          }\n        }],\n        line: 99\n      },\n      \"16\": {\n        loc: {\n          start: {\n            line: 100,\n            column: 28\n          },\n          end: {\n            line: 100,\n            column: 67\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 100,\n            column: 28\n          },\n          end: {\n            line: 100,\n            column: 61\n          }\n        }, {\n          start: {\n            line: 100,\n            column: 65\n          },\n          end: {\n            line: 100,\n            column: 67\n          }\n        }],\n        line: 100\n      },\n      \"17\": {\n        loc: {\n          start: {\n            line: 103,\n            column: 8\n          },\n          end: {\n            line: 106,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 103,\n            column: 8\n          },\n          end: {\n            line: 106,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 103\n      },\n      \"18\": {\n        loc: {\n          start: {\n            line: 116,\n            column: 8\n          },\n          end: {\n            line: 119,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 116,\n            column: 8\n          },\n          end: {\n            line: 119,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 116\n      },\n      \"19\": {\n        loc: {\n          start: {\n            line: 116,\n            column: 12\n          },\n          end: {\n            line: 116,\n            column: 69\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 116,\n            column: 12\n          },\n          end: {\n            line: 116,\n            column: 36\n          }\n        }, {\n          start: {\n            line: 116,\n            column: 40\n          },\n          end: {\n            line: 116,\n            column: 69\n          }\n        }],\n        line: 116\n      },\n      \"20\": {\n        loc: {\n          start: {\n            line: 127,\n            column: 12\n          },\n          end: {\n            line: 129,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 127,\n            column: 12\n          },\n          end: {\n            line: 129,\n            column: 13\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 127\n      }\n    },\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0,\n      \"36\": 0,\n      \"37\": 0,\n      \"38\": 0,\n      \"39\": 0,\n      \"40\": 0,\n      \"41\": 0,\n      \"42\": 0,\n      \"43\": 0,\n      \"44\": 0,\n      \"45\": 0,\n      \"46\": 0,\n      \"47\": 0,\n      \"48\": 0,\n      \"49\": 0,\n      \"50\": 0,\n      \"51\": 0,\n      \"52\": 0,\n      \"53\": 0,\n      \"54\": 0,\n      \"55\": 0,\n      \"56\": 0,\n      \"57\": 0,\n      \"58\": 0,\n      \"59\": 0,\n      \"60\": 0,\n      \"61\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0\n    },\n    b: {\n      \"0\": [0, 0],\n      \"1\": [0, 0],\n      \"2\": [0, 0],\n      \"3\": [0, 0],\n      \"4\": [0, 0],\n      \"5\": [0, 0],\n      \"6\": [0, 0],\n      \"7\": [0, 0],\n      \"8\": [0, 0],\n      \"9\": [0, 0],\n      \"10\": [0, 0],\n      \"11\": [0, 0],\n      \"12\": [0, 0],\n      \"13\": [0, 0],\n      \"14\": [0, 0],\n      \"15\": [0, 0],\n      \"16\": [0, 0],\n      \"17\": [0, 0],\n      \"18\": [0, 0],\n      \"19\": [0, 0],\n      \"20\": [0, 0]\n    },\n    inputSourceMap: {\n      version: 3,\n      file: \"contractor-detail-form.component.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contract-management\\\\components\\\\contractor-detail-form\\\\contractor-detail-form.component.ts\"],\n      names: [],\n      mappings: \";;;AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EACL,SAAS,EACT,KAAK,GAIN,MAAM,eAAe,CAAC;AACvB,OAAO,EACL,WAAW,EAEX,mBAAmB,EACnB,UAAU,GACX,MAAM,gBAAgB,CAAC;AACxB,OAAO,EACL,qBAAqB,GAEtB,MAAM,gCAAgC,CAAC;AACxC,OAAO,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAC3D,OAAO,EAAE,kBAAkB,EAAE,MAAM,8BAA8B,CAAC;AAClE,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AAEzD,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAG3D,OAAO,EAAE,iBAAiB,EAAE,MAAM,oDAAoD,CAAC;AACvF,OAAO,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAiBvD,IAAM,6BAA6B,GAAnC,MAAM,6BAA6B;IAexC,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,CAAgB,CAAC;IAC5D,CAAC;IAED,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,CAAgB,CAAC;IAC5D,CAAC;IA0CD,YACmB,iBAAoC,EACpC,WAAwB,EACxB,KAAmB;QAFnB,sBAAiB,GAAjB,iBAAiB,CAAmB;QACpC,gBAAW,GAAX,WAAW,CAAa;QACxB,UAAK,GAAL,KAAK,CAAc;QAjE7B,uBAAkB,GAAG,KAAK;QAG3B,4BAAuB,GAAG,KAAK,CAAC;QAExC,eAAU,GAAsB,IAAI,CAAC;QAErC,mBAAc,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;YACtC,QAAQ,EAAE,CAAC,EAAE,EAAE,UAAU,CAAC,QAAQ,CAAC;YACnC,QAAQ,EAAE,CAAC,EAAE,EAAE,UAAU,CAAC,QAAQ,CAAC;YACnC,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;YAC9C,cAAc,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;SAChD,CAAC,CAAC;QAUH,kCAA6B,GAC3B,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,IAAI,CACpC,SAAS,CAAC,EAAE,CAAC,EACb,SAAS,CAAC,CAAC,KAAK,EAAE,EAAE;YAClB,MAAM,WAAW,GACf,OAAO,KAAK,KAAK,QAAQ;gBACvB,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE;gBACrB,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;YAE3C,OAAO,IAAI,CAAC,iBAAiB;iBAC1B,MAAM,EAAE;iBACR,IAAI,CACH,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE,CAClB,WAAW,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,EAAE,CAChC,UAAU,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,CACxD,CACF,CACF,CAAC;QACN,CAAC,CAAC,CACH,CAAC;QAEJ,kCAA6B,GAC3B,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,IAAI,CACpC,SAAS,CAAC,EAAE,CAAC,EACb,SAAS,CAAC,CAAC,KAAK,EAAE,EAAE;YAClB,MAAM,WAAW,GACf,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;YAExE,OAAO,IAAI,CAAC,iBAAiB;iBAC1B,MAAM,EAAE;iBACR,IAAI,CACH,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE,CAClB,WAAW,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,EAAE,CAChC,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,CACrD,CACF,CACF,CAAC;QACN,CAAC,CAAC,CACH,CAAC;IAMD,CAAC;IAEJ,QAAQ;QACN,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;YACpC,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAClC,CAAC;IACH,CAAC;IAED,WAAW,CAAC,OAAsB;QAChC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;YAC/B,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;QACjC,CAAC;aAAM,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACzC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;YAC9B,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACjD,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;YACpC,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAClC,CAAC;IACH,CAAC;IAEO,wBAAwB;QAC9B,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC;gBAC1D,IAAI,EAAE,CAAC,UAAU,EAAE,EAAE;oBACnB,IAAI,UAAU,EAAE,CAAC;wBACf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;wBAC7B,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;4BAC7B,QAAQ,EAAE,UAAU,CAAC,QAAQ;4BAC7B,QAAQ,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE;4BACxC,aAAa,EAAE,UAAU,CAAC,aAAa,IAAI,EAAE;4BAC7C,cAAc,EAAE,UAAU,CAAC,cAAc,IAAI,EAAE;yBAChD,CAAC,CAAC;wBAEH,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;4BACjC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;4BAC/B,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;wBACjC,CAAC;oBACH,CAAC;gBACH,CAAC;gBACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;oBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,2CAA2C,CAAC,CAAC;gBACvF,CAAC;aACF,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,oBAAoB,CAAC,KAAmC;QACtD,MAAM,kBAAkB,GAAG,KAAK,CAAC,MAAM,CAAC,KAAmB,CAAC;QAC5D,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;YAC7B,QAAQ,EAAE,kBAAkB,CAAC,QAAQ;YACrC,QAAQ,EAAE,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,EAAE;YAChD,aAAa,EAAE,kBAAkB,CAAC,aAAa,IAAI,EAAE;YACrD,cAAc,EAAE,kBAAkB,CAAC,cAAc,IAAI,EAAE;SACxD,CAAC,CAAC;QACH,IAAI,CAAC,UAAU,GAAG,kBAAkB,CAAC;QAErC,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACjC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;YAC/B,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;QACjC,CAAC;IACH,CAAC;IAED,WAAW;QACT,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;YAC7B,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,EAAE;YACZ,aAAa,EAAE,EAAE;YACjB,cAAc,EAAE,EAAE;SACnB,CAAC,CAAC;QACH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC9D,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;YAC9B,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;QAChC,CAAC;IACH,CAAC;IAED,OAAO;QACL,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;IACnC,CAAC;IAED,QAAQ;QACN,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACtD,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC7C,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,CAAC,aAAa,EAAE,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;;;;;;;qCAhKA,KAAK;+BACL,KAAK;;;AAFK,6BAA6B;IAfzC,SAAS,CAAC;QACT,QAAQ,EAAE,4BAA4B;QACtC,8BAAsD;QAEtD,UAAU,EAAE,IAAI;QAChB,OAAO,EAAE;YACP,SAAS;YACT,mBAAmB;YACnB,kBAAkB;YAClB,cAAc;YACd,aAAa;YACb,eAAe;YACf,qBAAqB;SACtB;;KACF,CAAC;GACW,6BAA6B,CAkKzC\",\n      sourcesContent: [\"import { AsyncPipe } from '@angular/common';\\nimport {\\n  Component,\\n  Input,\\n  OnChanges,\\n  OnInit,\\n  SimpleChanges,\\n} from '@angular/core';\\nimport {\\n  FormBuilder,\\n  FormControl,\\n  ReactiveFormsModule,\\n  Validators,\\n} from '@angular/forms';\\nimport {\\n  MatAutocompleteModule,\\n  MatAutocompleteSelectedEvent,\\n} from '@angular/material/autocomplete';\\nimport { MatButtonModule } from '@angular/material/button';\\nimport { MatFormFieldModule } from '@angular/material/form-field';\\nimport { MatIconModule } from '@angular/material/icon';\\nimport { MatInputModule } from '@angular/material/input';\\nimport { Observable } from 'rxjs';\\nimport { map, startWith, switchMap } from 'rxjs/operators';\\n\\nimport { Contractor } from '@contractor-management/models/contractor.model';\\nimport { ContractorService } from '@contractor-management/services/contractor.service';\\nimport { AlertService } from '@shared/services/alert.service';\\n\\n@Component({\\n  selector: 'app-contractor-detail-form',\\n  templateUrl: './contractor-detail-form.component.html',\\n  styleUrl: './contractor-detail-form.component.scss',\\n  standalone: true,\\n  imports: [\\n    AsyncPipe,\\n    ReactiveFormsModule,\\n    MatFormFieldModule,\\n    MatInputModule,\\n    MatIconModule,\\n    MatButtonModule,\\n    MatAutocompleteModule,\\n  ],\\n})\\nexport class ContractorDetailFormComponent implements OnChanges, OnInit {\\n  @Input() isContractFinished = false;\\n  @Input() contractorId?: number;\\n\\n  private hasExplicitContractorId = false;\\n\\n  contractor: Contractor | null = null;\\n\\n  contractorForm = this.formBuilder.group({\\n    fullName: ['', Validators.required],\\n    idNumber: ['', Validators.required],\\n    personalEmail: [{ value: '', disabled: true }],\\n    corporateEmail: [{ value: '', disabled: true }],\\n  });\\n\\n  get fullNameControl(): FormControl {\\n    return this.contractorForm.get('fullName') as FormControl;\\n  }\\n\\n  get idNumberControl(): FormControl {\\n    return this.contractorForm.get('idNumber') as FormControl;\\n  }\\n\\n  filteredContractorsByFullName: Observable<Contractor[]> =\\n    this.fullNameControl.valueChanges.pipe(\\n      startWith(''),\\n      switchMap((value) => {\\n        const filterValue =\\n          typeof value === 'string'\\n            ? value.toLowerCase()\\n            : value?.fullName?.toLowerCase() || '';\\n\\n        return this.contractorService\\n          .getAll()\\n          .pipe(\\n            map((contractors) =>\\n              contractors.filter((contractor) =>\\n                contractor.fullName.toLowerCase().includes(filterValue),\\n              ),\\n            ),\\n          );\\n      }),\\n    );\\n\\n  filteredContractorsByIdNumber: Observable<Contractor[]> =\\n    this.idNumberControl.valueChanges.pipe(\\n      startWith(''),\\n      switchMap((value) => {\\n        const filterValue =\\n          typeof value === 'string' ? value : value?.idNumber?.toString() || '';\\n\\n        return this.contractorService\\n          .getAll()\\n          .pipe(\\n            map((contractors) =>\\n              contractors.filter((contractor) =>\\n                contractor.idNumber.toString().includes(filterValue),\\n              ),\\n            ),\\n          );\\n      }),\\n    );\\n\\n  constructor(\\n    private readonly contractorService: ContractorService,\\n    private readonly formBuilder: FormBuilder,\\n    private readonly alert: AlertService,\\n  ) {}\\n\\n  ngOnInit(): void {\\n    if (this.contractorId) {\\n      this.hasExplicitContractorId = true;\\n      this.loadContractorIfProvided();\\n    }\\n  }\\n\\n  ngOnChanges(changes: SimpleChanges): void {\\n    if (this.isContractFinished) {\\n      this.fullNameControl.disable();\\n      this.idNumberControl.disable();\\n    } else if (!this.hasExplicitContractorId) {\\n      this.fullNameControl.enable();\\n      this.idNumberControl.enable();\\n    }\\n\\n    if (changes['contractorId'] && this.contractorId) {\\n      this.hasExplicitContractorId = true;\\n      this.loadContractorIfProvided();\\n    }\\n  }\\n\\n  private loadContractorIfProvided(): void {\\n    if (this.contractorId) {\\n      this.contractorService.getById(this.contractorId).subscribe({\\n        next: (contractor) => {\\n          if (contractor) {\\n            this.contractor = contractor;\\n            this.contractorForm.patchValue({\\n              fullName: contractor.fullName,\\n              idNumber: contractor.idNumber.toString(),\\n              personalEmail: contractor.personalEmail || '',\\n              corporateEmail: contractor.corporateEmail || '',\\n            });\\n\\n            if (this.hasExplicitContractorId) {\\n              this.fullNameControl.disable();\\n              this.idNumberControl.disable();\\n            }\\n          }\\n        },\\n        error: (error) => {\\n          this.alert.error(error.error?.detail ?? 'Error al cargar los datos del contratista');\\n        },\\n      });\\n    }\\n  }\\n\\n  onContractorSelected(event: MatAutocompleteSelectedEvent): void {\\n    const selectedContractor = event.option.value as Contractor;\\n    this.contractorForm.patchValue({\\n      fullName: selectedContractor.fullName,\\n      idNumber: selectedContractor.idNumber.toString(),\\n      personalEmail: selectedContractor.personalEmail || '',\\n      corporateEmail: selectedContractor.corporateEmail || '',\\n    });\\n    this.contractor = selectedContractor;\\n\\n    if (this.hasExplicitContractorId) {\\n      this.fullNameControl.disable();\\n      this.idNumberControl.disable();\\n    }\\n  }\\n\\n  clearFields(): void {\\n    this.contractorForm.patchValue({\\n      fullName: '',\\n      idNumber: '',\\n      personalEmail: '',\\n      corporateEmail: '',\\n    });\\n    this.contractor = null;\\n\\n    if (!this.isContractFinished && !this.hasExplicitContractorId) {\\n      this.fullNameControl.enable();\\n      this.idNumberControl.enable();\\n    }\\n  }\\n\\n  isValid(): boolean {\\n    return this.contractorForm.valid;\\n  }\\n\\n  getValue(): Contractor | null {\\n    Object.keys(this.contractorForm.controls).forEach(key => {\\n      const control = this.contractorForm.get(key);\\n      if (control) {\\n        control.markAsTouched();\\n      }\\n    });\\n\\n    return this.contractor;\\n  }\\n}\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"f760f52b5346be88bed4f9f410b53cddf55c6c9f\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_1sm2ir9ycw = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_1sm2ir9ycw();\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./contractor-detail-form.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./contractor-detail-form.component.scss?ngResource\";\nimport { AsyncPipe } from '@angular/common';\nimport { Component, Input } from '@angular/core';\nimport { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { map, startWith, switchMap } from 'rxjs/operators';\nimport { ContractorService } from '@contractor-management/services/contractor.service';\nimport { AlertService } from '@shared/services/alert.service';\ncov_1sm2ir9ycw().s[0]++;\nlet ContractorDetailFormComponent = class ContractorDetailFormComponent {\n  get fullNameControl() {\n    cov_1sm2ir9ycw().f[0]++;\n    cov_1sm2ir9ycw().s[1]++;\n    return this.contractorForm.get('fullName');\n  }\n  get idNumberControl() {\n    cov_1sm2ir9ycw().f[1]++;\n    cov_1sm2ir9ycw().s[2]++;\n    return this.contractorForm.get('idNumber');\n  }\n  constructor(contractorService, formBuilder, alert) {\n    cov_1sm2ir9ycw().f[2]++;\n    cov_1sm2ir9ycw().s[3]++;\n    this.contractorService = contractorService;\n    cov_1sm2ir9ycw().s[4]++;\n    this.formBuilder = formBuilder;\n    cov_1sm2ir9ycw().s[5]++;\n    this.alert = alert;\n    cov_1sm2ir9ycw().s[6]++;\n    this.isContractFinished = false;\n    cov_1sm2ir9ycw().s[7]++;\n    this.hasExplicitContractorId = false;\n    cov_1sm2ir9ycw().s[8]++;\n    this.contractor = null;\n    cov_1sm2ir9ycw().s[9]++;\n    this.contractorForm = this.formBuilder.group({\n      fullName: ['', Validators.required],\n      idNumber: ['', Validators.required],\n      personalEmail: [{\n        value: '',\n        disabled: true\n      }],\n      corporateEmail: [{\n        value: '',\n        disabled: true\n      }]\n    });\n    cov_1sm2ir9ycw().s[10]++;\n    this.filteredContractorsByFullName = this.fullNameControl.valueChanges.pipe(startWith(''), switchMap(value => {\n      cov_1sm2ir9ycw().f[3]++;\n      const filterValue = (cov_1sm2ir9ycw().s[11]++, typeof value === 'string' ? (cov_1sm2ir9ycw().b[0][0]++, value.toLowerCase()) : (cov_1sm2ir9ycw().b[0][1]++, (cov_1sm2ir9ycw().b[1][0]++, value?.fullName?.toLowerCase()) || (cov_1sm2ir9ycw().b[1][1]++, '')));\n      cov_1sm2ir9ycw().s[12]++;\n      return this.contractorService.getAll().pipe(map(contractors => {\n        cov_1sm2ir9ycw().f[4]++;\n        cov_1sm2ir9ycw().s[13]++;\n        return contractors.filter(contractor => {\n          cov_1sm2ir9ycw().f[5]++;\n          cov_1sm2ir9ycw().s[14]++;\n          return contractor.fullName.toLowerCase().includes(filterValue);\n        });\n      }));\n    }));\n    cov_1sm2ir9ycw().s[15]++;\n    this.filteredContractorsByIdNumber = this.idNumberControl.valueChanges.pipe(startWith(''), switchMap(value => {\n      cov_1sm2ir9ycw().f[6]++;\n      const filterValue = (cov_1sm2ir9ycw().s[16]++, typeof value === 'string' ? (cov_1sm2ir9ycw().b[2][0]++, value) : (cov_1sm2ir9ycw().b[2][1]++, (cov_1sm2ir9ycw().b[3][0]++, value?.idNumber?.toString()) || (cov_1sm2ir9ycw().b[3][1]++, '')));\n      cov_1sm2ir9ycw().s[17]++;\n      return this.contractorService.getAll().pipe(map(contractors => {\n        cov_1sm2ir9ycw().f[7]++;\n        cov_1sm2ir9ycw().s[18]++;\n        return contractors.filter(contractor => {\n          cov_1sm2ir9ycw().f[8]++;\n          cov_1sm2ir9ycw().s[19]++;\n          return contractor.idNumber.toString().includes(filterValue);\n        });\n      }));\n    }));\n  }\n  ngOnInit() {\n    cov_1sm2ir9ycw().f[9]++;\n    cov_1sm2ir9ycw().s[20]++;\n    if (this.contractorId) {\n      cov_1sm2ir9ycw().b[4][0]++;\n      cov_1sm2ir9ycw().s[21]++;\n      this.hasExplicitContractorId = true;\n      cov_1sm2ir9ycw().s[22]++;\n      this.loadContractorIfProvided();\n    } else {\n      cov_1sm2ir9ycw().b[4][1]++;\n    }\n  }\n  ngOnChanges(changes) {\n    cov_1sm2ir9ycw().f[10]++;\n    cov_1sm2ir9ycw().s[23]++;\n    if (this.isContractFinished) {\n      cov_1sm2ir9ycw().b[5][0]++;\n      cov_1sm2ir9ycw().s[24]++;\n      this.fullNameControl.disable();\n      cov_1sm2ir9ycw().s[25]++;\n      this.idNumberControl.disable();\n    } else {\n      cov_1sm2ir9ycw().b[5][1]++;\n      cov_1sm2ir9ycw().s[26]++;\n      if (!this.hasExplicitContractorId) {\n        cov_1sm2ir9ycw().b[6][0]++;\n        cov_1sm2ir9ycw().s[27]++;\n        this.fullNameControl.enable();\n        cov_1sm2ir9ycw().s[28]++;\n        this.idNumberControl.enable();\n      } else {\n        cov_1sm2ir9ycw().b[6][1]++;\n      }\n    }\n    cov_1sm2ir9ycw().s[29]++;\n    if ((cov_1sm2ir9ycw().b[8][0]++, changes['contractorId']) && (cov_1sm2ir9ycw().b[8][1]++, this.contractorId)) {\n      cov_1sm2ir9ycw().b[7][0]++;\n      cov_1sm2ir9ycw().s[30]++;\n      this.hasExplicitContractorId = true;\n      cov_1sm2ir9ycw().s[31]++;\n      this.loadContractorIfProvided();\n    } else {\n      cov_1sm2ir9ycw().b[7][1]++;\n    }\n  }\n  loadContractorIfProvided() {\n    cov_1sm2ir9ycw().f[11]++;\n    cov_1sm2ir9ycw().s[32]++;\n    if (this.contractorId) {\n      cov_1sm2ir9ycw().b[9][0]++;\n      cov_1sm2ir9ycw().s[33]++;\n      this.contractorService.getById(this.contractorId).subscribe({\n        next: contractor => {\n          cov_1sm2ir9ycw().f[12]++;\n          cov_1sm2ir9ycw().s[34]++;\n          if (contractor) {\n            cov_1sm2ir9ycw().b[10][0]++;\n            cov_1sm2ir9ycw().s[35]++;\n            this.contractor = contractor;\n            cov_1sm2ir9ycw().s[36]++;\n            this.contractorForm.patchValue({\n              fullName: contractor.fullName,\n              idNumber: contractor.idNumber.toString(),\n              personalEmail: (cov_1sm2ir9ycw().b[11][0]++, contractor.personalEmail) || (cov_1sm2ir9ycw().b[11][1]++, ''),\n              corporateEmail: (cov_1sm2ir9ycw().b[12][0]++, contractor.corporateEmail) || (cov_1sm2ir9ycw().b[12][1]++, '')\n            });\n            cov_1sm2ir9ycw().s[37]++;\n            if (this.hasExplicitContractorId) {\n              cov_1sm2ir9ycw().b[13][0]++;\n              cov_1sm2ir9ycw().s[38]++;\n              this.fullNameControl.disable();\n              cov_1sm2ir9ycw().s[39]++;\n              this.idNumberControl.disable();\n            } else {\n              cov_1sm2ir9ycw().b[13][1]++;\n            }\n          } else {\n            cov_1sm2ir9ycw().b[10][1]++;\n          }\n        },\n        error: error => {\n          cov_1sm2ir9ycw().f[13]++;\n          cov_1sm2ir9ycw().s[40]++;\n          this.alert.error((cov_1sm2ir9ycw().b[14][0]++, error.error?.detail) ?? (cov_1sm2ir9ycw().b[14][1]++, 'Error al cargar los datos del contratista'));\n        }\n      });\n    } else {\n      cov_1sm2ir9ycw().b[9][1]++;\n    }\n  }\n  onContractorSelected(event) {\n    cov_1sm2ir9ycw().f[14]++;\n    const selectedContractor = (cov_1sm2ir9ycw().s[41]++, event.option.value);\n    cov_1sm2ir9ycw().s[42]++;\n    this.contractorForm.patchValue({\n      fullName: selectedContractor.fullName,\n      idNumber: selectedContractor.idNumber.toString(),\n      personalEmail: (cov_1sm2ir9ycw().b[15][0]++, selectedContractor.personalEmail) || (cov_1sm2ir9ycw().b[15][1]++, ''),\n      corporateEmail: (cov_1sm2ir9ycw().b[16][0]++, selectedContractor.corporateEmail) || (cov_1sm2ir9ycw().b[16][1]++, '')\n    });\n    cov_1sm2ir9ycw().s[43]++;\n    this.contractor = selectedContractor;\n    cov_1sm2ir9ycw().s[44]++;\n    if (this.hasExplicitContractorId) {\n      cov_1sm2ir9ycw().b[17][0]++;\n      cov_1sm2ir9ycw().s[45]++;\n      this.fullNameControl.disable();\n      cov_1sm2ir9ycw().s[46]++;\n      this.idNumberControl.disable();\n    } else {\n      cov_1sm2ir9ycw().b[17][1]++;\n    }\n  }\n  clearFields() {\n    cov_1sm2ir9ycw().f[15]++;\n    cov_1sm2ir9ycw().s[47]++;\n    this.contractorForm.patchValue({\n      fullName: '',\n      idNumber: '',\n      personalEmail: '',\n      corporateEmail: ''\n    });\n    cov_1sm2ir9ycw().s[48]++;\n    this.contractor = null;\n    cov_1sm2ir9ycw().s[49]++;\n    if ((cov_1sm2ir9ycw().b[19][0]++, !this.isContractFinished) && (cov_1sm2ir9ycw().b[19][1]++, !this.hasExplicitContractorId)) {\n      cov_1sm2ir9ycw().b[18][0]++;\n      cov_1sm2ir9ycw().s[50]++;\n      this.fullNameControl.enable();\n      cov_1sm2ir9ycw().s[51]++;\n      this.idNumberControl.enable();\n    } else {\n      cov_1sm2ir9ycw().b[18][1]++;\n    }\n  }\n  isValid() {\n    cov_1sm2ir9ycw().f[16]++;\n    cov_1sm2ir9ycw().s[52]++;\n    return this.contractorForm.valid;\n  }\n  getValue() {\n    cov_1sm2ir9ycw().f[17]++;\n    cov_1sm2ir9ycw().s[53]++;\n    Object.keys(this.contractorForm.controls).forEach(key => {\n      cov_1sm2ir9ycw().f[18]++;\n      const control = (cov_1sm2ir9ycw().s[54]++, this.contractorForm.get(key));\n      cov_1sm2ir9ycw().s[55]++;\n      if (control) {\n        cov_1sm2ir9ycw().b[20][0]++;\n        cov_1sm2ir9ycw().s[56]++;\n        control.markAsTouched();\n      } else {\n        cov_1sm2ir9ycw().b[20][1]++;\n      }\n    });\n    cov_1sm2ir9ycw().s[57]++;\n    return this.contractor;\n  }\n  static {\n    cov_1sm2ir9ycw().s[58]++;\n    this.ctorParameters = () => {\n      cov_1sm2ir9ycw().f[19]++;\n      cov_1sm2ir9ycw().s[59]++;\n      return [{\n        type: ContractorService\n      }, {\n        type: FormBuilder\n      }, {\n        type: AlertService\n      }];\n    };\n  }\n  static {\n    cov_1sm2ir9ycw().s[60]++;\n    this.propDecorators = {\n      isContractFinished: [{\n        type: Input\n      }],\n      contractorId: [{\n        type: Input\n      }]\n    };\n  }\n};\ncov_1sm2ir9ycw().s[61]++;\nContractorDetailFormComponent = __decorate([Component({\n  selector: 'app-contractor-detail-form',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: true,\n  imports: [AsyncPipe, ReactiveFormsModule, MatFormFieldModule, MatInputModule, MatIconModule, MatButtonModule, MatAutocompleteModule],\n  styles: [__NG_CLI_RESOURCE__1]\n})], ContractorDetailFormComponent);\nexport { ContractorDetailFormComponent };", "map": {"version": 3, "names": ["cov_1sm2ir9ycw", "actualCoverage", "AsyncPipe", "Component", "Input", "FormBuilder", "ReactiveFormsModule", "Validators", "MatAutocompleteModule", "MatButtonModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "map", "startWith", "switchMap", "ContractorService", "AlertService", "s", "ContractorDetailFormComponent", "fullNameControl", "f", "contractorForm", "get", "idNumberControl", "constructor", "contractorService", "formBuilder", "alert", "isContractFinished", "hasExplicitContractorId", "contractor", "group", "fullName", "required", "idNumber", "personalEmail", "value", "disabled", "corporateEmail", "filteredContractorsByFullName", "valueChanges", "pipe", "filterValue", "b", "toLowerCase", "getAll", "contractors", "filter", "includes", "filteredContractorsByIdNumber", "toString", "ngOnInit", "contractorId", "loadContractorIfProvided", "ngOnChanges", "changes", "disable", "enable", "getById", "subscribe", "next", "patchValue", "error", "detail", "onContractorSelected", "event", "selectedContractor", "option", "clearFields", "<PERSON><PERSON><PERSON><PERSON>", "valid", "getValue", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\components\\contractor-detail-form\\contractor-detail-form.component.ts"], "sourcesContent": ["import { AsyncPipe } from '@angular/common';\nimport {\n  Component,\n  Input,\n  OnChanges,\n  OnInit,\n  SimpleChanges,\n} from '@angular/core';\nimport {\n  FormBuilder,\n  FormControl,\n  ReactiveFormsModule,\n  Validators,\n} from '@angular/forms';\nimport {\n  MatAutocompleteModule,\n  MatAutocompleteSelectedEvent,\n} from '@angular/material/autocomplete';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { Observable } from 'rxjs';\nimport { map, startWith, switchMap } from 'rxjs/operators';\n\nimport { Contractor } from '@contractor-management/models/contractor.model';\nimport { ContractorService } from '@contractor-management/services/contractor.service';\nimport { AlertService } from '@shared/services/alert.service';\n\n@Component({\n  selector: 'app-contractor-detail-form',\n  templateUrl: './contractor-detail-form.component.html',\n  styleUrl: './contractor-detail-form.component.scss',\n  standalone: true,\n  imports: [\n    AsyncPipe,\n    ReactiveFormsModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatIconModule,\n    MatButtonModule,\n    MatAutocompleteModule,\n  ],\n})\nexport class ContractorDetailFormComponent implements OnChanges, OnInit {\n  @Input() isContractFinished = false;\n  @Input() contractorId?: number;\n\n  private hasExplicitContractorId = false;\n\n  contractor: Contractor | null = null;\n\n  contractorForm = this.formBuilder.group({\n    fullName: ['', Validators.required],\n    idNumber: ['', Validators.required],\n    personalEmail: [{ value: '', disabled: true }],\n    corporateEmail: [{ value: '', disabled: true }],\n  });\n\n  get fullNameControl(): FormControl {\n    return this.contractorForm.get('fullName') as FormControl;\n  }\n\n  get idNumberControl(): FormControl {\n    return this.contractorForm.get('idNumber') as FormControl;\n  }\n\n  filteredContractorsByFullName: Observable<Contractor[]> =\n    this.fullNameControl.valueChanges.pipe(\n      startWith(''),\n      switchMap((value) => {\n        const filterValue =\n          typeof value === 'string'\n            ? value.toLowerCase()\n            : value?.fullName?.toLowerCase() || '';\n\n        return this.contractorService\n          .getAll()\n          .pipe(\n            map((contractors) =>\n              contractors.filter((contractor) =>\n                contractor.fullName.toLowerCase().includes(filterValue),\n              ),\n            ),\n          );\n      }),\n    );\n\n  filteredContractorsByIdNumber: Observable<Contractor[]> =\n    this.idNumberControl.valueChanges.pipe(\n      startWith(''),\n      switchMap((value) => {\n        const filterValue =\n          typeof value === 'string' ? value : value?.idNumber?.toString() || '';\n\n        return this.contractorService\n          .getAll()\n          .pipe(\n            map((contractors) =>\n              contractors.filter((contractor) =>\n                contractor.idNumber.toString().includes(filterValue),\n              ),\n            ),\n          );\n      }),\n    );\n\n  constructor(\n    private readonly contractorService: ContractorService,\n    private readonly formBuilder: FormBuilder,\n    private readonly alert: AlertService,\n  ) {}\n\n  ngOnInit(): void {\n    if (this.contractorId) {\n      this.hasExplicitContractorId = true;\n      this.loadContractorIfProvided();\n    }\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    if (this.isContractFinished) {\n      this.fullNameControl.disable();\n      this.idNumberControl.disable();\n    } else if (!this.hasExplicitContractorId) {\n      this.fullNameControl.enable();\n      this.idNumberControl.enable();\n    }\n\n    if (changes['contractorId'] && this.contractorId) {\n      this.hasExplicitContractorId = true;\n      this.loadContractorIfProvided();\n    }\n  }\n\n  private loadContractorIfProvided(): void {\n    if (this.contractorId) {\n      this.contractorService.getById(this.contractorId).subscribe({\n        next: (contractor) => {\n          if (contractor) {\n            this.contractor = contractor;\n            this.contractorForm.patchValue({\n              fullName: contractor.fullName,\n              idNumber: contractor.idNumber.toString(),\n              personalEmail: contractor.personalEmail || '',\n              corporateEmail: contractor.corporateEmail || '',\n            });\n\n            if (this.hasExplicitContractorId) {\n              this.fullNameControl.disable();\n              this.idNumberControl.disable();\n            }\n          }\n        },\n        error: (error) => {\n          this.alert.error(error.error?.detail ?? 'Error al cargar los datos del contratista');\n        },\n      });\n    }\n  }\n\n  onContractorSelected(event: MatAutocompleteSelectedEvent): void {\n    const selectedContractor = event.option.value as Contractor;\n    this.contractorForm.patchValue({\n      fullName: selectedContractor.fullName,\n      idNumber: selectedContractor.idNumber.toString(),\n      personalEmail: selectedContractor.personalEmail || '',\n      corporateEmail: selectedContractor.corporateEmail || '',\n    });\n    this.contractor = selectedContractor;\n\n    if (this.hasExplicitContractorId) {\n      this.fullNameControl.disable();\n      this.idNumberControl.disable();\n    }\n  }\n\n  clearFields(): void {\n    this.contractorForm.patchValue({\n      fullName: '',\n      idNumber: '',\n      personalEmail: '',\n      corporateEmail: '',\n    });\n    this.contractor = null;\n\n    if (!this.isContractFinished && !this.hasExplicitContractorId) {\n      this.fullNameControl.enable();\n      this.idNumberControl.enable();\n    }\n  }\n\n  isValid(): boolean {\n    return this.contractorForm.valid;\n  }\n\n  getValue(): Contractor | null {\n    Object.keys(this.contractorForm.controls).forEach(key => {\n      const control = this.contractorForm.get(key);\n      if (control) {\n        control.markAsTouched();\n      }\n    });\n\n    return this.contractor;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2DM;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;AA3DN,SAASE,SAAS,QAAQ,iBAAiB;AAC3C,SACEC,SAAS,EACTC,KAAK,QAIA,eAAe;AACtB,SACEC,WAAW,EAEXC,mBAAmB,EACnBC,UAAU,QACL,gBAAgB;AACvB,SACEC,qBAAqB,QAEhB,gCAAgC;AACvC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AAExD,SAASC,GAAG,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AAG1D,SAASC,iBAAiB,QAAQ,oDAAoD;AACtF,SAASC,YAAY,QAAQ,gCAAgC;AAACjB,cAAA,GAAAkB,CAAA;AAiBvD,IAAMC,6BAA6B,GAAnC,MAAMA,6BAA6B;EAexC,IAAIC,eAAeA,CAAA;IAAApB,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAkB,CAAA;IACjB,OAAO,IAAI,CAACI,cAAc,CAACC,GAAG,CAAC,UAAU,CAAgB;EAC3D;EAEA,IAAIC,eAAeA,CAAA;IAAAxB,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAkB,CAAA;IACjB,OAAO,IAAI,CAACI,cAAc,CAACC,GAAG,CAAC,UAAU,CAAgB;EAC3D;EA0CAE,YACmBC,iBAAoC,EACpCC,WAAwB,EACxBC,KAAmB;IAAA5B,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAkB,CAAA;IAFnB,KAAAQ,iBAAiB,GAAjBA,iBAAiB;IAAmB1B,cAAA,GAAAkB,CAAA;IACpC,KAAAS,WAAW,GAAXA,WAAW;IAAa3B,cAAA,GAAAkB,CAAA;IACxB,KAAAU,KAAK,GAALA,KAAK;IAAc5B,cAAA,GAAAkB,CAAA;IAjE7B,KAAAW,kBAAkB,GAAG,KAAK;IAAA7B,cAAA,GAAAkB,CAAA;IAG3B,KAAAY,uBAAuB,GAAG,KAAK;IAAC9B,cAAA,GAAAkB,CAAA;IAExC,KAAAa,UAAU,GAAsB,IAAI;IAAC/B,cAAA,GAAAkB,CAAA;IAErC,KAAAI,cAAc,GAAG,IAAI,CAACK,WAAW,CAACK,KAAK,CAAC;MACtCC,QAAQ,EAAE,CAAC,EAAE,EAAE1B,UAAU,CAAC2B,QAAQ,CAAC;MACnCC,QAAQ,EAAE,CAAC,EAAE,EAAE5B,UAAU,CAAC2B,QAAQ,CAAC;MACnCE,aAAa,EAAE,CAAC;QAAEC,KAAK,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MAC9CC,cAAc,EAAE,CAAC;QAAEF,KAAK,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;KAC/C,CAAC;IAACtC,cAAA,GAAAkB,CAAA;IAUH,KAAAsB,6BAA6B,GAC3B,IAAI,CAACpB,eAAe,CAACqB,YAAY,CAACC,IAAI,CACpC5B,SAAS,CAAC,EAAE,CAAC,EACbC,SAAS,CAAEsB,KAAK,IAAI;MAAArC,cAAA,GAAAqB,CAAA;MAClB,MAAMsB,WAAW,IAAA3C,cAAA,GAAAkB,CAAA,QACf,OAAOmB,KAAK,KAAK,QAAQ,IAAArC,cAAA,GAAA4C,CAAA,UACrBP,KAAK,CAACQ,WAAW,EAAE,KAAA7C,cAAA,GAAA4C,CAAA,UACnB,CAAA5C,cAAA,GAAA4C,CAAA,UAAAP,KAAK,EAAEJ,QAAQ,EAAEY,WAAW,EAAE,MAAA7C,cAAA,GAAA4C,CAAA,UAAI,EAAE;MAAC5C,cAAA,GAAAkB,CAAA;MAE3C,OAAO,IAAI,CAACQ,iBAAiB,CAC1BoB,MAAM,EAAE,CACRJ,IAAI,CACH7B,GAAG,CAAEkC,WAAW,IACd;QAAA/C,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAkB,CAAA;QAAA,OAAA6B,WAAW,CAACC,MAAM,CAAEjB,UAAU,IAC5B;UAAA/B,cAAA,GAAAqB,CAAA;UAAArB,cAAA,GAAAkB,CAAA;UAAA,OAAAa,UAAU,CAACE,QAAQ,CAACY,WAAW,EAAE,CAACI,QAAQ,CAACN,WAAW,CAAC;QAAD,CAAC,CACxD;MADwD,CACxD,CACF,CACF;IACL,CAAC,CAAC,CACH;IAAC3C,cAAA,GAAAkB,CAAA;IAEJ,KAAAgC,6BAA6B,GAC3B,IAAI,CAAC1B,eAAe,CAACiB,YAAY,CAACC,IAAI,CACpC5B,SAAS,CAAC,EAAE,CAAC,EACbC,SAAS,CAAEsB,KAAK,IAAI;MAAArC,cAAA,GAAAqB,CAAA;MAClB,MAAMsB,WAAW,IAAA3C,cAAA,GAAAkB,CAAA,QACf,OAAOmB,KAAK,KAAK,QAAQ,IAAArC,cAAA,GAAA4C,CAAA,UAAGP,KAAK,KAAArC,cAAA,GAAA4C,CAAA,UAAG,CAAA5C,cAAA,GAAA4C,CAAA,UAAAP,KAAK,EAAEF,QAAQ,EAAEgB,QAAQ,EAAE,MAAAnD,cAAA,GAAA4C,CAAA,UAAI,EAAE;MAAC5C,cAAA,GAAAkB,CAAA;MAExE,OAAO,IAAI,CAACQ,iBAAiB,CAC1BoB,MAAM,EAAE,CACRJ,IAAI,CACH7B,GAAG,CAAEkC,WAAW,IACd;QAAA/C,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAkB,CAAA;QAAA,OAAA6B,WAAW,CAACC,MAAM,CAAEjB,UAAU,IAC5B;UAAA/B,cAAA,GAAAqB,CAAA;UAAArB,cAAA,GAAAkB,CAAA;UAAA,OAAAa,UAAU,CAACI,QAAQ,CAACgB,QAAQ,EAAE,CAACF,QAAQ,CAACN,WAAW,CAAC;QAAD,CAAC,CACrD;MADqD,CACrD,CACF,CACF;IACL,CAAC,CAAC,CACH;EAMA;EAEHS,QAAQA,CAAA;IAAApD,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAkB,CAAA;IACN,IAAI,IAAI,CAACmC,YAAY,EAAE;MAAArD,cAAA,GAAA4C,CAAA;MAAA5C,cAAA,GAAAkB,CAAA;MACrB,IAAI,CAACY,uBAAuB,GAAG,IAAI;MAAC9B,cAAA,GAAAkB,CAAA;MACpC,IAAI,CAACoC,wBAAwB,EAAE;IACjC,CAAC;MAAAtD,cAAA,GAAA4C,CAAA;IAAA;EACH;EAEAW,WAAWA,CAACC,OAAsB;IAAAxD,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAkB,CAAA;IAChC,IAAI,IAAI,CAACW,kBAAkB,EAAE;MAAA7B,cAAA,GAAA4C,CAAA;MAAA5C,cAAA,GAAAkB,CAAA;MAC3B,IAAI,CAACE,eAAe,CAACqC,OAAO,EAAE;MAACzD,cAAA,GAAAkB,CAAA;MAC/B,IAAI,CAACM,eAAe,CAACiC,OAAO,EAAE;IAChC,CAAC,MAAM;MAAAzD,cAAA,GAAA4C,CAAA;MAAA5C,cAAA,GAAAkB,CAAA;MAAA,IAAI,CAAC,IAAI,CAACY,uBAAuB,EAAE;QAAA9B,cAAA,GAAA4C,CAAA;QAAA5C,cAAA,GAAAkB,CAAA;QACxC,IAAI,CAACE,eAAe,CAACsC,MAAM,EAAE;QAAC1D,cAAA,GAAAkB,CAAA;QAC9B,IAAI,CAACM,eAAe,CAACkC,MAAM,EAAE;MAC/B,CAAC;QAAA1D,cAAA,GAAA4C,CAAA;MAAA;IAAD;IAAC5C,cAAA,GAAAkB,CAAA;IAED,IAAI,CAAAlB,cAAA,GAAA4C,CAAA,UAAAY,OAAO,CAAC,cAAc,CAAC,MAAAxD,cAAA,GAAA4C,CAAA,UAAI,IAAI,CAACS,YAAY,GAAE;MAAArD,cAAA,GAAA4C,CAAA;MAAA5C,cAAA,GAAAkB,CAAA;MAChD,IAAI,CAACY,uBAAuB,GAAG,IAAI;MAAC9B,cAAA,GAAAkB,CAAA;MACpC,IAAI,CAACoC,wBAAwB,EAAE;IACjC,CAAC;MAAAtD,cAAA,GAAA4C,CAAA;IAAA;EACH;EAEQU,wBAAwBA,CAAA;IAAAtD,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAkB,CAAA;IAC9B,IAAI,IAAI,CAACmC,YAAY,EAAE;MAAArD,cAAA,GAAA4C,CAAA;MAAA5C,cAAA,GAAAkB,CAAA;MACrB,IAAI,CAACQ,iBAAiB,CAACiC,OAAO,CAAC,IAAI,CAACN,YAAY,CAAC,CAACO,SAAS,CAAC;QAC1DC,IAAI,EAAG9B,UAAU,IAAI;UAAA/B,cAAA,GAAAqB,CAAA;UAAArB,cAAA,GAAAkB,CAAA;UACnB,IAAIa,UAAU,EAAE;YAAA/B,cAAA,GAAA4C,CAAA;YAAA5C,cAAA,GAAAkB,CAAA;YACd,IAAI,CAACa,UAAU,GAAGA,UAAU;YAAC/B,cAAA,GAAAkB,CAAA;YAC7B,IAAI,CAACI,cAAc,CAACwC,UAAU,CAAC;cAC7B7B,QAAQ,EAAEF,UAAU,CAACE,QAAQ;cAC7BE,QAAQ,EAAEJ,UAAU,CAACI,QAAQ,CAACgB,QAAQ,EAAE;cACxCf,aAAa,EAAE,CAAApC,cAAA,GAAA4C,CAAA,WAAAb,UAAU,CAACK,aAAa,MAAApC,cAAA,GAAA4C,CAAA,WAAI,EAAE;cAC7CL,cAAc,EAAE,CAAAvC,cAAA,GAAA4C,CAAA,WAAAb,UAAU,CAACQ,cAAc,MAAAvC,cAAA,GAAA4C,CAAA,WAAI,EAAE;aAChD,CAAC;YAAC5C,cAAA,GAAAkB,CAAA;YAEH,IAAI,IAAI,CAACY,uBAAuB,EAAE;cAAA9B,cAAA,GAAA4C,CAAA;cAAA5C,cAAA,GAAAkB,CAAA;cAChC,IAAI,CAACE,eAAe,CAACqC,OAAO,EAAE;cAACzD,cAAA,GAAAkB,CAAA;cAC/B,IAAI,CAACM,eAAe,CAACiC,OAAO,EAAE;YAChC,CAAC;cAAAzD,cAAA,GAAA4C,CAAA;YAAA;UACH,CAAC;YAAA5C,cAAA,GAAA4C,CAAA;UAAA;QACH,CAAC;QACDmB,KAAK,EAAGA,KAAK,IAAI;UAAA/D,cAAA,GAAAqB,CAAA;UAAArB,cAAA,GAAAkB,CAAA;UACf,IAAI,CAACU,KAAK,CAACmC,KAAK,CAAC,CAAA/D,cAAA,GAAA4C,CAAA,WAAAmB,KAAK,CAACA,KAAK,EAAEC,MAAM,MAAAhE,cAAA,GAAA4C,CAAA,WAAI,2CAA2C,EAAC;QACtF;OACD,CAAC;IACJ,CAAC;MAAA5C,cAAA,GAAA4C,CAAA;IAAA;EACH;EAEAqB,oBAAoBA,CAACC,KAAmC;IAAAlE,cAAA,GAAAqB,CAAA;IACtD,MAAM8C,kBAAkB,IAAAnE,cAAA,GAAAkB,CAAA,QAAGgD,KAAK,CAACE,MAAM,CAAC/B,KAAmB;IAACrC,cAAA,GAAAkB,CAAA;IAC5D,IAAI,CAACI,cAAc,CAACwC,UAAU,CAAC;MAC7B7B,QAAQ,EAAEkC,kBAAkB,CAAClC,QAAQ;MACrCE,QAAQ,EAAEgC,kBAAkB,CAAChC,QAAQ,CAACgB,QAAQ,EAAE;MAChDf,aAAa,EAAE,CAAApC,cAAA,GAAA4C,CAAA,WAAAuB,kBAAkB,CAAC/B,aAAa,MAAApC,cAAA,GAAA4C,CAAA,WAAI,EAAE;MACrDL,cAAc,EAAE,CAAAvC,cAAA,GAAA4C,CAAA,WAAAuB,kBAAkB,CAAC5B,cAAc,MAAAvC,cAAA,GAAA4C,CAAA,WAAI,EAAE;KACxD,CAAC;IAAC5C,cAAA,GAAAkB,CAAA;IACH,IAAI,CAACa,UAAU,GAAGoC,kBAAkB;IAACnE,cAAA,GAAAkB,CAAA;IAErC,IAAI,IAAI,CAACY,uBAAuB,EAAE;MAAA9B,cAAA,GAAA4C,CAAA;MAAA5C,cAAA,GAAAkB,CAAA;MAChC,IAAI,CAACE,eAAe,CAACqC,OAAO,EAAE;MAACzD,cAAA,GAAAkB,CAAA;MAC/B,IAAI,CAACM,eAAe,CAACiC,OAAO,EAAE;IAChC,CAAC;MAAAzD,cAAA,GAAA4C,CAAA;IAAA;EACH;EAEAyB,WAAWA,CAAA;IAAArE,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAkB,CAAA;IACT,IAAI,CAACI,cAAc,CAACwC,UAAU,CAAC;MAC7B7B,QAAQ,EAAE,EAAE;MACZE,QAAQ,EAAE,EAAE;MACZC,aAAa,EAAE,EAAE;MACjBG,cAAc,EAAE;KACjB,CAAC;IAACvC,cAAA,GAAAkB,CAAA;IACH,IAAI,CAACa,UAAU,GAAG,IAAI;IAAC/B,cAAA,GAAAkB,CAAA;IAEvB,IAAI,CAAAlB,cAAA,GAAA4C,CAAA,YAAC,IAAI,CAACf,kBAAkB,MAAA7B,cAAA,GAAA4C,CAAA,WAAI,CAAC,IAAI,CAACd,uBAAuB,GAAE;MAAA9B,cAAA,GAAA4C,CAAA;MAAA5C,cAAA,GAAAkB,CAAA;MAC7D,IAAI,CAACE,eAAe,CAACsC,MAAM,EAAE;MAAC1D,cAAA,GAAAkB,CAAA;MAC9B,IAAI,CAACM,eAAe,CAACkC,MAAM,EAAE;IAC/B,CAAC;MAAA1D,cAAA,GAAA4C,CAAA;IAAA;EACH;EAEA0B,OAAOA,CAAA;IAAAtE,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAkB,CAAA;IACL,OAAO,IAAI,CAACI,cAAc,CAACiD,KAAK;EAClC;EAEAC,QAAQA,CAAA;IAAAxE,cAAA,GAAAqB,CAAA;IAAArB,cAAA,GAAAkB,CAAA;IACNuD,MAAM,CAACC,IAAI,CAAC,IAAI,CAACpD,cAAc,CAACqD,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAAA7E,cAAA,GAAAqB,CAAA;MACtD,MAAMyD,OAAO,IAAA9E,cAAA,GAAAkB,CAAA,QAAG,IAAI,CAACI,cAAc,CAACC,GAAG,CAACsD,GAAG,CAAC;MAAC7E,cAAA,GAAAkB,CAAA;MAC7C,IAAI4D,OAAO,EAAE;QAAA9E,cAAA,GAAA4C,CAAA;QAAA5C,cAAA,GAAAkB,CAAA;QACX4D,OAAO,CAACC,aAAa,EAAE;MACzB,CAAC;QAAA/E,cAAA,GAAA4C,CAAA;MAAA;IACH,CAAC,CAAC;IAAC5C,cAAA,GAAAkB,CAAA;IAEH,OAAO,IAAI,CAACa,UAAU;EACxB;;;;;;;;;;;;;;;;;;;cAhKC3B;MAAK;;cACLA;MAAK;;;;;AAFKe,6BAA6B,GAAA6D,UAAA,EAfzC7E,SAAS,CAAC;EACT8E,QAAQ,EAAE,4BAA4B;EACtCC,QAAA,EAAAC,oBAAsD;EAEtDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPnF,SAAS,EACTI,mBAAmB,EACnBI,kBAAkB,EAClBE,cAAc,EACdD,aAAa,EACbF,eAAe,EACfD,qBAAqB,CACtB;;CACF,CAAC,C,EACWW,6BAA6B,CAkKzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}