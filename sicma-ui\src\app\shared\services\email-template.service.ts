import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '@env';
import { Observable } from 'rxjs';

import {
  EmailTemplate,
  SendEmailRequest,
} from '@shared/models/email-template.model';

@Injectable({
  providedIn: 'root',
})
export class EmailTemplateService {
  private readonly apiUrl = `${environment.apiUrl}/email-templates`;

  constructor(private readonly http: HttpClient) {}

  getTemplateByName(templateName: string): Observable<EmailTemplate> {
    return this.http.get<EmailTemplate>(`${this.apiUrl}/${templateName}`);
  }

  sendEmail(
    templateName: string,
    request: SendEmailRequest,
  ): Observable<{ message: string }> {
    return this.http.post<{ message: string }>(
      `${this.apiUrl}/send/${templateName}`,
      request,
    );
  }
}
