import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Router } from '@angular/router';
import { Contract } from '@contract-management/models/contract.model';

import { ContractValuesService } from '@contract-management/services/contract-values.service';
import { ContractService } from '@contract-management/services/contract.service';
import { ContractorService } from '@contractor-management/services/contractor.service';
import { AlertService } from '@shared/services/alert.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { finalize, forkJoin } from 'rxjs';

import { CurrencyPipe, DatePipe } from '@angular/common';
import { AuthService } from '@core/auth/services/auth.service';
import { HttpErrorResponse } from '@angular/common/http';

interface ContractTableData {
  id: number;
  contractNumber: number;
  object: string;
  startDate?: Date;
  endDate?: Date;
  numericValue?: number;
  status: { name: string };
}

@Component({
  selector: 'app-contractor-contracts-list-page',
  templateUrl: './contractor-contracts-list-page.component.html',
  styleUrl: './contractor-contracts-list-page.component.scss',
  standalone: true,
  imports: [
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatCardModule,
    DatePipe,
    CurrencyPipe,
  ],
})
export class ContractorContractsListPageComponent
  implements OnInit, AfterViewInit
{
  displayedColumns: string[] = [
    'contractNumber',
    'startDate',
    'endDate',
    'numericValue',
    'status',
    'actions',
  ];
  dataSource: MatTableDataSource<ContractTableData>;
  contractorFullName = '';
  contractorIdNumber?: number;
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private contractService: ContractService,
    private authService: AuthService,
    private contractValuesService: ContractValuesService,
    private contractorService: ContractorService,
    private alert: AlertService,
    private spinner: NgxSpinnerService,
    private router: Router,
  ) {
    this.dataSource = new MatTableDataSource();
  }

  ngOnInit(): void {
    this.loadContractorContracts();
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  handleVisualizeContract(contract: ContractTableData): void {
    this.router.navigate(['/mis-contratos', contract.id]);
  }

  private loadContractorContracts(): void {
    this.spinner.show();
    const currentUser = this.authService.getCurrentUser();
    if (currentUser?.username) {
      this.contractorService.getByEmail(currentUser.username).subscribe({
        next: (contractor) => {
          const contractorIdNumber = contractor.idNumber;
          this.contractorService
            .getByIdNumber(contractorIdNumber)
            .pipe(finalize(() => this.spinner.hide()))
            .subscribe({
              next: (contractorDetails) => {
                this.contractorFullName = contractorDetails.fullName || '';
                this.loadContractsByContractorIdNumber(contractorIdNumber);
              },
              error: (error: HttpErrorResponse) => {
                this.alert.error(error.error?.detail ?? 'Error al cargar los datos del contratista');
              },
            });
        },
        error: () => {
          this.spinner.hide();
          this.alert.info('El Contrastista no tiene contratos asignados');
        },
      });
    } else {
      this.spinner.hide();
      this.alert.error('Usuario no autenticado');
    }
  }

  private loadContractsByContractorIdNumber(contractorIdNumber: number): void {
    this.contractService.getByContractorIdNumber(contractorIdNumber).subscribe({
      next: (contracts) => {
        this.loadContractValues(contracts);
      },
      error: (error: HttpErrorResponse) => {
        this.alert.error(error.error?.detail ?? 'Error al cargar los contratos');
      },
    });
  }

  private loadContractValues(contracts: Contract[]): void {
    const observables = contracts.map((contract) =>
      contract.id !== undefined
        ? this.contractValuesService.getAllByContractId(contract.id)
        : [],
    );

    forkJoin(observables).subscribe({
      next: (contractValuesArray) => {
        this.dataSource.data = contracts.map((contract, index) => {
          const contractValues = contractValuesArray[index];
          const latestValues =
            contractValues.length > 0
              ? contractValues[contractValues.length - 1]
              : null;
          return {
            id: contract.id ?? 0,
            contractNumber: contract.contractNumber,
            object: contract.object,
            startDate: latestValues?.startDate
              ? new Date(latestValues.startDate)
              : undefined,
            endDate: latestValues?.endDate
              ? new Date(latestValues.endDate)
              : undefined,
            numericValue: latestValues?.numericValue,
            status: contract.status ?? { name: 'Unknown' },
          };
        });
      },
      error: (error: HttpErrorResponse) => {
        this.alert.error(error.error?.detail ?? 'Error al cargar los valores de los contratos');
      },
    });
  }
}
