import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { ConsultPeriod } from '@contractor-dashboard/models/ConsultPeriod.model';
import { Period } from '@contractor-dashboard/models/Period.model';
import { environment } from '@env';
import { PeriodService } from './period.service';

describe('PeriodService', () => {
  let service: PeriodService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/periods`;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [PeriodService],
    });
    service = TestBed.inject(PeriodService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  const mockPeriod: Period = {
    num_payment: 1,
    payment: 1000000,
    start_date: '2024-02-01',
    end_date: '2024-02-29',
    days_in_month: 29,
  };

  describe('getCantPeriodsById', () => {
    it('should return the count of periods by id', () => {
      const mockCount = { count: 12 };

      service.getCantPeriodsById(1).subscribe((result) => {
        expect(result).toEqual(mockCount);
      });

      const req = httpMock.expectOne(`${apiUrl}cantidad/1`);
      expect(req.request.method).toBe('GET');
      req.flush(mockCount);
    });

    it('should handle error when getting count of periods', () => {
      service.getCantPeriodsById(999).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}cantidad/999`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('create', () => {
    const newPeriod: Omit<ConsultPeriod, 'id'> = {
      id_contrat: 1,
      num_payment: 1,
    };

    it('should create a new period', () => {
      service.create(newPeriod).subscribe((period) => {
        expect(period).toEqual(mockPeriod);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newPeriod);
      req.flush(mockPeriod);
    });

    it('should handle error when creating period', () => {
      service.create(newPeriod).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });
});