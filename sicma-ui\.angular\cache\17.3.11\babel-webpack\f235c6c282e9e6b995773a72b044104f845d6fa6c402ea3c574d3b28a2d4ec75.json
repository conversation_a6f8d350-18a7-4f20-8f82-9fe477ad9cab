{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { CdpEntityService } from './cdp-entity.service';\ndescribe('CdpEntityService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/cdp-entities`;\n  const mockCdpEntity = {\n    id: 1,\n    name: 'Test CDP Entity'\n  };\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [CdpEntityService]\n    });\n    service = TestBed.inject(CdpEntityService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all cdp entities', () => {\n      const mockCdpEntities = [mockCdpEntity];\n      service.getAll().subscribe(cdpEntities => {\n        expect(cdpEntities).toEqual(mockCdpEntities);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockCdpEntities);\n    });\n  });\n  describe('getById', () => {\n    it('should return a cdp entity by id', () => {\n      const id = 1;\n      service.getById(id).subscribe(cdpEntity => {\n        expect(cdpEntity).toEqual(mockCdpEntity);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockCdpEntity);\n    });\n  });\n  describe('create', () => {\n    it('should create a new cdp entity', () => {\n      const newCdpEntity = {\n        name: 'New CDP Entity'\n      };\n      service.create(newCdpEntity).subscribe(cdpEntity => {\n        expect(cdpEntity).toEqual(mockCdpEntity);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newCdpEntity);\n      req.flush(mockCdpEntity);\n    });\n  });\n  describe('update', () => {\n    it('should update a cdp entity', () => {\n      const id = 1;\n      const updateData = {\n        name: 'Updated CDP Entity'\n      };\n      service.update(id, updateData).subscribe(cdpEntity => {\n        expect(cdpEntity).toEqual(mockCdpEntity);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockCdpEntity);\n    });\n  });\n  describe('delete', () => {\n    it('should delete a cdp entity', () => {\n      const id = 1;\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n  describe('getByName', () => {\n    it('should return a cdp entity by name', () => {\n      const name = 'Test CDP Entity';\n      service.getByName(name).subscribe(cdpEntity => {\n        expect(cdpEntity).toEqual(mockCdpEntity);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockCdpEntity);\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "CdpEntityService", "describe", "service", "httpMock", "apiUrl", "mockCdpEntity", "id", "name", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockCdpEntities", "getAll", "subscribe", "cdpEntities", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "getById", "cdpEntity", "newCdpEntity", "create", "body", "updateData", "update", "delete", "nothing", "getByName"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\services\\cdp-entity.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { CDPEntity } from '@contract-management/models/cdp-entity.model';\nimport { environment } from '@env';\nimport { CdpEntityService } from './cdp-entity.service';\n\ndescribe('CdpEntityService', () => {\n  let service: CdpEntityService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/cdp-entities`;\n\n  const mockCdpEntity: CDPEntity = {\n    id: 1,\n    name: 'Test CDP Entity',\n  };\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [CdpEntityService],\n    });\n    service = TestBed.inject(CdpEntityService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all cdp entities', () => {\n      const mockCdpEntities = [mockCdpEntity];\n\n      service.getAll().subscribe((cdpEntities) => {\n        expect(cdpEntities).toEqual(mockCdpEntities);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockCdpEntities);\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a cdp entity by id', () => {\n      const id = 1;\n\n      service.getById(id).subscribe((cdpEntity) => {\n        expect(cdpEntity).toEqual(mockCdpEntity);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockCdpEntity);\n    });\n  });\n\n  describe('create', () => {\n    it('should create a new cdp entity', () => {\n      const newCdpEntity: Omit<CDPEntity, 'id'> = {\n        name: 'New CDP Entity',\n      };\n\n      service.create(newCdpEntity).subscribe((cdpEntity) => {\n        expect(cdpEntity).toEqual(mockCdpEntity);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newCdpEntity);\n      req.flush(mockCdpEntity);\n    });\n  });\n\n  describe('update', () => {\n    it('should update a cdp entity', () => {\n      const id = 1;\n      const updateData: Partial<CDPEntity> = {\n        name: 'Updated CDP Entity',\n      };\n\n      service.update(id, updateData).subscribe((cdpEntity) => {\n        expect(cdpEntity).toEqual(mockCdpEntity);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockCdpEntity);\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete a cdp entity', () => {\n      const id = 1;\n\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n\n  describe('getByName', () => {\n    it('should return a cdp entity by name', () => {\n      const name = 'Test CDP Entity';\n\n      service.getByName(name).subscribe((cdpEntity) => {\n        expect(cdpEntity).toEqual(mockCdpEntity);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockCdpEntity);\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,gBAAgB,QAAQ,sBAAsB;AAEvDC,QAAQ,CAAC,kBAAkB,EAAE,MAAK;EAChC,IAAIC,OAAyB;EAC7B,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,eAAe;EAEnD,MAAMC,aAAa,GAAc;IAC/BC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE;GACP;EAEDC,UAAU,CAAC,MAAK;IACdV,OAAO,CAACW,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACd,uBAAuB,CAAC;MAClCe,SAAS,EAAE,CAACX,gBAAgB;KAC7B,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACc,MAAM,CAACZ,gBAAgB,CAAC;IAC1CG,QAAQ,GAAGL,OAAO,CAACc,MAAM,CAACf,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFgB,SAAS,CAAC,MAAK;IACbV,QAAQ,CAACW,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACd,OAAO,CAAC,CAACe,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFhB,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,gCAAgC,EAAE,MAAK;MACxC,MAAMG,eAAe,GAAG,CAACb,aAAa,CAAC;MAEvCH,OAAO,CAACiB,MAAM,EAAE,CAACC,SAAS,CAAEC,WAAW,IAAI;QACzCL,MAAM,CAACK,WAAW,CAAC,CAACC,OAAO,CAACJ,eAAe,CAAC;MAC9C,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,eAAe,CAAC;IAC5B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjB,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBc,EAAE,CAAC,kCAAkC,EAAE,MAAK;MAC1C,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAAC2B,OAAO,CAACvB,EAAE,CAAC,CAACc,SAAS,CAAEU,SAAS,IAAI;QAC1Cd,MAAM,CAACc,SAAS,CAAC,CAACR,OAAO,CAACjB,aAAa,CAAC;MAC1C,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvB,aAAa,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,gCAAgC,EAAE,MAAK;MACxC,MAAMgB,YAAY,GAA0B;QAC1CxB,IAAI,EAAE;OACP;MAEDL,OAAO,CAAC8B,MAAM,CAACD,YAAY,CAAC,CAACX,SAAS,CAAEU,SAAS,IAAI;QACnDd,MAAM,CAACc,SAAS,CAAC,CAACR,OAAO,CAACjB,aAAa,CAAC;MAC1C,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAACpB,MAAM,CAAC;MACtCY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACS,YAAY,CAAC;MAC9CR,GAAG,CAACK,KAAK,CAACvB,aAAa,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,4BAA4B,EAAE,MAAK;MACpC,MAAMT,EAAE,GAAG,CAAC;MACZ,MAAM4B,UAAU,GAAuB;QACrC3B,IAAI,EAAE;OACP;MAEDL,OAAO,CAACiC,MAAM,CAAC7B,EAAE,EAAE4B,UAAU,CAAC,CAACd,SAAS,CAAEU,SAAS,IAAI;QACrDd,MAAM,CAACc,SAAS,CAAC,CAACR,OAAO,CAACjB,aAAa,CAAC;MAC1C,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACY,UAAU,CAAC;MAC5CX,GAAG,CAACK,KAAK,CAACvB,aAAa,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBc,EAAE,CAAC,4BAA4B,EAAE,MAAK;MACpC,MAAMT,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAACkC,MAAM,CAAC9B,EAAE,CAAC,CAACc,SAAS,CAAC,MAAK;QAChCJ,MAAM,EAAE,CAACqB,OAAO,EAAE;MACpB,CAAC,CAAC;MAEF,MAAMd,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF3B,QAAQ,CAAC,WAAW,EAAE,MAAK;IACzBc,EAAE,CAAC,oCAAoC,EAAE,MAAK;MAC5C,MAAMR,IAAI,GAAG,iBAAiB;MAE9BL,OAAO,CAACoC,SAAS,CAAC/B,IAAI,CAAC,CAACa,SAAS,CAAEU,SAAS,IAAI;QAC9Cd,MAAM,CAACc,SAAS,CAAC,CAACR,OAAO,CAACjB,aAAa,CAAC;MAC1C,CAAC,CAAC;MAEF,MAAMkB,GAAG,GAAGpB,QAAQ,CAACqB,SAAS,CAAC,GAAGpB,MAAM,SAASG,IAAI,EAAE,CAAC;MACxDS,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACvB,aAAa,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}