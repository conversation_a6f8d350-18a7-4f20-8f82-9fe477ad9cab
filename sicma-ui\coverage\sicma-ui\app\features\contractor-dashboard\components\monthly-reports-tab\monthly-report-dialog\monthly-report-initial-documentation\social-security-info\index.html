
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for app/features/contractor-dashboard/components/monthly-reports-tab/monthly-report-dialog/monthly-report-initial-documentation/social-security-info</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../../../../../index.html">All files</a> app/features/contractor-dashboard/components/monthly-reports-tab/monthly-report-dialog/monthly-report-initial-documentation/social-security-info</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">83.33% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>60/72</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">71.11% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>32/45</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>17/17</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">82.6% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>57/69</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="social-security-info.component.ts"><a href="social-security-info.component.ts.html">social-security-info.component.ts</a></td>
	<td data-value="83.33" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 83%"></div><div class="cover-empty" style="width: 17%"></div></div>
	</td>
	<td data-value="83.33" class="pct high">83.33%</td>
	<td data-value="72" class="abs high">60/72</td>
	<td data-value="71.11" class="pct medium">71.11%</td>
	<td data-value="45" class="abs medium">32/45</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="17" class="abs high">17/17</td>
	<td data-value="82.6" class="pct high">82.6%</td>
	<td data-value="69" class="abs high">57/69</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-16T16:33:29.688Z
            </div>
        <script src="../../../../../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../../../../../sorter.js"></script>
        <script src="../../../../../../../../block-navigation.js"></script>
    </body>
</html>
    