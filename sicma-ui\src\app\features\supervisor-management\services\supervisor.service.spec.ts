import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { environment } from '@env';
import { Supervisor } from '@supervisor-management/models/supervisor.model';
import { SupervisorService } from './supervisor.service';

describe('SupervisorService', () => {
  let service: SupervisorService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/supervisors`;

  const mockSupervisor: Supervisor = {
    id: 1,
    fullName: '<PERSON> Doe',
    idNumber: 123456789,
    position: 'Manager',
    email: '<EMAIL>',
    idType: {
      id: 1,
      name: 'CC',
    },
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [SupervisorService],
    });
    service = TestBed.inject(SupervisorService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should get all supervisors', () => {
    const mockSupervisors = [mockSupervisor];

    service.getAll().subscribe((supervisors) => {
      expect(supervisors).toEqual(mockSupervisors);
    });

    const req = httpMock.expectOne(apiUrl);
    expect(req.request.method).toBe('GET');
    req.flush(mockSupervisors);
  });

  it('should get supervisor by id', () => {
    const id = 1;

    service.getById(id).subscribe((supervisor) => {
      expect(supervisor).toEqual(mockSupervisor);
    });

    const req = httpMock.expectOne(`${apiUrl}/${id}`);
    expect(req.request.method).toBe('GET');
    req.flush(mockSupervisor);
  });

  it('should get supervisor by id number', () => {
    const idNumber = 123456789;

    service.getByIdNumber(idNumber).subscribe((supervisor) => {
      expect(supervisor).toEqual(mockSupervisor);
    });

    const req = httpMock.expectOne(`${apiUrl}/id-number/${idNumber}`);
    expect(req.request.method).toBe('GET');
    req.flush(mockSupervisor);
  });

  it('should get supervisor by email', () => {
    const email = '<EMAIL>';

    service.getByEmail(email).subscribe((supervisor) => {
      expect(supervisor).toEqual(mockSupervisor);
    });

    const req = httpMock.expectOne(`${apiUrl}/email/${email}`);
    expect(req.request.method).toBe('GET');
    req.flush(mockSupervisor);
  });

  it('should create supervisor', () => {
    const newSupervisor: Omit<Supervisor, 'id'> = {
      fullName: 'John Doe',
      idNumber: 123456789,
      position: 'Manager',
      email: '<EMAIL>',
      idType: {
        id: 1,
        name: 'CC',
      },
    };

    service.create(newSupervisor).subscribe((supervisor) => {
      expect(supervisor).toEqual(mockSupervisor);
    });

    const req = httpMock.expectOne(apiUrl);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(newSupervisor);
    req.flush(mockSupervisor);
  });

  it('should create supervisor with signature', () => {
    const newSupervisor: Omit<Supervisor, 'id'> = {
      fullName: 'John Doe',
      idNumber: 123456789,
      position: 'Manager',
      email: '<EMAIL>',
      idType: {
        id: 1,
        name: 'CC',
      },
    };
    const signatureFile = new File([''], 'signature.png', {
      type: 'image/png',
    });

    service
      .createWithSignature(newSupervisor, signatureFile)
      .subscribe((supervisor) => {
        expect(supervisor).toEqual(mockSupervisor);
      });

    const req = httpMock.expectOne(`${apiUrl}/with-signature`);
    expect(req.request.method).toBe('POST');
    expect(req.request.body instanceof FormData).toBeTruthy();
    req.flush(mockSupervisor);
  });

  it('should update supervisor', () => {
    const id = 1;
    const updateData: Partial<Supervisor> = {
      fullName: 'John Updated',
      position: 'Senior Manager',
    };

    service.update(id, updateData).subscribe((supervisor) => {
      expect(supervisor).toEqual(mockSupervisor);
    });

    const req = httpMock.expectOne(`${apiUrl}/${id}`);
    expect(req.request.method).toBe('PUT');
    expect(req.request.body).toEqual(updateData);
    req.flush(mockSupervisor);
  });

  it('should update supervisor with signature', () => {
    const id = 1;
    const updateData: Partial<Supervisor> = {
      fullName: 'John Updated',
      position: 'Senior Manager',
    };
    const signatureFile = new File([''], 'signature.png', {
      type: 'image/png',
    });

    service
      .updateWithSignature(id, updateData, signatureFile)
      .subscribe((supervisor) => {
        expect(supervisor).toEqual(mockSupervisor);
      });

    const req = httpMock.expectOne(`${apiUrl}/${id}/with-signature`);
    expect(req.request.method).toBe('PUT');
    expect(req.request.body instanceof FormData).toBeTruthy();
    req.flush(mockSupervisor);
  });

  it('should delete supervisor', () => {
    const id = 1;

    service.delete(id).subscribe(() => {
      expect().nothing();
    });

    const req = httpMock.expectOne(`${apiUrl}/${id}`);
    expect(req.request.method).toBe('DELETE');
    req.flush(null);
  });
});