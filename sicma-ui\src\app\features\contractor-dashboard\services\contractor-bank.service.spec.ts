import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { ContractorBank } from '@contractor-dashboard/models/contractor-bank.model';
import { environment } from '@env';
import { ContractorBankService } from './contractor-bank.service';

describe('ContractorBankService', () => {
  let service: ContractorBankService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/contractor-banks`;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ContractorBankService],
    });
    service = TestBed.inject(ContractorBankService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  const mockContractorBank: ContractorBank = {
    id: 1,
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    certified: true,
    accountNumber: '*********',
    contractorId: 1,
    bankId: 1,
    bankAccountTypeId: 1,
  };

  describe('getAll', () => {
    it('should return all contractor banks', () => {
      const mockContractorBanks = [mockContractorBank];

      service.getAll().subscribe((contractorBanks) => {
        expect(contractorBanks).toEqual(mockContractorBanks);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockContractorBanks);
    });

    it('should handle error when getting all contractor banks', () => {
      service.getAll().subscribe({
        error: (error) => {
          expect(error.status).toBe(500);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Server error'), { status: 500 });
    });
  });

  describe('getById', () => {
    it('should return a contractor bank by id', () => {
      service.getById(1).subscribe((contractorBank) => {
        expect(contractorBank).toEqual(mockContractorBank);
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('GET');
      req.flush(mockContractorBank);
    });

    it('should handle error when getting contractor bank by id', () => {
      service.getById(999).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/999`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('create', () => {
    const newContractorBank: Omit<ContractorBank, 'id'> = {
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      certified: true,
      accountNumber: '*********',
      contractorId: 1,
      bankId: 1,
      bankAccountTypeId: 1,
    };

    it('should create a new contractor bank', () => {
      service.create(newContractorBank).subscribe((contractorBank) => {
        expect(contractorBank).toEqual(mockContractorBank);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newContractorBank);
      req.flush(mockContractorBank);
    });

    it('should handle error when creating contractor bank', () => {
      service.create(newContractorBank).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(apiUrl);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('update', () => {
    const updateData: Partial<ContractorBank> = {
      certified: false,
      accountNumber: '*********',
    };

    it('should update a contractor bank', () => {
      service.update(1, updateData).subscribe((contractorBank) => {
        expect(contractorBank).toEqual({
          ...mockContractorBank,
          ...updateData,
        });
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush({ ...mockContractorBank, ...updateData });
    });

    it('should handle error when updating contractor bank', () => {
      service.update(1, updateData).subscribe({
        error: (error) => {
          expect(error.status).toBe(400);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      req.error(new ErrorEvent('Bad request'), { status: 400 });
    });
  });

  describe('delete', () => {
    it('should delete a contractor bank', () => {
      service.delete(1).subscribe((response) => {
        expect(response).toBeNull();
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });

    it('should handle error when deleting contractor bank', () => {
      service.delete(1).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/1`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });

  describe('getByContractorId', () => {
    it('should return contractor banks by contractor id', () => {
      const contractorId = 1;
      const mockContractorBanks = [mockContractorBank];

      service.getByContractorId(contractorId).subscribe((contractorBanks) => {
        expect(contractorBanks).toEqual(mockContractorBanks);
      });

      const req = httpMock.expectOne(`${apiUrl}/contractor/${contractorId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockContractorBanks);
    });

    it('should handle error when getting contractor banks by contractor id', () => {
      const contractorId = 999;

      service.getByContractorId(contractorId).subscribe({
        error: (error) => {
          expect(error.status).toBe(404);
        },
      });

      const req = httpMock.expectOne(`${apiUrl}/contractor/${contractorId}`);
      req.error(new ErrorEvent('Not found'), { status: 404 });
    });
  });
});