import { AlertService } from '@shared/services/alert.service';
import { forkJoin, Observable, of } from 'rxjs';
import {
  debounceTime,
  finalize,
  map,
  startWith,
  switchMap,
} from 'rxjs/operators';
import { ContractService } from '../../services/contract.service';

import { AsyncPipe } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  MatSlideToggleChange,
  MatSlideToggleModule,
} from '@angular/material/slide-toggle';

import {
  MatAutocomplete,
  MatAutocompleteModule,
  MatAutocompleteSelectedEvent,
  MatAutocompleteTrigger,
} from '@angular/material/autocomplete';
import { MatDatepickerModule } from '@angular/material/datepicker';

import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { CausesSelection } from '@contract-management/models/causes-seletion.model';
import { ContractClass } from '@contract-management/models/contract-class.model';
import { Contract } from '@contract-management/models/contract.model';
import { Dependency } from '@contract-management/models/dependency.model';
import { Group } from '@contract-management/models/group.model';
import { InsuredRisks } from '@contract-management/models/insured_risks.model';
import { ManagementSupport } from '@contract-management/models/management-support.model';
import { TypeWarranty } from '@contract-management/models/type_warranty.model';
import { CausesSelectionService } from '@contract-management/services/causes-selection.service';
import { ContractClassService } from '@contract-management/services/contract-class.service';
import { DependencyService } from '@contract-management/services/dependency.service';
import { GroupService } from '@contract-management/services/group.service';
import { InsuredRisksService } from '@contract-management/services/insured_risks.service';
import { ManagementSupportService } from '@contract-management/services/management-support.service';
import { TypeWarrantyService } from '@contract-management/services/type_warranty.service';
import { Department } from '@shared/models/department.model';
import { Municipality } from '@shared/models/municipality.model';
import { DepartmentService } from '@shared/services/department.service';
import { MunicipalityService } from '@shared/services/municipality.service';
import { Supervisor } from '@supervisor-management/models/supervisor.model';
import { SupervisorService } from '@supervisor-management/services/supervisor.service';
import { NgxCurrencyDirective } from 'ngx-currency';
import { NgxSpinnerService } from 'ngx-spinner';
import { ContractType } from '../../models/contract-type.model';
import { ContractYear } from '../../models/contract-year.model';
import { SelectionModality } from '../../models/selection-modality.model';
import { TrackingType } from '../../models/tracking-type.model';
import { ContractTypeService } from '../../services/contract-type.service';
import { ContractYearService } from '../../services/contract-year.service';
import { SelectionModalityService } from '../../services/selection-modality.service';
import { StatusService } from '../../services/status.service';
import { TrackingTypeService } from '../../services/tracking-type.service';

interface ContractDetailFormValue {
  contractNumber: number;
  contractYear: number;
  object: string;
  sigepLink?: string;
  secopLink?: string;
  rup: boolean;
  addition: boolean;
  cession: boolean;
  settled: boolean;
  selectionModality: number;
  trackingType: number;
  contractType: number;
  dependency: number;
  group: number;
  earlyTermination: boolean | null;
  monthlyPayment: number;
  municipalityId: number;
  departmentId: number;
  secopCode: string;
  warranty: boolean;
  dateExpeditionWarranty?: string;
  typeWarrantyId?: number;
  insuredRisksId?: number;
  supervisorId: number;
  supervisorFullName: string;
  causesSelectionId: number;
  contractClassId: number;
  managementSupportId: number;
}

@Component({
  selector: 'app-contract-detail-form',
  templateUrl: './contract-detail-form.component.html',
  styleUrl: './contract-detail-form.component.scss',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatSlideToggleModule,
    MatAutocompleteModule,
    MatIconModule,
    MatButtonModule,
    MatDatepickerModule,
    AsyncPipe,
    NgxCurrencyDirective,
  ],
})
export class ContractDetailFormComponent implements OnInit {
  @ViewChild('departmentAuto') departmentAutocomplete?: MatAutocomplete;
  @ViewChild('departmentInput', { read: MatAutocompleteTrigger })
  departmentAutocompleteTrigger?: MatAutocompleteTrigger;
  @ViewChild('municipalityAuto') municipalityAutocomplete?: MatAutocomplete;
  @ViewChild('municipalityInput', { read: MatAutocompleteTrigger })
  municipalityAutocompleteTrigger?: MatAutocompleteTrigger;

  @Input() contract: Contract | null = null;
  contractForm: FormGroup = this.formBuilder.group({
    contractNumber: ['', [Validators.required, Validators.min(1)]],
    contractYear: ['', Validators.required],
    object: ['', Validators.required],
    sigepLink: [''],
    secopLink: [''],
    rup: [false],
    addition: [{ value: false, disabled: true }],
    cession: [{ value: false, disabled: true }],
    settled: [{ value: false, disabled: true }],
    selectionModality: ['', Validators.required],
    trackingType: ['', Validators.required],
    contractType: ['', Validators.required],
    dependency: ['', Validators.required],
    group: ['', Validators.required],
    earlyTermination: [{ value: null, disabled: true }],
    monthlyPayment: [null, [Validators.required, Validators.min(1)]],
    municipalityId: ['', Validators.required],
    departmentId: ['', Validators.required],
    secopCode: ['', Validators.required],
    warranty: [false],
    dateExpeditionWarranty: [''],
    typeWarrantyId: [''],
    insuredRisksId: [''],
    supervisorId: [null as number | null, Validators.required],
    supervisorFullName: ['', Validators.required],
    causesSelectionId: ['', Validators.required],
    contractClassId: ['', Validators.required],
    managementSupportId: ['', Validators.required],
    completed: [false],
  });
  @Output() earlyTerminationToggled = new EventEmitter<void>();
  @Output() contractCompletedChanged = new EventEmitter<boolean>();

  selectionModalities: SelectionModality[] = [];
  trackingTypes: TrackingType[] = [];
  contractTypes: ContractType[] = [];
  contractYears: ContractYear[] = [];
  typeWarranty: TypeWarranty[] = [];
  insuredRisks: InsuredRisks[] = [];
  dependencies: Dependency[] = [];
  groups: Group[] = [];
  filteredGroups: Group[] = [];
  departments: Department[] = [];
  municipalities: Municipality[] = [];
  filteredMunicipalities: Municipality[] = [];
  supervisors: Supervisor[] = [];
  managementSupport: ManagementSupport[] = [];
  causesSelection: CausesSelection[] = [];
  contractClass: ContractClass[] = [];

  supervisor: Supervisor | null = null;
  supervisorFullNameControl = this.contractForm.get(
    'supervisorFullName',
  ) as FormControl;
  filteredSupervisors = this.supervisorFullNameControl.valueChanges.pipe(
    startWith(''),
    map(value => {
      const searchValue = typeof value === 'string' ? value.toLowerCase() : '';
      return this.supervisors.filter(supervisor =>
        supervisor.fullName.toLowerCase().includes(searchValue)
      );
    })
  );

  departmentSearchCtrl = new FormControl('');
  municipalitySearchCtrl = new FormControl('');

  filteredDepartments: Observable<Department[]>;

  constructor(
    private readonly selectionModalityService: SelectionModalityService,
    private readonly trackingTypeService: TrackingTypeService,
    private readonly contractTypeService: ContractTypeService,
    private readonly alert: AlertService,
    private readonly formBuilder: FormBuilder,
    private readonly contractYearService: ContractYearService,
    private readonly contractService: ContractService,
    private readonly supervisorService: SupervisorService,
    private readonly typeWarrantyService: TypeWarrantyService,
    private readonly insuredRisksService: InsuredRisksService,
    private readonly dependencyService: DependencyService,
    private readonly groupservice: GroupService,
    private readonly departmentService: DepartmentService,
    private readonly municipalityService: MunicipalityService,
    private readonly managementSupportService: ManagementSupportService,
    private readonly causesSelectionService: CausesSelectionService,
    private readonly contractClassService: ContractClassService,
    private readonly statusService: StatusService,
    private readonly spinner: NgxSpinnerService,
  ) {
    this.filteredDepartments = this.departmentSearchCtrl.valueChanges.pipe(
      startWith(''),
      map((value) => this._filterDepartments(value || '')),
    );

    this.filteredMunicipalities = [];

    this.departmentSearchCtrl.valueChanges.subscribe((value) => {
      if (value) {
        this.municipalitySearchCtrl.enable();
      } else {
        this.municipalitySearchCtrl.disable();
      }
    });
  }

  ngOnInit(): void {
    this.loadData();
    this.setupContractNumberValidation();
    this.setupWarrantyValidation();

    if (!this.departmentSearchCtrl.value) {
      this.municipalitySearchCtrl.disable();
    }

    if (this.contract) {
      this.contractForm.patchValue({
        ...this.contract,
        contractYear: this.contract.contractYear?.id,
        selectionModality: this.contract.selectionModality?.id,
        trackingType: this.contract.trackingType?.id,
        contractType: this.contract.contractType?.id,
        dependency: this.contract.dependency?.id,
        group: this.contract.group?.id,
        municipalityId: this.contract.municipality?.id,
        departmentId: this.contract.department?.id,
        causesSelectionId: this.contract.causesSelection?.id,
        contractClassId: this.contract.contractClass?.id,
        managementSupportId: this.contract.managementSupport?.id
      });

      // Set supervisor data if available
      if (this.contract.supervisor) {
        this.supervisor = this.contract.supervisor;
        this.contractForm.patchValue({
          supervisorId: this.contract.supervisor.id,
          supervisorFullName: this.contract.supervisor.fullName
        });
      }

      const department = this.contract.department;
      if (department) {
        this.departmentSearchCtrl.setValue(department.name);
        if (this.contract.municipality) {
          this.municipalitySearchCtrl.setValue(this.contract.municipality.name);
        }
      }

      if (!this.contract.earlyTermination) {
        this.contractForm.get('earlyTermination')?.enable();
      }

      if (this.contract.status) {
        const isFinished = this.contract.status.name === 'FINALIZADO';
        this.contractForm.get('completed')?.setValue(isFinished);

        if (isFinished) {
          this.setFormControlsState(false);
        }
      }
    }
  }

  private loadData(): void {
    forkJoin({
      selectionModalities: this.selectionModalityService.getAll(),
      trackingTypes: this.trackingTypeService.getAll(),
      contractTypes: this.contractTypeService.getAll(),
      contractYears: this.contractYearService.getAll(),
      typeWarranty: this.typeWarrantyService.getAll(),
      insuredRisks: this.insuredRisksService.getAll(),
      dependencies: this.dependencyService.getAll(),
      groups: this.groupservice.getAll(),
      department: this.departmentService.getAll(),
      municipality: this.municipalityService.getAll(),
      supervisors: this.supervisorService.getAll(),
      managementSupport: this.managementSupportService.getAll(),
      causesSelection: this.causesSelectionService.getAll(),
      contractClass: this.contractClassService.getAll(),
    }).subscribe({
      next: ({
        selectionModalities,
        trackingTypes,
        contractTypes,
        contractYears,
        typeWarranty,
        insuredRisks,
        dependencies,
        groups,
        department,
        municipality,
        supervisors,
        managementSupport,
        causesSelection,
        contractClass,
      }) => {
        this.selectionModalities = selectionModalities;
        this.trackingTypes = trackingTypes;
        this.contractTypes = contractTypes;
        this.contractYears = contractYears;
        this.typeWarranty = typeWarranty;
        this.insuredRisks = insuredRisks;
        this.dependencies = dependencies;
        this.groups = groups;
        this.departments = department;
        this.municipalities = municipality;
        this.supervisors = supervisors;
        this.managementSupport = managementSupport;
        this.causesSelection = causesSelection;
        this.contractClass = contractClass;

        if (!this.contract) {
          const currentYear = new Date().getFullYear();
          const defaultYear = this.contractYears.find(y => y.year === currentYear);
          if (defaultYear) {
            this.contractForm.get('contractYear')?.setValue(defaultYear.id);
          }
        }

        if (this.contract?.dependency?.id) {
          this.onDependencyChange(this.contract.dependency.id);
        }
        if (this.contract?.department?.id) {
          this.onDepartmentChange(this.contract.department.id);
        }

        this.filteredDepartments = this.departmentSearchCtrl.valueChanges.pipe(
          startWith(''),
          map((value) => this._filterDepartments(value || '')),
        );
      },
      error: (_) => {
        this.alert.error(error.error?.detail ?? 'Error al cargar los datos');
      },
    });
  }

  onDependencyChange(dependencyId: number): void {
    if (dependencyId != null) {
      this.filteredGroups = this.groups.filter(
        (group) => group.dependency.id === dependencyId,
      );
    } else {
      this.filteredGroups = [];
    }
  }

  onDepartmentChange(departmentId: number): void {
    if (departmentId != null) {
      this.municipalitySearchCtrl.enable();
      this.loadMunicipalities(departmentId);
    } else {
      this.filteredMunicipalities = [];
      this.municipalitySearchCtrl.setValue('');
      this.municipalitySearchCtrl.disable();
      this.contractForm.get('municipalityId')?.setValue(null);
    }
  }

  loadMunicipalities(departmentId: number): void {
    this.municipalityService.getAllByDepartmentId(departmentId).subscribe({
      next: (municipalities) => {
        this.municipalities = municipalities;
        this.filteredMunicipalities = this.municipalities;

        this.municipalitySearchCtrl.valueChanges
          .pipe(
            startWith(''),
            map((value) => this._filterMunicipalities(value || '')),
          )
          .subscribe((filtered) => {
            this.filteredMunicipalities = filtered;
          });
      },
      error: (_) => {
        this.alert.error(error.error?.detail ?? 'Error al cargar municipios');
      },
    });
  }

  handleDepartmentSelection(event: MatAutocompleteSelectedEvent): void {
    const selectedDepartmentName = event.option.viewValue;
    const selectedDepartment = this.departments.find(
      (dept) => dept.name === selectedDepartmentName,
    );

    if (selectedDepartment) {
      this.contractForm.get('departmentId')?.setValue(selectedDepartment.id);
      this.contractForm.get('municipalityId')?.setValue(null);
      this.municipalitySearchCtrl.setValue('');
      this.onDepartmentChange(selectedDepartment.id);

      setTimeout(() => {
        this.departmentAutocompleteTrigger?.closePanel();
      }, 0);
    }
  }

  handleMunicipalitySelection(event: MatAutocompleteSelectedEvent): void {
    const selectedMunicipalityName = event.option.viewValue;
    const selectedMunicipality = this.municipalities.find(
      (mun) => mun.name === selectedMunicipalityName,
    );

    if (selectedMunicipality) {
      this.contractForm
        .get('municipalityId')
        ?.setValue(selectedMunicipality.id);

      setTimeout(() => {
        this.municipalityAutocompleteTrigger?.closePanel();
      }, 0);
    }
  }

  private _filterDepartments(value: string): Department[] {
    const filterValue = value.toLowerCase();
    return this.departments.filter((department) =>
      department.name.toLowerCase().includes(filterValue),
    );
  }

  private _filterMunicipalities(value: string): Municipality[] {
    const filterValue = value.toLowerCase();
    return this.municipalities.filter((municipality) =>
      municipality.name.toLowerCase().includes(filterValue),
    );
  }

  displayDepartment(departmentName: string): string {
    return departmentName;
  }

  displayMunicipality(municipalityName: string): string {
    return municipalityName;
  }

  displaySupervisorName(supervisor: Supervisor | string): string {
    if (typeof supervisor === 'string') {
      return supervisor;
    }
    return supervisor ? supervisor.fullName : '';
  }

  showAllDepartments(): void {
    if (!this.departmentAutocompleteTrigger?.panelOpen) {
      this.departmentSearchCtrl.setValue('');
      setTimeout(() => {
        this.departmentAutocompleteTrigger?.openPanel();
      }, 0);
    }
  }

  showAllMunicipalities(): void {
    if (this.departmentSearchCtrl.value && !this.municipalityAutocompleteTrigger?.panelOpen) {
      this.municipalitySearchCtrl.setValue('');
      setTimeout(() => {
        this.municipalityAutocompleteTrigger?.openPanel();
      }, 0);
    }
  }

  async onRupChange(event: MatSlideToggleChange): Promise<void> {
    if (!this.contract?.id) return;

    if (event.checked) {
      const confirmed = await this.alert.confirm('¿Está seguro de marcar RUP?');
      if (confirmed) {
        this.spinner.show();
        this.contractService
          .update(this.contract.id, { rup: true })
          .pipe(finalize(() => this.spinner.hide()))
          .subscribe({
            next: () => {
              this.contractForm.get('rup')?.setValue(true);
              this.alert.success('El contrato ha sido marcado como RUP.');
            },
            error: (_) => {
              this.contractForm.get('rup')?.setValue(false);
              this.alert.error(error.error?.detail ?? 'Error al actualizar el contrato');
            },
          });
      } else {
        this.contractForm.get('rup')?.setValue(false);
      }
    } else {
      const confirmed = await this.alert.confirm(
        '¿Está seguro de desmarcar RUP?',
      );
      if (confirmed) {
        this.spinner.show();
        this.contractService
          .update(this.contract.id, { rup: false })
          .pipe(finalize(() => this.spinner.hide()))
          .subscribe({
            next: () => {
              this.contractForm.get('rup')?.setValue(false);
              this.alert.success('El contrato ha sido desmarcado como RUP.');
            },
            error: (_) => {
              this.contractForm.get('rup')?.setValue(true);
              this.alert.error(error.error?.detail ?? 'Error al actualizar el contrato');
            },
          });
      } else {
        this.contractForm.get('rup')?.setValue(true);
      }
    }
  }

  async onCompletedChange(event: MatSlideToggleChange): Promise<void> {
    if (!this.contract?.id) return;

    if (event.checked) {
      const confirmed = await this.alert.confirm(
        '¿Está seguro de marcar el contrato como finalizado?',
      );
      if (confirmed) {
        this.spinner.show();
        this.statusService
          .getByName('FINALIZADO')
          .pipe(
            switchMap((status) =>
              this.contractService.update(this.contract!.id, {
                statusId: status.id,
              }),
            ),
            finalize(() => this.spinner.hide()),
          )
          .subscribe({
            next: () => {
              this.contractForm.get('completed')?.setValue(true);
              this.setFormControlsState(false);
              this.alert.success(
                'El contrato ha sido marcado como finalizado.',
              );
              this.contractCompletedChanged.emit(true);
            },
            error: (_) => {
              this.contractForm.get('completed')?.setValue(false);
              this.alert.error(error.error?.detail ?? 'Error al actualizar el estado del contrato');
            },
          });
      } else {
        this.contractForm.get('completed')?.setValue(false);
      }
    } else {
      const confirmed = await this.alert.confirm(
        '¿Está seguro de desmarcar el contrato como finalizado?',
      );
      if (confirmed) {
        this.spinner.show();
        this.statusService
          .getByName('ACTIVO')
          .pipe(
            switchMap((status) =>
              this.contractService.update(this.contract!.id, {
                statusId: status.id,
              }),
            ),
            finalize(() => this.spinner.hide()),
          )
          .subscribe({
            next: () => {
              this.contractForm.get('completed')?.setValue(false);
              this.setFormControlsState(true);
              this.alert.success('El contrato ha sido marcado como activo.');
              this.contractCompletedChanged.emit(false);
            },
            error: (_) => {
              this.contractForm.get('completed')?.setValue(true);
              this.alert.error(error.error?.detail ?? 'Error al actualizar el estado del contrato');
            },
          });
      } else {
        this.contractForm.get('completed')?.setValue(true);
      }
    }
  }

  private setFormControlsState(enable: boolean): void {
    const excludedControls = ['completed'];

    Object.keys(this.contractForm.controls).forEach((controlName) => {
      if (!excludedControls.includes(controlName)) {
        const control = this.contractForm.get(controlName);
        if (enable) {
          control?.enable();
        } else {
          control?.disable();
        }
      }
    });
  }

  private setupContractNumberValidation(): void {
    this.contractForm
      .get('contractNumber')
      ?.valueChanges.pipe(
        debounceTime(300),
        switchMap((contractNumber) => {
          const contractYearId = this.contractForm.get('contractYear')?.value;
          if (contractNumber && contractYearId) {
            return this.contractService.validateContractNumber(
              contractNumber,
              contractYearId,
            );
          }
          return of(true);
        }),
      )
      .subscribe((isValid) => {
        if (!isValid) {
          this.contractForm
            .get('contractNumber')
            ?.setErrors({ duplicateContract: true });
        }
      });

    this.contractForm.get('contractYear')?.valueChanges.subscribe(() => {
      this.contractForm.get('contractNumber')?.updateValueAndValidity();
    });
  }

  onSupervisorSelected(event: MatAutocompleteSelectedEvent): void {
    const supervisor = event.option.value as Supervisor;
    this.supervisor = supervisor;
    this.contractForm.patchValue({
      supervisorId: supervisor.id,
      supervisorFullName: supervisor.fullName,
    });
  }

  clearSupervisor(): void {
    this.supervisor = null;
    this.contractForm.patchValue({
      supervisorId: null,
      supervisorFullName: '',
    });
  }

  private setupWarrantyValidation(): void {
    const dateExpeditionWarranty = this.contractForm.get(
      'dateExpeditionWarranty',
    );
    const typeWarrantyId = this.contractForm.get('typeWarrantyId');
    const insuredRisksId = this.contractForm.get('insuredRisksId');

    const updateValidation = (hasWarranty: boolean) => {
      if (hasWarranty) {
        dateExpeditionWarranty?.setValidators([Validators.required]);
        typeWarrantyId?.setValidators([Validators.required]);
        insuredRisksId?.setValidators([Validators.required]);
      } else {
        dateExpeditionWarranty?.setValidators(null);
        typeWarrantyId?.setValidators(null);
        insuredRisksId?.setValidators(null);

        dateExpeditionWarranty?.patchValue(null, { emitEvent: false });
        typeWarrantyId?.patchValue(null, { emitEvent: false });
        insuredRisksId?.patchValue(null, { emitEvent: false });
      }

      dateExpeditionWarranty?.markAsUntouched();
      typeWarrantyId?.markAsUntouched();
      insuredRisksId?.markAsUntouched();

      dateExpeditionWarranty?.updateValueAndValidity({ emitEvent: false });
      typeWarrantyId?.updateValueAndValidity({ emitEvent: false });
      insuredRisksId?.updateValueAndValidity({ emitEvent: false });
    };

    const initialWarranty = this.contractForm.get('warranty')?.value || false;
    updateValidation(initialWarranty);

    this.contractForm.get('warranty')?.valueChanges.subscribe(updateValidation);
  }

  isValid(): boolean {
    return this.contractForm.valid;
  }

  getValue(): ContractDetailFormValue {
    Object.keys(this.contractForm.controls).forEach(key => {
      const control = this.contractForm.get(key);
      if (control) {
        control.markAsTouched();
      }
    });

    return this.contractForm.getRawValue();
  }
}
