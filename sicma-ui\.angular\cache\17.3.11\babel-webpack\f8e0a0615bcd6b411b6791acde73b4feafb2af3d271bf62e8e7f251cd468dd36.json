{"ast": null, "code": "function cov_6fjwihlrw() {\n  var path = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\monthly-reports-review-list\\\\monthly-reports-review-list.component.ts\";\n  var hash = \"68e7a732dd2409f732d85b040b5e65f69a22cbfc\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\monthly-reports-review-list\\\\monthly-reports-review-list.component.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 23,\n          column: 40\n        },\n        end: {\n          line: 173,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 25,\n          column: 8\n        },\n        end: {\n          line: 25,\n          column: 29\n        }\n      },\n      \"2\": {\n        start: {\n          line: 26,\n          column: 8\n        },\n        end: {\n          line: 26,\n          column: 57\n        }\n      },\n      \"3\": {\n        start: {\n          line: 27,\n          column: 8\n        },\n        end: {\n          line: 27,\n          column: 31\n        }\n      },\n      \"4\": {\n        start: {\n          line: 28,\n          column: 8\n        },\n        end: {\n          line: 28,\n          column: 27\n        }\n      },\n      \"5\": {\n        start: {\n          line: 29,\n          column: 8\n        },\n        end: {\n          line: 29,\n          column: 51\n        }\n      },\n      \"6\": {\n        start: {\n          line: 30,\n          column: 8\n        },\n        end: {\n          line: 30,\n          column: 39\n        }\n      },\n      \"7\": {\n        start: {\n          line: 31,\n          column: 8\n        },\n        end: {\n          line: 43,\n          column: 10\n        }\n      },\n      \"8\": {\n        start: {\n          line: 44,\n          column: 8\n        },\n        end: {\n          line: 44,\n          column: 51\n        }\n      },\n      \"9\": {\n        start: {\n          line: 45,\n          column: 8\n        },\n        end: {\n          line: 45,\n          column: 37\n        }\n      },\n      \"10\": {\n        start: {\n          line: 48,\n          column: 8\n        },\n        end: {\n          line: 48,\n          column: 34\n        }\n      },\n      \"11\": {\n        start: {\n          line: 51,\n          column: 8\n        },\n        end: {\n          line: 51,\n          column: 51\n        }\n      },\n      \"12\": {\n        start: {\n          line: 52,\n          column: 8\n        },\n        end: {\n          line: 52,\n          column: 41\n        }\n      },\n      \"13\": {\n        start: {\n          line: 53,\n          column: 8\n        },\n        end: {\n          line: 63,\n          column: 10\n        }\n      },\n      \"14\": {\n        start: {\n          line: 54,\n          column: 12\n        },\n        end: {\n          line: 62,\n          column: 13\n        }\n      },\n      \"15\": {\n        start: {\n          line: 56,\n          column: 20\n        },\n        end: {\n          line: 58,\n          column: 21\n        }\n      },\n      \"16\": {\n        start: {\n          line: 57,\n          column: 24\n        },\n        end: {\n          line: 57,\n          column: 61\n        }\n      },\n      \"17\": {\n        start: {\n          line: 59,\n          column: 20\n        },\n        end: {\n          line: 59,\n          column: 65\n        }\n      },\n      \"18\": {\n        start: {\n          line: 61,\n          column: 20\n        },\n        end: {\n          line: 61,\n          column: 42\n        }\n      },\n      \"19\": {\n        start: {\n          line: 66,\n          column: 28\n        },\n        end: {\n          line: 66,\n          column: 61\n        }\n      },\n      \"20\": {\n        start: {\n          line: 67,\n          column: 8\n        },\n        end: {\n          line: 70,\n          column: 9\n        }\n      },\n      \"21\": {\n        start: {\n          line: 68,\n          column: 12\n        },\n        end: {\n          line: 68,\n          column: 78\n        }\n      },\n      \"22\": {\n        start: {\n          line: 69,\n          column: 12\n        },\n        end: {\n          line: 69,\n          column: 19\n        }\n      },\n      \"23\": {\n        start: {\n          line: 71,\n          column: 8\n        },\n        end: {\n          line: 71,\n          column: 28\n        }\n      },\n      \"24\": {\n        start: {\n          line: 72,\n          column: 8\n        },\n        end: {\n          line: 93,\n          column: 11\n        }\n      },\n      \"25\": {\n        start: {\n          line: 74,\n          column: 33\n        },\n        end: {\n          line: 74,\n          column: 52\n        }\n      },\n      \"26\": {\n        start: {\n          line: 77,\n          column: 16\n        },\n        end: {\n          line: 77,\n          column: 68\n        }\n      },\n      \"27\": {\n        start: {\n          line: 78,\n          column: 16\n        },\n        end: {\n          line: 88,\n          column: 19\n        }\n      },\n      \"28\": {\n        start: {\n          line: 82,\n          column: 24\n        },\n        end: {\n          line: 82,\n          column: 55\n        }\n      },\n      \"29\": {\n        start: {\n          line: 83,\n          column: 24\n        },\n        end: {\n          line: 83,\n          column: 46\n        }\n      },\n      \"30\": {\n        start: {\n          line: 86,\n          column: 24\n        },\n        end: {\n          line: 86,\n          column: 96\n        }\n      },\n      \"31\": {\n        start: {\n          line: 91,\n          column: 16\n        },\n        end: {\n          line: 91,\n          column: 102\n        }\n      },\n      \"32\": {\n        start: {\n          line: 96,\n          column: 8\n        },\n        end: {\n          line: 107,\n          column: 11\n        }\n      },\n      \"33\": {\n        start: {\n          line: 97,\n          column: 12\n        },\n        end: {\n          line: 106,\n          column: 13\n        }\n      },\n      \"34\": {\n        start: {\n          line: 98,\n          column: 35\n        },\n        end: {\n          line: 101,\n          column: 17\n        }\n      },\n      \"35\": {\n        start: {\n          line: 102,\n          column: 16\n        },\n        end: {\n          line: 102,\n          column: 53\n        }\n      },\n      \"36\": {\n        start: {\n          line: 103,\n          column: 16\n        },\n        end: {\n          line: 103,\n          column: 59\n        }\n      },\n      \"37\": {\n        start: {\n          line: 104,\n          column: 16\n        },\n        end: {\n          line: 104,\n          column: 54\n        }\n      },\n      \"38\": {\n        start: {\n          line: 105,\n          column: 16\n        },\n        end: {\n          line: 105,\n          column: 49\n        }\n      },\n      \"39\": {\n        start: {\n          line: 110,\n          column: 28\n        },\n        end: {\n          line: 110,\n          column: 46\n        }\n      },\n      \"40\": {\n        start: {\n          line: 111,\n          column: 8\n        },\n        end: {\n          line: 111,\n          column: 66\n        }\n      },\n      \"41\": {\n        start: {\n          line: 112,\n          column: 8\n        },\n        end: {\n          line: 114,\n          column: 9\n        }\n      },\n      \"42\": {\n        start: {\n          line: 113,\n          column: 12\n        },\n        end: {\n          line: 113,\n          column: 50\n        }\n      },\n      \"43\": {\n        start: {\n          line: 117,\n          column: 8\n        },\n        end: {\n          line: 120,\n          column: 9\n        }\n      },\n      \"44\": {\n        start: {\n          line: 118,\n          column: 12\n        },\n        end: {\n          line: 118,\n          column: 71\n        }\n      },\n      \"45\": {\n        start: {\n          line: 119,\n          column: 12\n        },\n        end: {\n          line: 119,\n          column: 19\n        }\n      },\n      \"46\": {\n        start: {\n          line: 121,\n          column: 25\n        },\n        end: {\n          line: 121,\n          column: 41\n        }\n      },\n      \"47\": {\n        start: {\n          line: 122,\n          column: 8\n        },\n        end: {\n          line: 125,\n          column: 9\n        }\n      },\n      \"48\": {\n        start: {\n          line: 123,\n          column: 12\n        },\n        end: {\n          line: 123,\n          column: 76\n        }\n      },\n      \"49\": {\n        start: {\n          line: 124,\n          column: 12\n        },\n        end: {\n          line: 124,\n          column: 19\n        }\n      },\n      \"50\": {\n        start: {\n          line: 126,\n          column: 8\n        },\n        end: {\n          line: 126,\n          column: 28\n        }\n      },\n      \"51\": {\n        start: {\n          line: 127,\n          column: 8\n        },\n        end: {\n          line: 159,\n          column: 11\n        }\n      },\n      \"52\": {\n        start: {\n          line: 129,\n          column: 33\n        },\n        end: {\n          line: 129,\n          column: 52\n        }\n      },\n      \"53\": {\n        start: {\n          line: 132,\n          column: 16\n        },\n        end: {\n          line: 142,\n          column: 17\n        }\n      },\n      \"54\": {\n        start: {\n          line: 133,\n          column: 20\n        },\n        end: {\n          line: 136,\n          column: 22\n        }\n      },\n      \"55\": {\n        start: {\n          line: 139,\n          column: 20\n        },\n        end: {\n          line: 139,\n          column: 85\n        }\n      },\n      \"56\": {\n        start: {\n          line: 140,\n          column: 20\n        },\n        end: {\n          line: 141,\n          column: 56\n        }\n      },\n      \"57\": {\n        start: {\n          line: 143,\n          column: 34\n        },\n        end: {\n          line: 149,\n          column: 18\n        }\n      },\n      \"58\": {\n        start: {\n          line: 150,\n          column: 16\n        },\n        end: {\n          line: 154,\n          column: 19\n        }\n      },\n      \"59\": {\n        start: {\n          line: 151,\n          column: 20\n        },\n        end: {\n          line: 153,\n          column: 21\n        }\n      },\n      \"60\": {\n        start: {\n          line: 152,\n          column: 24\n        },\n        end: {\n          line: 152,\n          column: 50\n        }\n      },\n      \"61\": {\n        start: {\n          line: 157,\n          column: 16\n        },\n        end: {\n          line: 157,\n          column: 95\n        }\n      },\n      \"62\": {\n        start: {\n          line: 161,\n          column: 13\n        },\n        end: {\n          line: 168,\n          column: 6\n        }\n      },\n      \"63\": {\n        start: {\n          line: 161,\n          column: 41\n        },\n        end: {\n          line: 168,\n          column: 5\n        }\n      },\n      \"64\": {\n        start: {\n          line: 169,\n          column: 13\n        },\n        end: {\n          line: 172,\n          column: 6\n        }\n      },\n      \"65\": {\n        start: {\n          line: 174,\n          column: 0\n        },\n        end: {\n          line: 194,\n          column: 38\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 24,\n            column: 4\n          },\n          end: {\n            line: 24,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 24,\n            column: 94\n          },\n          end: {\n            line: 46,\n            column: 5\n          }\n        },\n        line: 24\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 47,\n            column: 4\n          },\n          end: {\n            line: 47,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 47,\n            column: 15\n          },\n          end: {\n            line: 49,\n            column: 5\n          }\n        },\n        line: 47\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 50,\n            column: 4\n          },\n          end: {\n            line: 50,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 50,\n            column: 22\n          },\n          end: {\n            line: 64,\n            column: 5\n          }\n        },\n        line: 50\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 53,\n            column: 46\n          },\n          end: {\n            line: 53,\n            column: 47\n          }\n        },\n        loc: {\n          start: {\n            line: 53,\n            column: 66\n          },\n          end: {\n            line: 63,\n            column: 9\n          }\n        },\n        line: 53\n      },\n      \"4\": {\n        name: \"(anonymous_4)\",\n        decl: {\n          start: {\n            line: 65,\n            column: 4\n          },\n          end: {\n            line: 65,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 65,\n            column: 25\n          },\n          end: {\n            line: 94,\n            column: 5\n          }\n        },\n        line: 65\n      },\n      \"5\": {\n        name: \"(anonymous_5)\",\n        decl: {\n          start: {\n            line: 74,\n            column: 27\n          },\n          end: {\n            line: 74,\n            column: 28\n          }\n        },\n        loc: {\n          start: {\n            line: 74,\n            column: 33\n          },\n          end: {\n            line: 74,\n            column: 52\n          }\n        },\n        line: 74\n      },\n      \"6\": {\n        name: \"(anonymous_6)\",\n        decl: {\n          start: {\n            line: 76,\n            column: 18\n          },\n          end: {\n            line: 76,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 76,\n            column: 34\n          },\n          end: {\n            line: 89,\n            column: 13\n          }\n        },\n        line: 76\n      },\n      \"7\": {\n        name: \"(anonymous_7)\",\n        decl: {\n          start: {\n            line: 81,\n            column: 26\n          },\n          end: {\n            line: 81,\n            column: 27\n          }\n        },\n        loc: {\n          start: {\n            line: 81,\n            column: 39\n          },\n          end: {\n            line: 84,\n            column: 21\n          }\n        },\n        line: 81\n      },\n      \"8\": {\n        name: \"(anonymous_8)\",\n        decl: {\n          start: {\n            line: 85,\n            column: 27\n          },\n          end: {\n            line: 85,\n            column: 28\n          }\n        },\n        loc: {\n          start: {\n            line: 85,\n            column: 38\n          },\n          end: {\n            line: 87,\n            column: 21\n          }\n        },\n        line: 85\n      },\n      \"9\": {\n        name: \"(anonymous_9)\",\n        decl: {\n          start: {\n            line: 90,\n            column: 19\n          },\n          end: {\n            line: 90,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 90,\n            column: 30\n          },\n          end: {\n            line: 92,\n            column: 13\n          }\n        },\n        line: 90\n      },\n      \"10\": {\n        name: \"(anonymous_10)\",\n        decl: {\n          start: {\n            line: 95,\n            column: 4\n          },\n          end: {\n            line: 95,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 95,\n            column: 21\n          },\n          end: {\n            line: 108,\n            column: 5\n          }\n        },\n        line: 95\n      },\n      \"11\": {\n        name: \"(anonymous_11)\",\n        decl: {\n          start: {\n            line: 96,\n            column: 19\n          },\n          end: {\n            line: 96,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 96,\n            column: 25\n          },\n          end: {\n            line: 107,\n            column: 9\n          }\n        },\n        line: 96\n      },\n      \"12\": {\n        name: \"(anonymous_12)\",\n        decl: {\n          start: {\n            line: 109,\n            column: 4\n          },\n          end: {\n            line: 109,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 109,\n            column: 23\n          },\n          end: {\n            line: 115,\n            column: 5\n          }\n        },\n        line: 109\n      },\n      \"13\": {\n        name: \"(anonymous_13)\",\n        decl: {\n          start: {\n            line: 116,\n            column: 4\n          },\n          end: {\n            line: 116,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 116,\n            column: 30\n          },\n          end: {\n            line: 160,\n            column: 5\n          }\n        },\n        line: 116\n      },\n      \"14\": {\n        name: \"(anonymous_14)\",\n        decl: {\n          start: {\n            line: 129,\n            column: 27\n          },\n          end: {\n            line: 129,\n            column: 28\n          }\n        },\n        loc: {\n          start: {\n            line: 129,\n            column: 33\n          },\n          end: {\n            line: 129,\n            column: 52\n          }\n        },\n        line: 129\n      },\n      \"15\": {\n        name: \"(anonymous_15)\",\n        decl: {\n          start: {\n            line: 131,\n            column: 18\n          },\n          end: {\n            line: 131,\n            column: 19\n          }\n        },\n        loc: {\n          start: {\n            line: 131,\n            column: 34\n          },\n          end: {\n            line: 155,\n            column: 13\n          }\n        },\n        line: 131\n      },\n      \"16\": {\n        name: \"(anonymous_16)\",\n        decl: {\n          start: {\n            line: 150,\n            column: 50\n          },\n          end: {\n            line: 150,\n            column: 51\n          }\n        },\n        loc: {\n          start: {\n            line: 150,\n            column: 62\n          },\n          end: {\n            line: 154,\n            column: 17\n          }\n        },\n        line: 150\n      },\n      \"17\": {\n        name: \"(anonymous_17)\",\n        decl: {\n          start: {\n            line: 156,\n            column: 19\n          },\n          end: {\n            line: 156,\n            column: 20\n          }\n        },\n        loc: {\n          start: {\n            line: 156,\n            column: 30\n          },\n          end: {\n            line: 158,\n            column: 13\n          }\n        },\n        line: 156\n      },\n      \"18\": {\n        name: \"(anonymous_18)\",\n        decl: {\n          start: {\n            line: 161,\n            column: 35\n          },\n          end: {\n            line: 161,\n            column: 36\n          }\n        },\n        loc: {\n          start: {\n            line: 161,\n            column: 41\n          },\n          end: {\n            line: 168,\n            column: 5\n          }\n        },\n        line: 161\n      }\n    },\n    branchMap: {\n      \"0\": {\n        loc: {\n          start: {\n            line: 54,\n            column: 12\n          },\n          end: {\n            line: 62,\n            column: 13\n          }\n        },\n        type: \"switch\",\n        locations: [{\n          start: {\n            line: 55,\n            column: 16\n          },\n          end: {\n            line: 59,\n            column: 65\n          }\n        }, {\n          start: {\n            line: 60,\n            column: 16\n          },\n          end: {\n            line: 61,\n            column: 42\n          }\n        }],\n        line: 54\n      },\n      \"1\": {\n        loc: {\n          start: {\n            line: 56,\n            column: 20\n          },\n          end: {\n            line: 58,\n            column: 21\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 56,\n            column: 20\n          },\n          end: {\n            line: 58,\n            column: 21\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 56\n      },\n      \"2\": {\n        loc: {\n          start: {\n            line: 59,\n            column: 34\n          },\n          end: {\n            line: 59,\n            column: 63\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 59,\n            column: 34\n          },\n          end: {\n            line: 59,\n            column: 57\n          }\n        }, {\n          start: {\n            line: 59,\n            column: 61\n          },\n          end: {\n            line: 59,\n            column: 63\n          }\n        }],\n        line: 59\n      },\n      \"3\": {\n        loc: {\n          start: {\n            line: 67,\n            column: 8\n          },\n          end: {\n            line: 70,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 67,\n            column: 8\n          },\n          end: {\n            line: 70,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 67\n      },\n      \"4\": {\n        loc: {\n          start: {\n            line: 77,\n            column: 42\n          },\n          end: {\n            line: 77,\n            column: 67\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 77,\n            column: 42\n          },\n          end: {\n            line: 77,\n            column: 61\n          }\n        }, {\n          start: {\n            line: 77,\n            column: 65\n          },\n          end: {\n            line: 77,\n            column: 67\n          }\n        }],\n        line: 77\n      },\n      \"5\": {\n        loc: {\n          start: {\n            line: 86,\n            column: 41\n          },\n          end: {\n            line: 86,\n            column: 94\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 86,\n            column: 41\n          },\n          end: {\n            line: 86,\n            column: 60\n          }\n        }, {\n          start: {\n            line: 86,\n            column: 64\n          },\n          end: {\n            line: 86,\n            column: 94\n          }\n        }],\n        line: 86\n      },\n      \"6\": {\n        loc: {\n          start: {\n            line: 91,\n            column: 33\n          },\n          end: {\n            line: 91,\n            column: 100\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 91,\n            column: 33\n          },\n          end: {\n            line: 91,\n            column: 52\n          }\n        }, {\n          start: {\n            line: 91,\n            column: 56\n          },\n          end: {\n            line: 91,\n            column: 100\n          }\n        }],\n        line: 91\n      },\n      \"7\": {\n        loc: {\n          start: {\n            line: 97,\n            column: 12\n          },\n          end: {\n            line: 106,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 97,\n            column: 12\n          },\n          end: {\n            line: 106,\n            column: 13\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 97\n      },\n      \"8\": {\n        loc: {\n          start: {\n            line: 112,\n            column: 8\n          },\n          end: {\n            line: 114,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 112,\n            column: 8\n          },\n          end: {\n            line: 114,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 112\n      },\n      \"9\": {\n        loc: {\n          start: {\n            line: 117,\n            column: 8\n          },\n          end: {\n            line: 120,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 117,\n            column: 8\n          },\n          end: {\n            line: 120,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 117\n      },\n      \"10\": {\n        loc: {\n          start: {\n            line: 122,\n            column: 8\n          },\n          end: {\n            line: 125,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 122,\n            column: 8\n          },\n          end: {\n            line: 125,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 122\n      },\n      \"11\": {\n        loc: {\n          start: {\n            line: 132,\n            column: 16\n          },\n          end: {\n            line: 142,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 132,\n            column: 16\n          },\n          end: {\n            line: 142,\n            column: 17\n          }\n        }, {\n          start: {\n            line: 138,\n            column: 21\n          },\n          end: {\n            line: 142,\n            column: 17\n          }\n        }],\n        line: 132\n      },\n      \"12\": {\n        loc: {\n          start: {\n            line: 134,\n            column: 28\n          },\n          end: {\n            line: 134,\n            column: 56\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 134,\n            column: 28\n          },\n          end: {\n            line: 134,\n            column: 51\n          }\n        }, {\n          start: {\n            line: 134,\n            column: 55\n          },\n          end: {\n            line: 134,\n            column: 56\n          }\n        }],\n        line: 134\n      },\n      \"13\": {\n        loc: {\n          start: {\n            line: 135,\n            column: 30\n          },\n          end: {\n            line: 135,\n            column: 61\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 135,\n            column: 30\n          },\n          end: {\n            line: 135,\n            column: 55\n          }\n        }, {\n          start: {\n            line: 135,\n            column: 59\n          },\n          end: {\n            line: 135,\n            column: 61\n          }\n        }],\n        line: 135\n      },\n      \"14\": {\n        loc: {\n          start: {\n            line: 139,\n            column: 56\n          },\n          end: {\n            line: 139,\n            column: 84\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 139,\n            column: 56\n          },\n          end: {\n            line: 139,\n            column: 79\n          }\n        }, {\n          start: {\n            line: 139,\n            column: 83\n          },\n          end: {\n            line: 139,\n            column: 84\n          }\n        }],\n        line: 139\n      },\n      \"15\": {\n        loc: {\n          start: {\n            line: 141,\n            column: 24\n          },\n          end: {\n            line: 141,\n            column: 55\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 141,\n            column: 24\n          },\n          end: {\n            line: 141,\n            column: 49\n          }\n        }, {\n          start: {\n            line: 141,\n            column: 53\n          },\n          end: {\n            line: 141,\n            column: 55\n          }\n        }],\n        line: 141\n      },\n      \"16\": {\n        loc: {\n          start: {\n            line: 151,\n            column: 20\n          },\n          end: {\n            line: 153,\n            column: 21\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 151,\n            column: 20\n          },\n          end: {\n            line: 153,\n            column: 21\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 151\n      },\n      \"17\": {\n        loc: {\n          start: {\n            line: 157,\n            column: 33\n          },\n          end: {\n            line: 157,\n            column: 93\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 157,\n            column: 33\n          },\n          end: {\n            line: 157,\n            column: 52\n          }\n        }, {\n          start: {\n            line: 157,\n            column: 56\n          },\n          end: {\n            line: 157,\n            column: 93\n          }\n        }],\n        line: 157\n      }\n    },\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0,\n      \"36\": 0,\n      \"37\": 0,\n      \"38\": 0,\n      \"39\": 0,\n      \"40\": 0,\n      \"41\": 0,\n      \"42\": 0,\n      \"43\": 0,\n      \"44\": 0,\n      \"45\": 0,\n      \"46\": 0,\n      \"47\": 0,\n      \"48\": 0,\n      \"49\": 0,\n      \"50\": 0,\n      \"51\": 0,\n      \"52\": 0,\n      \"53\": 0,\n      \"54\": 0,\n      \"55\": 0,\n      \"56\": 0,\n      \"57\": 0,\n      \"58\": 0,\n      \"59\": 0,\n      \"60\": 0,\n      \"61\": 0,\n      \"62\": 0,\n      \"63\": 0,\n      \"64\": 0,\n      \"65\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0\n    },\n    b: {\n      \"0\": [0, 0],\n      \"1\": [0, 0],\n      \"2\": [0, 0],\n      \"3\": [0, 0],\n      \"4\": [0, 0],\n      \"5\": [0, 0],\n      \"6\": [0, 0],\n      \"7\": [0, 0],\n      \"8\": [0, 0],\n      \"9\": [0, 0],\n      \"10\": [0, 0],\n      \"11\": [0, 0],\n      \"12\": [0, 0],\n      \"13\": [0, 0],\n      \"14\": [0, 0],\n      \"15\": [0, 0],\n      \"16\": [0, 0],\n      \"17\": [0, 0]\n    },\n    inputSourceMap: {\n      version: 3,\n      file: \"monthly-reports-review-list.component.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\monthly-reports-review-list\\\\monthly-reports-review-list.component.ts\"],\n      names: [],\n      mappings: \";;;AAAA,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,iBAAiB,CAAC;AACzD,OAAO,EAAiB,SAAS,EAAU,SAAS,EAAE,MAAM,eAAe,CAAC;AAC5E,OAAO,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAC3D,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AACrD,OAAO,EAAE,kBAAkB,EAAE,MAAM,8BAA8B,CAAC;AAClE,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACzD,OAAO,EAAE,YAAY,EAAE,kBAAkB,EAAE,MAAM,6BAA6B,CAAC;AAC/E,OAAO,EAAE,OAAO,EAAE,aAAa,EAAQ,MAAM,wBAAwB,CAAC;AACtE,OAAO,EAAE,kBAAkB,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AAC7E,OAAO,EAAE,gBAAgB,EAAE,MAAM,2BAA2B,CAAC;AAC7D,OAAO,EAAE,4BAA4B,EAAE,MAAM,4GAA4G,CAAC;AAC1J,OAAO,EAAE,oBAAoB,EAAE,MAAM,uDAAuD,CAAC;AAC7F,OAAO,EAAE,WAAW,EAAE,MAAM,kCAAkC,CAAC;AAC/D,OAAO,EAAE,YAAY,EAAE,MAAM,gCAAgC,CAAC;AAC9D,OAAO,EAAE,iBAAiB,EAAE,MAAM,oDAAoD,CAAC;AACvF,OAAO,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AAChD,OAAO,EAAE,QAAQ,EAAE,MAAM,MAAM,CAAC;AAsBzB,IAAM,iCAAiC,GAAvC,MAAM,iCAAiC;IAuB5C,YACmB,MAAiB,EACjB,oBAA0C,EAC1C,OAA0B,EAC1B,KAAmB,EACnB,iBAAoC,EACpC,WAAwB;QALxB,WAAM,GAAN,MAAM,CAAW;QACjB,yBAAoB,GAApB,oBAAoB,CAAsB;QAC1C,YAAO,GAAP,OAAO,CAAmB;QAC1B,UAAK,GAAL,KAAK,CAAc;QACnB,sBAAiB,GAAjB,iBAAiB,CAAmB;QACpC,gBAAW,GAAX,WAAW,CAAa;QA1B3C,qBAAgB,GAAa;YAC3B,cAAc;YACd,gBAAgB;YAChB,cAAc;YACd,oBAAoB;YACpB,WAAW;YACX,SAAS;YACT,oBAAoB;YACpB,eAAe;YACf,YAAY;YACZ,qBAAqB;YACrB,SAAS;SACV,CAAC;QACF,eAAU,GAAG,IAAI,kBAAkB,EAAsC,CAAC;QAK1E,uBAAkB,GAAG,EAAE,CAAC;IASrB,CAAC;IAEJ,QAAQ;QACN,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED,eAAe;QACb,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC3C,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAEjC,IAAI,CAAC,UAAU,CAAC,mBAAmB,GAAG,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE;YACvD,QAAQ,QAAQ,EAAE,CAAC;gBACjB,KAAK,qBAAqB;oBACxB,IAAI,IAAI,CAAC,kBAAkB,EAAE,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBACjE,OAAO,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC;oBACvC,CAAC;oBACD,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAC;gBAC/C;oBACE,OAAO,IAAI,CACT,QAAoD,CAC3C,CAAC;YAChB,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;IAED,kBAAkB;QAChB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;QACtD,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;YAClE,OAAO;QACT,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACpB,IAAI,CAAC,iBAAiB;aACnB,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC;aAChC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;aACzC,SAAS,CAAC;YACT,IAAI,EAAE,CAAC,UAAU,EAAE,EAAE;gBACnB,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC,QAAQ,IAAI,EAAE,CAAC;gBACpD,IAAI,CAAC,oBAAoB;qBACtB,mCAAmC,CAAC,WAAW,CAAC,QAAQ,CAAC;qBACzD,SAAS,CAAC;oBACT,IAAI,EAAE,CAAC,OAAO,EAAE,EAAE;wBAChB,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,OAAO,CAAC;wBAE/B,IAAI,CAAC,cAAc,EAAE,CAAC;oBACxB,CAAC;oBACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;wBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,8BAA8B,CACtD,CAAC;oBACJ,CAAC;iBACF,CAAC,CAAC;YACP,CAAC;YACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,4CAA4C,CACpE,CAAC;YACJ,CAAC;SACF,CAAC,CAAC;IACP,CAAC;IAED,cAAc;QACZ,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;gBACd,MAAM,UAAU,GAAS;oBACvB,MAAM,EAAE,qBAAqB;oBAC7B,SAAS,EAAE,KAAK;iBACjB,CAAC;gBACF,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;gBACrC,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC;gBAC3C,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAEtC,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;YACnC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,WAAW,CAAC,KAAY;QACtB,MAAM,WAAW,GAAI,KAAK,CAAC,MAA2B,CAAC,KAAK,CAAC;QAC7D,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAC1D,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;YAC9B,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;QACxC,CAAC;IACH,CAAC;IAED,iBAAiB,CAAC,MAA0C;QAC1D,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,wCAAwC,CAAC,CAAC;YAC3D,OAAO;QACT,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;QAClC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;YAChE,OAAO;QACT,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACpB,IAAI,CAAC,oBAAoB;aACtB,OAAO,CAAC,QAAQ,CAAC;aACjB,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;aACzC,SAAS,CAAC;YACT,IAAI,EAAE,CAAC,UAAU,EAAE,EAAE;gBACnB,IAAI,CAAC,UAAU,CAAC,mBAAmB,EAAE,CAAC;oBACpC,UAAU,CAAC,mBAAmB,GAAG;wBAC/B,EAAE,EAAE,MAAM,CAAC,gBAAgB,IAAI,CAAC;wBAChC,IAAI,EAAE,MAAM,CAAC,kBAAkB,IAAI,EAAE;qBACtC,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,UAAU,CAAC,mBAAmB,CAAC,EAAE,GAAG,MAAM,CAAC,gBAAgB,IAAI,CAAC,CAAC;oBACjE,UAAU,CAAC,mBAAmB,CAAC,IAAI;wBACjC,MAAM,CAAC,kBAAkB,IAAI,EAAE,CAAC;gBACpC,CAAC;gBAED,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE;oBAC/D,KAAK,EAAE,MAAM;oBACb,MAAM,EAAE,MAAM;oBACd,QAAQ,EAAE,OAAO;oBACjB,SAAS,EAAE,OAAO;oBAClB,IAAI,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE;iBAC7B,CAAC,CAAC;gBAEH,SAAS,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,EAAE;oBAC3C,IAAI,MAAM,EAAE,CAAC;wBACX,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC5B,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,KAAK,CAAC,KAAK,EAAE,MAAM,IAAI,qCAAqC,CAC7D,CAAC;YACJ,CAAC;SACF,CAAC,CAAC;IACP,CAAC;;;;;;;;;;4BAnJA,SAAS,SAAC,YAAY;uBACtB,SAAS,SAAC,OAAO;;;AAnBP,iCAAiC;IAnB7C,SAAS,CAAC;QACT,QAAQ,EAAE,iCAAiC;QAC3C,8BAA2D;QAE3D,UAAU,EAAE,IAAI;QAChB,OAAO,EAAE;YACP,eAAe;YACf,aAAa;YACb,aAAa;YACb,kBAAkB;YAClB,cAAc;YACd,cAAc;YACd,aAAa;YACb,kBAAkB;YAClB,gBAAgB;YAChB,QAAQ;YACR,YAAY;SACb;;KACF,CAAC;GACW,iCAAiC,CAsK7C\",\n      sourcesContent: [\"import { CurrencyPipe, DatePipe } from '@angular/common';\\nimport { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';\\nimport { MatButtonModule } from '@angular/material/button';\\nimport { MatCardModule } from '@angular/material/card';\\nimport { MatDialog } from '@angular/material/dialog';\\nimport { MatFormFieldModule } from '@angular/material/form-field';\\nimport { MatIconModule } from '@angular/material/icon';\\nimport { MatInputModule } from '@angular/material/input';\\nimport { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';\\nimport { MatSort, MatSortModule, Sort } from '@angular/material/sort';\\nimport { MatTableDataSource, MatTableModule } from '@angular/material/table';\\nimport { MatTooltipModule } from '@angular/material/tooltip';\\nimport { MonthlyReportDialogComponent } from '@contractor-dashboard/components/monthly-reports-tab/monthly-report-dialog/monthly-report-dialog.component';\\nimport { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';\\nimport { AuthService } from '@core/auth/services/auth.service';\\nimport { AlertService } from '@shared/services/alert.service';\\nimport { SupervisorService } from '@supervisor-management/services/supervisor.service';\\nimport { NgxSpinnerService } from 'ngx-spinner';\\nimport { finalize } from 'rxjs';\\nimport { MonthlyReportSupervisorExportModel } from '@contractor-dashboard/models/monthly_report_supervisor_export.model';\\n\\n@Component({\\n  selector: 'app-monthly-reports-review-list',\\n  templateUrl: './monthly-reports-review-list.component.html',\\n  styleUrl: './monthly-reports-review-list.component.scss',\\n  standalone: true,\\n  imports: [\\n    MatButtonModule,\\n    MatCardModule,\\n    MatIconModule,\\n    MatFormFieldModule,\\n    MatInputModule,\\n    MatTableModule,\\n    MatSortModule,\\n    MatPaginatorModule,\\n    MatTooltipModule,\\n    DatePipe,\\n    CurrencyPipe,\\n  ],\\n})\\nexport class MonthlyReportsReviewListComponent\\n  implements OnInit, AfterViewInit\\n{\\n  displayedColumns: string[] = [\\n    'reportNumber',\\n    'contractNumber',\\n    'contractYear',\\n    'contractorFullName',\\n    'startDate',\\n    'endDate',\\n    'contractDependency',\\n    'contractGroup',\\n    'totalValue',\\n    'currentReviewStatus',\\n    'actions',\\n  ];\\n  dataSource = new MatTableDataSource<MonthlyReportSupervisorExportModel>();\\n\\n  @ViewChild(MatPaginator) paginator!: MatPaginator;\\n  @ViewChild(MatSort) sort!: MatSort;\\n\\n  supervisorFullName = '';\\n\\n  constructor(\\n    private readonly dialog: MatDialog,\\n    private readonly monthlyReportService: MonthlyReportService,\\n    private readonly spinner: NgxSpinnerService,\\n    private readonly alert: AlertService,\\n    private readonly supervisorService: SupervisorService,\\n    private readonly authService: AuthService,\\n  ) {}\\n\\n  ngOnInit(): void {\\n    this.loadMonthlyReports();\\n  }\\n\\n  ngAfterViewInit(): void {\\n    this.dataSource.paginator = this.paginator;\\n    this.dataSource.sort = this.sort;\\n\\n    this.dataSource.sortingDataAccessor = (item, property) => {\\n      switch (property) {\\n        case 'currentReviewStatus':\\n          if (item.review_status_name?.toLowerCase().includes('pendiente')) {\\n            return '0' + item.review_status_name;\\n          }\\n          return '1' + (item.review_status_name || '');\\n        default:\\n          return item[\\n            property as keyof MonthlyReportSupervisorExportModel\\n          ] as string;\\n      }\\n    };\\n  }\\n\\n  loadMonthlyReports(): void {\\n    const currentUser = this.authService.getCurrentUser();\\n    if (!currentUser) {\\n      this.alert.error('No se encontr\\xF3 informaci\\xF3n del usuario actual');\\n      return;\\n    }\\n\\n    this.spinner.show();\\n    this.supervisorService\\n      .getByEmail(currentUser.username)\\n      .pipe(finalize(() => this.spinner.hide()))\\n      .subscribe({\\n        next: (supervisor) => {\\n          this.supervisorFullName = supervisor.fullName || '';\\n          this.monthlyReportService\\n            .getFilteredReportsBySupervisorEmail(currentUser.username)\\n            .subscribe({\\n              next: (reports) => {\\n                this.dataSource.data = reports;\\n\\n                this.setDefaultSort();\\n              },\\n              error: (error) => {\\n                this.alert.error(\\n                  error.error?.detail ?? 'Error al cargar los informes',\\n                );\\n              },\\n            });\\n        },\\n        error: (error) => {\\n          this.alert.error(\\n            error.error?.detail ?? 'Error al cargar informaci\\xF3n del supervisor',\\n          );\\n        },\\n      });\\n  }\\n\\n  setDefaultSort(): void {\\n    setTimeout(() => {\\n      if (this.sort) {\\n        const statusSort: Sort = {\\n          active: 'currentReviewStatus',\\n          direction: 'asc',\\n        };\\n        this.sort.active = statusSort.active;\\n        this.sort.direction = statusSort.direction;\\n        this.sort.sortChange.emit(statusSort);\\n\\n        this.dataSource.sort = this.sort;\\n      }\\n    });\\n  }\\n\\n  applyFilter(event: Event): void {\\n    const filterValue = (event.target as HTMLInputElement).value;\\n    this.dataSource.filter = filterValue.trim().toLowerCase();\\n    if (this.dataSource.paginator) {\\n      this.dataSource.paginator.firstPage();\\n    }\\n  }\\n\\n  openReportDetails(report: MonthlyReportSupervisorExportModel): void {\\n    if (!report) {\\n      this.alert.error('No se encontr\\xF3 informaci\\xF3n del informe');\\n      return;\\n    }\\n\\n    const reportId = report.report_id;\\n    if (!reportId) {\\n      this.alert.error('No se encontr\\xF3 el identificador del informe');\\n      return;\\n    }\\n\\n    this.spinner.show();\\n    this.monthlyReportService\\n      .getById(reportId)\\n      .pipe(finalize(() => this.spinner.hide()))\\n      .subscribe({\\n        next: (fullReport) => {\\n          if (!fullReport.currentReviewStatus) {\\n            fullReport.currentReviewStatus = {\\n              id: report.review_status_id ?? 0,\\n              name: report.review_status_name ?? '',\\n            };\\n          } else {\\n            fullReport.currentReviewStatus.id = report.review_status_id ?? 0;\\n            fullReport.currentReviewStatus.name =\\n              report.review_status_name ?? '';\\n          }\\n\\n          const dialogRef = this.dialog.open(MonthlyReportDialogComponent, {\\n            width: '90vw',\\n            height: '90vh',\\n            maxWidth: '100vw',\\n            maxHeight: '100vh',\\n            data: { report: fullReport },\\n          });\\n\\n          dialogRef.afterClosed().subscribe((result) => {\\n            if (result) {\\n              this.loadMonthlyReports();\\n            }\\n          });\\n        },\\n        error: (error) => {\\n          this.alert.error(\\n            error.error?.detail ?? 'Error al cargar el informe completo',\\n          );\\n        },\\n      });\\n  }\\n}\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"68e7a732dd2409f732d85b040b5e65f69a22cbfc\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_6fjwihlrw = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_6fjwihlrw();\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./monthly-reports-review-list.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./monthly-reports-review-list.component.scss?ngResource\";\nimport { CurrencyPipe, DatePipe } from '@angular/common';\nimport { Component, ViewChild } from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatDialog } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSort, MatSortModule } from '@angular/material/sort';\nimport { MatTableDataSource, MatTableModule } from '@angular/material/table';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MonthlyReportDialogComponent } from '@contractor-dashboard/components/monthly-reports-tab/monthly-report-dialog/monthly-report-dialog.component';\nimport { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { SupervisorService } from '@supervisor-management/services/supervisor.service';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { finalize } from 'rxjs';\ncov_6fjwihlrw().s[0]++;\nlet MonthlyReportsReviewListComponent = class MonthlyReportsReviewListComponent {\n  constructor(dialog, monthlyReportService, spinner, alert, supervisorService, authService) {\n    cov_6fjwihlrw().f[0]++;\n    cov_6fjwihlrw().s[1]++;\n    this.dialog = dialog;\n    cov_6fjwihlrw().s[2]++;\n    this.monthlyReportService = monthlyReportService;\n    cov_6fjwihlrw().s[3]++;\n    this.spinner = spinner;\n    cov_6fjwihlrw().s[4]++;\n    this.alert = alert;\n    cov_6fjwihlrw().s[5]++;\n    this.supervisorService = supervisorService;\n    cov_6fjwihlrw().s[6]++;\n    this.authService = authService;\n    cov_6fjwihlrw().s[7]++;\n    this.displayedColumns = ['reportNumber', 'contractNumber', 'contractYear', 'contractorFullName', 'startDate', 'endDate', 'contractDependency', 'contractGroup', 'totalValue', 'currentReviewStatus', 'actions'];\n    cov_6fjwihlrw().s[8]++;\n    this.dataSource = new MatTableDataSource();\n    cov_6fjwihlrw().s[9]++;\n    this.supervisorFullName = '';\n  }\n  ngOnInit() {\n    cov_6fjwihlrw().f[1]++;\n    cov_6fjwihlrw().s[10]++;\n    this.loadMonthlyReports();\n  }\n  ngAfterViewInit() {\n    cov_6fjwihlrw().f[2]++;\n    cov_6fjwihlrw().s[11]++;\n    this.dataSource.paginator = this.paginator;\n    cov_6fjwihlrw().s[12]++;\n    this.dataSource.sort = this.sort;\n    cov_6fjwihlrw().s[13]++;\n    this.dataSource.sortingDataAccessor = (item, property) => {\n      cov_6fjwihlrw().f[3]++;\n      cov_6fjwihlrw().s[14]++;\n      switch (property) {\n        case 'currentReviewStatus':\n          cov_6fjwihlrw().b[0][0]++;\n          cov_6fjwihlrw().s[15]++;\n          if (item.review_status_name?.toLowerCase().includes('pendiente')) {\n            cov_6fjwihlrw().b[1][0]++;\n            cov_6fjwihlrw().s[16]++;\n            return '0' + item.review_status_name;\n          } else {\n            cov_6fjwihlrw().b[1][1]++;\n          }\n          cov_6fjwihlrw().s[17]++;\n          return '1' + ((cov_6fjwihlrw().b[2][0]++, item.review_status_name) || (cov_6fjwihlrw().b[2][1]++, ''));\n        default:\n          cov_6fjwihlrw().b[0][1]++;\n          cov_6fjwihlrw().s[18]++;\n          return item[property];\n      }\n    };\n  }\n  loadMonthlyReports() {\n    cov_6fjwihlrw().f[4]++;\n    const currentUser = (cov_6fjwihlrw().s[19]++, this.authService.getCurrentUser());\n    cov_6fjwihlrw().s[20]++;\n    if (!currentUser) {\n      cov_6fjwihlrw().b[3][0]++;\n      cov_6fjwihlrw().s[21]++;\n      this.alert.error('No se encontró información del usuario actual');\n      cov_6fjwihlrw().s[22]++;\n      return;\n    } else {\n      cov_6fjwihlrw().b[3][1]++;\n    }\n    cov_6fjwihlrw().s[23]++;\n    this.spinner.show();\n    cov_6fjwihlrw().s[24]++;\n    this.supervisorService.getByEmail(currentUser.username).pipe(finalize(() => {\n      cov_6fjwihlrw().f[5]++;\n      cov_6fjwihlrw().s[25]++;\n      return this.spinner.hide();\n    })).subscribe({\n      next: supervisor => {\n        cov_6fjwihlrw().f[6]++;\n        cov_6fjwihlrw().s[26]++;\n        this.supervisorFullName = (cov_6fjwihlrw().b[4][0]++, supervisor.fullName) || (cov_6fjwihlrw().b[4][1]++, '');\n        cov_6fjwihlrw().s[27]++;\n        this.monthlyReportService.getFilteredReportsBySupervisorEmail(currentUser.username).subscribe({\n          next: reports => {\n            cov_6fjwihlrw().f[7]++;\n            cov_6fjwihlrw().s[28]++;\n            this.dataSource.data = reports;\n            cov_6fjwihlrw().s[29]++;\n            this.setDefaultSort();\n          },\n          error: error => {\n            cov_6fjwihlrw().f[8]++;\n            cov_6fjwihlrw().s[30]++;\n            this.alert.error((cov_6fjwihlrw().b[5][0]++, error.error?.detail) ?? (cov_6fjwihlrw().b[5][1]++, 'Error al cargar los informes'));\n          }\n        });\n      },\n      error: error => {\n        cov_6fjwihlrw().f[9]++;\n        cov_6fjwihlrw().s[31]++;\n        this.alert.error((cov_6fjwihlrw().b[6][0]++, error.error?.detail) ?? (cov_6fjwihlrw().b[6][1]++, 'Error al cargar información del supervisor'));\n      }\n    });\n  }\n  setDefaultSort() {\n    cov_6fjwihlrw().f[10]++;\n    cov_6fjwihlrw().s[32]++;\n    setTimeout(() => {\n      cov_6fjwihlrw().f[11]++;\n      cov_6fjwihlrw().s[33]++;\n      if (this.sort) {\n        cov_6fjwihlrw().b[7][0]++;\n        const statusSort = (cov_6fjwihlrw().s[34]++, {\n          active: 'currentReviewStatus',\n          direction: 'asc'\n        });\n        cov_6fjwihlrw().s[35]++;\n        this.sort.active = statusSort.active;\n        cov_6fjwihlrw().s[36]++;\n        this.sort.direction = statusSort.direction;\n        cov_6fjwihlrw().s[37]++;\n        this.sort.sortChange.emit(statusSort);\n        cov_6fjwihlrw().s[38]++;\n        this.dataSource.sort = this.sort;\n      } else {\n        cov_6fjwihlrw().b[7][1]++;\n      }\n    });\n  }\n  applyFilter(event) {\n    cov_6fjwihlrw().f[12]++;\n    const filterValue = (cov_6fjwihlrw().s[39]++, event.target.value);\n    cov_6fjwihlrw().s[40]++;\n    this.dataSource.filter = filterValue.trim().toLowerCase();\n    cov_6fjwihlrw().s[41]++;\n    if (this.dataSource.paginator) {\n      cov_6fjwihlrw().b[8][0]++;\n      cov_6fjwihlrw().s[42]++;\n      this.dataSource.paginator.firstPage();\n    } else {\n      cov_6fjwihlrw().b[8][1]++;\n    }\n  }\n  openReportDetails(report) {\n    cov_6fjwihlrw().f[13]++;\n    cov_6fjwihlrw().s[43]++;\n    if (!report) {\n      cov_6fjwihlrw().b[9][0]++;\n      cov_6fjwihlrw().s[44]++;\n      this.alert.error('No se encontró información del informe');\n      cov_6fjwihlrw().s[45]++;\n      return;\n    } else {\n      cov_6fjwihlrw().b[9][1]++;\n    }\n    const reportId = (cov_6fjwihlrw().s[46]++, report.report_id);\n    cov_6fjwihlrw().s[47]++;\n    if (!reportId) {\n      cov_6fjwihlrw().b[10][0]++;\n      cov_6fjwihlrw().s[48]++;\n      this.alert.error('No se encontró el identificador del informe');\n      cov_6fjwihlrw().s[49]++;\n      return;\n    } else {\n      cov_6fjwihlrw().b[10][1]++;\n    }\n    cov_6fjwihlrw().s[50]++;\n    this.spinner.show();\n    cov_6fjwihlrw().s[51]++;\n    this.monthlyReportService.getById(reportId).pipe(finalize(() => {\n      cov_6fjwihlrw().f[14]++;\n      cov_6fjwihlrw().s[52]++;\n      return this.spinner.hide();\n    })).subscribe({\n      next: fullReport => {\n        cov_6fjwihlrw().f[15]++;\n        cov_6fjwihlrw().s[53]++;\n        if (!fullReport.currentReviewStatus) {\n          cov_6fjwihlrw().b[11][0]++;\n          cov_6fjwihlrw().s[54]++;\n          fullReport.currentReviewStatus = {\n            id: (cov_6fjwihlrw().b[12][0]++, report.review_status_id) ?? (cov_6fjwihlrw().b[12][1]++, 0),\n            name: (cov_6fjwihlrw().b[13][0]++, report.review_status_name) ?? (cov_6fjwihlrw().b[13][1]++, '')\n          };\n        } else {\n          cov_6fjwihlrw().b[11][1]++;\n          cov_6fjwihlrw().s[55]++;\n          fullReport.currentReviewStatus.id = (cov_6fjwihlrw().b[14][0]++, report.review_status_id) ?? (cov_6fjwihlrw().b[14][1]++, 0);\n          cov_6fjwihlrw().s[56]++;\n          fullReport.currentReviewStatus.name = (cov_6fjwihlrw().b[15][0]++, report.review_status_name) ?? (cov_6fjwihlrw().b[15][1]++, '');\n        }\n        const dialogRef = (cov_6fjwihlrw().s[57]++, this.dialog.open(MonthlyReportDialogComponent, {\n          width: '90vw',\n          height: '90vh',\n          maxWidth: '100vw',\n          maxHeight: '100vh',\n          data: {\n            report: fullReport\n          }\n        }));\n        cov_6fjwihlrw().s[58]++;\n        dialogRef.afterClosed().subscribe(result => {\n          cov_6fjwihlrw().f[16]++;\n          cov_6fjwihlrw().s[59]++;\n          if (result) {\n            cov_6fjwihlrw().b[16][0]++;\n            cov_6fjwihlrw().s[60]++;\n            this.loadMonthlyReports();\n          } else {\n            cov_6fjwihlrw().b[16][1]++;\n          }\n        });\n      },\n      error: error => {\n        cov_6fjwihlrw().f[17]++;\n        cov_6fjwihlrw().s[61]++;\n        this.alert.error((cov_6fjwihlrw().b[17][0]++, error.error?.detail) ?? (cov_6fjwihlrw().b[17][1]++, 'Error al cargar el informe completo'));\n      }\n    });\n  }\n  static {\n    cov_6fjwihlrw().s[62]++;\n    this.ctorParameters = () => {\n      cov_6fjwihlrw().f[18]++;\n      cov_6fjwihlrw().s[63]++;\n      return [{\n        type: MatDialog\n      }, {\n        type: MonthlyReportService\n      }, {\n        type: NgxSpinnerService\n      }, {\n        type: AlertService\n      }, {\n        type: SupervisorService\n      }, {\n        type: AuthService\n      }];\n    };\n  }\n  static {\n    cov_6fjwihlrw().s[64]++;\n    this.propDecorators = {\n      paginator: [{\n        type: ViewChild,\n        args: [MatPaginator]\n      }],\n      sort: [{\n        type: ViewChild,\n        args: [MatSort]\n      }]\n    };\n  }\n};\ncov_6fjwihlrw().s[65]++;\nMonthlyReportsReviewListComponent = __decorate([Component({\n  selector: 'app-monthly-reports-review-list',\n  template: __NG_CLI_RESOURCE__0,\n  standalone: true,\n  imports: [MatButtonModule, MatCardModule, MatIconModule, MatFormFieldModule, MatInputModule, MatTableModule, MatSortModule, MatPaginatorModule, MatTooltipModule, DatePipe, CurrencyPipe],\n  styles: [__NG_CLI_RESOURCE__1]\n})], MonthlyReportsReviewListComponent);\nexport { MonthlyReportsReviewListComponent };", "map": {"version": 3, "names": ["cov_6fjwihlrw", "actualCoverage", "C<PERSON><PERSON>cyPipe", "DatePipe", "Component", "ViewChild", "MatButtonModule", "MatCardModule", "MatDialog", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatPaginator", "MatPaginatorModule", "MatSort", "MatSortModule", "MatTableDataSource", "MatTableModule", "MatTooltipModule", "MonthlyReportDialogComponent", "MonthlyReportService", "AuthService", "AlertService", "SupervisorService", "NgxSpinnerService", "finalize", "s", "MonthlyReportsReviewListComponent", "constructor", "dialog", "monthlyReportService", "spinner", "alert", "supervisorService", "authService", "f", "displayedColumns", "dataSource", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "loadMonthlyReports", "ngAfterViewInit", "paginator", "sort", "sortingDataAccessor", "item", "property", "b", "review_status_name", "toLowerCase", "includes", "currentUser", "getCurrentUser", "error", "show", "getByEmail", "username", "pipe", "hide", "subscribe", "next", "supervisor", "fullName", "getFilteredReportsBySupervisorEmail", "reports", "data", "setDefaultSort", "detail", "setTimeout", "statusSort", "active", "direction", "sortChange", "emit", "applyFilter", "event", "filterValue", "target", "value", "filter", "trim", "firstPage", "openReportDetails", "report", "reportId", "report_id", "getById", "fullReport", "currentReviewStatus", "id", "review_status_id", "name", "dialogRef", "open", "width", "height", "max<PERSON><PERSON><PERSON>", "maxHeight", "afterClosed", "result", "args", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\monthly-reports-review-list\\monthly-reports-review-list.component.ts"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>cy<PERSON><PERSON>e, DatePipe } from '@angular/common';\nimport { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatDialog } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSort, MatSortModule, Sort } from '@angular/material/sort';\nimport { MatTableDataSource, MatTableModule } from '@angular/material/table';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MonthlyReportDialogComponent } from '@contractor-dashboard/components/monthly-reports-tab/monthly-report-dialog/monthly-report-dialog.component';\nimport { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { SupervisorService } from '@supervisor-management/services/supervisor.service';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { finalize } from 'rxjs';\nimport { MonthlyReportSupervisorExportModel } from '@contractor-dashboard/models/monthly_report_supervisor_export.model';\n\n@Component({\n  selector: 'app-monthly-reports-review-list',\n  templateUrl: './monthly-reports-review-list.component.html',\n  styleUrl: './monthly-reports-review-list.component.scss',\n  standalone: true,\n  imports: [\n    MatButtonModule,\n    MatCardModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatTableModule,\n    MatSortModule,\n    MatPaginatorModule,\n    MatTooltipModule,\n    DatePipe,\n    CurrencyPipe,\n  ],\n})\nexport class MonthlyReportsReviewListComponent\n  implements OnInit, AfterViewInit\n{\n  displayedColumns: string[] = [\n    'reportNumber',\n    'contractNumber',\n    'contractYear',\n    'contractorFullName',\n    'startDate',\n    'endDate',\n    'contractDependency',\n    'contractGroup',\n    'totalValue',\n    'currentReviewStatus',\n    'actions',\n  ];\n  dataSource = new MatTableDataSource<MonthlyReportSupervisorExportModel>();\n\n  @ViewChild(MatPaginator) paginator!: MatPaginator;\n  @ViewChild(MatSort) sort!: MatSort;\n\n  supervisorFullName = '';\n\n  constructor(\n    private readonly dialog: MatDialog,\n    private readonly monthlyReportService: MonthlyReportService,\n    private readonly spinner: NgxSpinnerService,\n    private readonly alert: AlertService,\n    private readonly supervisorService: SupervisorService,\n    private readonly authService: AuthService,\n  ) {}\n\n  ngOnInit(): void {\n    this.loadMonthlyReports();\n  }\n\n  ngAfterViewInit(): void {\n    this.dataSource.paginator = this.paginator;\n    this.dataSource.sort = this.sort;\n\n    this.dataSource.sortingDataAccessor = (item, property) => {\n      switch (property) {\n        case 'currentReviewStatus':\n          if (item.review_status_name?.toLowerCase().includes('pendiente')) {\n            return '0' + item.review_status_name;\n          }\n          return '1' + (item.review_status_name || '');\n        default:\n          return item[\n            property as keyof MonthlyReportSupervisorExportModel\n          ] as string;\n      }\n    };\n  }\n\n  loadMonthlyReports(): void {\n    const currentUser = this.authService.getCurrentUser();\n    if (!currentUser) {\n      this.alert.error('No se encontró información del usuario actual');\n      return;\n    }\n\n    this.spinner.show();\n    this.supervisorService\n      .getByEmail(currentUser.username)\n      .pipe(finalize(() => this.spinner.hide()))\n      .subscribe({\n        next: (supervisor) => {\n          this.supervisorFullName = supervisor.fullName || '';\n          this.monthlyReportService\n            .getFilteredReportsBySupervisorEmail(currentUser.username)\n            .subscribe({\n              next: (reports) => {\n                this.dataSource.data = reports;\n\n                this.setDefaultSort();\n              },\n              error: (error) => {\n                this.alert.error(\n                  error.error?.detail ?? 'Error al cargar los informes',\n                );\n              },\n            });\n        },\n        error: (error) => {\n          this.alert.error(\n            error.error?.detail ?? 'Error al cargar información del supervisor',\n          );\n        },\n      });\n  }\n\n  setDefaultSort(): void {\n    setTimeout(() => {\n      if (this.sort) {\n        const statusSort: Sort = {\n          active: 'currentReviewStatus',\n          direction: 'asc',\n        };\n        this.sort.active = statusSort.active;\n        this.sort.direction = statusSort.direction;\n        this.sort.sortChange.emit(statusSort);\n\n        this.dataSource.sort = this.sort;\n      }\n    });\n  }\n\n  applyFilter(event: Event): void {\n    const filterValue = (event.target as HTMLInputElement).value;\n    this.dataSource.filter = filterValue.trim().toLowerCase();\n    if (this.dataSource.paginator) {\n      this.dataSource.paginator.firstPage();\n    }\n  }\n\n  openReportDetails(report: MonthlyReportSupervisorExportModel): void {\n    if (!report) {\n      this.alert.error('No se encontró información del informe');\n      return;\n    }\n\n    const reportId = report.report_id;\n    if (!reportId) {\n      this.alert.error('No se encontró el identificador del informe');\n      return;\n    }\n\n    this.spinner.show();\n    this.monthlyReportService\n      .getById(reportId)\n      .pipe(finalize(() => this.spinner.hide()))\n      .subscribe({\n        next: (fullReport) => {\n          if (!fullReport.currentReviewStatus) {\n            fullReport.currentReviewStatus = {\n              id: report.review_status_id ?? 0,\n              name: report.review_status_name ?? '',\n            };\n          } else {\n            fullReport.currentReviewStatus.id = report.review_status_id ?? 0;\n            fullReport.currentReviewStatus.name =\n              report.review_status_name ?? '';\n          }\n\n          const dialogRef = this.dialog.open(MonthlyReportDialogComponent, {\n            width: '90vw',\n            height: '90vh',\n            maxWidth: '100vw',\n            maxHeight: '100vh',\n            data: { report: fullReport },\n          });\n\n          dialogRef.afterClosed().subscribe((result) => {\n            if (result) {\n              this.loadMonthlyReports();\n            }\n          });\n        },\n        error: (error) => {\n          this.alert.error(\n            error.error?.detail ?? 'Error al cargar el informe completo',\n          );\n        },\n      });\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAYS;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;;;;AAZT,SAASE,YAAY,EAAEC,QAAQ,QAAQ,iBAAiB;AACxD,SAAwBC,SAAS,EAAUC,SAAS,QAAQ,eAAe;AAC3E,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,6BAA6B;AAC9E,SAASC,OAAO,EAAEC,aAAa,QAAc,wBAAwB;AACrE,SAASC,kBAAkB,EAAEC,cAAc,QAAQ,yBAAyB;AAC5E,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,4BAA4B,QAAQ,4GAA4G;AACzJ,SAASC,oBAAoB,QAAQ,uDAAuD;AAC5F,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,iBAAiB,QAAQ,oDAAoD;AACtF,SAASC,iBAAiB,QAAQ,aAAa;AAC/C,SAASC,QAAQ,QAAQ,MAAM;AAACzB,aAAA,GAAA0B,CAAA;AAsBzB,IAAMC,iCAAiC,GAAvC,MAAMA,iCAAiC;EAuB5CC,YACmBC,MAAiB,EACjBC,oBAA0C,EAC1CC,OAA0B,EAC1BC,KAAmB,EACnBC,iBAAoC,EACpCC,WAAwB;IAAAlC,aAAA,GAAAmC,CAAA;IAAAnC,aAAA,GAAA0B,CAAA;IALxB,KAAAG,MAAM,GAANA,MAAM;IAAW7B,aAAA,GAAA0B,CAAA;IACjB,KAAAI,oBAAoB,GAApBA,oBAAoB;IAAsB9B,aAAA,GAAA0B,CAAA;IAC1C,KAAAK,OAAO,GAAPA,OAAO;IAAmB/B,aAAA,GAAA0B,CAAA;IAC1B,KAAAM,KAAK,GAALA,KAAK;IAAchC,aAAA,GAAA0B,CAAA;IACnB,KAAAO,iBAAiB,GAAjBA,iBAAiB;IAAmBjC,aAAA,GAAA0B,CAAA;IACpC,KAAAQ,WAAW,GAAXA,WAAW;IAAalC,aAAA,GAAA0B,CAAA;IA1B3C,KAAAU,gBAAgB,GAAa,CAC3B,cAAc,EACd,gBAAgB,EAChB,cAAc,EACd,oBAAoB,EACpB,WAAW,EACX,SAAS,EACT,oBAAoB,EACpB,eAAe,EACf,YAAY,EACZ,qBAAqB,EACrB,SAAS,CACV;IAACpC,aAAA,GAAA0B,CAAA;IACF,KAAAW,UAAU,GAAG,IAAIrB,kBAAkB,EAAsC;IAAChB,aAAA,GAAA0B,CAAA;IAK1E,KAAAY,kBAAkB,GAAG,EAAE;EASpB;EAEHC,QAAQA,CAAA;IAAAvC,aAAA,GAAAmC,CAAA;IAAAnC,aAAA,GAAA0B,CAAA;IACN,IAAI,CAACc,kBAAkB,EAAE;EAC3B;EAEAC,eAAeA,CAAA;IAAAzC,aAAA,GAAAmC,CAAA;IAAAnC,aAAA,GAAA0B,CAAA;IACb,IAAI,CAACW,UAAU,CAACK,SAAS,GAAG,IAAI,CAACA,SAAS;IAAC1C,aAAA,GAAA0B,CAAA;IAC3C,IAAI,CAACW,UAAU,CAACM,IAAI,GAAG,IAAI,CAACA,IAAI;IAAC3C,aAAA,GAAA0B,CAAA;IAEjC,IAAI,CAACW,UAAU,CAACO,mBAAmB,GAAG,CAACC,IAAI,EAAEC,QAAQ,KAAI;MAAA9C,aAAA,GAAAmC,CAAA;MAAAnC,aAAA,GAAA0B,CAAA;MACvD,QAAQoB,QAAQ;QACd,KAAK,qBAAqB;UAAA9C,aAAA,GAAA+C,CAAA;UAAA/C,aAAA,GAAA0B,CAAA;UACxB,IAAImB,IAAI,CAACG,kBAAkB,EAAEC,WAAW,EAAE,CAACC,QAAQ,CAAC,WAAW,CAAC,EAAE;YAAAlD,aAAA,GAAA+C,CAAA;YAAA/C,aAAA,GAAA0B,CAAA;YAChE,OAAO,GAAG,GAAGmB,IAAI,CAACG,kBAAkB;UACtC,CAAC;YAAAhD,aAAA,GAAA+C,CAAA;UAAA;UAAA/C,aAAA,GAAA0B,CAAA;UACD,OAAO,GAAG,IAAI,CAAA1B,aAAA,GAAA+C,CAAA,UAAAF,IAAI,CAACG,kBAAkB,MAAAhD,aAAA,GAAA+C,CAAA,UAAI,EAAE,EAAC;QAC9C;UAAA/C,aAAA,GAAA+C,CAAA;UAAA/C,aAAA,GAAA0B,CAAA;UACE,OAAOmB,IAAI,CACTC,QAAoD,CAC3C;MACf;IACF,CAAC;EACH;EAEAN,kBAAkBA,CAAA;IAAAxC,aAAA,GAAAmC,CAAA;IAChB,MAAMgB,WAAW,IAAAnD,aAAA,GAAA0B,CAAA,QAAG,IAAI,CAACQ,WAAW,CAACkB,cAAc,EAAE;IAACpD,aAAA,GAAA0B,CAAA;IACtD,IAAI,CAACyB,WAAW,EAAE;MAAAnD,aAAA,GAAA+C,CAAA;MAAA/C,aAAA,GAAA0B,CAAA;MAChB,IAAI,CAACM,KAAK,CAACqB,KAAK,CAAC,+CAA+C,CAAC;MAACrD,aAAA,GAAA0B,CAAA;MAClE;IACF,CAAC;MAAA1B,aAAA,GAAA+C,CAAA;IAAA;IAAA/C,aAAA,GAAA0B,CAAA;IAED,IAAI,CAACK,OAAO,CAACuB,IAAI,EAAE;IAACtD,aAAA,GAAA0B,CAAA;IACpB,IAAI,CAACO,iBAAiB,CACnBsB,UAAU,CAACJ,WAAW,CAACK,QAAQ,CAAC,CAChCC,IAAI,CAAChC,QAAQ,CAAC,MAAM;MAAAzB,aAAA,GAAAmC,CAAA;MAAAnC,aAAA,GAAA0B,CAAA;MAAA,WAAI,CAACK,OAAO,CAAC2B,IAAI,EAAE;IAAF,CAAE,CAAC,CAAC,CACzCC,SAAS,CAAC;MACTC,IAAI,EAAGC,UAAU,IAAI;QAAA7D,aAAA,GAAAmC,CAAA;QAAAnC,aAAA,GAAA0B,CAAA;QACnB,IAAI,CAACY,kBAAkB,GAAG,CAAAtC,aAAA,GAAA+C,CAAA,UAAAc,UAAU,CAACC,QAAQ,MAAA9D,aAAA,GAAA+C,CAAA,UAAI,EAAE;QAAC/C,aAAA,GAAA0B,CAAA;QACpD,IAAI,CAACI,oBAAoB,CACtBiC,mCAAmC,CAACZ,WAAW,CAACK,QAAQ,CAAC,CACzDG,SAAS,CAAC;UACTC,IAAI,EAAGI,OAAO,IAAI;YAAAhE,aAAA,GAAAmC,CAAA;YAAAnC,aAAA,GAAA0B,CAAA;YAChB,IAAI,CAACW,UAAU,CAAC4B,IAAI,GAAGD,OAAO;YAAChE,aAAA,GAAA0B,CAAA;YAE/B,IAAI,CAACwC,cAAc,EAAE;UACvB,CAAC;UACDb,KAAK,EAAGA,KAAK,IAAI;YAAArD,aAAA,GAAAmC,CAAA;YAAAnC,aAAA,GAAA0B,CAAA;YACf,IAAI,CAACM,KAAK,CAACqB,KAAK,CACd,CAAArD,aAAA,GAAA+C,CAAA,UAAAM,KAAK,CAACA,KAAK,EAAEc,MAAM,MAAAnE,aAAA,GAAA+C,CAAA,UAAI,8BAA8B,EACtD;UACH;SACD,CAAC;MACN,CAAC;MACDM,KAAK,EAAGA,KAAK,IAAI;QAAArD,aAAA,GAAAmC,CAAA;QAAAnC,aAAA,GAAA0B,CAAA;QACf,IAAI,CAACM,KAAK,CAACqB,KAAK,CACd,CAAArD,aAAA,GAAA+C,CAAA,UAAAM,KAAK,CAACA,KAAK,EAAEc,MAAM,MAAAnE,aAAA,GAAA+C,CAAA,UAAI,4CAA4C,EACpE;MACH;KACD,CAAC;EACN;EAEAmB,cAAcA,CAAA;IAAAlE,aAAA,GAAAmC,CAAA;IAAAnC,aAAA,GAAA0B,CAAA;IACZ0C,UAAU,CAAC,MAAK;MAAApE,aAAA,GAAAmC,CAAA;MAAAnC,aAAA,GAAA0B,CAAA;MACd,IAAI,IAAI,CAACiB,IAAI,EAAE;QAAA3C,aAAA,GAAA+C,CAAA;QACb,MAAMsB,UAAU,IAAArE,aAAA,GAAA0B,CAAA,QAAS;UACvB4C,MAAM,EAAE,qBAAqB;UAC7BC,SAAS,EAAE;SACZ;QAACvE,aAAA,GAAA0B,CAAA;QACF,IAAI,CAACiB,IAAI,CAAC2B,MAAM,GAAGD,UAAU,CAACC,MAAM;QAACtE,aAAA,GAAA0B,CAAA;QACrC,IAAI,CAACiB,IAAI,CAAC4B,SAAS,GAAGF,UAAU,CAACE,SAAS;QAACvE,aAAA,GAAA0B,CAAA;QAC3C,IAAI,CAACiB,IAAI,CAAC6B,UAAU,CAACC,IAAI,CAACJ,UAAU,CAAC;QAACrE,aAAA,GAAA0B,CAAA;QAEtC,IAAI,CAACW,UAAU,CAACM,IAAI,GAAG,IAAI,CAACA,IAAI;MAClC,CAAC;QAAA3C,aAAA,GAAA+C,CAAA;MAAA;IACH,CAAC,CAAC;EACJ;EAEA2B,WAAWA,CAACC,KAAY;IAAA3E,aAAA,GAAAmC,CAAA;IACtB,MAAMyC,WAAW,IAAA5E,aAAA,GAAA0B,CAAA,QAAIiD,KAAK,CAACE,MAA2B,CAACC,KAAK;IAAC9E,aAAA,GAAA0B,CAAA;IAC7D,IAAI,CAACW,UAAU,CAAC0C,MAAM,GAAGH,WAAW,CAACI,IAAI,EAAE,CAAC/B,WAAW,EAAE;IAACjD,aAAA,GAAA0B,CAAA;IAC1D,IAAI,IAAI,CAACW,UAAU,CAACK,SAAS,EAAE;MAAA1C,aAAA,GAAA+C,CAAA;MAAA/C,aAAA,GAAA0B,CAAA;MAC7B,IAAI,CAACW,UAAU,CAACK,SAAS,CAACuC,SAAS,EAAE;IACvC,CAAC;MAAAjF,aAAA,GAAA+C,CAAA;IAAA;EACH;EAEAmC,iBAAiBA,CAACC,MAA0C;IAAAnF,aAAA,GAAAmC,CAAA;IAAAnC,aAAA,GAAA0B,CAAA;IAC1D,IAAI,CAACyD,MAAM,EAAE;MAAAnF,aAAA,GAAA+C,CAAA;MAAA/C,aAAA,GAAA0B,CAAA;MACX,IAAI,CAACM,KAAK,CAACqB,KAAK,CAAC,wCAAwC,CAAC;MAACrD,aAAA,GAAA0B,CAAA;MAC3D;IACF,CAAC;MAAA1B,aAAA,GAAA+C,CAAA;IAAA;IAED,MAAMqC,QAAQ,IAAApF,aAAA,GAAA0B,CAAA,QAAGyD,MAAM,CAACE,SAAS;IAACrF,aAAA,GAAA0B,CAAA;IAClC,IAAI,CAAC0D,QAAQ,EAAE;MAAApF,aAAA,GAAA+C,CAAA;MAAA/C,aAAA,GAAA0B,CAAA;MACb,IAAI,CAACM,KAAK,CAACqB,KAAK,CAAC,6CAA6C,CAAC;MAACrD,aAAA,GAAA0B,CAAA;MAChE;IACF,CAAC;MAAA1B,aAAA,GAAA+C,CAAA;IAAA;IAAA/C,aAAA,GAAA0B,CAAA;IAED,IAAI,CAACK,OAAO,CAACuB,IAAI,EAAE;IAACtD,aAAA,GAAA0B,CAAA;IACpB,IAAI,CAACI,oBAAoB,CACtBwD,OAAO,CAACF,QAAQ,CAAC,CACjB3B,IAAI,CAAChC,QAAQ,CAAC,MAAM;MAAAzB,aAAA,GAAAmC,CAAA;MAAAnC,aAAA,GAAA0B,CAAA;MAAA,WAAI,CAACK,OAAO,CAAC2B,IAAI,EAAE;IAAF,CAAE,CAAC,CAAC,CACzCC,SAAS,CAAC;MACTC,IAAI,EAAG2B,UAAU,IAAI;QAAAvF,aAAA,GAAAmC,CAAA;QAAAnC,aAAA,GAAA0B,CAAA;QACnB,IAAI,CAAC6D,UAAU,CAACC,mBAAmB,EAAE;UAAAxF,aAAA,GAAA+C,CAAA;UAAA/C,aAAA,GAAA0B,CAAA;UACnC6D,UAAU,CAACC,mBAAmB,GAAG;YAC/BC,EAAE,EAAE,CAAAzF,aAAA,GAAA+C,CAAA,WAAAoC,MAAM,CAACO,gBAAgB,MAAA1F,aAAA,GAAA+C,CAAA,WAAI,CAAC;YAChC4C,IAAI,EAAE,CAAA3F,aAAA,GAAA+C,CAAA,WAAAoC,MAAM,CAACnC,kBAAkB,MAAAhD,aAAA,GAAA+C,CAAA,WAAI,EAAE;WACtC;QACH,CAAC,MAAM;UAAA/C,aAAA,GAAA+C,CAAA;UAAA/C,aAAA,GAAA0B,CAAA;UACL6D,UAAU,CAACC,mBAAmB,CAACC,EAAE,GAAG,CAAAzF,aAAA,GAAA+C,CAAA,WAAAoC,MAAM,CAACO,gBAAgB,MAAA1F,aAAA,GAAA+C,CAAA,WAAI,CAAC;UAAC/C,aAAA,GAAA0B,CAAA;UACjE6D,UAAU,CAACC,mBAAmB,CAACG,IAAI,GACjC,CAAA3F,aAAA,GAAA+C,CAAA,WAAAoC,MAAM,CAACnC,kBAAkB,MAAAhD,aAAA,GAAA+C,CAAA,WAAI,EAAE;QACnC;QAEA,MAAM6C,SAAS,IAAA5F,aAAA,GAAA0B,CAAA,QAAG,IAAI,CAACG,MAAM,CAACgE,IAAI,CAAC1E,4BAA4B,EAAE;UAC/D2E,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,QAAQ,EAAE,OAAO;UACjBC,SAAS,EAAE,OAAO;UAClBhC,IAAI,EAAE;YAAEkB,MAAM,EAAEI;UAAU;SAC3B,CAAC;QAACvF,aAAA,GAAA0B,CAAA;QAEHkE,SAAS,CAACM,WAAW,EAAE,CAACvC,SAAS,CAAEwC,MAAM,IAAI;UAAAnG,aAAA,GAAAmC,CAAA;UAAAnC,aAAA,GAAA0B,CAAA;UAC3C,IAAIyE,MAAM,EAAE;YAAAnG,aAAA,GAAA+C,CAAA;YAAA/C,aAAA,GAAA0B,CAAA;YACV,IAAI,CAACc,kBAAkB,EAAE;UAC3B,CAAC;YAAAxC,aAAA,GAAA+C,CAAA;UAAA;QACH,CAAC,CAAC;MACJ,CAAC;MACDM,KAAK,EAAGA,KAAK,IAAI;QAAArD,aAAA,GAAAmC,CAAA;QAAAnC,aAAA,GAAA0B,CAAA;QACf,IAAI,CAACM,KAAK,CAACqB,KAAK,CACd,CAAArD,aAAA,GAAA+C,CAAA,WAAAM,KAAK,CAACA,KAAK,EAAEc,MAAM,MAAAnE,aAAA,GAAA+C,CAAA,WAAI,qCAAqC,EAC7D;MACH;KACD,CAAC;EACN;;;;;;;;;;;;;;;;;;;;;;;;;cAnJC1C,SAAS;QAAA+F,IAAA,GAACxF,YAAY;MAAA;;cACtBP,SAAS;QAAA+F,IAAA,GAACtF,OAAO;MAAA;;;;;AAnBPa,iCAAiC,GAAA0E,UAAA,EAnB7CjG,SAAS,CAAC;EACTkG,QAAQ,EAAE,iCAAiC;EAC3CC,QAAA,EAAAC,oBAA2D;EAE3DC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPpG,eAAe,EACfC,aAAa,EACbG,aAAa,EACbD,kBAAkB,EAClBE,cAAc,EACdM,cAAc,EACdF,aAAa,EACbF,kBAAkB,EAClBK,gBAAgB,EAChBf,QAAQ,EACRD,YAAY,CACb;;CACF,CAAC,C,EACWyB,iCAAiC,CAsK7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}