import { TestBed } from '@angular/core/testing';
import {
  ActivatedRouteSnapshot,
  Router,
  RouterStateSnapshot,
} from '@angular/router';
import { AuthService } from '@core/auth/services/auth.service';
import { profileGuard } from './profile.guard';

describe('profileGuard', () => {
  let router: jasmine.SpyObj<Router>;
  let authService: jasmine.SpyObj<AuthService>;
  let mockRoute: ActivatedRouteSnapshot;
  let mockState: RouterStateSnapshot;

  beforeEach(() => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const authServiceSpy = jasmine.createSpyObj('AuthService', [
      'getUserProfiles',
    ]);

    mockRoute = {
      data: {
        profiles: ['admin', 'supervisor'],
      },
      params: {},
      queryParams: {},
      fragment: null,
      outlet: 'primary',
      component: null,
      routeConfig: null,
      root: null,
      parent: null,
      firstChild: null,
      children: [],
      pathFromRoot: [],
      paramMap: {
        has: jasmine.createSpy('has'),
        get: jasmine.createSpy('get'),
        getAll: jasmine.createSpy('getAll'),
        keys: jasmine.createSpy('keys'),
      },
      queryParamMap: {
        has: jasmine.createSpy('has'),
        get: jasmine.createSpy('get'),
        getAll: jasmine.createSpy('getAll'),
        keys: jasmine.createSpy('keys'),
      },
      url: [],
      title: null,
    } as unknown as ActivatedRouteSnapshot;

    mockState = { url: '/test' } as RouterStateSnapshot;

    TestBed.configureTestingModule({
      providers: [
        { provide: Router, useValue: routerSpy },
        { provide: AuthService, useValue: authServiceSpy },
      ],
    });

    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
  });

  it('should be created', () => {
    expect(profileGuard).toBeTruthy();
  });

  it('should allow access when user has required profile', () => {
    authService.getUserProfiles.and.returnValue([
      { profile_name: 'admin', profile_id: 1 },
    ]);

    const result = TestBed.runInInjectionContext(() =>
      profileGuard(mockRoute, mockState),
    );

    expect(result).toBe(true);
    expect(router.navigate).not.toHaveBeenCalled();
  });

  it('should allow access when user has any of the required profiles', () => {
    authService.getUserProfiles.and.returnValue([
      { profile_name: 'supervisor', profile_id: 2 },
    ]);

    const result = TestBed.runInInjectionContext(() =>
      profileGuard(mockRoute, mockState),
    );

    expect(result).toBe(true);
    expect(router.navigate).not.toHaveBeenCalled();
  });

  it('should deny access when user has no required profiles', () => {
    authService.getUserProfiles.and.returnValue([
      { profile_name: 'user', profile_id: 3 },
    ]);

    const result = TestBed.runInInjectionContext(() =>
      profileGuard(mockRoute, mockState),
    );

    expect(result).toBe(false);
    expect(router.navigate).toHaveBeenCalledWith(['/unauthorized']);
  });

  it('should deny access when user has no profiles', () => {
    authService.getUserProfiles.and.returnValue([]);

    const result = TestBed.runInInjectionContext(() =>
      profileGuard(mockRoute, mockState),
    );

    expect(result).toBe(false);
    expect(router.navigate).toHaveBeenCalledWith(['/unauthorized']);
  });

  it('should handle route without profiles data', () => {
    const routeWithoutProfiles = {
      ...mockRoute,
      data: { profiles: [] },
    } as unknown as ActivatedRouteSnapshot;
    authService.getUserProfiles.and.returnValue([
      { profile_name: 'admin', profile_id: 1 },
    ]);

    const result = TestBed.runInInjectionContext(() =>
      profileGuard(routeWithoutProfiles, mockState),
    );

    expect(result).toBe(false);
    expect(router.navigate).toHaveBeenCalledWith(['/unauthorized']);
  });
});