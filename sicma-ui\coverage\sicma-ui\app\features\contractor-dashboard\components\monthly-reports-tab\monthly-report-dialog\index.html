
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for app/features/contractor-dashboard/components/monthly-reports-tab/monthly-report-dialog</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../../../index.html">All files</a> app/features/contractor-dashboard/components/monthly-reports-tab/monthly-report-dialog</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">67.32% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>171/254</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">51.2% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>64/125</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">62.82% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>49/78</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">67.32% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>171/254</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="monthly-report-dialog.component.ts"><a href="monthly-report-dialog.component.ts.html">monthly-report-dialog.component.ts</a></td>
	<td data-value="67.32" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 67%"></div><div class="cover-empty" style="width: 33%"></div></div>
	</td>
	<td data-value="67.32" class="pct medium">67.32%</td>
	<td data-value="254" class="abs medium">171/254</td>
	<td data-value="51.2" class="pct medium">51.2%</td>
	<td data-value="125" class="abs medium">64/125</td>
	<td data-value="62.82" class="pct medium">62.82%</td>
	<td data-value="78" class="abs medium">49/78</td>
	<td data-value="67.32" class="pct medium">67.32%</td>
	<td data-value="254" class="abs medium">171/254</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-15T19:17:53.681Z
            </div>
        <script src="../../../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../../../sorter.js"></script>
        <script src="../../../../../../block-navigation.js"></script>
    </body>
</html>
    