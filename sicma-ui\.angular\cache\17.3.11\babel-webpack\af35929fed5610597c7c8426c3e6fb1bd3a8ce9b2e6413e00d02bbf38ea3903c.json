{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { ContractSummaryComponent } from './contract-summary.component';\ndescribe('ContractSummaryComponent', () => {\n  let component;\n  let fixture;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [ContractSummaryComponent]\n    });\n    fixture = TestBed.createComponent(ContractSummaryComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "ContractSummaryComponent", "describe", "component", "fixture", "beforeEach", "configureTestingModule", "imports", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\components\\contract-summary\\contract-summary.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\n\nimport { ContractSummaryComponent } from './contract-summary.component';\n\ndescribe('ContractSummaryComponent', () => {\n  let component: ContractSummaryComponent;\n  let fixture: ComponentFixture<ContractSummaryComponent>;\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [ContractSummaryComponent],\n    });\n    fixture = TestBed.createComponent(ContractSummaryComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});"], "mappings": "AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AAEjE,SAASC,wBAAwB,QAAQ,8BAA8B;AAEvEC,QAAQ,CAAC,0BAA0B,EAAE,MAAK;EACxC,IAAIC,SAAmC;EACvC,IAAIC,OAAmD;EAEvDC,UAAU,CAAC,MAAK;IACdL,OAAO,CAACM,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACN,wBAAwB;KACnC,CAAC;IACFG,OAAO,GAAGJ,OAAO,CAACQ,eAAe,CAACP,wBAAwB,CAAC;IAC3DE,SAAS,GAAGC,OAAO,CAACK,iBAAiB;IACrCL,OAAO,CAACM,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACT,SAAS,CAAC,CAACU,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}