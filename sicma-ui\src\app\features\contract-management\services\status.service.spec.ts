import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { Status } from '@contract-management/models/status.model';
import { environment } from '@env';
import { StatusService } from './status.service';

describe('StatusService', () => {
  let service: StatusService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/statuses`;

  const mockStatus: Status = {
    id: 1,
    name: 'Test Status',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [StatusService],
    });
    service = TestBed.inject(StatusService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all statuses', () => {
      const mockStatuses = [mockStatus];

      service.getAll().subscribe((statuses) => {
        expect(statuses).toEqual(mockStatuses);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockStatuses);
    });
  });

  describe('getById', () => {
    it('should return a status by id', () => {
      const id = 1;

      service.getById(id).subscribe((status) => {
        expect(status).toEqual(mockStatus);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockStatus);
    });
  });

  describe('create', () => {
    it('should create a new status', () => {
      const newStatus: Omit<Status, 'id'> = {
        name: 'New Status',
      };

      service.create(newStatus).subscribe((status) => {
        expect(status).toEqual(mockStatus);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newStatus);
      req.flush(mockStatus);
    });
  });

  describe('update', () => {
    it('should update a status', () => {
      const id = 1;
      const updateData: Partial<Status> = {
        name: 'Updated Status',
      };

      service.update(id, updateData).subscribe((status) => {
        expect(status).toEqual(mockStatus);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockStatus);
    });
  });

  describe('delete', () => {
    it('should delete a status', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });

  describe('getByName', () => {
    it('should return a status by name', () => {
      const name = 'Test Status';

      service.getByName(name).subscribe((status) => {
        expect(status).toEqual(mockStatus);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockStatus);
    });
  });
});