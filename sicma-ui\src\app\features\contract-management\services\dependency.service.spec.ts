import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { Dependency } from '@contract-management/models/dependency.model';
import { environment } from '@env';
import { DependencyService } from './dependency.service';

describe('DependencyService', () => {
  let service: DependencyService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/dependencies`;

  const mockDependency: Dependency = {
    id: 1,
    name: 'Test Dependency',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [DependencyService],
    });
    service = TestBed.inject(DependencyService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAll', () => {
    it('should return all dependencies', () => {
      const mockDependencies = [mockDependency];

      service.getAll().subscribe((dependencies) => {
        expect(dependencies).toEqual(mockDependencies);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('GET');
      req.flush(mockDependencies);
    });
  });

  describe('getById', () => {
    it('should return a dependency by id', () => {
      const id = 1;

      service.getById(id).subscribe((dependency) => {
        expect(dependency).toEqual(mockDependency);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockDependency);
    });
  });

  describe('create', () => {
    it('should create a new dependency', () => {
      const newDependency: Omit<Dependency, 'id'> = {
        name: 'New Dependency',
      };

      service.create(newDependency).subscribe((dependency) => {
        expect(dependency).toEqual(mockDependency);
      });

      const req = httpMock.expectOne(apiUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(newDependency);
      req.flush(mockDependency);
    });
  });

  describe('update', () => {
    it('should update a dependency', () => {
      const id = 1;
      const updateData: Partial<Dependency> = {
        name: 'Updated Dependency',
      };

      service.update(id, updateData).subscribe((dependency) => {
        expect(dependency).toEqual(mockDependency);
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockDependency);
    });
  });

  describe('delete', () => {
    it('should delete a dependency', () => {
      const id = 1;

      service.delete(id).subscribe(() => {
        expect().nothing();
      });

      const req = httpMock.expectOne(`${apiUrl}/${id}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });

  describe('getByName', () => {
    it('should return a dependency by name', () => {
      const name = 'Test Dependency';

      service.getByName(name).subscribe((dependency) => {
        expect(dependency).toEqual(mockDependency);
      });

      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockDependency);
    });
  });
});