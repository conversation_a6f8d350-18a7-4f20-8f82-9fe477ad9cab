import { Component, OnInit, ViewChild } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { ActivatedRoute } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { finalize, switchMap } from 'rxjs/operators';

import { MatCardModule } from '@angular/material/card';
import { AssociatedContractorsListComponent } from '@contract-management/components/associated-contractors-list/associated-contractors-list.component';
import { CcpsListComponent } from '@contract-management/components/ccps-list/ccps-list.component';
import { ContractAuditHistoryComponent } from '@contract-management/components/contract-audit-history/contract-audit-history.component';
import { ContractValueSummaryComponent } from '@contract-management/components/contract-value-summary/contract-value-summary.component';
import { EarlyTerminationDialogComponent } from '@contract-management/components/early-termination-dialog/early-termination-dialog.component';
import { ObligationsListComponent } from '@contract-management/components/obligations-list/obligations-list.component';
import { ReductionsListComponent } from '@contract-management/components/reductions-list/reductions-list.component';
import { SuspensionsListComponent } from '@contract-management/components/suspensions-list/suspensions-list.component';
import { ContractDetails } from '@contract-management/models/contract-details.model';
import { Contract } from '@contract-management/models/contract.model';
import { ContractService } from '@contract-management/services/contract.service';
import { AlertService } from '@shared/services/alert.service';
import { ContractDetailFormComponent } from '../../components/contract-detail-form/contract-detail-form.component';
import { AdditionsListComponent } from '@contract-management/components/additions-list/additions-list.component';
import { ContractorDetailFormComponent } from '../../components/contractor-detail-form/contractor-detail-form.component';
import { ContractValues } from '@contract-management/models/contract-values.model';
import { Reduction } from '@contract-management/models/reduction.model';

@Component({
  selector: 'app-contract-detail-page',
  templateUrl: './contract-detail-page.component.html',
  styleUrl: './contract-detail-page.component.scss',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatDialogModule,
    MatCardModule,
    AssociatedContractorsListComponent,
    AdditionsListComponent,
    ObligationsListComponent,
    SuspensionsListComponent,
    ContractDetailFormComponent,
    ContractorDetailFormComponent,
    CcpsListComponent,
    ContractAuditHistoryComponent,
    ReductionsListComponent,
    ContractValueSummaryComponent,
  ],
})
export class ContractDetailPageComponent implements OnInit {
  contract: Contract | null = null;
  contractDetails: ContractDetails | null = null;
  isContractFinished = false;
  contractorId?: number;

  @ViewChild(AssociatedContractorsListComponent)
  AssociatedContractorsListComponent!: AssociatedContractorsListComponent;

  @ViewChild(ContractDetailFormComponent)
  contractDetailForm!: ContractDetailFormComponent;

  @ViewChild('contractorDetailForm')
  contractorDetailForm!: ContractorDetailFormComponent;

  @ViewChild(ContractValueSummaryComponent)
  contractValueSummary!: ContractValueSummaryComponent;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly contractService: ContractService,
    private readonly dialog: MatDialog,
    private readonly alert: AlertService,
    private readonly spinner: NgxSpinnerService,
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.loadContractData(+params['id']);
    });
  }

  private loadContractData(contractId: number): void {
    this.spinner.show();

    this.contractService
      .getDetailsById(contractId)
      .pipe(
        switchMap((details) => {
          this.contractDetails = details;
          this.contractorId = details.contractorId;

          return this.contractService.getById(contractId);
        }),
        finalize(() => this.spinner.hide()),
      )
      .subscribe({
        next: (contract) => {
          if (this.contractDetails) {
            if (this.contractDetails.causesSelectionId && this.contractDetails.causesSelectionName) {
              contract.causesSelection = {
                id: this.contractDetails.causesSelectionId,
                name: this.contractDetails.causesSelectionName
              };
            }

            if (this.contractDetails.contractClassId && this.contractDetails.contractClassName) {
              contract.contractClass = {
                id: this.contractDetails.contractClassId,
                name: this.contractDetails.contractClassName
              };
            }

            if (this.contractDetails.managementSupportId && this.contractDetails.managementSupportName) {
              contract.managementSupport = {
                id: this.contractDetails.managementSupportId,
                name: this.contractDetails.managementSupportName
              };
            }

            if (this.contractDetails.supervisorFullName && this.contractDetails.supervisorIdNumber) {
              if (this.contractDetails.supervisorIdNumber) {
                const idNumber = parseInt(this.contractDetails.supervisorIdNumber, 10);
                contract.supervisorId = idNumber;
              }

              contract.supervisor = {
                id: contract.supervisorId || 0,
                fullName: this.contractDetails.supervisorFullName,
                idNumber: parseInt(this.contractDetails.supervisorIdNumber, 10) || 0,
                position: this.contractDetails.supervisorPosition || '',
                email: '',
                idType: { id: 0, name: '' }
              };

              if (this.contractDetailForm) {
                setTimeout(() => {
                  if (contract.supervisor) {
                    this.contractDetailForm.supervisor = contract.supervisor;
                  }
                  this.contractDetailForm.contractForm.patchValue({
                    supervisorId: contract.supervisorId,
                    supervisorFullName: this.contractDetails?.supervisorFullName
                  });
                });
              }
            }
          }

          this.contract = contract;
          this.isContractFinished = contract.status?.name === 'FINALIZADO';
        },
        error: (_) => {
          this.alert.error(error.error?.detail ?? 'Error al cargar el contrato');
        },
      });
  }

  getContractorId(): number | undefined {
    return this.contractorId;
  }

  openEarlyTerminationDialog(): void {
    if (!this.contract || !this.AssociatedContractorsListComponent) return;

    const latestContractorContract =
      this.AssociatedContractorsListComponent.getLatestContractorContract();
    if (latestContractorContract) {
      this.dialog
        .open(EarlyTerminationDialogComponent, {
          width: '500px',
          data: {
            contractId: this.contract.id,
            contractorId: latestContractorContract.contractor?.id,
            lastContractorStartDate: latestContractorContract.contractStartDate,
          },
        })
        .afterClosed()
        .subscribe({
          next: (result) => {
            if (result) {
              if (this.contractDetailForm) {
                this.contractDetailForm.contractForm
                  .get('earlyTermination')
                  ?.disable();
              }
              if (
                this.AssociatedContractorsListComponent &&
                latestContractorContract.id
              ) {
                this.AssociatedContractorsListComponent.updateContractorContracts(
                  result.endDate,
                  latestContractorContract.id,
                );
              }
            } else {
              if (this.contractDetailForm) {
                this.contractDetailForm.contractForm
                  .get('earlyTermination')
                  ?.setValue(false);
              }
            }
          },
        });
    } else {
      this.alert.warning('No hay contratistas asociados a este contrato');
    }
  }

  onContractorContractsChanged(): void {
    if (this.contract) {
      this.contract.cession = true;
    }
    if (this.contractDetailForm) {
      this.contractDetailForm.contractForm.patchValue({ cession: true });
    }
  }

  uncheckEarlyTermination(): void {
    if (this.contractDetailForm) {
      this.contractDetailForm.contractForm
        .get('earlyTermination')
        ?.setValue(false);
      this.contractDetailForm.contractForm.get('earlyTermination')?.enable();
    }
  }

  updateContractStatus(isCompleted: boolean): void {
    this.isContractFinished = isCompleted;
  }

  onContractValuesChanged(contractValues: ContractValues[]): void {
    if (this.contract) {
      this.contract.contractValues = contractValues;
    }
  }

  onReductionsChanged(reductions: Reduction[]): void {
    if (this.contract) {
      this.contract.reduction = reductions;
    }
  }
}
