{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed, fakeAsync, tick } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { SuspensionService } from './suspension.service';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { ContractAuditHistoryService } from './contract-audit-history.service';\nimport { ContractAuditStatusService } from './contract-audit-status.service';\nimport { of as observableOf } from 'rxjs';\ndescribe('SuspensionService', () => {\n  let service;\n  let httpMock;\n  let authServiceSpy;\n  let contractAuditHistoryServiceSpy;\n  let contractAuditStatusServiceSpy;\n  const apiUrl = `${environment.apiUrl}/suspensions`;\n  const mockSuspension = {\n    id: 1,\n    startDate: new Date('2024-02-20'),\n    endDate: new Date('2024-03-20'),\n    reason: 'Test suspension reason',\n    contractId: 1,\n    suspensionDays: 29\n  };\n  const mockUser = {\n    id: 1,\n    username: 'testuser',\n    profiles: []\n  };\n  const mockAuditStatus = {\n    id: 1,\n    name: 'Edición de suspensión',\n    description: 'Edición de suspensión'\n  };\n  const mockAuditHistory = {\n    id: 1,\n    contractId: 1,\n    auditStatusId: 1,\n    auditDate: new Date(),\n    comment: 'Test comment',\n    auditorId: 1\n  };\n  beforeEach(() => {\n    authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);\n    contractAuditHistoryServiceSpy = jasmine.createSpyObj('ContractAuditHistoryService', ['create']);\n    contractAuditStatusServiceSpy = jasmine.createSpyObj('ContractAuditStatusService', ['getByName']);\n    authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n    contractAuditHistoryServiceSpy.create.and.returnValue(observableOf(mockAuditHistory));\n    contractAuditStatusServiceSpy.getByName.and.returnValue(observableOf(mockAuditStatus));\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [SuspensionService, {\n        provide: AuthService,\n        useValue: authServiceSpy\n      }, {\n        provide: ContractAuditHistoryService,\n        useValue: contractAuditHistoryServiceSpy\n      }, {\n        provide: ContractAuditStatusService,\n        useValue: contractAuditStatusServiceSpy\n      }]\n    });\n    service = TestBed.inject(SuspensionService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all suspensions', () => {\n      const mockSuspensions = [mockSuspension];\n      service.getAll().subscribe(suspensions => {\n        expect(suspensions).toEqual(mockSuspensions);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockSuspensions);\n    });\n    it('should handle error when getting all suspensions', () => {\n      service.getAll().subscribe({\n        next: () => fail('should have failed'),\n        error: error => {\n          expect(error.status).toBe(500);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.flush('Internal Server Error', {\n        status: 500,\n        statusText: 'Internal Server Error'\n      });\n    });\n  });\n  describe('getById', () => {\n    it('should return a suspension by id', () => {\n      const id = 1;\n      service.getById(id).subscribe(suspension => {\n        expect(suspension).toEqual(mockSuspension);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockSuspension);\n    });\n    it('should handle error when getting suspension by id', () => {\n      const id = 999;\n      service.getById(id).subscribe({\n        next: () => fail('should have failed'),\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.flush('Not Found', {\n        status: 404,\n        statusText: 'Not Found'\n      });\n    });\n  });\n  describe('create', () => {\n    it('should create a new suspension with audit when user is authenticated', fakeAsync(() => {\n      const newSuspension = {\n        startDate: new Date('2024-02-20'),\n        endDate: new Date('2024-03-20'),\n        reason: 'New suspension reason',\n        contractId: 1,\n        suspensionDays: 29\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n      let completed = false;\n      service.create(newSuspension).subscribe(suspension => {\n        expect(suspension).toEqual(mockSuspension);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith('Creación de suspensión');\n        completed = true;\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newSuspension);\n      req.flush(mockSuspension);\n      tick(100);\n      expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();\n      expect(completed).toBeTrue();\n    }));\n    it('should create a new suspension without audit when user is not authenticated', () => {\n      const newSuspension = {\n        startDate: new Date('2024-02-20'),\n        endDate: new Date('2024-03-20'),\n        reason: 'New suspension reason',\n        contractId: 1,\n        suspensionDays: 29\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n      service.create(newSuspension).subscribe(suspension => {\n        expect(suspension).toEqual(mockSuspension);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newSuspension);\n      req.flush(mockSuspension);\n    });\n    it('should handle error when creating a new suspension', () => {\n      const newSuspension = {\n        startDate: new Date('2024-02-20'),\n        endDate: new Date('2024-03-20'),\n        reason: 'New suspension reason',\n        contractId: 1,\n        suspensionDays: 29\n      };\n      service.create(newSuspension).subscribe({\n        next: () => fail('should have failed'),\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const req = httpMock.expectOne(apiUrl);\n      req.flush('Bad Request', {\n        status: 400,\n        statusText: 'Bad Request'\n      });\n    });\n  });\n  describe('update', () => {\n    it('should update a suspension with audit when startDate has changed and user is authenticated', fakeAsync(() => {\n      const id = 1;\n      const updateData = {\n        startDate: new Date('2024-03-01'),\n        endDate: new Date('2024-03-20'),\n        reason: 'Updated suspension reason',\n        contractId: 1,\n        suspensionDays: 20\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n      let completed = false;\n      service.update(id, updateData).subscribe(suspension => {\n        expect(suspension).toEqual(mockSuspension);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith('Edición de suspensión');\n        completed = true;\n      });\n      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n      getReq.flush({\n        ...mockSuspension,\n        startDate: new Date('2024-02-20'),\n        endDate: new Date('2024-03-20')\n      });\n      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateData);\n      putReq.flush(mockSuspension);\n      tick(100);\n      expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();\n      expect(completed).toBeTrue();\n    }));\n    it('should update a suspension with audit when endDate has changed and user is authenticated', fakeAsync(() => {\n      const id = 1;\n      const updateData = {\n        startDate: new Date('2024-02-20'),\n        endDate: new Date('2024-04-20'),\n        reason: 'Updated suspension reason',\n        contractId: 1,\n        suspensionDays: 60\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n      let completed = false;\n      service.update(id, updateData).subscribe(suspension => {\n        expect(suspension).toEqual(mockSuspension);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith('Edición de suspensión');\n        completed = true;\n      });\n      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n      getReq.flush({\n        ...mockSuspension,\n        startDate: new Date('2024-02-20'),\n        endDate: new Date('2024-03-20')\n      });\n      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateData);\n      putReq.flush(mockSuspension);\n      tick(100);\n      expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();\n      expect(completed).toBeTrue();\n    }));\n    it('should update a suspension with audit when both dates have changed and user is authenticated', fakeAsync(() => {\n      const id = 1;\n      const updateData = {\n        startDate: new Date('2024-03-01'),\n        endDate: new Date('2024-04-20'),\n        reason: 'Updated suspension reason',\n        contractId: 1,\n        suspensionDays: 50\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n      let completed = false;\n      service.update(id, updateData).subscribe(suspension => {\n        expect(suspension).toEqual(mockSuspension);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith('Edición de suspensión');\n        completed = true;\n      });\n      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n      getReq.flush({\n        ...mockSuspension,\n        startDate: new Date('2024-02-20'),\n        endDate: new Date('2024-03-20')\n      });\n      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateData);\n      putReq.flush(mockSuspension);\n      tick(100);\n      expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();\n      expect(completed).toBeTrue();\n    }));\n    it('should update a suspension without audit when dates have not changed and user is authenticated', fakeAsync(() => {\n      const id = 1;\n      const originalStartDate = new Date('2024-02-20');\n      const originalEndDate = new Date('2024-03-20');\n      const updateData = {\n        startDate: originalStartDate,\n        endDate: originalEndDate,\n        reason: 'Updated suspension reason only',\n        contractId: 1,\n        suspensionDays: 29\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n      service.update(id, updateData).subscribe(suspension => {\n        expect(suspension).toEqual(mockSuspension);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n      });\n      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n      getReq.flush({\n        ...mockSuspension,\n        startDate: originalStartDate,\n        endDate: originalEndDate\n      });\n      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateData);\n      putReq.flush(mockSuspension);\n      tick();\n    }));\n    it('should update a suspension without audit when user is not authenticated', () => {\n      const id = 1;\n      const updateData = {\n        startDate: new Date('2024-03-01'),\n        endDate: new Date('2024-04-20'),\n        reason: 'Updated suspension reason',\n        contractId: 1,\n        suspensionDays: 50\n      };\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n      service.update(id, updateData).subscribe(suspension => {\n        expect(suspension).toEqual(mockSuspension);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n      });\n      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n      getReq.flush(mockSuspension);\n      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateData);\n      putReq.flush(mockSuspension);\n    });\n    it('should handle error when getting suspension for update', () => {\n      const id = 999;\n      const updateData = {\n        reason: 'Updated suspension reason'\n      };\n      service.update(id, updateData).subscribe({\n        next: () => fail('should have failed'),\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.flush('Not Found', {\n        status: 404,\n        statusText: 'Not Found'\n      });\n    });\n    it('should handle error when updating a suspension', () => {\n      const id = 1;\n      const updateData = {\n        reason: 'Updated suspension reason'\n      };\n      service.update(id, updateData).subscribe({\n        next: () => fail('should have failed'),\n        error: error => {\n          expect(error.status).toBe(400);\n        }\n      });\n      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n      getReq.flush(mockSuspension);\n      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      putReq.flush('Bad Request', {\n        status: 400,\n        statusText: 'Bad Request'\n      });\n    });\n  });\n  describe('delete', () => {\n    it('should delete a suspension', () => {\n      const id = 1;\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n    it('should handle error when deleting a suspension', () => {\n      const id = 999;\n      service.delete(id).subscribe({\n        next: () => fail('should have failed'),\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.flush('Not Found', {\n        status: 404,\n        statusText: 'Not Found'\n      });\n    });\n  });\n  describe('getAllByContractId', () => {\n    it('should return all suspensions by contract id', () => {\n      const contractId = 1;\n      const mockSuspensions = [mockSuspension];\n      service.getAllByContractId(contractId).subscribe(suspensions => {\n        expect(suspensions).toEqual(mockSuspensions);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockSuspensions);\n    });\n    it('should handle error when getting suspensions by contract id', () => {\n      const contractId = 999;\n      service.getAllByContractId(contractId).subscribe({\n        next: () => fail('should have failed'),\n        error: error => {\n          expect(error.status).toBe(404);\n        }\n      });\n      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}`);\n      req.flush('Not Found', {\n        status: 404,\n        statusText: 'Not Found'\n      });\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "fakeAsync", "tick", "environment", "SuspensionService", "AuthService", "ContractAuditHistoryService", "ContractAuditStatusService", "of", "observableOf", "describe", "service", "httpMock", "authServiceSpy", "contractAuditHistoryServiceSpy", "contractAuditStatusServiceSpy", "apiUrl", "mockSuspension", "id", "startDate", "Date", "endDate", "reason", "contractId", "suspensionDays", "mockUser", "username", "profiles", "mockAuditStatus", "name", "description", "mockAuditHistory", "auditStatusId", "auditDate", "comment", "auditorId", "beforeEach", "jasmine", "createSpyObj", "getCurrentUser", "and", "returnValue", "create", "getByName", "configureTestingModule", "imports", "providers", "provide", "useValue", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockSuspensions", "getAll", "subscribe", "suspensions", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "next", "fail", "error", "status", "statusText", "getById", "suspension", "newSuspension", "completed", "toHaveBeenCalled", "toHaveBeenCalledWith", "body", "toBeTrue", "not", "updateData", "update", "getReq", "putReq", "originalStartDate", "originalEndDate", "delete", "nothing", "getAllByContractId"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\services\\suspension.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed, fakeAsync, tick } from '@angular/core/testing';\nimport { Suspension } from '@contract-management/models/suspension.model';\nimport { environment } from '@env';\nimport { SuspensionService } from './suspension.service';\nimport { AuthService } from '@core/auth/services/auth.service';\nimport { ContractAuditHistoryService } from './contract-audit-history.service';\nimport { ContractAuditStatusService } from './contract-audit-status.service';\nimport { User } from '@core/auth/models/user.model';\nimport { ContractAuditStatus } from '@contract-management/models/contract-audit-status.model';\nimport { ContractAuditHistory } from '@contract-management/models/contract-audit-history.model';\nimport { of as observableOf } from 'rxjs';\n\ndescribe('SuspensionService', () => {\n  let service: SuspensionService;\n  let httpMock: HttpTestingController;\n  let authServiceSpy: jasmine.SpyObj<AuthService>;\n  let contractAuditHistoryServiceSpy: jasmine.SpyObj<ContractAuditHistoryService>;\n  let contractAuditStatusServiceSpy: jasmine.SpyObj<ContractAuditStatusService>;\n  const apiUrl = `${environment.apiUrl}/suspensions`;\n\n  const mockSuspension: Suspension = {\n    id: 1,\n    startDate: new Date('2024-02-20'),\n    endDate: new Date('2024-03-20'),\n    reason: 'Test suspension reason',\n    contractId: 1,\n    suspensionDays: 29,\n  };\n\n  const mockUser: User = { id: 1, username: 'testuser', profiles: [] };\n\n  const mockAuditStatus: ContractAuditStatus = {\n    id: 1,\n    name: 'Edición de suspensión',\n    description: 'Edición de suspensión',\n  };\n\n  const mockAuditHistory: ContractAuditHistory = {\n    id: 1,\n    contractId: 1,\n    auditStatusId: 1,\n    auditDate: new Date(),\n    comment: 'Test comment',\n    auditorId: 1,\n  };\n\n  beforeEach(() => {\n    authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);\n    contractAuditHistoryServiceSpy = jasmine.createSpyObj(\n      'ContractAuditHistoryService',\n      ['create'],\n    );\n    contractAuditStatusServiceSpy = jasmine.createSpyObj(\n      'ContractAuditStatusService',\n      ['getByName'],\n    );\n\n    authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n    contractAuditHistoryServiceSpy.create.and.returnValue(\n      observableOf(mockAuditHistory),\n    );\n\n    contractAuditStatusServiceSpy.getByName.and.returnValue(\n      observableOf(mockAuditStatus),\n    );\n\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [\n        SuspensionService,\n        { provide: AuthService, useValue: authServiceSpy },\n        {\n          provide: ContractAuditHistoryService,\n          useValue: contractAuditHistoryServiceSpy,\n        },\n        {\n          provide: ContractAuditStatusService,\n          useValue: contractAuditStatusServiceSpy,\n        },\n      ],\n    });\n    service = TestBed.inject(SuspensionService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all suspensions', () => {\n      const mockSuspensions = [mockSuspension];\n\n      service.getAll().subscribe((suspensions) => {\n        expect(suspensions).toEqual(mockSuspensions);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockSuspensions);\n    });\n\n    it('should handle error when getting all suspensions', () => {\n      service.getAll().subscribe({\n        next: () => fail('should have failed'),\n        error: (error) => {\n          expect(error.status).toBe(500);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.flush('Internal Server Error', {\n        status: 500,\n        statusText: 'Internal Server Error',\n      });\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a suspension by id', () => {\n      const id = 1;\n\n      service.getById(id).subscribe((suspension) => {\n        expect(suspension).toEqual(mockSuspension);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockSuspension);\n    });\n\n    it('should handle error when getting suspension by id', () => {\n      const id = 999;\n\n      service.getById(id).subscribe({\n        next: () => fail('should have failed'),\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.flush('Not Found', { status: 404, statusText: 'Not Found' });\n    });\n  });\n\n  describe('create', () => {\n    it('should create a new suspension with audit when user is authenticated', fakeAsync(() => {\n      const newSuspension: Omit<Suspension, 'id'> = {\n        startDate: new Date('2024-02-20'),\n        endDate: new Date('2024-03-20'),\n        reason: 'New suspension reason',\n        contractId: 1,\n        suspensionDays: 29,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n\n      let completed = false;\n      service.create(newSuspension).subscribe((suspension) => {\n        expect(suspension).toEqual(mockSuspension);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith(\n          'Creación de suspensión',\n        );\n        completed = true;\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newSuspension);\n      req.flush(mockSuspension);\n\n      tick(100);\n\n      expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();\n      expect(completed).toBeTrue();\n    }));\n\n    it('should create a new suspension without audit when user is not authenticated', () => {\n      const newSuspension: Omit<Suspension, 'id'> = {\n        startDate: new Date('2024-02-20'),\n        endDate: new Date('2024-03-20'),\n        reason: 'New suspension reason',\n        contractId: 1,\n        suspensionDays: 29,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n\n      service.create(newSuspension).subscribe((suspension) => {\n        expect(suspension).toEqual(mockSuspension);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newSuspension);\n      req.flush(mockSuspension);\n    });\n\n    it('should handle error when creating a new suspension', () => {\n      const newSuspension: Omit<Suspension, 'id'> = {\n        startDate: new Date('2024-02-20'),\n        endDate: new Date('2024-03-20'),\n        reason: 'New suspension reason',\n        contractId: 1,\n        suspensionDays: 29,\n      };\n\n      service.create(newSuspension).subscribe({\n        next: () => fail('should have failed'),\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      req.flush('Bad Request', { status: 400, statusText: 'Bad Request' });\n    });\n  });\n\n  describe('update', () => {\n    it('should update a suspension with audit when startDate has changed and user is authenticated', fakeAsync(() => {\n      const id = 1;\n      const updateData: Partial<Suspension> = {\n        startDate: new Date('2024-03-01'),\n        endDate: new Date('2024-03-20'),\n        reason: 'Updated suspension reason',\n        contractId: 1,\n        suspensionDays: 20,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n\n      let completed = false;\n      service.update(id, updateData).subscribe((suspension) => {\n        expect(suspension).toEqual(mockSuspension);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith(\n          'Edición de suspensión',\n        );\n        completed = true;\n      });\n\n      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n\n      getReq.flush({\n        ...mockSuspension,\n        startDate: new Date('2024-02-20'),\n        endDate: new Date('2024-03-20'),\n      });\n\n      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateData);\n      putReq.flush(mockSuspension);\n\n      tick(100);\n\n      expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();\n      expect(completed).toBeTrue();\n    }));\n\n    it('should update a suspension with audit when endDate has changed and user is authenticated', fakeAsync(() => {\n      const id = 1;\n      const updateData: Partial<Suspension> = {\n        startDate: new Date('2024-02-20'),\n        endDate: new Date('2024-04-20'),\n        reason: 'Updated suspension reason',\n        contractId: 1,\n        suspensionDays: 60,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n\n      let completed = false;\n      service.update(id, updateData).subscribe((suspension) => {\n        expect(suspension).toEqual(mockSuspension);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith(\n          'Edición de suspensión',\n        );\n        completed = true;\n      });\n\n      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n\n      getReq.flush({\n        ...mockSuspension,\n        startDate: new Date('2024-02-20'),\n        endDate: new Date('2024-03-20'),\n      });\n\n      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateData);\n      putReq.flush(mockSuspension);\n\n      tick(100);\n\n      expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();\n      expect(completed).toBeTrue();\n    }));\n\n    it('should update a suspension with audit when both dates have changed and user is authenticated', fakeAsync(() => {\n      const id = 1;\n      const updateData: Partial<Suspension> = {\n        startDate: new Date('2024-03-01'),\n        endDate: new Date('2024-04-20'),\n        reason: 'Updated suspension reason',\n        contractId: 1,\n        suspensionDays: 50,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n\n      let completed = false;\n      service.update(id, updateData).subscribe((suspension) => {\n        expect(suspension).toEqual(mockSuspension);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).toHaveBeenCalledWith(\n          'Edición de suspensión',\n        );\n        completed = true;\n      });\n\n      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n\n      getReq.flush({\n        ...mockSuspension,\n        startDate: new Date('2024-02-20'),\n        endDate: new Date('2024-03-20'),\n      });\n\n      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateData);\n      putReq.flush(mockSuspension);\n\n      tick(100);\n\n      expect(contractAuditHistoryServiceSpy.create).toHaveBeenCalled();\n      expect(completed).toBeTrue();\n    }));\n\n    it('should update a suspension without audit when dates have not changed and user is authenticated', fakeAsync(() => {\n      const id = 1;\n\n      const originalStartDate = new Date('2024-02-20');\n      const originalEndDate = new Date('2024-03-20');\n\n      const updateData: Partial<Suspension> = {\n        startDate: originalStartDate,\n        endDate: originalEndDate,\n        reason: 'Updated suspension reason only',\n        contractId: 1,\n        suspensionDays: 29,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(mockUser);\n\n      service.update(id, updateData).subscribe((suspension) => {\n        expect(suspension).toEqual(mockSuspension);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n\n        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n      });\n\n      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n\n      getReq.flush({\n        ...mockSuspension,\n        startDate: originalStartDate,\n        endDate: originalEndDate,\n      });\n\n      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateData);\n      putReq.flush(mockSuspension);\n\n      tick();\n    }));\n\n    it('should update a suspension without audit when user is not authenticated', () => {\n      const id = 1;\n      const updateData: Partial<Suspension> = {\n        startDate: new Date('2024-03-01'),\n        endDate: new Date('2024-04-20'),\n        reason: 'Updated suspension reason',\n        contractId: 1,\n        suspensionDays: 50,\n      };\n\n      authServiceSpy.getCurrentUser.and.returnValue(null);\n\n      service.update(id, updateData).subscribe((suspension) => {\n        expect(suspension).toEqual(mockSuspension);\n        expect(authServiceSpy.getCurrentUser).toHaveBeenCalled();\n        expect(contractAuditStatusServiceSpy.getByName).not.toHaveBeenCalled();\n      });\n\n      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n      getReq.flush(mockSuspension);\n\n      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(putReq.request.method).toBe('PUT');\n      expect(putReq.request.body).toEqual(updateData);\n      putReq.flush(mockSuspension);\n    });\n\n    it('should handle error when getting suspension for update', () => {\n      const id = 999;\n      const updateData: Partial<Suspension> = {\n        reason: 'Updated suspension reason',\n      };\n\n      service.update(id, updateData).subscribe({\n        next: () => fail('should have failed'),\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.flush('Not Found', { status: 404, statusText: 'Not Found' });\n    });\n\n    it('should handle error when updating a suspension', () => {\n      const id = 1;\n      const updateData: Partial<Suspension> = {\n        reason: 'Updated suspension reason',\n      };\n\n      service.update(id, updateData).subscribe({\n        next: () => fail('should have failed'),\n        error: (error) => {\n          expect(error.status).toBe(400);\n        },\n      });\n\n      const getReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(getReq.request.method).toBe('GET');\n      getReq.flush(mockSuspension);\n\n      const putReq = httpMock.expectOne(`${apiUrl}/${id}`);\n      putReq.flush('Bad Request', { status: 400, statusText: 'Bad Request' });\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete a suspension', () => {\n      const id = 1;\n\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n\n    it('should handle error when deleting a suspension', () => {\n      const id = 999;\n\n      service.delete(id).subscribe({\n        next: () => fail('should have failed'),\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      req.flush('Not Found', { status: 404, statusText: 'Not Found' });\n    });\n  });\n\n  describe('getAllByContractId', () => {\n    it('should return all suspensions by contract id', () => {\n      const contractId = 1;\n      const mockSuspensions = [mockSuspension];\n\n      service.getAllByContractId(contractId).subscribe((suspensions) => {\n        expect(suspensions).toEqual(mockSuspensions);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockSuspensions);\n    });\n\n    it('should handle error when getting suspensions by contract id', () => {\n      const contractId = 999;\n\n      service.getAllByContractId(contractId).subscribe({\n        next: () => fail('should have failed'),\n        error: (error) => {\n          expect(error.status).toBe(404);\n        },\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/contract/${contractId}`);\n      req.flush('Not Found', { status: 404, statusText: 'Not Found' });\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,EAAEC,SAAS,EAAEC,IAAI,QAAQ,uBAAuB;AAEhE,SAASC,WAAW,QAAQ,MAAM;AAClC,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,0BAA0B,QAAQ,iCAAiC;AAI5E,SAASC,EAAE,IAAIC,YAAY,QAAQ,MAAM;AAEzCC,QAAQ,CAAC,mBAAmB,EAAE,MAAK;EACjC,IAAIC,OAA0B;EAC9B,IAAIC,QAA+B;EACnC,IAAIC,cAA2C;EAC/C,IAAIC,8BAA2E;EAC/E,IAAIC,6BAAyE;EAC7E,MAAMC,MAAM,GAAG,GAAGb,WAAW,CAACa,MAAM,cAAc;EAElD,MAAMC,cAAc,GAAe;IACjCC,EAAE,EAAE,CAAC;IACLC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;IACjCC,OAAO,EAAE,IAAID,IAAI,CAAC,YAAY,CAAC;IAC/BE,MAAM,EAAE,wBAAwB;IAChCC,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE;GACjB;EAED,MAAMC,QAAQ,GAAS;IAAEP,EAAE,EAAE,CAAC;IAAEQ,QAAQ,EAAE,UAAU;IAAEC,QAAQ,EAAE;EAAE,CAAE;EAEpE,MAAMC,eAAe,GAAwB;IAC3CV,EAAE,EAAE,CAAC;IACLW,IAAI,EAAE,uBAAuB;IAC7BC,WAAW,EAAE;GACd;EAED,MAAMC,gBAAgB,GAAyB;IAC7Cb,EAAE,EAAE,CAAC;IACLK,UAAU,EAAE,CAAC;IACbS,aAAa,EAAE,CAAC;IAChBC,SAAS,EAAE,IAAIb,IAAI,EAAE;IACrBc,OAAO,EAAE,cAAc;IACvBC,SAAS,EAAE;GACZ;EAEDC,UAAU,CAAC,MAAK;IACdvB,cAAc,GAAGwB,OAAO,CAACC,YAAY,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,CAAC;IACxExB,8BAA8B,GAAGuB,OAAO,CAACC,YAAY,CACnD,6BAA6B,EAC7B,CAAC,QAAQ,CAAC,CACX;IACDvB,6BAA6B,GAAGsB,OAAO,CAACC,YAAY,CAClD,4BAA4B,EAC5B,CAAC,WAAW,CAAC,CACd;IAEDzB,cAAc,CAAC0B,cAAc,CAACC,GAAG,CAACC,WAAW,CAAChB,QAAQ,CAAC;IACvDX,8BAA8B,CAAC4B,MAAM,CAACF,GAAG,CAACC,WAAW,CACnDhC,YAAY,CAACsB,gBAAgB,CAAC,CAC/B;IAEDhB,6BAA6B,CAAC4B,SAAS,CAACH,GAAG,CAACC,WAAW,CACrDhC,YAAY,CAACmB,eAAe,CAAC,CAC9B;IAED5B,OAAO,CAAC4C,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAAC/C,uBAAuB,CAAC;MAClCgD,SAAS,EAAE,CACT1C,iBAAiB,EACjB;QAAE2C,OAAO,EAAE1C,WAAW;QAAE2C,QAAQ,EAAEnC;MAAc,CAAE,EAClD;QACEkC,OAAO,EAAEzC,2BAA2B;QACpC0C,QAAQ,EAAElC;OACX,EACD;QACEiC,OAAO,EAAExC,0BAA0B;QACnCyC,QAAQ,EAAEjC;OACX;KAEJ,CAAC;IACFJ,OAAO,GAAGX,OAAO,CAACiD,MAAM,CAAC7C,iBAAiB,CAAC;IAC3CQ,QAAQ,GAAGZ,OAAO,CAACiD,MAAM,CAAClD,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFmD,SAAS,CAAC,MAAK;IACbtC,QAAQ,CAACuC,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAAC1C,OAAO,CAAC,CAAC2C,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEF5C,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB0C,EAAE,CAAC,+BAA+B,EAAE,MAAK;MACvC,MAAMG,eAAe,GAAG,CAACtC,cAAc,CAAC;MAExCN,OAAO,CAAC6C,MAAM,EAAE,CAACC,SAAS,CAAEC,WAAW,IAAI;QACzCL,MAAM,CAACK,WAAW,CAAC,CAACC,OAAO,CAACJ,eAAe,CAAC;MAC9C,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGhD,QAAQ,CAACiD,SAAS,CAAC7C,MAAM,CAAC;MACtCqC,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,eAAe,CAAC;IAC5B,CAAC,CAAC;IAEFH,EAAE,CAAC,kDAAkD,EAAE,MAAK;MAC1DzC,OAAO,CAAC6C,MAAM,EAAE,CAACC,SAAS,CAAC;QACzBS,IAAI,EAAEA,CAAA,KAAMC,IAAI,CAAC,oBAAoB,CAAC;QACtCC,KAAK,EAAGA,KAAK,IAAI;UACff,MAAM,CAACe,KAAK,CAACC,MAAM,CAAC,CAACL,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGhD,QAAQ,CAACiD,SAAS,CAAC7C,MAAM,CAAC;MACtC4C,GAAG,CAACK,KAAK,CAAC,uBAAuB,EAAE;QACjCI,MAAM,EAAE,GAAG;QACXC,UAAU,EAAE;OACb,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF5D,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvB0C,EAAE,CAAC,kCAAkC,EAAE,MAAK;MAC1C,MAAMlC,EAAE,GAAG,CAAC;MAEZP,OAAO,CAAC4D,OAAO,CAACrD,EAAE,CAAC,CAACuC,SAAS,CAAEe,UAAU,IAAI;QAC3CnB,MAAM,CAACmB,UAAU,CAAC,CAACb,OAAO,CAAC1C,cAAc,CAAC;MAC5C,CAAC,CAAC;MAEF,MAAM2C,GAAG,GAAGhD,QAAQ,CAACiD,SAAS,CAAC,GAAG7C,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDmC,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAAChD,cAAc,CAAC;IAC3B,CAAC,CAAC;IAEFmC,EAAE,CAAC,mDAAmD,EAAE,MAAK;MAC3D,MAAMlC,EAAE,GAAG,GAAG;MAEdP,OAAO,CAAC4D,OAAO,CAACrD,EAAE,CAAC,CAACuC,SAAS,CAAC;QAC5BS,IAAI,EAAEA,CAAA,KAAMC,IAAI,CAAC,oBAAoB,CAAC;QACtCC,KAAK,EAAGA,KAAK,IAAI;UACff,MAAM,CAACe,KAAK,CAACC,MAAM,CAAC,CAACL,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGhD,QAAQ,CAACiD,SAAS,CAAC,GAAG7C,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjD0C,GAAG,CAACK,KAAK,CAAC,WAAW,EAAE;QAAEI,MAAM,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAW,CAAE,CAAC;IAClE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF5D,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB0C,EAAE,CAAC,sEAAsE,EAAEnD,SAAS,CAAC,MAAK;MACxF,MAAMwE,aAAa,GAA2B;QAC5CtD,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;QACjCC,OAAO,EAAE,IAAID,IAAI,CAAC,YAAY,CAAC;QAC/BE,MAAM,EAAE,uBAAuB;QAC/BC,UAAU,EAAE,CAAC;QACbC,cAAc,EAAE;OACjB;MAEDX,cAAc,CAAC0B,cAAc,CAACC,GAAG,CAACC,WAAW,CAAChB,QAAQ,CAAC;MAEvD,IAAIiD,SAAS,GAAG,KAAK;MACrB/D,OAAO,CAAC+B,MAAM,CAAC+B,aAAa,CAAC,CAAChB,SAAS,CAAEe,UAAU,IAAI;QACrDnB,MAAM,CAACmB,UAAU,CAAC,CAACb,OAAO,CAAC1C,cAAc,CAAC;QAC1CoC,MAAM,CAACxC,cAAc,CAAC0B,cAAc,CAAC,CAACoC,gBAAgB,EAAE;QACxDtB,MAAM,CAACtC,6BAA6B,CAAC4B,SAAS,CAAC,CAACiC,oBAAoB,CAClE,wBAAwB,CACzB;QACDF,SAAS,GAAG,IAAI;MAClB,CAAC,CAAC;MAEF,MAAMd,GAAG,GAAGhD,QAAQ,CAACiD,SAAS,CAAC7C,MAAM,CAAC;MACtCqC,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACe,IAAI,CAAC,CAAClB,OAAO,CAACc,aAAa,CAAC;MAC/Cb,GAAG,CAACK,KAAK,CAAChD,cAAc,CAAC;MAEzBf,IAAI,CAAC,GAAG,CAAC;MAETmD,MAAM,CAACvC,8BAA8B,CAAC4B,MAAM,CAAC,CAACiC,gBAAgB,EAAE;MAChEtB,MAAM,CAACqB,SAAS,CAAC,CAACI,QAAQ,EAAE;IAC9B,CAAC,CAAC,CAAC;IAEH1B,EAAE,CAAC,6EAA6E,EAAE,MAAK;MACrF,MAAMqB,aAAa,GAA2B;QAC5CtD,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;QACjCC,OAAO,EAAE,IAAID,IAAI,CAAC,YAAY,CAAC;QAC/BE,MAAM,EAAE,uBAAuB;QAC/BC,UAAU,EAAE,CAAC;QACbC,cAAc,EAAE;OACjB;MAEDX,cAAc,CAAC0B,cAAc,CAACC,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;MAEnD9B,OAAO,CAAC+B,MAAM,CAAC+B,aAAa,CAAC,CAAChB,SAAS,CAAEe,UAAU,IAAI;QACrDnB,MAAM,CAACmB,UAAU,CAAC,CAACb,OAAO,CAAC1C,cAAc,CAAC;QAC1CoC,MAAM,CAACxC,cAAc,CAAC0B,cAAc,CAAC,CAACoC,gBAAgB,EAAE;QACxDtB,MAAM,CAACtC,6BAA6B,CAAC4B,SAAS,CAAC,CAACoC,GAAG,CAACJ,gBAAgB,EAAE;MACxE,CAAC,CAAC;MAEF,MAAMf,GAAG,GAAGhD,QAAQ,CAACiD,SAAS,CAAC7C,MAAM,CAAC;MACtCqC,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACe,IAAI,CAAC,CAAClB,OAAO,CAACc,aAAa,CAAC;MAC/Cb,GAAG,CAACK,KAAK,CAAChD,cAAc,CAAC;IAC3B,CAAC,CAAC;IAEFmC,EAAE,CAAC,oDAAoD,EAAE,MAAK;MAC5D,MAAMqB,aAAa,GAA2B;QAC5CtD,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;QACjCC,OAAO,EAAE,IAAID,IAAI,CAAC,YAAY,CAAC;QAC/BE,MAAM,EAAE,uBAAuB;QAC/BC,UAAU,EAAE,CAAC;QACbC,cAAc,EAAE;OACjB;MAEDb,OAAO,CAAC+B,MAAM,CAAC+B,aAAa,CAAC,CAAChB,SAAS,CAAC;QACtCS,IAAI,EAAEA,CAAA,KAAMC,IAAI,CAAC,oBAAoB,CAAC;QACtCC,KAAK,EAAGA,KAAK,IAAI;UACff,MAAM,CAACe,KAAK,CAACC,MAAM,CAAC,CAACL,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGhD,QAAQ,CAACiD,SAAS,CAAC7C,MAAM,CAAC;MACtC4C,GAAG,CAACK,KAAK,CAAC,aAAa,EAAE;QAAEI,MAAM,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAa,CAAE,CAAC;IACtE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF5D,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB0C,EAAE,CAAC,4FAA4F,EAAEnD,SAAS,CAAC,MAAK;MAC9G,MAAMiB,EAAE,GAAG,CAAC;MACZ,MAAM8D,UAAU,GAAwB;QACtC7D,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;QACjCC,OAAO,EAAE,IAAID,IAAI,CAAC,YAAY,CAAC;QAC/BE,MAAM,EAAE,2BAA2B;QACnCC,UAAU,EAAE,CAAC;QACbC,cAAc,EAAE;OACjB;MAEDX,cAAc,CAAC0B,cAAc,CAACC,GAAG,CAACC,WAAW,CAAChB,QAAQ,CAAC;MAEvD,IAAIiD,SAAS,GAAG,KAAK;MACrB/D,OAAO,CAACsE,MAAM,CAAC/D,EAAE,EAAE8D,UAAU,CAAC,CAACvB,SAAS,CAAEe,UAAU,IAAI;QACtDnB,MAAM,CAACmB,UAAU,CAAC,CAACb,OAAO,CAAC1C,cAAc,CAAC;QAC1CoC,MAAM,CAACxC,cAAc,CAAC0B,cAAc,CAAC,CAACoC,gBAAgB,EAAE;QACxDtB,MAAM,CAACtC,6BAA6B,CAAC4B,SAAS,CAAC,CAACiC,oBAAoB,CAClE,uBAAuB,CACxB;QACDF,SAAS,GAAG,IAAI;MAClB,CAAC,CAAC;MAEF,MAAMQ,MAAM,GAAGtE,QAAQ,CAACiD,SAAS,CAAC,GAAG7C,MAAM,IAAIE,EAAE,EAAE,CAAC;MACpDmC,MAAM,CAAC6B,MAAM,CAACpB,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MAEzCkB,MAAM,CAACjB,KAAK,CAAC;QACX,GAAGhD,cAAc;QACjBE,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;QACjCC,OAAO,EAAE,IAAID,IAAI,CAAC,YAAY;OAC/B,CAAC;MAEF,MAAM+D,MAAM,GAAGvE,QAAQ,CAACiD,SAAS,CAAC,GAAG7C,MAAM,IAAIE,EAAE,EAAE,CAAC;MACpDmC,MAAM,CAAC8B,MAAM,CAACrB,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACzCX,MAAM,CAAC8B,MAAM,CAACrB,OAAO,CAACe,IAAI,CAAC,CAAClB,OAAO,CAACqB,UAAU,CAAC;MAC/CG,MAAM,CAAClB,KAAK,CAAChD,cAAc,CAAC;MAE5Bf,IAAI,CAAC,GAAG,CAAC;MAETmD,MAAM,CAACvC,8BAA8B,CAAC4B,MAAM,CAAC,CAACiC,gBAAgB,EAAE;MAChEtB,MAAM,CAACqB,SAAS,CAAC,CAACI,QAAQ,EAAE;IAC9B,CAAC,CAAC,CAAC;IAEH1B,EAAE,CAAC,0FAA0F,EAAEnD,SAAS,CAAC,MAAK;MAC5G,MAAMiB,EAAE,GAAG,CAAC;MACZ,MAAM8D,UAAU,GAAwB;QACtC7D,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;QACjCC,OAAO,EAAE,IAAID,IAAI,CAAC,YAAY,CAAC;QAC/BE,MAAM,EAAE,2BAA2B;QACnCC,UAAU,EAAE,CAAC;QACbC,cAAc,EAAE;OACjB;MAEDX,cAAc,CAAC0B,cAAc,CAACC,GAAG,CAACC,WAAW,CAAChB,QAAQ,CAAC;MAEvD,IAAIiD,SAAS,GAAG,KAAK;MACrB/D,OAAO,CAACsE,MAAM,CAAC/D,EAAE,EAAE8D,UAAU,CAAC,CAACvB,SAAS,CAAEe,UAAU,IAAI;QACtDnB,MAAM,CAACmB,UAAU,CAAC,CAACb,OAAO,CAAC1C,cAAc,CAAC;QAC1CoC,MAAM,CAACxC,cAAc,CAAC0B,cAAc,CAAC,CAACoC,gBAAgB,EAAE;QACxDtB,MAAM,CAACtC,6BAA6B,CAAC4B,SAAS,CAAC,CAACiC,oBAAoB,CAClE,uBAAuB,CACxB;QACDF,SAAS,GAAG,IAAI;MAClB,CAAC,CAAC;MAEF,MAAMQ,MAAM,GAAGtE,QAAQ,CAACiD,SAAS,CAAC,GAAG7C,MAAM,IAAIE,EAAE,EAAE,CAAC;MACpDmC,MAAM,CAAC6B,MAAM,CAACpB,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MAEzCkB,MAAM,CAACjB,KAAK,CAAC;QACX,GAAGhD,cAAc;QACjBE,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;QACjCC,OAAO,EAAE,IAAID,IAAI,CAAC,YAAY;OAC/B,CAAC;MAEF,MAAM+D,MAAM,GAAGvE,QAAQ,CAACiD,SAAS,CAAC,GAAG7C,MAAM,IAAIE,EAAE,EAAE,CAAC;MACpDmC,MAAM,CAAC8B,MAAM,CAACrB,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACzCX,MAAM,CAAC8B,MAAM,CAACrB,OAAO,CAACe,IAAI,CAAC,CAAClB,OAAO,CAACqB,UAAU,CAAC;MAC/CG,MAAM,CAAClB,KAAK,CAAChD,cAAc,CAAC;MAE5Bf,IAAI,CAAC,GAAG,CAAC;MAETmD,MAAM,CAACvC,8BAA8B,CAAC4B,MAAM,CAAC,CAACiC,gBAAgB,EAAE;MAChEtB,MAAM,CAACqB,SAAS,CAAC,CAACI,QAAQ,EAAE;IAC9B,CAAC,CAAC,CAAC;IAEH1B,EAAE,CAAC,8FAA8F,EAAEnD,SAAS,CAAC,MAAK;MAChH,MAAMiB,EAAE,GAAG,CAAC;MACZ,MAAM8D,UAAU,GAAwB;QACtC7D,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;QACjCC,OAAO,EAAE,IAAID,IAAI,CAAC,YAAY,CAAC;QAC/BE,MAAM,EAAE,2BAA2B;QACnCC,UAAU,EAAE,CAAC;QACbC,cAAc,EAAE;OACjB;MAEDX,cAAc,CAAC0B,cAAc,CAACC,GAAG,CAACC,WAAW,CAAChB,QAAQ,CAAC;MAEvD,IAAIiD,SAAS,GAAG,KAAK;MACrB/D,OAAO,CAACsE,MAAM,CAAC/D,EAAE,EAAE8D,UAAU,CAAC,CAACvB,SAAS,CAAEe,UAAU,IAAI;QACtDnB,MAAM,CAACmB,UAAU,CAAC,CAACb,OAAO,CAAC1C,cAAc,CAAC;QAC1CoC,MAAM,CAACxC,cAAc,CAAC0B,cAAc,CAAC,CAACoC,gBAAgB,EAAE;QACxDtB,MAAM,CAACtC,6BAA6B,CAAC4B,SAAS,CAAC,CAACiC,oBAAoB,CAClE,uBAAuB,CACxB;QACDF,SAAS,GAAG,IAAI;MAClB,CAAC,CAAC;MAEF,MAAMQ,MAAM,GAAGtE,QAAQ,CAACiD,SAAS,CAAC,GAAG7C,MAAM,IAAIE,EAAE,EAAE,CAAC;MACpDmC,MAAM,CAAC6B,MAAM,CAACpB,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MAEzCkB,MAAM,CAACjB,KAAK,CAAC;QACX,GAAGhD,cAAc;QACjBE,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;QACjCC,OAAO,EAAE,IAAID,IAAI,CAAC,YAAY;OAC/B,CAAC;MAEF,MAAM+D,MAAM,GAAGvE,QAAQ,CAACiD,SAAS,CAAC,GAAG7C,MAAM,IAAIE,EAAE,EAAE,CAAC;MACpDmC,MAAM,CAAC8B,MAAM,CAACrB,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACzCX,MAAM,CAAC8B,MAAM,CAACrB,OAAO,CAACe,IAAI,CAAC,CAAClB,OAAO,CAACqB,UAAU,CAAC;MAC/CG,MAAM,CAAClB,KAAK,CAAChD,cAAc,CAAC;MAE5Bf,IAAI,CAAC,GAAG,CAAC;MAETmD,MAAM,CAACvC,8BAA8B,CAAC4B,MAAM,CAAC,CAACiC,gBAAgB,EAAE;MAChEtB,MAAM,CAACqB,SAAS,CAAC,CAACI,QAAQ,EAAE;IAC9B,CAAC,CAAC,CAAC;IAEH1B,EAAE,CAAC,gGAAgG,EAAEnD,SAAS,CAAC,MAAK;MAClH,MAAMiB,EAAE,GAAG,CAAC;MAEZ,MAAMkE,iBAAiB,GAAG,IAAIhE,IAAI,CAAC,YAAY,CAAC;MAChD,MAAMiE,eAAe,GAAG,IAAIjE,IAAI,CAAC,YAAY,CAAC;MAE9C,MAAM4D,UAAU,GAAwB;QACtC7D,SAAS,EAAEiE,iBAAiB;QAC5B/D,OAAO,EAAEgE,eAAe;QACxB/D,MAAM,EAAE,gCAAgC;QACxCC,UAAU,EAAE,CAAC;QACbC,cAAc,EAAE;OACjB;MAEDX,cAAc,CAAC0B,cAAc,CAACC,GAAG,CAACC,WAAW,CAAChB,QAAQ,CAAC;MAEvDd,OAAO,CAACsE,MAAM,CAAC/D,EAAE,EAAE8D,UAAU,CAAC,CAACvB,SAAS,CAAEe,UAAU,IAAI;QACtDnB,MAAM,CAACmB,UAAU,CAAC,CAACb,OAAO,CAAC1C,cAAc,CAAC;QAC1CoC,MAAM,CAACxC,cAAc,CAAC0B,cAAc,CAAC,CAACoC,gBAAgB,EAAE;QAExDtB,MAAM,CAACtC,6BAA6B,CAAC4B,SAAS,CAAC,CAACoC,GAAG,CAACJ,gBAAgB,EAAE;MACxE,CAAC,CAAC;MAEF,MAAMO,MAAM,GAAGtE,QAAQ,CAACiD,SAAS,CAAC,GAAG7C,MAAM,IAAIE,EAAE,EAAE,CAAC;MACpDmC,MAAM,CAAC6B,MAAM,CAACpB,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MAEzCkB,MAAM,CAACjB,KAAK,CAAC;QACX,GAAGhD,cAAc;QACjBE,SAAS,EAAEiE,iBAAiB;QAC5B/D,OAAO,EAAEgE;OACV,CAAC;MAEF,MAAMF,MAAM,GAAGvE,QAAQ,CAACiD,SAAS,CAAC,GAAG7C,MAAM,IAAIE,EAAE,EAAE,CAAC;MACpDmC,MAAM,CAAC8B,MAAM,CAACrB,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACzCX,MAAM,CAAC8B,MAAM,CAACrB,OAAO,CAACe,IAAI,CAAC,CAAClB,OAAO,CAACqB,UAAU,CAAC;MAC/CG,MAAM,CAAClB,KAAK,CAAChD,cAAc,CAAC;MAE5Bf,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;IAEHkD,EAAE,CAAC,yEAAyE,EAAE,MAAK;MACjF,MAAMlC,EAAE,GAAG,CAAC;MACZ,MAAM8D,UAAU,GAAwB;QACtC7D,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;QACjCC,OAAO,EAAE,IAAID,IAAI,CAAC,YAAY,CAAC;QAC/BE,MAAM,EAAE,2BAA2B;QACnCC,UAAU,EAAE,CAAC;QACbC,cAAc,EAAE;OACjB;MAEDX,cAAc,CAAC0B,cAAc,CAACC,GAAG,CAACC,WAAW,CAAC,IAAI,CAAC;MAEnD9B,OAAO,CAACsE,MAAM,CAAC/D,EAAE,EAAE8D,UAAU,CAAC,CAACvB,SAAS,CAAEe,UAAU,IAAI;QACtDnB,MAAM,CAACmB,UAAU,CAAC,CAACb,OAAO,CAAC1C,cAAc,CAAC;QAC1CoC,MAAM,CAACxC,cAAc,CAAC0B,cAAc,CAAC,CAACoC,gBAAgB,EAAE;QACxDtB,MAAM,CAACtC,6BAA6B,CAAC4B,SAAS,CAAC,CAACoC,GAAG,CAACJ,gBAAgB,EAAE;MACxE,CAAC,CAAC;MAEF,MAAMO,MAAM,GAAGtE,QAAQ,CAACiD,SAAS,CAAC,GAAG7C,MAAM,IAAIE,EAAE,EAAE,CAAC;MACpDmC,MAAM,CAAC6B,MAAM,CAACpB,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACzCkB,MAAM,CAACjB,KAAK,CAAChD,cAAc,CAAC;MAE5B,MAAMkE,MAAM,GAAGvE,QAAQ,CAACiD,SAAS,CAAC,GAAG7C,MAAM,IAAIE,EAAE,EAAE,CAAC;MACpDmC,MAAM,CAAC8B,MAAM,CAACrB,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACzCX,MAAM,CAAC8B,MAAM,CAACrB,OAAO,CAACe,IAAI,CAAC,CAAClB,OAAO,CAACqB,UAAU,CAAC;MAC/CG,MAAM,CAAClB,KAAK,CAAChD,cAAc,CAAC;IAC9B,CAAC,CAAC;IAEFmC,EAAE,CAAC,wDAAwD,EAAE,MAAK;MAChE,MAAMlC,EAAE,GAAG,GAAG;MACd,MAAM8D,UAAU,GAAwB;QACtC1D,MAAM,EAAE;OACT;MAEDX,OAAO,CAACsE,MAAM,CAAC/D,EAAE,EAAE8D,UAAU,CAAC,CAACvB,SAAS,CAAC;QACvCS,IAAI,EAAEA,CAAA,KAAMC,IAAI,CAAC,oBAAoB,CAAC;QACtCC,KAAK,EAAGA,KAAK,IAAI;UACff,MAAM,CAACe,KAAK,CAACC,MAAM,CAAC,CAACL,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGhD,QAAQ,CAACiD,SAAS,CAAC,GAAG7C,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjD0C,GAAG,CAACK,KAAK,CAAC,WAAW,EAAE;QAAEI,MAAM,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAW,CAAE,CAAC;IAClE,CAAC,CAAC;IAEFlB,EAAE,CAAC,gDAAgD,EAAE,MAAK;MACxD,MAAMlC,EAAE,GAAG,CAAC;MACZ,MAAM8D,UAAU,GAAwB;QACtC1D,MAAM,EAAE;OACT;MAEDX,OAAO,CAACsE,MAAM,CAAC/D,EAAE,EAAE8D,UAAU,CAAC,CAACvB,SAAS,CAAC;QACvCS,IAAI,EAAEA,CAAA,KAAMC,IAAI,CAAC,oBAAoB,CAAC;QACtCC,KAAK,EAAGA,KAAK,IAAI;UACff,MAAM,CAACe,KAAK,CAACC,MAAM,CAAC,CAACL,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMkB,MAAM,GAAGtE,QAAQ,CAACiD,SAAS,CAAC,GAAG7C,MAAM,IAAIE,EAAE,EAAE,CAAC;MACpDmC,MAAM,CAAC6B,MAAM,CAACpB,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACzCkB,MAAM,CAACjB,KAAK,CAAChD,cAAc,CAAC;MAE5B,MAAMkE,MAAM,GAAGvE,QAAQ,CAACiD,SAAS,CAAC,GAAG7C,MAAM,IAAIE,EAAE,EAAE,CAAC;MACpDiE,MAAM,CAAClB,KAAK,CAAC,aAAa,EAAE;QAAEI,MAAM,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAa,CAAE,CAAC;IACzE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF5D,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtB0C,EAAE,CAAC,4BAA4B,EAAE,MAAK;MACpC,MAAMlC,EAAE,GAAG,CAAC;MAEZP,OAAO,CAAC2E,MAAM,CAACpE,EAAE,CAAC,CAACuC,SAAS,CAAC,MAAK;QAChCJ,MAAM,EAAE,CAACkC,OAAO,EAAE;MACpB,CAAC,CAAC;MAEF,MAAM3B,GAAG,GAAGhD,QAAQ,CAACiD,SAAS,CAAC,GAAG7C,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDmC,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;IAEFb,EAAE,CAAC,gDAAgD,EAAE,MAAK;MACxD,MAAMlC,EAAE,GAAG,GAAG;MAEdP,OAAO,CAAC2E,MAAM,CAACpE,EAAE,CAAC,CAACuC,SAAS,CAAC;QAC3BS,IAAI,EAAEA,CAAA,KAAMC,IAAI,CAAC,oBAAoB,CAAC;QACtCC,KAAK,EAAGA,KAAK,IAAI;UACff,MAAM,CAACe,KAAK,CAACC,MAAM,CAAC,CAACL,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGhD,QAAQ,CAACiD,SAAS,CAAC,GAAG7C,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjD0C,GAAG,CAACK,KAAK,CAAC,WAAW,EAAE;QAAEI,MAAM,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAW,CAAE,CAAC;IAClE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF5D,QAAQ,CAAC,oBAAoB,EAAE,MAAK;IAClC0C,EAAE,CAAC,8CAA8C,EAAE,MAAK;MACtD,MAAM7B,UAAU,GAAG,CAAC;MACpB,MAAMgC,eAAe,GAAG,CAACtC,cAAc,CAAC;MAExCN,OAAO,CAAC6E,kBAAkB,CAACjE,UAAU,CAAC,CAACkC,SAAS,CAAEC,WAAW,IAAI;QAC/DL,MAAM,CAACK,WAAW,CAAC,CAACC,OAAO,CAACJ,eAAe,CAAC;MAC9C,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGhD,QAAQ,CAACiD,SAAS,CAAC,GAAG7C,MAAM,aAAaO,UAAU,EAAE,CAAC;MAClE8B,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,eAAe,CAAC;IAC5B,CAAC,CAAC;IAEFH,EAAE,CAAC,6DAA6D,EAAE,MAAK;MACrE,MAAM7B,UAAU,GAAG,GAAG;MAEtBZ,OAAO,CAAC6E,kBAAkB,CAACjE,UAAU,CAAC,CAACkC,SAAS,CAAC;QAC/CS,IAAI,EAAEA,CAAA,KAAMC,IAAI,CAAC,oBAAoB,CAAC;QACtCC,KAAK,EAAGA,KAAK,IAAI;UACff,MAAM,CAACe,KAAK,CAACC,MAAM,CAAC,CAACL,IAAI,CAAC,GAAG,CAAC;QAChC;OACD,CAAC;MAEF,MAAMJ,GAAG,GAAGhD,QAAQ,CAACiD,SAAS,CAAC,GAAG7C,MAAM,aAAaO,UAAU,EAAE,CAAC;MAClEqC,GAAG,CAACK,KAAK,CAAC,WAAW,EAAE;QAAEI,MAAM,EAAE,GAAG;QAAEC,UAAU,EAAE;MAAW,CAAE,CAAC;IAClE,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}