{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/Github/SICMA/sicma-ui/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { NoopAnimationsModule } from '@angular/platform-browser/animations';\nimport { TaxRegimeService } from '@contractor-dashboard/services/tax-regime.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { of, throwError } from 'rxjs';\nimport { TaxInfoComponent } from './tax-info.component';\ndescribe('TaxInfoComponent', () => {\n  let component;\n  let fixture;\n  let alertService;\n  let taxRegimeService;\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    const taxRegimeServiceSpy = jasmine.createSpyObj('TaxRegimeService', ['getAll']);\n    const alertServiceSpy = jasmine.createSpyObj('AlertService', ['error']);\n    yield TestBed.configureTestingModule({\n      imports: [TaxInfoComponent, NoopAnimationsModule],\n      providers: [{\n        provide: TaxRegimeService,\n        useValue: taxRegimeServiceSpy\n      }, {\n        provide: AlertService,\n        useValue: alertServiceSpy\n      }]\n    }).compileComponents();\n    taxRegimeService = TestBed.inject(TaxRegimeService);\n    alertService = TestBed.inject(AlertService);\n    taxRegimeService.getAll.and.returnValue(of([]));\n  }));\n  beforeEach(() => {\n    fixture = TestBed.createComponent(TaxInfoComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  describe('Initialization', () => {\n    it('should initialize form with empty values', () => {\n      expect(component.form.get('regimeType')?.value).toBe('');\n      expect(component.form.get('regimeSupportFile')?.value).toBeNull();\n      expect(component.taxFormFileName).toBeNull();\n    });\n    it('should handle error when loading tax regimes', () => {\n      taxRegimeService.getAll.and.returnValue(throwError(() => new Error()));\n      component.loadTaxRegimes();\n      expect(alertService.error).toHaveBeenCalledWith('Error al cargar los regímenes tributarios');\n    });\n  });\n  describe('Form Validation', () => {\n    it('should validate required fields', () => {\n      expect(component.form.get('regimeType')?.hasError('required')).toBeTrue();\n    });\n    it('should be valid when all required fields are filled', () => {\n      const form = component.form;\n      form.patchValue({\n        regimeType: 1\n      });\n      expect(form.valid).toBeTruthy();\n    });\n  });\n  describe('File Handling', () => {\n    it('should accept PDF file and set filename', () => {\n      const file = new File([''], 'test.pdf', {\n        type: 'application/pdf'\n      });\n      const event = {\n        target: {\n          files: [file]\n        }\n      };\n      component.onFileSelected(event, 'regimeSupportFile');\n      expect(component.taxFormFileName).toBe('test.pdf');\n      expect(alertService.error).not.toHaveBeenCalled();\n    });\n    it('should reject non-PDF file', () => {\n      const file = new File([''], 'test.txt', {\n        type: 'text/plain'\n      });\n      const event = {\n        target: {\n          files: [file]\n        }\n      };\n      component.onFileSelected(event, 'regimeSupportFile');\n      expect(component.taxFormFileName).toBeNull();\n      expect(alertService.error).toHaveBeenCalledWith('Solo se permiten archivos PDF');\n    });\n    it('should reject file larger than 1MB', () => {\n      const largeFile = new File([''.padStart(1024 * 1024 + 1, 'x')], 'large.pdf', {\n        type: 'application/pdf'\n      });\n      const event = {\n        target: {\n          files: [largeFile]\n        }\n      };\n      component.onFileSelected(event, 'regimeSupportFile');\n      expect(component.taxFormFileName).toBeNull();\n      expect(alertService.error).toHaveBeenCalledWith('El archivo no debe superar 1MB');\n    });\n  });\n  describe('Initial Data Loading', () => {\n    it('should load initial data correctly', () => {\n      const initialData = {\n        id: 1,\n        contractorContractId: 1,\n        bankId: 1,\n        bankAccountTypeId: 1,\n        accountNumber: '*********',\n        bankCertificateFileUrl: 'test.pdf',\n        taxRegimeId: 1,\n        taxFormFileUrl: 'test.pdf',\n        epsId: 1,\n        arlId: 1,\n        pensionFundId: 1,\n        hasDependents: false,\n        hasHousingInterest: false,\n        housingInterestAnnualPayment: 0,\n        hasPrepaidMedicine: false,\n        prepaidMedicineAnnualPayment: 0,\n        hasAfcAccount: false,\n        hasVoluntarySavings: false,\n        afcAccountAnnualPayment: 0,\n        voluntarySavingsAnnualPayment: 0\n      };\n      component.initialData = initialData;\n      component.ngOnChanges({\n        initialData: {\n          currentValue: initialData,\n          previousValue: undefined,\n          firstChange: true,\n          isFirstChange: () => true\n        }\n      });\n      expect(component.form.get('regimeType')?.value).toBe(1);\n    });\n    it('should handle undefined initial data', () => {\n      component.ngOnChanges({\n        initialData: {\n          currentValue: undefined,\n          previousValue: undefined,\n          firstChange: true,\n          isFirstChange: () => true\n        }\n      });\n      expect(component.form.get('regimeType')?.value).toBe('');\n      expect(component.taxFormFileName).toBeNull();\n    });\n    it('should handle file validation when file url exists', () => {\n      component.initialData = {\n        id: 1,\n        contractorContractId: 1,\n        taxRegimeId: 1,\n        taxFormFileUrl: 'http://example.com/file.pdf',\n        epsId: 1,\n        arlId: 1,\n        pensionFundId: 1,\n        bankId: 1,\n        bankAccountTypeId: 1,\n        accountNumber: '',\n        hasDependents: false,\n        hasHousingInterest: false,\n        hasPrepaidMedicine: false,\n        hasAfcAccount: false,\n        hasVoluntarySavings: false\n      };\n      component.form.patchValue({\n        regimeType: 1\n      });\n      expect(component.isValid).toBeTrue();\n    });\n    it('should handle file validation when no file url exists', () => {\n      component.initialData = {\n        id: 1,\n        contractorContractId: 1,\n        taxRegimeId: 1,\n        taxFormFileUrl: undefined,\n        epsId: 1,\n        arlId: 1,\n        pensionFundId: 1,\n        bankId: 1,\n        bankAccountTypeId: 1,\n        accountNumber: '',\n        hasDependents: false,\n        hasHousingInterest: false,\n        hasPrepaidMedicine: false,\n        hasAfcAccount: false,\n        hasVoluntarySavings: false\n      };\n      component.form.patchValue({\n        regimeType: 1\n      });\n      expect(component.isValid).toBeFalse();\n      const mockFile = new File([''], 'test.pdf', {\n        type: 'application/pdf'\n      });\n      component.form.patchValue({\n        regimeSupportFile: mockFile\n      });\n      expect(component.isValid).toBeTrue();\n    });\n  });\n  describe('Supervisor Mode', () => {\n    it('should show readonly inputs in supervisor mode', () => {\n      component.isSupervisor = true;\n      fixture.detectChanges();\n      expect(component.isSupervisor).toBeTrue();\n    });\n  });\n  describe('File Download', () => {\n    it('should open file in new tab when downloading', () => {\n      component.initialData = {\n        id: 1,\n        contractorContractId: 1,\n        taxRegimeId: 1,\n        taxFormFileUrl: 'http://example.com/file.pdf',\n        epsId: 1,\n        arlId: 1,\n        pensionFundId: 1,\n        bankId: 1,\n        bankAccountTypeId: 1,\n        accountNumber: '',\n        hasDependents: false,\n        hasHousingInterest: false,\n        hasPrepaidMedicine: false,\n        hasAfcAccount: false,\n        hasVoluntarySavings: false\n      };\n      const windowSpy = spyOn(window, 'open');\n      component.downloadFile();\n      expect(windowSpy).toHaveBeenCalledWith('http://example.com/file.pdf', '_blank');\n    });\n    it('should not attempt to open when no file url exists', () => {\n      component.initialData = {\n        id: 1,\n        contractorContractId: 1,\n        taxRegimeId: 1,\n        taxFormFileUrl: undefined,\n        epsId: 1,\n        arlId: 1,\n        pensionFundId: 1,\n        bankId: 1,\n        bankAccountTypeId: 1,\n        accountNumber: '',\n        hasDependents: false,\n        hasHousingInterest: false,\n        hasPrepaidMedicine: false,\n        hasAfcAccount: false,\n        hasVoluntarySavings: false\n      };\n      const windowSpy = spyOn(window, 'open');\n      component.downloadFile();\n      expect(windowSpy).not.toHaveBeenCalled();\n    });\n  });\n});", "map": {"version": 3, "names": ["TestBed", "NoopAnimationsModule", "TaxRegimeService", "AlertService", "of", "throwError", "TaxInfoComponent", "describe", "component", "fixture", "alertService", "taxRegimeService", "beforeEach", "_asyncToGenerator", "taxRegimeServiceSpy", "jasmine", "createSpyObj", "alertServiceSpy", "configureTestingModule", "imports", "providers", "provide", "useValue", "compileComponents", "inject", "getAll", "and", "returnValue", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy", "form", "get", "value", "toBe", "toBeNull", "taxFormFileName", "Error", "loadTaxRegimes", "error", "toHaveBeenCalledWith", "<PERSON><PERSON><PERSON><PERSON>", "toBeTrue", "patchValue", "regimeType", "valid", "file", "File", "type", "event", "target", "files", "onFileSelected", "not", "toHaveBeenCalled", "largeFile", "padStart", "initialData", "id", "contractorContractId", "bankId", "bankAccountTypeId", "accountNumber", "bankCertificateFileUrl", "taxRegimeId", "taxFormFileUrl", "epsId", "arlId", "pensionFundId", "hasDependents", "hasHousingInterest", "housingInterestAnnualPayment", "hasPrepaidMedicine", "prepaidMedicineAnnualPayment", "hasAfcAccount", "hasVoluntarySavings", "afcAccountAnnualPayment", "voluntarySavingsAnnualPayment", "ngOnChanges", "currentValue", "previousValue", "undefined", "firstChange", "isFirstChange", "<PERSON><PERSON><PERSON><PERSON>", "toBeFalse", "mockFile", "regimeSupportFile", "isSupervisor", "windowSpy", "spyOn", "window", "downloadFile"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\components\\monthly-reports-tab\\monthly-report-dialog\\monthly-report-initial-documentation\\tax-info\\tax-info.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\nimport { NoopAnimationsModule } from '@angular/platform-browser/animations';\nimport { InitialReportDocumentation } from '@contractor-dashboard/models/initial-report-documentation.model';\nimport { TaxRegimeService } from '@contractor-dashboard/services/tax-regime.service';\nimport { AlertService } from '@shared/services/alert.service';\nimport { of, throwError } from 'rxjs';\nimport { TaxInfoComponent } from './tax-info.component';\n\ndescribe('TaxInfoComponent', () => {\n  let component: TaxInfoComponent;\n  let fixture: ComponentFixture<TaxInfoComponent>;\n  let alertService: jasmine.SpyObj<AlertService>;\n  let taxRegimeService: jasmine.SpyObj<TaxRegimeService>;\n\n  beforeEach(async () => {\n    const taxRegimeServiceSpy = jasmine.createSpyObj('TaxRegimeService', [\n      'getAll',\n    ]);\n    const alertServiceSpy = jasmine.createSpyObj('AlertService', ['error']);\n\n    await TestBed.configureTestingModule({\n      imports: [TaxInfoComponent, NoopAnimationsModule],\n      providers: [\n        { provide: TaxRegimeService, useValue: taxRegimeServiceSpy },\n        { provide: AlertService, useValue: alertServiceSpy },\n      ],\n    }).compileComponents();\n\n    taxRegimeService = TestBed.inject(\n      TaxRegimeService,\n    ) as jasmine.SpyObj<TaxRegimeService>;\n    alertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;\n\n    taxRegimeService.getAll.and.returnValue(of([]));\n  });\n\n  beforeEach(() => {\n    fixture = TestBed.createComponent(TaxInfoComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  describe('Initialization', () => {\n    it('should initialize form with empty values', () => {\n      expect(component.form.get('regimeType')?.value).toBe('');\n      expect(component.form.get('regimeSupportFile')?.value).toBeNull();\n      expect(component.taxFormFileName).toBeNull();\n    });\n\n    it('should handle error when loading tax regimes', () => {\n      taxRegimeService.getAll.and.returnValue(throwError(() => new Error()));\n      component.loadTaxRegimes();\n      expect(alertService.error).toHaveBeenCalledWith(\n        'Error al cargar los regímenes tributarios',\n      );\n    });\n  });\n\n  describe('Form Validation', () => {\n    it('should validate required fields', () => {\n      expect(component.form.get('regimeType')?.hasError('required')).toBeTrue();\n    });\n\n    it('should be valid when all required fields are filled', () => {\n      const form = component.form;\n      form.patchValue({\n        regimeType: 1,\n      });\n      expect(form.valid).toBeTruthy();\n    });\n  });\n\n  describe('File Handling', () => {\n    it('should accept PDF file and set filename', () => {\n      const file = new File([''], 'test.pdf', { type: 'application/pdf' });\n      const event = { target: { files: [file] } } as unknown as Event;\n\n      component.onFileSelected(event, 'regimeSupportFile');\n      expect(component.taxFormFileName).toBe('test.pdf');\n      expect(alertService.error).not.toHaveBeenCalled();\n    });\n\n    it('should reject non-PDF file', () => {\n      const file = new File([''], 'test.txt', { type: 'text/plain' });\n      const event = { target: { files: [file] } } as unknown as Event;\n\n      component.onFileSelected(event, 'regimeSupportFile');\n      expect(component.taxFormFileName).toBeNull();\n      expect(alertService.error).toHaveBeenCalledWith(\n        'Solo se permiten archivos PDF',\n      );\n    });\n\n    it('should reject file larger than 1MB', () => {\n      const largeFile = new File(\n        [''.padStart(1024 * 1024 + 1, 'x')],\n        'large.pdf',\n        { type: 'application/pdf' },\n      );\n      const event = { target: { files: [largeFile] } } as unknown as Event;\n\n      component.onFileSelected(event, 'regimeSupportFile');\n      expect(component.taxFormFileName).toBeNull();\n      expect(alertService.error).toHaveBeenCalledWith(\n        'El archivo no debe superar 1MB',\n      );\n    });\n  });\n\n  describe('Initial Data Loading', () => {\n    it('should load initial data correctly', () => {\n      const initialData: InitialReportDocumentation = {\n        id: 1,\n        contractorContractId: 1,\n        bankId: 1,\n        bankAccountTypeId: 1,\n        accountNumber: '*********',\n        bankCertificateFileUrl: 'test.pdf',\n        taxRegimeId: 1,\n        taxFormFileUrl: 'test.pdf',\n        epsId: 1,\n        arlId: 1,\n        pensionFundId: 1,\n        hasDependents: false,\n        hasHousingInterest: false,\n        housingInterestAnnualPayment: 0,\n        hasPrepaidMedicine: false,\n        prepaidMedicineAnnualPayment: 0,\n        hasAfcAccount: false,\n        hasVoluntarySavings: false,\n        afcAccountAnnualPayment: 0,\n        voluntarySavingsAnnualPayment: 0,\n      };\n\n      component.initialData = initialData;\n      component.ngOnChanges({\n        initialData: {\n          currentValue: initialData,\n          previousValue: undefined,\n          firstChange: true,\n          isFirstChange: () => true,\n        },\n      });\n\n      expect(component.form.get('regimeType')?.value).toBe(1);\n    });\n\n    it('should handle undefined initial data', () => {\n      component.ngOnChanges({\n        initialData: {\n          currentValue: undefined,\n          previousValue: undefined,\n          firstChange: true,\n          isFirstChange: () => true,\n        },\n      });\n\n      expect(component.form.get('regimeType')?.value).toBe('');\n      expect(component.taxFormFileName).toBeNull();\n    });\n\n    it('should handle file validation when file url exists', () => {\n      component.initialData = {\n        id: 1,\n        contractorContractId: 1,\n        taxRegimeId: 1,\n        taxFormFileUrl: 'http://example.com/file.pdf',\n        epsId: 1,\n        arlId: 1,\n        pensionFundId: 1,\n        bankId: 1,\n        bankAccountTypeId: 1,\n        accountNumber: '',\n        hasDependents: false,\n        hasHousingInterest: false,\n        hasPrepaidMedicine: false,\n        hasAfcAccount: false,\n        hasVoluntarySavings: false,\n      };\n\n      component.form.patchValue({\n        regimeType: 1,\n      });\n\n      expect(component.isValid).toBeTrue();\n    });\n\n    it('should handle file validation when no file url exists', () => {\n      component.initialData = {\n        id: 1,\n        contractorContractId: 1,\n        taxRegimeId: 1,\n        taxFormFileUrl: undefined,\n        epsId: 1,\n        arlId: 1,\n        pensionFundId: 1,\n        bankId: 1,\n        bankAccountTypeId: 1,\n        accountNumber: '',\n        hasDependents: false,\n        hasHousingInterest: false,\n        hasPrepaidMedicine: false,\n        hasAfcAccount: false,\n        hasVoluntarySavings: false,\n      };\n\n      component.form.patchValue({\n        regimeType: 1,\n      });\n\n      expect(component.isValid).toBeFalse();\n\n      const mockFile = new File([''], 'test.pdf', { type: 'application/pdf' });\n      component.form.patchValue({\n        regimeSupportFile: mockFile,\n      });\n\n      expect(component.isValid).toBeTrue();\n    });\n  });\n\n  describe('Supervisor Mode', () => {\n    it('should show readonly inputs in supervisor mode', () => {\n      component.isSupervisor = true;\n      fixture.detectChanges();\n      expect(component.isSupervisor).toBeTrue();\n    });\n  });\n\n  describe('File Download', () => {\n    it('should open file in new tab when downloading', () => {\n      component.initialData = {\n        id: 1,\n        contractorContractId: 1,\n        taxRegimeId: 1,\n        taxFormFileUrl: 'http://example.com/file.pdf',\n        epsId: 1,\n        arlId: 1,\n        pensionFundId: 1,\n        bankId: 1,\n        bankAccountTypeId: 1,\n        accountNumber: '',\n        hasDependents: false,\n        hasHousingInterest: false,\n        hasPrepaidMedicine: false,\n        hasAfcAccount: false,\n        hasVoluntarySavings: false,\n      };\n\n      const windowSpy = spyOn(window, 'open');\n      component.downloadFile();\n\n      expect(windowSpy).toHaveBeenCalledWith(\n        'http://example.com/file.pdf',\n        '_blank',\n      );\n    });\n\n    it('should not attempt to open when no file url exists', () => {\n      component.initialData = {\n        id: 1,\n        contractorContractId: 1,\n        taxRegimeId: 1,\n        taxFormFileUrl: undefined,\n        epsId: 1,\n        arlId: 1,\n        pensionFundId: 1,\n        bankId: 1,\n        bankAccountTypeId: 1,\n        accountNumber: '',\n        hasDependents: false,\n        hasHousingInterest: false,\n        hasPrepaidMedicine: false,\n        hasAfcAccount: false,\n        hasVoluntarySavings: false,\n      };\n\n      const windowSpy = spyOn(window, 'open');\n      component.downloadFile();\n\n      expect(windowSpy).not.toHaveBeenCalled();\n    });\n  });\n});\n"], "mappings": ";AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AACjE,SAASC,oBAAoB,QAAQ,sCAAsC;AAE3E,SAASC,gBAAgB,QAAQ,mDAAmD;AACpF,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,EAAE,EAAEC,UAAU,QAAQ,MAAM;AACrC,SAASC,gBAAgB,QAAQ,sBAAsB;AAEvDC,QAAQ,CAAC,kBAAkB,EAAE,MAAK;EAChC,IAAIC,SAA2B;EAC/B,IAAIC,OAA2C;EAC/C,IAAIC,YAA0C;EAC9C,IAAIC,gBAAkD;EAEtDC,UAAU,eAAAC,iBAAA,CAAC,aAAW;IACpB,MAAMC,mBAAmB,GAAGC,OAAO,CAACC,YAAY,CAAC,kBAAkB,EAAE,CACnE,QAAQ,CACT,CAAC;IACF,MAAMC,eAAe,GAAGF,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC;IAEvE,MAAMhB,OAAO,CAACkB,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CAACb,gBAAgB,EAAEL,oBAAoB,CAAC;MACjDmB,SAAS,EAAE,CACT;QAAEC,OAAO,EAAEnB,gBAAgB;QAAEoB,QAAQ,EAAER;MAAmB,CAAE,EAC5D;QAAEO,OAAO,EAAElB,YAAY;QAAEmB,QAAQ,EAAEL;MAAe,CAAE;KAEvD,CAAC,CAACM,iBAAiB,EAAE;IAEtBZ,gBAAgB,GAAGX,OAAO,CAACwB,MAAM,CAC/BtB,gBAAgB,CACmB;IACrCQ,YAAY,GAAGV,OAAO,CAACwB,MAAM,CAACrB,YAAY,CAAiC;IAE3EQ,gBAAgB,CAACc,MAAM,CAACC,GAAG,CAACC,WAAW,CAACvB,EAAE,CAAC,EAAE,CAAC,CAAC;EACjD,CAAC,EAAC;EAEFQ,UAAU,CAAC,MAAK;IACdH,OAAO,GAAGT,OAAO,CAAC4B,eAAe,CAACtB,gBAAgB,CAAC;IACnDE,SAAS,GAAGC,OAAO,CAACoB,iBAAiB;IACrCpB,OAAO,CAACqB,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACxB,SAAS,CAAC,CAACyB,UAAU,EAAE;EAChC,CAAC,CAAC;EAEF1B,QAAQ,CAAC,gBAAgB,EAAE,MAAK;IAC9BwB,EAAE,CAAC,0CAA0C,EAAE,MAAK;MAClDC,MAAM,CAACxB,SAAS,CAAC0B,IAAI,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;MACxDL,MAAM,CAACxB,SAAS,CAAC0B,IAAI,CAACC,GAAG,CAAC,mBAAmB,CAAC,EAAEC,KAAK,CAAC,CAACE,QAAQ,EAAE;MACjEN,MAAM,CAACxB,SAAS,CAAC+B,eAAe,CAAC,CAACD,QAAQ,EAAE;IAC9C,CAAC,CAAC;IAEFP,EAAE,CAAC,8CAA8C,EAAE,MAAK;MACtDpB,gBAAgB,CAACc,MAAM,CAACC,GAAG,CAACC,WAAW,CAACtB,UAAU,CAAC,MAAM,IAAImC,KAAK,EAAE,CAAC,CAAC;MACtEhC,SAAS,CAACiC,cAAc,EAAE;MAC1BT,MAAM,CAACtB,YAAY,CAACgC,KAAK,CAAC,CAACC,oBAAoB,CAC7C,2CAA2C,CAC5C;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFpC,QAAQ,CAAC,iBAAiB,EAAE,MAAK;IAC/BwB,EAAE,CAAC,iCAAiC,EAAE,MAAK;MACzCC,MAAM,CAACxB,SAAS,CAAC0B,IAAI,CAACC,GAAG,CAAC,YAAY,CAAC,EAAES,QAAQ,CAAC,UAAU,CAAC,CAAC,CAACC,QAAQ,EAAE;IAC3E,CAAC,CAAC;IAEFd,EAAE,CAAC,qDAAqD,EAAE,MAAK;MAC7D,MAAMG,IAAI,GAAG1B,SAAS,CAAC0B,IAAI;MAC3BA,IAAI,CAACY,UAAU,CAAC;QACdC,UAAU,EAAE;OACb,CAAC;MACFf,MAAM,CAACE,IAAI,CAACc,KAAK,CAAC,CAACf,UAAU,EAAE;IACjC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF1B,QAAQ,CAAC,eAAe,EAAE,MAAK;IAC7BwB,EAAE,CAAC,yCAAyC,EAAE,MAAK;MACjD,MAAMkB,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE;QAAEC,IAAI,EAAE;MAAiB,CAAE,CAAC;MACpE,MAAMC,KAAK,GAAG;QAAEC,MAAM,EAAE;UAAEC,KAAK,EAAE,CAACL,IAAI;QAAC;MAAE,CAAsB;MAE/DzC,SAAS,CAAC+C,cAAc,CAACH,KAAK,EAAE,mBAAmB,CAAC;MACpDpB,MAAM,CAACxB,SAAS,CAAC+B,eAAe,CAAC,CAACF,IAAI,CAAC,UAAU,CAAC;MAClDL,MAAM,CAACtB,YAAY,CAACgC,KAAK,CAAC,CAACc,GAAG,CAACC,gBAAgB,EAAE;IACnD,CAAC,CAAC;IAEF1B,EAAE,CAAC,4BAA4B,EAAE,MAAK;MACpC,MAAMkB,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE;QAAEC,IAAI,EAAE;MAAY,CAAE,CAAC;MAC/D,MAAMC,KAAK,GAAG;QAAEC,MAAM,EAAE;UAAEC,KAAK,EAAE,CAACL,IAAI;QAAC;MAAE,CAAsB;MAE/DzC,SAAS,CAAC+C,cAAc,CAACH,KAAK,EAAE,mBAAmB,CAAC;MACpDpB,MAAM,CAACxB,SAAS,CAAC+B,eAAe,CAAC,CAACD,QAAQ,EAAE;MAC5CN,MAAM,CAACtB,YAAY,CAACgC,KAAK,CAAC,CAACC,oBAAoB,CAC7C,+BAA+B,CAChC;IACH,CAAC,CAAC;IAEFZ,EAAE,CAAC,oCAAoC,EAAE,MAAK;MAC5C,MAAM2B,SAAS,GAAG,IAAIR,IAAI,CACxB,CAAC,EAAE,CAACS,QAAQ,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,EACnC,WAAW,EACX;QAAER,IAAI,EAAE;MAAiB,CAAE,CAC5B;MACD,MAAMC,KAAK,GAAG;QAAEC,MAAM,EAAE;UAAEC,KAAK,EAAE,CAACI,SAAS;QAAC;MAAE,CAAsB;MAEpElD,SAAS,CAAC+C,cAAc,CAACH,KAAK,EAAE,mBAAmB,CAAC;MACpDpB,MAAM,CAACxB,SAAS,CAAC+B,eAAe,CAAC,CAACD,QAAQ,EAAE;MAC5CN,MAAM,CAACtB,YAAY,CAACgC,KAAK,CAAC,CAACC,oBAAoB,CAC7C,gCAAgC,CACjC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFpC,QAAQ,CAAC,sBAAsB,EAAE,MAAK;IACpCwB,EAAE,CAAC,oCAAoC,EAAE,MAAK;MAC5C,MAAM6B,WAAW,GAA+B;QAC9CC,EAAE,EAAE,CAAC;QACLC,oBAAoB,EAAE,CAAC;QACvBC,MAAM,EAAE,CAAC;QACTC,iBAAiB,EAAE,CAAC;QACpBC,aAAa,EAAE,WAAW;QAC1BC,sBAAsB,EAAE,UAAU;QAClCC,WAAW,EAAE,CAAC;QACdC,cAAc,EAAE,UAAU;QAC1BC,KAAK,EAAE,CAAC;QACRC,KAAK,EAAE,CAAC;QACRC,aAAa,EAAE,CAAC;QAChBC,aAAa,EAAE,KAAK;QACpBC,kBAAkB,EAAE,KAAK;QACzBC,4BAA4B,EAAE,CAAC;QAC/BC,kBAAkB,EAAE,KAAK;QACzBC,4BAA4B,EAAE,CAAC;QAC/BC,aAAa,EAAE,KAAK;QACpBC,mBAAmB,EAAE,KAAK;QAC1BC,uBAAuB,EAAE,CAAC;QAC1BC,6BAA6B,EAAE;OAChC;MAEDxE,SAAS,CAACoD,WAAW,GAAGA,WAAW;MACnCpD,SAAS,CAACyE,WAAW,CAAC;QACpBrB,WAAW,EAAE;UACXsB,YAAY,EAAEtB,WAAW;UACzBuB,aAAa,EAAEC,SAAS;UACxBC,WAAW,EAAE,IAAI;UACjBC,aAAa,EAAEA,CAAA,KAAM;;OAExB,CAAC;MAEFtD,MAAM,CAACxB,SAAS,CAAC0B,IAAI,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;IACzD,CAAC,CAAC;IAEFN,EAAE,CAAC,sCAAsC,EAAE,MAAK;MAC9CvB,SAAS,CAACyE,WAAW,CAAC;QACpBrB,WAAW,EAAE;UACXsB,YAAY,EAAEE,SAAS;UACvBD,aAAa,EAAEC,SAAS;UACxBC,WAAW,EAAE,IAAI;UACjBC,aAAa,EAAEA,CAAA,KAAM;;OAExB,CAAC;MAEFtD,MAAM,CAACxB,SAAS,CAAC0B,IAAI,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;MACxDL,MAAM,CAACxB,SAAS,CAAC+B,eAAe,CAAC,CAACD,QAAQ,EAAE;IAC9C,CAAC,CAAC;IAEFP,EAAE,CAAC,oDAAoD,EAAE,MAAK;MAC5DvB,SAAS,CAACoD,WAAW,GAAG;QACtBC,EAAE,EAAE,CAAC;QACLC,oBAAoB,EAAE,CAAC;QACvBK,WAAW,EAAE,CAAC;QACdC,cAAc,EAAE,6BAA6B;QAC7CC,KAAK,EAAE,CAAC;QACRC,KAAK,EAAE,CAAC;QACRC,aAAa,EAAE,CAAC;QAChBR,MAAM,EAAE,CAAC;QACTC,iBAAiB,EAAE,CAAC;QACpBC,aAAa,EAAE,EAAE;QACjBO,aAAa,EAAE,KAAK;QACpBC,kBAAkB,EAAE,KAAK;QACzBE,kBAAkB,EAAE,KAAK;QACzBE,aAAa,EAAE,KAAK;QACpBC,mBAAmB,EAAE;OACtB;MAEDtE,SAAS,CAAC0B,IAAI,CAACY,UAAU,CAAC;QACxBC,UAAU,EAAE;OACb,CAAC;MAEFf,MAAM,CAACxB,SAAS,CAAC+E,OAAO,CAAC,CAAC1C,QAAQ,EAAE;IACtC,CAAC,CAAC;IAEFd,EAAE,CAAC,uDAAuD,EAAE,MAAK;MAC/DvB,SAAS,CAACoD,WAAW,GAAG;QACtBC,EAAE,EAAE,CAAC;QACLC,oBAAoB,EAAE,CAAC;QACvBK,WAAW,EAAE,CAAC;QACdC,cAAc,EAAEgB,SAAS;QACzBf,KAAK,EAAE,CAAC;QACRC,KAAK,EAAE,CAAC;QACRC,aAAa,EAAE,CAAC;QAChBR,MAAM,EAAE,CAAC;QACTC,iBAAiB,EAAE,CAAC;QACpBC,aAAa,EAAE,EAAE;QACjBO,aAAa,EAAE,KAAK;QACpBC,kBAAkB,EAAE,KAAK;QACzBE,kBAAkB,EAAE,KAAK;QACzBE,aAAa,EAAE,KAAK;QACpBC,mBAAmB,EAAE;OACtB;MAEDtE,SAAS,CAAC0B,IAAI,CAACY,UAAU,CAAC;QACxBC,UAAU,EAAE;OACb,CAAC;MAEFf,MAAM,CAACxB,SAAS,CAAC+E,OAAO,CAAC,CAACC,SAAS,EAAE;MAErC,MAAMC,QAAQ,GAAG,IAAIvC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE;QAAEC,IAAI,EAAE;MAAiB,CAAE,CAAC;MACxE3C,SAAS,CAAC0B,IAAI,CAACY,UAAU,CAAC;QACxB4C,iBAAiB,EAAED;OACpB,CAAC;MAEFzD,MAAM,CAACxB,SAAS,CAAC+E,OAAO,CAAC,CAAC1C,QAAQ,EAAE;IACtC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFtC,QAAQ,CAAC,iBAAiB,EAAE,MAAK;IAC/BwB,EAAE,CAAC,gDAAgD,EAAE,MAAK;MACxDvB,SAAS,CAACmF,YAAY,GAAG,IAAI;MAC7BlF,OAAO,CAACqB,aAAa,EAAE;MACvBE,MAAM,CAACxB,SAAS,CAACmF,YAAY,CAAC,CAAC9C,QAAQ,EAAE;IAC3C,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFtC,QAAQ,CAAC,eAAe,EAAE,MAAK;IAC7BwB,EAAE,CAAC,8CAA8C,EAAE,MAAK;MACtDvB,SAAS,CAACoD,WAAW,GAAG;QACtBC,EAAE,EAAE,CAAC;QACLC,oBAAoB,EAAE,CAAC;QACvBK,WAAW,EAAE,CAAC;QACdC,cAAc,EAAE,6BAA6B;QAC7CC,KAAK,EAAE,CAAC;QACRC,KAAK,EAAE,CAAC;QACRC,aAAa,EAAE,CAAC;QAChBR,MAAM,EAAE,CAAC;QACTC,iBAAiB,EAAE,CAAC;QACpBC,aAAa,EAAE,EAAE;QACjBO,aAAa,EAAE,KAAK;QACpBC,kBAAkB,EAAE,KAAK;QACzBE,kBAAkB,EAAE,KAAK;QACzBE,aAAa,EAAE,KAAK;QACpBC,mBAAmB,EAAE;OACtB;MAED,MAAMc,SAAS,GAAGC,KAAK,CAACC,MAAM,EAAE,MAAM,CAAC;MACvCtF,SAAS,CAACuF,YAAY,EAAE;MAExB/D,MAAM,CAAC4D,SAAS,CAAC,CAACjD,oBAAoB,CACpC,6BAA6B,EAC7B,QAAQ,CACT;IACH,CAAC,CAAC;IAEFZ,EAAE,CAAC,oDAAoD,EAAE,MAAK;MAC5DvB,SAAS,CAACoD,WAAW,GAAG;QACtBC,EAAE,EAAE,CAAC;QACLC,oBAAoB,EAAE,CAAC;QACvBK,WAAW,EAAE,CAAC;QACdC,cAAc,EAAEgB,SAAS;QACzBf,KAAK,EAAE,CAAC;QACRC,KAAK,EAAE,CAAC;QACRC,aAAa,EAAE,CAAC;QAChBR,MAAM,EAAE,CAAC;QACTC,iBAAiB,EAAE,CAAC;QACpBC,aAAa,EAAE,EAAE;QACjBO,aAAa,EAAE,KAAK;QACpBC,kBAAkB,EAAE,KAAK;QACzBE,kBAAkB,EAAE,KAAK;QACzBE,aAAa,EAAE,KAAK;QACpBC,mBAAmB,EAAE;OACtB;MAED,MAAMc,SAAS,GAAGC,KAAK,CAACC,MAAM,EAAE,MAAM,CAAC;MACvCtF,SAAS,CAACuF,YAAY,EAAE;MAExB/D,MAAM,CAAC4D,SAAS,CAAC,CAACpC,GAAG,CAACC,gBAAgB,EAAE;IAC1C,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}