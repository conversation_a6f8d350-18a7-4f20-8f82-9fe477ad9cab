import { ContractYear } from './contract-year.model';

export interface ContractList {
  id: number;
  contractNumber: number;
  contractorIdNumber: number;
  fullName: string;
  subscriptionDate?: Date;
  startDate?: Date;
  endDate?: Date;
  durationDays?: number;
  initialValue?: number;
  totalValue?: number;
  monthlyPayment: number;
  addition: boolean;
  hasCcp: boolean;
  contractYear?: ContractYear;
}
