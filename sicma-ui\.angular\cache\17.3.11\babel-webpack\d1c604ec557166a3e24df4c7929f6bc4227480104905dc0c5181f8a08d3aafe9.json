{"ast": null, "code": "import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { MunicipalityService } from './municipality.service';\ndescribe('MunicipalityService', () => {\n  let service;\n  let httpMock;\n  const apiUrl = `${environment.apiUrl}/municipalities`;\n  const mockMunicipality = {\n    id: 1,\n    name: 'Test Municipality',\n    departmentId: 1,\n    department: {\n      id: 1,\n      name: 'Test Department'\n    }\n  };\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [MunicipalityService]\n    });\n    service = TestBed.inject(MunicipalityService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  describe('getAll', () => {\n    it('should return all municipalities', () => {\n      const mockMunicipalities = [mockMunicipality];\n      service.getAll().subscribe(municipalities => {\n        expect(municipalities).toEqual(mockMunicipalities);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockMunicipalities);\n    });\n  });\n  describe('getById', () => {\n    it('should return a municipality by id', () => {\n      const id = 1;\n      service.getById(id).subscribe(municipality => {\n        expect(municipality).toEqual(mockMunicipality);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockMunicipality);\n    });\n  });\n  describe('create', () => {\n    it('should create a new municipality', () => {\n      const newMunicipality = {\n        name: 'New Municipality',\n        departmentId: 1,\n        department: {\n          id: 1,\n          name: 'Test Department'\n        }\n      };\n      service.create(newMunicipality).subscribe(municipality => {\n        expect(municipality).toEqual(mockMunicipality);\n      });\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newMunicipality);\n      req.flush(mockMunicipality);\n    });\n  });\n  describe('update', () => {\n    it('should update a municipality', () => {\n      const id = 1;\n      const updateData = {\n        name: 'Updated Municipality'\n      };\n      service.update(id, updateData).subscribe(municipality => {\n        expect(municipality).toEqual(mockMunicipality);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockMunicipality);\n    });\n  });\n  describe('delete', () => {\n    it('should delete a municipality', () => {\n      const id = 1;\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n  describe('getAllByDepartmentId', () => {\n    it('should return all municipalities by department id', () => {\n      const departmentId = 1;\n      const mockMunicipalities = [mockMunicipality];\n      service.getAllByDepartmentId(departmentId).subscribe(municipalities => {\n        expect(municipalities).toEqual(mockMunicipalities);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/department/${departmentId}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockMunicipalities);\n    });\n  });\n  describe('getByName', () => {\n    it('should return a municipality by name', () => {\n      const name = 'Test Municipality';\n      service.getByName(name).subscribe(municipality => {\n        expect(municipality).toEqual(mockMunicipality);\n      });\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockMunicipality);\n    });\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "HttpTestingController", "TestBed", "environment", "MunicipalityService", "describe", "service", "httpMock", "apiUrl", "mockMunicipality", "id", "name", "departmentId", "department", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockMunicipalities", "getAll", "subscribe", "municipalities", "toEqual", "req", "expectOne", "request", "method", "toBe", "flush", "getById", "municipality", "newMunicipality", "create", "body", "updateData", "update", "delete", "nothing", "getAllByDepartmentId", "getByName"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\shared\\services\\municipality.service.spec.ts"], "sourcesContent": ["import {\n  HttpClientTestingModule,\n  HttpTestingController,\n} from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { environment } from '@env';\nimport { Municipality } from '@shared/models/municipality.model';\nimport { MunicipalityService } from './municipality.service';\n\ndescribe('MunicipalityService', () => {\n  let service: MunicipalityService;\n  let httpMock: HttpTestingController;\n  const apiUrl = `${environment.apiUrl}/municipalities`;\n\n  const mockMunicipality: Municipality = {\n    id: 1,\n    name: 'Test Municipality',\n    departmentId: 1,\n    department: {\n      id: 1,\n      name: 'Test Department',\n    },\n  };\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [MunicipalityService],\n    });\n    service = TestBed.inject(MunicipalityService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  describe('getAll', () => {\n    it('should return all municipalities', () => {\n      const mockMunicipalities = [mockMunicipality];\n\n      service.getAll().subscribe((municipalities) => {\n        expect(municipalities).toEqual(mockMunicipalities);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockMunicipalities);\n    });\n  });\n\n  describe('getById', () => {\n    it('should return a municipality by id', () => {\n      const id = 1;\n\n      service.getById(id).subscribe((municipality) => {\n        expect(municipality).toEqual(mockMunicipality);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockMunicipality);\n    });\n  });\n\n  describe('create', () => {\n    it('should create a new municipality', () => {\n      const newMunicipality: Omit<Municipality, 'id'> = {\n        name: 'New Municipality',\n        departmentId: 1,\n        department: {\n          id: 1,\n          name: 'Test Department',\n        },\n      };\n\n      service.create(newMunicipality).subscribe((municipality) => {\n        expect(municipality).toEqual(mockMunicipality);\n      });\n\n      const req = httpMock.expectOne(apiUrl);\n      expect(req.request.method).toBe('POST');\n      expect(req.request.body).toEqual(newMunicipality);\n      req.flush(mockMunicipality);\n    });\n  });\n\n  describe('update', () => {\n    it('should update a municipality', () => {\n      const id = 1;\n      const updateData: Partial<Municipality> = {\n        name: 'Updated Municipality',\n      };\n\n      service.update(id, updateData).subscribe((municipality) => {\n        expect(municipality).toEqual(mockMunicipality);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('PUT');\n      expect(req.request.body).toEqual(updateData);\n      req.flush(mockMunicipality);\n    });\n  });\n\n  describe('delete', () => {\n    it('should delete a municipality', () => {\n      const id = 1;\n\n      service.delete(id).subscribe(() => {\n        expect().nothing();\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/${id}`);\n      expect(req.request.method).toBe('DELETE');\n      req.flush(null);\n    });\n  });\n\n  describe('getAllByDepartmentId', () => {\n    it('should return all municipalities by department id', () => {\n      const departmentId = 1;\n      const mockMunicipalities = [mockMunicipality];\n\n      service.getAllByDepartmentId(departmentId).subscribe((municipalities) => {\n        expect(municipalities).toEqual(mockMunicipalities);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/department/${departmentId}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockMunicipalities);\n    });\n  });\n\n  describe('getByName', () => {\n    it('should return a municipality by name', () => {\n      const name = 'Test Municipality';\n\n      service.getByName(name).subscribe((municipality) => {\n        expect(municipality).toEqual(mockMunicipality);\n      });\n\n      const req = httpMock.expectOne(`${apiUrl}/name/${name}`);\n      expect(req.request.method).toBe('GET');\n      req.flush(mockMunicipality);\n    });\n  });\n});"], "mappings": "AAAA,SACEA,uBAAuB,EACvBC,qBAAqB,QAChB,8BAA8B;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,WAAW,QAAQ,MAAM;AAElC,SAASC,mBAAmB,QAAQ,wBAAwB;AAE5DC,QAAQ,CAAC,qBAAqB,EAAE,MAAK;EACnC,IAAIC,OAA4B;EAChC,IAAIC,QAA+B;EACnC,MAAMC,MAAM,GAAG,GAAGL,WAAW,CAACK,MAAM,iBAAiB;EAErD,MAAMC,gBAAgB,GAAiB;IACrCC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,mBAAmB;IACzBC,YAAY,EAAE,CAAC;IACfC,UAAU,EAAE;MACVH,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE;;GAET;EAEDG,UAAU,CAAC,MAAK;IACdZ,OAAO,CAACa,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAAChB,uBAAuB,CAAC;MAClCiB,SAAS,EAAE,CAACb,mBAAmB;KAChC,CAAC;IACFE,OAAO,GAAGJ,OAAO,CAACgB,MAAM,CAACd,mBAAmB,CAAC;IAC7CG,QAAQ,GAAGL,OAAO,CAACgB,MAAM,CAACjB,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFkB,SAAS,CAAC,MAAK;IACbZ,QAAQ,CAACa,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAAChB,OAAO,CAAC,CAACiB,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFlB,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBgB,EAAE,CAAC,kCAAkC,EAAE,MAAK;MAC1C,MAAMG,kBAAkB,GAAG,CAACf,gBAAgB,CAAC;MAE7CH,OAAO,CAACmB,MAAM,EAAE,CAACC,SAAS,CAAEC,cAAc,IAAI;QAC5CL,MAAM,CAACK,cAAc,CAAC,CAACC,OAAO,CAACJ,kBAAkB,CAAC;MACpD,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGtB,QAAQ,CAACuB,SAAS,CAACtB,MAAM,CAAC;MACtCc,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,kBAAkB,CAAC;IAC/B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFnB,QAAQ,CAAC,SAAS,EAAE,MAAK;IACvBgB,EAAE,CAAC,oCAAoC,EAAE,MAAK;MAC5C,MAAMX,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAAC6B,OAAO,CAACzB,EAAE,CAAC,CAACgB,SAAS,CAAEU,YAAY,IAAI;QAC7Cd,MAAM,CAACc,YAAY,CAAC,CAACR,OAAO,CAACnB,gBAAgB,CAAC;MAChD,CAAC,CAAC;MAEF,MAAMoB,GAAG,GAAGtB,QAAQ,CAACuB,SAAS,CAAC,GAAGtB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACzB,gBAAgB,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBgB,EAAE,CAAC,kCAAkC,EAAE,MAAK;MAC1C,MAAMgB,eAAe,GAA6B;QAChD1B,IAAI,EAAE,kBAAkB;QACxBC,YAAY,EAAE,CAAC;QACfC,UAAU,EAAE;UACVH,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE;;OAET;MAEDL,OAAO,CAACgC,MAAM,CAACD,eAAe,CAAC,CAACX,SAAS,CAAEU,YAAY,IAAI;QACzDd,MAAM,CAACc,YAAY,CAAC,CAACR,OAAO,CAACnB,gBAAgB,CAAC;MAChD,CAAC,CAAC;MAEF,MAAMoB,GAAG,GAAGtB,QAAQ,CAACuB,SAAS,CAACtB,MAAM,CAAC;MACtCc,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;MACvCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACS,eAAe,CAAC;MACjDR,GAAG,CAACK,KAAK,CAACzB,gBAAgB,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBgB,EAAE,CAAC,8BAA8B,EAAE,MAAK;MACtC,MAAMX,EAAE,GAAG,CAAC;MACZ,MAAM8B,UAAU,GAA0B;QACxC7B,IAAI,EAAE;OACP;MAEDL,OAAO,CAACmC,MAAM,CAAC/B,EAAE,EAAE8B,UAAU,CAAC,CAACd,SAAS,CAAEU,YAAY,IAAI;QACxDd,MAAM,CAACc,YAAY,CAAC,CAACR,OAAO,CAACnB,gBAAgB,CAAC;MAChD,CAAC,CAAC;MAEF,MAAMoB,GAAG,GAAGtB,QAAQ,CAACuB,SAAS,CAAC,GAAGtB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCX,MAAM,CAACO,GAAG,CAACE,OAAO,CAACQ,IAAI,CAAC,CAACX,OAAO,CAACY,UAAU,CAAC;MAC5CX,GAAG,CAACK,KAAK,CAACzB,gBAAgB,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFJ,QAAQ,CAAC,QAAQ,EAAE,MAAK;IACtBgB,EAAE,CAAC,8BAA8B,EAAE,MAAK;MACtC,MAAMX,EAAE,GAAG,CAAC;MAEZJ,OAAO,CAACoC,MAAM,CAAChC,EAAE,CAAC,CAACgB,SAAS,CAAC,MAAK;QAChCJ,MAAM,EAAE,CAACqB,OAAO,EAAE;MACpB,CAAC,CAAC;MAEF,MAAMd,GAAG,GAAGtB,QAAQ,CAACuB,SAAS,CAAC,GAAGtB,MAAM,IAAIE,EAAE,EAAE,CAAC;MACjDY,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC;MACzCJ,GAAG,CAACK,KAAK,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF7B,QAAQ,CAAC,sBAAsB,EAAE,MAAK;IACpCgB,EAAE,CAAC,mDAAmD,EAAE,MAAK;MAC3D,MAAMT,YAAY,GAAG,CAAC;MACtB,MAAMY,kBAAkB,GAAG,CAACf,gBAAgB,CAAC;MAE7CH,OAAO,CAACsC,oBAAoB,CAAChC,YAAY,CAAC,CAACc,SAAS,CAAEC,cAAc,IAAI;QACtEL,MAAM,CAACK,cAAc,CAAC,CAACC,OAAO,CAACJ,kBAAkB,CAAC;MACpD,CAAC,CAAC;MAEF,MAAMK,GAAG,GAAGtB,QAAQ,CAACuB,SAAS,CAAC,GAAGtB,MAAM,eAAeI,YAAY,EAAE,CAAC;MACtEU,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACV,kBAAkB,CAAC;IAC/B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFnB,QAAQ,CAAC,WAAW,EAAE,MAAK;IACzBgB,EAAE,CAAC,sCAAsC,EAAE,MAAK;MAC9C,MAAMV,IAAI,GAAG,mBAAmB;MAEhCL,OAAO,CAACuC,SAAS,CAAClC,IAAI,CAAC,CAACe,SAAS,CAAEU,YAAY,IAAI;QACjDd,MAAM,CAACc,YAAY,CAAC,CAACR,OAAO,CAACnB,gBAAgB,CAAC;MAChD,CAAC,CAAC;MAEF,MAAMoB,GAAG,GAAGtB,QAAQ,CAACuB,SAAS,CAAC,GAAGtB,MAAM,SAASG,IAAI,EAAE,CAAC;MACxDW,MAAM,CAACO,GAAG,CAACE,OAAO,CAACC,MAAM,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;MACtCJ,GAAG,CAACK,KAAK,CAACzB,gBAAgB,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}