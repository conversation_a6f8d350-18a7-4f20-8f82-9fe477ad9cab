{"ast": null, "code": "function cov_1mu6fcb1j5() {\n  var path = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contract-management\\\\components\\\\contract-value-summary\\\\contract-value-summary.component.ts\";\n  var hash = \"f0107b614e7f233bac8203f800ff302e981527ae\";\n  var global = new Function(\"return this\")();\n  var gcv = \"__coverage__\";\n  var coverageData = {\n    path: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contract-management\\\\components\\\\contract-value-summary\\\\contract-value-summary.component.ts\",\n    statementMap: {\n      \"0\": {\n        start: {\n          line: 14,\n          column: 36\n        },\n        end: {\n          line: 130,\n          column: 1\n        }\n      },\n      \"1\": {\n        start: {\n          line: 16,\n          column: 8\n        },\n        end: {\n          line: 16,\n          column: 23\n        }\n      },\n      \"2\": {\n        start: {\n          line: 17,\n          column: 8\n        },\n        end: {\n          line: 17,\n          column: 29\n        }\n      },\n      \"3\": {\n        start: {\n          line: 18,\n          column: 8\n        },\n        end: {\n          line: 18,\n          column: 29\n        }\n      },\n      \"4\": {\n        start: {\n          line: 19,\n          column: 8\n        },\n        end: {\n          line: 19,\n          column: 40\n        }\n      },\n      \"5\": {\n        start: {\n          line: 20,\n          column: 8\n        },\n        end: {\n          line: 20,\n          column: 56\n        }\n      },\n      \"6\": {\n        start: {\n          line: 21,\n          column: 8\n        },\n        end: {\n          line: 21,\n          column: 63\n        }\n      },\n      \"7\": {\n        start: {\n          line: 22,\n          column: 8\n        },\n        end: {\n          line: 34,\n          column: 10\n        }\n      },\n      \"8\": {\n        start: {\n          line: 37,\n          column: 8\n        },\n        end: {\n          line: 37,\n          column: 44\n        }\n      },\n      \"9\": {\n        start: {\n          line: 40,\n          column: 8\n        },\n        end: {\n          line: 44,\n          column: 9\n        }\n      },\n      \"10\": {\n        start: {\n          line: 42,\n          column: 12\n        },\n        end: {\n          line: 42,\n          column: 48\n        }\n      },\n      \"11\": {\n        start: {\n          line: 43,\n          column: 12\n        },\n        end: {\n          line: 43,\n          column: 37\n        }\n      },\n      \"12\": {\n        start: {\n          line: 47,\n          column: 8\n        },\n        end: {\n          line: 49,\n          column: 9\n        }\n      },\n      \"13\": {\n        start: {\n          line: 48,\n          column: 12\n        },\n        end: {\n          line: 48,\n          column: 29\n        }\n      },\n      \"14\": {\n        start: {\n          line: 50,\n          column: 8\n        },\n        end: {\n          line: 55,\n          column: 22\n        }\n      },\n      \"15\": {\n        start: {\n          line: 51,\n          column: 12\n        },\n        end: {\n          line: 53,\n          column: 13\n        }\n      },\n      \"16\": {\n        start: {\n          line: 52,\n          column: 16\n        },\n        end: {\n          line: 52,\n          column: 31\n        }\n      },\n      \"17\": {\n        start: {\n          line: 54,\n          column: 12\n        },\n        end: {\n          line: 54,\n          column: 26\n        }\n      },\n      \"18\": {\n        start: {\n          line: 58,\n          column: 29\n        },\n        end: {\n          line: 58,\n          column: 59\n        }\n      },\n      \"19\": {\n        start: {\n          line: 59,\n          column: 8\n        },\n        end: {\n          line: 64,\n          column: 9\n        }\n      },\n      \"20\": {\n        start: {\n          line: 60,\n          column: 12\n        },\n        end: {\n          line: 60,\n          column: 62\n        }\n      },\n      \"21\": {\n        start: {\n          line: 63,\n          column: 12\n        },\n        end: {\n          line: 63,\n          column: 50\n        }\n      },\n      \"22\": {\n        start: {\n          line: 67,\n          column: 29\n        },\n        end: {\n          line: 67,\n          column: 59\n        }\n      },\n      \"23\": {\n        start: {\n          line: 68,\n          column: 8\n        },\n        end: {\n          line: 68,\n          column: 60\n        }\n      },\n      \"24\": {\n        start: {\n          line: 71,\n          column: 8\n        },\n        end: {\n          line: 73,\n          column: 9\n        }\n      },\n      \"25\": {\n        start: {\n          line: 72,\n          column: 12\n        },\n        end: {\n          line: 72,\n          column: 21\n        }\n      },\n      \"26\": {\n        start: {\n          line: 74,\n          column: 29\n        },\n        end: {\n          line: 74,\n          column: 59\n        }\n      },\n      \"27\": {\n        start: {\n          line: 75,\n          column: 8\n        },\n        end: {\n          line: 77,\n          column: 9\n        }\n      },\n      \"28\": {\n        start: {\n          line: 76,\n          column: 12\n        },\n        end: {\n          line: 76,\n          column: 100\n        }\n      },\n      \"29\": {\n        start: {\n          line: 76,\n          column: 71\n        },\n        end: {\n          line: 76,\n          column: 95\n        }\n      },\n      \"30\": {\n        start: {\n          line: 78,\n          column: 8\n        },\n        end: {\n          line: 80,\n          column: 65\n        }\n      },\n      \"31\": {\n        start: {\n          line: 79,\n          column: 29\n        },\n        end: {\n          line: 79,\n          column: 57\n        }\n      },\n      \"32\": {\n        start: {\n          line: 80,\n          column: 36\n        },\n        end: {\n          line: 80,\n          column: 60\n        }\n      },\n      \"33\": {\n        start: {\n          line: 83,\n          column: 8\n        },\n        end: {\n          line: 85,\n          column: 9\n        }\n      },\n      \"34\": {\n        start: {\n          line: 84,\n          column: 12\n        },\n        end: {\n          line: 84,\n          column: 21\n        }\n      },\n      \"35\": {\n        start: {\n          line: 86,\n          column: 8\n        },\n        end: {\n          line: 87,\n          column: 74\n        }\n      },\n      \"36\": {\n        start: {\n          line: 87,\n          column: 40\n        },\n        end: {\n          line: 87,\n          column: 69\n        }\n      },\n      \"37\": {\n        start: {\n          line: 90,\n          column: 29\n        },\n        end: {\n          line: 90,\n          column: 51\n        }\n      },\n      \"38\": {\n        start: {\n          line: 91,\n          column: 26\n        },\n        end: {\n          line: 91,\n          column: 50\n        }\n      },\n      \"39\": {\n        start: {\n          line: 92,\n          column: 27\n        },\n        end: {\n          line: 92,\n          column: 52\n        }\n      },\n      \"40\": {\n        start: {\n          line: 93,\n          column: 8\n        },\n        end: {\n          line: 93,\n          column: 53\n        }\n      },\n      \"41\": {\n        start: {\n          line: 96,\n          column: 8\n        },\n        end: {\n          line: 118,\n          column: 11\n        }\n      },\n      \"42\": {\n        start: {\n          line: 107,\n          column: 12\n        },\n        end: {\n          line: 108,\n          column: 23\n        }\n      },\n      \"43\": {\n        start: {\n          line: 108,\n          column: 16\n        },\n        end: {\n          line: 108,\n          column: 23\n        }\n      },\n      \"44\": {\n        start: {\n          line: 109,\n          column: 12\n        },\n        end: {\n          line: 117,\n          column: 13\n        }\n      },\n      \"45\": {\n        start: {\n          line: 110,\n          column: 30\n        },\n        end: {\n          line: 110,\n          column: 93\n        }\n      },\n      \"46\": {\n        start: {\n          line: 110,\n          column: 74\n        },\n        end: {\n          line: 110,\n          column: 92\n        }\n      },\n      \"47\": {\n        start: {\n          line: 111,\n          column: 16\n        },\n        end: {\n          line: 116,\n          column: 17\n        }\n      },\n      \"48\": {\n        start: {\n          line: 112,\n          column: 20\n        },\n        end: {\n          line: 112,\n          column: 65\n        }\n      },\n      \"49\": {\n        start: {\n          line: 113,\n          column: 20\n        },\n        end: {\n          line: 113,\n          column: 56\n        }\n      },\n      \"50\": {\n        start: {\n          line: 114,\n          column: 20\n        },\n        end: {\n          line: 114,\n          column: 82\n        }\n      },\n      \"51\": {\n        start: {\n          line: 115,\n          column: 20\n        },\n        end: {\n          line: 115,\n          column: 45\n        }\n      },\n      \"52\": {\n        start: {\n          line: 120,\n          column: 13\n        },\n        end: {\n          line: 123,\n          column: 6\n        }\n      },\n      \"53\": {\n        start: {\n          line: 120,\n          column: 41\n        },\n        end: {\n          line: 123,\n          column: 5\n        }\n      },\n      \"54\": {\n        start: {\n          line: 124,\n          column: 13\n        },\n        end: {\n          line: 129,\n          column: 6\n        }\n      },\n      \"55\": {\n        start: {\n          line: 131,\n          column: 0\n        },\n        end: {\n          line: 148,\n          column: 34\n        }\n      }\n    },\n    fnMap: {\n      \"0\": {\n        name: \"(anonymous_0)\",\n        decl: {\n          start: {\n            line: 15,\n            column: 4\n          },\n          end: {\n            line: 15,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 15,\n            column: 29\n          },\n          end: {\n            line: 35,\n            column: 5\n          }\n        },\n        line: 15\n      },\n      \"1\": {\n        name: \"(anonymous_1)\",\n        decl: {\n          start: {\n            line: 36,\n            column: 4\n          },\n          end: {\n            line: 36,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 36,\n            column: 15\n          },\n          end: {\n            line: 38,\n            column: 5\n          }\n        },\n        line: 36\n      },\n      \"2\": {\n        name: \"(anonymous_2)\",\n        decl: {\n          start: {\n            line: 39,\n            column: 4\n          },\n          end: {\n            line: 39,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 39,\n            column: 25\n          },\n          end: {\n            line: 45,\n            column: 5\n          }\n        },\n        line: 39\n      },\n      \"3\": {\n        name: \"(anonymous_3)\",\n        decl: {\n          start: {\n            line: 46,\n            column: 4\n          },\n          end: {\n            line: 46,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 46,\n            column: 30\n          },\n          end: {\n            line: 56,\n            column: 5\n          }\n        },\n        line: 46\n      },\n      \"4\": {\n        name: \"(anonymous_4)\",\n        decl: {\n          start: {\n            line: 50,\n            column: 51\n          },\n          end: {\n            line: 50,\n            column: 52\n          }\n        },\n        loc: {\n          start: {\n            line: 50,\n            column: 72\n          },\n          end: {\n            line: 55,\n            column: 9\n          }\n        },\n        line: 50\n      },\n      \"5\": {\n        name: \"(anonymous_5)\",\n        decl: {\n          start: {\n            line: 57,\n            column: 4\n          },\n          end: {\n            line: 57,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 57,\n            column: 35\n          },\n          end: {\n            line: 65,\n            column: 5\n          }\n        },\n        line: 57\n      },\n      \"6\": {\n        name: \"(anonymous_6)\",\n        decl: {\n          start: {\n            line: 66,\n            column: 4\n          },\n          end: {\n            line: 66,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 66,\n            column: 22\n          },\n          end: {\n            line: 69,\n            column: 5\n          }\n        },\n        line: 66\n      },\n      \"7\": {\n        name: \"(anonymous_7)\",\n        decl: {\n          start: {\n            line: 70,\n            column: 4\n          },\n          end: {\n            line: 70,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 70,\n            column: 24\n          },\n          end: {\n            line: 81,\n            column: 5\n          }\n        },\n        line: 70\n      },\n      \"8\": {\n        name: \"(anonymous_8)\",\n        decl: {\n          start: {\n            line: 76,\n            column: 55\n          },\n          end: {\n            line: 76,\n            column: 56\n          }\n        },\n        loc: {\n          start: {\n            line: 76,\n            column: 71\n          },\n          end: {\n            line: 76,\n            column: 95\n          }\n        },\n        line: 76\n      },\n      \"9\": {\n        name: \"(anonymous_9)\",\n        decl: {\n          start: {\n            line: 79,\n            column: 20\n          },\n          end: {\n            line: 79,\n            column: 21\n          }\n        },\n        loc: {\n          start: {\n            line: 79,\n            column: 29\n          },\n          end: {\n            line: 79,\n            column: 57\n          }\n        },\n        line: 79\n      },\n      \"10\": {\n        name: \"(anonymous_10)\",\n        decl: {\n          start: {\n            line: 80,\n            column: 20\n          },\n          end: {\n            line: 80,\n            column: 21\n          }\n        },\n        loc: {\n          start: {\n            line: 80,\n            column: 36\n          },\n          end: {\n            line: 80,\n            column: 60\n          }\n        },\n        line: 80\n      },\n      \"11\": {\n        name: \"(anonymous_11)\",\n        decl: {\n          start: {\n            line: 82,\n            column: 4\n          },\n          end: {\n            line: 82,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 82,\n            column: 25\n          },\n          end: {\n            line: 88,\n            column: 5\n          }\n        },\n        line: 82\n      },\n      \"12\": {\n        name: \"(anonymous_12)\",\n        decl: {\n          start: {\n            line: 87,\n            column: 20\n          },\n          end: {\n            line: 87,\n            column: 21\n          }\n        },\n        loc: {\n          start: {\n            line: 87,\n            column: 40\n          },\n          end: {\n            line: 87,\n            column: 69\n          }\n        },\n        line: 87\n      },\n      \"13\": {\n        name: \"(anonymous_13)\",\n        decl: {\n          start: {\n            line: 89,\n            column: 4\n          },\n          end: {\n            line: 89,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 89,\n            column: 20\n          },\n          end: {\n            line: 94,\n            column: 5\n          }\n        },\n        line: 89\n      },\n      \"14\": {\n        name: \"(anonymous_14)\",\n        decl: {\n          start: {\n            line: 95,\n            column: 4\n          },\n          end: {\n            line: 95,\n            column: 5\n          }\n        },\n        loc: {\n          start: {\n            line: 95,\n            column: 44\n          },\n          end: {\n            line: 119,\n            column: 5\n          }\n        },\n        line: 95\n      },\n      \"15\": {\n        name: \"(anonymous_15)\",\n        decl: {\n          start: {\n            line: 106,\n            column: 23\n          },\n          end: {\n            line: 106,\n            column: 24\n          }\n        },\n        loc: {\n          start: {\n            line: 106,\n            column: 35\n          },\n          end: {\n            line: 118,\n            column: 9\n          }\n        },\n        line: 106\n      },\n      \"16\": {\n        name: \"(anonymous_16)\",\n        decl: {\n          start: {\n            line: 110,\n            column: 69\n          },\n          end: {\n            line: 110,\n            column: 70\n          }\n        },\n        loc: {\n          start: {\n            line: 110,\n            column: 74\n          },\n          end: {\n            line: 110,\n            column: 92\n          }\n        },\n        line: 110\n      },\n      \"17\": {\n        name: \"(anonymous_17)\",\n        decl: {\n          start: {\n            line: 120,\n            column: 35\n          },\n          end: {\n            line: 120,\n            column: 36\n          }\n        },\n        loc: {\n          start: {\n            line: 120,\n            column: 41\n          },\n          end: {\n            line: 123,\n            column: 5\n          }\n        },\n        line: 120\n      }\n    },\n    branchMap: {\n      \"0\": {\n        loc: {\n          start: {\n            line: 40,\n            column: 8\n          },\n          end: {\n            line: 44,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 40,\n            column: 8\n          },\n          end: {\n            line: 44,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 40\n      },\n      \"1\": {\n        loc: {\n          start: {\n            line: 40,\n            column: 12\n          },\n          end: {\n            line: 41,\n            column: 73\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 40,\n            column: 13\n          },\n          end: {\n            line: 40,\n            column: 32\n          }\n        }, {\n          start: {\n            line: 40,\n            column: 36\n          },\n          end: {\n            line: 40,\n            column: 68\n          }\n        }, {\n          start: {\n            line: 41,\n            column: 13\n          },\n          end: {\n            line: 41,\n            column: 34\n          }\n        }, {\n          start: {\n            line: 41,\n            column: 38\n          },\n          end: {\n            line: 41,\n            column: 72\n          }\n        }],\n        line: 40\n      },\n      \"2\": {\n        loc: {\n          start: {\n            line: 47,\n            column: 8\n          },\n          end: {\n            line: 49,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 47,\n            column: 8\n          },\n          end: {\n            line: 49,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 47\n      },\n      \"3\": {\n        loc: {\n          start: {\n            line: 51,\n            column: 12\n          },\n          end: {\n            line: 53,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 51,\n            column: 12\n          },\n          end: {\n            line: 53,\n            column: 13\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 51\n      },\n      \"4\": {\n        loc: {\n          start: {\n            line: 51,\n            column: 16\n          },\n          end: {\n            line: 51,\n            column: 106\n          }\n        },\n        type: \"binary-expr\",\n        locations: [{\n          start: {\n            line: 51,\n            column: 16\n          },\n          end: {\n            line: 51,\n            column: 23\n          }\n        }, {\n          start: {\n            line: 51,\n            column: 28\n          },\n          end: {\n            line: 51,\n            column: 52\n          }\n        }, {\n          start: {\n            line: 51,\n            column: 56\n          },\n          end: {\n            line: 51,\n            column: 79\n          }\n        }, {\n          start: {\n            line: 51,\n            column: 83\n          },\n          end: {\n            line: 51,\n            column: 105\n          }\n        }],\n        line: 51\n      },\n      \"5\": {\n        loc: {\n          start: {\n            line: 59,\n            column: 8\n          },\n          end: {\n            line: 64,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 59,\n            column: 8\n          },\n          end: {\n            line: 64,\n            column: 9\n          }\n        }, {\n          start: {\n            line: 62,\n            column: 13\n          },\n          end: {\n            line: 64,\n            column: 9\n          }\n        }],\n        line: 59\n      },\n      \"6\": {\n        loc: {\n          start: {\n            line: 68,\n            column: 15\n          },\n          end: {\n            line: 68,\n            column: 59\n          }\n        },\n        type: \"cond-expr\",\n        locations: [{\n          start: {\n            line: 68,\n            column: 30\n          },\n          end: {\n            line: 68,\n            column: 55\n          }\n        }, {\n          start: {\n            line: 68,\n            column: 58\n          },\n          end: {\n            line: 68,\n            column: 59\n          }\n        }],\n        line: 68\n      },\n      \"7\": {\n        loc: {\n          start: {\n            line: 71,\n            column: 8\n          },\n          end: {\n            line: 73,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 71,\n            column: 8\n          },\n          end: {\n            line: 73,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 71\n      },\n      \"8\": {\n        loc: {\n          start: {\n            line: 75,\n            column: 8\n          },\n          end: {\n            line: 77,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 75,\n            column: 8\n          },\n          end: {\n            line: 77,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 75\n      },\n      \"9\": {\n        loc: {\n          start: {\n            line: 83,\n            column: 8\n          },\n          end: {\n            line: 85,\n            column: 9\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 83,\n            column: 8\n          },\n          end: {\n            line: 85,\n            column: 9\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 83\n      },\n      \"10\": {\n        loc: {\n          start: {\n            line: 107,\n            column: 12\n          },\n          end: {\n            line: 108,\n            column: 23\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 107,\n            column: 12\n          },\n          end: {\n            line: 108,\n            column: 23\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 107\n      },\n      \"11\": {\n        loc: {\n          start: {\n            line: 109,\n            column: 12\n          },\n          end: {\n            line: 117,\n            column: 13\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 109,\n            column: 12\n          },\n          end: {\n            line: 117,\n            column: 13\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 109\n      },\n      \"12\": {\n        loc: {\n          start: {\n            line: 111,\n            column: 16\n          },\n          end: {\n            line: 116,\n            column: 17\n          }\n        },\n        type: \"if\",\n        locations: [{\n          start: {\n            line: 111,\n            column: 16\n          },\n          end: {\n            line: 116,\n            column: 17\n          }\n        }, {\n          start: {\n            line: undefined,\n            column: undefined\n          },\n          end: {\n            line: undefined,\n            column: undefined\n          }\n        }],\n        line: 111\n      }\n    },\n    s: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0,\n      \"18\": 0,\n      \"19\": 0,\n      \"20\": 0,\n      \"21\": 0,\n      \"22\": 0,\n      \"23\": 0,\n      \"24\": 0,\n      \"25\": 0,\n      \"26\": 0,\n      \"27\": 0,\n      \"28\": 0,\n      \"29\": 0,\n      \"30\": 0,\n      \"31\": 0,\n      \"32\": 0,\n      \"33\": 0,\n      \"34\": 0,\n      \"35\": 0,\n      \"36\": 0,\n      \"37\": 0,\n      \"38\": 0,\n      \"39\": 0,\n      \"40\": 0,\n      \"41\": 0,\n      \"42\": 0,\n      \"43\": 0,\n      \"44\": 0,\n      \"45\": 0,\n      \"46\": 0,\n      \"47\": 0,\n      \"48\": 0,\n      \"49\": 0,\n      \"50\": 0,\n      \"51\": 0,\n      \"52\": 0,\n      \"53\": 0,\n      \"54\": 0,\n      \"55\": 0\n    },\n    f: {\n      \"0\": 0,\n      \"1\": 0,\n      \"2\": 0,\n      \"3\": 0,\n      \"4\": 0,\n      \"5\": 0,\n      \"6\": 0,\n      \"7\": 0,\n      \"8\": 0,\n      \"9\": 0,\n      \"10\": 0,\n      \"11\": 0,\n      \"12\": 0,\n      \"13\": 0,\n      \"14\": 0,\n      \"15\": 0,\n      \"16\": 0,\n      \"17\": 0\n    },\n    b: {\n      \"0\": [0, 0],\n      \"1\": [0, 0, 0, 0],\n      \"2\": [0, 0],\n      \"3\": [0, 0],\n      \"4\": [0, 0, 0, 0],\n      \"5\": [0, 0],\n      \"6\": [0, 0],\n      \"7\": [0, 0],\n      \"8\": [0, 0],\n      \"9\": [0, 0],\n      \"10\": [0, 0],\n      \"11\": [0, 0],\n      \"12\": [0, 0]\n    },\n    inputSourceMap: {\n      version: 3,\n      file: \"contract-value-summary.component.js\",\n      sourceRoot: \"\",\n      sources: [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Github\\\\SICMA\\\\sicma-ui\\\\src\\\\app\\\\features\\\\contract-management\\\\components\\\\contract-value-summary\\\\contract-value-summary.component.ts\"],\n      names: [],\n      mappings: \";;;AAAA,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,iBAAiB,CAAC;AACzD,OAAO,EAAE,iBAAiB,EAAE,SAAS,EAAE,YAAY,EAAE,KAAK,EAAqB,MAAM,EAAiB,MAAM,eAAe,CAAC;AAC5H,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AACrD,OAAO,EAAE,kBAAkB,EAAE,MAAM,8BAA8B,CAAC;AAClE,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAC3D,OAAO,EAAE,kBAAkB,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AAC7E,OAAO,EAAE,gBAAgB,EAAE,MAAM,2BAA2B,CAAC;AAI7D,OAAO,EAAE,6BAA6B,EAAE,MAAM,yFAAyF,CAAC;AAkBjI,IAAM,6BAA6B,GAAnC,MAAM,6BAA6B;IAqBxC,YACmB,GAAsB,EACtB,MAAiB;QADjB,QAAG,GAAH,GAAG,CAAmB;QACtB,WAAM,GAAN,MAAM,CAAW;QArB3B,eAAU,GAAgB,EAAE;QAC5B,uBAAkB,GAAG,KAAK;QACzB,0BAAqB,GAAG,IAAI,YAAY,EAAoB;QAEtE,2BAAsB,GAAG,IAAI,kBAAkB,EAAkB,CAAC;QAClE,qBAAgB,GAAa;YAC3B,YAAY;YACZ,WAAW;YACX,YAAY;YACZ,qBAAqB;YACrB,YAAY;YACZ,KAAK;YACL,IAAI;YACJ,kBAAkB;YAClB,aAAa;YACb,mBAAmB;YACnB,SAAS;SACV,CAAC;IAKC,CAAC;IAEJ,QAAQ;QACN,IAAI,CAAC,4BAA4B,EAAE,CAAC;IACtC,CAAC;IAED,WAAW,CAAC,OAAsB;QAChC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,WAAW,CAAC;YACzD,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,EAAE,CAAC;YAClE,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACpC,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,uBAAuB;QACrB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,cAAc,EAAE,MAAM,EAAE,CAAC;YAC3C,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,MAAkC,EAAE,OAAuB,EAAE,EAAE;YACzG,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,KAAK,SAAS,IAAI,MAAM,CAAC,EAAE,KAAK,SAAS,IAAI,OAAO,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC;gBAC/F,OAAO,OAAO,CAAC;YACjB,CAAC;YACD,OAAO,MAAM,CAAC;QAChB,CAAC,EAAE,SAAS,CAAC,CAAC;IAChB,CAAC;IAEO,4BAA4B;QAClC,MAAM,YAAY,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QACpD,IAAI,YAAY,EAAE,CAAC;YACjB,IAAI,CAAC,sBAAsB,CAAC,IAAI,GAAG,CAAC,YAAY,CAAC,CAAC;QACpD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,sBAAsB,CAAC,IAAI,GAAG,EAAE,CAAC;QACxC,CAAC;IACH,CAAC;IAED,eAAe;QACb,MAAM,YAAY,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QACpD,OAAO,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;IACtD,CAAC;IAED,iBAAiB;QACf,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,cAAc,EAAE,CAAC;YACnC,OAAO,CAAC,CAAC;QACX,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QACpD,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAC1F,CAAC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc;aAChC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,KAAK,YAAY,CAAC,EAAE,CAAC;aAC7C,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,kBAAkB;QAChB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC;YAC7B,OAAO,CAAC,CAAC;QACX,CAAC;QAED,OAAO,IAAI,CAAC,UAAU;aACnB,MAAM,CAAC,CAAC,GAAG,EAAE,SAAS,EAAE,EAAE,CAAC,GAAG,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;IAClE,CAAC;IAED,aAAa;QACX,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC5C,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3C,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE7C,OAAO,YAAY,GAAG,SAAS,GAAG,UAAU,CAAC;IAC/C,CAAC;IAED,wBAAwB,CAAC,aAA6B;QACpD,IAAI,CAAC,MAAM;aACR,IAAI,CAAC,6BAA6B,EAAE;YACnC,KAAK,EAAE,QAAQ;YACf,IAAI,EAAE;gBACJ,aAAa;gBACb,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,cAAc,EAAE,IAAI;aACrB;SACF,CAAC;aACD,WAAW,EAAE;aACb,SAAS,CAAC,CAAC,MAAuB,EAAE,EAAE;YACrC,IAAI,CAAC,MAAM;gBAAE,OAAO;YAEpB,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;gBACjC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,CAAC,CAAC;gBAC9E,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;oBACjB,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;oBAE7C,IAAI,CAAC,4BAA4B,EAAE,CAAC;oBACpC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;oBAC9D,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;gBAC3B,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;IACP,CAAC;;;;;;2BAzHA,KAAK;6BACL,KAAK;qCACL,KAAK;wCACL,MAAM;;;AAJI,6BAA6B;IAhBzC,SAAS,CAAC;QACT,QAAQ,EAAE,4BAA4B;QACtC,UAAU,EAAE,IAAI;QAChB,OAAO,EAAE;YACP,aAAa;YACb,kBAAkB;YAClB,cAAc;YACd,aAAa;YACb,eAAe;YACf,gBAAgB;YAChB,YAAY;YACZ,QAAQ;SACT;QACD,8BAAsD;;KAEvD,CAAC;GACW,6BAA6B,CA2HzC\",\n      sourcesContent: [\"import { CurrencyPipe, DatePipe } from '@angular/common';\\nimport { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';\\nimport { MatCardModule } from '@angular/material/card';\\nimport { MatDialog } from '@angular/material/dialog';\\nimport { MatFormFieldModule } from '@angular/material/form-field';\\nimport { MatIconModule } from '@angular/material/icon';\\nimport { MatButtonModule } from '@angular/material/button';\\nimport { MatTableDataSource, MatTableModule } from '@angular/material/table';\\nimport { MatTooltipModule } from '@angular/material/tooltip';\\nimport { ContractValues } from '@contract-management/models/contract-values.model';\\nimport { Contract } from '@contract-management/models/contract.model';\\nimport { Reduction } from '@contract-management/models/reduction.model';\\nimport { ContractValuesDialogComponent } from '@contract-management/components/contract-values-dialog/contract-values-dialog.component';\\n\\n@Component({\\n  selector: 'app-contract-value-summary',\\n  standalone: true,\\n  imports: [\\n    MatCardModule,\\n    MatFormFieldModule,\\n    MatTableModule,\\n    MatIconModule,\\n    MatButtonModule,\\n    MatTooltipModule,\\n    CurrencyPipe,\\n    DatePipe\\n  ],\\n  templateUrl: './contract-value-summary.component.html',\\n  styleUrls: ['./contract-value-summary.component.scss']\\n})\\nexport class ContractValueSummaryComponent implements OnInit, OnChanges {\\n  @Input() contract!: Contract;\\n  @Input() reductions: Reduction[] = [];\\n  @Input() isContractFinished = false;\\n  @Output() contractValuesChanged = new EventEmitter<ContractValues[]>();\\n\\n  initialValueDataSource = new MatTableDataSource<ContractValues>();\\n  displayedColumns: string[] = [\\n    'valorTotal',\\n    'valorMADS',\\n    'valorOtros',\\n    'valorVigenciaFutura',\\n    'entidadCDP',\\n    'cdp',\\n    'rp',\\n    'fechaSuscripcion',\\n    'fechaInicio',\\n    'fechaFinalizacion',\\n    'actions'\\n  ];\\n\\n  constructor(\\n    private readonly cdr: ChangeDetectorRef,\\n    private readonly dialog: MatDialog\\n  ) {}\\n\\n  ngOnInit(): void {\\n    this.updateInitialValueDataSource();\\n  }\\n\\n  ngOnChanges(changes: SimpleChanges): void {\\n    if ((changes['contract'] && !changes['contract'].firstChange) ||\\n        (changes['reductions'] && !changes['reductions'].firstChange)) {\\n      this.updateInitialValueDataSource();\\n      this.cdr.detectChanges();\\n    }\\n  }\\n\\n  getInitialContractValue(): ContractValues | undefined {\\n    if (!this.contract?.contractValues?.length) {\\n      return undefined;\\n    }\\n\\n    return this.contract.contractValues.reduce((lowest: ContractValues | undefined, current: ContractValues) => {\\n      if (!lowest || (current.id !== undefined && lowest.id !== undefined && current.id < lowest.id)) {\\n        return current;\\n      }\\n      return lowest;\\n    }, undefined);\\n  }\\n\\n  private updateInitialValueDataSource(): void {\\n    const initialValue = this.getInitialContractValue();\\n    if (initialValue) {\\n      this.initialValueDataSource.data = [initialValue];\\n    } else {\\n      this.initialValueDataSource.data = [];\\n    }\\n  }\\n\\n  getInitialValue(): number {\\n    const initialValue = this.getInitialContractValue();\\n    return initialValue ? initialValue.numericValue : 0;\\n  }\\n\\n  getTotalAdditions(): number {\\n    if (!this.contract?.contractValues) {\\n      return 0;\\n    }\\n\\n    const initialValue = this.getInitialContractValue();\\n    if (!initialValue) {\\n      return this.contract.contractValues.reduce((sum, value) => sum + value.numericValue, 0);\\n    }\\n\\n    return this.contract.contractValues\\n      .filter(value => value.id !== initialValue.id)\\n      .reduce((sum, value) => sum + value.numericValue, 0);\\n  }\\n\\n  getTotalReductions(): number {\\n    if (!this.reductions?.length) {\\n      return 0;\\n    }\\n\\n    return this.reductions\\n      .reduce((sum, reduction) => sum + reduction.valueRedution, 0);\\n  }\\n\\n  getFinalValue(): number {\\n    const initialValue = this.getInitialValue();\\n    const additions = this.getTotalAdditions();\\n    const reductions = this.getTotalReductions();\\n\\n    return initialValue + additions - reductions;\\n  }\\n\\n  openContractValuesDialog(contractValue: ContractValues): void {\\n    this.dialog\\n      .open(ContractValuesDialogComponent, {\\n        width: '1000px',\\n        data: {\\n          contractValue,\\n          contract: this.contract,\\n          isInitialValue: true,\\n        },\\n      })\\n      .afterClosed()\\n      .subscribe((result?: ContractValues) => {\\n        if (!result) return;\\n\\n        if (this.contract.contractValues) {\\n          const index = this.contract.contractValues.findIndex(v => v.id === result.id);\\n          if (index !== -1) {\\n            this.contract.contractValues[index] = result;\\n\\n            this.updateInitialValueDataSource();\\n            this.contractValuesChanged.emit(this.contract.contractValues);\\n            this.cdr.detectChanges();\\n          }\\n        }\\n      });\\n  }\\n}\\n\"]\n    },\n    _coverageSchema: \"1a1c01bbd47fc00a2c39e90264f33305004495a9\",\n    hash: \"f0107b614e7f233bac8203f800ff302e981527ae\"\n  };\n  var coverage = global[gcv] || (global[gcv] = {});\n  if (!coverage[path] || coverage[path].hash !== hash) {\n    coverage[path] = coverageData;\n  }\n  var actualCoverage = coverage[path];\n  {\n    // @ts-ignore\n    cov_1mu6fcb1j5 = function () {\n      return actualCoverage;\n    };\n  }\n  return actualCoverage;\n}\ncov_1mu6fcb1j5();\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./contract-value-summary.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./contract-value-summary.component.scss?ngResource\";\nimport { CurrencyPipe, DatePipe } from '@angular/common';\nimport { ChangeDetectorRef, Component, EventEmitter, Input, Output } from '@angular/core';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatDialog } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatTableDataSource, MatTableModule } from '@angular/material/table';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { ContractValuesDialogComponent } from '@contract-management/components/contract-values-dialog/contract-values-dialog.component';\ncov_1mu6fcb1j5().s[0]++;\nlet ContractValueSummaryComponent = class ContractValueSummaryComponent {\n  constructor(cdr, dialog) {\n    cov_1mu6fcb1j5().f[0]++;\n    cov_1mu6fcb1j5().s[1]++;\n    this.cdr = cdr;\n    cov_1mu6fcb1j5().s[2]++;\n    this.dialog = dialog;\n    cov_1mu6fcb1j5().s[3]++;\n    this.reductions = [];\n    cov_1mu6fcb1j5().s[4]++;\n    this.isContractFinished = false;\n    cov_1mu6fcb1j5().s[5]++;\n    this.contractValuesChanged = new EventEmitter();\n    cov_1mu6fcb1j5().s[6]++;\n    this.initialValueDataSource = new MatTableDataSource();\n    cov_1mu6fcb1j5().s[7]++;\n    this.displayedColumns = ['valorTotal', 'valorMADS', 'valorOtros', 'valorVigenciaFutura', 'entidadCDP', 'cdp', 'rp', 'fechaSuscripcion', 'fechaInicio', 'fechaFinalizacion', 'actions'];\n  }\n  ngOnInit() {\n    cov_1mu6fcb1j5().f[1]++;\n    cov_1mu6fcb1j5().s[8]++;\n    this.updateInitialValueDataSource();\n  }\n  ngOnChanges(changes) {\n    cov_1mu6fcb1j5().f[2]++;\n    cov_1mu6fcb1j5().s[9]++;\n    if ((cov_1mu6fcb1j5().b[1][0]++, changes['contract']) && (cov_1mu6fcb1j5().b[1][1]++, !changes['contract'].firstChange) || (cov_1mu6fcb1j5().b[1][2]++, changes['reductions']) && (cov_1mu6fcb1j5().b[1][3]++, !changes['reductions'].firstChange)) {\n      cov_1mu6fcb1j5().b[0][0]++;\n      cov_1mu6fcb1j5().s[10]++;\n      this.updateInitialValueDataSource();\n      cov_1mu6fcb1j5().s[11]++;\n      this.cdr.detectChanges();\n    } else {\n      cov_1mu6fcb1j5().b[0][1]++;\n    }\n  }\n  getInitialContractValue() {\n    cov_1mu6fcb1j5().f[3]++;\n    cov_1mu6fcb1j5().s[12]++;\n    if (!this.contract?.contractValues?.length) {\n      cov_1mu6fcb1j5().b[2][0]++;\n      cov_1mu6fcb1j5().s[13]++;\n      return undefined;\n    } else {\n      cov_1mu6fcb1j5().b[2][1]++;\n    }\n    cov_1mu6fcb1j5().s[14]++;\n    return this.contract.contractValues.reduce((lowest, current) => {\n      cov_1mu6fcb1j5().f[4]++;\n      cov_1mu6fcb1j5().s[15]++;\n      if ((cov_1mu6fcb1j5().b[4][0]++, !lowest) || (cov_1mu6fcb1j5().b[4][1]++, current.id !== undefined) && (cov_1mu6fcb1j5().b[4][2]++, lowest.id !== undefined) && (cov_1mu6fcb1j5().b[4][3]++, current.id < lowest.id)) {\n        cov_1mu6fcb1j5().b[3][0]++;\n        cov_1mu6fcb1j5().s[16]++;\n        return current;\n      } else {\n        cov_1mu6fcb1j5().b[3][1]++;\n      }\n      cov_1mu6fcb1j5().s[17]++;\n      return lowest;\n    }, undefined);\n  }\n  updateInitialValueDataSource() {\n    cov_1mu6fcb1j5().f[5]++;\n    const initialValue = (cov_1mu6fcb1j5().s[18]++, this.getInitialContractValue());\n    cov_1mu6fcb1j5().s[19]++;\n    if (initialValue) {\n      cov_1mu6fcb1j5().b[5][0]++;\n      cov_1mu6fcb1j5().s[20]++;\n      this.initialValueDataSource.data = [initialValue];\n    } else {\n      cov_1mu6fcb1j5().b[5][1]++;\n      cov_1mu6fcb1j5().s[21]++;\n      this.initialValueDataSource.data = [];\n    }\n  }\n  getInitialValue() {\n    cov_1mu6fcb1j5().f[6]++;\n    const initialValue = (cov_1mu6fcb1j5().s[22]++, this.getInitialContractValue());\n    cov_1mu6fcb1j5().s[23]++;\n    return initialValue ? (cov_1mu6fcb1j5().b[6][0]++, initialValue.numericValue) : (cov_1mu6fcb1j5().b[6][1]++, 0);\n  }\n  getTotalAdditions() {\n    cov_1mu6fcb1j5().f[7]++;\n    cov_1mu6fcb1j5().s[24]++;\n    if (!this.contract?.contractValues) {\n      cov_1mu6fcb1j5().b[7][0]++;\n      cov_1mu6fcb1j5().s[25]++;\n      return 0;\n    } else {\n      cov_1mu6fcb1j5().b[7][1]++;\n    }\n    const initialValue = (cov_1mu6fcb1j5().s[26]++, this.getInitialContractValue());\n    cov_1mu6fcb1j5().s[27]++;\n    if (!initialValue) {\n      cov_1mu6fcb1j5().b[8][0]++;\n      cov_1mu6fcb1j5().s[28]++;\n      return this.contract.contractValues.reduce((sum, value) => {\n        cov_1mu6fcb1j5().f[8]++;\n        cov_1mu6fcb1j5().s[29]++;\n        return sum + value.numericValue;\n      }, 0);\n    } else {\n      cov_1mu6fcb1j5().b[8][1]++;\n    }\n    cov_1mu6fcb1j5().s[30]++;\n    return this.contract.contractValues.filter(value => {\n      cov_1mu6fcb1j5().f[9]++;\n      cov_1mu6fcb1j5().s[31]++;\n      return value.id !== initialValue.id;\n    }).reduce((sum, value) => {\n      cov_1mu6fcb1j5().f[10]++;\n      cov_1mu6fcb1j5().s[32]++;\n      return sum + value.numericValue;\n    }, 0);\n  }\n  getTotalReductions() {\n    cov_1mu6fcb1j5().f[11]++;\n    cov_1mu6fcb1j5().s[33]++;\n    if (!this.reductions?.length) {\n      cov_1mu6fcb1j5().b[9][0]++;\n      cov_1mu6fcb1j5().s[34]++;\n      return 0;\n    } else {\n      cov_1mu6fcb1j5().b[9][1]++;\n    }\n    cov_1mu6fcb1j5().s[35]++;\n    return this.reductions.reduce((sum, reduction) => {\n      cov_1mu6fcb1j5().f[12]++;\n      cov_1mu6fcb1j5().s[36]++;\n      return sum + reduction.valueRedution;\n    }, 0);\n  }\n  getFinalValue() {\n    cov_1mu6fcb1j5().f[13]++;\n    const initialValue = (cov_1mu6fcb1j5().s[37]++, this.getInitialValue());\n    const additions = (cov_1mu6fcb1j5().s[38]++, this.getTotalAdditions());\n    const reductions = (cov_1mu6fcb1j5().s[39]++, this.getTotalReductions());\n    cov_1mu6fcb1j5().s[40]++;\n    return initialValue + additions - reductions;\n  }\n  openContractValuesDialog(contractValue) {\n    cov_1mu6fcb1j5().f[14]++;\n    cov_1mu6fcb1j5().s[41]++;\n    this.dialog.open(ContractValuesDialogComponent, {\n      width: '1000px',\n      data: {\n        contractValue,\n        contract: this.contract,\n        isInitialValue: true\n      }\n    }).afterClosed().subscribe(result => {\n      cov_1mu6fcb1j5().f[15]++;\n      cov_1mu6fcb1j5().s[42]++;\n      if (!result) {\n        cov_1mu6fcb1j5().b[10][0]++;\n        cov_1mu6fcb1j5().s[43]++;\n        return;\n      } else {\n        cov_1mu6fcb1j5().b[10][1]++;\n      }\n      cov_1mu6fcb1j5().s[44]++;\n      if (this.contract.contractValues) {\n        cov_1mu6fcb1j5().b[11][0]++;\n        const index = (cov_1mu6fcb1j5().s[45]++, this.contract.contractValues.findIndex(v => {\n          cov_1mu6fcb1j5().f[16]++;\n          cov_1mu6fcb1j5().s[46]++;\n          return v.id === result.id;\n        }));\n        cov_1mu6fcb1j5().s[47]++;\n        if (index !== -1) {\n          cov_1mu6fcb1j5().b[12][0]++;\n          cov_1mu6fcb1j5().s[48]++;\n          this.contract.contractValues[index] = result;\n          cov_1mu6fcb1j5().s[49]++;\n          this.updateInitialValueDataSource();\n          cov_1mu6fcb1j5().s[50]++;\n          this.contractValuesChanged.emit(this.contract.contractValues);\n          cov_1mu6fcb1j5().s[51]++;\n          this.cdr.detectChanges();\n        } else {\n          cov_1mu6fcb1j5().b[12][1]++;\n        }\n      } else {\n        cov_1mu6fcb1j5().b[11][1]++;\n      }\n    });\n  }\n  static {\n    cov_1mu6fcb1j5().s[52]++;\n    this.ctorParameters = () => {\n      cov_1mu6fcb1j5().f[17]++;\n      cov_1mu6fcb1j5().s[53]++;\n      return [{\n        type: ChangeDetectorRef\n      }, {\n        type: MatDialog\n      }];\n    };\n  }\n  static {\n    cov_1mu6fcb1j5().s[54]++;\n    this.propDecorators = {\n      contract: [{\n        type: Input\n      }],\n      reductions: [{\n        type: Input\n      }],\n      isContractFinished: [{\n        type: Input\n      }],\n      contractValuesChanged: [{\n        type: Output\n      }]\n    };\n  }\n};\ncov_1mu6fcb1j5().s[55]++;\nContractValueSummaryComponent = __decorate([Component({\n  selector: 'app-contract-value-summary',\n  standalone: true,\n  imports: [MatCardModule, MatFormFieldModule, MatTableModule, MatIconModule, MatButtonModule, MatTooltipModule, CurrencyPipe, DatePipe],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], ContractValueSummaryComponent);\nexport { ContractValueSummaryComponent };", "map": {"version": 3, "names": ["cov_1mu6fcb1j5", "actualCoverage", "C<PERSON><PERSON>cyPipe", "DatePipe", "ChangeDetectorRef", "Component", "EventEmitter", "Input", "Output", "MatCardModule", "MatDialog", "MatFormFieldModule", "MatIconModule", "MatButtonModule", "MatTableDataSource", "MatTableModule", "MatTooltipModule", "ContractValuesDialogComponent", "s", "ContractValueSummaryComponent", "constructor", "cdr", "dialog", "f", "reductions", "isContractFinished", "contractValuesChanged", "initialValueDataSource", "displayedColumns", "ngOnInit", "updateInitialValueDataSource", "ngOnChanges", "changes", "b", "firstChange", "detectChanges", "getInitialContractValue", "contract", "contractValues", "length", "undefined", "reduce", "lowest", "current", "id", "initialValue", "data", "getInitialValue", "numericValue", "getTotalAdditions", "sum", "value", "filter", "getTotalReductions", "reduction", "valueRedution", "getFinalValue", "additions", "openContractValuesDialog", "contractValue", "open", "width", "isInitialValue", "afterClosed", "subscribe", "result", "index", "findIndex", "v", "emit", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\components\\contract-value-summary\\contract-value-summary.component.ts"], "sourcesContent": ["import { CurrencyPipe, DatePipe } from '@angular/common';\nimport { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatDialog } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatTableDataSource, MatTableModule } from '@angular/material/table';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { ContractValues } from '@contract-management/models/contract-values.model';\nimport { Contract } from '@contract-management/models/contract.model';\nimport { Reduction } from '@contract-management/models/reduction.model';\nimport { ContractValuesDialogComponent } from '@contract-management/components/contract-values-dialog/contract-values-dialog.component';\n\n@Component({\n  selector: 'app-contract-value-summary',\n  standalone: true,\n  imports: [\n    MatCardModule,\n    MatFormFieldModule,\n    MatTableModule,\n    MatIconModule,\n    MatButtonModule,\n    MatTooltipModule,\n    CurrencyPipe,\n    DatePipe\n  ],\n  templateUrl: './contract-value-summary.component.html',\n  styleUrls: ['./contract-value-summary.component.scss']\n})\nexport class ContractValueSummaryComponent implements OnInit, OnChanges {\n  @Input() contract!: Contract;\n  @Input() reductions: Reduction[] = [];\n  @Input() isContractFinished = false;\n  @Output() contractValuesChanged = new EventEmitter<ContractValues[]>();\n\n  initialValueDataSource = new MatTableDataSource<ContractValues>();\n  displayedColumns: string[] = [\n    'valorTotal',\n    'valorMADS',\n    'valorOtros',\n    'valorVigenciaFutura',\n    'entidadCDP',\n    'cdp',\n    'rp',\n    'fechaSuscripcion',\n    'fechaInicio',\n    'fechaFinalizacion',\n    'actions'\n  ];\n\n  constructor(\n    private readonly cdr: ChangeDetectorRef,\n    private readonly dialog: MatDialog\n  ) {}\n\n  ngOnInit(): void {\n    this.updateInitialValueDataSource();\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    if ((changes['contract'] && !changes['contract'].firstChange) ||\n        (changes['reductions'] && !changes['reductions'].firstChange)) {\n      this.updateInitialValueDataSource();\n      this.cdr.detectChanges();\n    }\n  }\n\n  getInitialContractValue(): ContractValues | undefined {\n    if (!this.contract?.contractValues?.length) {\n      return undefined;\n    }\n\n    return this.contract.contractValues.reduce((lowest: ContractValues | undefined, current: ContractValues) => {\n      if (!lowest || (current.id !== undefined && lowest.id !== undefined && current.id < lowest.id)) {\n        return current;\n      }\n      return lowest;\n    }, undefined);\n  }\n\n  private updateInitialValueDataSource(): void {\n    const initialValue = this.getInitialContractValue();\n    if (initialValue) {\n      this.initialValueDataSource.data = [initialValue];\n    } else {\n      this.initialValueDataSource.data = [];\n    }\n  }\n\n  getInitialValue(): number {\n    const initialValue = this.getInitialContractValue();\n    return initialValue ? initialValue.numericValue : 0;\n  }\n\n  getTotalAdditions(): number {\n    if (!this.contract?.contractValues) {\n      return 0;\n    }\n\n    const initialValue = this.getInitialContractValue();\n    if (!initialValue) {\n      return this.contract.contractValues.reduce((sum, value) => sum + value.numericValue, 0);\n    }\n\n    return this.contract.contractValues\n      .filter(value => value.id !== initialValue.id)\n      .reduce((sum, value) => sum + value.numericValue, 0);\n  }\n\n  getTotalReductions(): number {\n    if (!this.reductions?.length) {\n      return 0;\n    }\n\n    return this.reductions\n      .reduce((sum, reduction) => sum + reduction.valueRedution, 0);\n  }\n\n  getFinalValue(): number {\n    const initialValue = this.getInitialValue();\n    const additions = this.getTotalAdditions();\n    const reductions = this.getTotalReductions();\n\n    return initialValue + additions - reductions;\n  }\n\n  openContractValuesDialog(contractValue: ContractValues): void {\n    this.dialog\n      .open(ContractValuesDialogComponent, {\n        width: '1000px',\n        data: {\n          contractValue,\n          contract: this.contract,\n          isInitialValue: true,\n        },\n      })\n      .afterClosed()\n      .subscribe((result?: ContractValues) => {\n        if (!result) return;\n\n        if (this.contract.contractValues) {\n          const index = this.contract.contractValues.findIndex(v => v.id === result.id);\n          if (index !== -1) {\n            this.contract.contractValues[index] = result;\n\n            this.updateInitialValueDataSource();\n            this.contractValuesChanged.emit(this.contract.contractValues);\n            this.cdr.detectChanges();\n          }\n        }\n      });\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoDqB;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;;;;AApDrB,SAASE,YAAY,EAAEC,QAAQ,QAAQ,iBAAiB;AACxD,SAASC,iBAAiB,EAAEC,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAqBC,MAAM,QAAuB,eAAe;AAC3H,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,EAAEC,cAAc,QAAQ,yBAAyB;AAC5E,SAASC,gBAAgB,QAAQ,2BAA2B;AAI5D,SAASC,6BAA6B,QAAQ,yFAAyF;AAACjB,cAAA,GAAAkB,CAAA;AAkBjI,IAAMC,6BAA6B,GAAnC,MAAMA,6BAA6B;EAqBxCC,YACmBC,GAAsB,EACtBC,MAAiB;IAAAtB,cAAA,GAAAuB,CAAA;IAAAvB,cAAA,GAAAkB,CAAA;IADjB,KAAAG,GAAG,GAAHA,GAAG;IAAmBrB,cAAA,GAAAkB,CAAA;IACtB,KAAAI,MAAM,GAANA,MAAM;IAAWtB,cAAA,GAAAkB,CAAA;IArB3B,KAAAM,UAAU,GAAgB,EAAE;IAAAxB,cAAA,GAAAkB,CAAA;IAC5B,KAAAO,kBAAkB,GAAG,KAAK;IAAAzB,cAAA,GAAAkB,CAAA;IACzB,KAAAQ,qBAAqB,GAAG,IAAIpB,YAAY,EAAoB;IAAAN,cAAA,GAAAkB,CAAA;IAEtE,KAAAS,sBAAsB,GAAG,IAAIb,kBAAkB,EAAkB;IAACd,cAAA,GAAAkB,CAAA;IAClE,KAAAU,gBAAgB,GAAa,CAC3B,YAAY,EACZ,WAAW,EACX,YAAY,EACZ,qBAAqB,EACrB,YAAY,EACZ,KAAK,EACL,IAAI,EACJ,kBAAkB,EAClB,aAAa,EACb,mBAAmB,EACnB,SAAS,CACV;EAKE;EAEHC,QAAQA,CAAA;IAAA7B,cAAA,GAAAuB,CAAA;IAAAvB,cAAA,GAAAkB,CAAA;IACN,IAAI,CAACY,4BAA4B,EAAE;EACrC;EAEAC,WAAWA,CAACC,OAAsB;IAAAhC,cAAA,GAAAuB,CAAA;IAAAvB,cAAA,GAAAkB,CAAA;IAChC,IAAK,CAAAlB,cAAA,GAAAiC,CAAA,UAAAD,OAAO,CAAC,UAAU,CAAC,MAAAhC,cAAA,GAAAiC,CAAA,UAAI,CAACD,OAAO,CAAC,UAAU,CAAC,CAACE,WAAW,KACvD,CAAAlC,cAAA,GAAAiC,CAAA,UAAAD,OAAO,CAAC,YAAY,CAAC,MAAAhC,cAAA,GAAAiC,CAAA,UAAI,CAACD,OAAO,CAAC,YAAY,CAAC,CAACE,WAAW,CAAC,EAAE;MAAAlC,cAAA,GAAAiC,CAAA;MAAAjC,cAAA,GAAAkB,CAAA;MACjE,IAAI,CAACY,4BAA4B,EAAE;MAAC9B,cAAA,GAAAkB,CAAA;MACpC,IAAI,CAACG,GAAG,CAACc,aAAa,EAAE;IAC1B,CAAC;MAAAnC,cAAA,GAAAiC,CAAA;IAAA;EACH;EAEAG,uBAAuBA,CAAA;IAAApC,cAAA,GAAAuB,CAAA;IAAAvB,cAAA,GAAAkB,CAAA;IACrB,IAAI,CAAC,IAAI,CAACmB,QAAQ,EAAEC,cAAc,EAAEC,MAAM,EAAE;MAAAvC,cAAA,GAAAiC,CAAA;MAAAjC,cAAA,GAAAkB,CAAA;MAC1C,OAAOsB,SAAS;IAClB,CAAC;MAAAxC,cAAA,GAAAiC,CAAA;IAAA;IAAAjC,cAAA,GAAAkB,CAAA;IAED,OAAO,IAAI,CAACmB,QAAQ,CAACC,cAAc,CAACG,MAAM,CAAC,CAACC,MAAkC,EAAEC,OAAuB,KAAI;MAAA3C,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAkB,CAAA;MACzG,IAAI,CAAAlB,cAAA,GAAAiC,CAAA,WAACS,MAAM,KAAK,CAAA1C,cAAA,GAAAiC,CAAA,UAAAU,OAAO,CAACC,EAAE,KAAKJ,SAAS,MAAAxC,cAAA,GAAAiC,CAAA,UAAIS,MAAM,CAACE,EAAE,KAAKJ,SAAS,MAAAxC,cAAA,GAAAiC,CAAA,UAAIU,OAAO,CAACC,EAAE,GAAGF,MAAM,CAACE,EAAE,CAAC,EAAE;QAAA5C,cAAA,GAAAiC,CAAA;QAAAjC,cAAA,GAAAkB,CAAA;QAC9F,OAAOyB,OAAO;MAChB,CAAC;QAAA3C,cAAA,GAAAiC,CAAA;MAAA;MAAAjC,cAAA,GAAAkB,CAAA;MACD,OAAOwB,MAAM;IACf,CAAC,EAAEF,SAAS,CAAC;EACf;EAEQV,4BAA4BA,CAAA;IAAA9B,cAAA,GAAAuB,CAAA;IAClC,MAAMsB,YAAY,IAAA7C,cAAA,GAAAkB,CAAA,QAAG,IAAI,CAACkB,uBAAuB,EAAE;IAACpC,cAAA,GAAAkB,CAAA;IACpD,IAAI2B,YAAY,EAAE;MAAA7C,cAAA,GAAAiC,CAAA;MAAAjC,cAAA,GAAAkB,CAAA;MAChB,IAAI,CAACS,sBAAsB,CAACmB,IAAI,GAAG,CAACD,YAAY,CAAC;IACnD,CAAC,MAAM;MAAA7C,cAAA,GAAAiC,CAAA;MAAAjC,cAAA,GAAAkB,CAAA;MACL,IAAI,CAACS,sBAAsB,CAACmB,IAAI,GAAG,EAAE;IACvC;EACF;EAEAC,eAAeA,CAAA;IAAA/C,cAAA,GAAAuB,CAAA;IACb,MAAMsB,YAAY,IAAA7C,cAAA,GAAAkB,CAAA,QAAG,IAAI,CAACkB,uBAAuB,EAAE;IAACpC,cAAA,GAAAkB,CAAA;IACpD,OAAO2B,YAAY,IAAA7C,cAAA,GAAAiC,CAAA,UAAGY,YAAY,CAACG,YAAY,KAAAhD,cAAA,GAAAiC,CAAA,UAAG,CAAC;EACrD;EAEAgB,iBAAiBA,CAAA;IAAAjD,cAAA,GAAAuB,CAAA;IAAAvB,cAAA,GAAAkB,CAAA;IACf,IAAI,CAAC,IAAI,CAACmB,QAAQ,EAAEC,cAAc,EAAE;MAAAtC,cAAA,GAAAiC,CAAA;MAAAjC,cAAA,GAAAkB,CAAA;MAClC,OAAO,CAAC;IACV,CAAC;MAAAlB,cAAA,GAAAiC,CAAA;IAAA;IAED,MAAMY,YAAY,IAAA7C,cAAA,GAAAkB,CAAA,QAAG,IAAI,CAACkB,uBAAuB,EAAE;IAACpC,cAAA,GAAAkB,CAAA;IACpD,IAAI,CAAC2B,YAAY,EAAE;MAAA7C,cAAA,GAAAiC,CAAA;MAAAjC,cAAA,GAAAkB,CAAA;MACjB,OAAO,IAAI,CAACmB,QAAQ,CAACC,cAAc,CAACG,MAAM,CAAC,CAACS,GAAG,EAAEC,KAAK,KAAK;QAAAnD,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAkB,CAAA;QAAA,OAAAgC,GAAG,GAAGC,KAAK,CAACH,YAAY;MAAZ,CAAY,EAAE,CAAC,CAAC;IACzF,CAAC;MAAAhD,cAAA,GAAAiC,CAAA;IAAA;IAAAjC,cAAA,GAAAkB,CAAA;IAED,OAAO,IAAI,CAACmB,QAAQ,CAACC,cAAc,CAChCc,MAAM,CAACD,KAAK,IAAI;MAAAnD,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAkB,CAAA;MAAA,OAAAiC,KAAK,CAACP,EAAE,KAAKC,YAAY,CAACD,EAAE;IAAF,CAAE,CAAC,CAC7CH,MAAM,CAAC,CAACS,GAAG,EAAEC,KAAK,KAAK;MAAAnD,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAkB,CAAA;MAAA,OAAAgC,GAAG,GAAGC,KAAK,CAACH,YAAY;IAAZ,CAAY,EAAE,CAAC,CAAC;EACxD;EAEAK,kBAAkBA,CAAA;IAAArD,cAAA,GAAAuB,CAAA;IAAAvB,cAAA,GAAAkB,CAAA;IAChB,IAAI,CAAC,IAAI,CAACM,UAAU,EAAEe,MAAM,EAAE;MAAAvC,cAAA,GAAAiC,CAAA;MAAAjC,cAAA,GAAAkB,CAAA;MAC5B,OAAO,CAAC;IACV,CAAC;MAAAlB,cAAA,GAAAiC,CAAA;IAAA;IAAAjC,cAAA,GAAAkB,CAAA;IAED,OAAO,IAAI,CAACM,UAAU,CACnBiB,MAAM,CAAC,CAACS,GAAG,EAAEI,SAAS,KAAK;MAAAtD,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAkB,CAAA;MAAA,OAAAgC,GAAG,GAAGI,SAAS,CAACC,aAAa;IAAb,CAAa,EAAE,CAAC,CAAC;EACjE;EAEAC,aAAaA,CAAA;IAAAxD,cAAA,GAAAuB,CAAA;IACX,MAAMsB,YAAY,IAAA7C,cAAA,GAAAkB,CAAA,QAAG,IAAI,CAAC6B,eAAe,EAAE;IAC3C,MAAMU,SAAS,IAAAzD,cAAA,GAAAkB,CAAA,QAAG,IAAI,CAAC+B,iBAAiB,EAAE;IAC1C,MAAMzB,UAAU,IAAAxB,cAAA,GAAAkB,CAAA,QAAG,IAAI,CAACmC,kBAAkB,EAAE;IAACrD,cAAA,GAAAkB,CAAA;IAE7C,OAAO2B,YAAY,GAAGY,SAAS,GAAGjC,UAAU;EAC9C;EAEAkC,wBAAwBA,CAACC,aAA6B;IAAA3D,cAAA,GAAAuB,CAAA;IAAAvB,cAAA,GAAAkB,CAAA;IACpD,IAAI,CAACI,MAAM,CACRsC,IAAI,CAAC3C,6BAA6B,EAAE;MACnC4C,KAAK,EAAE,QAAQ;MACff,IAAI,EAAE;QACJa,aAAa;QACbtB,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvByB,cAAc,EAAE;;KAEnB,CAAC,CACDC,WAAW,EAAE,CACbC,SAAS,CAAEC,MAAuB,IAAI;MAAAjE,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAkB,CAAA;MACrC,IAAI,CAAC+C,MAAM,EAAE;QAAAjE,cAAA,GAAAiC,CAAA;QAAAjC,cAAA,GAAAkB,CAAA;QAAA;MAAA,CAAO;QAAAlB,cAAA,GAAAiC,CAAA;MAAA;MAAAjC,cAAA,GAAAkB,CAAA;MAEpB,IAAI,IAAI,CAACmB,QAAQ,CAACC,cAAc,EAAE;QAAAtC,cAAA,GAAAiC,CAAA;QAChC,MAAMiC,KAAK,IAAAlE,cAAA,GAAAkB,CAAA,QAAG,IAAI,CAACmB,QAAQ,CAACC,cAAc,CAAC6B,SAAS,CAACC,CAAC,IAAI;UAAApE,cAAA,GAAAuB,CAAA;UAAAvB,cAAA,GAAAkB,CAAA;UAAA,OAAAkD,CAAC,CAACxB,EAAE,KAAKqB,MAAM,CAACrB,EAAE;QAAF,CAAE,CAAC;QAAC5C,cAAA,GAAAkB,CAAA;QAC9E,IAAIgD,KAAK,KAAK,CAAC,CAAC,EAAE;UAAAlE,cAAA,GAAAiC,CAAA;UAAAjC,cAAA,GAAAkB,CAAA;UAChB,IAAI,CAACmB,QAAQ,CAACC,cAAc,CAAC4B,KAAK,CAAC,GAAGD,MAAM;UAACjE,cAAA,GAAAkB,CAAA;UAE7C,IAAI,CAACY,4BAA4B,EAAE;UAAC9B,cAAA,GAAAkB,CAAA;UACpC,IAAI,CAACQ,qBAAqB,CAAC2C,IAAI,CAAC,IAAI,CAAChC,QAAQ,CAACC,cAAc,CAAC;UAACtC,cAAA,GAAAkB,CAAA;UAC9D,IAAI,CAACG,GAAG,CAACc,aAAa,EAAE;QAC1B,CAAC;UAAAnC,cAAA,GAAAiC,CAAA;QAAA;MACH,CAAC;QAAAjC,cAAA,GAAAiC,CAAA;MAAA;IACH,CAAC,CAAC;EACN;;;;;;;;;;;;;;;;;cAzHC1B;MAAK;;cACLA;MAAK;;cACLA;MAAK;;cACLC;MAAM;;;;;AAJIW,6BAA6B,GAAAmD,UAAA,EAhBzCjE,SAAS,CAAC;EACTkE,QAAQ,EAAE,4BAA4B;EACtCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPhE,aAAa,EACbE,kBAAkB,EAClBI,cAAc,EACdH,aAAa,EACbC,eAAe,EACfG,gBAAgB,EAChBd,YAAY,EACZC,QAAQ,CACT;EACDuE,QAAA,EAAAC,oBAAsD;;CAEvD,CAAC,C,EACWxD,6BAA6B,CA2HzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}