import { ObligationsDialogComponent } from '@contract-management/components/obligations-dialog/obligations-dialog.component';
import { Obligation } from '@contract-management/models/obligation.model';
import { ObligationService } from '@contract-management/services/obligation.service';
import { InitialReportDocumentation } from '@contractor-dashboard/models/initial-report-documentation.model';
import { MonthlyReport } from '@contractor-dashboard/models/monthly-report.model';
import { ReportObligation } from '@contractor-dashboard/models/report-obligation.model';
import { ReportReviewHistory } from '@contractor-dashboard/models/report-review-history.model';
import { ReportReviewStatus } from '@contractor-dashboard/models/report-review-status.model';
import { SocialSecurityContribution } from '@contractor-dashboard/models/social-security-contribution.model';
import { InitialReportDocumentationService } from '@contractor-dashboard/services/initial-report-documentation.service';
import { MonthlyReportService } from '@contractor-dashboard/services/monthly-report.service';
import { ReportObligationService } from '@contractor-dashboard/services/report-obligation.service';
import { ReportReviewHistoryService } from '@contractor-dashboard/services/report-review-history.service';
import { ReportReviewStatusService } from '@contractor-dashboard/services/report-review-status.service';
import { SocialSecurityContributionService } from '@contractor-dashboard/services/social-security-contribution.service';
import { AuthService } from '@core/auth/services/auth.service';
import { AlertService } from '@shared/services/alert.service';
import { ContractorBasicInformationComponent } from './contractor-basic-information/contractor-basic-information.component';
import { MonthlyReportApprovalDialogComponent } from './monthly-report-approval-dialog/monthly-report-approval-dialog.component';
import { MonthlyReportBasicDataComponent } from './monthly-report-basic-data/monthly-report-basic-data.component';
import { MonthlyReportInitialDocumentationComponent } from './monthly-report-initial-documentation/monthly-report-initial-documentation.component';
import { MonthlyReportSocialSecurityInformationComponent } from './monthly-report-social-security-information/monthly-report-social-security-information.component';

import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  Inject,
  OnInit,
  ViewChild,
} from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup } from '@angular/forms';
import { MatButton, MatIconButton } from '@angular/material/button';
import {
  MatCard,
  MatCardContent,
  MatCardHeader,
  MatCardSubtitle,
  MatCardTitle,
} from '@angular/material/card';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import {
  MatStep,
  MatStepper,
  MatStepperNext,
  MatStepperPrevious,
} from '@angular/material/stepper';
import { forkJoin, of, switchMap } from 'rxjs';
import { catchError, finalize } from 'rxjs/operators';
import { MonthlyReportObligationsComponent } from './monthly-report-obligations/monthly-report-obligations.component';

@Component({
  selector: 'app-monthly-report-dialog',
  templateUrl: './monthly-report-dialog.component.html',
  styleUrl: './monthly-report-dialog.component.scss',
  standalone: true,
  imports: [
    MatIconButton,
    MatIcon,
    MatCard,
    MatCardHeader,
    MatCardTitle,
    MatCardSubtitle,
    MatCardContent,
    MatStepper,
    MatStep,
    MonthlyReportInitialDocumentationComponent,
    MatButton,
    MatStepperNext,
    ContractorBasicInformationComponent,
    MatStepperPrevious,
    MonthlyReportBasicDataComponent,
    MonthlyReportObligationsComponent,
    MonthlyReportSocialSecurityInformationComponent,
  ],
})
export class MonthlyReportDialogComponent implements OnInit, AfterViewInit {
  @ViewChild('stepper') stepper!: MatStepper;
  @ViewChild('initialDocumentationForm')
  initialDocumentationForm!: MonthlyReportInitialDocumentationComponent;
  @ViewChild('contractorBasicInfoForm')
  contractorBasicInfoForm!: ContractorBasicInformationComponent;
  @ViewChild(MonthlyReportSocialSecurityInformationComponent)
  socialSecurityInfoComponent!: MonthlyReportSocialSecurityInformationComponent;
  @ViewChild(MonthlyReportBasicDataComponent)
  monthlyReportBasicDataComponent!: MonthlyReportBasicDataComponent;

  isSecondStepValid = false;
  report: MonthlyReport;
  obligations: Obligation[] = [];
  reportObligations: ReportObligation[] = [];
  socialSecurityContribution: SocialSecurityContribution | null = null;
  initialReportDocumentation: InitialReportDocumentation | null = null;
  isLoading = true;
  isNewReport: boolean;
  currentStep = 0;
  isFirstStepValid = false;
  isFirstReport: boolean;
  fallbackForm: FormGroup;
  isSocialSecurityFormValid = false;
  isSupervisor = false;
  showRejectionComments = false;
  rejectionComments = '';
  isBasicDataStepValid = true;

  constructor(
    public dialogRef: MatDialogRef<MonthlyReportDialogComponent>,
    @Inject(MAT_DIALOG_DATA)
    public data: { report: MonthlyReport; isNewReport: boolean },
    private readonly authService: AuthService,
    private readonly reportObligationService: ReportObligationService,
    private readonly socialSecurityContributionService: SocialSecurityContributionService,
    private readonly alert: AlertService,
    private readonly obligationService: ObligationService,
    private readonly monthlyReportService: MonthlyReportService,
    private readonly reportReviewStatusService: ReportReviewStatusService,
    private readonly reportReviewHistoryService: ReportReviewHistoryService,
    private readonly initialReportDocumentationService: InitialReportDocumentationService,
    private readonly cdr: ChangeDetectorRef,
    private readonly fb: FormBuilder,
    private readonly dialog: MatDialog,
  ) {
    this.report = data.report;
    this.isNewReport = data.isNewReport || false;
    this.isFirstReport = data.report.isFirstReport || false;
    this.fallbackForm = this.fb.group({});
    this.isSupervisor = this.authService.hasProfile('SUPERVISOR');
  }

  ngOnInit(): void {
    this.loadReportDetails();
    this.checkRejectionStatus();
  }

  ngAfterViewInit(): void {
    if (this.stepper) {
      this.stepper.selectionChange.subscribe((event) => {
        const currentIndex = event.selectedIndex;
        const previousIndex = event.previouslySelectedIndex;

        if (this.isFirstReport && currentIndex > previousIndex) {
          switch (currentIndex) {
            case 1:
              this.saveInitialDocumentation();
              break;
            case 2:
              this.saveContractorBasicInfo();
              break;
            case 3:
              this.saveMonthlyReport();
              break;
          }
        } else if (
          !this.isFirstReport &&
          currentIndex === 1 &&
          previousIndex === 0
        ) {
          this.saveMonthlyReport();
        }
      });
    }
    if (this.initialDocumentationForm) {
      this.initialDocumentationForm.formValidityChange.subscribe((isValid) => {
        this.isFirstStepValid = isValid;
        this.cdr.detectChanges();
      });
    }
    if (this.contractorBasicInfoForm) {
      this.contractorBasicInfoForm.formValidityChange.subscribe((isValid) => {
        this.isSecondStepValid = isValid;
        this.cdr.detectChanges();
      });
    }
    if (this.monthlyReportBasicDataComponent) {
      this.monthlyReportBasicDataComponent.formValidityChange.subscribe(
        (isValid) => {
          this.isBasicDataStepValid = isValid;
          this.cdr.detectChanges();
        },
      );
    }
  }

  loadReportDetails(): void {
    this.isLoading = true;
    forkJoin({
      obligations: this.obligationService
        .getAllByContractId(this.report.contractorContract?.contract?.id || 0)
        .pipe(
          catchError((_) => {
            return of([]);
          }),
        ),
      reportObligations:
        this.isNewReport || this.report.id === undefined
          ? of([])
          : this.reportObligationService
              .getByMonthlyReportId(this.report.id)
              .pipe(
                catchError((_) => {
                  return of([]);
                }),
              ),
      socialSecurityContribution:
        this.report.id !== undefined
          ? this.socialSecurityContributionService
              .getByMonthlyReportId(this.report.id)
              .pipe(
                catchError((_) => {
                  return of(null);
                }),
              )
          : of(null),
      initialReportDocumentation: this.initialReportDocumentationService
        .getByContractorContractId(this.report.contractorContract?.id || 0)
        .pipe(
          catchError((_) => {
            return of(null);
          }),
        ),
    })
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe({
        next: ({
          obligations,
          reportObligations,
          socialSecurityContribution,
          initialReportDocumentation,
        }) => {
          this.obligations = obligations;
          this.reportObligations = reportObligations;
          this.socialSecurityContribution = socialSecurityContribution;
          this.initialReportDocumentation = initialReportDocumentation;
          this.prepareReportObligations();
          this.cdr.detectChanges();
        },
        error: (error) => {
          this.alert.error(
            error.error?.detail ?? 'Error al cargar los detalles del informe',
          );
          this.obligations = [];
          this.reportObligations = [];
          this.socialSecurityContribution = null;
        },
      });
  }

  prepareReportObligations(): void {
    this.obligations.forEach((obligation) => {
      if (
        !this.reportObligations.some((ro) => ro.obligationId === obligation.id)
      ) {
        this.reportObligations.push({
          id: 0,
          monthlyReportId: this.report.id || 0,
          obligationId: obligation.id ?? 0,
          description: '',
          evidence: '',
          filePath: '',
        });
      }
    });
  }

  onClose(): void {
    this.dialogRef.close();
  }

  onCloseSocialSecurity(): void {
    if (!this.validateObligations()) {
      return;
    }

    if (this.socialSecurityInfoComponent) {
      const socialSecurityData =
        this.socialSecurityInfoComponent.getSocialSecurityData();
      this.saveSocialSecurity(socialSecurityData);
    }
  }

  saveSocialSecurity(socialSecurityData: SocialSecurityContribution): void {
    this.isLoading = true;
    this.cdr.detectChanges();

    const formData = new FormData();
    const file =
      this.socialSecurityInfoComponent?.socialSecurityForm.get(
        'certificateFile',
      )?.value;
    if (file) {
      formData.append('file', file);
    }

    formData.append(
      'data',
      JSON.stringify({
        paymentFormNumber: socialSecurityData.paymentFormNumber,
        healthContribution: socialSecurityData.healthContribution,
        pensionContribution: socialSecurityData.pensionContribution,
        arlContribution: socialSecurityData.arlContribution,
        compensationFundContribution:
          socialSecurityData.compensationFundContribution,
        monthlyReportId: this.report.id,
        arlAffiliationClassId: socialSecurityData.arlAffiliationClassId,
        compensationFundId: socialSecurityData.compensationFundId,
        ibc: socialSecurityData.ibc,
      }),
    );

    const saveOperation = socialSecurityData.id
      ? this.socialSecurityContributionService.updateWithFile(
          socialSecurityData.id,
          formData,
        )
      : this.socialSecurityContributionService.createWithFile(formData);

    saveOperation
      .pipe(
        finalize(() => {
          this.isLoading = false;
          this.cdr.detectChanges();
        }),
      )
      .subscribe({
        next: (savedSocialSecurity) => {
          this.socialSecurityContribution = savedSocialSecurity;
          this.changeReportStatus();
        },
        error: (error) => {
          if (error.status === 400) {
            const spanishMessages: Record<string, string> = {
              'Invalid file type. Only PDF files are allowed.':
                'Tipo de archivo inválido. Solo se permiten archivos PDF.',
              'Empty file provided': 'El archivo está vacío',
              'Error uploading file to S3': 'Error al subir el archivo',
            };

            const errorMessage = error.error.detail.replace(
              'Validation Error: ',
              '',
            ) as string;
            const translatedMessage =
              spanishMessages[errorMessage] || 'Error de validación';

            this.alert.error(translatedMessage);
          } else {
            this.alert.error(
              error.error?.detail ??
                'Error al guardar la información de seguridad social',
            );
          }
        },
      });
  }

  async changeReportStatus(): Promise<void> {
    if (!this.validateObligations()) {
      return;
    }

    const confirmed = await this.alert.confirm(
      '¿Está seguro de enviar el informe a revisión?',
      'Una vez enviado, no podrá realizar más cambios hasta que el supervisor lo revise',
    );

    if (!confirmed) {
      return;
    }

    this.reportReviewStatusService
      .getByName('Pendiente de revisión')
      .subscribe({
        next: (reportReviewStatus) => {
          if (reportReviewStatus) {
            this.reportReviewHistoryService
              .create({
                monthlyReportId: this.report.id,
                reviewStatusId: reportReviewStatus.id,
                reviewDate: new Date(),
                reviewerId: this.authService.getCurrentUser()?.id,
              })
              .subscribe({
                next: (createdReviewHistory) => {
                  this.report.reviewHistory = [
                    createdReviewHistory,
                    ...(this.report.reviewHistory || []),
                  ];
                  this.report.currentReviewStatus = reportReviewStatus;
                  this.alert.success(
                    'Informe finalizado y enviado para revisión',
                  );
                  this.dialogRef.close(true);
                },
                error: (error) => {
                  const translatedMessage =
                    error.error?.detail ??
                    'Error al cambiar el estado del informe';
                  this.alert.error(translatedMessage);
                },
              });
          } else {
            this.alert.error('Error al obtener el estado de revisión');
          }
        },
        error: (error) => {
          this.alert.error(
            error.error?.detail ?? 'Error al obtener el estado de revisión',
          );
        },
      });
  }

  isFirstStep(): boolean {
    return this.stepper ? this.stepper.selectedIndex === 0 : true;
  }

  isLastStep(): boolean {
    return this.stepper
      ? this.stepper.selectedIndex === this.stepper.steps.length - 1
      : false;
  }

  updateStepperState(): void {
    this.isFirstStep();
    this.isLastStep();
  }

  onSaveEditing(updatedObligation: ReportObligation): void {
    this.isLoading = true;
    this.cdr.detectChanges();

    const saveOperation = updatedObligation.id
      ? this.reportObligationService.update(
          updatedObligation.id,
          updatedObligation,
        )
      : this.reportObligationService.create(updatedObligation);

    saveOperation
      .pipe(
        finalize(() => {
          this.isLoading = false;
          this.cdr.detectChanges();
        }),
      )
      .subscribe({
        next: (savedReportObligation) => {
          this.reportObligations = this.reportObligations.map((ro) =>
            ro.obligationId === savedReportObligation.obligationId
              ? savedReportObligation
              : ro,
          );
          this.alert.success('Cambios guardados exitosamente');
        },
        error: (error) => {
          this.alert.error(
            error.error?.detail ?? 'Error al guardar los cambios',
          );
        },
      });
  }

  get contractorFormControl(): AbstractControl {
    return this.contractorBasicInfoForm?.contractorForm || this.fallbackForm;
  }

  openObligationsDialog(): void {
    if (!this.report.contractorContract?.contract?.id) {
      this.alert.error('Error: ID del contrato no disponible');
      return;
    }

    const dialogRef = this.dialog.open(ObligationsDialogComponent, {
      width: '80%',
      maxWidth: '1200px',
      maxHeight: '90vh',
      autoFocus: false,
      disableClose: true,
      data: this.report.contractorContract.contract,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result?.action) {
        this.updateObligations();
      }
    });
  }

  updateObligations(): void {
    this.isLoading = true;
    this.obligationService
      .getAllByContractId(this.report.contractorContract?.contract?.id || 0)
      .pipe(
        finalize(() => {
          this.isLoading = false;
          this.cdr.detectChanges();
        }),
      )
      .subscribe({
        next: (obligations) => {
          this.obligations = obligations;
          this.prepareReportObligations();
        },
        error: (error) => {
          this.alert.error(
            error.error?.detail ?? 'Error al actualizar las obligaciones',
          );
        },
      });
  }

  async saveInitialDocumentation(): Promise<void> {
    if (this.isSupervisor) {
      return;
    }

    if (this.initialDocumentationForm) {
      const saved =
        await this.initialDocumentationForm.saveInitialDocumentation();
      if (saved) {
        this.initialReportDocumentationService
          .getByContractorContractId(this.report.contractorContract?.id || 0)
          .subscribe({
            next: (initialReportDocumentation: InitialReportDocumentation) => {
              this.initialReportDocumentation = initialReportDocumentation;
              this.monthlyReportService
                .update(this.report.id, {
                  contractorBankName:
                    this.initialDocumentationForm.bankInfoForm.getBankName(
                      this.initialDocumentationForm.bankInfoForm.form.get(
                        'bank',
                      )?.value,
                    ),
                  contractorAccountNumber:
                    this.initialDocumentationForm.bankInfoForm.form.get(
                      'accountNumber',
                    )?.value,
                  contractorAccountType:
                    this.initialDocumentationForm.bankInfoForm.getAccountTypeName(
                      this.initialDocumentationForm.bankInfoForm.form.get(
                        'accountType',
                      )?.value,
                    ),
                })
                .subscribe({
                  next: () => {
                    this.alert.success(
                      'Documentación inicial guardada exitosamente',
                    );
                  },
                  error: (error) => {
                    this.alert.error(
                      error.error?.detail ??
                        'Error al guardar la documentación inicial',
                    );
                  },
                });
            },
            error: (error) => {
              this.alert.error(
                error.error?.detail ??
                  'Error al guardar la documentación inicial',
              );
              this.stepper.selectedIndex = 0;
            },
          });
      } else {
        this.stepper.selectedIndex = 0;
      }
    }
  }

  async saveContractorBasicInfo(): Promise<void> {
    if (this.isSupervisor) {
      return;
    }

    if (this.contractorBasicInfoForm) {
      const saved = await this.contractorBasicInfoForm.updateContractor();
      if (saved) {
        this.monthlyReportBasicDataComponent.loadContractDetails();
      } else {
        this.stepper.selectedIndex = 1;
      }
    }
  }

  saveMonthlyReport(): void {
    if (this.isSupervisor || !this.report.id) {
      return;
    }

    this.isLoading = true;
    this.cdr.detectChanges();

    this.monthlyReportService
      .update(this.report.id, this.report)
      .pipe(
        switchMap((savedReport) => {
          this.report = savedReport;
          return this.monthlyReportBasicDataComponent.saveData();
        }),
        catchError((error) => {
          this.alert.error(
            error.error?.detail ?? 'Error al guardar el informe mensual',
          );
          this.stepper.previous();
          return of(false);
        }),
        finalize(() => {
          this.isLoading = false;
          this.cdr.detectChanges();
        }),
      )
      .subscribe({
        next: (dataSaved: boolean) => {
          if (!dataSaved) {
            this.stepper.previous();
            return;
          }
          this.alert.success('Informe mensual guardado exitosamente');
        },
        error: (error) => {
          this.alert.error(
            error.error?.detail ?? 'Error al guardar el informe mensual',
          );
          this.stepper.previous();
        },
      });
  }

  onApproveReport(): void {
    const dialogRef = this.dialog.open(MonthlyReportApprovalDialogComponent, {
      width: '500px',
      data: { isRejection: false },
    });

    dialogRef.afterClosed().subscribe((comments) => {
      if (comments) {
        this.reportReviewStatusService.getByName('Aprobado').subscribe({
          next: (status: ReportReviewStatus) => {
            this.monthlyReportService
              .update(this.report.id, { observations: comments })
              .subscribe({
                next: (updatedReport: MonthlyReport) => {
                  this.report = updatedReport;
                  this.reportReviewHistoryService
                    .create({
                      monthlyReportId: this.report.id,
                      reviewStatusId: status.id,
                      reviewDate: new Date(),
                      comment: comments,
                      reviewerId: this.authService.getCurrentUser()?.id,
                    })
                    .subscribe({
                      next: (reviewHistory: ReportReviewHistory) => {
                        this.report.currentReviewStatus = status;
                        this.report.reviewHistory = [
                          reviewHistory,
                          ...(this.report.reviewHistory || []),
                        ];

                        this.alert.success('Informe aprobado exitosamente');
                        this.dialogRef.close(true);
                      },
                      error: (error) => {
                        this.alert.error(
                          error.error?.detail ??
                            'Error al registrar la aprobación del informe',
                        );
                      },
                    });
                },
                error: (error) => {
                  this.alert.error(
                    error.error?.detail ??
                      'Error al actualizar las observaciones del informe',
                  );
                },
              });
          },
          error: (error) => {
            this.alert.error(
              error.error?.detail ?? 'Error al obtener el estado de aprobación',
            );
          },
        });
      }
    });
  }

  onRejectReport(): void {
    const dialogRef = this.dialog.open(MonthlyReportApprovalDialogComponent, {
      width: '500px',
      data: { isRejection: true },
    });

    dialogRef.afterClosed().subscribe((comments) => {
      if (comments) {
        this.reportReviewStatusService.getByName('Rechazado').subscribe({
          next: (status: ReportReviewStatus) => {
            this.monthlyReportService
              .update(this.report.id, { observations: comments })
              .subscribe({
                next: (updatedReport: MonthlyReport) => {
                  this.report = updatedReport;
                  this.reportReviewHistoryService
                    .create({
                      monthlyReportId: this.report.id,
                      reviewStatusId: status.id,
                      reviewDate: new Date(),
                      comment: comments,
                      reviewerId: this.authService.getCurrentUser()?.id,
                    })
                    .subscribe({
                      next: (reviewHistory: ReportReviewHistory) => {
                        this.report.currentReviewStatus = status;
                        this.report.reviewHistory = [
                          reviewHistory,
                          ...(this.report.reviewHistory || []),
                        ];

                        this.alert.success('Informe rechazado exitosamente');
                        this.dialogRef.close(true);
                      },
                      error: (error) => {
                        this.alert.error(
                          error.error?.detail ??
                            'Error al registrar el rechazo del informe',
                        );
                      },
                    });
                },
                error: (error) => {
                  this.alert.error(
                    error.error?.detail ??
                      'Error al actualizar las observaciones del informe',
                  );
                },
              });
          },
          error: (error) => {
            this.alert.error(
              error.error?.detail ?? 'Error al obtener el estado de rechazo',
            );
          },
        });
      }
    });
  }

  checkRejectionStatus(): void {
    if (
      !this.isSupervisor &&
      this.report.currentReviewStatus?.name === 'Rechazado' &&
      this.report.reviewHistory &&
      this.report.reviewHistory.length > 0
    ) {
      const rejectionHistories = this.report.reviewHistory
        .filter((history) => history.reviewStatus?.name === 'Rechazado')
        .sort(
          (a, b) =>
            new Date(b.reviewDate).getTime() - new Date(a.reviewDate).getTime(),
        );

      const latestRejection = rejectionHistories[0];

      if (latestRejection?.comment) {
        this.showRejectionComments = true;
        this.rejectionComments = latestRejection.comment;
      }
    }
  }

  get showLastStep(): boolean {
    return this.report?.reportNumber >= 2;
  }

  validateObligations(): boolean {
    if (!this.reportObligations.length) {
      this.alert.error(
        'No hay obligaciones registradas. Debe agregar al menos una obligación antes de finalizar el informe',
      );
      return false;
    }

    const hasEmptyObligations = this.reportObligations.some(
      (obligation) =>
        !obligation.description?.trim() || !obligation.evidence?.trim(),
    );

    if (hasEmptyObligations) {
      this.alert.error(
        'Debe completar la descripción y evidencia de todas las obligaciones antes de finalizar el informe',
      );
      return false;
    }

    return true;
  }
}
