{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/Github/SICMA/sicma-ui/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { SelectPeriodDialogComponent } from './select-period-dialog.component';\ndescribe('SelectPeriodDialogComponent', () => {\n  let component;\n  let fixture;\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    yield TestBed.configureTestingModule({\n      imports: [SelectPeriodDialogComponent],\n      providers: [{\n        provide: MatDialogRef,\n        useValue: {\n          close: jasmine.createSpy('close')\n        }\n      }, {\n        provide: MAT_DIALOG_DATA,\n        useValue: {}\n      }]\n    }).compileComponents();\n    fixture = TestBed.createComponent(SelectPeriodDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  }));\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "MatDialogRef", "MAT_DIALOG_DATA", "SelectPeriodDialogComponent", "describe", "component", "fixture", "beforeEach", "_asyncToGenerator", "configureTestingModule", "imports", "providers", "provide", "useValue", "close", "jasmine", "createSpy", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contractor-dashboard\\components\\monthly-reports-tab\\select-period-dialog\\select-period-dialog.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\r\nimport { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';\r\n\r\nimport { SelectPeriodDialogComponent } from './select-period-dialog.component';\r\n\r\ndescribe('SelectPeriodDialogComponent', () => {\r\n  let component: SelectPeriodDialogComponent;\r\n  let fixture: ComponentFixture<SelectPeriodDialogComponent>;\r\n\r\n  beforeEach(async () => {\r\n    await TestBed.configureTestingModule({\r\n      imports: [SelectPeriodDialogComponent],\r\n      providers: [\r\n        {\r\n          provide: MatDialogRef,\r\n          useValue: {\r\n            close: jasmine.createSpy('close'),\r\n          },\r\n        },\r\n        {\r\n          provide: MAT_DIALOG_DATA,\r\n          useValue: {},\r\n        },\r\n      ],\r\n    }).compileComponents();\r\n\r\n    fixture = TestBed.createComponent(SelectPeriodDialogComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r"], "mappings": ";AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AACjE,SAASC,YAAY,EAAEC,eAAe,QAAQ,0BAA0B;AAExE,SAASC,2BAA2B,QAAQ,kCAAkC;AAE9EC,QAAQ,CAAC,6BAA6B,EAAE,MAAK;EAC3C,IAAIC,SAAsC;EAC1C,IAAIC,OAAsD;EAE1DC,UAAU,eAAAC,iBAAA,CAAC,aAAW;IACpB,MAAMR,OAAO,CAACS,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CAACP,2BAA2B,CAAC;MACtCQ,SAAS,EAAE,CACT;QACEC,OAAO,EAAEX,YAAY;QACrBY,QAAQ,EAAE;UACRC,KAAK,EAAEC,OAAO,CAACC,SAAS,CAAC,OAAO;;OAEnC,EACD;QACEJ,OAAO,EAAEV,eAAe;QACxBW,QAAQ,EAAE;OACX;KAEJ,CAAC,CAACI,iBAAiB,EAAE;IAEtBX,OAAO,GAAGN,OAAO,CAACkB,eAAe,CAACf,2BAA2B,CAAC;IAC9DE,SAAS,GAAGC,OAAO,CAACa,iBAAiB;IACrCb,OAAO,CAACc,aAAa,EAAE;EACzB,CAAC,EAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACjB,SAAS,CAAC,CAACkB,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}