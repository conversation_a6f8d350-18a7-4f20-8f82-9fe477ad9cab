{"ast": null, "code": "import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { TestBed } from '@angular/core/testing';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { ObligationsDialogComponent } from './obligations-dialog.component';\ndescribe('ObligationsDialogComponent', () => {\n  let component;\n  let fixture;\n  let dialogRefSpy;\n  const mockContract = {\n    id: 1,\n    contractNumber: 123,\n    object: 'Test Contract',\n    rup: false,\n    secopCode: 12345,\n    addition: false,\n    cession: false,\n    settled: false,\n    causesSelectionId: 1,\n    managementSupportId: 1,\n    contractClassId: 1,\n    monthlyPayment: 1000000,\n    obligations: [{\n      id: 1,\n      name: 'Obligation 1',\n      contractId: 1\n    }, {\n      id: 2,\n      name: 'Obligation 2',\n      contractId: 1\n    }]\n  };\n  beforeEach(() => {\n    dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close']);\n    TestBed.configureTestingModule({\n      imports: [ObligationsDialogComponent, HttpClientTestingModule, BrowserAnimationsModule],\n      providers: [{\n        provide: MatDialogRef,\n        useValue: dialogRefSpy\n      }, {\n        provide: MAT_DIALOG_DATA,\n        useValue: mockContract\n      }]\n    });\n    fixture = TestBed.createComponent(ObligationsDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should be initialized with the correct contract data', () => {\n    expect(component.contract).toEqual(jasmine.objectContaining({\n      id: 1,\n      contractNumber: 123,\n      object: 'Test Contract'\n    }));\n  });\n  it('should initialize actionPerformed as false', () => {\n    const privateComponent = component;\n    expect(privateComponent.actionPerformed).toBeFalse();\n  });\n  it('should set actionPerformed to true when onObligationChanged is called', () => {\n    component.onObligationChanged();\n    const privateComponent = component;\n    expect(privateComponent.actionPerformed).toBeTrue();\n  });\n  it('should close the dialog with action:true when changes were made', () => {\n    component.onObligationChanged();\n    component.onClose();\n    expect(dialogRefSpy.close).toHaveBeenCalledWith({\n      action: true\n    });\n  });\n  it('should close the dialog with action:false when no changes were made', () => {\n    component.onClose();\n    expect(dialogRefSpy.close).toHaveBeenCalledWith({\n      action: false\n    });\n  });\n  it('should pass the contract to the obligations list component', () => {\n    fixture.detectChanges();\n    const obligationsListEl = fixture.nativeElement.querySelector('app-obligations-list');\n    expect(obligationsListEl).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["HttpClientTestingModule", "TestBed", "MAT_DIALOG_DATA", "MatDialogRef", "BrowserAnimationsModule", "ObligationsDialogComponent", "describe", "component", "fixture", "dialogRefSpy", "mockContract", "id", "contractNumber", "object", "rup", "secopCode", "addition", "cession", "settled", "causesSelectionId", "managementSupportId", "contractClassId", "monthlyPayment", "obligations", "name", "contractId", "beforeEach", "jasmine", "createSpyObj", "configureTestingModule", "imports", "providers", "provide", "useValue", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy", "contract", "toEqual", "objectContaining", "privateComponent", "actionPerformed", "toBeFalse", "onObligationChanged", "toBeTrue", "onClose", "close", "toHaveBeenCalledWith", "action", "obligationsListEl", "nativeElement", "querySelector"], "sources": ["C:\\Users\\<USER>\\Documents\\Github\\SICMA\\sicma-ui\\src\\app\\features\\contract-management\\components\\obligations-dialog\\obligations-dialog.component.spec.ts"], "sourcesContent": ["import { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { ComponentFixture, TestBed } from '@angular/core/testing';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { ObligationsDialogComponent } from './obligations-dialog.component';\nimport { Contract } from '@contract-management/models/contract.model';\n\ndescribe('ObligationsDialogComponent', () => {\n  let component: ObligationsDialogComponent;\n  let fixture: ComponentFixture<ObligationsDialogComponent>;\n  let dialogRefSpy: jasmine.SpyObj<MatDialogRef<ObligationsDialogComponent>>;\n\n  const mockContract: Partial<Contract> = {\n    id: 1,\n    contractNumber: 123,\n    object: 'Test Contract',\n    rup: false,\n    secopCode: 12345,\n    addition: false,\n    cession: false,\n    settled: false,\n    causesSelectionId: 1,\n    managementSupportId: 1,\n    contractClassId: 1,\n    monthlyPayment: 1000000,\n    obligations: [\n      { id: 1, name: 'Obligation 1', contractId: 1 },\n      { id: 2, name: 'Obligation 2', contractId: 1 },\n    ],\n  };\n\n  beforeEach(() => {\n    dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close']);\n\n    TestBed.configureTestingModule({\n      imports: [\n        ObligationsDialogComponent,\n        HttpClientTestingModule,\n        BrowserAnimationsModule,\n      ],\n      providers: [\n        { provide: MatDialogRef, useValue: dialogRefSpy },\n        { provide: MAT_DIALOG_DATA, useValue: mockContract },\n      ],\n    });\n    fixture = TestBed.createComponent(ObligationsDialogComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should be initialized with the correct contract data', () => {\n    expect(component.contract).toEqual(\n      jasmine.objectContaining({\n        id: 1,\n        contractNumber: 123,\n        object: 'Test Contract',\n      }),\n    );\n  });\n\n  it('should initialize actionPerformed as false', () => {\n    const privateComponent = component as unknown as {\n      actionPerformed: boolean;\n    };\n    expect(privateComponent.actionPerformed).toBeFalse();\n  });\n\n  it('should set actionPerformed to true when onObligationChanged is called', () => {\n    component.onObligationChanged();\n    const privateComponent = component as unknown as {\n      actionPerformed: boolean;\n    };\n    expect(privateComponent.actionPerformed).toBeTrue();\n  });\n\n  it('should close the dialog with action:true when changes were made', () => {\n    component.onObligationChanged();\n    component.onClose();\n    expect(dialogRefSpy.close).toHaveBeenCalledWith({ action: true });\n  });\n\n  it('should close the dialog with action:false when no changes were made', () => {\n    component.onClose();\n    expect(dialogRefSpy.close).toHaveBeenCalledWith({ action: false });\n  });\n\n  it('should pass the contract to the obligations list component', () => {\n    fixture.detectChanges();\n    const obligationsListEl = fixture.nativeElement.querySelector(\n      'app-obligations-list',\n    );\n    expect(obligationsListEl).toBeTruthy();\n  });\n});"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,8BAA8B;AACtE,SAA2BC,OAAO,QAAQ,uBAAuB;AACjE,SAASC,eAAe,EAAEC,YAAY,QAAQ,0BAA0B;AACxE,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,0BAA0B,QAAQ,gCAAgC;AAG3EC,QAAQ,CAAC,4BAA4B,EAAE,MAAK;EAC1C,IAAIC,SAAqC;EACzC,IAAIC,OAAqD;EACzD,IAAIC,YAAsE;EAE1E,MAAMC,YAAY,GAAsB;IACtCC,EAAE,EAAE,CAAC;IACLC,cAAc,EAAE,GAAG;IACnBC,MAAM,EAAE,eAAe;IACvBC,GAAG,EAAE,KAAK;IACVC,SAAS,EAAE,KAAK;IAChBC,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,KAAK;IACdC,iBAAiB,EAAE,CAAC;IACpBC,mBAAmB,EAAE,CAAC;IACtBC,eAAe,EAAE,CAAC;IAClBC,cAAc,EAAE,OAAO;IACvBC,WAAW,EAAE,CACX;MAAEZ,EAAE,EAAE,CAAC;MAAEa,IAAI,EAAE,cAAc;MAAEC,UAAU,EAAE;IAAC,CAAE,EAC9C;MAAEd,EAAE,EAAE,CAAC;MAAEa,IAAI,EAAE,cAAc;MAAEC,UAAU,EAAE;IAAC,CAAE;GAEjD;EAEDC,UAAU,CAAC,MAAK;IACdjB,YAAY,GAAGkB,OAAO,CAACC,YAAY,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC;IAE9D3B,OAAO,CAAC4B,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CACPzB,0BAA0B,EAC1BL,uBAAuB,EACvBI,uBAAuB,CACxB;MACD2B,SAAS,EAAE,CACT;QAAEC,OAAO,EAAE7B,YAAY;QAAE8B,QAAQ,EAAExB;MAAY,CAAE,EACjD;QAAEuB,OAAO,EAAE9B,eAAe;QAAE+B,QAAQ,EAAEvB;MAAY,CAAE;KAEvD,CAAC;IACFF,OAAO,GAAGP,OAAO,CAACiC,eAAe,CAAC7B,0BAA0B,CAAC;IAC7DE,SAAS,GAAGC,OAAO,CAAC2B,iBAAiB;IACrC3B,OAAO,CAAC4B,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAAC/B,SAAS,CAAC,CAACgC,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,sDAAsD,EAAE,MAAK;IAC9DC,MAAM,CAAC/B,SAAS,CAACiC,QAAQ,CAAC,CAACC,OAAO,CAChCd,OAAO,CAACe,gBAAgB,CAAC;MACvB/B,EAAE,EAAE,CAAC;MACLC,cAAc,EAAE,GAAG;MACnBC,MAAM,EAAE;KACT,CAAC,CACH;EACH,CAAC,CAAC;EAEFwB,EAAE,CAAC,4CAA4C,EAAE,MAAK;IACpD,MAAMM,gBAAgB,GAAGpC,SAExB;IACD+B,MAAM,CAACK,gBAAgB,CAACC,eAAe,CAAC,CAACC,SAAS,EAAE;EACtD,CAAC,CAAC;EAEFR,EAAE,CAAC,uEAAuE,EAAE,MAAK;IAC/E9B,SAAS,CAACuC,mBAAmB,EAAE;IAC/B,MAAMH,gBAAgB,GAAGpC,SAExB;IACD+B,MAAM,CAACK,gBAAgB,CAACC,eAAe,CAAC,CAACG,QAAQ,EAAE;EACrD,CAAC,CAAC;EAEFV,EAAE,CAAC,iEAAiE,EAAE,MAAK;IACzE9B,SAAS,CAACuC,mBAAmB,EAAE;IAC/BvC,SAAS,CAACyC,OAAO,EAAE;IACnBV,MAAM,CAAC7B,YAAY,CAACwC,KAAK,CAAC,CAACC,oBAAoB,CAAC;MAAEC,MAAM,EAAE;IAAI,CAAE,CAAC;EACnE,CAAC,CAAC;EAEFd,EAAE,CAAC,qEAAqE,EAAE,MAAK;IAC7E9B,SAAS,CAACyC,OAAO,EAAE;IACnBV,MAAM,CAAC7B,YAAY,CAACwC,KAAK,CAAC,CAACC,oBAAoB,CAAC;MAAEC,MAAM,EAAE;IAAK,CAAE,CAAC;EACpE,CAAC,CAAC;EAEFd,EAAE,CAAC,4DAA4D,EAAE,MAAK;IACpE7B,OAAO,CAAC4B,aAAa,EAAE;IACvB,MAAMgB,iBAAiB,GAAG5C,OAAO,CAAC6C,aAAa,CAACC,aAAa,CAC3D,sBAAsB,CACvB;IACDhB,MAAM,CAACc,iBAAiB,CAAC,CAACb,UAAU,EAAE;EACxC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}